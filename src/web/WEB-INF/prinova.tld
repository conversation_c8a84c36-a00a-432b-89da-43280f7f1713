<?xml version="1.0" encoding="ISO-8859-1" ?>

<taglib version="2.0" xmlns="http://java.sun.com/xml/ns/j2ee"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd">

    <jsp-version>2.0</jsp-version>
    <description>The MessagePoint Tag Library</description>
    <display-name>MessagePoint Tag Library</display-name>
    <tlib-version>2.0</tlib-version>

    <short-name>msgpt</short-name>
    <uri>http://messagepoint.prinova.com/taglib</uri>

    <tag>
        <description>Get the currently signed in User object and place it into the requested name and scope</description>
        <name>GetUser</name>
        <tag-class>com.prinova.messagepoint.tag.GetUser</tag-class>
		<body-content>scriptless</body-content>
	    <attribute>
            <name>outputName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Redirect the User to their default view page</description>
        <name>UserDefaultViewRedirect</name>
        <tag-class>com.prinova.messagepoint.tag.UserDefaultViewRedirect</tag-class>
		<body-content>JSP</body-content>
        <attribute>
            <name>defaultRedirect</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Check if the current user has been granted the authority being specified, and if so, display the contents of the tag body.</description>
        <name>IfAuthGranted</name>
        <tag-class>com.prinova.messagepoint.tag.IfAuthGranted</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <name>authority</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>elseStatement</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Check if the current user has been granted the authority being specified, and if so, display the contents of the tag body.</description>
        <name>AnyAuthGranted</name>
        <tag-class>com.prinova.messagepoint.tag.AnyAuthGranted</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <name>permissions</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Retrieve a list of Hibernate Objects and place them into the requested variable name and scope.</description>
        <name>GetHibernateObjects</name>
        <tag-class>com.prinova.messagepoint.tag.GetHibernateObjects</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <name>hibernateObjectName</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>outputName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>startingItem</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>endingItem</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>orderByPropertyName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>

    </tag>

    <tag>
        <description>Retrieve a list of Hibernate Objects and place them into the requested variable name and scope.</description>
        <name>QueryHibernateObjects</name>
        <tag-class>com.prinova.messagepoint.tag.QueryHibernateObjects</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <name>hqlQuery</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>outputName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Retrieve a single Hibernate Object by ObjectName and ID and place it into the requested variable name and scope.</description>
        <name>GetHibernateObject</name>
        <tag-class>com.prinova.messagepoint.tag.GetHibernateObject</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <name>hibernateObjectName</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>id</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>outputName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Do tabs...</description>
        <name>WorkflowTab</name>
        <tag-class>com.prinova.messagepoint.tag.view.WorkflowTabTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>type</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>edit</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>disableAll</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>theme</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>useWorkflow</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>flowParameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cancelUrl</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>btnAlign</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Do tabs...</description>
        <name>PreviewMessageFlowTabTag</name>
        <tag-class>com.prinova.messagepoint.tag.view.PreviewMessageFlowTabTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>type</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>edit</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>disableAll</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>flowParameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Insert Schedule Selectors Tabs - Implement Add/Remove/Done/Flow buttons</description>
        <name>InsertScheduleSelectorsFlowTabTag</name>
        <tag-class>com.prinova.messagepoint.tag.view.InsertScheduleSelectorsFlowTabTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>type</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>edit</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>disableAll</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>useWorkflow</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>flowParameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>btnAlign</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Do tabs...</description>
        <name>TargetingFlowTabTag</name>
        <tag-class>com.prinova.messagepoint.tag.view.TargetingFlowTabTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>type</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>edit</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>disableAll</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Do tabs...</description>
        <name>ApprovalFlowTabTag</name>
        <tag-class>com.prinova.messagepoint.tag.view.ApprovalFlowTabTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>type</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>edit</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>permission</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>disableAll</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>flowParameter</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Do tabs...</description>
        <name>MessageConfirmationFlowTabTag</name>
        <tag-class>com.prinova.messagepoint.tag.view.MessageConfirmationFlowTabTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>type</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>parameter</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>edit</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
		<description>Calculate page navigation</description>
        <name>CalculatePageNavigation</name>
        <tag-class>com.prinova.messagepoint.tag.CalculatePageNavigation</tag-class>
		<body-content>scriptless</body-content>
        <attribute>
            <name>page</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>pageSize</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>maxPages</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>itemsName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>startingItemName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>endingItemName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>totalItemsName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>totalPagesName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>outputName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>className</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
		<description>Retrieves objects using pagination</description>
        <name>GetPagedHibernateObjects</name>
        <tag-class>com.prinova.messagepoint.tag.GetPagedHibernateObjects</tag-class>
		<body-content>scriptless</body-content>
        <attribute>
            <name>hqlFromQuery</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>page</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>pageSize</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>maxPages</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>itemsName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>startingItemName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>endingItemName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>totalItemsName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>totalPagesName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>outputName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
         <attribute>
            <name>orderBy</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
    	<description>Output page navigation</description>
        <name>OutputPageNavigation</name>
        <tag-class>com.prinova.messagepoint.tag.OutputPageNavigation</tag-class>
		<body-content>scriptless</body-content>
        <attribute>
            <name>page</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>pageSize</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>maxPages</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>queryStringParms</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>token</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>startingItemName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>endingItemName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>totalItemsName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>totalPagesName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>customLayout</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
    <description>Given a page, this tag builds a full URL to that page</description>
        <name>URLBuilder</name>
        <tag-class>com.prinova.messagepoint.tag.URLBuilder</tag-class>
		<body-content>scriptless</body-content>
        <attribute>
            <name>page</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>ssl</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>fullyQualify</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>maintainQueryStringParms</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>queryStringParms</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Write out the HTML to represent the document and it's zones for selection.</description>
        <name>DocumentTag</name>
        <tag-class>com.prinova.messagepoint.tag.view.DocumentTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>document</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>contentType</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>contentObject</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>readOnly</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>showDisabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
     	<attribute>
            <name>type</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
     	<attribute>
            <name>hasPartAction</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
     	<attribute>
            <name>selectedZoneId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
     	<attribute>
            <name>selectedSectionId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
     	<attribute>
            <name>size</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
     	<attribute>
            <name>showSectionNames</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
     	<attribute>
            <name>maxWidth</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Write out the tabs and sub tabs used for navigation.</description>
        <name>NewNavigationTabs</name>
        <tag-class>com.prinova.messagepoint.tag.view.NewNavigationTabTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <name>tab</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>edit</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Generate buttons and drop down menus for a view page.</description>
        <name>DropDownMenu</name>
        <tag-class>com.prinova.messagepoint.tag.view.DropDownMenuTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>edit</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Write out the standard header for a Messagepoint page.</description>
        <name>Banner</name>
        <tag-class>com.prinova.messagepoint.tag.layout.BannerTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <name>edit</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cssTableClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>customLinks</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>type</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>monitorSession</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>tab</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>applyTabs</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Write out the breadcrumbs for current page context.</description>
        <name>Breadcrumb</name>
        <tag-class>com.prinova.messagepoint.tag.layout.BreadcrumbTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>titleText</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>contentObjectName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>includeURI</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Write out the standard context bar for a Messagepoint page.</description>
        <name>ContextBar</name>
        <tag-class>com.prinova.messagepoint.tag.layout.ContextBarTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <name>touchpointSelectionContext</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>branchContext</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>globalContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>languageContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>workflowURL</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>workflowPermission</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>searchContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>branchContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>touchpointSetupContext</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>collectionContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>touchpointAttributesContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>touchpointContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>touchpointContextDisabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>channelConntextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Write out the standard header for a Messagepoint page.</description>
        <name>LowerContainer</name>
        <tag-class>com.prinova.messagepoint.tag.layout.LowerContainerTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>fullPanel</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>noPanel</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>extendedWidth</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Write out the standard header for a Messagepoint page.</description>
        <name>SignInLowerContainer</name>
        <tag-class>com.prinova.messagepoint.tag.layout.SignInLowerContainerTag</tag-class>
        <body-content>JSP</body-content>
    </tag>
    <tag>
        <description>Write out the standard content panel for open area pages</description>
        <name>ContentPanel</name>
        <tag-class>com.prinova.messagepoint.tag.layout.ContentPanelTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>extendedWidth</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Write out the trees.</description>
        <name>TreeContainer</name>
        <tag-class>com.prinova.messagepoint.tag.view.TreeContainerTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>outputName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>scope</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>allView</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>sessionId</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>userFolders</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>pageManaged</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>labelOverride</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>applyHeader</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
   </tag>
    <tag>
        <description>Write out the trees.</description>
        <name>ContentData</name>
        <tag-class>com.prinova.messagepoint.tag.layout.ContentPanelTemplateTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>pairedWithTree</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>titleString</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>hasCollapseButtons</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>noHeader</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>extendedWidth</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Content data panel action controls</description>
        <name>ContentDataActions</name>
        <tag-class>com.prinova.messagepoint.tag.layout.ContentDataControlsTag</tag-class>
        <body-content>scriptless</body-content>
    </tag>
    <tag>
        <description>Content data panel action controls</description>
        <name>ContentDataFilters</name>
        <tag-class>com.prinova.messagepoint.tag.layout.ContentDataFiltersTag</tag-class>
        <body-content>scriptless</body-content>
    </tag>
    <tag>
        <description>Generate button.</description>
        <name>Button</name>
        <tag-class>com.prinova.messagepoint.tag.view.ButtonTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>label</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>URL</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>tabindex</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>type</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>primary</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>flowControl</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>icon</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Generate slim button.</description>
        <name>SlimButton</name>
        <tag-class>com.prinova.messagepoint.tag.view.SlimButtonTag</tag-class>
        <body-content>JSP</body-content>
        <attribute>
            <name>label</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>URL</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>disabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Bound datepicker interface.</description>
        <name>Calendar</name>
        <tag-class>com.prinova.messagepoint.tag.CalendarTag</tag-class>
        <body-content>JSP</body-content>
		<attribute>
			<name>path</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>dateFormat</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>viewableDateFormat</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onchange</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>onkeyup</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>cssClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
    </tag>
    <tag>
        <description>Calendar CSS and Javascript includes.</description>
        <name>CalendarIncludes</name>
        <tag-class>com.prinova.messagepoint.tag.CalendarIncludesTag</tag-class>
        <body-content>empty</body-content>
    </tag>
    <tag>
    	<description>Implement jQuery input filtering.</description>
    	<name>InputFilter</name>
    	<tag-class>com.prinova.messagepoint.tag.InputFilterTag</tag-class>
    	<body-content>scriptless</body-content>
    	<attribute>
    		<name>type</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    </tag>
    <tag>
    	<description>Popup frame</description>
    	<name>Popup</name>
    	<tag-class>com.prinova.messagepoint.tag.layout.PopupTag</tag-class>
    	<body-content>scriptless</body-content>
    	<attribute>
    		<name>theme</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>title</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>id</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    </tag>
    <tag>
        <description>Sign in body display.</description>
        <name>SignInBody</name>
        <tag-class>com.prinova.messagepoint.tag.layout.SignInBodyTag</tag-class>
        <body-content>JSP</body-content>
    </tag>
    <tag>
        <description>Write out the standard header for a Messagepoint page.</description>
        <name>SignInBanner</name>
        <tag-class>com.prinova.messagepoint.tag.layout.SignInBannerTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>primary</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
	<tag>
        <description>Apply shadow framing to rectangular displays.</description>
        <name>ShadowFrame</name>
        <tag-class>com.prinova.messagepoint.tag.view.ShadowFrameTag</tag-class>
        <body-content>JSP</body-content>
    </tag>
    <tag>
    	<description>Specifies a button table row.</description>
    	<name>Information</name>
    	<tag-class>com.prinova.messagepoint.tag.view.InformationTag</tag-class>
    	<body-content>scriptless</body-content>
        <attribute>
    		<name>type</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
        <attribute>
            <name>singleLine</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    	<attribute>
    		<name>errorMsgs</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
        <attribute>
            <name>dismissible</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>containerClasses</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>extraClasses</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>floating</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
    	<description>Manage standard table displays.</description>
    	<name>DataTable</name>
    	<tag-class>com.prinova.messagepoint.tag.layout.TableTag</tag-class>
    	<body-content>scriptless</body-content>
    	<attribute>
    		<name>labelWidths</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
        <attribute>
            <name>labelPosition</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    	<attribute>
    		<name>contentWidth</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>isTigerStriped</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>isColumnStriped</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>listHeader</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>multiColumn</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>id</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>style</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>theme</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>hasListHeader</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>searchFilter</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>columnVisibility</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>columnReorder</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>scroller</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>drillDown</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>singleSelect</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>multiSelect</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>selectAllPages</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>multiDrillDown</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>dragAndDrop</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>numUnreorderableCols</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>async</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>sort</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>paginate</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>asyncPaginate</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>lengthSelect</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
        <attribute>
            <name>simplePagination</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    	<attribute>
    		<name>permitsShowAll</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>maxPageSize</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>tableTools</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
        <attribute>
            <name>tableToolsButtons</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>exportFilename</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    	<attribute>
    		<name>display</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>mobileView</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>serverMethod</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
        <attribute>
            <name>staticData</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
    	<description>Specifies a button table row.</description>
    	<name>TableButtons</name>
    	<tag-class>com.prinova.messagepoint.tag.layout.TableButtonsTag</tag-class>
    	<body-content>JSP</body-content>
    	<attribute>
    		<name>type</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    </tag>
    <tag>
    	<description>Specifies a single TD or TH with an option of sortable or colvisable.</description>
    	<name>TableElement</name>
    	<tag-class>com.prinova.messagepoint.tag.layout.TableElementTag</tag-class>
    	<body-content>scriptless</body-content>
        <attribute>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    	<attribute>
    		<name>label</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>align</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>width</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>modelContent</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>style</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>sortable</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>colvisable</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>ssortdatatype</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>stype</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    </tag>
    <tag>
    	<description>Specifies a standard table row item (TD1: Label, TD2: Content).</description>
    	<name>TableItem</name>
    	<tag-class>com.prinova.messagepoint.tag.layout.TableItemTag</tag-class>
    	<body-content>scriptless</body-content>
    	<attribute>
    		<name>label</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>mandatory</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>id</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>cssClass</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>style</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>modelContent</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>type</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>valign</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    </tag>
    <tag>
    	<description>Contains content to be tied to a folding enabled header.</description>
    	<name>TableFoldingContent</name>
    	<tag-class>com.prinova.messagepoint.tag.layout.TableFoldingContentTag</tag-class>
    	<body-content>JSP</body-content>
    	<attribute>
    		<name>id</name>
    		<required>true</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    </tag>

    <tag>
        <description>Generate a div based table layout</description>
        <name>GridLayout</name>
        <tag-class>com.prinova.messagepoint.tag.layout.GridLayoutTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>width</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>align</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Generate a div based table layout</description>
        <name>GridLayoutRow</name>
        <tag-class>com.prinova.messagepoint.tag.layout.GridLayoutRowTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Generate a div based table layout</description>
        <name>GridLayoutCell</name>
        <tag-class>com.prinova.messagepoint.tag.layout.GridLayoutCellTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Generate a div based flow layout</description>
        <name>FlowLayout</name>
        <tag-class>com.prinova.messagepoint.tag.layout.FlowLayoutTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>vertical</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>align</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Surrounds content used in a flow container</description>
        <name>FlowLayoutItem</name>
        <tag-class>com.prinova.messagepoint.tag.layout.FlowLayoutItemTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>align</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Generate a div based flow layout</description>
        <name>FormLayout</name>
        <tag-class>com.prinova.messagepoint.tag.layout.FormLayout.FormLayoutTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>vertical</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>labelWidth</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>inputWidth</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>rightAlign</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>columns</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>center</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Inserts a input control into a FormLayout</description>
        <name>FormHeader</name>
        <tag-class>com.prinova.messagepoint.tag.layout.FormLayout.FormHeaderTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>label</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>labelString</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
        <description>Inserts a input control into a FormLayout</description>
        <name>FormField</name>
        <tag-class>com.prinova.messagepoint.tag.layout.FormLayout.FormFieldTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>label</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>labelString</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>mandatory</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Container for iFrame window</description>
        <name>iFrameContainer</name>
        <tag-class>com.prinova.messagepoint.tag.layout.iFrameContainer</tag-class>
        <body-content>scriptless</body-content>
    </tag>

    <tag>
    	<description>Specifies a standard table header</description>
    	<name>TableHeader</name>
    	<tag-class>com.prinova.messagepoint.tag.layout.TableHeaderTag</tag-class>
    	<body-content>scriptless</body-content>
    	<attribute>
    		<name>label</name>
    		<required>true</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>label2</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>type</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>foldingContentId</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>contentReduced</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>iconClass</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>editPath</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>canEdit</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>escapeLabel</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    </tag>
    <tag>
    	<description>Generates a table row.</description>
    	<name>TableListGroup</name>
    	<tag-class>com.prinova.messagepoint.tag.layout.TableListGroupTag</tag-class>
    	<body-content>scriptless</body-content>
    	<attribute>
    		<name>style</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>id</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>reducedSpacing</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>iFrameId</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>iFrameSrc</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>drillDownEnabled</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
        <attribute>
            <name>jsonDataObject</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Forces text wrapping on strings that may have long unbroken words.</description>
        <name>TextWidthFormat</name>
        <tag-class>com.prinova.messagepoint.tag.layout.TextWidthFormatTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>text</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>charLength</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>isURL</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Format text (max length)</description>
        <name>TxtFmt</name>
        <tag-class>com.prinova.messagepoint.tag.layout.TxtFmtTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>maxLength</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>applyTooltip</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>
    <tag>
    	<description>Provides options popup display when editing record and element lists.</description>
    	<name>EncodeURL</name>
    	<tag-class>com.prinova.messagepoint.tag.EncodeUrlTag</tag-class>
    	<body-content>JSP</body-content>
    	<attribute>
    		<name>url</name>
    		<required>true</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    </tag>
	<tag>
    	<description>Generates a context menu.</description>
    	<name>ContextMenu</name>
    	<tag-class>com.prinova.messagepoint.tag.view.ContextMenuTag</tag-class>
    	<body-content>scriptless</body-content>
    	<attribute>
    		<name>name</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    </tag>
	<tag>
    	<description>Generates a context menu entry.</description>
    	<name>ContextMenuEntry</name>
    	<tag-class>com.prinova.messagepoint.tag.view.ContextMenuEntryTag</tag-class>
    	<body-content>scriptless</body-content>
    	<attribute>
    		<name>name</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    	<attribute>
    		<name>link</name>
    		<required>false</required>
    		<rtexprvalue>true</rtexprvalue>
    	</attribute>
    </tag>
    <tag>
        <name>DataSourceTree</name>
        <tag-class>com.prinova.messagepoint.tag.DataSourceTreeTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>id</name>
            <required>true</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>selectedNodeId</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>dataSource</name>
            <required>true</required>
            <type>java.lang.Object</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>viewOnly</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Initializes a slider panel given a target and content panel</description>
        <name>SliderToggle</name>
        <tag-class>com.prinova.messagepoint.tag.view.SliderToggleTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>toggleButtonId</name>
            <type>java.lang.String</type>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>firstPanel</name>
            <type>java.lang.String</type>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>secondPanel</name>
            <type>java.lang.String</type>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
        </attribute>
        <attribute>
            <name>contentPanel</name>
            <type>java.lang.String</type>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>onBeforeSlide</name>
            <type>java.lang.String</type>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>onAfterSlide</name>
            <type>java.lang.String</type>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Provides tree navigation functionality for dynamic content</description>
        <name>VariantTree</name>
        <tag-class>com.prinova.messagepoint.tag.VariantTreeTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>id</name>
            <required>true</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>expanded</name>
            <required>true</required>
            <type>java.lang.Boolean</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>onChange</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>onContextMenuSelect</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>onResize</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>dataBinding</name>
            <required>true</required>
            <type>java.lang.Object</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>selectedNodeId</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>showVersioning</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>applyDocumentContext</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>connectedContext</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>contentCompareContext</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>treesCompareContext</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>compareToObjectId</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>compareDataType</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>compareToNodeId</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>showContentStatus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>syncCommit</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>initializeFetch</name>
            <required>false</required>
            <type>java.lang.Boolean</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>canUpdate</name>
            <required>false</required>
            <type>java.lang.Boolean</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Provides checkbox tree navigation functionality</description>
        <name>CheckboxTree</name>
        <tag-class>com.prinova.messagepoint.tag.CheckboxTreeTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>id</name>
            <required>true</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>expanded</name>
            <required>true</required>
            <type>java.lang.Boolean</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>multiselect</name>
            <required>false</required>
            <type>java.lang.Boolean</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>onChange</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>onResize</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>dataBinding</name>
            <required>true</required>
            <type>java.lang.Object</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>selectedNodesList</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Provides tree navigation functionality for dynamic content</description>
        <name>CollectionDataValuesTree</name>
        <tag-class>com.prinova.messagepoint.tag.view.CollectionDataValuesTreeTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>id</name>
            <required>true</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <type>java.lang.Boolean</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>style</name>
            <required>false</required>
            <type>java.lang.String</type>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Write out the standard header for a Messagepoint page.</description>
        <name>BannerNew</name>
        <tag-class>com.prinova.messagepoint.tag.layout.BannerTemplateTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <name>edit</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>type</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>tab</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>monitorSession</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>applyTabs</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>showMenus</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>addOnMode</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>subTitle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <name>Html5</name>
        <tag-class>com.prinova.messagepoint.tag.layout.Html5TemplateTag</tag-class>
        <body-content>scriptless</body-content>
    </tag>

    <tag>
        <name>Section</name>
        <tag-class>com.prinova.messagepoint.tag.layout.SectionTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <name>HeaderNew</name>
        <tag-class>com.prinova.messagepoint.tag.layout.MessagepointHeader</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>title</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>viewType</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>extendedScripts</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>applyCSRFSecurity</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>disableDataTable</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>includeMessagepointCore</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>disableMaintenancePopup</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>responseCacheable</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <name>Script</name>
        <tag-class>com.prinova.messagepoint.tag.layout.ScriptTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>src</name>
            <type>java.lang.String</type>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>legacySrc</name>
            <type>java.lang.String</type>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>async</name>
            <type>boolean</type>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>defer</name>
            <type>boolean</type>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>insertBeforeExternals</name>
            <type>boolean</type>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <name>Stylesheet</name>
        <tag-class>com.prinova.messagepoint.tag.layout.StylesheetReferenceTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>href</name>
            <type>java.lang.String</type>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>id</name>
            <type>java.lang.String</type>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>standalone</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <name>BodyNew</name>
        <tag-class>com.prinova.messagepoint.tag.layout.BodyTemplateTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>cssClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>theme</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>waitForScriptLoading</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>type</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>mobile</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>cssStyle</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>dataRole</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <name>ContextBarNew</name>
        <tag-class>com.prinova.messagepoint.tag.layout.ContextBarTemplateTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>touchpointSelectionContext</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>branchContext</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>globalContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>languageContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>workflowURL</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>workflowPermission</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>searchContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>branchContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>touchpointSetupContext</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>collectionContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>channelContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>touchpointAttributesContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>touchpointContextApplied</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>touchpointContextDisabled</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>touchpointAdminContext</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>searchContextType</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <name>FormControl</name>
        <tag-class>com.prinova.messagepoint.tag.layout.FormControlTag</tag-class>
        <description>Standard label control layout markup</description>
        <body-content>scriptless</body-content>
        <attribute>
            <name>verticalAlign</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>label</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <name>labelString</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <name>BundleDeliverySelect</name>
        <tag-class>com.prinova.messagepoint.tag.BundleDeliverySelect</tag-class>
        <description>Selection of endpoint servers with optional filename override</description>
        <body-content>scriptless</body-content>
        <attribute>
            <name>id</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>deServerGuidPath</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>filenameOverridePath</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>eventTypeFilter</name>
            <rtexprvalue>true</rtexprvalue>
            <required>false</required>
            <type>java.lang.Integer</type>
        </attribute>
    </tag>

    <tag>
        <name>AsyncJsonDataTable</name>
        <tag-class>com.prinova.messagepoint.tag.AsyncJsonDataTable</tag-class>
        <description></description>
        <body-content>scriptless</body-content>
        <attribute>
            <name>id</name>
            <rtexprvalue>true</rtexprvalue>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>asyncService</name>
            <rtexprvalue>true</rtexprvalue>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>dataType</name>
            <rtexprvalue>true</rtexprvalue>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>onPageChangeHandler</name>
            <rtexprvalue>true</rtexprvalue>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>multiSelect</name>
            <rtexprvalue>true</rtexprvalue>
            <required>false</required>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <name>selectAllPages</name>
            <rtexprvalue>true</rtexprvalue>
            <required>false</required>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <name>externalApi</name>
            <rtexprvalue>true</rtexprvalue>
            <required>false</required>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <name>fetchCallback</name>
            <rtexprvalue>true</rtexprvalue>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>showHeader</name>
            <rtexprvalue>true</rtexprvalue>
            <required>false</required>
            <type>java.lang.Boolean</type>
        </attribute>
    </tag>

    <tag>
        <name>AsyncJsonDataTableColumn</name>
        <tag-class>com.prinova.messagepoint.tag.AsyncJsonDataTableColumn</tag-class>
        <description></description>
        <body-content>scriptless</body-content>
        <attribute>
            <name>key</name>
            <rtexprvalue>true</rtexprvalue>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>showHeaderLabel</name>
            <rtexprvalue>true</rtexprvalue>
            <required>false</required>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <name>label</name>
            <rtexprvalue>true</rtexprvalue>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>sortable</name>
            <rtexprvalue>true</rtexprvalue>
            <required>false</required>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <name>width</name>
            <rtexprvalue>true</rtexprvalue>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <function>
		<name>contains</name>
		<function-class>com.prinova.messagepoint.tag.Functions</function-class>
		<function-signature>boolean contains( java.util.Collection, java.lang.Object )</function-signature>
		<example></example>
	</function>

    <function>
		<name>containsValue</name>
		<function-class>com.prinova.messagepoint.tag.Functions</function-class>
		<function-signature>boolean containsValue( java.util.Collection, java.lang.Object )</function-signature>
		<example></example>
	</function>

    <function>
		<name>size</name>
		<function-class>com.prinova.messagepoint.tag.Functions</function-class>
		<function-signature>int size( java.util.Collection )</function-signature>
		<example></example>
	</function>
    <function>
		<name>hashCode</name>
		<function-class>com.prinova.messagepoint.tag.Functions</function-class>
		<function-signature>int hashCode( java.lang.Object )</function-signature>
		<example></example>
	</function>


    <function>
		<name>getMessage</name>
		<function-class>com.prinova.messagepoint.tag.Functions</function-class>
		<function-signature>java.lang.String getMessage( java.lang.String )</function-signature>
		<example></example>
	</function>

    <function>
		<name>forHrefPercent</name>
		<function-class>com.prinova.messagepoint.tag.Functions</function-class>
		<function-signature>java.lang.String forHrefPercent( java.lang.String, boolean )</function-signature>
		<example></example>
	</function>

	<function>
		<name>cleanEmptyParams</name>
		<function-class>com.prinova.messagepoint.tag.URLBuilder</function-class>
		<function-signature>java.lang.String cleanEmptyParams(java.lang.String)</function-signature>
		<example></example>
	</function>

	<function>
		<name>unescapeHtml</name>
		<function-class>com.prinova.messagepoint.util.StringUtil</function-class>
		<function-signature>java.lang.String unescapeHtml( java.lang.String)</function-signature>
		<example></example>
	</function>

    <function>
        <name>msToHms</name>
        <function-class>com.prinova.messagepoint.util.StringUtil</function-class>
        <function-signature>java.lang.String msToHms( java.lang.String)</function-signature>
        <example></example>
    </function>

    <function>
        <name>getResourceToken</name>
        <function-class>com.prinova.messagepoint.util.HttpRequestUtil</function-class>
        <function-signature>com.prinova.messagepoint.util.HttpRequestUtil.ResourceToken buildResourceToken()</function-signature>
    </function>

    <function>
        <name>removeSpecialCharacters</name>
        <function-class>com.prinova.messagepoint.util.StringUtil</function-class>
        <function-signature>java.lang.String removeSpecialCharacters(java.lang.String)</function-signature>
        <example></example>
    </function>

</taglib>
