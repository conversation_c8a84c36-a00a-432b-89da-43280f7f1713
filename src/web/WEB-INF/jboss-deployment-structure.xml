<?xml version="1.0" encoding="UTF-8"?>
<jboss-deployment-structure xmlns="urn:jboss:deployment-structure:1.2">
    <deployment>
        <exclusions>
        <!-- don't exclude next 
            <module name="org.jboss.logging" />
            <module name="org.jboss.logging.jul-to-slf4j-stub" />
            <module name="org.jboss.logmanager" />
            <module name="org.jboss.logmanager.log4j" />
            <module name="org.slf4j" />
            <module name="org.slf4j.impl" />

            but exclude org.apache.commons.logging for proper logging under JBoss 7 -->
            <module name="org.apache.commons.logging" />
            <module name="org.apache.logging.log4j.api"/>

            <!-- Excluding jackson modules as MP should provide this dependency -->
            <module name="com.fasterxml.jackson.core.jackson-core" />
            <module name="com.fasterxml.jackson.core.jackson-databind" />
            <module name="com.fasterxml.jackson.core.jackson-annotations" />
            <module name="com.fasterxml.jackson.jaxrs.jackson-jaxrs-json-provider" />
            <module name="org.jboss.resteasy.resteasy-jackson2-provider" />
            <module name="com.fasterxml.jackson.datatype.jackson-datatype-jsr310" />
            <module name="com.fasterxml.jackson.datatype.jackson-datatype-jdk8" />

        </exclusions>
    </deployment>
</jboss-deployment-structure>