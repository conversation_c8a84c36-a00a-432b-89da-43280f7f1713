<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
  <xs:element name="MessageTrackingImport">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="Job"/>
        <xs:element maxOccurs="unbounded" ref="ProductionTracking"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="Job">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="RunDateTime"/>
      </xs:sequence>
      <xs:attribute name="batch" use="required" type="xs:integer"/>
      <xs:attribute name="externalId" use="required" type="xs:string"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="RunDateTime" type="xs:dateTime"/>
  <xs:element name="ProductionTracking">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="customerId"/>
        <xs:element ref="customerLanguageCd"/>
        <xs:element maxOccurs="unbounded" ref="zone"/>
      </xs:sequence>
      <xs:attribute name="seq" use="required" type="xs:integer"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="customerId" type="xs:string"/>
  <xs:element name="customerLanguageCd" type="xs:language" />
  <xs:element name="zone">
    <xs:complexType>
      <xs:sequence>
        <xs:element maxOccurs="unbounded" ref="Message"/>
      </xs:sequence>
      <xs:attribute name="id" use="required" type="xs:integer"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="Message">
    <xs:complexType>
      <xs:attribute name="externalId" use="required" type="xs:string"/>
      <xs:attribute name="played" use="required" type="xs:boolean"/>
    </xs:complexType>
  </xs:element>
</xs:schema>
