<?xml version="1.0" encoding="UTF-8"?>
<MessageImport>
	<Message seq="1" externalId="111">
		<name>Message 1 a plain text message</name>
		<statusCd>PRODUCTION</statusCd>
		<Delivery>
			<zoneId>1</zoneId>
			<zoneId>2</zoneId>
		</Delivery>
		<ContentTypeCd>1</ContentTypeCd>
		<Mime>PLAIN-TEXT</Mime>
		<Content languageCd="en">
			<TextContent>
				<text>This text content is a plain text</text>
			</TextContent>
			<ContentHistory>
				<CreatedDateTime>2008-01-25T07:30:59.0</CreatedDateTime>
				<text>This is the original context</text>
			</ContentHistory>
			<ContentHistory>
				<CreatedDateTime>2008-04-02T09:40:20.0</CreatedDateTime>
				<text>This is the 2nd revision of the original context</text>
			</ContentHistory>
		</Content>
		<Content languageCd="fr">
			<TextContent>
				<text>This is the French version of the plain text</text>
			</TextContent>
			<ContentHistory>
				<CreatedDateTime>2008-01-25T07:30:59.0</CreatedDateTime>
				<text>This is the original French context</text>
			</ContentHistory>
			<ContentHistory>
				<CreatedDateTime>2008-04-25T09:40:20.0</CreatedDateTime>
				<text>This is the 2nd revision of the original French context</text>
			</ContentHistory>
		</Content>
	</Message>

	<Message seq="2" externalId="112">
		<name>Message 2 a html content message</name>
		<statusCd>PRODUCTION</statusCd>
		<Delivery>
			<zoneId>2</zoneId>
		</Delivery>
		<ContentTypeCd>1</ContentTypeCd>
		<Mime>XHTML</Mime>
		<Content languageCd="en">
			<TextContent>
				<text><![CDATA[
				"
					<span class='IntroTitle'>Welcome Back!</span>
					<br />
					<span class='IntroText'>
						Welcome back as a customer of
						CheckFirstInvestments. For a refresher on all
						your customer benefits sign onto our website at
					</span>
					<span class='IntroEmph'>
						www.checkfirstinvestments.com
					</span>
					<span class='IntroText'>
						. Also be sure to check out your account summary
						when you sign in.
					</span>
					"]]>
				</text>
			</TextContent>
			<ContentHistory>
				<CreatedDateTime>2008-01-30T07:30:59.0</CreatedDateTime>
				<text>
					<![CDATA[
				"
					<span class='IntroTitle'>Welcome Back!</span>
					<br />
					<span class='IntroText'>
						Welcome back as a customer of XXXXXX. For a
						refresher on all your customer benefits sign
						onto our website at
					</span>
					<span class='IntroEmph'>www.XXXXXX.com</span>
					<span class='IntroText'>
						. Also be sure to check out your account summary
						when you sign in.
					</span>
					"]]>
				</text>
			</ContentHistory>
			<ContentHistory>
				<CreatedDateTime>2008-04-21T09:40:20.0</CreatedDateTime>
				<text>
					<![CDATA[
				"
					<span class='IntroTitle'>Welcome Back!</span>
					<br />
					<span class='IntroText'>
						Welcome back as a customer of CheckFirst. For a
						refresher on all your customer benefits sign
						onto our website at
					</span>
					<span class='IntroEmph'>www.checkfirst.com</span>
					<span class='IntroText'>
						. Also be sure to check out your account summary
						when you sign in.
					</span>
					"]]>
				</text>
			</ContentHistory>
		</Content>
	</Message>
	<Message seq="3" externalId="113">
		<name>Message 3 plain text message that is in process</name>
		<statusCd>IN-PROCESS</statusCd>
		<Delivery>
			<zoneId>1</zoneId>
			<zoneId>2</zoneId>
		</Delivery>
		<ContentTypeCd>1</ContentTypeCd>
		<Mime>PLAIN-TEXT</Mime>
		<Content languageCd="en">
			<TextContent>
				<text>Happy Retirement</text>
			</TextContent>
			<ContentHistory>
				<CreatedDateTime>2008-01-25T07:30:59.0</CreatedDateTime>
				<text>Happy Retirement - History</text>
			</ContentHistory>
		</Content>
	</Message>
	
	<Message seq="4" externalId="114">
		<name>Message 4 - #$%non alphanumeric name will be rejected</name>
		<statusCd>IN-PROCESS</statusCd>
		<Delivery>
			<zoneId>1</zoneId>
			<zoneId>2</zoneId>
		</Delivery>
		<ContentTypeCd>1</ContentTypeCd>
		<Mime>PLAIN-TEXT</Mime>
		<Content languageCd="en">
			<TextContent>
				<text>Happy Birthday</text>
			</TextContent>
			<ContentHistory>
				<CreatedDateTime>2008-01-25T07:30:59.0</CreatedDateTime>
				<text>Happy Birthday - History</text>
			</ContentHistory>
		</Content>
	</Message>	
		
</MessageImport>


