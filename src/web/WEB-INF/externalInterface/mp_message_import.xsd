<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified">
	<xs:element name="MessageImport">
		<xs:complexType>
			<xs:sequence>
				<xs:element maxOccurs="unbounded" ref="Message" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="Message">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="name" />
				<xs:element ref="statusCd" />
				<xs:element ref="Delivery" />
				<xs:element ref="ContentTypeCd" />
				<xs:element ref="Mime" />
				<xs:element maxOccurs="unbounded" ref="Content" />
			</xs:sequence>
			<xs:attribute name="externalId" use="required"
				type="xs:string" />
			<xs:attribute name="seq" use="required" type="xs:integer" />
		</xs:complexType>
	</xs:element>
	<xs:element name="name" type="xs:string" />
	<xs:element name="statusCd" type="MP_CONTENT_STATUS_CODE" />
	<xs:element name="Delivery">
		<xs:complexType>
			<xs:sequence>
				<xs:element maxOccurs="unbounded" ref="zoneId" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="zoneId" type="xs:string" />
	<xs:element name="ContentTypeCd" type="MP_CONTENT_TYPE_CODE" />
	<xs:element name="Mime" type="MP_MIME_CODE" />
	<xs:element name="Content">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="TextContent" />
				<xs:element maxOccurs="unbounded" ref="ContentHistory" />
			</xs:sequence>
			<xs:attribute name="languageCd" use="required"
				type="xs:language" />
		</xs:complexType>
	</xs:element>
	<xs:element name="TextContent">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="text" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="ContentHistory">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="CreatedDateTime" />
				<xs:element ref="text" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="CreatedDateTime" type="xs:dateTime" />
	<xs:element name="text" type="xs:string" />
	
	<xs:simpleType name="MP_CONTENT_TYPE_CODE">
		<xs:restriction base="xs:string">
			<xs:enumeration value="1" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:simpleType name="MP_CONTENT_STATUS_CODE">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PRODUCTION" />
			<xs:enumeration value="IN-PROCESS" />			
		</xs:restriction>
	</xs:simpleType>
	
	<xs:simpleType name="MP_MIME_CODE">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PLAIN-TEXT" />
			<xs:enumeration value="XHTML" />
		</xs:restriction>
	</xs:simpleType>


</xs:schema>
