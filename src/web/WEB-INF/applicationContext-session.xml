<?xml version="1.0" encoding="UTF-8"?>
<beans profile="spring-session" xmlns="http://www.springframework.org/schema/beans"
       xmlns:security="http://www.springframework.org/schema/security"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       					http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
              			http://www.springframework.org/schema/security
						http://www.springframework.org/schema/security/spring-security-3.2.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd http://www.springframework.org/schema/context https://www.springframework.org/schema/context/spring-context.xsd">

    <bean class="org.springframework.session.web.http.DefaultCookieSerializer" name="mpCookieSerializer">
        <property name="cookieName" value="MPSESSIONID"/>
        <property name="useHttpOnlyCookie" value="true" />
        <property name="useSecureCookie" value="true" />
        <property name="sameSite" value="" />
    </bean>

    <util:list list-class="java.util.ArrayList" value-type="javax.servlet.http.HttpSessionListener" id="listeners">
        <bean class="com.prinova.messagepoint.controller.HttpSessionTimeoutListener" />
    </util:list>

    <context:annotation-config/>
    <context:component-scan base-package="com.prinova.messagepoint.redis,com.prinova.messagepoint.redis.config,com.prinova.messagepoint.redis.client,com.prinova.messagepoint.redis.ops" />
    <bean class="org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration" name="redisHttpSessionConfiguration">
        <property name="redisNamespace">
            <bean factory-bean="messagepointRedisConfiguration" factory-method="getRedisNamespace" />
        </property>
        <property name="cookieSerializer" ref="mpCookieSerializer" />
        <property name="httpSessionListeners" ref="listeners" />
    </bean>

    <bean class="com.prinova.messagepoint.util.ApplicationUtil" />

    <util:constant static-field="org.springframework.session.data.redis.config.ConfigureRedisAction.NO_OP"/>

</beans>
