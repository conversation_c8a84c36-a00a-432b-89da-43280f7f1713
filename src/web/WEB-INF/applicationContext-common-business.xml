<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:security="http://www.springframework.org/schema/security"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
			http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
			http://www.springframework.org/schema/context
			http://www.springframework.org/schema/context/spring-context-4.2.xsd
			http://www.springframework.org/schema/tx
			http://www.springframework.org/schema/tx/spring-tx-4.2.xsd
			http://www.springframework.org/schema/aop 
			http://www.springframework.org/schema/aop/spring-aop-4.2.xsd
			http://www.springframework.org/schema/security
			http://www.springframework.org/schema/security/spring-security-3.2.xsd"> 

	<bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
		<property name="defaultEncoding" value="UTF-8"/>
		<property name="basename"><value>/WEB-INF/i18n/messages</value></property>
		<property name="cacheSeconds" value="-1" />
	</bean>
	
	<bean id="localeResolver" class="com.prinova.messagepoint.integrator.MessagepointLocaleResolver">
	</bean>	

    <bean id="mpDaoImpl" class="com.prinova.messagepoint.security.MessagepointDaoImpl" />
	<bean id="mpPreAuthUserDetailImpl" class="com.prinova.messagepoint.security.PreAuthUserDetailImpl" />
    
    <bean id="statusHandlerInterceptor" class="com.prinova.messagepoint.status.StatusHandlerInterceptor" />
    
    <bean id="workflowFieldValidator" class="com.prinova.messagepoint.model.workflow.WorkflowFieldValidator" >
    	<property name="fieldValidatorList">
   			<list>
   				<bean class="com.prinova.messagepoint.status.fieldvalidator.DateFieldValidator" />
				<bean class="com.prinova.messagepoint.status.fieldvalidator.ContentFieldValidator" />
				<bean class="com.prinova.messagepoint.status.fieldvalidator.PersistentCollectionFieldValidator" />
				<bean class="com.prinova.messagepoint.status.fieldvalidator.DefaultFieldValidator" />
   			</list>
    	</property>
	</bean>
       
	<bean id="hibernateSessionFactory"
		class="org.springframework.orm.hibernate5.LocalSessionFactoryBean">
		<property name="mappingLocations">
		  <list>
			<value>classpath*:com/prinova/messagepoint/model/**/*.hbm.xml</value>
			<value>classpath*:com/prinova/messagepoint/reports/model/**/*.hbm.xml</value>
			<value>classpath*:com/prinova/licence/webapp/models/**/*.hbm.xml</value>
		  	<value>classpath*:com/prinova/messagepoint/platform/**/*.hbm.xml</value>
			<value>classpath*:com/prinova/messagepoint/connected/**/*.hbm.xml</value>
		  </list>
		</property>
		<property name="annotatedClasses">
			<list>
				<value>com.prinova.messagepoint.model.task.TaskView</value>
			</list>
		</property>
		<property name="entityInterceptor" ref="statusHandlerInterceptor" />
		<property name="hibernateProperties">
			<ref bean="hibernateProperties"/>
		</property>
	</bean>

	<!-- This is a helper bean that provides access to the configured session factory -->
	<bean id="hibernateUtil" class="com.prinova.messagepoint.util.HibernateUtil" scope="singleton" />

    <bean id="hibernateObjectManager" class="com.prinova.messagepoint.model.manager.HibernateObjectManager" >
    	<property name="sessionFactory" ref="hibernateSessionFactory" />
    	<property name="enableFilter" value="false" />    	
    </bean>

	<bean id="emailManager" class="com.prinova.messagepoint.util.EmailManager"/>
	
	<bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver" />
	
	<bean id="applicationUtil" class="com.prinova.messagepoint.util.ApplicationUtil" />
	
	<bean id="Comment" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="1" />
		<property name="maxLength" value="1000" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.symbols.space" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9\p{L}\p{Sc}\s_\-'\*.$!+=\?,:;@#\[\]\\/]*+" />
    </bean>
	
	<bean id="Description" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="1" />
		<property name="maxLength" value="255" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.symbols.space" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9\p{L}\p{Sc}\s_\-'\*.$!+=\?,:;@#\[\]\\/]*+" />
    </bean>
	
	<bean id="ObjectName" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="true" />
		<property name="minLength" value="2" />
		<property name="maxLength" value="96" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.space.dash.underscore.apos" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9\p{L}\s_\-']*+" />
    </bean>
	
	<bean id="FileName" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="1" />
		<property name="maxLength" value="255" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.dot.dash.backslash.slash.colon.space" />
		<property name="restrictedCharsRegex" value="^(?!~)[A-Za-z0-9!(){}\s_\-./\\:~]*+" />
    </bean>
	
	<bean id="Password" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="6" />
		<property name="maxLength" value="32" />
		<property name="restrictedCharsList" value="" />
		<property name="restrictedCharsRegex" value="" />
    </bean>
	
	<bean id="PersonName" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="true" />
		<property name="minLength" value="2" />
		<property name="maxLength" value="64" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.space.dot.dash.underscore.apos" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9\p{L}_'\-.\s]*+"/>
    </bean>
	
	<bean id="Email" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="5" />
		<property name="maxLength" value="96" />
		<property name="restrictedCharsList" value="" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9!#$\*\+=\?_\-/\(\)`~']([A-Za-z0-9!#$\*\+=\?\._\-/\(\)`~']?[A-Za-z0-9!#$\*\+=\?_\-/\(\)`~']+)*@[A-Za-z0-9\-]([A-Za-z0-9.\-]?[A-Za-z0-9\-]+)*\.[A-Za-z]{2,}$"/>
		<property name="restrictedCharsErrorCode" value="error.input.email.charrestriction" />
    </bean>
	
	<bean id="Phone" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="3" />
		<property name="maxLength" value="32" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.parenth.dash.space" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9()\-\s]*+" />
    </bean>
	
	<bean id="PostalCode" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="5" />
		<property name="maxLength" value="10" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.dash.space" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9\-\s]*+" />
    </bean>
	
	<bean id="Date" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="true" />
		<property name="minLength" value="6" />
		<property name="maxLength" value="32" />
		<property name="restrictedCharsList" value="" />
		<property name="restrictedCharsRegex" value="" />
    </bean>
	
	<bean id="Username" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="true" />
		<property name="minLength" value="2" />
		<property name="maxLength" value="32" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.at.dot.dash.underscore.apos" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9_\-@.']*+" />
    </bean>

	<bean id="messagepointStartup" class="com.prinova.messagepoint.MessagePointStartUp">
		<property name="licenceFile">
		 	<value>${prinova.license.file.name}</value>
		 </property>
		
		<property name="defaultLicenceFile">
		 	<value>WEB-INF/licence.xml</value>
		 </property>
	</bean>

	<bean id="requestParameterTypesSource" class="org.springframework.context.support.ResourceBundleMessageSource">
		<property name="basename"><value>requestParameterTypes</value></property>
	</bean>
	
    <bean id="applicationSecurityManager" class="com.prinova.messagepoint.util.ApplicationSecurityManager"/>
	
	<bean id="globalExceptionManager" class="com.prinova.messagepoint.ExceptionManager" factory-method="instance"/>
	
    <bean id="marshallerClientMessagepoint" class="org.springframework.oxm.jaxb.Jaxb2Marshaller">
        <property name="contextPath" value="com.prinova.messagepoint.platform.ws.client.messagepoint.api.schemas" />
    </bean>
        
	<bean id="wsClientMessagepoint" class="com.prinova.messagepoint.platform.ws.client.WsClientMessagepoint">
	    <property name="marshaller" ref="marshallerClientMessagepoint" />
	    <property name="unmarshaller" ref="marshallerClientMessagepoint" />
	</bean>

   <bean id = "messagepointApplicationListener" class = "com.prinova.messagepoint.model.interceptor.MessagepointApplicationListener"/>
	<bean id = "jobCompletedEventListener" class = "com.prinova.messagepoint.model.interceptor.jobs.JobCompletedEventListener"/>

</beans>
