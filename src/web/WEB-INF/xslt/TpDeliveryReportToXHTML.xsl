<?xml version="1.0" encoding="UTF-8" ?>
<xsl:stylesheet 
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" 
  version="2.0">
    <xsl:include href="reportStyleTemplate.xsl"/>
	<xsl:output method="text"/>
	<xsl:output method="html" indent="yes" name="html"/>

	<xsl:template match="/">
	    <!--Output job report and link to its batch report-->
		<xsl:result-document href="TPDeliveryReport.html" format="html">
		  <html>
		   <head><title>TP Delivery Report</title>
		   <script type="text/javascript" >
					function openReportWindow(reportId){
					    var URL = document.URL;
						var newWindow = window.open(URL + '&#x26;' + 'tpDeliveryReportId='+reportId, 'Popup_Popup', 'scrollbars=yes, statusbar=no, toolbar=yes, menubar=no, location=no, resizable=yes, width=850, height=900');
						newWindow.focus();
					}
				</script>		
		   </head>		   
           <xsl:call-template name="ReportHead"/>
		   <body>
		     <div align="left" style="background-color: #ffffff;">
				<div id="reportContainer" style="padding: 8px;">		
				 <xsl:call-template name="ReportSummary" />
				 <xsl:call-template name="ReportJobs" />
				</div>
			</div>
		   </body>
		  </html>
		</xsl:result-document>
	    <!--Output batch report for each job-->
		<xsl:for-each select="//Report/Touchpoints/Touchpoint/Dates/Date">
			<xsl:if test="./Recipients/Recipient">
			<xsl:variable name="filename" select="concat(@jobId,'.html')" />
			<xsl:result-document href="{$filename}" format="html">
				<html>
				  <head>
					  <title><xsl:value-of select="$message[@id='xslt_messages.tp.delivery.report']" /> -- <xsl:value-of select="$message[@id='xslt_messages.recipient.level']" /></title>
		  		<script type="text/javascript" >
					function openNewWindow(webURL){
						var newWindow = window.open(webURL);
						newWindow.focus();
					}
				</script>		
				  </head>
                  <xsl:call-template name="ReportHead"/>
				  <body>
				   <div align="left" style="background-color: #ffffff;">
					<div id="reportContainer" style="padding: 8px;">
					   <div name="backLink" class="sectionContent">			
							<a href="javascript:history.go(-1)"><xsl:value-of select="$message[@id='xslt_messages.back.to.the.report']" /></a>
					    </div>
					   <xsl:call-template name="JobSummary" />
					   <xsl:call-template name="ReportJob" />
					</div>
					</div>
				  </body>
				</html>
			</xsl:result-document>
			</xsl:if>
		</xsl:for-each>
	</xsl:template>
	
	<xsl:template name="ReportJob">
	<div class="sectionContent">
		<br />
		<xsl:variable name="hasMeta1">
			<xsl:choose><xsl:when test = "../../Data/Meta1">True</xsl:when>
						<xsl:otherwise> False</xsl:otherwise>
			</xsl:choose>
	    </xsl:variable>
		<xsl:variable name="hasMeta2">
			<xsl:choose><xsl:when test = "../../Data/Meta2">True</xsl:when>
						<xsl:otherwise> False</xsl:otherwise>
			</xsl:choose>
	    </xsl:variable>
	    <xsl:variable name="hasEmail">
			<xsl:choose><xsl:when test = "./Recipients/Recipient/@email">True</xsl:when>
						<xsl:otherwise>False</xsl:otherwise>
			</xsl:choose>
	    </xsl:variable>
	    <xsl:variable name="hasWebFile">
			<xsl:choose><xsl:when test = "./Recipients/Recipient/@webfile">True</xsl:when>
						<xsl:otherwise>False</xsl:otherwise>
			</xsl:choose>
	    </xsl:variable>
	    <xsl:variable name="hasWebFilePHP">
			<xsl:choose><xsl:when test = "./Recipients/Recipient/@webfilephp">True</xsl:when>
						<xsl:otherwise>False</xsl:otherwise>
			</xsl:choose>
	    </xsl:variable>
		<table class="coloredTable">
	    	<colgroup>
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
            </colgroup>
			<thead>
                <tr>
                    <th style="text-align:left; width:26%;"><xsl:value-of select="$message[@id='xslt_messages.recipient.id']" /></th>    
                    <xsl:if test="$hasMeta1='True'">   
                      <th style="text-align: left" width="13%">
                        <xsl:choose>
                        <xsl:when test="../../Data/Meta1/@name">
                        	<xsl:value-of select="../../Data/Meta1/@name"/>
                         </xsl:when>                   
                         <xsl:otherwise>
                            <xsl:value-of select="$message[@id='xslt_messages.metadata']" />1
                         </xsl:otherwise>
                         </xsl:choose>
                        </th>
                    </xsl:if>
                    <xsl:if test="$hasMeta2='True'">   
                       <th style="text-align:center;" width="13%">
                        <xsl:choose>
                        <xsl:when test="../../Data/Meta2/@name">
                        	<xsl:value-of select="../../Data/Meta2/@name"/>
                         </xsl:when>                   
                         <xsl:otherwise>
                            <xsl:value-of select="$message[@id='xslt_messages.metadata']" />2
                         </xsl:otherwise>
                         </xsl:choose>
                        </th>
                     </xsl:if>
                     <xsl:if test="$hasEmail='True'">   
                       <th width="34%" style="text-align:left;"><xsl:value-of select="$message[@id='xslt_messages.email']" /></th>
                       <th width="14%">Send</th>
                     </xsl:if>
                     <xsl:if test="$hasWebFile='True'">   
                       <th width="34%" style="text-align:left;"><xsl:value-of select="$message[@id='xslt_messages.web.page']" /></th>
                       <th width="14%">Send</th>
                     </xsl:if>
                     <xsl:if test="$hasEmail!='True' and $hasWebFile!='True'">   
                       <th width="14%" style="text-align:center;"><xsl:value-of select="$message[@id='xslt_messages.send']" /></th>
                     </xsl:if>                    
                </tr>
			</thead>
			<tbody>
			<xsl:for-each select="./Recipients/Recipient" >
              <xsl:variable name="backgroundColor">
                <xsl:choose>
                    <xsl:when test="position() mod 2=1">background-color:#EEEEEE;</xsl:when>
                    <xsl:otherwise>background-color:#FFFFFF;</xsl:otherwise>
                </xsl:choose>
              </xsl:variable>
		      <xsl:variable name="hasNoDuplicates">
				<xsl:choose><xsl:when test = "@failed">False</xsl:when>
							<xsl:otherwise>True</xsl:otherwise>
				</xsl:choose>
		      </xsl:variable>
              <tr style="{$backgroundColor}" >
                <td style="text-align:left;"><xsl:value-of select="Id"/></td>
                <xsl:if test="$hasMeta1='True'">   
                    <td style="text-align:left;"><xsl:value-of select="Meta1"/></td>
                </xsl:if>
                <xsl:if test="$hasMeta2='True'">   
                    <td style="text-align:center;"><xsl:value-of select="Meta2"/></td>
                </xsl:if>
                <xsl:if test="$hasEmail='True'"> 
					<td style="text-align:left;"><xsl:value-of select="./@email"/></td>
				</xsl:if>
                <xsl:if test="$hasWebFile='True' and $hasWebFilePHP='False' and $hasNoDuplicates='True'"> 
					<td style="text-align:left;"><a href="javascript:openNewWindow('{./@webfile}')"><xsl:value-of select="./@webname"/></a></td>
				</xsl:if>
                <xsl:if test="$hasWebFile='True' and $hasWebFilePHP='True' and $hasNoDuplicates='True'"> 
					<td style="text-align:left;"><a href="javascript:openNewWindow('{./@webfile}')">(ASP)</a> / <a href="javascript:openNewWindow('{./@webfilephp}')">(PHP)</a> - <xsl:value-of select="./@webname"/></td>
				</xsl:if>
                <xsl:if test="$hasNoDuplicates='False'"> 
					<td style="text-align:left;"><xsl:value-of select="./@webname"/></td>
				</xsl:if>
				<td style="text-align:center;">
					<xsl:choose>
						<xsl:when test="@failed"><xsl:value-of select="$message[@id='xslt_messages.failed']" /></xsl:when>
						<xsl:when test="@unknown"><xsl:value-of select="$message[@id='xslt_messages.no.status']" /></xsl:when>
						<xsl:otherwise><xsl:value-of select="$message[@id='xslt_messages.succeeded']" /></xsl:otherwise>
					</xsl:choose>
				</td>
			 </tr>
			</xsl:for-each>
			</tbody>
		</table>
	  </div>
	</xsl:template>	
	<xsl:template name="ReportJobs">
    <div class="sectionContent">
    <xsl:choose>
    <xsl:when test="//Report/Touchpoints/Touchpoint/Dates/Date">
      <table class="coloredTable">
	    	<colgroup>
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
            </colgroup>
           <thead>
              <tr>
                 <th style="text-align: left;" width="30%"><xsl:value-of select="$message[@id='xslt_messages.touchpoint']" /></th>
                 <th style="text-align: right;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.job.id']" /></th>
                 <th width="25%">Delivery Date</th>
                 <th style="text-align: right;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.sent']" /></th>
                 <th style="text-align: right;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.failed']" /></th>
                 <th style="text-align: right;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.delivered']" /></th>
              </tr>
           </thead>
        <tbody>          
          <xsl:for-each select="//Report/Touchpoints/Touchpoint/Dates/Date">
          <xsl:variable name="backgroundColor">
            <xsl:choose>
            <xsl:when test="position() mod 2=1">background-color:#EEEEEE;</xsl:when>
            <xsl:otherwise>background-color:#FFFFFF;</xsl:otherwise>
            </xsl:choose>
          </xsl:variable>
          <tr style="{$backgroundColor}" >
            <td style="text-align: left;"><xsl:value-of select="../../Name"/></td>
            <td>
			  <xsl:choose>
                  <xsl:when test="./Recipients/Recipient">
                      <span>
                        <a href="javascript:openReportWindow({@jobId})"><xsl:value-of select="@jobId"/></a>
                      </span>
                  </xsl:when>
                  <xsl:otherwise><xsl:value-of select="@jobId"/></xsl:otherwise>
			  </xsl:choose>    
            </td>                
            <td style="text-align: center;">            
            <xsl:call-template name="FormatDate">
              <xsl:with-param name="DateTime">
                <xsl:value-of select="@on"/>
              </xsl:with-param>
            </xsl:call-template>
            </td>
            <td><xsl:value-of select="@sent"/></td>
            <td><xsl:value-of select="@failed"/></td>
            <td><xsl:value-of select="@delivered"/></td>
          </tr>
          </xsl:for-each>
        </tbody>
      </table>
       </xsl:when>
       <xsl:otherwise><xsl:value-of select="$message[@id='xslt_messages.no.data.for.time.period']" /></xsl:otherwise>
    </xsl:choose>
    </div>
  </xsl:template>
  <xsl:template name="JobSummary">
    <div class="sectionHeader"><xsl:value-of select="$message[@id='xslt_messages.touchpoint.delivery.report']" /> -  <xsl:value-of select="//Report/@name" /></div>
	<div class="sectionContent">
		<table width="100%" cellspacing="0" cellpadding="0" border="0">
			<tr>
				<td  class="multiColumnLabel"> </td>
				<td class="multiColumnvalue"></td>
				<td  class="multiColumnLabel"> </td>
				<td class="multiColumnvalue"></td>
			</tr>
			<tr>
				<td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.report.type']" />:</td>
				<td><xsl:value-of select="$message[@id='xslt_messages.recipient.level']" /></td>
				<td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.recipients']" />:</td>
				<td><xsl:value-of select="count(./Recipients/Recipient)"/></td>
			</tr>
			<tr>
				<td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.requested.by']" />: </td>
				<td><xsl:value-of select="/Report/Request/User"/>&#32;(<xsl:call-template name="FormatDate">
	              <xsl:with-param name="DateTime">
	                <xsl:value-of select="@on"/>
	              </xsl:with-param>
              	  <xsl:with-param name="noHour">true</xsl:with-param>
              	  <xsl:with-param name="noMinute">true</xsl:with-param>
              	  <xsl:with-param name="noSecond">true</xsl:with-param>
	            </xsl:call-template>)			
	            </td>	
				<td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.failed.deliveries']" />:</td>
				<td>
					<xsl:value-of select="@failed"/>
				</td>
			</tr>
		</table>
	</div>
  </xsl:template>
  <xsl:template name="ReportSummary">
  	<div class="sectionHeader"><xsl:value-of select="$message[@id='xslt_messages.touchpoint.delivery.report']" />- <xsl:value-of select="//Report/@name" /></div>
    <div class="sectionContent">
      <table width="100%" cellspacing="0" cellpadding="0" border="0">
	    <tr>
			<td  class="multiColumnLabel"> </td>
			<td class="multiColumnvalue"></td>
			<td  class="multiColumnLabel"> </td>
			<td class="multiColumnvalue"></td>
		</tr>
        <tr>
          <td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.requested.by']" />:</td>
          <td>
            <xsl:value-of select="/Report/Request/User"/>&#32;(<xsl:call-template name="FormatDate">
              <xsl:with-param name="DateTime">
                <xsl:value-of select="/Report/Request/RequestDate"/>
              </xsl:with-param>
              <xsl:with-param name="noHour">true</xsl:with-param>
              <xsl:with-param name="noMinute">true</xsl:with-param>
              <xsl:with-param name="noSecond">true</xsl:with-param>
            </xsl:call-template>)
          </td>
          <td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.date.range']" />:</td>
          <td>
             <xsl:call-template name="FormatDate">
              <xsl:with-param name="DateTime">
                <xsl:value-of select="/Report/Request/FromDate"/>
              </xsl:with-param>
              <xsl:with-param name="noHour">false</xsl:with-param>
              <xsl:with-param name="noMinute">false</xsl:with-param>
              <xsl:with-param name="noSecond">false</xsl:with-param>
            </xsl:call-template> ~ <xsl:call-template name="FormatDate">
              <xsl:with-param name="DateTime">
                <xsl:value-of select="/Report/Request/ToDate"/>
              </xsl:with-param>
              <xsl:with-param name="noHour">false</xsl:with-param>
              <xsl:with-param name="noMinute">false</xsl:with-param>
              <xsl:with-param name="noSecond">false</xsl:with-param>
            </xsl:call-template>
          </td>
        </tr>
      </table>
    </div>
    <br />
  </xsl:template>
</xsl:stylesheet>