<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

  <xsl:include href="reportStyleTemplate.xsl"/>
  
  <xsl:key name="selectionkey" match="Selection" use="@id"/>
  <xsl:template match="/">
  <html>
  	<xsl:call-template name="ReportHead"/>
  		<body>
    	<xsl:call-template name="ReportContents" />
    	<xsl:call-template name="ReportSummary" />
    	<xsl:call-template name="SmartTextDetail" />
    	</body>
	</html>
  </xsl:template>
  <xsl:template name="ReportContents">
    <div class="sectionHeader">
      <a name="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.contents']" /></a>
    </div>
    <div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <a href="#section_A"><xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
      </div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <a href="#section_B"><xsl:value-of select="$message[@id='xslt_messages.details']" /></a>
      </div>
    </div>
    <br />
  </xsl:template>
  <xsl:template name="ReportSummary">
    <div class="sectionHeader">
      <a class="sectionHeaderPlainLink" name="section_A" href="#contents"><xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
    </div>
    <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.request.overview']" /></div>
    <xsl:apply-templates select="//SmartTextAudit" />
    <div class="sectionSubHeader">
      <table class="reportContentTable">
      <tr>
          <td class="tableHeader">
            <xsl:value-of select="$message[@id='xslt_messages.ID']" />
          </td>
          <td class="tableHeader">
            <xsl:value-of select="$message[@id='xslt_messages.ACTIVE']" />
          </td>
          <td class="tableHeader">
            <xsl:value-of select="$message[@id='xslt_messages.WORKING.COPY']" />
          </td>
        </tr>
    <xsl:for-each select="//SmartTextAudit/SmartText">
          <xsl:variable name="messageindex"><xsl:value-of select="./@id"/></xsl:variable>
          <tr>
            <td>
              <xsl:value-of select="@externalid"/>
            </td>
            <td>
              <xsl:for-each select="Version">
                <xsl:if test="@status!='Working Copy'">
                  <a>
                    <xsl:attribute name="href">#ImageIndex_a</xsl:attribute>
                    <xsl:value-of select="Name"/>
                  </a>
                </xsl:if>
              </xsl:for-each>
            </td>
            <td>
              <xsl:for-each select="Version">
                <xsl:if test="@status='Working Copy'">
                  <a>
                    <xsl:attribute name="href">#ImageIndex_w</xsl:attribute>
                    <xsl:value-of select="Name"/>
                  </a>
                </xsl:if>
              </xsl:for-each>
            </td>
          </tr>
        </xsl:for-each>
        </table>
    </div>
  </xsl:template>
  <xsl:template match="//SmartTextAudit">
  <div class="sectionContent">
      <table class="reportContentTable">
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.export.version']" />: </td>
          <td>
            <xsl:value-of select="./@ver"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested.by']" />: </td>
          <td>
            <xsl:value-of select="./Metadata/User"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested']" />: </td>
          <td>
            <xsl:call-template name="FormatDate">
              <xsl:with-param name="DateTime">
                <xsl:value-of select="./Metadata/RequestDate"/>
              </xsl:with-param>
            </xsl:call-template>            
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.report.type']" />: </td>
          <td>
            <xsl:value-of select="./Metadata/ReportType" />
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.instance']" />:</td>
          <td class="singleColumnValue"><xsl:value-of select="./Metadata/InstanceName"/></td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.system.default.language']" />:</td>
          <td class="singleColumnValue"><xsl:value-of select="./Metadata/SystemDefaultLocale"/></td>
        </tr>
      </table>
    </div>
  </xsl:template>
  <xsl:template name="SmartTextDetail">
    <div class="sectionHeader">
      <a name="section_B" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <xsl:value-of select="$message[@id='xslt_messages.details']" /></a>
    </div>
    <xsl:apply-templates select="//SmartTextAudit/SmartText" />
  </xsl:template>
  
  <!-- Smart Text Templates below -->
  <xsl:template match="//SmartTextAudit/SmartText">
    <xsl:param name="suppressed">false</xsl:param>
    <xsl:for-each select="Version">
      <xsl:variable name="versiontype">
        <xsl:if test="@status='Working Copy'">
          <xsl:value-of select="'_w'"/>
        </xsl:if>
        <xsl:if test="@status!='Working Copy'">
          <xsl:value-of select="'_a'"/>
        </xsl:if>
      </xsl:variable>
      <xsl:variable name="imageid">
        <xsl:value-of select="../@id"/>
      </xsl:variable>
      <xsl:variable name="imagetype">
        <xsl:value-of select="../@type"/>
      </xsl:variable>
      <!-- Detail START -->
      <div class="sectionSubHeader">
      	<a>
          <xsl:attribute name="class">plainLink</xsl:attribute>
          <xsl:attribute name="name">ImageIndex<xsl:value-of select="$versiontype"/></xsl:attribute>
          <xsl:attribute name="href">#ImageIndex<xsl:value-of select="$versiontype"/></xsl:attribute>
          <xsl:value-of select="Name"/> <xsl:if test="@status='Working Copy'"> (<xsl:value-of select="$message[@id='xslt_messages.working.copy']" />)</xsl:if><xsl:if test="@status!='Working Copy'"> (<xsl:value-of select="$message[@id='xslt_messages.active']" />)</xsl:if>
        </a>
      </div>
      <div class="sectionContent">
        <table class="reportContentTable">
          <tr>
            <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.type']" />:</td>
            <td class="doubleColumnValue">
              <xsl:variable name="type">
                <xsl:call-template name="capitalize">
                  <xsl:with-param name="string" select="../@type"/>
                </xsl:call-template>
              </xsl:variable>
              <xsl:value-of select="$type"/>
            </td>
            <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.assigned.to']" />:</td>
            <td class="doubleColumnValue"><div class="contentWidth"><xsl:value-of select="../@assignedto"/></div></td>
          </tr>
          <tr>
            <xsl:if test="(Description)">
              <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.description']" />:</td>
              <td colspan="3" class="doubleColumnValue"><div class="contentWidth"><xsl:value-of select="Description"/></div></td>
            </xsl:if>
          </tr>
          <tr>
            <xsl:if test="(Metatags)">
              <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.metatags']" />:</td>
              <td colspan="3" class="doubleColumnValue"><div class="contentWidth"><xsl:value-of select="Metatags"/></div></td>
            </xsl:if>
          </tr>
          <tr>
            <xsl:if test="(Comments)">
              <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.comments']" />:</td>
              <td colspan="3" class="doubleColumnValue"><div class="contentWidth"><xsl:value-of select="Comments"/></div></td>
            </xsl:if>
          </tr>
          <xsl:if test="count(//SmartTextAudit/ReferenceData/Touchpoints/Touchpoint) > 0">
	        <tr>
	          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.touchpoints']" />: </td>
	          <td colspan="3" class="doubleColumnValue"><div class="contentContainer">
	          <xsl:for-each select="//SmartTextAudit/ReferenceData/Touchpoints/Touchpoint">
	          <div class="blockItem" style="padding-top: 0px; padding-bottom: 0px; background-color: #f5f5f5;">
                <div style="display: inline-block; padding: 4px;"><xsl:value-of select="Name"/></div>
              </div>
	          </xsl:for-each>
	          </div>
	          </td>
	        </tr>
	       </xsl:if>
        </table>
        <div class="spacer"></div>
        <table class="reportContentTable">
          <xsl:if test="( (Content) or (Contents) or (Selections) )">
            <tr>
              <xsl:variable name="backhref">smartextSelectionIndex_<xsl:value-of select="$imageid"/><xsl:value-of select="$versiontype"/></xsl:variable>
              <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.content']" />:</td>
              <td class="singleColumnValue">
                <xsl:if test="$imagetype='Dynamic'">
                  <div>
                    <b>
                      <a class="plainLink">
                        <xsl:attribute name="name"><xsl:value-of select="$backhref"/></xsl:attribute>
                        <xsl:value-of select="$message[@id='xslt_messages.variants']" />
                      </a>
                    </b>
                  </div>
                </xsl:if>
                <xsl:choose>
                  <xsl:when test="$suppressed='true'">
                    <div class="contentContainer">
                      <i><xsl:value-of select="$message[@id='xslt_messages.content.suppressed']" /></i>
                    </div>
                  </xsl:when>
                  <xsl:otherwise>
                    <xsl:apply-templates mode="TreeView" select="Selections/Selection">
                      <xsl:with-param name="versiontype" select="$versiontype"/>
                    </xsl:apply-templates>
                    <xsl:for-each select="Contents/Content">
                      <div>
                        <xsl:variable name="locale">
                          <xsl:call-template name="translatelang">
                            <xsl:with-param name="code" select="@locale"/>
                          </xsl:call-template>
                        </xsl:variable>
                        <xsl:value-of select="$locale"/>
                      </div>
                      <div class="contentContainer">
			            <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.system.language']" /></i></xsl:if>
                        <xsl:value-of disable-output-escaping="yes" select="." />
                      </div>
                    </xsl:for-each>
                    <xsl:apply-templates mode="imagelibrary" select="Selections/Selection">
                      <xsl:with-param name="backhref" select="$backhref"/>
                      <xsl:with-param name="versiontype" select="$versiontype"/>
                    </xsl:apply-templates>
                  </xsl:otherwise>
                </xsl:choose>
              </td>
            </tr>
          </xsl:if>
        </table>
        <div class="spacer"></div>
        <xsl:if test="(Approvals)">
          <table class="reportContentTable">
            <tr>
              <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.approvals']" />:</td>
            </tr>
          </table>
          <xsl:apply-templates select="Approvals/ApprovalHistory" />
        </xsl:if>
      </div>
      <!-- MESSAGE END -->
    </xsl:for-each>
  </xsl:template>
  <xsl:template match="Approvals/ApprovalHistory">
    <xsl:choose>    
    <xsl:when test="./Descript">
    <table class="reportContentTable">
      <tr>
        <td>
          <xsl:if test="Descript">
            <table class="outsideTable" width="100%">
              <tr>
                <td class="tableHeader" width="25%" ><xsl:value-of select="$message[@id='xslt_messages.state']" /></td>
                <td class="tableHeader" width="14%" ><xsl:value-of select="$message[@id='xslt_messages.action']" /></td>
                <td class="tableHeader" width="17%" ><xsl:value-of select="$message[@id='xslt_messages.date']" /></td>
                <td class="tableHeader" width="12%" ><xsl:value-of select="$message[@id='xslt_messages.user']" /></td>                
                <td class="tableHeader" width="12%" ><xsl:value-of select="$message[@id='xslt_messages.assigned.to']" /></td>
                <td class="tableHeader" width="20%" ><xsl:value-of select="$message[@id='xslt_messages.notes']" /></td>
              </tr>
              <xsl:for-each select="Descript">
                <tr>
                  <td>
                    <xsl:value-of select="."/>
                  </td>
                  <td>
                    <xsl:value-of select="./@action"/>
                  </td>
                  <td>
                    <xsl:call-template name="FormatDate">
                      <xsl:with-param name="DateTime">
                        <xsl:value-of select="./@date" />
                      </xsl:with-param>
                	  <xsl:with-param name="noHour">true</xsl:with-param>
                	  <xsl:with-param name="noMinute">true</xsl:with-param>                       
                    </xsl:call-template>
                  </td>
                  <td>
                    <xsl:value-of select="./@user"/>
                  </td>                  
                  <td>
                    <xsl:value-of select="./@assignedTo"/>
                  </td>
                  <td>
                    <xsl:value-of select="./@notes"/>
                  </td>                  
                </tr>
              </xsl:for-each>
            </table>
          </xsl:if>
        </td>
      </tr>
    </table>
    </xsl:when>
    <xsl:otherwise>
    <table class="reportContentTable">
      <tr>
        <td width="14%"></td>
        <td class="outsideTable" ><xsl:value-of select="$message[@id='xslt_messages.no.events.for.model']" /></td>
        <td width="16%"></td> 
      </tr>
     </table>   
     <br />
    </xsl:otherwise>
    </xsl:choose>
  </xsl:template>
  <xsl:template match="Selection" mode="TPTreeView">
    <ul>
      <li>
        <xsl:choose>
          <xsl:when test="@id=/SmartTextAudit/SmartText/Selections/@selectorrefid">
            <a href="#section_touchpoint_messages"><xsl:value-of select="Name"/></a>
          </xsl:when>
          <xsl:otherwise>
            <xsl:value-of select="Name"/>
          </xsl:otherwise>
        </xsl:choose>
      </li>
      <xsl:apply-templates select="Selection" mode="TPTreeView"/>
    </ul>
  </xsl:template>
  <xsl:template match="Selections" mode="selectiontree">
    <div class="sectionContent">
      <xsl:for-each select="Selections/Selection">
        <xsl:value-of select="../../Name"/>
        <xsl:apply-templates select="." mode="TPTreeView"/>
      </xsl:for-each>
    </div>
  </xsl:template>
  <!--  Write out selection tree -->
  <xsl:template mode="TreeView" match="Selection">
    <xsl:param name="versiontype"/>
    <xsl:variable name="selectId">
      <xsl:choose>
        <xsl:when test="Name='Default'">
          <xsl:value-of select="'def'"/>
        </xsl:when>
        <xsl:otherwise>
          <xsl:value-of select="@id"/>
        </xsl:otherwise>
      </xsl:choose>
    </xsl:variable>
    <ul>
	   <li>
	     <a>
	       <xsl:attribute name="href">#smartextSelectionIndex_<xsl:value-of select="$selectId"/><xsl:value-of select="$versiontype"/></xsl:attribute>
	       <xsl:value-of select="Name"/>
	     </a>
         <xsl:for-each select="Selection">
          <xsl:sort/>
              <xsl:apply-templates mode="TreeView" select=".">
                <xsl:with-param name="versiontype" select="$versiontype"/>
              </xsl:apply-templates>            
          </xsl:for-each>
      </li>
    </ul>
  </xsl:template>
  <xsl:template match="Selection" mode="imagelibrary">
    <xsl:param name="backhref"/>
    <xsl:param name="versiontype"/>
    <xsl:variable name="selectname">
      <xsl:value-of select="Name"/>
    </xsl:variable>
    <xsl:variable name="selectionId">
      <xsl:choose>
        <xsl:when test="Name='Default'">
          <xsl:value-of select="'def'"/>
        </xsl:when>
        <xsl:otherwise>
          <xsl:value-of select="@id"/>
        </xsl:otherwise>
      </xsl:choose>
    </xsl:variable>
    <div>
      <br/>
      <b>
        <a class="plainLink">
          <xsl:attribute name="name">smartextSelectionIndex_<xsl:value-of select="$selectionId"/><xsl:value-of select="$versiontype"/></xsl:attribute>
          <xsl:attribute name="href">#<xsl:value-of select="$backhref"/></xsl:attribute>
          <xsl:value-of select="$selectname"/>
          <xsl:choose>
            <xsl:when test="@status='WIP'">
              (<xsl:value-of select="$message[@id='xslt_messages.working.copy']" />)
            </xsl:when>
            <xsl:when test="@status='Active'">
              (<xsl:value-of select="$message[@id='xslt_messages.active']" />)
            </xsl:when>
          </xsl:choose>

        </a><br/>
        <xsl:if test="./DataValue">
           <xsl:value-of select="$message[@id='xslt_messages.date.values']" />: <xsl:value-of select= "./DataValue"/>
        </xsl:if>
      </b>
    </div>

    <xsl:for-each select="Contents/Content">
      <div>
        <xsl:variable name="locale">
          <xsl:call-template name="translatelang">
            <xsl:with-param name="code" select="@locale"/>
          </xsl:call-template>
        </xsl:variable>
        <xsl:value-of select="$locale"/>
      </div>
      <xsl:call-template name="imagedata">
        <xsl:with-param name="imagename" select="@imagename"/>
        <xsl:with-param name="uploaded" select="@uploaded"/>
      </xsl:call-template>
      <div class="contentContainer">
        <xsl:if test="@sameasdefaultlanguage='true'"><i><xsl:value-of select="$message[@id='xslt_messages.same.as.default.system.language']" /></i></xsl:if>
        <xsl:if test="@sameasparent='true'"><i><xsl:value-of select="$message[@id='xslt_messages.inherited']" /></i></xsl:if>
        <xsl:value-of disable-output-escaping="yes" select="." />
      </div>
    </xsl:for-each>

    <xsl:apply-templates select="Selection" mode="imagelibrary">
      <xsl:with-param name="backhref" select="$backhref"/>
      <xsl:with-param name="versiontype" select="$versiontype"/>
    </xsl:apply-templates>
  </xsl:template>
  <xsl:template name="imagedata">
    <xsl:param name="imagename"/>
    <xsl:param name="uploaded"/>

    <xsl:if test="string-length($imagename) > 0">
      <div class="contentContainer">
        <xsl:value-of select="$message[@id='xslt_messages.reference.name']" />: <xsl:value-of select="@imagename"/>
      </div>
    </xsl:if>
    <xsl:if test="string-length($uploaded) > 0 and $uploaded != 'unknown'">
      <xsl:variable name="uploaded">
        <xsl:call-template name="formdate">
          <xsl:with-param name="date" select="@uploaded"/>
        </xsl:call-template>
      </xsl:variable>
      <div class="contentContainer">
        <xsl:value-of select="$message[@id='xslt_messages.upload.date']" />: <xsl:value-of select="$uploaded"/>
      </div>
    </xsl:if>
    <xsl:if test="string-length($uploaded) > 0 and $uploaded = 'unknown'">
      <div class="contentContainer">
        <xsl:value-of select="$message[@id='xslt_messages.upload.date']" />: <xsl:value-of select="$message[@id='xslt_messages.unknown']" />
      </div>
    </xsl:if>
  </xsl:template>
  <!-- convert a date in the form yyyy-mm-dd to MMM dd, yyyy -->
  <xsl:template name="formdate">
    <xsl:param name="date"/>
    <xsl:variable name="len">
      <xsl:value-of select="string-length($date)"/>
    </xsl:variable>
    <xsl:variable name="yr">
      <xsl:value-of select="substring($date,1,4)" />
    </xsl:variable>
    <xsl:variable name="mo">
      <xsl:value-of select="substring($date,6,2)" />
    </xsl:variable>
    <xsl:variable name="dt">
      <xsl:value-of select="substring($date,9,2)" />
    </xsl:variable>
    <xsl:variable name="month">
      <xsl:choose>
        <xsl:when test="$mo = '01'">Jan</xsl:when>
        <xsl:when test="$mo = '02'">Feb</xsl:when>
        <xsl:when test="$mo = '03'">Mar</xsl:when>
        <xsl:when test="$mo = '04'">Apr</xsl:when>
        <xsl:when test="$mo = '05'">May</xsl:when>
        <xsl:when test="$mo = '06'">Jun</xsl:when>
        <xsl:when test="$mo = '07'">Jul</xsl:when>
        <xsl:when test="$mo = '08'">Aug</xsl:when>
        <xsl:when test="$mo = '09'">Sep</xsl:when>
        <xsl:when test="$mo = '10'">Oct</xsl:when>
        <xsl:when test="$mo = '11'">Nov</xsl:when>
        <xsl:when test="$mo = '12'">Dec</xsl:when>
      </xsl:choose>
    </xsl:variable>
    <xsl:if test="$len > 0">
      <xsl:value-of select="$month"/>&#160;
      <xsl:value-of select="$dt"/>,&#160;
      <xsl:value-of select="$yr"/>
    </xsl:if>
  </xsl:template>
  <!-- Translate a five letter locale code to a locale description -->
  <xsl:template name="translatelang">
    <xsl:param name="code"/>
    <xsl:choose>
      <xsl:when test="$code='en_us'">English (US)</xsl:when>
      <xsl:when test="$code='en_ca'">English (Canada)</xsl:when>
      <xsl:when test="$code='en_gb'">English (UK)</xsl:when>
      <xsl:when test="$code='en_au'">English (Australia)</xsl:when>
      <xsl:when test="$code='en_bz'">English (Belize)</xsl:when>
      <xsl:when test="$code='en_bs'">English (Caribbean)</xsl:when>
      <xsl:when test="$code='en_in'">English (India)</xsl:when>
      <xsl:when test="$code='en_ie'">English (Ireland)</xsl:when>
      <xsl:when test="$code='en_jm'">English (Jamaica)</xsl:when>
      <xsl:when test="$code='en_my'">English (Malaysia)</xsl:when>
      <xsl:when test="$code='en_nz'">English (New Zealand)</xsl:when>
      <xsl:when test="$code='en_ph'">English (Philippines)</xsl:when>
      <xsl:when test="$code='en_sg'">English (Singapore)</xsl:when>
      <xsl:when test="$code='en_za'">English (South Africa)</xsl:when>
      <xsl:when test="$code='en_tt'">English (Trinidad and Tobago)</xsl:when>
      <xsl:when test="$code='en_zw'">English (Zimbabwe)</xsl:when>
      <xsl:when test="$code='fr_fr'">French (France)</xsl:when>
      <xsl:when test="$code='fr_ca'">French (Canada)</xsl:when>
      <xsl:when test="$code='fr_be'">French (Belgium)</xsl:when>
      <xsl:when test="$code='fr_lu'">French (Luxembourg)</xsl:when>
      <xsl:when test="$code='fr_mc'">French (Monaco)</xsl:when>
      <xsl:when test="$code='fr_ch'">French (Switzerland)</xsl:when>
      <xsl:when test="$code='fr_nl'">French (Netherlands)</xsl:when>
      <xsl:when test="$code='de_de'">German (Germany)</xsl:when>
      <xsl:when test="$code='de_at'">German (Austria)</xsl:when>
      <xsl:when test="$code='de_li'">German (Liechtenstein)</xsl:when>
      <xsl:when test="$code='de_lu'">German (Luxembourg)</xsl:when>
      <xsl:when test="$code='de_ch'">German (Switzerland)</xsl:when>
      <xsl:when test="$code='it_it'">Italian (Italy)</xsl:when>
      <xsl:when test="$code='it_ch'">Italian (Switzerland)</xsl:when>
      <xsl:when test="$code='es_es'">Spanish (Spain)</xsl:when>
      <xsl:when test="$code='es_ar'">Spanish (Argentina)</xsl:when>
      <xsl:when test="$code='es_ve'">Spanish (Venezuela)</xsl:when>
      <xsl:when test="$code='es_bo'">Spanish (Bolivia)</xsl:when>
      <xsl:when test="$code='es_cl'">Spanish (Chile)</xsl:when>
      <xsl:when test="$code='es_co'">Spanish (Colombia)</xsl:when>
      <xsl:when test="$code='es_cr'">Spanish (Costa Rica)</xsl:when>
      <xsl:when test="$code='es_do'">Spanish (Dominican Republic)</xsl:when>
      <xsl:when test="$code='es_ec'">Spanish (Ecuador)</xsl:when>
      <xsl:when test="$code='es_sv'">Spanish (El Salvador)</xsl:when>
      <xsl:when test="$code='es_gt'">Spanish (Guatemala)</xsl:when>
      <xsl:when test="$code='es_hn'">Spanish (Honduras)</xsl:when>
      <xsl:when test="$code='es_mx'">Spanish (Mexico)</xsl:when>
      <xsl:when test="$code='es_ni'">Spanish (Nicaragua)</xsl:when>
      <xsl:when test="$code='es_pa'">Spanish (Panama)</xsl:when>
      <xsl:when test="$code='es_py'">Spanish (Paraguay)</xsl:when>
      <xsl:when test="$code='es_pe'">Spanish (Peru)</xsl:when>
      <xsl:when test="$code='es_pr'">Spanish (Puerto Rico)</xsl:when>
      <xsl:when test="$code='es_us'">Spanish (US)</xsl:when>
      <xsl:when test="$code='es_uy'">Spanish (Uruguay)</xsl:when>
      <xsl:when test="$code='af_za'">Afrikaans (South Africa)</xsl:when>
      <xsl:when test="$code='sq_al'">Albanian (Albania)</xsl:when>
      <xsl:when test="$code='eu_eu'">Basque (Basque)</xsl:when>
      <xsl:when test="$code='br_fr'">Breton (France)</xsl:when>
      <xsl:when test="$code='ca_ad'">Catalan (Catalan)</xsl:when>
      <xsl:when test="$code='da_dk'">Danish (Denmark)</xsl:when>
      <xsl:when test="$code='nl_be'">Dutch (Belgium)</xsl:when>
      <xsl:when test="$code='nl_nl'">Dutch (Netherlands)</xsl:when>
      <xsl:when test="$code='et_ee'">Estonian (Estonia)</xsl:when>
      <xsl:when test="$code='fo_fo'">Faroese (Faroe Islands)</xsl:when>
      <xsl:when test="$code='fi_fi'">Finnish (Finland)</xsl:when>
      <xsl:when test="$code='gl_gl'">Galician (Galician)</xsl:when>
      <xsl:when test="$code='is_is'">Icelandic (Iceland)</xsl:when>
      <xsl:when test="$code='ga_ie'">Irish (Ireland)</xsl:when>
      <xsl:when test="$code='lb_lu'">Luxembourgish (Luxembourg)</xsl:when>
      <xsl:when test="$code='no_nb'">Norwegian (Bokmal)</xsl:when>
      <xsl:when test="$code='no_nn'">Norwegian (Nynorsk)</xsl:when>
      <xsl:when test="$code='pt_br'">Portuguese (Brazil)</xsl:when>
      <xsl:when test="$code='pt_pt'">Portuguese (Portugal)</xsl:when>
      <xsl:when test="$code='gd_gb'">Scottish Gaelic (UK)</xsl:when>
      <xsl:when test="$code='sw_km'">Swahili (Comoros)</xsl:when>
      <xsl:when test="$code='sw_ke'">Swahili (Kenya)</xsl:when>
      <xsl:when test="$code='sw_tz'">Swahili (Tanzania)</xsl:when>
      <xsl:when test="$code='sw_ug'">Swahili (Uganda)</xsl:when>
      <xsl:when test="$code='sv_fi'">Swedish (Finland)</xsl:when>
      <xsl:when test="$code='sv_se'">Swedish (Sweden)</xsl:when>
      <xsl:when test="$code='wa_be'">Walloon (Belgium)</xsl:when>
      <xsl:when test="$code='wa_fr'">Walloon (France)</xsl:when>
      <xsl:when test="$code='hu_hu'">Hungarian (Hungary)</xsl:when>
      <xsl:when test="$code='pl_pl'">Polish (Poland)</xsl:when>
      <xsl:when test="$code='bs_ba'">Bosnian (Bosnia - Latin)</xsl:when>
      <xsl:when test="$code='hr_hr'">Croatian (Croatia)</xsl:when>
      <xsl:when test="$code='cs_cz'">Czech (Czech Republic)</xsl:when>
      <xsl:when test="$code='ro_ro'">Romanian (Romania)</xsl:when>
      <xsl:when test="$code='sr_sp'">Serbian (Serbia - Latin)</xsl:when>
      <xsl:when test="$code='sr_ba'">Serbian (Bosnia - Latin)</xsl:when>
      <xsl:when test="$code='sr_cs'">Serbian (Montenegro - Latin)</xsl:when>
      <xsl:when test="$code='sk_sk'">Slovak (Slovakia)</xsl:when>
      <xsl:when test="$code='sl_si'">Slovenian (Slovenia)</xsl:when>
      <xsl:when test="$code='zh_cn'">Chinese (China)</xsl:when>
      <xsl:when test="$code='zh_hk'">Chinese (Hong Kong S.A.R.)</xsl:when>
      <xsl:when test="$code='zh_mo'">Chinese (Macau S.A.R.)</xsl:when>
      <xsl:when test="$code='zh_sg'">Chinese (Singapore)</xsl:when>
      <xsl:when test="$code='zh_tw'">Chinese (Taiwan)</xsl:when>
      <xsl:when test="$code='ja_jp'">Japanese (Japan)</xsl:when>
      <xsl:when test="$code='ko_kr'">Korean (Korea)</xsl:when>
      <xsl:when test="$code='vi_vn'">Vietnamese (Vietnam)</xsl:when>
      <xsl:when test="$code='hk_hk'">Cantonese (Hong Kong S.A.R.)</xsl:when>
      <xsl:when test="$code='hk_mo'">Cantonese (Macau S.A.R.)</xsl:when>
      <xsl:when test="$code='il_ph'">Ilokano (Philippines)</xsl:when>
      <xsl:when test="$code='he_il'">Hebrew (Israel)</xsl:when>
      <xsl:when test="$code='ru_ru'">Russian (Russia)</xsl:when>
      <xsl:when test="$code='th_th'">Thai (Thailand)</xsl:when>
      <xsl:when test="$code='ta_in'">Tamil (India)</xsl:when>
      <xsl:when test="$code='hi_in'">Hindi (India)</xsl:when>
      <xsl:when test="$code='pa_in'">Punjabi (India)</xsl:when>
      <xsl:when test="$code='uk_ua'">Ukrainian (Ukraine)</xsl:when>
      <xsl:when test="$code='id_id'">Indonesian (Indonesia)</xsl:when>
      <xsl:when test="$code='ms_my'">Malay (Malaysia)</xsl:when>
      <xsl:when test="$code='ms_bn'">Malay (Brunei)</xsl:when>
      <xsl:when test="$code='ar_sa'">Arabic (Saudi Arabia)</xsl:when>
      <xsl:when test="$code='ar_ae'">Arabic (United Arab Emirates)</xsl:when>
      <xsl:when test="$code='ar_dz'">Arabic (Algeria)</xsl:when>
      <xsl:when test="$code='ar_bh'">Arabic (Bahrain)</xsl:when>
      <xsl:when test="$code='ar_eg'">Arabic (Egypt)</xsl:when>
      <xsl:when test="$code='ar_iq'">Arabic (Iraq)</xsl:when>
      <xsl:when test="$code='ar_jo'">Arabic (Jordan)</xsl:when>
      <xsl:when test="$code='ar_kw'">Arabic (Kuwait)</xsl:when>
      <xsl:when test="$code='ar_lb'">Arabic (Lebanon)</xsl:when>
      <xsl:when test="$code='ar_ly'">Arabic (Libya)</xsl:when>
      <xsl:when test="$code='ar_ma'">Arabic (Morocco)</xsl:when>
      <xsl:when test="$code='ar_om'">Arabic (Oman)</xsl:when>
      <xsl:when test="$code='ar_qa'">Arabic (Qatar)</xsl:when>
      <xsl:when test="$code='ar_sy'">Arabic (Syria)</xsl:when>
      <xsl:when test="$code='ar_tn'">Arabic (Tunisia)</xsl:when>
      <xsl:when test="$code='ar_ye'">Arabic (Yemen)</xsl:when>
    </xsl:choose>
  </xsl:template>
  <!-- Utility Templates below -->
  <!-- Capitalize first letter of a string -->
  <xsl:template name="capitalize">
    <xsl:param name="string"/>
    <xsl:param name="caps" select="true()"/>

    <xsl:if test="$caps">
      <xsl:value-of select="translate(substring($string,1,1),'abcdefghijklmnopqrstuvwxyz','ABCDEFGHIJKLMNOPQRSTUVWXYZ')"/>
      <xsl:value-of select="substring($string,2)"/>
    </xsl:if>
    <xsl:if test="not($caps)">
      <xsl:value-of select="$string"/>
    </xsl:if>
  </xsl:template>
  
  </xsl:stylesheet>