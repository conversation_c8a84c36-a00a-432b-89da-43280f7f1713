<?xml version="1.0" encoding="iso-8859-1"?>
<xsl:stylesheet version="2.0"
xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

  <xsl:variable name="message" select="document('xsltMessages.xml')/messages/message" />
  
  <xsl:template name="ReportHead">   
	<head>
	  <meta http-equiv="Content-Type" content="text/html" charset="utf-8" />
	    <title>Touchpoint <xsl:text disable-output-escaping="yes"><![CDATA[&#174;]]></xsl:text> - Audit Reports</title>
	    <style type="text/css"> 
	    /*body and table style should be same with the style in message*/
		body{
		    background-color: #ffffff;
		    color: #1A1A1A;
		    font: 82.5% "55 Helvetica Roman",Helvetica Roman,Helvetica,Arial,sans-serif;
		    text-align: left;
		    font-size: 13px;
		}
	    a {
	        font-size: 13px;
	    	color: #1a1a1a;					/*colorDarkText*/
	    	cursor: pointer;
	    }
	    ul {
			padding-left: 15px;
            list-style-type: circle;
        }
		/*Report style*/
		.blockItem {
			border: 1px #c0c6e4 solid;
			background-color: #e2e7ff;
			color: #414141;
			white-space: nowrap;
			font-family: 'Prinova Standard', Helvetica, Arial, sans-serif;
			padding: 2px 6px;
			-webkit-border-radius: 3px;  
		    -moz-border-radius: 3px;  
		    border-radius: 3px;
		    font-size: 11px;
		 	margin: 2px;
		 	display: inline-block;
		 	cursor: default;
		}
	    .outsideTable {
		      border: 1px solid #bbb;
		      padding: 0px 0px;	    
		}
		.nestedTableLevel0 {
		    border: 3px solid #bbb;
		    padding: 0px;
		    margin:0px;
		    width: 100%;
		}
		.nestedTableLevel1 {
		    border: 3px solid #a00;
		    padding: 0px;
		    margin:0px;
		    width: 100%;
		}
		.nestedTableLevel2 {
		    border: 3px solid #aa0;
		    padding: 0px;
		    margin:0px;
		    width: 100%;
		}
		.nestedTableLevel3 {
		    border: 3px solid #aaa;
		    padding: 0px;
		    margin:0px;
		    width: 100%;
		}
		.nestedTableLevel4 {
		    border: 3px solid #0aa;
		    padding: 0px;
		    margin:0px;
		    width: 100%;;
		}
		.reportContentTable{
		    width:100%; 
		    border: 0px;
		    cellpadding: 0px;
		    cellspacing: 0px;
		}
		.sectionHeader {
			font-size:18px;
		    font-weight: bold;
		    margin: 8px;
		    padding-left: 10px;
		}
		.sectionSubHeader {
		    font-size:13px;
		    font-weight: bold;
		    margin: 8px 0px 0px 8px;
		    padding-left: 22px;
		}
		.sectionContent {
		    font-size:13px;
		    padding: 5px 30px;		    
		}
		.noteContent{
		    color:black;
		    font-weight:normal;
		}
		.HighLigtContent{
		    color:blue;
		    font-weight:bolder;
		}
		.plainLink {
		    color:blue;
		    text-decoration: none;
		}
		.sectionHeaderPlainLink {
		    color:black;
		    text-decoration: none;
		    font-size:18px;
		}
		.tableHeader {
		    font-weight: bold;
		}	
		.coloredTable
		{
			border: 1px solid black;
		}
		.coloredTable td
		{
			border: 1px solid black;
		}
		.singleColumnLabel {
		    width: 15%;
		}
		.multiColumnLabel {
		    width: 16%;
		}
		.multiColumnvalue {
		    width: 34%;
		}
		.dataColumn {
		    width: 30%;
		}
		.level2 {
		    padding-left: 20px;
		}
		.level3 {
		    padding-left: 40px;
		}
        .contentContainer {
        	border: 1px solid #bbb; 
        	padding: 8px 12px; 
        	width: 550px;
        	position: relative;
        }
        .contentContainerLong {
        	border: 1px solid #bbb; 
        	padding: 8px 12px; 
        	position: relative;
        }
        td.currentStepContainer.taskStepDataCell .activeTaskContainer {
			opacity: 1;
		}
		.taskStepDataCell .activeTaskContainer {
			opacity: 0.5;
		}
        .taskStepDataCell {
			width: 200px;
			padding: 0px;
		}
		.taskStepContainer {
			border: 1px solid #eee; 
			padding: 7px; 
			-webkit-border-radius: 8px; 
			-moz-border-radius: 8px; 
			border-radius: 8px;
			min-height: 70px;
			background-color: #fafafa;
		}
		.activeTaskContainer {
			border: 1px #bdbdbd solid;
			background-color: #fff;
		}
		.taskAssigneeLabel {
			margin-top: 3px;
			margin-left: 3px;
			font-size: 11px;
		}
		.stepUsersContainer {
			padding-top: 5px;
		}
		div.userAction_rejected, div.userAction_approved, div.userAction_none {
			border: 1px #c3c3c3 solid;
		    border-radius: 3px;
		    font-weight: normal;
		    float: none;
		    display: inline-block;
		    font-size: 13px;
		    margin: 2px;
		    padding: 3px 6px;
		    white-space: nowrap;
		}
		.doubleColumnLabel {
			width: 15%;
		}
		.doubleColumnValue {
			width: 35%;
		}
		.spacer {
			height: 8px;
		}
		.singleLineItem	{
			padding: 2px 0px;
		}
		.contentWidth {
            width: 500px;
		}
		.coloredTable{
            width:100%;
            border-collapse:collapse;
            vertical-align: bottom;
            border: 0px;
        }
        .coloredTable thead{
        	font-weight: bold;
            border: 1px solid #95B3D7;  
            text-align:center;
            background-color:#95B3D7;
            color:#FFFFFF;
            padding:2px 6px 2px 3px;
        }
        .coloredTable thead colHeader{
            border: 0px;  
        }
        .coloredTable tbody{
            border: 1px solid #B1B1B1;
            text-align:right;
            background-color:#FFFFFF;
            color:#000000;
        }
        .coloredTable col{
            border: 1px solid #B1B1B1;
        }
        .coloredTable td{
            border: 0px;
            padding-right:6px;
            padding-left:3px;
        }
        .coloredTable th{
            border: 1px solid #95B3D7;  
        }
        .logDisplay {
            height: 250px;
            scroll: auto;
            overflow:scroll;
            border: 0px solid #B1B1B1;
            padding: 8px;
            margin: 0px;
        }
        
		.mceHorizontalGuide, .mceVerticalGuide {
			position: absolute;
			z-index: 0;
		}
		.mceHorizontalGuide {
			left: 0px;	
		}
		.mceVerticalGuide {
			top: 0px;		
		}
		.mceGuideContainer {
			position: absolute;
			top: 0px;
			left: 0px;
			z-index: -1;
		}
	    </style>
	</head>
  </xsl:template>
    <xsl:template name="FormatDate">
    <xsl:param name="DateTime" />
    <xsl:param name="noHour" />
    <xsl:param name="noMinute" />
    <xsl:param name="noSecond" />
    <xsl:choose>
    <xsl:when test="$DateTime != ''"> 
    <!-- input date format: 2011-04-20T10:41:31.031-04:00 -->
    <!-- new date format Apr 19,2011 11:02-->
    <xsl:variable name="sYear">
      <xsl:value-of select="substring($DateTime,1,4)" />
    </xsl:variable>
    <xsl:variable name="sMonth">
      <xsl:value-of select="substring($DateTime,6,2)" />
    </xsl:variable>  
    <xsl:variable name="sDay">
      <xsl:value-of select="substring($DateTime,9,2)" />
    </xsl:variable>
    <xsl:variable name="sHour">
      <xsl:value-of select="substring($DateTime,12,2)" />
    </xsl:variable>  
    <xsl:variable name="sMinute">
      <xsl:value-of select="substring($DateTime,15,2)" />
    </xsl:variable> 
    <xsl:variable name="sSecond">
      <xsl:value-of select="substring($DateTime,18,2)" />
    </xsl:variable>
    <xsl:variable name="sMon">
      <xsl:choose>
        <xsl:when test="$sMonth = '01'">Jan </xsl:when>
        <xsl:when test="$sMonth = '02'">Feb </xsl:when>
        <xsl:when test="$sMonth = '03'">Mar </xsl:when>
        <xsl:when test="$sMonth = '04'">Apr </xsl:when>
        <xsl:when test="$sMonth = '05'">May </xsl:when>
        <xsl:when test="$sMonth = '06'">Jun </xsl:when>
        <xsl:when test="$sMonth = '07'">Jul </xsl:when>
        <xsl:when test="$sMonth = '08'">Aug </xsl:when>
        <xsl:when test="$sMonth = '09'">Sep </xsl:when>
        <xsl:when test="$sMonth = '10'">Oct </xsl:when>
        <xsl:when test="$sMonth = '11'">Nov </xsl:when>
        <xsl:when test="$sMonth = '12'">Dec </xsl:when>
      </xsl:choose>
    </xsl:variable>
    <xsl:value-of select="$sYear"/>
    <xsl:value-of select="'-'"/>
    <xsl:value-of select="$sMonth"/>
    <xsl:value-of select="'-'"/>
    <xsl:value-of select="$sDay"/>
    <xsl:value-of select="' '"/>
    <xsl:if test="$noHour='true'">
    	<xsl:value-of select="$sHour"/>
    </xsl:if>
    <xsl:if test="$noMinute='true'">
    	<xsl:value-of select="':'"/>
    	<xsl:value-of select="$sMinute"/>
    </xsl:if>
    <xsl:if test="$noSecond='true'">
	  	<xsl:value-of select="':'"/>
	  	<xsl:value-of select="$sSecond"/>
    </xsl:if>
    </xsl:when>
    <xsl:otherwise>
    	Unspecified
    </xsl:otherwise>
    </xsl:choose>
  </xsl:template>
  <xsl:template name="break">
      <xsl:param name="text" select="."/>
      <xsl:choose>
         <xsl:when test="contains($text, '&#xa;')">
            <xsl:value-of select="substring-before($text, '&#xa;')"/>
            <br/>
            <xsl:call-template name="break">
               <xsl:with-param 
                 name="text" 
                 select="substring-after($text, '&#xa;')"
        />
            </xsl:call-template>
         </xsl:when>
         <xsl:otherwise>
            <xsl:value-of select="$text"/>
         </xsl:otherwise>
      </xsl:choose>
   </xsl:template>
</xsl:stylesheet>