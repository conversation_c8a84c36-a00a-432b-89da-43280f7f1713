<?xml version="1.0" encoding="UTF-8" ?>
<xsl:stylesheet version="2.0"
    xmlns:xsl="http://www.w3.org/1999/XSL/Transform"  >

   <xsl:include href="reportStyleTemplate.xsl"/>
   <xsl:variable name="keyArray">
      <item>umh</item>
      <item>de</item>
      <item>license</item>
      <item>webserver</item>
      <item>jre</item>
      <item>jvm</item>
      <item>db</item>
      <item>os</item>
      <item>hardware</item>
   </xsl:variable>
   <xsl:template match="/">
      <html>
         <xsl:call-template name="ReportHead"/>
         <body>
            <xsl:call-template name="ReportSummary" />
            <xsl:call-template name="ReportDetails" />
         </body>
      </html>
   </xsl:template>
   <xsl:template name="ReportDetails">
      <div class="sectionHeader"><xsl:value-of select="$message[@id='xslt_messages.diagnostics.report']" /></div>
      <div class="sectionContent">
         <table class="coloredTable">
            <colgroup>
               <col class="col" />
               <col class="col" />
            </colgroup>
            <thead>
               <tr>
                  <th width="31%"></th>
                  <th />
               </tr>
            </thead>
            <xsl:variable name="displayCount" select="count(($keyArray)/item)" />            
            <xsl:call-template name="searchCategory">
               <xsl:with-param name ="searchCount">
                  <xsl:value-of select="$displayCount"/>
               </xsl:with-param>
               <xsl:with-param name ="searchIndex">1</xsl:with-param>
               <xsl:with-param name ="displayKey"><xsl:value-of select="($keyArray)/item[1]" /></xsl:with-param>
            </xsl:call-template>
            <thead>
               <tr>
                  <th colspan="2" style="text-align: left;" ><xsl:value-of select="$message[@id='xslt_messages.log']" /></th>
               </tr>
            </thead>
            <tbody>
               <tr style="background-color:#FFFFFF;">
                  <td style="text-align: left;padding-right:0px;" colspan="2" >
                     <xsl:value-of select="$message[@id='xslt_messages.access.log.save.select.package']" />
                  </td>
               </tr>
            </tbody>
         </table>
      </div>
   </xsl:template>
   <xsl:template name="searchCategory">
      <xsl:param name ="searchIndex" />
      <xsl:param name ="searchCount" />
      <xsl:param name ="displayKey" />
      <xsl:variable name="displayNextKey" select="($keyArray)/item[$searchIndex+1]" />
      <xsl:for-each select="//Diagnostics/Categories/Category">
         <xsl:call-template name="displayCategory">
            <xsl:with-param name="displayKey">
               <xsl:value-of select="$displayKey"/>
            </xsl:with-param>
         </xsl:call-template>
      </xsl:for-each>
      <xsl:if test="$searchIndex &lt; $searchCount">
         <xsl:call-template name="searchCategory">
            <xsl:with-param name ="searchCount">
               <xsl:value-of select="$searchCount"/>
            </xsl:with-param>
            <xsl:with-param name ="searchIndex">
               <xsl:value-of select="$searchIndex + 1"/>
            </xsl:with-param>
            <xsl:with-param name ="displayKey">
               <xsl:value-of select="$displayNextKey" />
            </xsl:with-param>
         </xsl:call-template>
      </xsl:if>
   </xsl:template>
   <xsl:template name="displayCategory">
      <xsl:param name="displayKey" />
      <xsl:if test="$displayKey = ./@key">
         <thead>
            <tr>
               <th colspan="2" style="text-align: left;">
                  <xsl:value-of select="./@label"/>
               </th>
            </tr>
         </thead>
         <tbody>
            <xsl:for-each select="Properties/Property">
               <xsl:variable name="backgroundColor">
                  <xsl:choose>
                     <xsl:when test="position() mod 2=1">background-color:#EEEEEE;</xsl:when>
                     <xsl:otherwise>background-color:#FFFFFF;</xsl:otherwise>
                  </xsl:choose>
               </xsl:variable>
               <tr style="{$backgroundColor} text-align: left;" >
               	 <xsl:choose>
               	 <xsl:when test="./@label">
               	 <td>
                     <xsl:value-of select="./@label" />
                  </td>
                  <td>
                     <xsl:value-of select="./@value" />
                  </td>
                  </xsl:when>
                  <xsl:otherwise>
                  <td colspan="2">
                     <xsl:value-of select="." />
                  </td>
                  </xsl:otherwise>
               	 </xsl:choose>                  
               </tr>
            </xsl:for-each>            
            <xsl:for-each select="Properties/SubCategories/SubCategory">
               <xsl:variable name="continueBackgroundSetting">
            	<xsl:value-of select="count(Properties/Property)+1" />
               </xsl:variable>
               <xsl:variable name="backgroundColor">
                  <xsl:choose>
                     <xsl:when test="$continueBackgroundSetting mod 2=1">background-color:#EEEEEE;</xsl:when>
                     <xsl:otherwise>background-color:#FFFFFF;</xsl:otherwise>
                  </xsl:choose>
               </xsl:variable>
               <tr style="{$backgroundColor} text-align: left;" >
                  <td>
                     <xsl:value-of select="./@label" />
                  </td>
                  <td />
               </tr>
               <xsl:for-each select="Property">
                  <xsl:variable name="subBackgroundColor">
                  <xsl:choose>
                     <xsl:when test="($continueBackgroundSetting+position()) mod 2=1">background-color:#EEEEEE;</xsl:when>
                     <xsl:otherwise>background-color:#FFFFFF;</xsl:otherwise>
                  </xsl:choose>
               </xsl:variable>
               	  <tr style="{$subBackgroundColor}">
                        <td style="text-align: left; width: 30%; padding-left:12px;"><xsl:value-of select="./@label" /></td>
                        <td style="text-align: left;"><xsl:value-of select="./@value" /></td>
                  </tr>
               </xsl:for-each>
               </xsl:for-each>
         </tbody>
      </xsl:if>
   </xsl:template>
   <xsl:template name="ReportSummary">
      <div class="sectionHeader"><xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></div>
      <div class="sectionContent">
         <table width="100%" cellspacing="0" cellpadding="0" border="0">
            <tr>
               <td class="multiColumnLabel"></td>
               <td class="multiColumnvalue"></td>
               <td class="multiColumnLabel"></td>
               <td class="multiColumnvalue"></td>
            </tr>
            <tr>
               <td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.date.run']" />:</td>
               <td>
                  <xsl:call-template name="FormatDate">
                     <xsl:with-param name="DateTime">
                        <xsl:value-of select="//Diagnostics/Request/RequestDate"/>
                     </xsl:with-param>
                     <xsl:with-param name="noHour">true</xsl:with-param>
                     <xsl:with-param name="noMinute">true</xsl:with-param>
                     <xsl:with-param name="noSecond">true</xsl:with-param>
                  </xsl:call-template>
               </td>
               <td class="tableHeader"></td>
               <td />
            </tr>
         </table>
      </div>
      <br />
   </xsl:template>
</xsl:stylesheet>