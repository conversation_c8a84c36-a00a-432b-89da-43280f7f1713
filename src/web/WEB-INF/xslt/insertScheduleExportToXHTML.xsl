<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

  <xsl:include href="reportStyleTemplate.xsl"/>

  <xsl:key name="insertkey" match="/InsertScheduleExport/ReferenceData/Inserts/Insert" use="@id"/>
  <xsl:key name="ratesheetkey" match="/InsertScheduleExport/ReferenceData/RateSheets/RateSheet" use="@id"/>
  <xsl:key name="insertschedulekey" match="/InsertScheduleExport/InsertSchedules/InsertSchedule" use="@id"/>
  <xsl:key name="otherinsertschedulekey" match="/InsertScheduleExport/ReferenceData/UnexportedInsertSchedules/UnexportedInsertSchedule" use="@id"/>

  <xsl:variable name="showtargeting">
    <xsl:value-of select="InsertScheduleExport/Metadata/Settings/@targeting"/>
  </xsl:variable>

  <xsl:template match="/">
   <html>
    <xsl:call-template name="ReportHead"/>
   	<body>
    <div class="sectionHeader"><a class="sectionHeaderPlainLink" name="contents"><xsl:value-of select="$message[@id='xslt_messages.contents']" /></a></div>
    
    <div class="sectionContent">
      <div class="singleLineItem"><xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <a href="#section_request_data"><xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a></div>
      <div class="singleLineItem"><xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <a href="#section_insert_schedules"><xsl:value-of select="$message[@id='xslt_messages.insert.schedules']" /></a></div>
      <div class="singleLineItem"><xsl:value-of select="$message[@id='xslt_messages.section']" /> C: <a href="#section_insert_data"><xsl:value-of select="$message[@id='xslt_messages.inserts.reference.data']" /></a></div>
      <div class="singleLineItem"><xsl:value-of select="$message[@id='xslt_messages.section']" /> D: <a href="#section_rate_sheet_data"><xsl:value-of select="$message[@id='xslt_messages.rate.sheet.reference.data']" /></a></div>
    </div>

    <div class="sectionHeader">
      <a name="section_request_data" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
    </div>
    <xsl:apply-templates select="InsertScheduleExport/Metadata"/>
    <xsl:apply-templates select="InsertScheduleExport/InsertSchedules" mode="TableListView" />

    <div class="sectionHeader">
      <a name="section_insert_schedules" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <xsl:value-of select="$message[@id='xslt_messages.insert.schedules']" /></a>
    </div>
    <xsl:apply-templates select="InsertScheduleExport/InsertSchedules"/>

    <div class="sectionHeader">
      <a name="section_insert_data" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> C: <xsl:value-of select="$message[@id='xslt_messages.inserts.reference.data']" /></a>
    </div>
    <xsl:apply-templates select="InsertScheduleExport/ReferenceData/Inserts"/>

    <div class="sectionHeader">
      <a name="section_rate_sheet_data" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> D: <xsl:value-of select="$message[@id='xslt_messages.rate.sheet.reference.data']" /></a>
    </div>
    <xsl:apply-templates select="InsertScheduleExport/ReferenceData/RateSheets"/>
    </body>
    </html>
  </xsl:template>



  <xsl:template match="Metadata">
    <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.request.overview']" /></div>

    <div class="sectionContent">
      <table class="reportContentTable">
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.export.version']" />:</td>
          <td><xsl:value-of select="/InsertScheduleExport/@ver"/></td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested.by']" />:</td>
          <td><xsl:value-of select="User"/></td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.request.date']" />:</td>
          <td>
            <xsl:variable name="requestdate">
              <xsl:call-template name="formdate">
                <xsl:with-param name="date" select="RequestDate"/>
              </xsl:call-template>
            </xsl:variable>
            <xsl:value-of select="$requestdate"/>
          </td>
        </tr>
      </table>
    </div>      

    <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.request.options']" /></div>

    <div class="sectionContent">
      <table class="reportContentTable">
        <tr>
          <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.date']" />:</td>
          <td class="doubleColumnValue">
            <xsl:variable name="fromdate">
              <xsl:call-template name="formdate">
                <xsl:with-param name="date" select="Settings/@from"/>
              </xsl:call-template>
            </xsl:variable>
            <xsl:value-of select="$fromdate"/>
          </td>
          <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.include.insert.targeting']" />:</td>
          <td class="doubleColumnValue">
            <xsl:choose>
              <xsl:when test="Settings/@targeting='true'"><xsl:value-of select="$message[@id='xslt_messages.yes']" /></xsl:when>
              <xsl:otherwise><xsl:value-of select="$message[@id='xslt_messages.no']" /></xsl:otherwise>
            </xsl:choose>
          </td>
        </tr>
      </table>
    </div>
  </xsl:template>



  <xsl:template match="RateSheets">
    <xsl:for-each select="RateSheet">
      <xsl:variable name="ratesheetid">
        <xsl:value-of select="@id"/>
      </xsl:variable>

      <div class="sectionSubHeader">
        <a href="#contents" class="plainLink">
          <xsl:attribute name="name">rateSheet_<xsl:value-of select="$ratesheetid"/></xsl:attribute>
          <xsl:value-of select="Name"/>
        </a>         
      </div>

      <div class="sectionContent">
        <table class="reportContentTable">
          <tr>
            <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.date.range']" />:</td>
            <td class="doubleColumnValue">
              <xsl:value-of select="$message[@id='xslt_messages.stating']" /> <xsl:call-template name="formdate">
                <xsl:with-param name="date" select="StartDate"/>
              </xsl:call-template>
            </td>

            <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.envelope']" />:</td>
            <td class="doubleColumnValue"><xsl:value-of select="Envelope/Name"/> (<xsl:value-of select="Envelope/Weight"/>&#160;<xsl:value-of select="Envelope/Weight/@unit"/>)</td>
          </tr>
          <tr>
            <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.description']" />:</td>
            <td colspan="3" class="doubleColumnValue"><xsl:value-of select="Description"/></td>
          </tr>
        </table>

        <div class="spacer"></div>

        <table class="reportContentTable">
          <tr>
            <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.thresholds']" />:</td>
            <td>
              <div class="contentContainer">
                <table class="reportContentTable">
                  <xsl:for-each select="Thresholds/Threshold">
                    <tr>
                      <td>
                        <xsl:value-of select="$message[@id='xslt_messages.less.than']" /> <xsl:value-of select="@weight"/>&#160;<xsl:value-of select="@weightUnit"/>, <xsl:value-of select="$message[@id='xslt_messages.for']" /> $<xsl:value-of select="@rate"/>
                      </td>
                    </tr>
                  </xsl:for-each>
                </table>
              </div>
            </td>
          </tr>
        </table>
      </div>
    </xsl:for-each>
  </xsl:template>



  <xsl:template match="Inserts">
    <xsl:for-each select="Insert">
      <xsl:variable name="insertid">
        <xsl:value-of select="@id"/>
      </xsl:variable>
      <xsl:variable name="externalid">
        <xsl:value-of select="ExternalId"/>
      </xsl:variable>

      <div class="sectionSubHeader">
        <a href="#contents" class="plainLink">
          <xsl:attribute name="name">insert_<xsl:value-of select="$insertid"/></xsl:attribute>
          <xsl:if test="string-length($externalid)>0">
            <xsl:value-of select="ExternalId"/>
          </xsl:if>
          <xsl:value-of select="Name"/>
        </a>
      </div>

      <div class="sectionContent">
        <table class="reportContentTable">
          <tr>
            <td class="doubleColumnLabel" style="vertical-align:top"><xsl:value-of select="$message[@id='xslt_messages.touchpoints']" />:</td>
            <td class="doubleColumnValue">
              <xsl:for-each select="Touchpoints/Touchpoint">
                <xsl:value-of select="Name"/><br />
              </xsl:for-each>
              <br/>
            </td>

            <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.current.state']" />:</td>
            <td class="doubleColumnValue"><xsl:value-of select="Status"/></td>
          </tr>
          <tr>
            <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.delivery']" />:</td>
            <td class="doubleColumnValue"><xsl:value-of select="Delivery"/></td>

            <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.weight']" />:</td>
            <td class="doubleColumnValue"><xsl:value-of select="Weight"/>&#160;<xsl:value-of select="Weight/@unit"/></td>
          </tr>
          <tr>
            <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.description']" />:</td>
            <td colspan="3" class="doubleColumnValue"><xsl:value-of select="Description"/></td>
          </tr>
        </table>
      </div>

      <div class="spacer"></div>

      <xsl:if test="$showtargeting='true'">
        <table class="reportContentTable">
          <tr>
            <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.targeting']" />:</td>
            <td>
              <div class="contentContainer">
                <xsl:for-each select="TargetCriteria">
                  <xsl:for-each select="InclusionsGroupA/TargetGroup">
                    <xsl:if test="position()=1"><xsl:value-of select="$message[@id='xslt_messages.include']" />:<br/></xsl:if>
                    &#160;&#160;&#160;&#160;<xsl:if test="position()!=1"><xsl:value-of select="../@relationship" /> </xsl:if><xsl:value-of disable-output-escaping="yes" select="."/><br/>
                    <!-- &#160;&#160;&#160;&#160;Target Group 1<br/>
                         &#160;&#160;&#160;&#160;OR Target Group 2<br/> -->
                  </xsl:for-each>
                  <xsl:for-each select="InclusionsGroupB/TargetGroup">
                    <xsl:if test="position()=1"><xsl:value-of select="$message[@id='xslt_messages.and.of.these.include']" />:<br/></xsl:if>
                    &#160;&#160;&#160;&#160;<xsl:if test="position()!=1"><xsl:value-of select="../@relationship" /> </xsl:if><xsl:value-of disable-output-escaping="yes" select="."/><br/>
                  </xsl:for-each>
                  <xsl:for-each select="Exclusions/TargetGroup">
                    <xsl:if test="position()=1"><xsl:value-of select="$message[@id='xslt_messages.exclude']" />:<br/></xsl:if>
                    &#160;&#160;&#160;&#160;<xsl:if test="position()!=1"><xsl:value-of select="../@relationship" /> </xsl:if><xsl:value-of disable-output-escaping="yes" select="."/><br/>
                  </xsl:for-each>
                  <xsl:if test="not(node())"><xsl:value-of select="$message[@id='xslt_messages.none']" /></xsl:if>
                </xsl:for-each>
                <xsl:if test="not(TargetCriteria)"><xsl:value-of select="$message[@id='xslt_messages.none']" /></xsl:if>
              </div>
            </td>
          </tr>
        </table>
      </xsl:if>
      <div class="spacer"></div>

      <table class="reportContentTable">
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.used.by']" />:</td>
          <td>
            <div class="contentContainer">
              <table class="reportContentTable">
                <xsl:for-each select="InsertSchedules/InsertSchedule">
                  <xsl:variable name="schedulerefid">
                    <xsl:value-of select="@refid"/>
                  </xsl:variable>
                  <xsl:variable name="exported">
                    <xsl:value-of select="@exported"/>
                  </xsl:variable>
                  <tr>
                    <xsl:choose>
                      <xsl:when test="$exported='true'">
                        <td>
                          <a>
                            <xsl:attribute name="href">#scheduleIndex_<xsl:value-of select="$schedulerefid"/></xsl:attribute>
                            <xsl:value-of select="key('insertschedulekey', $schedulerefid)/Name"/>
                          </a>
                        </td>
                        <td>
                          <xsl:variable name="startdate">
                            <xsl:call-template name="formdate">
                              <xsl:with-param name="date" select="key('insertschedulekey', $schedulerefid)/StartDate"/>
                            </xsl:call-template>
                          </xsl:variable>
                          <xsl:variable name="enddate">
                            <xsl:call-template name="formdate">
                              <xsl:with-param name="date" select="key('insertschedulekey', $schedulerefid)/EndDate"/>
                            </xsl:call-template>
                            <xsl:if test="string-length($startdate)=0 and string-length(key('insertschedulekey', $schedulerefid)/EndDate)!=0">
                              <xsl:value-of select="$message[@id='xslt_messages.indefinite']" />
                            </xsl:if>
                          </xsl:variable>

                          <xsl:if test="string-length($startdate)!=0 or string-length($enddate)!=0">
                            <xsl:value-of select="$startdate"/>&#160;-&#160;<xsl:value-of select="$enddate"/>
                          </xsl:if>
                        </td>
                      </xsl:when>
                      <xsl:when test="$exported='false'">
                        <td>
                          <xsl:value-of select="key('otherinsertschedulekey', $schedulerefid)/@name"/>
                        </td>
                        <td>
                          <xsl:value-of select="$message[@id='xslt_messages.unexported']" />
                        </td>
                      </xsl:when>
                    </xsl:choose>
                  </tr>
                </xsl:for-each>
              </table>
            </div>
          </td>
        </tr>
      </table>
    </xsl:for-each>
  </xsl:template>



  <xsl:template match="InsertSchedules">
    <xsl:for-each select="InsertSchedule">
      <xsl:variable name="scheduleid">
        <xsl:value-of select="@id"/>
      </xsl:variable>

      <xsl:variable name="startdate">
        <xsl:call-template name="formdate">
          <xsl:with-param name="date" select="StartDate"/>
        </xsl:call-template>
      </xsl:variable>

      <xsl:variable name="enddate">
        <xsl:call-template name="formdate">
          <xsl:with-param name="date" select="EndDate"/>
        </xsl:call-template>
        <xsl:if test="string-length(EndDate)=0 and string-length(StartDate)!=0">
          <xsl:value-of select="$message[@id='xslt_messages.indefinite']" />
        </xsl:if>
      </xsl:variable>

      <div class="sectionSubHeader">
        <a href="#scheduleIndex" class="plainLink">
          <xsl:attribute name="name">scheduleIndex_<xsl:value-of select="$scheduleid"/></xsl:attribute>
          <xsl:if test="string-length(ExternalId)>0">
            <xsl:value-of select="ExternalId"/>&#160;:&#160;
          </xsl:if>
          <xsl:value-of select="Name" />
          <xsl:if test="string-length($startdate)>0 or string-length($enddate)>0">
            &#160;(<xsl:value-of select="$startdate" /> - <xsl:value-of select="$enddate"/>)
          </xsl:if>
        </a>
      </div>

      <div class="sectionContent">
        <table class="reportContentTable">
          <tr>
            <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.touchpoint']" />:</td>
            <td class="doubleColumnValue">
              <xsl:value-of select="Touchpoint/Name"/>
            </td>
            <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.current.state']" />:</td>
            <td class="doubleColumnValue"><xsl:value-of select="Status"/></td>
          </tr>
          <tr>
            <td class="doubleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.description']" />:</td>
            <td colspan="3" class="doubleColumnValue"><xsl:value-of select="Description"/></td>
          </tr>
        </table>
      </div>

      <div class="spacer"></div>

      <table class="reportContentTable">
        <tr>

          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.bin.assignments']" />:</td>
          <td>
            <div class="contentContainer">
              <table class="reportContentTable">
                <tr>
                  <td class="tableHeader" width="10%">
                    <xsl:value-of select="$message[@id='xslt_messages.BIN']" />
                  </td>
                  <td class="tableHeader" width="45%">
                    <xsl:value-of select="$message[@id='xslt_messages.ASSIGNMENT']" />
                  </td>
                  <td class="tableHeader" width="45%">
                    <xsl:value-of select="$message[@id='xslt_messages.TIMING']" />
                  </td>
                </tr>

                <xsl:for-each select="BinAssignments/BinAssignment">
                  <tr>
                    <td width="10%">
                      <xsl:value-of select="@binnumber"/>
                    </td>
                    <td width="45%">
                      <xsl:variable name="insertid">
                        <xsl:value-of select="@insertrefid"/>
                      </xsl:variable>

                      <a>
                        <xsl:attribute name="href">#insert_<xsl:value-of select="$insertid"/></xsl:attribute>
                        <xsl:value-of select="key('insertkey', $insertid)/Name"/>
                      </a>
                    </td>

                    <td width="45%">
                      <xsl:variable name="insertstarttime">
                        <xsl:call-template name="formdate">
                          <xsl:with-param name="date" select="@from"/>
                        </xsl:call-template>
                      </xsl:variable>
                      <xsl:variable name="insertendtime">
                        <xsl:call-template name="formdate">
                          <xsl:with-param name="date" select="@to"/>
                        </xsl:call-template>
                        <xsl:if test="string-length(@to)=0 and string-length(@from)!=0">
                          <xsl:value-of select="$message[@id='xslt_messages.indefinite']" />
                        </xsl:if>
                      </xsl:variable>
                      
                      <xsl:choose>
                        <xsl:when test="string-length($insertstarttime)>0 and string-length($insertendtime)>0">
                          <xsl:value-of select="$insertstarttime"/> - <xsl:value-of select="$insertendtime"/>
                        </xsl:when>
                        <xsl:otherwise>
                          <xsl:value-of select="'None'"/>
                        </xsl:otherwise>
                      </xsl:choose>
                    </td>
                  </tr>
                </xsl:for-each>
              </table>
            </div>
          </td>
        </tr>
      </table>

      <div class="spacer"></div>

      <table class="reportContentTable">
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.rate.sheets']" />:</td>
          <td>
            <div class="contentContainer">
              <table class="reportContentTable">
                <xsl:for-each select="RateSheets/RateSheet">
                  <xsl:variable name="ratesheetid">
                    <xsl:value-of select="@ratesheetrefid"/>
                  </xsl:variable>
                  <tr>
                    <td>
                      <xsl:value-of select="$message[@id='xslt_messages.use']" /> 
                      <a>
                        <xsl:attribute name="href">#rateSheet_<xsl:value-of select="$ratesheetid"/></xsl:attribute>
                        <xsl:value-of select="key('ratesheetkey', $ratesheetid)/Name"/>
                      </a>
                    </td>
                    <td>
                      <xsl:choose>
                        <xsl:when test="@numberofsheets!=0">
                          <xsl:value-of select="$message[@id='xslt_messages.when.greater.than']" /> <xsl:value-of select="@numberofsheets"/> <xsl:value-of select="$message[@id='xslt_messages.sheets']" />
                        </xsl:when>
                        <xsl:when test="count(../RateSheet)=1">
                          <xsl:value-of select="$message[@id='xslt_messages.always']" />
                        </xsl:when>
                        <xsl:otherwise>
                          <xsl:value-of select="$message[@id='xslt_messages.otherwise']" />
                        </xsl:otherwise>
                      </xsl:choose>
                    </td>
                  </tr>
                </xsl:for-each>
              </table>
            </div>
          </td>
        </tr>
      </table>
    </xsl:for-each>
  </xsl:template>



  <xsl:template match="InsertSchedules" mode="TableListView">
    <div class="sectionSubHeader"><a name="scheduleIndex"><xsl:value-of select="$message[@id='xslt_messages.selected.insert.schedule.index']" /></a></div>

    <div class="sectionContent">
      <table class="reportContentTable">
        <tr>
          <td class="tableHeader" width="10%">
            <xsl:value-of select="$message[@id='xslt_messages.ID']" />
          </td>
          <td class="tableHeader" width="45%">
            <xsl:value-of select="$message[@id='xslt_messages.NAME']" />
          </td>
          <td class="tableHeader" width="45%">
            <xsl:value-of select="$message[@id='xslt_messages.DATE.RANGE']" />
          </td>
        </tr>

        <xsl:for-each select="InsertSchedule">
          <xsl:variable name="scheduleid"><xsl:value-of select="@id"/></xsl:variable>

          <tr>
            <td width="10%">
              <xsl:value-of select="ExternalId"/>
            </td>
            <td width="45%">
              <a>
                <xsl:attribute name="href">#scheduleIndex_<xsl:value-of select="$scheduleid"/></xsl:attribute>
                <xsl:value-of select="Name"/>
              </a>
            </td>
            <td width="45%">
              <xsl:variable name="startdate">
                <xsl:call-template name="formdate">
                  <xsl:with-param name="date" select="StartDate"/>
                </xsl:call-template>
              </xsl:variable>
              <xsl:variable name="enddate">
                <xsl:call-template name="formdate">
                  <xsl:with-param name="date" select="EndDate"/>
                </xsl:call-template>
                <xsl:if test="string-length(EndDate)=0 and string-length(StartDate)!=0">
                  <xsl:value-of select="$message[@id='xslt_messages.indefinite']" />
                </xsl:if>
              </xsl:variable>

              <xsl:if test="string-length($startdate)!=0 or string-length($enddate)!=0">
                <xsl:value-of select="$startdate"/>&#160;-&#160;<xsl:value-of select="$enddate"/>
              </xsl:if>
            </td>
          </tr>
        </xsl:for-each>
      </table>
    </div>

  </xsl:template>


  <!-- Utility Templates below -->
  <!-- Capitalize first letter of a string -->
  <xsl:template name="capitalize">
    <xsl:param name="string"/>
    <xsl:param name="caps" select="true()"/>

    <xsl:if test="$caps">
      <xsl:value-of select="translate(substring($string,1,1),'abcdefghijklmnopqrstuvwxyz','ABCDEFGHIJKLMNOPQRSTUVWXYZ')"/>
      <xsl:value-of select="substring($string,2)"/>
    </xsl:if>
    <xsl:if test="not($caps)">
      <xsl:value-of select="$string"/>
    </xsl:if>
  </xsl:template>

  <!-- convert a date in the form yyyy-mm-dd to MMM dd, yyyy -->
  <xsl:template name="formdate">
    <xsl:param name="date"/>
    <xsl:variable name="len">
      <xsl:value-of select="string-length($date)"/>
    </xsl:variable>
    <xsl:variable name="yr">
      <xsl:value-of select="substring($date,1,4)" />
    </xsl:variable>
    <xsl:variable name="mo">
      <xsl:value-of select="substring($date,6,2)" />
    </xsl:variable>
    <xsl:variable name="dt">
      <xsl:value-of select="substring($date,9,2)" />
    </xsl:variable>
    <xsl:variable name="month">
      <xsl:choose>
        <xsl:when test="$mo = '01'">Jan</xsl:when>
        <xsl:when test="$mo = '02'">Feb</xsl:when>
        <xsl:when test="$mo = '03'">Mar</xsl:when>
        <xsl:when test="$mo = '04'">Apr</xsl:when>
        <xsl:when test="$mo = '05'">May</xsl:when>
        <xsl:when test="$mo = '06'">Jun</xsl:when>
        <xsl:when test="$mo = '07'">Jul</xsl:when>
        <xsl:when test="$mo = '08'">Aug</xsl:when>
        <xsl:when test="$mo = '09'">Sep</xsl:when>
        <xsl:when test="$mo = '10'">Oct</xsl:when>
        <xsl:when test="$mo = '11'">Nov</xsl:when>
        <xsl:when test="$mo = '12'">Dec</xsl:when>
      </xsl:choose>
    </xsl:variable>
    <xsl:if test="$len > 0">
      <xsl:value-of select="$month"/>&#160;
      <xsl:value-of select="$dt"/>,&#160;
      <xsl:value-of select="$yr"/>
    </xsl:if>
  </xsl:template>

  <!-- Translate a two letter language code to a language description -->
  <xsl:template name="translatelang">
    <xsl:param name="code"/>
    <xsl:choose>
      <xsl:when test="$code='en'">English</xsl:when>
      <xsl:when test="$code='fr'">French</xsl:when>
      <xsl:when test="$code='es'">Spanish</xsl:when>
    </xsl:choose>
  </xsl:template>
</xsl:stylesheet>
