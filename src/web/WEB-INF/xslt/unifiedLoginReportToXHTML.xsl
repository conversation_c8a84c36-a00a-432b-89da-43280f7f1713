<?xml version="1.0" encoding="UTF-8" ?>
<xsl:stylesheet version="2.0"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:msxsl="urn:schemas-microsoft-com:xslt"
                xmlns:ConstantsSetting="http://ConstantsSetting.com" exclude-result-prefixes="ConstantsSetting"
>

  <xsl:include href="reportStyleTemplate.xsl"/>
  
  <xsl:template match="/">
    <xsl:call-template name="ReportDefinition" />
    <xsl:call-template name="ReportContents" />
    <xsl:call-template name="ReportSummary" />
    <xsl:call-template name="ListOfPods" />
    <xsl:call-template name="DomainsMapping" />
  </xsl:template>

  <xsl:template name="ReportDefinition">
	<html>
	   <xsl:call-template name="ReportHead"/>
	   <body>
	   </body>
	</html>
  </xsl:template>
  
  <xsl:template name="ReportContents">
    <div class="sectionHeader">
      <a class="sectionHeaderPlainLink" name="contents"><xsl:value-of select="$message[@id='xslt_messages.contents']" /></a>
    </div>
    <br />
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> A:
        <a href="#section_A"><xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
      </div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <a href="#section_B"><xsl:value-of select="$message[@id='xslt_messages.list.of.pods']" /></a>
      </div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> C: <a href="#section_C"><xsl:value-of select="$message[@id='xslt_messages.mapping.of.domains']" /></a>
      </div>
   </xsl:template>
  
  <xsl:template name="ListOfPods">
    <div class="sectionHeader">
      <a class="sectionHeaderPlainLink" href="#contents" name="section_B"><xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <xsl:value-of select="$message[@id='xslt_messages.list.of.pods']" /></a>
    </div>
    <xsl:call-template name="ListOf">
    </xsl:call-template>
  </xsl:template>
      
 <xsl:template name="ListOf">
    <div class="sectionContent">
    <xsl:choose>
    <xsl:when test="//UnifiedLoginReport/Pods/Pod">
      <table class="coloredTable">
	    	<colgroup>
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
            </colgroup>
           <thead>
              <tr>
                 <th style="text-align: left;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.name']" /></th>
                 <th style="text-align: left;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.pod.description']" /></th>
                 <th style="text-align: left;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.url']" /></th>
                 <th style="text-align: left;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.pod.type']" /></th>
                 <th style="text-align: left;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.status']" /></th>
                 <th style="text-align: left;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.state']" /></th>
                 <th style="text-align: left;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.active']" /></th>
                 <th style="text-align: left;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.master.schema.name']" /></th>
              </tr>
           </thead>
        <tbody>          
          <xsl:for-each select="//UnifiedLoginReport/Pods/Pod">
          <xsl:variable name="backgroundColor">
            <xsl:choose>
            <xsl:when test="position() mod 2=1">background-color:#EEEEEE;</xsl:when>
            <xsl:otherwise>background-color:#FFFFFF;</xsl:otherwise>
            </xsl:choose>
          </xsl:variable>
          <tr style="{$backgroundColor}; text-align: left;" >
            <td><xsl:value-of select="Name"/></td>
            <td><xsl:value-of select="PodDescription"/></td>
            <td><xsl:value-of select="URL"/></td>
            <td><xsl:value-of select="PodType"/></td>
            <td><xsl:value-of select="Status"/></td>
            <td><xsl:value-of select="State"/></td>
            <td><xsl:value-of select="Active"/></td>
            <td><xsl:value-of select="MasterSchemaName"/></td>
          </tr>
          </xsl:for-each>
        </tbody>
      </table>
       </xsl:when>
    </xsl:choose>
    </div>
  </xsl:template>
  
   <xsl:template name="DomainsMapping">
    <div class="sectionHeader">
      <a class="sectionHeaderPlainLink" href="#contents" name="section_C"><xsl:value-of select="$message[@id='xslt_messages.section']" /> C: <xsl:value-of select="$message[@id='xslt_messages.mapping.of.domains']" /></a>
    </div>
    <xsl:call-template name="DomainMapping">
    </xsl:call-template>
  </xsl:template>
      
 <xsl:template name="DomainMapping">
    <div class="sectionContent">
    <xsl:choose>
    <xsl:when test="//UnifiedLoginReport/DomainsMapping/DomainMapping">
      <table class="coloredTable">
	    	<colgroup>
                  <col class="col" />
                  <col class="col" />
                  <col class="col" />
            </colgroup>
           <thead>
              <tr>
                 <th style="text-align: left;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.name']" /></th>
                 <th style="text-align: left;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.production']" /></th>
                 <th style="text-align: left;" width="10%"><xsl:value-of select="$message[@id='xslt_messages.prerelease']" /></th>
              </tr>
           </thead>
        <tbody>          
          <xsl:for-each select="//UnifiedLoginReport/DomainsMapping/DomainMapping">
          <xsl:variable name="backgroundColor">
            <xsl:choose>
            <xsl:when test="position() mod 2=1">background-color:#EEEEEE;</xsl:when>
            <xsl:otherwise>background-color:#FFFFFF;</xsl:otherwise>
            </xsl:choose>
          </xsl:variable>
          <tr style="{$backgroundColor}; text-align: left;" >
            <td><xsl:value-of select="Name"/></td>
            <td><xsl:value-of select="Production"/></td>
            <td><xsl:value-of select="PreRelease"/></td>
          </tr>
          </xsl:for-each>
        </tbody>
      </table>
       </xsl:when>
    </xsl:choose>
    </div>
  </xsl:template>
  
<xsl:template name="ReportSummary">
    <br />
    <br />
    <div class="sectionHeader">
      <a class="sectionHeaderPlainLink" href="#contents" name="section_A"><xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
      <br />
    </div>
    <xsl:value-of select="name(.)"/>
    <xsl:apply-templates select="//UnifiedLoginReport/Request"/>
  </xsl:template>
  <xsl:template match="Request">
    <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.request.overview']" /></div>
    <div class="sectionContent">
      <table class="reportContentTable">
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.export.version']" />: </td>
          <td>
            <xsl:value-of select="@version"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested.by']" />: </td>
          <td>
            <xsl:value-of select="@by"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested.on']" />: </td>
          <td>
            <xsl:call-template name="formdate">
            	<xsl:with-param name="date"><xsl:value-of select="@on"/></xsl:with-param>
            </xsl:call-template>
          </td>
        </tr>
      </table>
    </div>
  </xsl:template>
  <!-- convert a date in the form yyyy-mm-dd to MMM dd, yyyy -->
  <xsl:template name="formdate">
    <xsl:param name="date"/>
    <xsl:variable name="len">
      <xsl:value-of select="string-length($date)"/>
    </xsl:variable>
    <xsl:variable name="yr">
      <xsl:value-of select="substring($date,1,4)" />
    </xsl:variable>
    <xsl:variable name="mo">
      <xsl:value-of select="substring($date,6,2)" />
    </xsl:variable>
    <xsl:variable name="dt">
      <xsl:value-of select="substring($date,9,2)" />
    </xsl:variable>
    <xsl:variable name="month">
      <xsl:choose>
        <xsl:when test="$mo = '01'">Jan</xsl:when>
        <xsl:when test="$mo = '02'">Feb</xsl:when>
        <xsl:when test="$mo = '03'">Mar</xsl:when>
        <xsl:when test="$mo = '04'">Apr</xsl:when>
        <xsl:when test="$mo = '05'">May</xsl:when>
        <xsl:when test="$mo = '06'">Jun</xsl:when>
        <xsl:when test="$mo = '07'">Jul</xsl:when>
        <xsl:when test="$mo = '08'">Aug</xsl:when>
        <xsl:when test="$mo = '09'">Sep</xsl:when>
        <xsl:when test="$mo = '10'">Oct</xsl:when>
        <xsl:when test="$mo = '11'">Nov</xsl:when>
        <xsl:when test="$mo = '12'">Dec</xsl:when>
      </xsl:choose>
    </xsl:variable>
    <xsl:if test="$len > 0">
      <xsl:value-of select="$month"/>&#160;
      <xsl:value-of select="$dt"/>,&#160;
      <xsl:value-of select="$yr"/>
    </xsl:if>
  </xsl:template>
 </xsl:stylesheet>