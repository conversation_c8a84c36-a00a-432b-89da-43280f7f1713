<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

  <xsl:include href="reportStyleTemplate.xsl"/>
  
  <xsl:template match="/">
  <html>
  	<xsl:call-template name="ReportHead"/>
  		<body>
    	<xsl:call-template name="ReportContents" />
    	<xsl:call-template name="ReportSummary" />
    	<xsl:call-template name="Order" />
    	</body>
	</html>
  </xsl:template>
  <xsl:template name="ReportContents">
    <div class="sectionHeader">
      <a name="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.contents']" /></a>
    </div>
    <div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <a href="#section_A"><xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
      </div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <a href="#section_B"><xsl:value-of select="$message[@id='xslt_messages.connected.orders']" /></a>
      </div>
    </div>
    <br />
  </xsl:template>
  <xsl:template name="ReportSummary">
    <div class="sectionHeader">
      <a class="sectionHeaderPlainLink" name="section_A" href="#contents"><xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
    </div>
    <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.request.overview']" /></div>
    <xsl:apply-templates select="//ConnectedAudit" />
    <div class="sectionSubHeader">
      <a class="plainLink" name="ConnectedIndex"><xsl:value-of select="$message[@id='xslt_messages.connected.orders.index']" /></a>
    </div>
    <xsl:apply-templates select="//ConnectedAudit/Orders">
      <xsl:with-param name="bConnectedIndex">True</xsl:with-param>
    </xsl:apply-templates>
  </xsl:template>
  <xsl:template match="//ConnectedAudit">
    <div class="sectionContent">
      <table class="reportContentTable">
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.export.version']" />: </td>
          <td>
            <xsl:value-of select="./@ver"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested.by']" />: </td>
          <td>
            <xsl:value-of select="./Metadata/User"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested']" />: </td>
          <td>
            <xsl:call-template name="FormatDate">
              <xsl:with-param name="DateTime">
                <xsl:value-of select="./Metadata/RequestDate"/>
              </xsl:with-param>
            </xsl:call-template>            
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.report.type']" />: </td>
          <td>
          	<xsl:value-of select="./Metadata/ReportType" />
          </td>
        </tr>
          <tr>
              <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.instance']" />:</td>
              <td class="singleColumnValue"><xsl:value-of select="./Metadata/InstanceName"/></td>
          </tr>
          <tr>
              <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.system.default.language']" />:</td>
              <td class="singleColumnValue"><xsl:value-of select="./Metadata/SystemDefaultLocale"/></td>
          </tr>
      </table>
    </div>
  </xsl:template>
  <xsl:template name="Order">
    <div class="sectionHeader">
      <a name="section_B" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <xsl:value-of select="$message[@id='xslt_messages.connected.orders']" /></a>
    </div>
    <xsl:apply-templates select="//ConnectedAudit/Orders">
      <xsl:with-param name="bConnectedIndex">False</xsl:with-param>
    </xsl:apply-templates>
  </xsl:template>
  <xsl:template match="//ConnectedAudit/Orders" >
    <xsl:param name="bConnectedIndex" />
    <div class="sectionSubHeader">
      <a href="#ConnectedIndex" name="ConnectedIndex" class="plainLink">
        <xsl:value-of select="../Recipient"/>
      </a>
    </div>
    <xsl:choose>
      <xsl:when test="$bConnectedIndex='True'">
        <div class="sectionContent">
          <ul>
            <xsl:call-template name="OutputConnected">
              <xsl:with-param name="bConnectedIndex">
                <xsl:value-of select="$bConnectedIndex"/>
              </xsl:with-param>
            </xsl:call-template>
          </ul>
        </div>
      </xsl:when>
      <xsl:otherwise>
        <xsl:call-template name="OutputConnected">
          <xsl:with-param name="bConnectedIndex">
            <xsl:value-of select="$bConnectedIndex"/>
          </xsl:with-param>
        </xsl:call-template>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>
  <xsl:template name="OutputConnected">
    <xsl:param name="bConnectedIndex" />
    <xsl:for-each select="./Order">
      <xsl:variable name="seq" select="position()" />
      <xsl:variable name="TaskName">
        <xsl:value-of select="./@guid"/>
      </xsl:variable>
      <xsl:variable name="HrefConnectedName">#<xsl:value-of select="./@guid"/>
      </xsl:variable>
      <xsl:choose>
        <xsl:when test="$bConnectedIndex='True'">
          <li>
            <a href="{$HrefConnectedName}{$seq}" class="plainLink">
              <xsl:value-of select="$TaskName"/>
            </a>
          </li>
        </xsl:when >
        <xsl:otherwise>
          <div class="sectionSubHeader"><a name="{$TaskName}{$seq}" href="#ConnectedIndex" class="plainLink"><xsl:value-of select="$TaskName"/></a></div>
          <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.details']" /></div>
		  <div class="sectionContent">
	      <table class="reportContentTable">
	        <tr>
	          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.guid']" />: </td>
	          <td>
	            <xsl:value-of select="@guid"/>
	          </td>
	          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.recipient']" />: </td>
	          <td>
	            <xsl:value-of select="Recipient"/>
	          </td>
	        </tr>
	        <tr>
	          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.created']" />: </td>
	          <td>
	          	<xsl:call-template name="FormatDate">
            	<xsl:with-param name="DateTime">
              	<xsl:value-of select="@created"/>
            	</xsl:with-param>
            	<xsl:with-param name="noHour">true</xsl:with-param>
            	<xsl:with-param name="noMinute">true</xsl:with-param>             
          		</xsl:call-template>     
	          </td>
	          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.created.by']" />: </td>
	          <td>
	          	<xsl:value-of select="@createdby"/>
	          </td>
	        </tr>
	        <tr>
	          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.next.action']" />: </td>
	          <td>
	          	<xsl:value-of select="@nextaction"/>
	          </td>
	          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.status']" />: </td>
	          <td>
	          	<xsl:value-of select="@status"/>
	          </td>
	        </tr>
	        <tr>
	          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.proof']" />: </td>
	          <td>
	          	<xsl:value-of select="@proof"/>
	          </td>
              <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.assigned.to']" />: </td>
              <td>
                <xsl:value-of select="@assignedto"/>
              </td>
	        </tr>
            <tr>
              <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.last.activation.date']" />: </td>
              <td>
                  <xsl:call-template name="FormatDate">
                      <xsl:with-param name="DateTime">
                          <xsl:value-of select="@lastactivationdate"/>
                      </xsl:with-param>
                      <xsl:with-param name="noHour">true</xsl:with-param>
                      <xsl:with-param name="noMinute">true</xsl:with-param>
                  </xsl:call-template>
              </td>
              <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.last.production.date']" />: </td>
              <td>
                  <xsl:call-template name="FormatDate">
                      <xsl:with-param name="DateTime">
                          <xsl:value-of select="@lastproductiondate"/>
                      </xsl:with-param>
                      <xsl:with-param name="noHour">true</xsl:with-param>
                      <xsl:with-param name="noMinute">true</xsl:with-param>
                  </xsl:call-template>
              </td>
             </tr>
              <tr>
                  <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.final.approver']" />: </td>
                  <td>
                      <xsl:value-of select="@finalapprover"/>
                  </td>
              </tr>

	      </table>
  		</div>
		</xsl:otherwise>
      </xsl:choose>
    </xsl:for-each>
  </xsl:template>
  </xsl:stylesheet>