<?xml version="1.0" encoding="UTF-8" ?>
<xsl:stylesheet version="2.0"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:msxsl="urn:schemas-microsoft-com:xslt"
                xmlns:ConstantsSetting="http://ConstantsSetting.com" exclude-result-prefixes="ConstantsSetting"
>

  <xsl:include href="reportStyleTemplate.xsl"/>
  
  <xsl:variable name="DirectReferences">DirectReferences_</xsl:variable>
  <xsl:variable name="ReferencesMap">ReferencesMap_</xsl:variable>

  <xsl:template match="/">
    <xsl:call-template name="ReportDefinition" />
    <xsl:call-template name="ReportContents" />
    <xsl:call-template name="ReportSummary" />
    <xsl:call-template name="DirectReferences" />
    <xsl:call-template name="ReferencesMap" />
  </xsl:template>

  <xsl:template name="ReferencesMap">
    <div class="sectionHeader">
      <a class="sectionHeaderPlainLink" href="#contents" name="section_C"><xsl:value-of select="$message[@id='xslt_messages.section']" /> C: <xsl:value-of select="$message[@id='xslt_messages.reference.map']" /></a>
    </div>
    <xsl:call-template name="ReferenceMap">
      <xsl:with-param name="WithDetails">True</xsl:with-param>
    </xsl:call-template>
  </xsl:template>
  
  <xsl:template name="ReferenceMap" >
    <xsl:param name="WithDetails" />

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/DataSourceAssociations" />
      <xsl:with-param name="IdentifiedValue">.Data Source Association: </xsl:with-param>
      <xsl:with-param name="Children"></xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/Attachments" />
      <xsl:with-param name="IdentifiedValue">.Attachment: </xsl:with-param>
      <xsl:with-param name="Children">Touchpoint</xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>
    
    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/FilterConditions" />
      <xsl:with-param name="IdentifiedValue">.Filter Condition: </xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/SmartTexts" />
      <xsl:with-param name="IdentifiedValue">SmartText</xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>
    
    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/SmartCanvases" />
      <xsl:with-param name="IdentifiedValue">SmartCanvas</xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>
    
    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/ImageLibraries" />
      <xsl:with-param name="IdentifiedValue">ImageLibrary</xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>    
    
    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/TemplateModifiers" />
      <xsl:with-param name="IdentifiedValue">TemplateModifier</xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>    
    
    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/Messages" />
      <xsl:with-param name="IdentifiedValue">Message</xsl:with-param>
      <xsl:with-param name="Children">Tag</xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>
    
    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/LocalSmartTexts" />
      <xsl:with-param name="IdentifiedValue">LocalSmartText</xsl:with-param>
      <xsl:with-param name="Children">Tag</xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template> 
    
    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/LocalImages" />
      <xsl:with-param name="IdentifiedValue">LocalImage</xsl:with-param>
      <xsl:with-param name="Children">Tag</xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>   
    
    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/Rules" />
      <xsl:with-param name="IdentifiedValue">.Rule: </xsl:with-param>
      <xsl:with-param name="Children">TargetGroup</xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/TargetGroups" />
      <xsl:with-param name="IdentifiedValue">.TargetGroup: </xsl:with-param>
      <xsl:with-param name="Children">Message|SmartText|LocalSmartText|SmartCanvas</xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/SharedContent" />
      <xsl:with-param name="IdentifiedValue">.Shared Content: </xsl:with-param>
      <xsl:with-param name="Children">Message</xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>
    
    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/Selectors" />
      <xsl:with-param name="IdentifiedValue">.Selector: </xsl:with-param>
      <xsl:with-param name="Children">Message|SelectorGroup|Constant|Touchpoint|SmartText|ImageLibrary|LocalSmartText|LocalImage|SmartCanvas</xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/Touchpoints" />
      <xsl:with-param name="IdentifiedValue">.Touchpoint: </xsl:with-param>
      <xsl:with-param name="Children"></xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>
    
    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/ConnectedOrders" />
      <xsl:with-param name="IdentifiedValue">.ConnectedOrder: </xsl:with-param>
      <xsl:with-param name="Children"></xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>
    
    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/Variables" />
      <xsl:with-param name="IdentifiedValue">.Variable: </xsl:with-param>
      <xsl:with-param name="Children"></xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/TextStyles" />
      <xsl:with-param name="IdentifiedValue">.TextStyle: </xsl:with-param>
      <xsl:with-param name="Children"></xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/ParagraphStyles" />
      <xsl:with-param name="IdentifiedValue">.ParagraphStyle: </xsl:with-param>
      <xsl:with-param name="Children"></xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/ListStyles" />
      <xsl:with-param name="IdentifiedValue">.ListStyle: </xsl:with-param>
      <xsl:with-param name="Children"></xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/TextStyleCustomizations" />
      <xsl:with-param name="IdentifiedValue">.TextStyleCustomization: </xsl:with-param>
      <xsl:with-param name="Children"></xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/ParagraphStyleCustomizations" />
      <xsl:with-param name="IdentifiedValue">.ParagraphStyleCustomization: </xsl:with-param>
      <xsl:with-param name="Children"></xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/ListStyleCustomizations" />
      <xsl:with-param name="IdentifiedValue">.ListStyleCustomizations: </xsl:with-param>
      <xsl:with-param name="Children"></xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/TouchpointCollections" />
      <xsl:with-param name="IdentifiedValue">.TouchpointCollection: </xsl:with-param>
      <xsl:with-param name="Children"></xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/ChannelConfigurations" />
      <xsl:with-param name="IdentifiedValue">.ChannelConfiguration: </xsl:with-param>
      <xsl:with-param name="Children"></xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>

    <xsl:call-template name="GetReference">
      <xsl:with-param name="Position" select="/WhereUsed/References/Zones" />
      <xsl:with-param name="IdentifiedValue">.Zone: </xsl:with-param>
      <xsl:with-param name="Children"></xsl:with-param>
      <xsl:with-param name="WithDetails">
        <xsl:copy-of select="$WithDetails"></xsl:copy-of>
      </xsl:with-param>
    </xsl:call-template>
  </xsl:template>

  <xsl:template name="GetReference">
    <xsl:param name="WithDetails" />
    <xsl:param name="Position"/>
    <xsl:param name="IdentifiedValue"/>
    <xsl:param name="Children"/>
    <div class="sectionSubHeader">
      <xsl:variable name="HrefTo">#<xsl:value-of select="$DirectReferences"/><xsl:copy-of select="name($Position)"/></xsl:variable>
      <xsl:variable name="HrefIdentified">
         <xsl:value-of select="$ReferencesMap"/><xsl:copy-of select="name($Position)"/>
      </xsl:variable>
      <a class="plainLink" href="{$HrefTo}" name="{$HrefIdentified}">
        <xsl:copy-of select="name($Position)"/>
      </a>
    </div>
    <xsl:for-each select="$Position/*">
      <xsl:variable name="NodeInfo">
        <xsl:value-of select="name(.)"/>
        <xsl:copy-of select="position()"/>
      </xsl:variable>
      <xsl:variable name="HrefTo">#<xsl:value-of select="$DirectReferences"/><xsl:copy-of select="$NodeInfo"/></xsl:variable>
      <xsl:variable name="HrefIdentified">
          <xsl:value-of select="$ReferencesMap"/><xsl:copy-of select="$NodeInfo"/>
      </xsl:variable>
      <div class="sectionSubHeader">
        <a class="plainLink" href="{$HrefTo}" name="{$HrefIdentified}">
          <!--<xsl:value-of select="position()"/>-->
          <xsl:choose>
            <xsl:when test="$IdentifiedValue='Message' or $IdentifiedValue='LocalSmartText' or $IdentifiedValue='LocalImage' or $IdentifiedValue='TargetGroup'">
              <!--Message External Id: <xsl:value-of select="@extid"/>-->
            </xsl:when>
            <xsl:otherwise>
              <!--<xsl:copy-of select="$IdentifiedValue"/>-->
              <xsl:value-of select="./Name"/>
            </xsl:otherwise>
          </xsl:choose>
        </a>
      </div>
      <xsl:choose>
        <xsl:when test="$IdentifiedValue='Message' or $IdentifiedValue='SmartText' or $IdentifiedValue='ImageLibrary' or $IdentifiedValue='LocalSmartText' or $IdentifiedValue='LocalImage' or $IdentifiedValue='SmartCanvas'">
            <div class="sectionContent">
            <table class="nestedTableLevel0">
              <tr>
                <td width="10%" class="tableHeader">
                	<xsl:if test="$IdentifiedValue='Message' or $IdentifiedValue='LocalSmartText' or $IdentifiedValue='LocalImage'">
                		<xsl:value-of select="$message[@id='xslt_messages.external.id']" />
                	</xsl:if>
                </td>
                <td width="30%" class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.active']" /></td>
                <td width="30%" class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.wc.or.archived']" /></td>
				<xsl:choose>
        			<xsl:when test="$IdentifiedValue='Message' or $IdentifiedValue='LocalSmartText' or $IdentifiedValue='LocalImage'">
        				<td width="30%" class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.touchpoint']" /></td>
        			</xsl:when>
        			<xsl:otherwise>
        				<td width="30%" class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.touchpoints']" /></td>
        			</xsl:otherwise>
        		</xsl:choose>
              </tr>
              <tr>
              <td>
                <a class="plainLink" href="{$HrefTo}" name="{$HrefIdentified}">
                  <xsl:value-of select="./@extid"/>
                </a>
              </td>

              <xsl:variable name="activeInstance">
                <xsl:for-each select="Instance">
                  <xsl:choose>
                    <xsl:when test="@id='-101' ">
                      <xsl:copy-of select="./Name"/>
                    </xsl:when>
                  </xsl:choose>
                </xsl:for-each>
              </xsl:variable>

              <xsl:variable name="activeInstanceVariant">
                <xsl:for-each select="Instance">
                  <xsl:choose>
                    <xsl:when test="@id='-103' ">
                      <xsl:copy-of select="./Name"/>
                    </xsl:when>
                  </xsl:choose>
                </xsl:for-each>
              </xsl:variable>

              <xsl:variable name="workingCopyInstance">
                <xsl:for-each select="Instance">
                  <xsl:choose>
                    <xsl:when test="@id='-100' ">
                      <xsl:copy-of select="./Name"/>
                    </xsl:when>
                  </xsl:choose>
                </xsl:for-each>
              </xsl:variable>

              <xsl:variable name="workingCopyInstanceVariant">
                <xsl:for-each select="Instance">
                  <xsl:choose>
                    <xsl:when test="@id='-102' ">
                      <xsl:copy-of select="./Name"/>
                    </xsl:when>
                  </xsl:choose>
                </xsl:for-each>
              </xsl:variable>
              
              <xsl:variable name="touchpoints">
                <xsl:for-each select="Touchpoints/Touchpoint">
                  <xsl:copy-of select="./Name"/>
                  <xsl:if test="not(position() = last())">,&#160;</xsl:if>
                </xsl:for-each>
              </xsl:variable>              
              
              <td>
                <xsl:value-of select="$activeInstance"/>
	            <xsl:apply-templates mode="TreeView" select="Instance[@id='-101']/Selection">
	              <xsl:with-param name="modelid" select="@id"/>
	            </xsl:apply-templates>

                <xsl:value-of select="$activeInstanceVariant"/>
                <xsl:apply-templates mode="TreeView" select="Instance[@id='-103']/Selection">
                  <xsl:with-param name="modelid" select="@id"/>
                </xsl:apply-templates>
              </td>
              <td>
                <xsl:value-of select="$workingCopyInstance"/>
	            <xsl:apply-templates mode="TreeView" select="Instance[@id='-100']/Selection">
	              <xsl:with-param name="modelid" select="@id"/>
	            </xsl:apply-templates>

                <xsl:value-of select="$workingCopyInstanceVariant"/>
                <xsl:apply-templates mode="TreeView" select="Instance[@id='-102']/Selection">
                  <xsl:with-param name="modelid" select="@id"/>
                </xsl:apply-templates>
              </td>
              <td>
              	<xsl:value-of select="$touchpoints"/>
              </td>
              </tr>
            </table>
                                    
            <xsl:for-each select="*|node()">
              <xsl:for-each select="*|node()">
                <xsl:if test ="contains($Children,name(.))">
                  <xsl:apply-templates select=".">
                    <xsl:with-param name="WithDetails">
                      <xsl:copy-of select="$WithDetails"></xsl:copy-of>
                    </xsl:with-param>
                    <xsl:with-param name="Level">
                      <xsl:number value="0"/>
                    </xsl:with-param>
                  </xsl:apply-templates>
                </xsl:if>
              </xsl:for-each>
            </xsl:for-each>
          </div>
        </xsl:when>
        <xsl:otherwise>
            <div class="sectionContent">
              <xsl:for-each select="*|node()">
                <xsl:choose>
                  <xsl:when test="name(.)='Name'">
                  </xsl:when>
                  <xsl:when test="name(.)='Conditions'">
                    <xsl:if test="$WithDetails='True'">
                      <table class="nestedTableLevel0">
                        <xsl:for-each select="Condition">
                          <tr>
                            <td>
                              <b>
                               <xsl:value-of select="position()"/>.<xsl:value-of select="$message[@id='xslt_messages.condition.id']" />:
                              </b>
                              <xsl:value-of select="@id"/>
                            </td>
                            <td>
                              <b><xsl:value-of select="$message[@id='xslt_messages.name']" />: </b>
                              <xsl:value-of select="."/>
                            </td>
                          </tr>
                        </xsl:for-each>
                      </table>
                    </xsl:if>
                  </xsl:when>
                  <xsl:otherwise>
                    <xsl:for-each select="*|node()">
                      <xsl:if test ="contains($Children,name(.))">
                        <xsl:apply-templates select=".">
                          <xsl:with-param name="WithDetails">
                            <xsl:value-of select="$WithDetails"></xsl:value-of>
                          </xsl:with-param>
                          <xsl:with-param name="Level">
                            <xsl:number value="0"/>
                          </xsl:with-param>
                        </xsl:apply-templates>
                        <br />           
                      	<br />
                      </xsl:if>
                    </xsl:for-each>
                  </xsl:otherwise>
                </xsl:choose>
              </xsl:for-each>
            </div>
        </xsl:otherwise>
      </xsl:choose>
    </xsl:for-each>
  </xsl:template>
  
<xsl:template match="Rule | TougetGroup | Insert | Tag | Message | SelectorGroup | Constant | Touchpoint | TargetGroup | Attachment | DataSourceAssociation | SmartText | ImageLibrary | LocalSmartText | LocalImage | SmartCanvas | ConnectedOrder | Variable | Clipboard | TextStyle | ParagraphStyle | ListStyle | TextStyleCustomization | ParagraphStyleCustomization | ListStyleCustomization | TouchpointCollection | ChannelConfiguration | Zone">
  <xsl:param name="WithDetails"/>
  <xsl:param name="Level"/>
  <xsl:param name="ParentsInfo" />
  <xsl:param name="ParentsName" />

  <xsl:variable name="CurrentNodeSeq">
    <xsl:value-of select="name(.)"/>
  </xsl:variable>
  <xsl:variable name="CurrentNodeName">
    <xsl:value-of select="./Name"/>
  </xsl:variable>

  <xsl:variable name="CurrentNodeInfo">
    <xsl:value-of select="$ParentsInfo"/>==>
    <xsl:value-of select="name(.)"/>
  </xsl:variable>

  <xsl:if test="$WithDetails='True'">
    <br />
    <div class="HighLigtContent">
      <xsl:value-of select="$message[@id='xslt_messages.level']" />:<xsl:copy-of select="$Level+1"/>
      <xsl:value-of select="$CurrentNodeInfo"/>
    </div>
  </xsl:if>
  
  <xsl:variable name="Style">
  	<xsl:if test="$WithDetails='True'">
      nestedTableLevel<xsl:copy-of select="$Level+1"/>
    </xsl:if>
  </xsl:variable> 
  <xsl:variable name="TDWidth">
    <xsl:choose>
      <xsl:when test="$WithDetails='True'">
         <xsl:copy-of select="120-($Level+1)*3"/>px
      </xsl:when>
      <xsl:otherwise>
         120px
      </xsl:otherwise>
    </xsl:choose>
  </xsl:variable> 
  
  <table class="{$Style}">
    <tr>
      <td>
        <xsl:if test="$WithDetails='True'">          
        <table class="reportContentTable">
           <tr>
            <td width="{$TDWidth}">
              <xsl:choose>
                <xsl:when test="Name">
                  <xsl:value-of select="$message[@id='xslt_messages.name']" />:
                </xsl:when>
                <xsl:when test="../Constant">
                  <xsl:value-of select="$message[@id='xslt_messages.constant']" />:
                </xsl:when>
                <xsl:when test="@extid">
                  <xsl:value-of select="$message[@id='xslt_messages.external.id']" />:
                </xsl:when>
              </xsl:choose>
            </td>
            <td>
              <xsl:choose>
                <xsl:when test="Name">
                  <xsl:value-of select="Name"/>
                </xsl:when>
                <xsl:when test="../Constant">
                  <xsl:value-of select="../Constant"/>
                </xsl:when>
                <xsl:when test="@extid">
                  <xsl:value-of select="@extid"/>
                </xsl:when>
              </xsl:choose>
            </td>
          </tr>
          <xsl:if test="@stockid">
			<tr>
	          <td>
	            <xsl:value-of select="$message[@id='xslt_messages.stock.id']" />:
	          </td>
	          <td>
	            <xsl:value-of select="@stockid" />
	          </td>
            </tr>          
          </xsl:if>
          <xsl:if test="name(.)='Insert' or name(.)='Tag'">
          	<tr>
	          <td>
	            <xsl:value-of select="$message[@id='xslt_messages.status']" />:
	          </td>          
              <td>
                <xsl:choose>
                  <xsl:when test="@active='true' "><xsl:value-of select="$message[@id='xslt_messages.active']" /></xsl:when>
                  <xsl:when test="@archived='true' "><xsl:value-of select="$message[@id='xslt_messages.archived']" /></xsl:when>
                  <xsl:otherwise><xsl:value-of select="$message[@id='xslt_messages.inactive']" /></xsl:otherwise>
                </xsl:choose>
              </td>            
            </tr>          
          </xsl:if>         
          <xsl:choose>
            <xsl:when test="Associations | Conditions">
              <tr>
                <td>
                  <xsl:value-of select="$message[@id='xslt_messages.conditions']" />:
                </td>
                <td>
              <xsl:for-each select="Associations/Condition | Conditions/Condition">
                <div>
                  <xsl:value-of select=".."/>
                </div>
              </xsl:for-each>
                </td>
              </tr>
            </xsl:when>
            <xsl:when test="Association">
              <tr>
                <td>
                  <xsl:value-of select="$message[@id='xslt_messages.conditions']" />:
                </td>
                <td>
                  <xsl:for-each select="Association">
                    <xsl:value-of select="."/>
                    <br />
                  </xsl:for-each>
                </td>
              </tr>
            </xsl:when>       
          </xsl:choose>            
          <tr>
            <td colspan="2">
              <table>
                <xsl:for-each select="Instance">
                  <tr>
                    <td width="{$TDWidth}"><xsl:value-of select="$message[@id='xslt_messages.name']" />:</td>
                    <td>
                      <xsl:value-of select="./Name"/>
                    </td>
                  </tr>
                  <tr>
                    <td><xsl:value-of select="$message[@id='xslt_messages.status']" />:</td>
                    <td>
                      <xsl:choose>
                        <xsl:when test="@active='true' ">
                          <xsl:value-of select="$message[@id='xslt_messages.active']" />
                        </xsl:when>
                        <xsl:otherwise><xsl:value-of select="$message[@id='xslt_messages.wc.or.archived']" /></xsl:otherwise>
                      </xsl:choose>
                    </td>
                  </tr>
                  <tr>
                    <td/>
                    <td>
                        <xsl:apply-templates mode="TreeView" select="Selection">
                            <xsl:with-param name="modelid" select="@id"/>
                        </xsl:apply-templates>
                    </td>
                  </tr>
                </xsl:for-each>
              </table>
            </td>
          </tr>
        </table>          
        </xsl:if>      
          
        <xsl:for-each select="./node()">
          <xsl:if test="name(.)='Instance'">
            <xsl:for-each select="./node()">
            <xsl:apply-templates select="Tag">
              <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
              <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
              <xsl:with-param name="Level" select="$Level+1" />
              <xsl:with-param name="WithDetails">
                <xsl:copy-of select="$WithDetails"></xsl:copy-of>
              </xsl:with-param>
            </xsl:apply-templates>
          </xsl:for-each>
        </xsl:if>

        <xsl:if test="$WithDetails='False'">
          <xsl:if test="name(.)='Name'">
            <br />
            <div>
              <xsl:copy-of select="$CurrentNodeSeq"/>
              <br />
              <span class="HighLigtContent"><xsl:value-of select="$message[@id='xslt_messages.on.level']" />:</span><xsl:copy-of select="$Level+1"/>
              <br />
              <span class="HighLigtContent"><xsl:value-of select="$message[@id='xslt_messages.name']" />:</span><xsl:value-of select="$CurrentNodeName"/>              
              <xsl:if test="$ParentsInfo!=''">
                <br />
                <span class="HighLigtContent"><xsl:value-of select="$message[@id='xslt_messages.ancestor.info']" />:</span>
                <xsl:value-of select="$CurrentNodeInfo"/>
              </xsl:if>
            </div>
          </xsl:if>
        </xsl:if>


        <xsl:if test="TargetGroup | Insert | Tag | Message | Constant | Touchpoint | Attachment | SmartText | ImageLibrary | LocalSmartText | LocalImage | SmartCanvas | ConnectedOrder | TextStyle | ParagraphStyle | ListStyle | TextStyleCustomization | ParagraphStyleCustomization | ListStyleCustomization | TouchpointCollection | ChannelConfiguration | Zone">

          <xsl:apply-templates select="TargetGroup">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="Insert">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="Tag">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="Message">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="LocalSmartText">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="LocalImage">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="SmartText">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="SmartCanvas">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>            
          <xsl:apply-templates select="Constant">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="Touchpoint">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
              <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
              <xsl:with-param name="Level" select="$Level+1" />
              <xsl:with-param name="WithDetails">
                <xsl:copy-of select="$WithDetails"></xsl:copy-of>
              </xsl:with-param>
            </xsl:apply-templates>
          <xsl:apply-templates select="Attachment">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="ImageLibrary">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>  
          <xsl:apply-templates select="ConnectedOrder">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="TextStyle">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="ParagraphStyle">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="ListStyle">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="TextStyleCustomization">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="ParagraphStyleCustomization">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="ListStyleCustomization">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="TouchpointCollection">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="ChannelConfiguration">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
          <xsl:apply-templates select="Zone">
            <xsl:with-param name="ParentsInfo" select="$CurrentNodeInfo" />
            <xsl:with-param name="ParentsName" select="$CurrentNodeName" />
            <xsl:with-param name="Level" select="$Level+1" />
            <xsl:with-param name="WithDetails">
              <xsl:copy-of select="$WithDetails"></xsl:copy-of>
            </xsl:with-param>
          </xsl:apply-templates>
        </xsl:if>
        </xsl:for-each>
      </td>
    </tr>
  </table>
</xsl:template>
  
  <xsl:template name="DirectReferences">
    <br />
    <br />
    <div class="sectionHeader">
      <a class="sectionHeaderPlainLink" href="#contents" name="section_B"><xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <xsl:value-of select="$message[@id='xslt_messages.direct.references']" /></a>
      <br />
    </div>
    <xsl:call-template name="OutputDirectReferences"/>
  </xsl:template>

  <xsl:template name="OutputDirectReferences">
    <xsl:choose>
		<xsl:when test="//WhereUsed/References/*">
		    <xsl:for-each select="//WhereUsed/References/*">
			      <xsl:sort select="name(.)"/>
			      <xsl:variable name="HrefTo">#<xsl:value-of select="$ReferencesMap"/><xsl:copy-of select="name(.)"/></xsl:variable>
			      <xsl:variable name="HrefIdentified">
			        <xsl:value-of select="$DirectReferences"/><xsl:copy-of select="name(.)"/>
			      </xsl:variable>
			      <div class="sectionSubHeader">
			         <a class="plainLink" href="{$HrefTo}" name="{$HrefIdentified}">
			          <xsl:value-of select="name(.)"/>
			        </a>
			      </div>
          
		          <xsl:choose>
		            <xsl:when test="name(.)='Messages' or name(.)='SmartTexts' or name(.)='ImageLibraries' or name(.)='LocalSmartTexts' or name(.)='LocalImages' or name(.)='SmartCanvases'">
		              <div class="sectionContent">
		                <table class="reportContentTable">
		                  <tr>
		                    <td width="10%" class="tableHeader">
		                    	<xsl:if test="name(.)='Messages' or name(.)='LocalSmartTexts' or name(.)='LocalImages'">
		                    		<xsl:value-of select="$message[@id='xslt_messages.external.id']" />
		                    	</xsl:if>
		                    </td>
		                    <td width="30%" class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.active']" /></td>
		                    <td width="30%" class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.wc.or.archived']" /></td>
							<xsl:choose>
			        			<xsl:when test="name(.)='Messages' or name(.)='LocalSmartTexts' or name(.)='LocalImages'">
			        				<td width="30%" class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.touchpoint']" /></td>
			        			</xsl:when>
			        			<xsl:otherwise>
			        				<td width="30%" class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.touchpoints']" /></td>
			        			</xsl:otherwise>
			        		</xsl:choose>
                          </tr>
		                  <xsl:for-each select="./node()">
		                    <xsl:call-template name="OutputEachValue" />
		                  </xsl:for-each>
		                </table>
		              </div>
		            </xsl:when>
		            <xsl:otherwise>
		              <xsl:for-each select="./node()">
		                <xsl:call-template name="OutputEachValue" />
		              </xsl:for-each>
		            </xsl:otherwise>
		          </xsl:choose>
        	</xsl:for-each>		
		</xsl:when>
		<xsl:otherwise>			
			<div class="sectionContent">
				<xsl:value-of select="$message[@id='xslt_messages.no.direct.reference.for.this.asset']" />
		    </div>
		</xsl:otherwise>
    </xsl:choose>		      

    </xsl:template>

    <xsl:template name="OutputEachValue">      
    	<xsl:variable name="NodeInfo">
          <xsl:copy-of select="name(.)"/>
          <xsl:copy-of select="position()"/>
        </xsl:variable>
        <xsl:variable name="NodeLinkTo">#<xsl:value-of select="$ReferencesMap"/><xsl:copy-of select="$NodeInfo"/></xsl:variable>
        <xsl:variable name="NodeName">
          <xsl:value-of select="$DirectReferences"/>
          <xsl:copy-of select="$NodeInfo"/>
        </xsl:variable>
      <xsl:choose>
        <xsl:when test="Instance">          
          <tr>
          <td>
            <a class="plainLink" href="{$NodeLinkTo}" name="{$NodeName}">
              <xsl:choose>
                <xsl:when test="@extid">
                  <xsl:value-of select="@extid"/>
                </xsl:when>
              </xsl:choose>
            </a>
          </td>

          <xsl:variable name="activeInstance">
            <xsl:for-each select="Instance">
              <xsl:choose>
                <xsl:when test="@id='-101' ">
                  <xsl:copy-of select="./Name"/>
                </xsl:when>
              </xsl:choose>
            </xsl:for-each>
          </xsl:variable>

          <xsl:variable name="activeInstanceVariant">
            <xsl:for-each select="Instance">
              <xsl:choose>
                <xsl:when test="@id='-103' ">
                  <xsl:copy-of select="./Name"/>
                </xsl:when>
              </xsl:choose>
            </xsl:for-each>
          </xsl:variable>

          <xsl:variable name="workingCopyInstance">
            <xsl:for-each select="Instance">
              <xsl:choose>
                <xsl:when test="@id='-100' ">
                  <xsl:copy-of select="./Name"/>
                </xsl:when>
              </xsl:choose>
            </xsl:for-each>
          </xsl:variable>

          <xsl:variable name="workingCopyInstanceVariant">
            <xsl:for-each select="Instance">
              <xsl:choose>
                <xsl:when test="@id='-102' ">
                  <xsl:copy-of select="./Name"/>
                </xsl:when>
              </xsl:choose>
            </xsl:for-each>
          </xsl:variable>
          
          <xsl:variable name="touchpoints">
            <xsl:for-each select="Touchpoints/Touchpoint">
              <xsl:copy-of select="./Name"/>
              <xsl:if test="not(position() = last())">,&#160;</xsl:if>
            </xsl:for-each>
          </xsl:variable>

          <td>
            <xsl:value-of select="$activeInstance"/>
              <xsl:if test="$activeInstance != '' and $activeInstanceVariant != ''">
                  <br />
              </xsl:if>
            <xsl:value-of select="$activeInstanceVariant"/>
          </td>
          <td>
            <xsl:value-of select="$workingCopyInstance"/>
              <xsl:if test="$workingCopyInstance != '' and $workingCopyInstanceVariant != ''">
                  <br />
              </xsl:if>
            <xsl:value-of select="$workingCopyInstanceVariant"/>
          </td>
          <td>
          	<xsl:value-of select="$touchpoints"/>
          </td>
          </tr>
        </xsl:when>
      </xsl:choose>
  </xsl:template>

  <xsl:template name="ReportSummary">
    <br />
    <br />
    <div class="sectionHeader">
      <a class="sectionHeaderPlainLink" href="#contents" name="section_A"><xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
      <br />
    </div>
    <xsl:value-of select="name(.)"/>
    <xsl:apply-templates select="//WhereUsed/Request"/>
  </xsl:template>

  <xsl:template match="Request">
    <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.where.used']" /></div>
    <div class="sectionContent">
      <table class="reportContentTable">
        <tr>
          <xsl:if test="./Variable">
            <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.variable']" />:</td>
          </xsl:if>
          <xsl:if test="./Constant">
            <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.constant.name']" />:</td>
          </xsl:if>
          <xsl:if test="./LocalSmartText">
          	<td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.local.smart.text']" />:</td>
          </xsl:if>
          <xsl:if test="./LocalImage">
          	<td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.local.image']" />:</td>
          </xsl:if>
          <xsl:if test="./SmartText">
          	<td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.smart.text']" />:</td>
          </xsl:if>
          <xsl:if test="./SmartCanvas">
          	<td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.smart.canvas']" />:</td>
          </xsl:if>
          <xsl:if test="./ImageLibrary">
          	<td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.image.library']" />:</td>
          </xsl:if>
          <xsl:if test="./TextStyle">
          	<td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.text.style']" />:</td>
          </xsl:if>
          <xsl:if test="./ParagraphStyle">
          	<td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.paragraph.style']" />:</td>
          </xsl:if>
          <xsl:if test="./ListStyle">
            <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.list.style']" />:</td>
          </xsl:if>
          <xsl:if test="./TextStyleCustomization">
            <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.text.style.customization']" />:</td>
          </xsl:if>
          <xsl:if test="./ParagraphStyleCustomization">
            <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.paragraph.style.customization']" />:</td>
          </xsl:if>
          <xsl:if test="./ListStyleCustomization">
            <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.list.style.customization']" />:</td>
          </xsl:if>
          <xsl:if test="./TargetGroup">
          	<td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.target.group']" />:</td>
          </xsl:if>
          <xsl:if test="./TouchpointCollection">
            <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.touchpoint.collection']" />:</td>
          </xsl:if>
          <xsl:if test="./ChannelConfiguration">
            <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.channel.configuration']" />:</td>
          </xsl:if>
          <xsl:if test="./Zone">
            <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.zone']" />:</td>
          </xsl:if>
          <xsl:if test="./TargetingRule">
            <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.target.rule']" />:</td>
          </xsl:if>
          <td>
            <xsl:if test="name(./Variable)">
                <xsl:value-of select="./Variable"/>
            </xsl:if>
            <xsl:if test="name(./Constant)">
                <xsl:value-of select="./Constant"/>
            </xsl:if>
            <xsl:if test="name(./LocalSmartText)">
            		<xsl:value-of select="./LocalSmartText" />
            </xsl:if>
            <xsl:if test="name(./LocalImage)">
            		<xsl:value-of select="./LocalImage" />
            </xsl:if>
            <xsl:if test="name(./SmartText)">
            		<xsl:value-of select="./SmartText" />
            </xsl:if>
            <xsl:if test="name(./SmartCanvas)">
            		<xsl:value-of select="./SmartCanvas" />
            </xsl:if>
            <xsl:if test="name(./ImageLibrary)">
            		<xsl:value-of select="./ImageLibrary" />
            </xsl:if>
            <xsl:if test="name(./TextStyle)">
            		<xsl:value-of select="./TextStyle" />
            </xsl:if>
            <xsl:if test="name(./ParagraphStyle)">
            		<xsl:value-of select="./ParagraphStyle" />
            </xsl:if>
            <xsl:if test="name(./ListStyle)">
                    <xsl:value-of select="./ListStyle" />
            </xsl:if>
            <xsl:if test="name(./TextStyleCustomization)">
              <xsl:value-of select="./TextStyleCustomization" />
            </xsl:if>
            <xsl:if test="name(./ParagraphStyleCustomization)">
              <xsl:value-of select="./ParagraphStyleCustomization" />
            </xsl:if>
            <xsl:if test="name(./ListStyleCustomization)">
              <xsl:value-of select="./ListStyleCustomization" />
            </xsl:if>
            <xsl:if test="name(./TargetGroup)">
            		<xsl:value-of select="./TargetGroup" />
            </xsl:if>
            <xsl:if test="name(./TouchpointCollection)">
              <xsl:value-of select="./TouchpointCollection" />
            </xsl:if>
            <xsl:if test="name(./ChannelConfiguration)">
              <xsl:value-of select="./ChannelConfiguration" />
            </xsl:if>
            <xsl:if test="name(./Zone)">
              <xsl:value-of select="./Zone" />
            </xsl:if>
            <xsl:if test="name(./TargetingRule)">
              <xsl:value-of select="./TargetingRule" />
            </xsl:if>
          </td>
        </tr>
      </table>
    </div>
    <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.request.overview']" /></div>
    <div class="sectionContent">
      <table class="reportContentTable">
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.export.version']" />: </td>
          <td>
            <xsl:value-of select="@version"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested.by']" />: </td>
          <td>
            <xsl:value-of select="@by"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested.on']" />: </td>
          <td>
            <xsl:call-template name="formdate">
            	<xsl:with-param name="date"><xsl:value-of select="@on"/></xsl:with-param>
            </xsl:call-template>
          </td>
        </tr>
      </table>
    </div>
  </xsl:template>
  <!-- convert a date in the form yyyy-mm-dd to MMM dd, yyyy -->
  <xsl:template name="formdate">
    <xsl:param name="date"/>
    <xsl:variable name="len">
      <xsl:value-of select="string-length($date)"/>
    </xsl:variable>
    <xsl:variable name="yr">
      <xsl:value-of select="substring($date,1,4)" />
    </xsl:variable>
    <xsl:variable name="mo">
      <xsl:value-of select="substring($date,6,2)" />
    </xsl:variable>
    <xsl:variable name="dt">
      <xsl:value-of select="substring($date,9,2)" />
    </xsl:variable>
    <xsl:variable name="month">
      <xsl:choose>
        <xsl:when test="$mo = '01'">Jan</xsl:when>
        <xsl:when test="$mo = '02'">Feb</xsl:when>
        <xsl:when test="$mo = '03'">Mar</xsl:when>
        <xsl:when test="$mo = '04'">Apr</xsl:when>
        <xsl:when test="$mo = '05'">May</xsl:when>
        <xsl:when test="$mo = '06'">Jun</xsl:when>
        <xsl:when test="$mo = '07'">Jul</xsl:when>
        <xsl:when test="$mo = '08'">Aug</xsl:when>
        <xsl:when test="$mo = '09'">Sep</xsl:when>
        <xsl:when test="$mo = '10'">Oct</xsl:when>
        <xsl:when test="$mo = '11'">Nov</xsl:when>
        <xsl:when test="$mo = '12'">Dec</xsl:when>
      </xsl:choose>
    </xsl:variable>
    <xsl:if test="$len > 0">
      <xsl:value-of select="$month"/>&#160;
      <xsl:value-of select="$dt"/>,&#160;
      <xsl:value-of select="$yr"/>
    </xsl:if>
  </xsl:template>
  <xsl:template match="IndirectReferences">
    <tr>
      <td width="40%"><xsl:value-of select="$message[@id='xslt_messages.include.indirect.map']" /></td>
      <td>True</td>
    </tr>
    <tr>
      <td alian="top"><xsl:value-of select="$message[@id='xslt_messages.included.indirect.references']" />:</td>
      <td>
        <xsl:call-template name="OutputIndirectReferences" />
      </td>
    </tr>
    <tr>
      <td style="width: 30%"><xsl:value-of select="$message[@id='xslt_messages.include.active.objects.only']" />:</td>
      <td>No</td>
    </tr>
  </xsl:template>
  <xsl:template name="OutputIndirectReferences">
    <xsl:choose>
      <xsl:when test="//WhereUsed/Request/IndirectReferences/*">
        <xsl:for-each select="./Item">
          <xsl:sort select="." order="ascending"/>
          <div>
            <xsl:variable name="hrefLinkName" select="."></xsl:variable>
            <xsl:variable name="hrefLinkValue1"><xsl:value-of select="$message[@id='xslt_messages.message']" /></xsl:variable>
            <xsl:variable name="hrefLinkValue2"><xsl:value-of select="$message[@id='xslt_messages.rules']" /></xsl:variable>
            <xsl:variable name="hrefLinkValue3"><xsl:value-of select="$message[@id='xslt_messages.touchpoints']" /></xsl:variable>
            <xsl:variable name="hrefLink">
              #sub_section_<xsl:copy-of select="$hrefLinkName" />
            </xsl:variable>
            <!--<a href="{$hrefLink}">-->
              <xsl:copy-of select="$hrefLinkName" />
              <xsl:if test="($hrefLinkName=$hrefLinkValue1)">
                <span class="noteContent">(<xsl:value-of select="$message[@id='xslt_messages.through.content']" />)</span>
              </xsl:if>
              <xsl:if test="($hrefLinkName=$hrefLinkValue2)">
                <span class="noteContent">(<xsl:value-of select="$message[@id='xslt_messages.through.conditions']" />)</span>
              </xsl:if>
              <xsl:if test="($hrefLinkName=$hrefLinkValue3)">
                <span class="noteContent">(<xsl:value-of select="$message[@id='xslt_messages.email.address']" />)</span>
              </xsl:if>
            <!--</a>-->
          </div>
        </xsl:for-each>
      </xsl:when>
      <xsl:otherwise>
			<div class="sectionContent">
		    	<xsl:value-of select="$message[@id='xslt_messages.no.indirect.references.for.variable']" />
		    </div>      
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>

  <xsl:template name="ReportContents">
    <div class="sectionHeader">
      <a class="sectionHeaderPlainLink" name="contents"><xsl:value-of select="$message[@id='xslt_messages.contents']" /></a>
    </div>
    <br />
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> A:
        <a href="#section_A"><xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
      </div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <a href="#section_B"><xsl:value-of select="$message[@id='xslt_messages.direct.references']" /></a>
      </div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> C: <a href="#section_C"><xsl:value-of select="$message[@id='xslt_messages.reference.map']" /></a>
      </div>
  </xsl:template>

  <xsl:template name="ReportDefinition">
    <html>
      <xsl:call-template name="ReportHead"/>
      <body>
      </body>
    </html>
  </xsl:template>
  
  <!--  Write out selection tree -->
  <xsl:template mode="TreeView" match="Selection">
    <xsl:param name="modelid"/>
    
    <xsl:variable name="selectId">
      <xsl:choose>
        <xsl:when test="Name='Default'">
          <xsl:value-of select="'def'"/>
        </xsl:when>
        <xsl:otherwise>
          <xsl:value-of select="@id"/>
        </xsl:otherwise>
      </xsl:choose>
    </xsl:variable>
    
    <ul>
	  <li>
	    <xsl:choose>
		    <xsl:when test="@reftype='direct'">
		    	<b><xsl:value-of select="Name"/></b>
		    </xsl:when>
		    <xsl:when test="@reftype='indirect'">
		    	<i><xsl:value-of select="Name"/></i>
		    </xsl:when>
		    <xsl:otherwise>
		    	<xsl:value-of select="Name"/>
		    </xsl:otherwise>
	    </xsl:choose>
        <xsl:for-each select="Selection">
          <xsl:sort/>
          <xsl:apply-templates mode="TreeView" select=".">
            <xsl:with-param name="modelid" select="$modelid"/>
          </xsl:apply-templates>
        </xsl:for-each>
      </li>
    </ul>
  </xsl:template>  
</xsl:stylesheet>