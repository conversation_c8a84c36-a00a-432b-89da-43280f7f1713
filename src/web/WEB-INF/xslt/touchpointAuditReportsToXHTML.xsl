<?xml version="1.0" encoding="UTF-8" ?>
<xsl:stylesheet version="2.0"
    xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
    xmlns:msxsl="urn:schemas-microsoft-com:xslt"
  >
  <xsl:variable name="Selection">Selection_</xsl:variable>
  <xsl:variable name="TPIndexSeq">0</xsl:variable>
  <xsl:variable name="TPSeq">0</xsl:variable>
  
  <xsl:include href="reportStyleTemplate.xsl"/>
  
  <xsl:template match="/">
    <html>
        <xsl:call-template name="ReportHead"/>
    	<body>
	    <xsl:call-template name="ReportContents" />
	    <xsl:call-template name="ReportSummary" />
	    <xsl:call-template name="ReportDetails" />
	    <xsl:call-template name="ReportReferences" />
	    </body>
    </html>
  </xsl:template>
  <xsl:template match="//TouchpointAudit/Touchpoints/Touchpoint">
    <div class="sectionSubHeader">
      <a href="#TouchpointIndex" class="plainLink">
        <xsl:value-of select="Name"/>
      </a>
    </div>

    <div class="sectionContent">
      <table class="reportContentTable">
        <xsl:for-each select="Sections/Section">
          <tr>
            <td class="dataColumn">
              <i><xsl:value-of select="$message[@id='xslt_messages.section']" /></i> - <xsl:value-of select="Name"/>
            </td>
            <td></td>
          </tr>
          <xsl:for-each select="Zones/Zone">
            <tr>
              <td class="dataColumn">
                <div class="level2">
                  <i><xsl:value-of select="$message[@id='xslt_messages.zone']" /></i> - <xsl:value-of select="Name"/>
                </div>
              </td>
              <td>
                <xsl:value-of select="@type"/>
                <xsl:if test="string-length(@graphictype)!=0">
                  (<xsl:value-of select="@graphictype"/>)
                </xsl:if>
              </td>
            </tr>
            <xsl:for-each select="Parts/Part">
              <tr>
                <td class="dataColumn">
                  <div class="level3">
                    <i><xsl:value-of select="$message[@id='xslt_messages.part']" /></i> - <xsl:value-of select="."/>
                  </div>
                </td>
                <xsl:if test="@type='Text'">
                  <td><xsl:value-of select="$message[@id='xslt_messages.text']" /></td>
                </xsl:if>
                <xsl:if test="@type='Graphic'">
                  <td>
                    <xsl:value-of select="$message[@id='xslt_messages.graphic']" />(<xsl:value-of select="@graphictype"/>)
                  </td>
                </xsl:if>
              </tr>
            </xsl:for-each>
          </xsl:for-each>
        </xsl:for-each>
      </table>
    </div>
  </xsl:template>
  <xsl:template name="ReportReferences">
    <div class="sectionHeader">
      <a name="section_C" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> C: <xsl:value-of select="$message[@id='xslt_messages.touchpoint.reference.data']" /></a>
    </div>
    <xsl:apply-templates select="//TouchpointAudit/Touchpoints/Touchpoint" />
  </xsl:template>
  <xsl:template name="ReportDetails">
    <div class="sectionHeader">
      <a name="section_B" href="#contents" class="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <xsl:value-of select="$message[@id='xslt_messages.touchpoint.selections']" /></a>
    </div>
    <xsl:apply-templates select="//TouchpointAudit/Touchpoints">
      <xsl:with-param name="bTouchpointIndex">False</xsl:with-param>
    </xsl:apply-templates>
  </xsl:template>
  <xsl:template name="OutputProofs">
  <xsl:choose>
    <xsl:when test="count(Versions/Version/Proofs/Proof)!=0">
      <br />
      <table class="reportContentTable">
        <tr>
          <td class="tableHeader" width="14%"><xsl:value-of select="$message[@id='xslt_messages.proofs']" />:</td>
          <td class="tableHeader" width="14%"><xsl:value-of select="$message[@id='xslt_messages.versions']" />:</td>
          <td class="tableHeader" width="14%"><xsl:value-of select="$message[@id='xslt_messages.language']" /></td>
          <td class="tableHeader" width="14%"><xsl:value-of select="$message[@id='xslt_messages.date']" /></td>
          <td class="tableHeader" width="46%"><xsl:value-of select="$message[@id='xslt_messages.path']" /></td>
        </tr>
        <xsl:for-each select="Versions/Version/Proofs/Proof">
          <tr>
            <td></td>
            <td>
              <xsl:choose>
                <xsl:when test="../../@status='wc'">
                  <xsl:value-of select="$message[@id='xslt_messages.working.copy']" />
                </xsl:when>
                <xsl:when test="../../@status='released'">
                  <xsl:value-of select="$message[@id='xslt_messages.released']" />
                </xsl:when>
                <xsl:when test="../../@status='active'">
                  <xsl:value-of select="$message[@id='xslt_messages.active']" />
                </xsl:when>
              </xsl:choose>
            </td>
            <td><xsl:value-of select="./@language"/></td>
            <td>
              <xsl:call-template name="FormatDate">
                <xsl:with-param name="DateTime">
                  <xsl:value-of select="./@completed"/>
                </xsl:with-param>
              </xsl:call-template>
              <xsl:if test="./@stale">
                <xsl:copy-of select="'[Stale]'"/>
              </xsl:if>
            </td>
            <td><xsl:value-of select="."/></td>
          </tr>
        </xsl:for-each>
      </table>
    </xsl:when>
    <xsl:otherwise>
      <br />
      <table class="reportContentTable">
        <tr>
          <td class="tableHeader" width="14%"><xsl:value-of select="$message[@id='xslt_messages.proofs']" />:</td>
          <td class="tableHeader" width="86%"><xsl:value-of select="$message[@id='xslt_messages.no.proofs.for.selection']" /></td>
        </tr>
        </table>
    </xsl:otherwise>
    </xsl:choose>  	
  </xsl:template>
  <xsl:template name="Messages">
  <xsl:choose>
    <xsl:when test="count(Messages/Message)!=0">
      <br />
      <table class="contentContainerLong" width="90%">
        <tr>
          <td class="tableHeader" width="15%"><xsl:value-of select="$message[@id='xslt_messages.messages']" />:</td>
          <td class="tableHeader" width="25%"><xsl:value-of select="$message[@id='xslt_messages.name']" />:</td>
          <td class="tableHeader" width="25%"><xsl:value-of select="$message[@id='xslt_messages.zone']" /></td>
          <td class="tableHeader" width="15%"><xsl:value-of select="$message[@id='xslt_messages.status']" /></td>
          <td class="tableHeader" width="10%"><xsl:value-of select="$message[@id='xslt_messages.type']" /></td>
          <td class="tableHeader" width="10%"><xsl:value-of select="$message[@id='xslt_messages.content.type']" /></td>
        </tr>
        <xsl:for-each select="Messages/Message">
          <tr>
            <td></td>
            <td><xsl:value-of select="."/></td>
            <td><xsl:value-of select="./@zone"/></td>
            <td><xsl:value-of select="./@status"/></td>
            <td><xsl:value-of select="./@type"/></td>
            <td><xsl:value-of select="./@contenttype"/></td>
          </tr>
        </xsl:for-each>
      </table>
    </xsl:when>
    <xsl:otherwise>
      <br />
      <table class="contentContainerLong"  width="90%">
        <tr>
          <td class="tableHeader" width="15%"><xsl:value-of select="$message[@id='xslt_messages.messages']" />:</td>
          <td class="tableHeader" width="85%"><xsl:value-of select="$message[@id='xslt_messages.no.messages.for.this.selection']" /></td>
        </tr>
        </table>
    </xsl:otherwise>
    </xsl:choose>  	
  </xsl:template>
  <xsl:template match="Versions/Version/ApprovalHistory">
    <xsl:choose>    
    <xsl:when test="./Descript">
    <table class="reportContentTable">
      <tr>
        <td>
          <xsl:if test="Descript">
            <table class="outsideTable" width="100%">
              <tr>
                <td class="tableHeader" width="25%" ><xsl:value-of select="$message[@id='xslt_messages.state']" /></td>
                <td class="tableHeader" width="14%" ><xsl:value-of select="$message[@id='xslt_messages.action']" /></td>
                <td class="tableHeader" width="17%" ><xsl:value-of select="$message[@id='xslt_messages.date']" /></td>
                <td class="tableHeader" width="12%" ><xsl:value-of select="$message[@id='xslt_messages.user']" /></td>
                <td class="tableHeader" width="12%" ><xsl:value-of select="$message[@id='xslt_messages.assigned.to']" /></td>
                <td class="tableHeader" width="20%" ><xsl:value-of select="$message[@id='xslt_messages.notes']" /></td>
              </tr>
              <xsl:for-each select="Descript">
                <tr>
                  <td>
                    <xsl:value-of select="."/>
                  </td>
                  <td>
                    <xsl:value-of select="./@action"/>
                  </td>
                  <td>
                    <xsl:call-template name="FormatDate">
                      <xsl:with-param name="DateTime">
                        <xsl:value-of select="./@date" />
                      </xsl:with-param>
                	  <xsl:with-param name="noHour">true</xsl:with-param>
                	  <xsl:with-param name="noMinute">true</xsl:with-param>                       
                    </xsl:call-template>
                  </td>
                  <td>
                    <xsl:value-of select="./@user"/>
                  </td>                  
                  <td>
                    <xsl:value-of select="./@assignedTo"/>
                  </td>
                  <td>
                    <xsl:value-of select="./@notes"/>
                  </td>                  
                </tr>
              </xsl:for-each>
            </table>
          </xsl:if>
        </td>
      </tr>
    </table>
    </xsl:when>
    <xsl:otherwise>
    <table class="reportContentTable">
      <tr>
        <td class="outsideTable" ><xsl:value-of select="$message[@id='xslt_messages.no.events.for.date.range']" /></td>
      </tr>
     </table>   
     <br />
    </xsl:otherwise>
    </xsl:choose>
  </xsl:template>
  <xsl:template name="OutputSatus">
    <xsl:param name="curStatus" />
    <td class="tableHeader">
      <xsl:choose>
        <xsl:when test="./@status='wc'">
          <xsl:value-of select="$message[@id='xslt_messages.working.copy']" />&#160;
        </xsl:when>
        <xsl:when test="./@status='released'">
          <xsl:value-of select="$message[@id='xslt_messages.working.copy']" /> (<xsl:value-of select="$message[@id='xslt_messages.in.approval']" />)&#160;
        </xsl:when>
        <xsl:when test="./@status='active'">
          <xsl:value-of select="$message[@id='xslt_messages.active']" />&#160;
        </xsl:when>
      </xsl:choose>
      <xsl:choose>
        <xsl:when test="./@releasedAsOf">
          <xsl:value-of select="$message[@id='xslt_messages.as.of']" />&#160;
        </xsl:when>
        <xsl:when test="./@created">
          <xsl:value-of select="$message[@id='xslt_messages.created']" />&#160;
        </xsl:when>
        <xsl:when test="./@activeAsOf">
          <xsl:value-of select="$message[@id='xslt_messages.as.of']" />&#160;
        </xsl:when>
      </xsl:choose>
      <xsl:choose>
        <xsl:when test="./@releasedAsOf">
          <xsl:call-template name="FormatDate">
            <xsl:with-param name="DateTime">
              <xsl:value-of select="./@releasedAsOf"/>
            </xsl:with-param>
            <xsl:with-param name="noHour">true</xsl:with-param>
            <xsl:with-param name="noMinute">true</xsl:with-param>             
          </xsl:call-template>          
        </xsl:when>
        <xsl:when test="./@created">
          <xsl:call-template name="FormatDate">
            <xsl:with-param name="DateTime">
              <xsl:value-of select="./@created"/>
            </xsl:with-param>
            <xsl:with-param name="noHour">true</xsl:with-param>
            <xsl:with-param name="noMinute">true</xsl:with-param>             
          </xsl:call-template>          
        </xsl:when>
        <xsl:when test="./@activeAsOf">
          <xsl:call-template name="FormatDate">
            <xsl:with-param name="DateTime">
              <xsl:value-of select="./@activeAsOf"/>
            </xsl:with-param>
            <xsl:with-param name="noHour">true</xsl:with-param>
            <xsl:with-param name="noMinute">true</xsl:with-param>             
          </xsl:call-template>          
        </xsl:when>
      </xsl:choose>
    </td>
  </xsl:template>
  <xsl:template name ="OutputVersions">
    <xsl:if test="./Versions">
      <xsl:for-each select="./Versions/Version">
        <table class="reportContentTable">
          <tr>
            <xsl:call-template name="OutputSatus">
              <xsl:with-param name="curStatus">
                <xsl:value-of select="./@status"/>
              </xsl:with-param>
            </xsl:call-template>
          </tr>
        </table>
        <xsl:apply-templates select="./ApprovalHistory" />
      </xsl:for-each>
    </xsl:if>
  </xsl:template>
  <xsl:template name="OutputDataValue">
    <br />
    <table class="reportContentTable">
      <xsl:if test="./DataValue">
        <xsl:for-each select="./DataValue">
          <xsl:choose>
            <xsl:when test="position() = 1">
              <tr>
                <td class="tableHeader" width="14%"><xsl:value-of select="$message[@id='xslt_messages.date.values']" />: </td>
                <td width="28%">
                  <xsl:value-of select= "."/>
                </td>
                <td width="28%">
                  <xsl:if test="name(following-sibling::*[1])='DataValue'">
                    <xsl:value-of select="(following-sibling::*[1])"/>
                  </xsl:if>
                </td>
                <td width="30%"/>
              </tr>
            </xsl:when>
            <xsl:when test="position() != 1 and position() mod 2 = 1" >
              <tr>
                <td class="tableHeader" width="14%"></td>
                <td width="28%">
                  <xsl:value-of select= "."/>
                </td>
                <td width="28%">
                  <xsl:if test="name(following-sibling::*[1])='DataValue'">
                    <xsl:value-of select="(following-sibling::*[1])"/>
                  </xsl:if>
                </td>
                <td width="30%"/>
              </tr>
            </xsl:when>
          </xsl:choose>
        </xsl:for-each>
      </xsl:if>
    </table>
  </xsl:template>
  <xsl:template match="Selection">
    <xsl:param name="bSelectionIndex" />
    <xsl:param name="nLevel" />
    <xsl:param name="sParent" />
    <xsl:param name="sTouchpointName" />

    <xsl:variable name="currentNode">
      <xsl:value-of select="./Name"/>
    </xsl:variable>

    <xsl:choose>
      <xsl:when test="$bSelectionIndex='True'">
        <ul>
          <li>
            <a href="#{$Selection}{$sTouchpointName}_{$currentNode}" class="plainLink">
              <xsl:value-of select="$currentNode"/>
            </a>
          </li>
          <xsl:apply-templates select="./Selection">
            <xsl:with-param name="bSelectionIndex">
              <xsl:value-of select="$bSelectionIndex"/>
            </xsl:with-param>
            <xsl:with-param name="sTouchpointName">
              <xsl:value-of select="$sTouchpointName"/>
            </xsl:with-param>
          </xsl:apply-templates>
        </ul>
      </xsl:when>
      <xsl:otherwise>
        <div class="HighLigtContent">
          <a class="plainLink" name="{$Selection}{$sTouchpointName}_{$currentNode}" href="#{$sTouchpointName}">
            <xsl:choose>
              <xsl:when test="$nLevel='0'">
                <xsl:value-of select="$message[@id='xslt_messages.level']" />:<xsl:value-of select="$nLevel" />==&gt;<xsl:value-of select="$currentNode" />
              </xsl:when>
              <xsl:otherwise>
                <xsl:value-of select="$message[@id='xslt_messages.level']" />:<xsl:value-of select="$nLevel" />==&gt;<xsl:value-of select="$sParent" />==&gt;<xsl:value-of select="$currentNode" />
              </xsl:otherwise>
            </xsl:choose>
          </a>
        </div>
        <table class="nestedTableLevel{$nLevel+1}">
          <tr>
            <td>
            	<xsl:call-template name="OutputProofs" />
              <xsl:if test="$nLevel!='0'">
                <xsl:call-template name="OutputDataValue" />
              </xsl:if>
              <xsl:call-template name="OutputVersions" />
              <xsl:call-template name="Messages" />
              <xsl:apply-templates select="./Selection">
                <xsl:with-param name="bSelectionIndex">
                  <xsl:value-of select="$bSelectionIndex"/>
                </xsl:with-param>
                <xsl:with-param name="nLevel">
                  <xsl:copy-of select="$nLevel+1"/>
                </xsl:with-param>
                <xsl:with-param name="sParent">
                  <xsl:copy-of select="$sParent" />
                  <xsl:if test="$nLevel!='0'">==&gt;</xsl:if>
                  <xsl:copy-of select="$currentNode"/>
                </xsl:with-param>
                <xsl:with-param name="sTouchpointName">
                  <xsl:value-of select="$sTouchpointName"/>
                </xsl:with-param>
              </xsl:apply-templates>
            </td>
          </tr>
        </table>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>
  <xsl:template name="OutputTouchpointSelections">
    <xsl:param name="sTouchpointName" />
    <div class="sectionContent">
      <table class="reportContentTable">
        <tr>
          <td style="vertical-align:top" width="14%" class="tableHeader">
            <xsl:value-of select="$message[@id='xslt_messages.touchpoint.selections']" />:
          </td>
          <td>
            <xsl:apply-templates select="./Selections/Selection">
              <xsl:with-param name="bSelectionIndex">True</xsl:with-param>
              <xsl:with-param name="sTouchpointName">
                <xsl:value-of select="$sTouchpointName"/>
              </xsl:with-param>
            </xsl:apply-templates>
          </td>
        </tr>
      </table>
    </div>
    <div class="sectionContent">
      <xsl:apply-templates select="./Selections/Selection">
        <xsl:with-param name="bSelectionIndex">False</xsl:with-param>
        <xsl:with-param name="nLevel">0</xsl:with-param>
        <xsl:with-param name="sParent" />
        <xsl:with-param name="sTouchpointName">
          <xsl:value-of select="$sTouchpointName"/>
        </xsl:with-param>
      </xsl:apply-templates>
    </div>
    <br />
  </xsl:template>
  <xsl:template name="OutputTouchpoint">
    <xsl:param name="bTouchpointIndex" />
    <xsl:for-each select="./Touchpoint">
      <xsl:variable name="seq" select="position()" />
      <xsl:variable name="TouchpointName">
        <xsl:value-of select="./Name"/>
      </xsl:variable>
      <xsl:variable name="HrefTouchpointName">#<xsl:value-of select="./Name"/>
      </xsl:variable>
      <xsl:choose>
        <xsl:when test="$bTouchpointIndex='True'">
          <li>
            <a href="{$HrefTouchpointName}{$seq}" class="plainLink">
              <xsl:value-of select="$TouchpointName"/>
            </a>
          </li>
        </xsl:when >
        <xsl:otherwise>
          <div class="sectionSubHeader">
            <a name="{$TouchpointName}{$seq}" href="#TouchpointIndex" class="plainLink">
              <xsl:value-of select="$TouchpointName"/>
            </a>
          </div>
          <xsl:call-template name="OutputTouchpointSelections">
            <xsl:with-param name ="sTouchpointName">
              <xsl:value-of select="$TouchpointName"/>
            </xsl:with-param>
          </xsl:call-template>
        </xsl:otherwise>
      </xsl:choose>
    </xsl:for-each>
  </xsl:template>
  <xsl:template match="//TouchpointAudit/Touchpoints" >
    <xsl:param name="bTouchpointIndex" />
    <xsl:choose>
      <xsl:when test="$bTouchpointIndex='True'">
        <div class="sectionContent">
          <ul>
            <xsl:call-template name="OutputTouchpoint">
              <xsl:with-param name="bTouchpointIndex">
                <xsl:value-of select="$bTouchpointIndex"/>
              </xsl:with-param>
            </xsl:call-template>
          </ul>
        </div>
      </xsl:when>
      <xsl:otherwise>
        <xsl:call-template name="OutputTouchpoint">
          <xsl:with-param name="bTouchpointIndex">
            <xsl:value-of select="$bTouchpointIndex"/>
          </xsl:with-param>
        </xsl:call-template>
      </xsl:otherwise>
    </xsl:choose>
  </xsl:template>
  <xsl:template match="//TouchpointAudit">
    <div class="sectionContent">
      <table class="reportContentTable">
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.export.version']" />: </td>
          <td>
            <xsl:value-of select="./@ver"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested.by']" />: </td>
          <td>
            <xsl:value-of select="./Metadata/User"/>
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.requested']" />: </td>
          <td>
            <xsl:call-template name="FormatDate">
              <xsl:with-param name="DateTime">
                <xsl:value-of select="./Metadata/RequestDate"/>
              </xsl:with-param>
            </xsl:call-template>            
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <br />
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.report.type']" />: </td>
          <td>
            <xsl:choose>
          	  <xsl:when test="./@type='audit'">
          	  	<xsl:value-of select="$message[@id='xslt_messages.touchpoint.audit.report']" />
          	  </xsl:when>
          	  <xsl:when test="./@type='change'">
          	    <xsl:value-of select="$message[@id='xslt_messages.touchpoint.change.report']" />
          	  </xsl:when>
          	  <xsl:otherwise><xsl:value-of select="$message[@id='xslt_messages.unknown.type']" /></xsl:otherwise>
          	</xsl:choose>
          </td>
        </tr>
        <xsl:if test="./@type='change'"> 
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.from.date']" />: </td>
          <td>
            <xsl:call-template name="FormatDate">
              <xsl:with-param name="DateTime">
                <xsl:value-of select="./Metadata/FromDate"/>
              </xsl:with-param>
            </xsl:call-template>            
          </td>
        </tr>
        <tr>
          <td class="singleColumnLabel"><xsl:value-of select="$message[@id='xslt_messages.to.date']" />: </td>
          <td>
            <xsl:choose>
           		<xsl:when test="./Metadata/ToDate">
           		 <xsl:call-template name="FormatDate">
              		<xsl:with-param name="DateTime">
                		<xsl:value-of select="./Metadata/ToDate"/>
              		</xsl:with-param>
            	</xsl:call-template>
           		</xsl:when>
           		<xsl:otherwise>
           		   <xsl:value-of select="$message[@id='xslt_messages.unspecified.assuming.requested.date']" />
           		</xsl:otherwise>
            </xsl:choose>                        
          </td>
        </tr>
        </xsl:if>
      </table>
    </div>
  </xsl:template>
  <xsl:template name="ReportSummary">
    <div class="sectionHeader">
      <a class="sectionHeaderPlainLink" name="section_A" href="#contents"><xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
    </div>
    <div class="sectionSubHeader"><xsl:value-of select="$message[@id='xslt_messages.request.overview']" /></div>
    <xsl:apply-templates select="//TouchpointAudit" />
    <div class="sectionSubHeader">
      <a class="plainLink" name="TouchpointIndex"><xsl:value-of select="$message[@id='xslt_messages.touchpoint.index']" /></a>
    </div>
    <xsl:apply-templates select="//TouchpointAudit/Touchpoints">
      <xsl:with-param name="bTouchpointIndex">True</xsl:with-param>
    </xsl:apply-templates>
  </xsl:template>
  <xsl:template name="ReportContents">
    <div class="sectionHeader">
      <a name="sectionHeaderPlainLink"><xsl:value-of select="$message[@id='xslt_messages.contents']" /></a>
    </div>
    <div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> A: <a href="#section_A"><xsl:value-of select="$message[@id='xslt_messages.report.request.summary']" /></a>
      </div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> B: <a href="#section_B"><xsl:value-of select="$message[@id='xslt_messages.touchpoint.selections']" /></a>
      </div>
      <div class="sectionSubHeader">
        <xsl:value-of select="$message[@id='xslt_messages.section']" /> C: <a href="#section_C"><xsl:value-of select="$message[@id='xslt_messages.touchpoint.reference.data']" /></a>
      </div>
    </div>
    <br />
  </xsl:template>
</xsl:stylesheet>