<?xml version="1.0" encoding="UTF-8" ?>
<xsl:stylesheet 
  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" 
  version="2.0">
    <xsl:include href="reportStyleTemplate.xsl"/>
	<xsl:output method="text"/>
	<xsl:output method="html" indent="yes" name="html"/>
	
	<xsl:template match="/">
	    <!--Output job report and link to its batch report-->
		<xsl:result-document href="BatchReportJobLevel.html" format="html">
		  <html>
		   <head>
		   	  <title>BatchReportJobLevel.html</title>
				<link rel="stylesheet" type="text/css" href="../includes/themes/commoncss/theme_minimal.css" />
				<link rel="stylesheet" type="text/css" href="../includes/javascript/jQueryPlugins/styledInterfaces/styleActionElement.css" />
				<script type="text/javascript" src="../includes/javascript/jQueryPlugins/styledInterfaces/jquery.styleActionElement.js"></script>
				<script type="text/javascript" src="../includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"></script>	
				<script type="text/javascript" src="../includes/javascript/popup.js" ></script>
				<script type="text/javascript" src="../includes/javascript/dynamic_tables.js" ></script>
				<script type="text/javascript" src="../includes/javascript/popupActions.js"></script>
		   	  	<script type="text/javascript" >
					function openReportWindow(reportId){
					    var URL = document.URL;
						var newWindow = window.open(URL + '&#x26;' + 'batchReportId='+reportId, 'Popup_Popup', 'scrollbars=yes, statusbar=no, toolbar=yes, menubar=no, location=no, resizable=yes, width=850, height=900');
						newWindow.focus();
					}
				</script>		   
		   </head>
		   <xsl:call-template name="ReportHead"/>
		   <body>
		     <div align="left" style="background-color: #ffffff;">
				<div id="reportContainer" style="padding: 8px;">	
				 <xsl:call-template name="ReportSummary" />
				 <xsl:call-template name="ReportJobs" />
				</div>
			</div>
		   </body>
		  </html>
		</xsl:result-document>
	    <!--Output batch report for each job-->
		<xsl:for-each select="//Report/Jobs/Job">
			<xsl:if test="./Batches/Batch">
			<xsl:variable name="filename" select="concat(@id,'.html')" /> <!-- Creating  -->
			<xsl:result-document href="{$filename}" format="html">
				<html>
				  <head>
				  	<title>BatchReportBatchLevel.html</title>
				  	<script type="text/javascript" >
						function backReportWindow(){
						    var URL = document.URL;
						    var endPosition = URL.search("&#x26;batchReportId");
							var newWindow = window.open(URL.substring(0, endPosition), 'Popup_Popup', 'scrollbars=yes, statusbar=no, toolbar=no, menubar=no, location=no, resizable=yes, width=850, height=900');
							newWindow.focus();
						}
					</script>						  
				  </head>
		          <xsl:call-template name="ReportHead"/>
				  <body>
				   <div align="left" style="background-color: #ffffff;">
					<div id="reportContainer" style="padding: 8px;">
					   <xsl:call-template name="JobSummary" />
					   <xsl:call-template name="ReportJob" />
					</div>
					</div>
				  </body>
				</html>
			</xsl:result-document>
			</xsl:if>
		</xsl:for-each>
	</xsl:template>
	
	<xsl:template name="ReportJob">
	<div class="sectionContent">
		<table class="coloredTable">
		    <colgroup>
               <col class="col" />
               <col class="col" />
               <col class="col" />
               <col class="col" />
               <col class="col" />
               <col class="col" />
           </colgroup>
             <thead class="tableHeader">
                <tr>
                   <th rowspan="2" width="6%" style="text-align:center;"><xsl:value-of select="$message[@id='xslt_messages.batch.num']" /></th>
                   <th rowspan="2" width="30%" style="text-align:center;"><xsl:value-of select="$message[@id='xslt_messages.ran.on']" /></th>
                   <th rowspan="2" width="12%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.recipients']" /></th>
                   <th colspan="2" width="26%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.elapsed.time']" /></th>
                   <th colspan="2" width="26%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.recipients.minute']" /></th>
                </tr>
                <tr>
                   <th width="13%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.qualification']" /></th>
                   <th width="13%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.delivery']" /></th>
                   <th width="13%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.qualification']" /></th>
                   <th width="13%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.delivery']" /></th>
                </tr>
             </thead>
			<tbody>
			<xsl:for-each select="./Batches/Batch" >
	          <xsl:variable name="backgroundColor">
	            <xsl:choose>
	            <xsl:when test="position() mod 2=1">background-color:#EEEEEE;</xsl:when>
	            <xsl:otherwise>background-color:#FFFFFF;</xsl:otherwise>
	            </xsl:choose>
	          </xsl:variable>
	          <tr style="{$backgroundColor}" >
				<td style="text-align:center;"><xsl:value-of select="./@id"/></td>
				<td style="text-align:center;">
				    <xsl:call-template name="FormatDate">
					  <xsl:with-param name="DateTime">
						<xsl:value-of select="./@start"/>
					  </xsl:with-param>
					</xsl:call-template>
				</td>
				<td><xsl:value-of select="./@recipients"/></td>
				<td><xsl:value-of select="./Qualification/@elapsed"/></td>
				<td><xsl:value-of select="./Delivery/@elapsed"/></td>
				<td><xsl:value-of select="./Qualification/@recipientsPerMin"/></td>
				<td><xsl:value-of select="./Delivery/@recipientsPerMin"/></td>
			</tr>
			</xsl:for-each>
			</tbody>
		</table>
	  </div>
	</xsl:template>	
	<xsl:template name="ReportJobs">
    <div class="sectionContent">
      <xsl:choose>
    	<xsl:when test="//Report/Jobs/Job">
      <table class="coloredTable">
      		<colgroup>
               <col class="col" />
               <col class="col" />
               <col class="col" />
               <col class="col" />
               <col class="col" />
               <col class="col" />
           </colgroup>
           <thead>
              <tr>
                 <th rowspan="2" width="6%">Job#</th>
                 <th rowspan="2" width="29%" style="text-align:left"><xsl:value-of select="$message[@id='xslt_messages.touchpoint']" /></th>
                 <th rowspan="2" width="5%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.batches']" /></th>
                 <th rowspan="2" width="12%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.recipients']" /></th>
                 <th colspan="2" width="24%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.elapsed.time']" /></th>
                 <th colspan="2" width="24%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.recipients.minute']" /></th>
              </tr>
              <tr>
                 <th width="12%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.qualification']" /></th>
                 <th width="12%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.delivery']" /></th>
                 <th width="12%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.qualification']" /></th>
                 <th width="12%" style="text-align:right"><xsl:value-of select="$message[@id='xslt_messages.delivery']" /></th>
              </tr>
           </thead>
        <tbody>
          <xsl:for-each select="//Report/Jobs/Job">
          	 <xsl:variable name="backgroundColor">
	            <xsl:choose>
	            <xsl:when test="position() mod 2=1">background-color:#EEEEEE;</xsl:when>
	            <xsl:otherwise>background-color:#FFFFFF;</xsl:otherwise>
	            </xsl:choose>
	          </xsl:variable>
	          <tr style="{$backgroundColor}" >
            <td style="text-align:center;">
			  <xsl:choose>
			  <xsl:when test="./Batches/Batch">
              <span>
                <a href="javascript:openReportWindow({@id})"><xsl:value-of select="@id"/></a>		
              </span>
			  </xsl:when>
			 <xsl:otherwise><xsl:value-of select="@id"/></xsl:otherwise>
			  </xsl:choose>    
            </td>
            <td style="text-align:left"><xsl:value-of select="Touchpoint/Name"/></td>
            <td><xsl:value-of select="count(./Batches/Batch)"/></td>
            <td><xsl:value-of select="./@recipients"/></td>
            <td><xsl:value-of select="./Qualification/@elapsed"/></td>
            <td><xsl:value-of select="./Delivery/@elapsed"/></td>
            <td><xsl:value-of select="./Qualification/@recipientsPerMin"/></td>
            <td><xsl:value-of select="./Delivery/@recipientsPerMin"/></td>
          </tr>
          </xsl:for-each>
        </tbody>
      </table>
      </xsl:when>
      <xsl:otherwise>No data</xsl:otherwise>
      </xsl:choose>
    </div>
  </xsl:template>
  <xsl:template name="JobSummary">
  	<div name="backLink" class="sectionContent">			
			<a href="javascript:backReportWindow()"><xsl:value-of select="$message[@id='xslt_messages.back.to.batch.report']" /></a>
	</div>
	<div class="sectionHeader"><xsl:value-of select="$message[@id='xslt_messages.batch.report']" /> -- <xsl:value-of select="$message[@id='xslt_messages.batch.level']" /></div>
	<div class="sectionContent">
		<table width="100%" cellspacing="0" cellpadding="0" border="0">
			<tr>
				<td  class="multiColumnLabel"> </td>
				<td class="multiColumnvalue"></td>
				<td  class="multiColumnLabel"> </td>
				<td class="multiColumnvalue"></td>
			</tr>
			<tr>
				<td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.for']" />:</td>
				<td><xsl:value-of select="Touchpoint/Name"/></td>
				<td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.job']" />: </td>
				<td><xsl:value-of select="@id"/></td>
			</tr>
			<tr>
				<td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.run.date']" />:</td>
				<td>
					<xsl:call-template name="FormatDate">
					  <xsl:with-param name="DateTime">
						<xsl:value-of select="./@start"/>
					  </xsl:with-param>
					   <xsl:with-param name="noSecond">true
              </xsl:with-param>             	       
					</xsl:call-template>
				</td>
				<td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.batches']" />:</td>
				<td><xsl:value-of select="count(./Batches/Batch)"/></td>
			</tr>
		</table>
	</div>
  </xsl:template>
  <xsl:template name="ReportSummary">
	<xsl:param name="ReportLevel" />  
	<div class="sectionHeader"><xsl:value-of select="$message[@id='xslt_messages.batch.report']" /> -- <xsl:value-of select="$message[@id='xslt_messages.job.level']" /></div>
    <div class="sectionContent">
      <table width="100%" cellspacing="0" cellpadding="0" border="0">
        <tr>
	        <td  class="multiColumnLabel"> </td>
			<td class="multiColumnvalue"></td>
			<td  class="multiColumnLabel"> </td>
			<td class="multiColumnvalue"></td>
        </tr>
        <tr>
          <td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.requested.by']" />: </td>
          <td>
            <xsl:value-of select="/Report/Request/User"/>
          </td>          
          <td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.date.range']" />:</td>
          <td>
            <xsl:call-template name="FormatDate">
              <xsl:with-param name="DateTime">
                <xsl:value-of select="/Report/Request/FromDate"/>
              </xsl:with-param>
               <xsl:with-param name="noSecond">true
              </xsl:with-param>
               <xsl:with-param name="noHour">true
              </xsl:with-param>
              <xsl:with-param name="noMinute">true
              </xsl:with-param>
            </xsl:call-template> ~ <xsl:call-template name="FormatDate">
              <xsl:with-param name="DateTime">
                <xsl:value-of select="/Report/Request/ToDate"/>
              </xsl:with-param>
               <xsl:with-param name="noSecond">true
              </xsl:with-param>
               <xsl:with-param name="noHour">true
              </xsl:with-param>
              <xsl:with-param name="noMinute">true
              </xsl:with-param>
            </xsl:call-template>
          </td>
        </tr>
        <tr>
          <td class="tableHeader"><xsl:value-of select="$message[@id='xslt_messages.job.count']" />:</td>
          <td>
            <xsl:value-of select="count(/Report/Jobs/Job)"/>
          </td>
          <td /><td />
        </tr>
      </table>
    </div>
    <br />
  </xsl:template>
</xsl:stylesheet>