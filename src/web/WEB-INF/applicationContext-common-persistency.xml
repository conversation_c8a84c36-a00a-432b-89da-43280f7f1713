<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
						http://www.springframework.org/schema/context
						http://www.springframework.org/schema/context/spring-context-4.2.xsd
                     	http://www.springframework.org/schema/tx
						http://www.springframework.org/schema/tx/spring-tx-4.2.xsd
                    	http://www.springframework.org/schema/aop 
                    	http://www.springframework.org/schema/aop/spring-aop-4.2.xsd">

	<bean class="com.prinova.messagepoint.classpath.MessagepointPropertySourceFactory" />

	<bean id="transactionManager" 
		class="org.springframework.transaction.jta.JtaTransactionManager">		
		<property name="userTransactionName">
			<value>java:comp/UserTransaction</value>
		</property>
	</bean>
	
	<!-- Defines the hibernate properties -->
	<bean id="hibernateProperties"
		class="org.springframework.beans.factory.config.PropertiesFactoryBean">
		<property name="properties">
			<props>
			    <prop key="messagepoint.data.source">messagepoint</prop>
				<!-- <prop key="hibernate.dialect">${db.messagepoint.hibernate.dialect}</prop> -->
				<prop key="hibernate.dialect">com.prinova.messagepoint.util.MessagepointPostgresDialect</prop>

				<prop key="hibernate.show_sql">false</prop>
				<prop key="hibernate.format_sql">false</prop>
				<prop key="hibernate.jdbc.fetch_size">50</prop>
				<prop key="hibernate.jdbc.batch_size">20</prop>
				
				<prop key="hibernate.transaction.coordinator_class">jta</prop>
				<prop key="hibernate.transaction.jta.platform">org.hibernate.service.jta.platform.internal.JBossAppServerJtaPlatform</prop>
				<!-- <prop key="hibernate.transaction.jta.platform">org.hibernate.service.jta.platform.internal.JBossStandAloneJtaPlatform</prop> -->

				<prop key="hibernate.allow_update_outside_transaction">true</prop>

				<prop key="hibernate.multiTenancy">SCHEMA</prop>
				<prop key="hibernate.tenant_identifier_resolver">com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver</prop>
				<prop key="hibernate.multi_tenant_connection_provider">com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider</prop>
				 				
				<!-- ONLY these 7 c3p0 properties may be set here...the rest must be managed inside the c3p0.properties file! -->
				<prop key="hibernate.c3p0.acquire_increment">2</prop> <!-- How many connections to acquire when allocating more for the pool -->
				<prop key="hibernate.c3p0.idle_test_period">200</prop> <!-- If this is a number greater than 0, c3p0 will test all idle, pooled but unchecked-out connections, every this number of seconds. -->
				<prop key="hibernate.c3p0.timeout">300</prop> <!-- The seconds a Connection can remain pooled but unused before being discarded. Zero means idle connections never expire. -->
				<prop key="hibernate.c3p0.max_size">100</prop> <!-- The maximum number of connections to keep in the pool. -->
				<prop key="hibernate.c3p0.min_size">5</prop> <!-- The minimum number of connections to keep in the pool. -->
				<prop key="hibernate.c3p0.validate">false</prop> <!-- VERY expensive operation, so leave this set to false! -->
				<prop key="hibernate.c3p0.max_statements">0</prop> <!-- The size of c3p0's PreparedStatement cache. Zero means statement caching is turned off. -->

				<!-- <prop key="hibernate.cache.provider_class">org.hibernate.cache.EhCacheProvider</prop> -->				
				<prop key="hibernate.cache.provider_class">org.hibernate.cache.internal.NoCachingRegionFactory</prop>
				
				<prop key="hibernate.cache.use_second_level_cache">false</prop>
				<prop key="hibernate.cache.use_query_cache">false</prop>	
				<prop key="hibernate.generate_statistics">false</prop>			
				<prop key="hibernate.query.substitutions">true 1, false 0</prop>
				
				<prop key="hibernate.enable_lazy_load_no_trans">true</prop>
			</props>
		</property>
	</bean>	
	
	<tx:annotation-driven transaction-manager="transactionManager" />  
	
</beans>