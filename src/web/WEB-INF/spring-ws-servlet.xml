<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:sws="http://www.springframework.org/schema/web-services"
	   xmlns:util="http://www.springframework.org/schema/util"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
       http://www.springframework.org/schema/web-services http://www.springframework.org/schema/web-services/web-services-2.0.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.2.xsd http://www.springframework.org/schema/util https://www.springframework.org/schema/util/spring-util.xsd">

    <context:component-scan base-package="com.prinova.messagepoint.platform.ws"/>

    <sws:annotation-driven/>

	<!-- Order of the interceptors is important, because it defines the order they are fired in -->
	<sws:interceptors>
		<bean id="wsBranchInterceptor" class="com.prinova.messagepoint.platform.ws.MpCustomerInterceptor"/>
		<bean id="wsSecurityInterceptor" class="com.prinova.messagepoint.platform.ws.MpSecurityInterceptor">
			<property name="validationActions" value="UsernameToken" />
    	    <property name="validationCallbackHandler" ref="springSecurityHandler"/>
	    </bean>
	    <bean id="wsAuthEndpointInterceptor" class="com.prinova.messagepoint.platform.ws.WSAuthInterceptor"/>
	</sws:interceptors>
	
    <sws:dynamic-wsdl id="api" portTypeName="api" locationUri="/api/"
                      targetNamespace="http://messagepoint.com/api/definitions">
        <sws:xsd location="/WEB-INF/api.xsd"/>
    </sws:dynamic-wsdl>
</beans>
