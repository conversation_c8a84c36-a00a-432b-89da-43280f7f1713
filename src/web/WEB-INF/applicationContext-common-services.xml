<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans-4.2.xsd">
                    	
	<!-- Messagepoint Service Manager -->
	<bean id="serviceManager" class="com.prinova.messagepoint.platform.services.MessagepointServiceFactory" factory-method="getInstance">
		<property name="services">
			<map>
				<!-- Audit Service Mappings -->
				<entry key="audit.CreateAuditEventService" 			   					value-ref="audit.CreateAuditEventService" />
				
				<!-- Attachments Service Mappings 
				     ========================== -->
				<entry key="attachment.CreateOrUpdateAttachmentService" 			   	value-ref="attachment.CreateOrUpdateAttachmentService" />
				<entry key="attachment.DeleteAttachmentService" 			   			value-ref="attachment.DeleteAttachmentService" />

				<!-- Brand Service Mappings
				     ========================== -->
				<entry key="brand.CreateOrUpdateBrandProfileService" 			   		value-ref="brand.CreateOrUpdateBrandProfileService" />
				<entry key="brand.BulkDeleteBrandProfileService" 						value-ref="brand.BulkDeleteBrandProfileService" />

				<!-- Tasks Service Mappings 
				     ========================== -->
				<entry key="tasks.CreateOrUpdateTaskService" 							value-ref="tasks.CreateOrUpdateTaskService" />
				<entry key="tasks.DeleteTaskService" 									value-ref="tasks.DeleteTaskService" />
				<entry key="tasks.BulkUpdateTaskService" 								value-ref="tasks.BulkUpdateTaskService" />
				<entry key="tasks.BulkDeleteTaskService" 								value-ref="tasks.BulkDeleteTaskService" />

				<!-- Projects Service Mappings 
				     ========================== -->
				<entry key="projects.CreateOrUpdateProjectService" 						value-ref="projects.CreateOrUpdateProjectService" />
				<entry key="projects.DeleteProjectService" 								value-ref="projects.DeleteProjectService" />
				<entry key="projects.BulkDeleteProjectService" 							value-ref="projects.BulkDeleteProjectService" />


				<!-- Message Service Mappings
				     ========================== -->
				<entry key="content.UpdateContentObjectPreviewService" 					value-ref="content.UpdateContentObjectPreviewService" />
				<entry key="content.ContentObjectDynamicVariantPreviewService" 			value-ref="content.ContentObjectDynamicVariantPreviewService" />
				<entry key="content.DeleteContentObjectPreviewService" 					value-ref="content.DeleteContentObjectPreviewService" />
				<entry key="content.BulkDeleteWorkingContentObjectDataService" 			value-ref="content.BulkDeleteWorkingContentObjectDataService" />
				<entry key="content.BulkArchiveContentObjectService" 		    		value-ref="content.BulkArchiveContentObjectService" />
				<entry key="content.BulkCheckoutContentObjectService" 		    		value-ref="content.BulkCheckoutContentObjectService" />
				<entry key="content.BulkDeleteArchivedContentObjectDataService" 		value-ref="content.BulkDeleteArchivedContentObjectDataService" />
				<entry key="message.GetMessageContentService" 							value-ref="message.GetMessageContentService" />
				<entry key="content.BulkUpdateContentObjectService" 		    		value-ref="content.BulkUpdateContentObjectService" />
				<entry key="content.UpdateContentObjectMoveToZoneService" 				value-ref="content.UpdateContentObjectMoveToZoneService" />
				<entry key="contentObject.MessageVersioningService" 					value-ref="contentObject.MessageVersioningService" />
                <entry key="message.UpdateMessageHashService"                           value-ref="message.UpdateMessageHashService" />
				<entry key="content.ContentPowerEditService"                           	value-ref="content.ContentPowerEditService" />
				<entry key="content.ContentObjectAssociationPostSaveService"            value-ref="content.ContentObjectAssociationPostSaveService" />
				<entry key="content.ContentUpdateService"                           	value-ref="content.ContentUpdateService" />
				<entry key="content.ContentObjectPushToTranslationService" 				value-ref="content.ContentObjectPushToTranslationService" />
				
				<!-- Embedded Content Service Mappings 
				     ========================== -->

				<!-- Content Library Service Mappings 
				     ========================== -->
				<entry key="content.ContentObjectBulkImageCreateService" 				value-ref="content.ContentObjectBulkImageCreateService" />
				<entry key="content.ContentObjectBulkSftpImageSyncService" 				value-ref="content.ContentObjectBulkSftpImageSyncService" />
                <entry key="contentObject.GlobalContentObjectVersioningService"         value-ref="contentObject.GlobalContentObjectVersioningService" />

				<!-- Export Service Mappings
				     ========================== -->
				<entry key="export.CreateOrUpdateAuditReportService" 					value-ref="export.CreateOrUpdateAuditReportService" />
				<entry key="export.GenerateMessageAuditReportService" 					value-ref="export.GenerateMessageAuditReportService" />
				<entry key="export.ExportMessageToXMLService" 							value-ref="export.ExportMessageToXMLService" />
				<entry key="export.GenerateInsertScheduleAuditReportService" 			value-ref="export.GenerateInsertScheduleAuditReportService" />
				<entry key="export.ExportInsertScheduleToXMLService" 					value-ref="export.ExportInsertScheduleToXMLService" />
				<entry key="export.ExportDocumentToXMLService"							value-ref="export.ExportDocumentToXMLService" />
				<entry key="export.ExportDynamicContentObjectService"					value-ref="export.ExportDynamicContentObjectService" />
				<entry key="export.ExportTPVariantsToExcelService"						value-ref="export.ExportTPVariantsToExcelService" />
				<entry key="export.ExportTPMessagesToExcelService"						value-ref="export.ExportTPMessagesToExcelService" />
				<entry key="export.ExportTPVariantsToXMLService" 						value-ref="export.ExportTPVariantsToXMLService" />
				<entry key="export.ExportTPMessagesToXMLService"						value-ref="export.ExportTPMessagesToXMLService" />
				<entry key="export.ExportVariantMetadataToExcelService"					value-ref="export.ExportVariantMetadataToExcelService" />
				<entry key="export.ExportRationalizerDocumentToExcelService"			value-ref="export.ExportRationalizerDocumentToExcelService" />
				<entry key="export.ExportRationalizerDocumentToXMLService"				value-ref="export.ExportRationalizerDocumentToXMLService" />
				<entry key="export.ExportRationalizerMetadataTagsToExcelService"		value-ref="export.ExportRationalizerMetadataTagsToExcelService" />
				<entry key="export.ExportRationalizerQueryResultToExcelService"			value-ref="export.ExportRationalizerQueryResultToExcelService" />
				<entry key="export.ExportUsersToExcelService"							value-ref="export.ExportUsersToExcelService" />
				<entry key="export.GenerateMessagepointObjectExportService"				value-ref="export.GenerateMessagepointObjectExportService" />
				<entry key="export.GenerateRationalizerObjectExportService"				value-ref="export.GenerateRationalizerObjectExportService" />
				<entry key="export.ExportTouchpointToXMLService" 						value-ref="export.ExportTouchpointToXMLService" />
				<entry key="export.GenerateTouchpointAuditReportService" 				value-ref="export.GenerateTouchpointAuditReportService" />		
				<entry key="export.ExportOperationsReportToXMLService" 					value-ref="export.ExportOperationsReportToXMLService" />
				<entry key="export.GenerateOperationsReportService" 					value-ref="export.GenerateOperationsReportService" />				
				<entry key="export.ExportTpDeliveryReportToXMLService" 					value-ref="export.ExportTpDeliveryReportToXMLService" />
				<entry key="export.GenerateTpDeliveryReportService" 					value-ref="export.GenerateTpDeliveryReportService" />	
				<entry key="export.ExportProjectToXMLService" 							value-ref="export.ExportProjectToXMLService" />
				<entry key="export.GenerateProjectAuditReportService" 					value-ref="export.GenerateProjectAuditReportService" />
				<entry key="export.ExportImageLibraryToXMLService" 						value-ref="export.ExportImageLibraryToXMLService" />
				<entry key="export.GenerateImageLibraryAuditReportService" 				value-ref="export.GenerateImageLibraryAuditReportService" />
				<entry key="export.ExportSmartTextToXMLService" 						value-ref="export.ExportSmartTextToXMLService" />
				<entry key="export.GenerateSmartTextAuditReportService" 				value-ref="export.GenerateSmartTextAuditReportService" />
				<entry key="export.ExportUsersToXMLService" 							value-ref="export.ExportUsersToXMLService" />
				<entry key="export.GenerateCompareReportService"						value-ref="export.GenerateCompareReportService" />
				<entry key="export.ExportRolesToExcelService" 							value-ref="export.ExportRolesToExcelService" />
				<entry key="export.ExportMessagesOnApprovalToExcelService" 				value-ref="export.ExportMessagesOnApprovalToExcelService" />
				<entry key="export.ExportJobPerformanceReportToXMLService"				value-ref="export.ExportJobPerformanceReportToXMLService" />
				<entry key="export.ExportRationalizerDocumentsToMessagepointService"	value-ref="export.ExportRationalizerDocumentsToMessagepointService" />
				<entry key="export.ExportContentToExcelService"							value-ref="export.ExportContentToExcelService" />
				<entry key="export.ExportContentToJSONService"							value-ref="export.ExportContentToJSONService" />

				<!-- Imports Service Mappings -->
				<entry key="imports.ImportTouchpointService"							value-ref="imports.ImportTouchpointService"/>
				<entry key="imports.ImportBulkUploadService"							value-ref="imports.ImportBulkUploadService"/>
				<entry key="imports.BulkUploadVariantsService"							value-ref="imports.BulkUploadVariantsService"/>
				<entry key="imports.ImportMessagesService"								value-ref="imports.ImportMessagesService"/>
				<entry key="imports.ImportEmbeddedContentsService"						value-ref="imports.ImportEmbeddedContentsService"/>
				<entry key="imports.ImportContentLibraryService"						value-ref="imports.ImportContentLibraryService"/>
				<entry key="imports.BulkUploadUsersUpdateService"						value-ref="imports.BulkUploadUsersUpdateService"/>
				<entry key="imports.ImportJSONtoContentService"							value-ref="imports.ImportJSONtoContentService"/>

				<!-- Selectable Message/constant Service Mappings 
				     ========================== -->
				<entry key="content.ContentObjectContentSelectionUpdateService" 			value-ref="content.ContentObjectContentSelectionUpdateService" />
				<entry key="content.CreateContentObjectDynamicVariantTreeNodeService" 		value-ref="content.CreateContentObjectDynamicVariantTreeNodeService" />
				<entry key="content.MessageContentSelectionCheckInProductionService" 		value-ref="content.MessageContentSelectionCheckInProductionService" />
				<entry key="content.ContentObjectDynamicVariantCloneService" 				value-ref="content.ContentObjectDynamicVariantCloneService" />
				<entry key="content.ContentObjectVariantCloneCOWEditService" 				value-ref="content.ContentObjectVariantCloneCOWEditService" />
				<entry key="content.DeleteContentObjectDynamicVariantTreeNodeService"		value-ref="content.DeleteContentObjectDynamicVariantTreeNodeService" />
				<entry key="content.TouchpointContentSelectionUpdateService" 				value-ref="content.TouchpointContentSelectionUpdateService" />
				<entry key="content.BulkUpdateTextStylesService"							value-ref="content.BulkUpdateTextStylesService" />
				<entry key="content.BulkDeleteStylesService"								value-ref="content.BulkDeleteStylesService" />
				<entry key="content.CreateOrUpdateTextStyleService"							value-ref="content.CreateOrUpdateTextStyleService" />
				<entry key="content.TextStyleService"										value-ref="content.TextStyleService" />
				<entry key="content.CreateOrUpdateParagraphStyleService"					value-ref="content.CreateOrUpdateParagraphStyleService" />
				<entry key="content.CloneParagraphStyleService"								value-ref="content.CloneParagraphStyleService" />
				<entry key="content.CreateOrUpdateListStyleService"							value-ref="content.CreateOrUpdateListStyleService" />
				<entry key="content.CloneListStyleService"									value-ref="content.CloneListStyleService" />
				<entry key="content.CreateHistoricalContentObjectAssociationService"		value-ref="content.CreateHistoricalContentObjectAssociationService" />
				<entry key="content.CreateOrUpdateTextStyleFontService"						value-ref="content.CreateOrUpdateTextStyleFontService" />
				<entry key="content.DeleteTextStyleFontService"								value-ref="content.DeleteTextStyleFontService" />
				<entry key="content.CreateOrUpdateContentTargetingService" 					value-ref="content.CreateOrUpdateContentTargetingService" />
				<entry key="content.UpdateSystemVariablesService"							value-ref="content.UpdateSystemVariablesService" />
				<entry key="content.CreateOrUpdateClipboardContentService" 			   		value-ref="content.CreateOrUpdateClipboardContentService" />
				<entry key="content.CreateNewContentObjectService" 							value-ref="content.CreateNewContentObjectService" />
				<entry key="content.UpdateContentObjectContentService" 						value-ref="content.UpdateContentObjectContentService" />
				<entry key="content.UpdateContentObjectZoneDeliveryService" 				value-ref="content.UpdateContentObjectZoneDeliveryService" />
				<entry key="content.UpdateContentObjectZonePriorityService" 				value-ref="content.UpdateContentObjectZonePriorityService" />
				<entry key="content.ContentObjectCloneService"                          	value-ref="content.ContentObjectCloneService" />
				<entry key="content.UpdateContentObjectOverviewService" 					value-ref="content.UpdateContentObjectOverviewService" />
				<entry key="content.UpdateContentObjectTouchpointAssignmentService" 		value-ref="content.UpdateContentObjectTouchpointAssignmentService" />
				<entry key="content.CreateOrUpdateTextStyleTransformationProfileService"	value-ref="content.CreateOrUpdateTextStyleTransformationProfileService" />
				<entry key="content.ContentTransformService"								value-ref="content.ContentTransformService" />

				<!-- Content Intelligence Service Mappings
				     ========================== -->
				<entry key="contentintelligence.CreateOrUpdateContentAssistantService"	value-ref="contentintelligence.CreateOrUpdateContentAssistantService" />
				<entry key="contentintelligence.ContentAssistantBatchService"			value-ref="contentintelligence.ContentAssistantBatchService" />

				<!-- Approval Services 
	     			========================== -->
	     		<entry key="approval.UpdateApprovalService" 	 						value-ref="approval.UpdateApprovalService" />

				<!-- Targeting Services 
	     			========================== -->
	     		<entry key="targeting.UpdateTargetingService" 							value-ref="targeting.UpdateTargetingService" />
	     		<entry key="targeting.UpdateTargetGroupTouchpointAssignmentService" 	value-ref="targeting.UpdateTargetGroupTouchpointAssignmentService" />
                <entry key="targeting.TargetingSyncService"                             value-ref="targeting.TargetingSyncService" />

				<!-- Report Services 
	     			========================== -->
	     		<entry key="report.UpdateReportScenarioService" 						value-ref="report.UpdateReportScenarioService" />
	     		<entry key="report.ImportDeliveryReportService" 						value-ref="report.ImportDeliveryReportService" />
	     		<entry key="report.ImportDeliveryStatsService"             			 	value-ref="report.ImportDeliveryStatsService"  />
	     		<entry key="report.DeleteReportByJobPartService"            			value-ref="report.DeleteReportByJobPartService"  />
	     		<entry key="report.ScenarioProcessor"            						value-ref="report.ScenarioProcessor"  />
				<entry key="report.ImportSimulationStatsService"						value-ref="report.ImportSimulationStatsService" />
				<entry key="report.ImportInsertDeliveryStatsService"					value-ref="report.ImportInsertDeliveryStatsService" />
				<entry key="report.DeleteReportScenarioService"							value-ref="report.DeleteReportScenarioService" />
				<entry key="report.BulkDeleteReportScenarioService"						value-ref="report.BulkDeleteReportScenarioService" />
				<entry key="report.BulkUpdateReportScenarioService"						value-ref="report.BulkUpdateReportScenarioService" />

				<!-- Testing Services 
	     			========================== -->
	     		<entry key="testing.UpdateTestingScenarioService" 						value-ref="testing.UpdateTestingScenarioService" />
				<entry key="testing.DeleteTestingScenarioService"						value-ref="testing.DeleteTestingScenarioService" />
				<entry key="testing.BulkDeleteTestingScenarioService"					value-ref="testing.BulkDeleteTestingScenarioService" />
				<entry key="testing.BulkUpdateTestingScenarioService"				value-ref="testing.BulkUpdateTestingScenarioService" />
				<entry key="testing.BulkUpdateTestScenarioSyncService"				value-ref="testing.BulkUpdateTestScenarioSyncService" />
				<entry key="testing.UpdateTestSuiteService" 									value-ref="testing.UpdateTestSuiteService" />
				<entry key="testing.DeleteTestSuiteService"									value-ref="testing.DeleteTestSuiteService" />
				<entry key="testing.BulkDeleteTestSuiteService"								value-ref="testing.BulkDeleteTestSuiteService" />
				<entry key="testing.BulkUpdateTestSuiteService"							value-ref="testing.BulkUpdateTestSuiteService" />
				<entry key="testing.SendTestResultService" value-ref="testing.SendTestResultService" />
				<entry key="testing.SendTestSuiteService" value-ref="testing.SendTestSuiteService" />
				
				<!-- Simulation Services 
	     			========================== -->
	     		<entry key="simulation.CreateSimulationService" 						value-ref="simulation.CreateSimulationService" />
	     		<entry key="simulation.DeleteSimulationService" 						value-ref="simulation.DeleteSimulationService" />
	     		<entry key="simulation.CancelSimulationService" 						value-ref="simulation.CancelSimulationService" />
	     		<entry key="simulation.ExportSimulationToXMLService" 					value-ref="simulation.ExportSimulationToXMLService" />
				<entry key="simulation.BulkDeleteSimulationService"						value-ref="simulation.BulkDeleteSimulationService" />
				<entry key="simulation.BulkCancelSimulationService"						value-ref="simulation.BulkCancelSimulationService" />
				<entry key="simulation.BulkTestResultsDownloadService"					value-ref="simulation.BulkTestResultsDownloadService" />
				
				<!-- Common Services 
	     			========================== -->
	     		<entry key="common.DeleteModelService" 										value-ref="common.DeleteModelService" />
	     		<entry key="common.BulkDeleteModelsService" 							value-ref="common.BulkDeleteModelsService" />
	     		<entry key="common.ProcessInvalidSigninService" 						value-ref="common.ProcessInvalidSigninService" />
				<entry key="common.CreateOrUpdatePasswordRecoveryService"				value-ref="common.CreateOrUpdatePasswordRecoveryService" />
				<entry key="common.UpdateEditManagerService"				 			value-ref="common.UpdateEditManagerService" />

				<!-- Admin Service Mappings 
				     ========================== -->
				<entry key="admin.RetrieveSecuritySettingsService" 	 					value-ref="admin.RetrieveSecuritySettingsService" />
				<entry key="admin.roleDeletionService" 	 		    					value-ref="admin.roleDeletionService" />
				<entry key="admin.rolePersistenceService" 	 		    				value-ref="admin.rolePersistenceService" />
				<entry key="admin.UpdateLocaleSettingsService" 	 						value-ref="admin.UpdateLocaleSettingsService" />
				<entry key="admin.UpdateSecuritySettingsService" 	 					value-ref="admin.UpdateSecuritySettingsService" />
				<entry key="admin.UpdateSystemPropertiesService" 	 					value-ref="admin.UpdateSystemPropertiesService" />
				<entry key="admin.UpdateTenantPermissionsService" 	 					value-ref="admin.UpdateTenantPermissionsService" />
				<entry key="admin.UpdateTenantService" 	 								value-ref="admin.UpdateTenantService" />
				<entry key="admin.TenantActivateDeactivateService" 	 					value-ref="admin.TenantActivateDeactivateService" />
				<entry key="admin.UpdateThemeService" 	 								value-ref="admin.UpdateThemeService" />
				<entry key="admin.UpdateUserSettingsService" 	 						value-ref="admin.UpdateUserSettingsService" />
				<entry key="admin.UpdateWorkflowApprovalsService" 	 					value-ref="admin.UpdateWorkflowApprovalsService" />
				<entry key="admin.UpdateWorkflowService" 	 							value-ref="admin.UpdateWorkflowService" />
				<entry key="admin.userCreateOrUpdateService" 	 						value-ref="admin.userCreateOrUpdateService" />
				<entry key="admin.BulkUserCreateOrUpdateService" 	 					value-ref="admin.BulkUserCreateOrUpdateService" />
				<entry key="admin.UpdateUserService" 	 								value-ref="admin.UpdateUserService" />
				<entry key="admin.ServerNamespacePropertyUpdateService"					value-ref="admin.ServerNamespacePropertyUpdateService" />
				<entry key="admin.UpdateMaintenanceService" 	 						value-ref="admin.UpdateMaintenanceService" />
				<entry key="admin.CreateOrUpdateBranchesService"						value-ref="admin.CreateOrUpdateBranchesService" />
				<entry key="admin.CreateOrUpdateNodesService"							value-ref="admin.CreateOrUpdateNodesService" />
				<entry key="admin.UpdateUserLocaleService"								value-ref="admin.UpdateUserLocaleService" />
				<entry key="admin.MigrateLocalUsersAndDisableUserMenuItemsService"		value-ref="admin.MigrateLocalUsersAndDisableUserMenuItemsService" />
				<entry key="admin.DeleteLicenceService"									value-ref="admin.DeleteLicenceService" />
				<entry key="admin.BulkDeleteLicencesService"							value-ref="admin.BulkDeleteLicencesService" />
				<entry key="admin.proxylogin.CreateOrUpdateRedirectionInfoService"		value-ref="admin.proxylogin.CreateOrUpdateRedirectionInfoService" />
				<entry key="admin.proxylogin.CreateOrUpdatePodService"					value-ref="admin.proxylogin.CreateOrUpdatePodService" />
				<entry key="admin.proxylogin.PodScanService"							value-ref="admin.proxylogin.PodScanService" />
				<entry key="admin.UpdateFilerootManagementService"						value-ref="admin.UpdateFilerootManagementService" />
				<entry key="admin.StatusPollingBackgroundTaskService"					value-ref="admin.StatusPollingBackgroundTaskService" />
				<entry key="admin.UpdateDataExpirationScheduleService"					value-ref="admin.UpdateDataExpirationScheduleService" />
				<entry key="admin.SigningKeyPairService"								value-ref="admin.SigningKeyPairService" />
				<entry key="admin.RestApiTokenService"									value-ref="admin.RestApiTokenService" />
				<entry key="admin.RestApiTokenDeleteService"									value-ref="admin.RestApiTokenDeleteService" />
				
				<!-- Data Admin Service Mappings 
				     ========================== -->
				<entry key="dataadmin.BridgeDataElementVariablesService"				value-ref="dataadmin.BridgeDataElementVariablesService" />
				<entry key="dataadmin.CheckAndDisableMenuItemsService"					value-ref="dataadmin.CheckAndDisableMenuItemsService" />
				<entry key="dataadmin.CleanStalledJobsService"							value-ref="dataadmin.CleanStalledJobsService" />
				<entry key="dataadmin.UpdateConditionElementService"					value-ref="dataadmin.UpdateConditionElementService" />  
				<entry key="dataadmin.UpdateTargetingSearchStringService"				value-ref="dataadmin.UpdateTargetingSearchStringService" />
				<entry key="dataadmin.UpdateTargetingTPAssignmentService"				value-ref="dataadmin.UpdateTargetingTPAssignmentService" />
				<entry key="dataadmin.UpdateDataElementService"							value-ref="dataadmin.UpdateDataElementService" />  
				<entry key="dataadmin.UpdateDataFileService"							value-ref="dataadmin.UpdateDataFileService" />
				<entry key="dataadmin.UpdateDataGroupService"							value-ref="dataadmin.UpdateDataGroupService" />  
				<entry key="dataadmin.DeleteDataGroupService"							value-ref="dataadmin.DeleteDataGroupService" />
				<entry key="dataadmin.UpdateDataRecordService"							value-ref="dataadmin.UpdateDataRecordService" /> 
				<entry key="dataadmin.UpdateDataSourceService"							value-ref="dataadmin.UpdateDataSourceService" /> 
				<entry key="dataadmin.UpdateTargetGroupService"							value-ref="dataadmin.UpdateTargetGroupService" />
				<entry key="dataadmin.GetVariablesService"								value-ref="dataadmin.GetVariablesService" />       
				<entry key="dataadmin.UpdateVariableService"							value-ref="dataadmin.UpdateVariableService" />
				<entry key="dataadmin.DeleteVariableService"							value-ref="dataadmin.DeleteVariableService" />
				<entry key="dataadmin.BulkDeleteVariablesService"						value-ref="dataadmin.BulkDeleteVariablesService" />
				<entry key="dataadmin.DeleteTargetingRuleService"						value-ref="dataadmin.DeleteTargetingRuleService" />
				<entry key="dataadmin.BulkDeleteTargetingRulesService"					value-ref="dataadmin.BulkDeleteTargetingRulesService" />
				<entry key="dataadmin.DeleteTargetGroupService"							value-ref="dataadmin.DeleteTargetGroupService" />
				<entry key="dataadmin.BulkDeleteTargetGroupsService"					value-ref="dataadmin.BulkDeleteTargetGroupsService" />								
				<entry key="dataadmin.ImportLookupValuesService"						value-ref="dataadmin.ImportLookupValuesService" />
				<entry key="dataadmin.UpdateDataSourceAssociationService"				value-ref="dataadmin.UpdateDataSourceAssociationService" />
				<entry key="dataadmin.UpdateDataResourceService"						value-ref="dataadmin.UpdateDataResourceService" />
				<entry key="dataadmin.UpdateFilterConditionService"						value-ref="dataadmin.UpdateFilterConditionService" />
				<entry key="dataadmin.UpdateXmlDataTagDefinitionService"				value-ref="dataadmin.UpdateXmlDataTagDefinitionService" /> 
				<entry key="dataadmin.UpdateXmlDataElementService"						value-ref="dataadmin.UpdateXmlDataElementService" />
				<entry key="dataadmin.UpdateJSONDataDefinitionService"					value-ref="dataadmin.UpdateJSONDataDefinitionService" />
				<entry key="dataadmin.UpdateJSONDataElementService"						value-ref="dataadmin.UpdateJSONDataElementService" />
				<entry key="dataadmin.DeleteDataFileService"							value-ref="dataadmin.DeleteDataFileService" />
				<entry key="dataadmin.BulkDeleteDataFilesService"						value-ref="dataadmin.BulkDeleteDataFilesService" />
				<entry key="dataadmin.DeleteDataResourceService"						value-ref="dataadmin.DeleteDataResourceService" />
				<entry key="dataadmin.BulkDeleteDataResourcesService"					value-ref="dataadmin.BulkDeleteDataResourcesService" />
				<entry key="dataadmin.MigrateDataResourceRemoteFilePathService"			value-ref="dataadmin.MigrateDataResourceRemoteFilePathService" />
				<entry key="dataadmin.CloneDataFileService"								value-ref="dataadmin.CloneDataFileService" />
				<entry key="dataadmin.CreateNewLookupTableService" 						value-ref="dataadmin.CreateNewLookupTableService" />
				<entry key="dataadmin.UpdateLookupTableService" 						value-ref="dataadmin.UpdateLookupTableService" />
				<entry key="dataadmin.LookupTableCloneService" 							value-ref="dataadmin.LookupTableCloneService" />
				<entry key="dataadmin.LookupTableCloneForCheckoutService" 				value-ref="dataadmin.LookupTableCloneForCheckoutService" />				
				<entry key="dataadmin.LookupTableUnlockModelService" 					value-ref="dataadmin.LookupTableUnlockModelService" />
				<entry key="dataadmin.LookupTableBulkAbortWIPService" 					value-ref="dataadmin.LookupTableBulkAbortWIPService" />
				<entry key="dataadmin.LookupTableBulkCheckoutService" 					value-ref="dataadmin.LookupTableBulkCheckoutService" />
				<entry key="dataadmin.LookupTableBulkArchiveService" 					value-ref="dataadmin.LookupTableBulkArchiveService" />
				<entry key="dataadmin.LookupTableBulkDeleteArchiveService" 				value-ref="dataadmin.LookupTableBulkDeleteArchiveService" />
				<entry key="dataadmin.ImportDataSourceLayoutFromXMLService" 			value-ref="dataadmin.ImportDataSourceLayoutFromXMLService" />
				<entry key="dataadmin.ImportDataSourceLayoutFromJSONService" 			value-ref="dataadmin.ImportDataSourceLayoutFromJSONService" />
                <entry key="dataadmin.DataElementVariableSyncService" 			        value-ref="dataadmin.DataElementVariableSyncService" />
                <entry key="dataadmin.DataSourceAssociationSyncService" 			    value-ref="dataadmin.DataSourceAssociationSyncService" />
                <entry key="dataadmin.DataSourceSyncService" 			                value-ref="dataadmin.DataSourceSyncService" />
				<entry key="dataadmin.MetatagsUpdateService" 			                value-ref="dataadmin.MetatagsUpdateService" />

				<!-- External Event -->
				<entry key="externalevent.GetExternalEventService" 						value-ref="externalevent.GetExternalEventService" />
				<entry key="externalevent.CreateExternalEventService"					value-ref="externalevent.CreateExternalEventService" />
				<entry key="externalevent.DeleteExternalEventService"					value-ref="externalevent.DeleteExternalEventService" />
				<entry key="externalevent.BulkDeleteExternalEventsService"				value-ref="externalevent.BulkDeleteExternalEventsService" />
				
				<!-- Rationalizer -->
				<entry key="rationalizer.BulkDeleteRationalizerDocumentsService"		value-ref="rationalizer.BulkDeleteRationalizerDocumentsService" />
				<entry key="rationalizer.BulkDeleteRationalizerContentService"		    value-ref="rationalizer.BulkDeleteRationalizerContentService" />
				<entry key="rationalizer.CreateOrUpdateRationalizerApplicationService"	value-ref="rationalizer.CreateOrUpdateRationalizerApplicationService" />
				<entry key="rationalizer.BulkDeleteRationalizerQueryService"			value-ref="rationalizer.BulkDeleteRationalizerQueryService" />
				<entry key="rationalizer.CreateOrUpdateRationalizerQueryService"		value-ref="rationalizer.CreateOrUpdateRationalizerQueryService" />
				<entry key="rationalizer.UpdateRationalizerApplicationVisibiltyService"	value-ref="rationalizer.UpdateRationalizerApplicationVisibiltyService" />
				<entry key="rationalizer.UpdateRationalizerAppNavigationService"		value-ref="rationalizer.UpdateRationalizerAppNavigationService" />
				<entry key="rationalizer.CreateOrUpdateRationalizerContentService"		value-ref="rationalizer.CreateOrUpdateRationalizerContentService" />
				<entry key="rationalizer.UpdateRationalizerWorkflowAssignmentService" 	value-ref="rationalizer.UpdateRationalizerWorkflowAssignmentService" />
				<entry key="rationalizer.RationalizerContentUnlockModelService" 		value-ref="rationalizer.RationalizerContentUnlockModelService" />
				<entry key="rationalizer.DeactivateRationalizerContentService" 		    value-ref="rationalizer.DeactivateRationalizerContentService" />
				<entry key="rationalizer.MigrateRationalizerPostProcessScriptsService"  value-ref="rationalizer.MigrateRationalizerPostProcessScriptsService"/>

				<!-- Touchpoint Admin Service Mappings 
				     ========================== -->
				<entry key="tpadmin.UpdateDeliveryEventService"							value-ref="tpadmin.UpdateDeliveryEventService" />  
				<entry key="tpadmin.UpdateApplicationService"							value-ref="tpadmin.UpdateApplicationService" />
				<entry key="tpadmin.UpdateZoneService"									value-ref="tpadmin.UpdateZoneService" />
				<entry key="tpadmin.UpdateTemplateModifiersService"						value-ref="tpadmin.UpdateTemplateModifiersService" />
				<entry key="tpadmin.CreateOrUpdateTemplateVariantsService"				value-ref="tpadmin.CreateOrUpdateTemplateVariantsService" />
				<entry key="tpadmin.BulkUpdateZoneVisibilityService"					value-ref="tpadmin.BulkUpdateZoneVisibilityService" />
				<entry key="tpadmin.BulkUpdateZoneStyleService"							value-ref="tpadmin.BulkUpdateZoneStyleService" />
				<entry key="tpadmin.UpdateZoneDataGroupService"							value-ref="tpadmin.UpdateZoneDataGroupService" />
				<entry key="tpadmin.UpdateSectionService"								value-ref="tpadmin.UpdateSectionService" />
				<entry key="tpadmin.UpdateDocumentService"								value-ref="tpadmin.UpdateDocumentService" />
				<entry key="tpadmin.DeleteDocumentSectionService"						value-ref="tpadmin.DeleteDocumentSectionService" />
				<entry key="tpadmin.CreateDocumentService"								value-ref="tpadmin.CreateDocumentService" />
				<entry key="tpadmin.CreateDocumentAdvancedService"						value-ref="tpadmin.CreateDocumentAdvancedService" />
				<entry key="tpadmin.EMailTemplateService"								value-ref="tpadmin.EMailTemplateService" />
				<entry key="tpadmin.CleanupEMailTemplateService"						value-ref="tpadmin.CleanupEMailTemplateService" />
				<entry key="tpadmin.MoveEMailTemplateService"							value-ref="tpadmin.MoveEMailTemplateService" />
				<entry key="tpadmin.UpdateDocumentExtendedReportingVariablesService"	value-ref="tpadmin.UpdateDocumentExtendedReportingVariablesService" />
				<entry key="tpadmin.UpdateTouchpointDataSourceAssociationService"		value-ref="tpadmin.UpdateTouchpointDataSourceAssociationService" />
				<entry key="tpadmin.TouchpointLanguageEditService"						value-ref="tpadmin.TouchpointLanguageEditService" />
				<entry key="tpadmin.CreateOrUpdateCompositionFileSetService"			value-ref="tpadmin.CreateOrUpdateCompositionFileSetService" />
				<entry key="tpadmin.CleanupCompositionPacksService"						value-ref="tpadmin.CleanupCompositionPacksService" />
				<entry key="tpadmin.CreateOrUpdateTextStyleCustomizationService"		value-ref="tpadmin.CreateOrUpdateTextStyleCustomizationService" />
				<entry key="tpadmin.RemoveStyleCustomizationService"					value-ref="tpadmin.RemoveStyleCustomizationService" />
				<entry key="tpadmin.UpdateTouchpointLayoutService"						value-ref="tpadmin.UpdateTouchpointLayoutService" />
				<entry key="tpadmin.UpdateTouchpointCollectionService"					value-ref="tpadmin.UpdateTouchpointCollectionService" />
				<entry key="tpadmin.BulkDeleteTouchpointCollectionsService"				value-ref="tpadmin.BulkDeleteTouchpointCollectionsService" />
				<entry key="tpadmin.DeleteTouchpointCollectionService"					value-ref="tpadmin.DeleteTouchpointCollectionService" />
				<entry key="tpadmin.CreateSegmentationAnalysisService" 					value-ref="tpadmin.CreateSegmentationAnalysisService" />
				<entry key="tpadmin.ParseSegmentationAnalysisReportService" 			value-ref="tpadmin.ParseSegmentationAnalysisReportService" />
				<entry key="tpadmin.CreateOrUpdateAlternateLayoutService" 				value-ref="tpadmin.CreateOrUpdateAlternateLayoutService" />
				<entry key="tpadmin.TouchpointVersioningService" 						value-ref="tpadmin.TouchpointVersioningService" />
				<entry key="tpadmin.TPAccessControlService" 	 						value-ref="tpadmin.TPAccessControlService" />
				<entry key="tpadmin.BulkDeleteZoneService"								value-ref="tpadmin.BulkDeleteZoneService" />
				<entry key="tpadmin.DeleteZoneService"									value-ref="tpadmin.DeleteZoneService" />
				<entry key="tpadmin.UpdateStripoTemplateService"						value-ref="tpadmin.UpdateStripoTemplateService" />
				<entry key="tpadmin.BulkCleanupDuplicatedZonesService"					value-ref="tpadmin.BulkCleanupDuplicatedZonesService" />
				<entry key="tpadmin.UpdateFormattingSelectionsService"					value-ref="tpadmin.UpdateFormattingSelectionsService" />
				
				<!-- Job Service Mappings 
				     ========================== -->
				<entry key="job.JobPackerService"										value-ref="job.JobPackerService" />
				<entry key="job.InvokeExternalTriggeredEventService"					value-ref="job.InvokeExternalTriggeredEventService" />
				<entry key="job.GetSelectableContentService"							value-ref="job.GetSelectableContentService" />
				<entry key="job.UpdateDeliveryEventStatusService"						value-ref="job.UpdateDeliveryEventStatusService" />
				<entry key="job.UpdateJobMetaDataService"								value-ref="job.UpdateJobMetaDataService" />
				<entry key="job.GetTPSelectableMessageContentService"					value-ref="job.GetTPSelectableMessageContentService" />
				<entry key="job.GetEmbeddedContentXMLService"							value-ref="job.GetEmbeddedContentXMLService" />
				<entry key="job.GetGraphicLibraryXMLService"							value-ref="job.GetGraphicLibraryXMLService" />
				<entry key="job.AddDeliveryEventStateTransitionService"					value-ref="job.AddDeliveryEventStateTransitionService" />

				<!-- Touchpoint Selection 
				     ========================== -->
				<entry key="tpselection.ApproveTouchpointVariantService"				value-ref="tpselection.ApproveTouchpointVariantService" />
				<entry key="tpselection.CreateOrUpdateTouchpointVariantService"		value-ref="tpselection.CreateOrUpdateTouchpointVariantService" />
				<entry key="tpselection.GetTouchpointVariantListService"		        value-ref="tpselection.GetTouchpointVariantListService" />
				<entry key="tpselection.UpdateTouchpointProofingDataService" 			value-ref="tpselection.UpdateTouchpointProofingDataService" />
				<entry key="tpselection.UpdateTouchpointChannelService" 				value-ref="tpselection.UpdateTouchpointChannelService" />
				<entry key="tpselection.UpdateTouchpointVariantVisibiltyService" 		value-ref="tpselection.UpdateTouchpointVariantVisibiltyService" />
				<entry key="tpselection.CreateTouchpointVariantProofService" 			value-ref="tpselection.CreateTouchpointVariantProofService" />
				<entry key="tpselection.UpdateTouchpointVariantTemplateService"		value-ref="tpselection.UpdateTouchpointVariantTemplateService" />
				<entry key="tpselection.UpdateAlternateLayoutAssignmentService" 		value-ref="tpselection.UpdateAlternateLayoutAssignmentService" />
				<entry key="tpselection.UpdateVariantWorkflowAssignmentService" 		value-ref="tpselection.UpdateVariantWorkflowAssignmentService" />
				<entry key="tpselection.UpdateMessageWorkflowAssignmentService" 		value-ref="tpselection.UpdateMessageWorkflowAssignmentService" />
				
				<!-- Language Selection 
				     ========================== -->
				<entry key="languageselection.CreateOrUpdateLanguageSelectionService"	value-ref="languageselection.CreateOrUpdateLanguageSelectionService" />
				<entry key="languageselection.GetLanguageSelectionListService"		    value-ref="languageselection.GetLanguageSelectionListService" />										

				<!-- Version Service Mappings 
				     ========================== -->
				<entry key="version.ModelVersionMappingDBService" 						value-ref="version.ModelVersionMappingDBService" />
				<entry key="version.VersionedModelDBService" 							value-ref="version.VersionedModelDBService" />
				<entry key="version.CheckContentObjectInProductionService" 				value-ref="version.CheckContentObjectInProductionService" />
				<entry key="version.CheckVersionInProductionService" 					value-ref="version.CheckVersionInProductionService" />
				<entry key="version.CheckoutFromProductionService" 						value-ref="version.CheckoutFromProductionService" />	
				<entry key="content.UnlockContentObjectService" 						value-ref="content.UnlockContentObjectService" />
				<entry key="content.ReassignContentObjectForTranslationService" 		value-ref="content.ReassignContentObjectForTranslationService" />

				<!-- workgroup Service Mappings
				     ========================== -->
				<entry key="workgroup.CreateWorkgroupService" 							value-ref="workgroup.CreateWorkgroupService" />
				<entry key="workgroup.UpdateWorkgroupService" 							value-ref="workgroup.UpdateWorkgroupService" />
				<entry key="workgroup.DeleteWorkgroupService" 							value-ref="workgroup.DeleteWorkgroupService" />
				<entry key="workgroup.UpdateWorkgroupZoneAssociationService" 			value-ref="workgroup.UpdateWorkgroupZoneAssociationService" />
				
				<!-- file Service Mappings 
					 ========================== -->
				<entry key="file.CreateOrUpdateSandboxFileService" 						value-ref="file.CreateOrUpdateSandboxFileService" />	
				<entry key="file.CreateOrUpdateDatabaseFileService" 					value-ref="file.CreateOrUpdateDatabaseFileService" />
				<entry key="file.UpdateSourceService" 									value-ref="file.UpdateSourceService" />		

				<!-- Parameter Service Mappings 
				     ========================== -->
				<entry key="parameter.CreateOrUpdateParameterService" 		   			value-ref="parameter.CreateOrUpdateParameterService" />
				<entry key="parameter.DeleteParameterService" 		   					value-ref="parameter.DeleteParameterService" />
				<entry key="parameter.CreateParameterGroupService" 		   				value-ref="parameter.CreateParameterGroupService" />
				<entry key="parameter.UpdateParameterGroupService" 		   				value-ref="parameter.UpdateParameterGroupService" />
				<entry key="parameter.CloneParameterGroupInstanceCollectionService" 	value-ref="parameter.CloneParameterGroupInstanceCollectionService" />
				<entry key="parameter.CreateParameterGroupInstanceCollectionService" 	value-ref="parameter.CreateParameterGroupInstanceCollectionService" />
				<entry key="parameter.UpdateParameterGroupInstanceCollectionService" 	value-ref="parameter.UpdateParameterGroupInstanceCollectionService" />
				<entry key="parameter.CreateParamGroupTreeNodeService" 					value-ref="parameter.CreateParamGroupTreeNodeService" />
				<entry key="parameter.RenameParameterGroupTreeNodeService" 				value-ref="parameter.RenameParameterGroupTreeNodeService" />
                <entry key="parameter.ParameterGroupSyncService"                        value-ref="parameter.ParameterGroupSyncService" />

				<!-- Insert Service Mappings 
				     ========================== -->
				<entry key="insert.CreateOrUpdateInsertService" 			   			value-ref="insert.CreateOrUpdateInsertService" />
				<entry key="insert.DeactivateService" 			   						value-ref="insert.DeactivateService" />
				<entry key="insert.DeleteService"	 			   						value-ref="insert.DeleteService" />
				<entry key="insert.ReassignService" 			   						value-ref="insert.ReassignService" />
				<entry key="insert.ReinstateService" 			   						value-ref="insert.ReinstateService" />
				<entry key="insert.ReleaseForApprovalService" 			   				value-ref="insert.ReleaseForApprovalService" />
				<entry key="insert.GetInsertListService" 				   				value-ref="insert.GetInsertListService" />		
				<entry key="insert.ApproveOrRejectService" 				   				value-ref="insert.ApproveOrRejectService" />
				<entry key="insert.ArchiveService" 				   						value-ref="insert.ArchiveService" />				
				<entry key="insert.DiscardService" 				   						value-ref="insert.DiscardService" />				

				<!-- InsertSchedule Service Mappings 
				     ========================== -->
				<entry key="insert.GetInsertScheduleListService" 				   		value-ref="insert.GetInsertScheduleListService" />		
				<entry key="insert.CreateOrUpdateInsertScheduleService" 			   	value-ref="insert.CreateOrUpdateInsertScheduleService" />
				<entry key="insert.ExportInsertSchedulesToCSVService" 				   	value-ref="insert.ExportInsertSchedulesToCSVService" />
				
				<!-- RateSchedule Service Mappings 
				     ========================== -->
				<entry key="insert.CreateOrUpdateRateScheduleService" 			   		value-ref="insert.CreateOrUpdateRateScheduleService" />

				<!-- Tag Service Mappings 
				     ========================== -->
				<entry key="tag.CreateOrUpdateTagService" 			   					value-ref="tag.CreateOrUpdateTagService" />
				<entry key="tag.ReassignService" 			   							value-ref="tag.ReassignService" />
				<entry key="tag.BulkDeleteTagsService" 			   						value-ref="tag.BulkDeleteTagsService" />
				
				<!-- Where To Used 
				     ========================== -->
				<entry key="wtu.DirectReferencesFetchService" 			   				value-ref="wtu.DirectReferencesFetchService" />
				<entry key="wtu.DirectAllReferencesFetchService" 			   			value-ref="wtu.DirectAllReferencesFetchService" />
				<entry key="wtu.SingleDirectReferencesFetchService" 			   		value-ref="wtu.SingleDirectReferencesFetchService" />
				
				<!-- Where Used Reports -->
				<entry key="whereused.CreateWhereUsedReportService" 			   		value-ref="whereused.CreateWhereUsedReportService" />
				<entry key="export.ExportWhereUsedToXMLService" 			   			value-ref="export.ExportWhereUsedToXMLService" />
				
				<!-- Diagnostics Reports -->
				<entry key="admin.CreateDiagnosticsReportService" 			   			value-ref="admin.CreateDiagnosticsReportService" />
				<entry key="export.ExportDiagnosticsToXMLService" 			   			value-ref="export.ExportDiagnosticsToXMLService" />				
				
				<!-- Unified Login Reports -->
				<entry key="admin.CreateUnifiedLoginReportService" 			   			value-ref="admin.CreateUnifiedLoginReportService" />
				<entry key="export.ExportUnifiedLoginReportToXMLService" 			   	value-ref="export.ExportUnifiedLoginReportToXMLService" />				
								
				<!--  Email services -->
				<entry key="email.UpdateFailedEmailDeliveriesService"                	value-ref="email.UpdateFailedEmailDeliveriesService"/>
				<entry key="email.CheckPOP3ReturnService"								value-ref="email.CheckPOP3ReturnService"/>
				<entry key="email.CheckExactTargetDeliveriesService"					value-ref="email.CheckExactTargetDeliveriesService"/>
				
				<!-- Variant Workflow services -->
				<entry key="workflow.CreateOrUpdateWorkflowService"                		value-ref="workflow.CreateOrUpdateWorkflowService"/>
				<entry key="workflow.ShareTouchpointWorkflowService"                	value-ref="workflow.ShareTouchpointWorkflowService"/>
				<entry key="workflow.WorkflowApproveService"                			value-ref="workflow.WorkflowApproveService"/>
				<entry key="workflow.WorkflowRejectService"                				value-ref="workflow.WorkflowRejectService"/>
				<entry key="workflow.WorkflowReleaseForApprovalService"   				value-ref="workflow.WorkflowReleaseForApprovalService" />
				<entry key="workflow.DeleteWorkflowService" 							value-ref="workflow.DeleteWorkflowService" />
				<entry key="workflow.BulkDeleteWorkflowService" 						value-ref="workflow.BulkDeleteWorkflowService" />				
				
				<!-- Query Service -->
				<entry key="query.HibernatePaginationService" 							value-ref="query.HibernatePaginationService"/>
                <entry key="query.SqlQueryPaginationService"                            value-ref="query.SqlQueryPaginationService" />
				
				<!-- Tag Cloud -->
				<entry key="tagcloud.UpdateTagCloudService" 							value-ref="tagcloud.UpdateTagCloudService"/>
<!--				<entry key="tagcloud.CreateOrUpdateTagCloudService" 			   		value-ref="tagcloud.CreateOrUpdateTagCloudService" />-->
				<entry key="tagcloud.BulkDeleteTagCloudService" 							value-ref="tagcloud.BulkDeleteTagCloudService" />

				<!-- Communications -->
				<entry key="communication.CreateCommunicationProofService" 						value-ref="communication.CreateCommunicationProofService" />
				<entry key="communication.CreateOrUpdateCommunicationService" 					value-ref="communication.CreateOrUpdateCommunicationService" />
				<entry key="communication.CreateCommunicationProductionBatchService" 			value-ref="communication.CreateCommunicationProductionBatchService" />
				<entry key="communication.CreateCommunicationProductionOnApprovalService"		value-ref="communication.CreateCommunicationProductionOnApprovalService" />
				<entry key="communication.CommunicationUnlockModelService" 						value-ref="communication.CommunicationUnlockModelService" />
				<entry key="communication.UpdateConnectedWorkflowAssignmentService" 		value-ref="communication.UpdateConnectedWorkflowAssignmentService" />

				<!-- ConnectedII experimental -->
				<entry key="connected.InteractiveDataService"                     			value-ref="connected.InteractiveDataService" />

				<!-- Metadata -->
				<entry key="metadata.CreateOrUpdateMetadataFormDefinitionService" 				value-ref="metadata.CreateOrUpdateMetadataFormDefinitionService" />
				<entry key="metadata.CreateOrUpdateMetadataFormService" 						value-ref="metadata.CreateOrUpdateMetadataFormService" />
				<entry key="metadata.BulkDeleteMetadataFormDefinitionService" 					value-ref="metadata.BulkDeleteMetadataFormDefinitionService" />
				<entry key="metadata.ClearMetadataService"										value-ref="metadata.ClearMetadataService" />
				<entry key="metadata.CreateOrUpdateMetadataPointsOfInterestService"				value-ref="metadata.CreateOrUpdateMetadataPointsOfInterestService" />

				<!-- Licence -->
				<entry key="licence.AddOrUpdateMessagepointLicencesService" 			value-ref="licence.AddOrUpdateMessagepointLicencesService" />
				<entry key="license.CreateOrUpdateMessagepointLicenseService" 			value-ref="license.CreateOrUpdateMessagepointLicenseService" />
				
				<!-- Notification services -->
				<entry key="notification.CreateOrUpdateNotificationSettingsService" 	value-ref="notification.CreateOrUpdateNotificationSettingsService" />

				<!-- DE Server services -->
				<entry key="deserver.CreateOrUpdateDEServerService" value-ref="deserver.CreateOrUpdateDEServerService" />
				
				<!-- Dictionary -->
				<entry key="dictionary.MigrateDictionariesService" 				value-ref="dictionary.MigrateDictionariesService" />
				<entry key="dictionary.CreateOrUpdateDictionaryService" 	value-ref="dictionary.CreateOrUpdateDictionaryService" />
				<entry key="dictionary.DeleteDictionaryService" 						value-ref="dictionary.DeleteDictionaryService" />
				<entry key="dictionary.BulkUpdateDictionaryService" 			value-ref="dictionary.BulkUpdateDictionaryService" />
				<entry key="dictionary.BulkDeleteDictionaryService" 				value-ref="dictionary.BulkDeleteDictionaryService" />

				<!-- Bundle Delivery -->
				<entry key="bundledelivery.MigrateBundleDeliveryPostProcessScriptsService" value-ref="bundledelivery.MigrateBundleDeliveryPostProcessScriptsService"/>

				<!-- Configurable Links Service Mapping -->
				<entry key="link.ConfigurableLinksService" value-ref="link.ConfigurableLinksService"/>

				<entry key="sync.syncDataSourcesService" value-ref="sync.syncDataSourcesService"/>
                <entry key="sync.syncDataCollectionService" value-ref="sync.syncDataCollectionService"/>
				<entry key="sync.syncLookupTablesService" value-ref="sync.syncLookupTablesService"/>
				<entry key="sync.syncDataElementVariablesService" value-ref="sync.syncDataElementVariablesService"/>
                <entry key="sync.syncParameterGroupsService" value-ref="sync.syncParameterGroupsService"/>
                <entry key="sync.syncTargetRulesService" value-ref="sync.syncTargetRulesService"/>
                <entry key="sync.syncTargetGroupsService" value-ref="sync.syncTargetGroupsService"/>
                <entry key="sync.syncDocumentAndLayoutsService" value-ref="sync.syncDocumentAndLayoutsService"/>
                <entry key="sync.syncCompositionPackageService" value-ref="sync.syncCompositionPackageService"/>
                <entry key="sync.syncContentObjectsService" value-ref="sync.syncContentObjectsService"/>
				<entry key="sync.syncTextStyleService" value-ref="sync.syncTextStyleService"/>
                <entry key="sync.syncDataFilesService" value-ref="sync.syncDataFilesService"/>
				<entry key="sync.syncTextStyleFontService" value-ref="sync.syncTextStyleFontService"/>
				<entry key="sync.syncDataResourcesService" value-ref="sync.syncDataResourcesService"/>
                <entry key="sync.syncTouchpointVariantsService" value-ref="sync.syncTouchpointVariantsService"/>
				<entry key="sync.syncChannelConfigurationService" value-ref="sync.syncChannelConfigurationService"/>
                <entry key="sync.syncListStylesService" value-ref="sync.syncListStylesService"/>
                <entry key="sync.syncAssociateReferencedObjectsService" value-ref="sync.syncAssociateReferencedObjectsService"/>
                <entry key="sync.syncParagraphStylesService" value-ref="sync.syncParagraphStylesService"/>
                <entry key="sync.syncMetadataTemplateService" value-ref="sync.syncMetadataTemplateService"/>
			</map>
		</property>
		
		<property name="multiThreadingServices"> <!-- Some services have instance variables. Such service should be prototype rather than singleton in multiple threading environment.  -->
			<map> <!-- Service Name -->													<!-- bean name -->
				<entry key="export.CreateOrUpdateAuditReportService" 					value="export.CreateOrUpdateAuditReportService" />
				<entry key="export.GenerateMessageAuditReportService" 					value="export.GenerateMessageAuditReportService" />
				<entry key="export.ExportMessageToXMLService" 							value="export.ExportMessageToXMLService" />
				<entry key="export.GenerateInsertScheduleAuditReportService" 			value="export.GenerateInsertScheduleAuditReportService" />
				<entry key="export.ExportInsertScheduleToXMLService" 					value="export.ExportInsertScheduleToXMLService" />
				<entry key="export.ExportDocumentToXMLService"							value="export.ExportDocumentToXMLService" />
				<entry key="export.ExportDynamicContentObjectService"					value="export.ExportDynamicContentObjectService" />
				<entry key="export.ExportTPVariantsToExcelService"						value="export.ExportTPVariantsToExcelService" />
				<entry key="export.ExportTPMessagesToExcelService"						value="export.ExportTPMessagesToExcelService" />
				<entry key="export.ExportVariantMetadataToExcelService"					value="export.ExportVariantMetadataToExcelService" />
				<entry key="export.ExportRationalizerDocumentToExcelService"			value="export.ExportRationalizerDocumentToExcelService" />
				<entry key="export.ExportRationalizerDocumentToXMLService"				value="export.ExportRationalizerDocumentToXMLService" />
				<entry key="export.ExportRationalizerMetadataTagsToExcelService"		value="export.ExportRationalizerMetadataTagsToExcelService" />
				<entry key="export.ExportRationalizerQueryResultToExcelService"			value="export.ExportRationalizerQueryResultToExcelService" />
				<entry key="export.ExportUsersToExcelService"							value="export.ExportUsersToExcelService" />
				<entry key="export.GenerateMessagepointObjectExportService"				value="export.GenerateMessagepointObjectExportService" />
				<entry key="export.GenerateRationalizerObjectExportService"				value="export.GenerateRationalizerObjectExportService" />
				<entry key="export.ExportTouchpointToXMLService" 						value="export.ExportTouchpointToXMLService" />
				<entry key="export.GenerateTouchpointAuditReportService" 				value="export.GenerateTouchpointAuditReportService" />		
				<entry key="export.ExportOperationsReportToXMLService" 					value="export.ExportOperationsReportToXMLService" />
				<entry key="export.GenerateOperationsReportService" 					value="export.GenerateOperationsReportService" />				
				<entry key="export.ExportTpDeliveryReportToXMLService" 					value="export.ExportTpDeliveryReportToXMLService" />
				<entry key="export.GenerateTpDeliveryReportService" 					value="export.GenerateTpDeliveryReportService" />	
				<entry key="export.ExportProjectToXMLService" 							value="export.ExportProjectToXMLService" />
				<entry key="export.GenerateProjectAuditReportService" 					value="export.GenerateProjectAuditReportService" />
				<entry key="export.ExportImageLibraryToXMLService" 						value="export.ExportImageLibraryToXMLService" />
				<entry key="export.GenerateImageLibraryAuditReportService" 				value="export.GenerateImageLibraryAuditReportService" />
				<entry key="export.ExportSmartTextToXMLService" 						value="export.ExportSmartTextToXMLService" />
				<entry key="export.GenerateSmartTextAuditReportService" 				value="export.GenerateSmartTextAuditReportService" />
				<entry key="export.GenerateCompareReportService"						value="export.GenerateCompareReportService" />
				<entry key="export.ExportRolesToExcelService"							value="export.ExportRolesToExcelService" />
				<entry key="export.ExportJobPerformanceReportToXMLService"				value="export.ExportJobPerformanceReportToXMLService" />
				<entry key="export.ExportConnectedToXMLService" 						value="export.ExportConnectedToXMLService" />
				<entry key="export.GenerateConnectedAuditReportService" 				value="export.GenerateConnectedAuditReportService" />
				<entry key="export.ExportRationalizerDocumentsToMessagepointService" 	value="export.ExportRationalizerDocumentsToMessagepointService" />
				<entry key="export.ExportContentToExcelService"					 		value="export.ExportContentToExcelService" />
				<entry key="export.ExportContentToJSONService"					 		value="export.ExportContentToJSONService" />

				<entry key="imports.ImportTouchpointService"							value="imports.ImportTouchpointService"/>
				<entry key="imports.ImportBulkUploadService"							value="imports.ImportBulkUploadService"/>
				<entry key="imports.BulkUploadVariantsService"							value="imports.BulkUploadVariantsService"/>
				<entry key="imports.ImportMessagesService"								value="imports.ImportMessagesService"/>
				<entry key="imports.ImportEmbeddedContentsService"						value="imports.ImportEmbeddedContentsService"/>
				<entry key="imports.ImportContentLibraryService"						value="imports.ImportContentLibraryService"/>
				<entry key="imports.BulkUploadUsersUpdateService"						value="imports.BulkUploadUsersUpdateService"/>
				<entry key="imports.ImportJSONtoContentService"							value="imports.ImportJSONtoContentService"/>

                <entry key="contentObject.MessageVersioningService"                           value="contentObject.MessageVersioningService" />

                <entry key="parameter.ParameterGroupSyncService"                        value="parameter.ParameterGroupSyncService" />
                <entry key="tpadmin.TouchpointVersioningService"                        value="tpadmin.TouchpointVersioningService" />
                
                <entry key="content.CreateHistoricalContentObjectAssociationService"    value="content.CreateHistoricalContentObjectAssociationService" />

                <entry key="contentObject.GlobalContentObjectVersioningService"         value="contentObject.GlobalContentObjectVersioningService" />

                <entry key="targeting.TargetingSyncService"                             value="targeting.TargetingSyncService" />

                <entry key="dataadmin.DataElementVariableSyncService" 			        value="dataadmin.DataElementVariableSyncService" />
                <entry key="dataadmin.DataSourceAssociationSyncService" 			    value="dataadmin.DataSourceAssociationSyncService" />
                <entry key="dataadmin.DataSourceSyncService" 			                value="dataadmin.DataSourceSyncService" />

                <entry key="wtu.DirectReferencesFetchService"                           value="wtu.DirectReferencesFetchService" />
                <entry key="wtu.DirectAllReferencesFetchService"                        value="wtu.DirectAllReferencesFetchService" />
                <entry key="wtu.SingleDirectReferencesFetchService"                     value="wtu.SingleDirectReferencesFetchService" />
			</map>
		</property>
	</bean>
	
	<!-- Audit Services 
	     ========================== -->
	<bean id="audit.CreateAuditEventService"   							class="com.prinova.messagepoint.platform.services.audit.CreateAuditEventService" />    
	
	<!-- Attachments Services 
	     ========================== -->
	<bean id="attachment.CreateOrUpdateAttachmentService"   			class="com.prinova.messagepoint.platform.services.attachment.CreateOrUpdateAttachmentService" />
	<bean id="attachment.DeleteAttachmentService"   					class="com.prinova.messagepoint.platform.services.attachment.DeleteAttachmentService" />

	<!-- Brand Services
	     ========================== -->
	<bean id="brand.CreateOrUpdateBrandProfileService"   				class="com.prinova.messagepoint.platform.services.brand.CreateOrUpdateBrandProfileService" />
	<bean id="brand.BulkDeleteBrandProfileService"   					class="com.prinova.messagepoint.platform.services.brand.BulkDeleteBrandProfileService" />

	<!--  Email Services
	      ========================= -->
	<bean id="email.UpdateFailedEmailDeliveriesService"					class="com.prinova.messagepoint.platform.services.email.UpdateFailedEmailDeliveriesService" />
	<bean id="email.CheckPOP3ReturnService" 							class="com.prinova.messagepoint.platform.services.email.CheckPOP3ReturnService" />
	<bean id="email.CheckExactTargetDeliveriesService"					class="com.prinova.messagepoint.platform.services.email.CheckExactTargetDeliveriesService" />
	
	<!-- Tasks Services 
	     ========================== -->
	<bean id="tasks.CreateOrUpdateTaskService" 							class="com.prinova.messagepoint.platform.services.tasks.CreateOrUpdateTaskService" />
	<bean id="tasks.DeleteTaskService" 									class="com.prinova.messagepoint.platform.services.tasks.DeleteTaskService" />
	<bean id="tasks.BulkUpdateTaskService" 								class="com.prinova.messagepoint.platform.services.tasks.BulkUpdateTaskService" />
	<bean id="tasks.BulkDeleteTaskService" 								class="com.prinova.messagepoint.platform.services.tasks.BulkDeleteTaskService" />	
	
	<!-- Projects Services 
	     ========================== -->
	<bean id="projects.CreateOrUpdateProjectService" 					class="com.prinova.messagepoint.platform.services.projects.CreateOrUpdateProjectService" />
	<bean id="projects.DeleteProjectService" 							class="com.prinova.messagepoint.platform.services.projects.DeleteProjectService" />
	<bean id="projects.BulkDeleteProjectService" 						class="com.prinova.messagepoint.platform.services.projects.BulkDeleteProjectService" />	
	
	<!-- Message Services 
	     ================ -->
	<bean id="content.UpdateContentObjectPreviewService"  				class="com.prinova.messagepoint.platform.services.content.UpdateContentObjectPreviewService" />
	<bean id="content.ContentObjectDynamicVariantPreviewService"  		class="com.prinova.messagepoint.platform.services.content.ContentObjectDynamicVariantPreviewService" />
	<bean id="content.DeleteContentObjectPreviewService" 				class="com.prinova.messagepoint.platform.services.content.DeleteContentObjectPreviewService" />
	<bean id="content.BulkDeleteWorkingContentObjectDataService" 		class="com.prinova.messagepoint.platform.services.content.BulkDeleteWorkingContentObjectDataService" />
	<bean id="content.BulkArchiveContentObjectService" 		        	class="com.prinova.messagepoint.platform.services.content.BulkArchiveContentObjectService" />
	<bean id="content.BulkCheckoutContentObjectService" 		        class="com.prinova.messagepoint.platform.services.content.BulkCheckoutContentObjectService" />
	<bean id="content.BulkDeleteArchivedContentObjectDataService" 		class="com.prinova.messagepoint.platform.services.content.BulkDeleteArchivedContentObjectDataService" />
	<bean id="message.GetMessageContentService"							class="com.prinova.messagepoint.platform.services.message.GetMessageContentService" />
	<bean id="content.BulkUpdateContentObjectService" 		        	class="com.prinova.messagepoint.platform.services.content.BulkUpdateContentObjectService" />
	<bean id="content.UpdateContentObjectMoveToZoneService"  			class="com.prinova.messagepoint.platform.services.content.UpdateContentObjectMoveToZoneService" />
    <bean id="message.UpdateMessageHashService"                         class="com.prinova.messagepoint.platform.services.message.UpdateMessageHashService" />
	<bean id="content.ContentPowerEditService"                          class="com.prinova.messagepoint.platform.services.content.ContentPowerEditService" />
	<bean id="content.ContentObjectAssociationPostSaveService"          class="com.prinova.messagepoint.platform.services.content.ContentObjectAssociationPostSaveService" />
	<bean id="content.ContentUpdateService"                          	class="com.prinova.messagepoint.platform.services.content.ContentUpdateService" />
	<bean id="content.ContentObjectPushToTranslationService"     		class="com.prinova.messagepoint.platform.services.content.ContentObjectPushToTranslationService" />

	<bean id="contentObject.MessageVersioningService" 						class="com.prinova.messagepoint.platform.services.message.MessageVersioningService"  scope="prototype" >
        <property name="clearSessionThreshold" value="200000" />
    </bean>

	<!-- Embedded Content Services 
	     ================ -->

	<!-- Content Library Services 
	     ================ -->
	<bean id="content.ContentObjectBulkImageCreateService" 					class="com.prinova.messagepoint.platform.services.content.ContentObjectBulkImageCreateService" />
	<bean id="content.ContentObjectBulkSftpImageSyncService" 			class="com.prinova.messagepoint.platform.services.content.ContentObjectBulkSftpImageSyncService" />

    <!-- Global Content Object Services
         ================ -->
    <bean id="contentObject.GlobalContentObjectVersioningService"               class="com.prinova.messagepoint.platform.services.content.GlobalContentObjectVersioningService" scope="prototype" />

	<!-- Export Services
	     ========================== -->
	<bean id="export.CreateOrUpdateAuditReportService"	    			class="com.prinova.messagepoint.platform.services.export.CreateOrUpdateAuditReportService" scope="prototype" />
	<bean id="export.GenerateMessageAuditReportService"	    			class="com.prinova.messagepoint.platform.services.export.GenerateMessageAuditReportService" scope="prototype" />
	<bean id="export.ExportMessageToXMLService"	    					class="com.prinova.messagepoint.platform.services.export.ExportMessageToXMLService" scope="prototype" />
	<bean id="export.GenerateInsertScheduleAuditReportService"	    	class="com.prinova.messagepoint.platform.services.export.GenerateInsertScheduleAuditReportService" scope="prototype" />
	<bean id="export.ExportInsertScheduleToXMLService"	    			class="com.prinova.messagepoint.platform.services.export.ExportInsertScheduleToXMLService" scope="prototype" />
	<bean id="export.ExportDocumentToXMLService"						class="com.prinova.messagepoint.platform.services.export.ExportDocumentToXMLService" scope="prototype" />
	<bean id="export.ExportDynamicContentObjectService"					class="com.prinova.messagepoint.platform.services.export.ExportDynamicContentObjectService" scope="prototype" />
	<bean id="export.ExportTPVariantsToExcelService"					class="com.prinova.messagepoint.platform.services.export.ExportTPVariantsToExcelService" scope="prototype" />
	<bean id="export.ExportTPMessagesToExcelService"					class="com.prinova.messagepoint.platform.services.export.ExportTPMessagesToExcelService" scope="prototype" />
	<bean id="export.ExportTPVariantsToXMLService"						class="com.prinova.messagepoint.platform.services.export.ExportTPVariantsToXMLService" scope="prototype" />
	<bean id="export.ExportTPMessagesToXMLService"						class="com.prinova.messagepoint.platform.services.export.ExportTPMessagesToXMLService" scope="prototype" />
	<bean id="export.ExportVariantMetadataToExcelService"				class="com.prinova.messagepoint.platform.services.export.ExportVariantMetadataToExcelService" scope="prototype" />
	<bean id="export.ExportRationalizerDocumentToExcelService"			class="com.prinova.messagepoint.platform.services.export.ExportRationalizerDocumentToExcelService" scope="prototype" />
	<bean id="export.ExportRationalizerDocumentToXMLService"			class="com.prinova.messagepoint.platform.services.export.ExportRationalizerDocumentToXMLService" scope="prototype" />
	<bean id="export.ExportRationalizerMetadataTagsToExcelService"		class="com.prinova.messagepoint.platform.services.export.ExportRationalizerMetadataTagsToExcelService" scope="prototype" />
	<bean id="export.ExportRationalizerQueryResultToExcelService"       class="com.prinova.messagepoint.platform.services.export.rationalizer.similarity.ExportRationalizerQueryResultToExcelService" scope="prototype" />
	<bean id="export.ExportUsersToExcelService"							class="com.prinova.messagepoint.platform.services.export.ExportUsersToExcelService" scope="prototype" />
	<bean id="export.ExportUsersToXMLService"							class="com.prinova.messagepoint.platform.services.export.ExportUsersToXMLService" scope="prototype" />
	<bean id="export.GenerateMessagepointObjectExportService"			class="com.prinova.messagepoint.platform.services.export.GenerateMessagepointObjectExportService" scope="prototype" />
	<bean id="export.GenerateRationalizerObjectExportService"			class="com.prinova.messagepoint.platform.services.export.GenerateRationalizerObjectExportService" scope="prototype" />
	<bean id="export.GenerateTouchpointAuditReportService"	    		class="com.prinova.messagepoint.platform.services.export.GenerateTouchpointAuditReportService" scope="prototype" />
	<bean id="export.ExportTouchpointToXMLService"	    				class="com.prinova.messagepoint.platform.services.export.ExportTouchpointToXMLService" scope="prototype" />
	<bean id="export.GenerateOperationsReportService"	    			class="com.prinova.messagepoint.platform.services.export.GenerateOperationsReportService" scope="prototype" />
	<bean id="export.ExportOperationsReportToXMLService"				class="com.prinova.messagepoint.platform.services.export.ExportOperationsReportToXMLService" scope="prototype" />	
	<bean id="export.GenerateTpDeliveryReportService"	    			class="com.prinova.messagepoint.platform.services.export.GenerateTpDeliveryReportService" scope="prototype" />
	<bean id="export.ExportTpDeliveryReportToXMLService"				class="com.prinova.messagepoint.platform.services.export.ExportTpDeliveryReportToXMLService" scope="prototype" />		
	<bean id="export.GenerateProjectAuditReportService"	    			class="com.prinova.messagepoint.platform.services.export.GenerateProjectAuditReportService" scope="prototype" />
	<bean id="export.ExportProjectToXMLService"	    					class="com.prinova.messagepoint.platform.services.export.ExportProjectToXMLService" scope="prototype" />
	<bean id="export.GenerateImageLibraryAuditReportService"	    	class="com.prinova.messagepoint.platform.services.export.GenerateImageLibraryAuditReportService" scope="prototype" />
	<bean id="export.ExportImageLibraryToXMLService"	    			class="com.prinova.messagepoint.platform.services.export.ExportImageLibraryToXMLService" scope="prototype" />
	<bean id="export.GenerateSmartTextAuditReportService"	    		class="com.prinova.messagepoint.platform.services.export.GenerateSmartTextAuditReportService" scope="prototype" />
	<bean id="export.ExportSmartTextToXMLService"	    				class="com.prinova.messagepoint.platform.services.export.ExportSmartTextToXMLService" scope="prototype" />
	<bean id="export.GenerateCompareReportService"						class="com.prinova.messagepoint.platform.services.export.GenerateCompareReportService" scope="prototype" />
	<bean id="export.ExportRolesToExcelService"							class="com.prinova.messagepoint.platform.services.export.ExportRolesToExcelService" scope="prototype" />
	<bean id="export.ExportMessagesOnApprovalToExcelService"			class="com.prinova.messagepoint.platform.services.export.ExportMessagesOnApprovalToExcelService" scope="prototype" />
	<bean id="export.ExportJobPerformanceReportToXMLService"			class="com.prinova.messagepoint.platform.services.export.ExportJobPerformanceReportToXMLService" scope="prototype" />
	<bean id="export.GenerateConnectedAuditReportService"	    		class="com.prinova.messagepoint.platform.services.export.GenerateConnectedAuditReportService" scope="prototype" />
	<bean id="export.ExportConnectedToXMLService"	    				class="com.prinova.messagepoint.platform.services.export.ExportConnectedToXMLService" scope="prototype" />
	<bean id="export.ExportRationalizerDocumentsToMessagepointService"	class="com.prinova.messagepoint.platform.services.export.ExportRationalizerDocumentsToMessagepointService" scope="prototype" />
	<bean id="export.ExportContentToExcelService"						class="com.prinova.messagepoint.platform.services.export.ExportContentToExcelService" scope="prototype" />
	<bean id="export.ExportContentToJSONService"						class="com.prinova.messagepoint.platform.services.export.ExportContentToJSONService" scope="prototype" />

	<bean id="cacheDataRepository" class="com.prinova.messagepoint.connected.data.oracle.OracleCacheDataRepository"/>

	<bean id="cacheDataService" class="com.prinova.messagepoint.connected.data.CacheDataService">
		<constructor-arg ref="cacheDataRepository"/>
	</bean>
	<!--  Import Services -->
	<bean id="imports.ImportTouchpointService" class="com.prinova.messagepoint.platform.services.imports.ImportDocumentService" scope="prototype" />
	<bean id="imports.ImportBulkUploadService"							class="com.prinova.messagepoint.platform.services.imports.ImportBulkUploadService" scope="prototype" />
	<bean id="imports.BulkUploadVariantsService"						class="com.prinova.messagepoint.platform.services.imports.BulkUploadVariantsService" scope="prototype" />
	<bean id="imports.ImportMessagesService"							class="com.prinova.messagepoint.platform.services.imports.ImportMessagesService" scope="prototype" />
	<bean id="imports.ImportEmbeddedContentsService"					class="com.prinova.messagepoint.platform.services.imports.ImportEmbeddedContentsService" scope="prototype" />
	<bean id="imports.ImportContentLibraryService"                      class="com.prinova.messagepoint.platform.services.imports.ImportContentLibraryService" scope="prototype" />
	<bean id="imports.BulkUploadUsersUpdateService"						class="com.prinova.messagepoint.platform.services.imports.BulkUploadUsersUpdateService" scope="prototype" />
	<bean id="imports.ImportJSONtoContentService"						class="com.prinova.messagepoint.platform.services.imports.ImportJSONtoContentService" scope="prototype" />

	<!-- Selectable message/constant Services 
	     ========================== -->
	<bean id="content.ContentObjectContentSelectionUpdateService" 			class="com.prinova.messagepoint.platform.services.content.ContentObjectContentSelectionUpdateService" />
	<bean id="content.CreateContentObjectDynamicVariantTreeNodeService" 	class="com.prinova.messagepoint.platform.services.content.CreateContentObjectDynamicVariantTreeNodeService" />
	<bean id="content.MessageContentSelectionCheckInProductionService" 		class="com.prinova.messagepoint.platform.services.content.MessageContentSelectionCheckInProductionService" />
	<bean id="content.ContentObjectDynamicVariantCloneService" 				class="com.prinova.messagepoint.platform.services.content.ContentObjectDynamicVariantCloneService" />
	<bean id="content.ContentObjectVariantCloneCOWEditService" 				class="com.prinova.messagepoint.platform.services.content.ContentObjectVariantCloneCOWEditService" />
	<bean id="content.DeleteContentObjectDynamicVariantTreeNodeService" 	class="com.prinova.messagepoint.platform.services.content.DeleteContentObjectDynamicVariantTreeNodeService" />
	<bean id="content.TouchpointContentSelectionUpdateService" 				class="com.prinova.messagepoint.platform.services.content.TouchpointContentSelectionUpdateService" />
	<bean id="content.BulkUpdateTextStylesService" 							class="com.prinova.messagepoint.platform.services.content.BulkUpdateTextStylesService" />
	<bean id="content.BulkDeleteStylesService" 								class="com.prinova.messagepoint.platform.services.content.BulkDeleteStylesService" />
	<bean id="content.CreateOrUpdateTextStyleService" 						class="com.prinova.messagepoint.platform.services.content.CreateOrUpdateTextStyleService" />
	<bean id="content.TextStyleService" 									class="com.prinova.messagepoint.platform.services.content.TextStyleService" />
	<bean id="content.CreateOrUpdateParagraphStyleService" 					class="com.prinova.messagepoint.platform.services.content.CreateOrUpdateParagraphStyleService" />
	<bean id="content.CloneParagraphStyleService" 							class="com.prinova.messagepoint.platform.services.content.CloneParagraphStyleService" />
	<bean id="content.CreateOrUpdateListStyleService" 						class="com.prinova.messagepoint.platform.services.content.CreateOrUpdateListStyleService" />
	<bean id="content.CloneListStyleService" 								class="com.prinova.messagepoint.platform.services.content.CloneListStyleService" />
	<bean id="content.CreateHistoricalContentObjectAssociationService" 		class="com.prinova.messagepoint.platform.services.content.CreateHistoricalContentObjectAssociationService"  scope="prototype" />
	<bean id="content.CreateOrUpdateTextStyleFontService" 					class="com.prinova.messagepoint.platform.services.content.CreateOrUpdateTextStyleFontService" />
	<bean id="content.DeleteTextStyleFontService" 							class="com.prinova.messagepoint.platform.services.content.DeleteTextStyleFontService" />
	<bean id="content.CreateOrUpdateContentTargetingService" 				class="com.prinova.messagepoint.platform.services.content.CreateOrUpdateContentTargetingService" />
	<bean id="content.UpdateSystemVariablesService" 						class="com.prinova.messagepoint.platform.services.content.UpdateSystemVariablesService" />
	<bean id="content.CreateOrUpdateClipboardContentService" 				class="com.prinova.messagepoint.platform.services.content.CreateOrUpdateClipboardContentService" />
	<bean id="content.CreateNewContentObjectService" 						class="com.prinova.messagepoint.platform.services.content.CreateNewContentObjectService" />
	<bean id="content.UpdateContentObjectContentService" 					class="com.prinova.messagepoint.platform.services.content.UpdateContentObjectContentService" />
	<bean id="content.UpdateContentObjectZoneDeliveryService"				class="com.prinova.messagepoint.platform.services.content.UpdateContentObjectZoneDeliveryService" />
	<bean id="content.UpdateContentObjectZonePriorityService"  				class="com.prinova.messagepoint.platform.services.content.UpdateContentObjectZonePriorityService" />
	<bean id="content.ContentObjectCloneService" 							class="com.prinova.messagepoint.platform.services.content.ContentObjectCloneService" />
	<bean id="content.UpdateContentObjectOverviewService" 					class="com.prinova.messagepoint.platform.services.content.UpdateContentObjectOverviewService" />
	<bean id="content.UpdateContentObjectTouchpointAssignmentService"		class="com.prinova.messagepoint.platform.services.content.UpdateContentObjectTouchpointAssignmentService" />
	<bean id="content.CreateOrUpdateTextStyleTransformationProfileService"	class="com.prinova.messagepoint.platform.services.content.CreateOrUpdateTextStyleTransformationProfileService" />
	<bean id="content.ContentTransformService"								class="com.prinova.messagepoint.platform.services.content.ContentTransformService" />

	<!-- Content Intelligence Services
	     ========================== -->
	<bean id="contentintelligence.CreateOrUpdateContentAssistantService"	class="com.prinova.messagepoint.platform.services.contentintelligence.CreateOrUpdateContentAssistantService" />
	<bean id="contentintelligence.ContentAssistantBatchService"				class="com.prinova.messagepoint.platform.services.contentintelligence.ContentAssistantBatchService" />

	<!-- Approval Services 
	     ========================== -->
	<bean id="approval.UpdateApprovalService" 							class="com.prinova.messagepoint.platform.services.approval.UpdateApprovalService" />

	<!-- Targeting Services 
	     ========================== -->
	<bean id="targeting.UpdateTargetingService" 						class="com.prinova.messagepoint.platform.services.targeting.UpdateTargetingService" />
	<bean id="targeting.UpdateTargetGroupTouchpointAssignmentService" 	class="com.prinova.messagepoint.platform.services.targeting.UpdateTargetGroupTouchpointAssignmentService" />
    <bean id="targeting.TargetingSyncService"                           class="com.prinova.messagepoint.platform.services.targeting.TargetingSyncService" scope="prototype" />

	<!-- Report Services 
	     ========================== -->
	<bean id="report.UpdateReportScenarioService" 						class="com.prinova.messagepoint.platform.services.report.UpdateReportScenarioService" />
	<bean id="report.ImportDeliveryReportService" 						class="com.prinova.messagepoint.platform.services.report.ImportDeliveryReportService" />	
	<bean id="report.ImportDeliveryStatsService" 						class="com.prinova.messagepoint.platform.services.report.ImportDeliveryStatsService" />	
	<bean id="report.DeleteReportByJobPartService" 						class="com.prinova.messagepoint.platform.services.report.DeleteReportByJobPartService" />	
	<bean id="report.ScenarioProcessor"									class="com.prinova.messagepoint.reports.processor.ScenarioProcessor" />
	<bean id="report.ImportSimulationStatsService"						class="com.prinova.messagepoint.platform.services.report.ImportSimulationStatsService" />
	<bean id="report.ImportInsertDeliveryStatsService"					class="com.prinova.messagepoint.platform.services.report.ImportInsertDeliveryStatsService" />
	<bean id="report.DeleteReportScenarioService"						class="com.prinova.messagepoint.platform.services.report.DeleteReportScenarioService" />
	<bean id="report.BulkDeleteReportScenarioService"					class="com.prinova.messagepoint.platform.services.report.BulkDeleteReportScenarioService" />
	<bean id="report.BulkUpdateReportScenarioService"					class="com.prinova.messagepoint.platform.services.report.BulkUpdateReportScenarioService" />
	
	<!-- Testing Services 
	     ========================== -->
	<bean id="testing.UpdateTestingScenarioService" 					class="com.prinova.messagepoint.platform.services.testing.UpdateTestingScenarioService" />
	<bean id="testing.DeleteTestingScenarioService"					class="com.prinova.messagepoint.platform.services.testing.DeleteTestingScenarioService" />
	<bean id="testing.BulkDeleteTestingScenarioService"			class="com.prinova.messagepoint.platform.services.testing.BulkDeleteTestingScenarioService" />
	<bean id="testing.BulkUpdateTestingScenarioService"			class="com.prinova.messagepoint.platform.services.testing.BulkUpdateTestingScenarioService" />
	<bean id="testing.BulkUpdateTestScenarioSyncService"			class="com.prinova.messagepoint.platform.services.testing.BulkUpdateTestScenarioSyncService" />
	<bean id="testing.UpdateTestSuiteService" 								class="com.prinova.messagepoint.platform.services.testing.UpdateTestSuiteService" />
	<bean id="testing.DeleteTestSuiteService"								class="com.prinova.messagepoint.platform.services.testing.DeleteTestSuiteService" />
	<bean id="testing.BulkDeleteTestSuiteService"						class="com.prinova.messagepoint.platform.services.testing.BulkDeleteTestSuiteService" />
	<bean id="testing.BulkUpdateTestSuiteService"						class="com.prinova.messagepoint.platform.services.testing.BulkUpdateTestSuiteService" />
	<bean id="testing.SendTestResultService" class="com.prinova.messagepoint.platform.services.testing.SendTestResultService" />
	<bean id="testing.SendTestSuiteService" class="com.prinova.messagepoint.platform.services.testing.SendTestSuiteService" />
	
	<!-- Simulation Services 
	     ========================== -->
	<bean id="simulation.CreateSimulationService" 						class="com.prinova.messagepoint.platform.services.simulation.CreateSimulationService" />
	<bean id="simulation.DeleteSimulationService" 						class="com.prinova.messagepoint.platform.services.simulation.DeleteSimulationService" />
	<bean id="simulation.CancelSimulationService" 						class="com.prinova.messagepoint.platform.services.simulation.CancelSimulationService" />
	<bean id="simulation.ExportSimulationToXMLService" 					class="com.prinova.messagepoint.platform.services.simulation.ExportSimulationToXMLService" />
	<bean id="simulation.BulkDeleteSimulationService"					class="com.prinova.messagepoint.platform.services.simulation.BulkDeleteSimulationService" />
	<bean id="simulation.BulkCancelSimulationService"					class="com.prinova.messagepoint.platform.services.simulation.BulkCancelSimulationService" />
	<bean id="simulation.BulkTestResultsDownloadService"					class="com.prinova.messagepoint.platform.services.simulation.BulkTestResultsDownloadService" />
	
	<!-- Common Services 
	     ========================== -->
	<bean id="common.DeleteModelService" 										class="com.prinova.messagepoint.platform.services.common.DeleteModelService" />
	<bean id="common.BulkDeleteModelsService" 							class="com.prinova.messagepoint.platform.services.common.BulkDeleteModelsService" />
	<bean id="common.ProcessInvalidSigninService" 						class="com.prinova.messagepoint.platform.services.common.ProcessInvalidSigninService" />
	<bean id="common.CreateOrUpdatePasswordRecoveryService"				class="com.prinova.messagepoint.platform.services.common.CreateOrUpdatePasswordRecoveryService" />
	<bean id="common.UpdateEditManagerService" 							class="com.prinova.messagepoint.platform.services.common.UpdateEditManagerService" />
	
	<!-- Admin Services 
	     ========================== -->
	<bean id="admin.RetrieveSecuritySettingsService" 					class="com.prinova.messagepoint.platform.services.admin.RetrieveSecuritySettingsService" />
	<bean id="admin.roleDeletionService"   		    					class="com.prinova.messagepoint.platform.services.admin.RoleDeletionService" />
	<bean id="admin.rolePersistenceService"   		    				class="com.prinova.messagepoint.platform.services.admin.RolePersistenceService" />
	<bean id="admin.UpdateLocaleSettingsService"   						class="com.prinova.messagepoint.platform.services.admin.UpdateLocaleSettingsService" />
	<bean id="admin.UpdateSecuritySettingsService"   					class="com.prinova.messagepoint.platform.services.admin.UpdateSecuritySettingsService" />
	<bean id="admin.UpdateSystemPropertiesService"   					class="com.prinova.messagepoint.platform.services.admin.UpdateSystemPropertiesService" />
	<bean id="admin.UpdateTenantPermissionsService"   					class="com.prinova.messagepoint.platform.services.admin.UpdateTenantPermissionsService" />
	<bean id="admin.UpdateTenantService"   								class="com.prinova.messagepoint.platform.services.admin.UpdateTenantService" />
	<bean id="admin.TenantActivateDeactivateService"   					class="com.prinova.messagepoint.platform.services.admin.TenantActivateDeactivateService" />
	<bean id="admin.UpdateThemeService"   								class="com.prinova.messagepoint.platform.services.admin.UpdateThemeService" />
	<bean id="admin.UpdateUserSettingsService"   						class="com.prinova.messagepoint.platform.services.admin.UpdateUserSettingsService" />
	<bean id="admin.UpdateWorkflowApprovalsService"   					class="com.prinova.messagepoint.platform.services.admin.UpdateWorkflowApprovalsService" />
	<bean id="admin.UpdateWorkflowService"   							class="com.prinova.messagepoint.platform.services.admin.UpdateWorkflowService" />
	<bean id="admin.userCreateOrUpdateService"   						class="com.prinova.messagepoint.platform.services.admin.UserCreateOrUpdateService" />
	<bean id="admin.BulkUserCreateOrUpdateService"   					class="com.prinova.messagepoint.platform.services.admin.BulkUserCreateOrUpdateService" scope="prototype"/>
	<bean id="admin.UpdateUserService"			   						class="com.prinova.messagepoint.platform.services.admin.UpdateUserService" />	
	<bean id="admin.ServerNamespacePropertyUpdateService" 				class="com.prinova.messagepoint.platform.services.admin.ServerNamespacePropertyUpdateService" />
	<bean id="admin.UpdateMaintenanceService" 							class="com.prinova.messagepoint.platform.services.admin.UpdateMaintenanceService" />
	<bean id="admin.CreateOrUpdateBranchesService"						class="com.prinova.messagepoint.platform.services.admin.CreateOrUpdateBranchesService" />
	<bean id="admin.CreateOrUpdateNodesService"							class="com.prinova.messagepoint.platform.services.admin.CreateOrUpdateNodesService" />
	<bean id="admin.UpdateUserLocaleService"							class="com.prinova.messagepoint.platform.services.admin.UpdateUserLocaleService" />
	<bean id="admin.MigrateLocalUsersAndDisableUserMenuItemsService"	class="com.prinova.messagepoint.platform.services.admin.MigrateLocalUsersAndDisableUserMenuItemsService" />
	<bean id="admin.DeleteLicenceService" 								class="com.prinova.messagepoint.platform.services.admin.DeleteLicenceService" />
	<bean id="admin.BulkDeleteLicencesService" 							class="com.prinova.messagepoint.platform.services.admin.BulkDeleteLicencesService" />
	<bean id="admin.proxylogin.CreateOrUpdateRedirectionInfoService"	class="com.prinova.messagepoint.platform.services.admin.proxylogin.CreateOrUpdateRedirectionInfoService" />
	<bean id="admin.proxylogin.CreateOrUpdatePodService"				class="com.prinova.messagepoint.platform.services.admin.proxylogin.CreateOrUpdatePodService" />
	<bean id="admin.proxylogin.PodScanService"							class="com.prinova.messagepoint.platform.services.admin.proxylogin.PodScanService" />
	<bean id="admin.UpdateFilerootManagementService"					class="com.prinova.messagepoint.platform.services.admin.UpdateFilerootManagementService" />
	<bean id="admin.StatusPollingBackgroundTaskService" 				class="com.prinova.messagepoint.platform.services.admin.StatusPollingBackgroundTaskService" />
	<bean id="admin.UpdateDataExpirationScheduleService"				class="com.prinova.messagepoint.platform.services.admin.UpdateDataExpirationScheduleService" />
	<bean id="admin.SigningKeyPairService"								class="com.prinova.messagepoint.platform.services.admin.SigningKeyPairService" />
	<bean id="admin.RestApiTokenService"								class="com.prinova.messagepoint.platform.services.admin.RestApiTokenService" />
	<bean id="admin.RestApiTokenDeleteService"								class="com.prinova.messagepoint.platform.services.admin.RestApiTokenDeleteService" />

	<!-- Data Admin Services 
	     ========================== -->
	<bean id="dataadmin.BridgeDataElementVariablesService"				class="com.prinova.messagepoint.platform.services.dataadmin.BridgeDataElementVariablesService" />
	<bean id="dataadmin.CheckAndDisableMenuItemsService" 				class="com.prinova.messagepoint.platform.services.dataadmin.CheckAndDisableMenuItemsService" />
	<bean id="dataadmin.CleanStalledJobsService" 						class="com.prinova.messagepoint.platform.services.dataadmin.CleanStalledJobsService" />
	<bean id="dataadmin.UpdateConditionElementService" 					class="com.prinova.messagepoint.platform.services.dataadmin.UpdateConditionElementService" />
	<bean id="dataadmin.UpdateTargetingSearchStringService" 			class="com.prinova.messagepoint.platform.services.dataadmin.UpdateTargetingSearchStringService" />
	<bean id="dataadmin.UpdateTargetingTPAssignmentService" 			class="com.prinova.messagepoint.platform.services.dataadmin.UpdateTargetingTPAssignmentService" />
	<bean id="dataadmin.UpdateDataElementService" 						class="com.prinova.messagepoint.platform.services.dataadmin.UpdateDataElementService" />
	<bean id="dataadmin.UpdateDataFileService" 							class="com.prinova.messagepoint.platform.services.dataadmin.UpdateDataFileService" />
	<bean id="dataadmin.UpdateDataGroupService" 						class="com.prinova.messagepoint.platform.services.dataadmin.UpdateDataGroupService" />
	<bean id="dataadmin.DeleteDataGroupService" 						class="com.prinova.messagepoint.platform.services.dataadmin.DeleteDataGroupService" />
	<bean id="dataadmin.UpdateDataRecordService" 						class="com.prinova.messagepoint.platform.services.dataadmin.UpdateDataRecordService" />
	<bean id="dataadmin.UpdateDataSourceService" 						class="com.prinova.messagepoint.platform.services.dataadmin.UpdateDataSourceService" />
	<bean id="dataadmin.UpdateTargetGroupService" 						class="com.prinova.messagepoint.platform.services.dataadmin.UpdateTargetGroupService" />
	<bean id="dataadmin.UpdateVariableService" 							class="com.prinova.messagepoint.platform.services.dataadmin.UpdateVariableService" />
	<bean id="dataadmin.GetVariablesService" 							class="com.prinova.messagepoint.platform.services.dataadmin.GetVariablesService" />
	<bean id="dataadmin.DeleteVariableService" 							class="com.prinova.messagepoint.platform.services.dataadmin.DeleteVariableService" />
	<bean id="dataadmin.BulkDeleteVariablesService" 					class="com.prinova.messagepoint.platform.services.dataadmin.BulkDeleteVariablesService" />
	<bean id="dataadmin.DeleteTargetingRuleService" 					class="com.prinova.messagepoint.platform.services.dataadmin.DeleteTargetingRuleService" />
	<bean id="dataadmin.BulkDeleteTargetingRulesService" 				class="com.prinova.messagepoint.platform.services.dataadmin.BulkDeleteTargetingRulesService" />
	<bean id="dataadmin.DeleteTargetGroupService" 						class="com.prinova.messagepoint.platform.services.dataadmin.DeleteTargetGroupService" />
	<bean id="dataadmin.BulkDeleteTargetGroupsService" 					class="com.prinova.messagepoint.platform.services.dataadmin.BulkDeleteTargetGroupsService" />		
	<bean id="dataadmin.ImportLookupValuesService" 						class="com.prinova.messagepoint.platform.services.dataadmin.ImportLookupValuesService" />
	<bean id="dataadmin.UpdateDataSourceAssociationService" 			class="com.prinova.messagepoint.platform.services.dataadmin.UpdateDataSourceAssociationService" />
	<bean id="dataadmin.UpdateDataResourceService" 						class="com.prinova.messagepoint.platform.services.dataadmin.UpdateDataResourceService" />
	<bean id="dataadmin.UpdateFilterConditionService" 					class="com.prinova.messagepoint.platform.services.dataadmin.UpdateFilterConditionService" />
	<bean id="dataadmin.UpdateXmlDataTagDefinitionService" 				class="com.prinova.messagepoint.platform.services.dataadmin.xmllayout.UpdateXmlDataTagDefinitionService" />
	<bean id="dataadmin.UpdateXmlDataElementService" 					class="com.prinova.messagepoint.platform.services.dataadmin.xmllayout.UpdateXmlDataElementService" />
	<bean id="dataadmin.UpdateJSONDataDefinitionService" 				class="com.prinova.messagepoint.platform.services.dataadmin.jsonlayout.UpdateJSONDataDefinitionService" />
	<bean id="dataadmin.UpdateJSONDataElementService" 					class="com.prinova.messagepoint.platform.services.dataadmin.jsonlayout.UpdateJSONDataElementService" />
	<bean id="dataadmin.DeleteDataFileService" 							class="com.prinova.messagepoint.platform.services.dataadmin.DeleteDataFileService" />
	<bean id="dataadmin.BulkDeleteDataFilesService" 					class="com.prinova.messagepoint.platform.services.dataadmin.BulkDeleteDataFilesService" />
	<bean id="dataadmin.DeleteDataResourceService" 						class="com.prinova.messagepoint.platform.services.dataadmin.DeleteDataResourceService" />
	<bean id="dataadmin.BulkDeleteDataResourcesService" 				class="com.prinova.messagepoint.platform.services.dataadmin.BulkDeleteDataResourcesService" />
	<bean id="dataadmin.MigrateDataResourceRemoteFilePathService" 		class="com.prinova.messagepoint.platform.services.dataadmin.MigrateDataResourceRemoteFilePathService" />
	<bean id="dataadmin.CloneDataFileService" 							class="com.prinova.messagepoint.platform.services.dataadmin.CloneDataFileService" />
	<bean id="dataadmin.CreateNewLookupTableService" 					class="com.prinova.messagepoint.platform.services.dataadmin.CreateNewLookupTableService" />
	<bean id="dataadmin.UpdateLookupTableService" 						class="com.prinova.messagepoint.platform.services.dataadmin.UpdateLookupTableService" />
	<bean id="dataadmin.LookupTableCloneService" 						class="com.prinova.messagepoint.platform.services.dataadmin.LookupTableCloneService" />
	<bean id="dataadmin.LookupTableCloneForCheckoutService" 			class="com.prinova.messagepoint.platform.services.dataadmin.LookupTableCloneForCheckoutService" />	
	<bean id="dataadmin.LookupTableUnlockModelService" 					class="com.prinova.messagepoint.platform.services.dataadmin.LookupTableUnlockModelService" />
	<bean id="dataadmin.LookupTableBulkAbortWIPService" 				class="com.prinova.messagepoint.platform.services.dataadmin.LookupTableBulkAbortWIPService" />
	<bean id="dataadmin.LookupTableBulkCheckoutService" 				class="com.prinova.messagepoint.platform.services.dataadmin.LookupTableBulkCheckoutService" />
	<bean id="dataadmin.LookupTableBulkArchiveService" 					class="com.prinova.messagepoint.platform.services.dataadmin.LookupTableBulkArchiveService" />
	<bean id="dataadmin.LookupTableBulkDeleteArchiveService" 			class="com.prinova.messagepoint.platform.services.dataadmin.LookupTableBulkDeleteArchiveService" />		
	<bean id="dataadmin.ImportDataSourceLayoutFromXMLService" 			class="com.prinova.messagepoint.platform.services.dataadmin.ImportDataSourceLayoutFromXMLService" />
	<bean id="dataadmin.ImportDataSourceLayoutFromJSONService" 			class="com.prinova.messagepoint.platform.services.dataadmin.jsonlayout.ImportDataSourceLayoutFromJSONService" />
    <bean id="dataadmin.DataSourceSyncService" 			                class="com.prinova.messagepoint.platform.services.dataadmin.DataSourceSyncService"    scope="prototype"  />
    <bean id="dataadmin.DataSourceAssociationSyncService" 			    class="com.prinova.messagepoint.platform.services.dataadmin.DataSourceAssociationSyncService"    scope="prototype"  />
    <bean id="dataadmin.DataElementVariableSyncService" 			    class="com.prinova.messagepoint.platform.services.dataadmin.DataElementVariableSyncService"    scope="prototype"  />
	<bean id="dataadmin.MetatagsUpdateService" 			                class="com.prinova.messagepoint.platform.services.dataadmin.MetatagsUpdateService"  />

    <!-- External Event -->
	<bean id="externalevent.GetExternalEventService" 					class="com.prinova.messagepoint.platform.services.externalevent.GetExternalEventService" />
	<bean id="externalevent.CreateExternalEventService" 				class="com.prinova.messagepoint.platform.services.externalevent.CreateExternalEventService" />
	<bean id="externalevent.DeleteExternalEventService" 				class="com.prinova.messagepoint.platform.services.externalevent.DeleteExternalEventService" />
	<bean id="externalevent.BulkDeleteExternalEventsService" 			class="com.prinova.messagepoint.platform.services.externalevent.BulkDeleteExternalEventsService" />
	
	<!-- Rationalizer -->
	<bean id="rationalizer.BulkDeleteRationalizerDocumentsService"			class="com.prinova.messagepoint.platform.services.rationalizer.BulkDeleteRationalizerDocumentsService" />
	<bean id="rationalizer.BulkDeleteRationalizerContentService"			class="com.prinova.messagepoint.platform.services.rationalizer.BulkDeleteRationalizerContentService" />
	<bean id="rationalizer.CreateOrUpdateRationalizerApplicationService"	class="com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerApplicationService" />
	<bean id="rationalizer.BulkDeleteRationalizerQueryService"				class="com.prinova.messagepoint.platform.services.rationalizer.BulkDeleteRationalizerQueryService" />
	<bean id="rationalizer.CreateOrUpdateRationalizerQueryService"			class="com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerQueryService" />
	<bean id="rationalizer.UpdateRationalizerApplicationVisibiltyService"	class="com.prinova.messagepoint.platform.services.rationalizer.UpdateRationalizerApplicationVisibiltyService" />
	<bean id="rationalizer.UpdateRationalizerAppNavigationService"			class="com.prinova.messagepoint.platform.services.rationalizer.UpdateRationalizerAppNavigationService" />
	<bean id="rationalizer.CreateOrUpdateRationalizerContentService"		class="com.prinova.messagepoint.platform.services.rationalizer.CreateOrUpdateRationalizerContentService" />
	<bean id="rationalizer.UpdateRationalizerWorkflowAssignmentService" 	class="com.prinova.messagepoint.platform.services.rationalizer.UpdateRationalizerWorkflowAssignmentService" />
	<bean id="rationalizer.RationalizerContentUnlockModelService"       	class="com.prinova.messagepoint.platform.services.rationalizer.RationalizerContentUnlockModelService"/>
	<bean id="rationalizer.DeactivateRationalizerContentService" 	        class="com.prinova.messagepoint.platform.services.rationalizer.DeactivateRationalizerContentService"/>
	<bean id="rationalizer.MigrateRationalizerPostProcessScriptsService" 	class="com.prinova.messagepoint.platform.services.rationalizer.MigrateRationalizerPostProcessScriptsService"/>

	<!-- Touchpoint Admin Services 
	     ========================== -->
	<bean id="tpadmin.UpdateDeliveryEventService" 						class="com.prinova.messagepoint.platform.services.tpadmin.UpdateDeliveryEventService" />
	<bean id="tpadmin.UpdateApplicationService" 						class="com.prinova.messagepoint.platform.services.tpadmin.UpdateApplicationService" />
	<bean id="tpadmin.UpdateZoneService" 								class="com.prinova.messagepoint.platform.services.tpadmin.UpdateZoneService"/>
	<bean id="tpadmin.UpdateTemplateModifiersService" 					class="com.prinova.messagepoint.platform.services.tpadmin.UpdateTemplateModifiersService" />
	<bean id="tpadmin.CreateOrUpdateTemplateVariantsService"			class="com.prinova.messagepoint.platform.services.tpadmin.CreateOrUpdateTemplateVariantsService" />
	<bean id="tpadmin.BulkUpdateZoneVisibilityService" 					class="com.prinova.messagepoint.platform.services.tpadmin.BulkUpdateZoneVisibilityService" />
	<bean id="tpadmin.BulkUpdateZoneStyleService" 						class="com.prinova.messagepoint.platform.services.tpadmin.BulkUpdateZoneStyleService"/>
	<bean id="tpadmin.UpdateZoneDataGroupService" 						class="com.prinova.messagepoint.platform.services.tpadmin.UpdateZoneDataGroupService" />
	<bean id="tpadmin.UpdateSectionService" 							class="com.prinova.messagepoint.platform.services.tpadmin.UpdateSectionService" />
	<bean id="tpadmin.UpdateDocumentService" 							class="com.prinova.messagepoint.platform.services.tpadmin.UpdateDocumentService"/>
	<bean id="tpadmin.DeleteDocumentSectionService" 					class="com.prinova.messagepoint.platform.services.tpadmin.DeleteDocumentSectionService" />
	<bean id="tpadmin.CreateDocumentService" 							class="com.prinova.messagepoint.platform.services.tpadmin.CreateDocumentService" />
	<bean id="tpadmin.CreateDocumentAdvancedService" 					class="com.prinova.messagepoint.platform.services.tpadmin.CreateDocumentAdvancedService" />
	<bean id="tpadmin.EMailTemplateService" 							class="com.prinova.messagepoint.platform.services.tpadmin.EMailTemplateService" />
	<bean id="tpadmin.CleanupEMailTemplateService" 						class="com.prinova.messagepoint.platform.services.tpadmin.CleanupEMailTemplateService" />
	<bean id="tpadmin.MoveEMailTemplateService" 						class="com.prinova.messagepoint.platform.services.tpadmin.MoveEMailTemplateService" />
	<bean id="tpadmin.UpdateDocumentExtendedReportingVariablesService" 	class="com.prinova.messagepoint.platform.services.tpadmin.UpdateDocumentExtendedReportingVariablesService" />
	<bean id="tpadmin.TouchpointLanguageEditService" 					class="com.prinova.messagepoint.platform.services.tpadmin.TouchpointLanguageEditService" />
	<bean id="tpadmin.CreateOrUpdateCompositionFileSetService" 			class="com.prinova.messagepoint.platform.services.tpadmin.CreateOrUpdateCompositionFileSetService" />
	<bean id="tpadmin.CleanupCompositionPacksService" 					class="com.prinova.messagepoint.platform.services.tpadmin.CleanupCompositionPacksService" />
	<bean id="tpadmin.CreateOrUpdateTextStyleCustomizationService" 		class="com.prinova.messagepoint.platform.services.tpadmin.CreateOrUpdateTextStyleCustomizationService" />
	<bean id="tpadmin.RemoveStyleCustomizationService" 					class="com.prinova.messagepoint.platform.services.tpadmin.RemoveStyleCustomizationService" />
	<bean id="tpadmin.UpdateTouchpointDataSourceAssociationService" 	class="com.prinova.messagepoint.platform.services.tpadmin.UpdateTouchpointDataSourceAssociationService" />
	<bean id="tpadmin.UpdateTouchpointLayoutService" 					class="com.prinova.messagepoint.platform.services.tpadmin.UpdateTouchpointLayoutService" />
	<bean id="tpadmin.UpdateTouchpointCollectionService" 				class="com.prinova.messagepoint.platform.services.tpadmin.UpdateTouchpointCollectionService" />
	<bean id="tpadmin.BulkDeleteTouchpointCollectionsService" 			class="com.prinova.messagepoint.platform.services.tpadmin.BulkDeleteTouchpointCollectionsService" />
	<bean id="tpadmin.DeleteTouchpointCollectionService" 				class="com.prinova.messagepoint.platform.services.tpadmin.DeleteTouchpointCollectionService" />
	<bean id="tpadmin.CreateSegmentationAnalysisService" 				class="com.prinova.messagepoint.platform.services.tpadmin.CreateSegmentationAnalysisService" />
	<bean id="tpadmin.ParseSegmentationAnalysisReportService" 			class="com.prinova.messagepoint.platform.services.tpadmin.ParseSegmentationAnalysisReportService" />
	<bean id="tpadmin.CreateOrUpdateAlternateLayoutService" 			class="com.prinova.messagepoint.platform.services.tpadmin.CreateOrUpdateAlternateLayoutService" />
	<bean id="tpadmin.TouchpointVersioningService" 						class="com.prinova.messagepoint.platform.services.tpadmin.TouchpointVersioningService"   scope="prototype"  />
	<bean id="tpadmin.TPAccessControlService" 							class="com.prinova.messagepoint.platform.services.tpadmin.TPAccessControlService" />
	<bean id="tpadmin.BulkDeleteZoneService"							class="com.prinova.messagepoint.platform.services.tpadmin.BulkDeleteZoneService" />
	<bean id="tpadmin.DeleteZoneService"								class="com.prinova.messagepoint.platform.services.tpadmin.DeleteZoneService" />
	<bean id="tpadmin.UpdateStripoTemplateService"						class="com.prinova.messagepoint.platform.services.tpadmin.UpdateStripoTemplateService" />
	<bean id="tpadmin.BulkCleanupDuplicatedZonesService"			    class="com.prinova.messagepoint.platform.services.tpadmin.BulkCleanupDuplicatedZonesService" />
	<bean id="tpadmin.UpdateFormattingSelectionsService"			    class="com.prinova.messagepoint.platform.services.tpadmin.UpdateFormattingSelectionsService" />

	<!-- Touchpoint Selection 
	     ========================== -->
	<bean id="tpselection.ApproveTouchpointVariantService" 			class="com.prinova.messagepoint.platform.services.tpselection.ApproveTouchpointVariantService" />
	<bean id="tpselection.CreateOrUpdateTouchpointVariantService" 	class="com.prinova.messagepoint.platform.services.tpselection.CreateOrUpdateTouchpointVariantService" />
	<bean id="tpselection.GetTouchpointVariantListService" 			class="com.prinova.messagepoint.platform.services.tpselection.GetTouchpointVariantListService" />
	<bean id="tpselection.UpdateTouchpointProofingDataService" 			class="com.prinova.messagepoint.platform.services.tpselection.UpdateTouchpointProofingDataService" />
	<bean id="tpselection.UpdateTouchpointChannelService" 				class="com.prinova.messagepoint.platform.services.tpselection.UpdateTouchpointChannelService" />
	<bean id="tpselection.UpdateTouchpointVariantVisibiltyService" 	class="com.prinova.messagepoint.platform.services.tpselection.UpdateTouchpointVariantVisibiltyService" />
	<bean id="tpselection.CreateTouchpointVariantProofService" 		class="com.prinova.messagepoint.platform.services.tpselection.CreateTouchpointVariantProofService" />
	<bean id="tpselection.UpdateTouchpointVariantTemplateService"		class="com.prinova.messagepoint.platform.services.tpselection.UpdateTouchpointVariantTemplateService" />
	<bean id="tpselection.UpdateAlternateLayoutAssignmentService" 		class="com.prinova.messagepoint.platform.services.tpselection.UpdateAlternateLayoutAssignmentService" />
	<bean id="tpselection.UpdateVariantWorkflowAssignmentService" 		class="com.prinova.messagepoint.platform.services.tpselection.UpdateVariantWorkflowAssignmentService" />
	<bean id="tpselection.UpdateMessageWorkflowAssignmentService" 		class="com.prinova.messagepoint.platform.services.tpselection.UpdateMessageWorkflowAssignmentService" />
	
	<!-- Language Selection 
	     ========================== -->
	<bean id="languageselection.CreateOrUpdateLanguageSelectionService" 	class="com.prinova.messagepoint.platform.services.languageselection.CreateOrUpdateLanguageSelectionService" />
	<bean id="languageselection.GetLanguageSelectionListService" 			class="com.prinova.messagepoint.platform.services.languageselection.GetLanguageSelectionListService" />	
		
	<!-- Job Services 
	     ========================== -->
	<bean id="job.JobPackerService" 									class="com.prinova.messagepoint.platform.services.job.JobPackerService" />
	<bean id="job.InvokeExternalTriggeredEventService"					class="com.prinova.messagepoint.platform.services.job.InvokeExternalTriggeredEventService" />
	<bean id="job.GetSelectableContentService"							class="com.prinova.messagepoint.platform.services.job.GetSelectableContentService" />
	<bean id="job.UpdateDeliveryEventStatusService"						class="com.prinova.messagepoint.platform.services.job.UpdateDeliveryEventStatusService" />
	<bean id="job.UpdateJobMetaDataService"								class="com.prinova.messagepoint.platform.services.job.UpdateJobMetaDataService" />
	<bean id="job.GetTPSelectableMessageContentService"					class="com.prinova.messagepoint.platform.services.job.GetTPSelectableMessageContentService" />	
	<bean id="job.GetEmbeddedContentXMLService"							class="com.prinova.messagepoint.platform.services.job.GetEmbeddedContentXMLService"/>
	<bean id="job.GetGraphicLibraryXMLService"							class="com.prinova.messagepoint.platform.services.job.GetGraphicLibraryXMLService"/>
	<bean id="job.AddDeliveryEventStateTransitionService"				class="com.prinova.messagepoint.platform.services.job.AddDeliveryEventStateTransitionService"/>

	<!-- Version Services 
	     ================= -->
	<bean id="version.ModelVersionMappingDBService" 					class="com.prinova.messagepoint.platform.services.version.ModelVersionMappingDBService" />
	<bean id="version.VersionedModelDBService" 							class="com.prinova.messagepoint.platform.services.version.VersionedModelDBService" />
	<bean id="version.CheckContentObjectInProductionService" 			class="com.prinova.messagepoint.platform.services.version.CheckContentObjectInProductionService" />
	<bean id="version.CheckVersionInProductionService" 					class="com.prinova.messagepoint.platform.services.version.CheckVersionInProductionService" />
	<bean id="version.CheckoutFromProductionService" 					class="com.prinova.messagepoint.platform.services.version.CheckoutFromProductionService" />
	<bean id="content.UnlockContentObjectService"   					class="com.prinova.messagepoint.platform.services.content.UnlockContentObjectService" />
	<bean id="content.ReassignContentObjectForTranslationService"   	class="com.prinova.messagepoint.platform.services.content.ReassignContentObjectForTranslationService" />

	<!-- Workgroup Services
	     ================= -->
	<bean id="workgroup.CreateWorkgroupService"   						class="com.prinova.messagepoint.platform.services.workgroup.CreateWorkgroupService" />
	<bean id="workgroup.UpdateWorkgroupService"   						class="com.prinova.messagepoint.platform.services.workgroup.UpdateWorkgroupService" />
	<bean id="workgroup.DeleteWorkgroupService"   						class="com.prinova.messagepoint.platform.services.workgroup.DeleteWorkgroupService" />
	<bean id="workgroup.UpdateWorkgroupZoneAssociationService"  		class="com.prinova.messagepoint.platform.services.workgroup.UpdateWorkgroupZoneAssociationService" />
			
	<!-- File Services
		 ================= -->
	<bean id="file.CreateOrUpdateSandboxFileService" 					class="com.prinova.messagepoint.platform.services.file.CreateOrUpdateSandboxFileService" />	
	<bean id="file.CreateOrUpdateDatabaseFileService" 					class="com.prinova.messagepoint.platform.services.file.CreateOrUpdateDatabaseFileService" />
	<bean id="file.UpdateSourceService" 								class="com.prinova.messagepoint.platform.services.file.UpdateSourceService" />	
			
	<!-- Parameter Services 
	     ================= -->
	<bean id="parameter.CreateOrUpdateParameterService"   				class="com.prinova.messagepoint.platform.services.parameter.CreateOrUpdateParameterService" />
	<bean id="parameter.DeleteParameterService"   						class="com.prinova.messagepoint.platform.services.parameter.DeleteParameterService" />
	<bean id="parameter.CreateParameterGroupService"   					class="com.prinova.messagepoint.platform.services.parameter.CreateParameterGroupService" />
	<bean id="parameter.UpdateParameterGroupService"   					class="com.prinova.messagepoint.platform.services.parameter.UpdateParameterGroupService" />
	<bean id="parameter.CloneParameterGroupInstanceCollectionService"   class="com.prinova.messagepoint.platform.services.parameter.CloneParameterGroupInstanceCollectionService" />
	<bean id="parameter.CreateParameterGroupInstanceCollectionService"  class="com.prinova.messagepoint.platform.services.parameter.CreateParameterGroupInstanceCollectionService" />
	<bean id="parameter.UpdateParameterGroupInstanceCollectionService" 	class="com.prinova.messagepoint.platform.services.parameter.UpdateParameterGroupInstanceCollectionService" />
	<bean id="parameter.CreateParamGroupTreeNodeService"  				class="com.prinova.messagepoint.platform.services.parameter.CreateParamGroupTreeNodeService" />
	<bean id="parameter.RenameParameterGroupTreeNodeService"  			class="com.prinova.messagepoint.platform.services.parameter.RenameParameterGroupTreeNodeService" />	
    <bean id="parameter.ParameterGroupSyncService"                      class="com.prinova.messagepoint.platform.services.parameter.ParameterGroupSyncService" scope="prototype" /> 

	<!-- Insert Services 
	     ================= -->
	<bean id="insert.CreateOrUpdateInsertService"   					class="com.prinova.messagepoint.platform.services.insert.CreateOrUpdateInsertService" />
	<bean id="insert.DeactivateService" 			   					class="com.prinova.messagepoint.platform.services.insert.DeactivateService" />
	<bean id="insert.DeleteService"	 			   						class="com.prinova.messagepoint.platform.services.insert.DeleteService" />
	<bean id="insert.ReassignService" 			   						class="com.prinova.messagepoint.platform.services.insert.ReassignService" />
	<bean id="insert.ReinstateService" 			   						class="com.prinova.messagepoint.platform.services.insert.ReinstateService" />
	<bean id="insert.ReleaseForApprovalService" 						class="com.prinova.messagepoint.platform.services.insert.ReleaseForApprovalService" />				
	<bean id="insert.GetInsertListService" 								class="com.prinova.messagepoint.platform.services.insert.GetInsertListService" />
	<bean id="insert.ApproveOrRejectService" 	  						class="com.prinova.messagepoint.platform.services.insert.ApprovalOrRejectService" />		
	<bean id="insert.ArchiveService"		 	  						class="com.prinova.messagepoint.platform.services.insert.ArchiveService" />
	<bean id="insert.DiscardService"		 	  						class="com.prinova.messagepoint.platform.services.insert.DiscardService" />

	<!-- Insert Schedule Services 
	     ================= -->
	<bean id="insert.GetInsertScheduleListService" 						class="com.prinova.messagepoint.platform.services.insert.GetInsertScheduleListService" />
	<bean id="insert.CreateOrUpdateInsertScheduleService"   			class="com.prinova.messagepoint.platform.services.insert.CreateOrUpdateInsertScheduleService" />
	<bean id="insert.ExportInsertSchedulesToCSVService"		 	  		class="com.prinova.messagepoint.platform.services.insert.ExportInsertSchedulesToCSVService" />
	     	
	<!-- Rate Sheet Services 
	     ================= -->
	<bean id="insert.CreateOrUpdateRateScheduleService"   				class="com.prinova.messagepoint.platform.services.insert.CreateOrUpdateRateScheduleService" />

	<!-- Tag Services 
	     ================= -->
	<bean id="tag.CreateOrUpdateTagService"   							class="com.prinova.messagepoint.platform.services.tag.CreateOrUpdateTagService" />
	<bean id="tag.ReassignService"   									class="com.prinova.messagepoint.platform.services.tag.ReassignService" />
	<bean id="tag.BulkDeleteTagsService"   								class="com.prinova.messagepoint.platform.services.tag.BulkDeleteTagsService" />
	
	<!-- Where To Used Services 
	     ================= -->
	<bean id="wtu.DirectReferencesFetchService"   						class="com.prinova.messagepoint.wtu.services.DirectReferencesFetchService"   scope="prototype"  />
	<bean id="wtu.DirectAllReferencesFetchService"   					class="com.prinova.messagepoint.wtu.services.DirectAllReferencesFetchService"   scope="prototype"  />
	<bean id="wtu.SingleDirectReferencesFetchService"   				class="com.prinova.messagepoint.wtu.services.SingleDirectReferencesFetchService"   scope="prototype"  />

	<!-- Where Used Report Services 
	     ================= -->
	<bean id="whereused.CreateWhereUsedReportService"   				class="com.prinova.messagepoint.platform.services.whereused.CreateWhereUsedReportService" />
	<bean id="export.ExportWhereUsedToXMLService"   					class="com.prinova.messagepoint.platform.services.export.ExportWhereUsedToXMLService" />

	<!-- Diagnostics Report Services 
	     ================= -->
	<bean id="admin.CreateDiagnosticsReportService"   					class="com.prinova.messagepoint.platform.services.admin.CreateDiagnosticsReportService" />
	<bean id="export.ExportDiagnosticsToXMLService"   					class="com.prinova.messagepoint.platform.services.export.ExportDiagnosticsToXMLService" />

	<!-- Unified Login Report Services 
		 ================= -->
	<bean id="admin.CreateUnifiedLoginReportService"	   				class="com.prinova.messagepoint.platform.services.admin.CreateUnifiedLoginReportService" />
	<bean id="export.ExportUnifiedLoginReportToXMLService"   			class="com.prinova.messagepoint.platform.services.export.ExportUnifiedLoginReportToXMLService" />
		
	<!-- Variant Workflow services 
		 ================= -->
	<bean id="workflow.CreateOrUpdateWorkflowService"                	class="com.prinova.messagepoint.platform.services.workflow.CreateOrUpdateWorkflowService"/>
	<bean id="workflow.ShareTouchpointWorkflowService"                	class="com.prinova.messagepoint.platform.services.workflow.ShareTouchpointWorkflowService"/>
	<bean id="workflow.WorkflowApproveService"                			class="com.prinova.messagepoint.platform.services.workflow.WorkflowApproveService"/>
	<bean id="workflow.WorkflowRejectService"                			class="com.prinova.messagepoint.platform.services.workflow.WorkflowRejectService"/>
	<bean id="workflow.WorkflowReleaseForApprovalService"               class="com.prinova.messagepoint.platform.services.workflow.WorkflowReleaseForApprovalService"/>
	<bean id="workflow.DeleteWorkflowService" 							class="com.prinova.messagepoint.platform.services.workflow.DeleteWorkflowService" />
	<bean id="workflow.BulkDeleteWorkflowService" 						class="com.prinova.messagepoint.platform.services.workflow.BulkDeleteWorkflowService" />		
	
	<!-- Query Service
		 ================= -->
	<bean id="query.HibernatePaginationService" 						class="com.prinova.messagepoint.query.service.HibernatePaginationService"/>
    <bean id="query.SqlQueryPaginationService"                          class="com.prinova.messagepoint.query.service.SqlQueryPaginationService" />
	
	<!-- Tag Cloud Service 
	     ================= -->
	<bean id="tagcloud.UpdateTagCloudService" 							class="com.prinova.messagepoint.platform.services.tagcloud.UpdateTagCloudService"/>
<!--	<bean id="tagcloud.CreateOrUpdateTagCloudService" 						class="com.prinova.messagepoint.platform.services.tagcloud.CreateOrUpdateTagCloudService"/>-->
	<bean id="tagcloud.BulkDeleteTagCloudService" 						class="com.prinova.messagepoint.platform.services.tagcloud.BulkDeleteTagCloudService"/>

	<!-- Communications 
	     ================= -->
	<bean id="communication.CreateCommunicationProofService" 			class="com.prinova.messagepoint.platform.services.communication.CreateCommunicationProofService"/>
	<bean id="communication.CreateOrUpdateCommunicationService" 		class="com.prinova.messagepoint.platform.services.communication.CreateOrUpdateCommunicationService"/>
	<bean id="communication.CreateCommunicationProductionBatchService" 	class="com.prinova.messagepoint.platform.services.communication.CreateCommunicationProductionBatchService"/>
	<bean id="communication.CommunicationUnlockModelService" 			class="com.prinova.messagepoint.platform.services.communication.CommunicationUnlockModelService"/>
	<bean id="communication.CreateCommunicationProductionOnApprovalService" 	class="com.prinova.messagepoint.platform.services.communication.CreateCommunicationProductionOnApprovalService"/>
	<bean id="communication.UpdateConnectedWorkflowAssignmentService" 		class="com.prinova.messagepoint.platform.services.communication.UpdateConnectedWorkflowAssignmentService" />
	<bean id="connected.InteractiveDataService" 						class="com.prinova.messagepoint.connected.nojob.ConnectedInteractiveDataService" />

	<!-- Metadata 
	     ================= -->
	<bean id="metadata.CreateOrUpdateMetadataFormDefinitionService" 	class="com.prinova.messagepoint.platform.services.metadata.CreateOrUpdateMetadataFormDefinitionService"/>
	<bean id="metadata.CreateOrUpdateMetadataFormService" 				class="com.prinova.messagepoint.platform.services.metadata.CreateOrUpdateMetadataFormService"/>
	<bean id="metadata.BulkDeleteMetadataFormDefinitionService" 		class="com.prinova.messagepoint.platform.services.metadata.BulkDeleteMetadataFormDefinitionService"/>
	<bean id="metadata.ClearMetadataService"							class="com.prinova.messagepoint.platform.services.metadata.ClearMetadataService" />
	<bean id="metadata.CreateOrUpdateMetadataPointsOfInterestService"	class="com.prinova.messagepoint.platform.services.metadata.CreateOrUpdateMetadataPointsOfInterestService" />

	<!-- Licence
		================= -->
	<bean id="licence.AddOrUpdateMessagepointLicencesService" 			class="com.prinova.messagepoint.platform.services.licence.AddOrUpdateMessagepointLicencesService"/>
	<bean id="license.CreateOrUpdateMessagepointLicenseService" 		class="com.prinova.licence.service.CreateOrUpdateMessagepointLicenseService"/>

	<!-- Notification services 
		================= -->
	<bean id="notification.CreateOrUpdateNotificationSettingsService" 	class="com.prinova.messagepoint.platform.services.notification.CreateOrUpdateNotificationSettingsService" />

	<!-- DE Server Services -->
	<bean id="deserver.CreateOrUpdateDEServerService" class="com.prinova.messagepoint.platform.services.admin.deserver.CreateOrUpdateDEServerService" />

	<!-- Dictionary -->
	<bean id="dictionary.MigrateDictionariesService" 				class="com.prinova.messagepoint.platform.services.dictionary.MigrateDictionariesService" />
	<bean id="dictionary.CreateOrUpdateDictionaryService" 	class="com.prinova.messagepoint.platform.services.dictionary.CreateOrUpdateDictionaryService" />
	<bean id="dictionary.DeleteDictionaryService" 					class="com.prinova.messagepoint.platform.services.dictionary.DeleteDictionaryService" />
	<bean id="dictionary.BulkUpdateDictionaryService" 			class="com.prinova.messagepoint.platform.services.dictionary.BulkUpdateDictionaryService" />
	<bean id="dictionary.BulkDeleteDictionaryService" 				class="com.prinova.messagepoint.platform.services.dictionary.BulkDeleteDictionaryService" />

	<!-- Bundle Delivery -->
	<bean id="bundledelivery.MigrateBundleDeliveryPostProcessScriptsService" class="com.prinova.messagepoint.platform.services.bundledelivery.MigrateBundleDeliveryPostProcessScriptsService" />

	<!-- Elastic Search Http Client -->
	<bean id="rationalizerElasticSearchFactory" class="com.prinova.messagepoint.platform.services.elasticsearch.HttpClientRationalizerElasticSearchFactory"/>
	<bean id="messagepointElasticSearchFactory" class="com.prinova.messagepoint.platform.services.elasticsearch.HttpClientMessagepointElasticSearchFactory"/>

	<!-- Marcie Diagnostic Http Service -->
	<bean id="marcieDiagnosticService" class="com.prinova.messagepoint.platform.services.elasticsearch.HttpMarcieDiagnosticService"/>

	<!-- Rationalizer Services -->
	<bean id="rationalizerDCFieldsService" class="com.prinova.messagepoint.platform.services.rationalizer.RationalizerDCFieldsService"/>
	<bean id="rationalizerUtilsService" class="com.prinova.messagepoint.platform.services.rationalizer.RationalizerUtilsService"/>

	<!-- Rationalizer Document Adapter -->
	<bean id="rationalizerDocumentAdapter" class="com.prinova.messagepoint.platform.services.rationalizer.RationalizerDocumentAdapter"/>

	<!-- Rationalizer Document Adapter -->
	<bean id="rationalizerMetadataAdapter" class="com.prinova.messagepoint.platform.services.rationalizer.RationalizerMetadataAdapter"/>


	<!-- Rationalizer Document Upload Service -->
	<bean id="rationalizerUploadService" class="com.prinova.messagepoint.platform.services.rationalizer.RationalizerUploadService" />

    <!-- Rationalizer Document Ingestion Upload Service -->
	<bean id="rationalizerIngestionUploadService" class="com.prinova.messagepoint.platform.services.rationalizer.RationalizerIngestionUploadService" />

	<!-- Rationalizer Document Creator Service -->
	<bean id="rationalizerDocumentCreatorService" class="com.prinova.messagepoint.platform.services.rationalizer.RationalizerDocumentCreatorService"/>

	<!-- Configurable Links Service -->
	<bean id="link.ConfigurableLinksService" class="com.prinova.messagepoint.platform.services.link.ConfigurableLinksService"/>
	<!-- Messagepoint Spring Profile Manager -->
	<bean class="com.prinova.messagepoint.MessagepointSpringProfileManager" name="mpSpringProfileManager" />

	<!-- Rationalizer Consolidate Service -->
	<bean id="rationalizerConsolidateService" class="com.prinova.messagepoint.platform.services.rationalizer.RationalizerConsolidateService"/>

	<!-- Rationalizer Touchpoint Export Service -->
	<bean id="rationalizerTouchpointExportService" class="com.prinova.messagepoint.platform.services.rationalizer.RationalizerTouchpointExportService"/>

	<!-- Rationalizer Touchpoint Export Tree Compare Service -->
	<bean id="rationalizerTouchpointExportTreeCompareService" class="com.prinova.messagepoint.platform.services.rationalizer.RationalizerTouchpointExportTreeCompareService"/>

	<!-- Sync 2.0 Main Service -->
	<bean class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncMainControlService" name="syncMainControlService" scope="prototype" />
	<bean id="sync.syncDataSourcesService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncDataSourcesService" />
	<bean id="sync.syncLookupTablesService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncLookupTablesService" />
	<bean id="sync.syncDataElementVariablesService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncDataElementVariablesService" />
    <bean id="sync.syncDataCollectionService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncDataCollectionService" />
    <bean id="sync.syncParameterGroupsService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncParameterGroupsService" />
    <bean id="sync.syncTargetRulesService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncTargetRulesService" />
    <bean id="sync.syncTargetGroupsService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncTargetGroupsService" />
    <bean id="sync.syncDocumentAndLayoutsService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncDocumentAndLayoutsService" />
    <bean id="sync.syncCompositionPackageService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncCompositionPackageService" />
    <bean id="sync.syncContentObjectsService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncContentObjectsService" />
	<bean id="sync.syncTextStyleService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncTextStyleService" />
    <bean id="sync.syncDataFilesService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncDataFilesService" />
	<bean id="sync.syncTextStyleFontService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncTextStyleFontService" />
	<bean id="sync.syncDataResourcesService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncDataResourcesService" />
    <bean id="sync.syncTouchpointVariantsService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncTouchpointVariantsService" />
	<bean id="sync.syncChannelConfigurationService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncChannelConfigurationService" />
    <bean id="sync.syncListStylesService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncListStylesService" scope="prototype"  />
    <bean id="sync.syncAssociateReferencedObjectsService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncAssociateReferencedObjectsService" scope="prototype"  />
    <bean id="sync.syncParagraphStylesService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncParagraphStylesService" scope="prototype"  />
    <bean id="sync.syncMetadataTemplateService" class="com.prinova.messagepoint.platform.services.backgroundtask.sync.SyncMetadataTemplateService" scope="prototype"  />
</beans>
