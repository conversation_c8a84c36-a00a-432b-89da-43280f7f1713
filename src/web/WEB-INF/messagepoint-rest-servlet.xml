<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:security="http://www.springframework.org/schema/security"
       xsi:schemaLocation="
       http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/mvc
       http://www.springframework.org/schema/mvc/spring-mvc.xsd
        http://www.springframework.org/schema/security
        http://www.springframework.org/schema/security/spring-security-5.8.xsd">

    <context:component-scan base-package="com.prinova.messagepoint.platform.mprest, com.prinova.messagepoint.platform.auth" />
    <mvc:annotation-driven />

    <security:global-method-security pre-post-annotations="enabled" secured-annotations="enabled"/>
    <security:method-security secured-enabled="true" pre-post-enabled="true" />

    <mvc:interceptors>
        <bean id="mpRestRateLimitInterceptor" class="com.prinova.messagepoint.platform.auth.MpRestRateLimitInterceptor" />
        <bean id="mpRestAuthenticationInterceptor" class="com.prinova.messagepoint.platform.auth.MpRestAuthenticationInterceptor" />
        <bean id="swaggerUIHandlerInterceptor" class="com.prinova.messagepoint.platform.auth.SwaggerUIHandlerInterceptor" />
    </mvc:interceptors>

    <!-- Springdocs Swagger UI -->
    <bean id="mvcConversionService" class="org.springframework.format.support.FormattingConversionServiceFactoryBean" />
    <bean class="org.springdoc.core.SpringDocConfigProperties" id="springDocConfigProperties" />
    <bean class="org.springdoc.core.SpringDocConfiguration" id="springDocConfiguration" />
    <bean class="org.springdoc.webmvc.core.SpringDocWebMvcConfiguration" id="springDocWebMvcConfiguration" />

    <bean class="com.prinova.messagepoint.OpenApiConfiguration" id="openApiConfiguration" />

    <bean class="org.springdoc.core.SwaggerUiOAuthProperties" id="swaggerUiOAuthProperties" />
    <bean class="org.springdoc.core.SwaggerUiConfigProperties" id="swaggerUiConfigProperties" />
    <bean class="org.springdoc.webmvc.ui.SwaggerConfig" id="swaggerConfig" />

    <mvc:resources mapping="/rest/swagger-ui/swagger-initializer.js" location="static/" />
    <mvc:resources mapping="/rest/swagger-ui/**" location="classpath:/META-INF/resources/webjars/swagger-ui/4.17.1/" />
    <mvc:resources mapping="/swagger-ui/**" location="classpath:/META-INF/resources/webjars/swagger-ui/4.17.1/" />

</beans>