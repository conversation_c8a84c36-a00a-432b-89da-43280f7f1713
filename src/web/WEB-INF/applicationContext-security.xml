<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:security="http://www.springframework.org/schema/security"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
       					http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
              			http://www.springframework.org/schema/security
						http://www.springframework.org/schema/security/spring-security-5.8.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

	<bean id="springSecurityFilterChain" class="org.springframework.security.web.FilterChainProxy">
		<security:filter-chain-map request-matcher="ant">
			<security:filter-chain pattern="/sso/**" filters="ssoEntryPoint"/>
			<security:filter-chain pattern="/**"
								   filters="httpSessionContextIntegrationFilter,
							logoutFilter,
							authenticationProcessingFilter,
							securityContextHolderAwareRequestFilter,
							anonymousProcessingFilter,
							exceptionTranslationFilter,
							filterInvocationInterceptor,
							statusProviderSecurityFilter" />
		</security:filter-chain-map>
	</bean>

	<!-- Entry point to initialize authentication, default values taken from properties file -->
	<bean id="ssoEntryPoint" class="com.prinova.messagepoint.security.sso.SSOFilter">
	</bean>

	<bean id="httpSessionContextIntegrationFilter" class="org.springframework.security.web.context.SecurityContextPersistenceFilter"/>
	<bean id="statusProviderSecurityFilter" class="com.prinova.messagepoint.security.StatusProviderSecurityFilter" />

	<bean id="logoutFilter" class="com.prinova.messagepoint.controller.MessagepointLogoutFilter">
		<constructor-arg value="/displaymessage.form?msgkey=page.text.signoutsuccess"/>    <!-- URL redirected to after logout -->
		<constructor-arg>
			<list>
				<bean class="org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler"/>
				<bean class="com.prinova.messagepoint.security.SessionDestroyer"/>
			</list>
		</constructor-arg>
		<property name="filterProcessesUrl" value="/signout" />
	</bean>

	<bean id="messagepointAuthenticationSuccessHandler" class="com.prinova.messagepoint.controller.MessagepointAuthenticationSuccessHandler">
		<property name="defaultTargetUrl" value="/index.jsp"/>
		<property name="unmatchingUserIdUrl" value="/edit_userid.form"/>
		<property name="unmatchingPasswordUrl" value="/edit_password.form"/>
		<property name="unmatchingUserIdAndPasswordUrl" value="/edit_userid_password.form"/>
	</bean>

	<bean id="messagepointAuthenticationFailureHandler" class="com.prinova.messagepoint.controller.MessagepointAuthenticationFailureHandler">
		<property name="authenticationFailureUrl" value="/signin.jsp"/>
	</bean>

	<bean id="authenticationProcessingFilter" class="com.prinova.messagepoint.controller.MessagepointAuthenticationProcessingFilter">
		<property name="authenticationManager" ref="authenticationManager"/>
		<property name="filterProcessesUrl" value="/j_spring_security_check"/>
		<property name="authenticationSuccessHandler" ref="messagepointAuthenticationSuccessHandler"/>
		<property name="authenticationFailureHandler" ref="messagepointAuthenticationFailureHandler"/>
		<property name="sessionAuthenticationStrategy">
			<bean class="org.springframework.security.web.authentication.session.SessionFixationProtectionStrategy"/>
		</property>
	</bean>

	<bean id="securityContextHolderAwareRequestFilter" class="org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter"/>

	<bean id="anonymousProcessingFilter" class="org.springframework.security.web.authentication.AnonymousAuthenticationFilter">
		<constructor-arg name="key" value="anonymousProviderKeyForUMH" />
	</bean>

	<bean id="exceptionTranslationFilter" class="org.springframework.security.web.access.ExceptionTranslationFilter">
		<constructor-arg name="authenticationEntryPoint">
			<bean class="com.prinova.messagepoint.controller.MessagepointAuthProcessingFilterEntryPoint">
				<constructor-arg name="loginFormUrl" value="/signin.jsp" />
				<property name="forceHttps" value="false"/>
			</bean>
		</constructor-arg>
		<property name="accessDeniedHandler">
			<bean class="org.springframework.security.web.access.AccessDeniedHandlerImpl">
				<property name="errorPage" value="/displaymessage.form?msgkey=code.text.accessdenied"/>
			</bean>
		</property>
	</bean>

	<bean id="filterInvocationInterceptor" class="org.springframework.security.web.access.intercept.FilterSecurityInterceptor">
		<property name="authenticationManager" ref="authenticationManager"/>
		<property name="accessDecisionManager">
			<bean class="org.springframework.security.access.vote.AffirmativeBased">
				<constructor-arg>
					<util:list list-class="java.util.ArrayList" value-type="org.springframework.security.access.AccessDecisionVoter">
						<bean class="org.springframework.security.access.vote.RoleVoter"/>
						<bean class="org.springframework.security.access.vote.AuthenticatedVoter"/>
						<bean class="org.springframework.security.web.access.expression.WebExpressionVoter" />
					</util:list>
				</constructor-arg>
				<property name="allowIfAllAbstainDecisions" value="false"/>
			</bean>
		</property>

		<property name="securityMetadataSource">
			<security:filter-security-metadata-source request-matcher="ant">
				<security:intercept-url pattern="/sso/**" access="hasAnyRole('ROLE_ANONYMOUS')" />
				<security:intercept-url pattern="/index.jsp" access="hasAnyRole('ROLE_USER', 'ROLE_SYSTEM_MULTI_TENANT')" />
				<security:intercept-url pattern="/security/owasp.csrfguard.form" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/attachment/attachment_**" access="hasAnyRole('ROLE_TOUCHPOINT_ADMIN')" />
				<security:intercept-url pattern="/switch_to_tenant.form" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/switch_to_node.form" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/password_recovery**" access="hasAnyRole('ROLE_ANONYMOUS','ROLE_USER')" />
				<security:intercept-url pattern="/user/settings_edit**" access="hasAnyRole('ROLE_ANONYMOUS','ROLE_USER')" />
				<security:intercept-url pattern="/user/workspace_reset**" access="hasAnyRole('ROLE_ANONYMOUS','ROLE_USER')" />
				<security:intercept-url pattern="/edit_userid.form" access="hasAnyRole('ROLE_INVALID_USERID','ROLE_USER')" />
				<security:intercept-url pattern="/edit_password.form" access="hasAnyRole('ROLE_INVALID_PASSWORD','ROLE_USER')" />
				<security:intercept-url pattern="/edit_userid_password.form" access="hasAnyRole('ROLE_INVALID_USERID_PASSWORD','ROLE_USER')" />
				<security:intercept-url pattern="/message/index.jsp" access="hasAnyRole('ROLE_MESSAGE_VIEW_ALL','ROLE_MESSAGE_VIEW_MY')" />
				<security:intercept-url pattern="/message/redirect.form" access="hasAnyRole('ROLE_MESSAGE_VIEW_ALL','ROLE_MESSAGE_VIEW_MY')" />
				<security:intercept-url pattern="/insert/insert_list**" access="hasAnyRole('ROLE_INSERT_VIEW','ROLE_INSERT_APPROVE','ROLE_INSERT_ARCHIVE','ROLE_INSERT_REASSIGN')" />
				<security:intercept-url pattern="/insert/insert_view**" access="hasAnyRole('ROLE_INSERT_VIEW','ROLE_INSERT_APPROVE','ROLE_INSERT_ARCHIVE','ROLE_INSERT_REASSIGN')" />
				<security:intercept-url pattern="/insert/insert_overview_edit**" access="hasAnyRole('ROLE_INSERT_EDIT')" />
				<security:intercept-url pattern="/insert/insert_targeting_edit**" access="hasAnyRole('ROLE_INSERT_EDIT')" />
				<security:intercept-url pattern="/insert/insert_content_edit**" access="hasAnyRole('ROLE_INSERT_EDIT')" />
				<security:intercept-url pattern="/insert/insert_schedule_list**" access="hasAnyRole('ROLE_INSERT_SCHEDULE_VIEW','ROLE_INSERT_SCHEDULE_SETUP','ROLE_INSERT_SCHEDULE_APPROVE','ROLE_INSERT_SCHEDULE_REASSIGN')" />
				<security:intercept-url pattern="/insert/insert_schedule_view**" access="hasAnyRole('ROLE_INSERT_SCHEDULE_VIEW','ROLE_INSERT_SCHEDULE_SETUP','ROLE_INSERT_SCHEDULE_APPROVE','ROLE_INSERT_SCHEDULE_REASSIGN')" />
				<security:intercept-url pattern="/insert/insert_schedule_overview_edit**" access="hasAnyRole('ROLE_INSERT_SCHEDULE_EDIT','ROLE_INSERT_SCHEDULE_SETUP')" />
				<security:intercept-url pattern="/insert/insert_schedule_rate_schedule_edit**" access="hasAnyRole('ROLE_INSERT_SCHEDULE_EDIT','ROLE_INSERT_SCHEDULE_SETUP')" />
				<security:intercept-url pattern="/insert/insert_schedule_selector_edit**" access="hasAnyRole('ROLE_INSERT_SCHEDULE_EDIT','ROLE_INSERT_SCHEDULE_SETUP')" />
				<security:intercept-url pattern="/insert/insert_schedule_bin_assignment_edit**" access="hasAnyRole('ROLE_INSERT_SCHEDULE_EDIT','ROLE_INSERT_SCHEDULE_SETUP')" />
				<security:intercept-url pattern="/insert/insert_schedule_insert_timing_edit**" access="hasAnyRole('ROLE_INSERT_SCHEDULE_EDIT','ROLE_INSERT_SCHEDULE_SETUP')" />
				<security:intercept-url pattern="/insert/rate_schedule_list**" access="hasAnyRole('ROLE_RATE_SCHEDULE_VIEW')" />
				<security:intercept-url pattern="/insert/rate_schedule_view**" access="hasAnyRole('ROLE_RATE_SCHEDULE_VIEW')" />
				<security:intercept-url pattern="/insert/rate_schedule_edit**" access="hasAnyRole('ROLE_RATE_SCHEDULE_EDIT')" />
				<security:intercept-url pattern="/content/messagepartcategories**" access="hasAnyRole('ROLE_TOUCHPOINT_ADMIN')" />
				<security:intercept-url pattern="/content/messagepartcategory_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_ADMIN')" />
				<security:intercept-url pattern="/content/text_style_list**" access="hasAnyRole('ROLE_STYLES_UPDATE')" />
				<security:intercept-url pattern="/content/text_style_connectors_list**" access="hasAnyRole('ROLE_STYLES_UPDATE')" />
				<security:intercept-url pattern="/content/paragraph_style_list**" access="hasAnyRole('ROLE_STYLES_UPDATE')" />
				<security:intercept-url pattern="/content/paragraph_style_edit**" access="hasAnyRole('ROLE_STYLES_UPDATE')" />
				<security:intercept-url pattern="/content/list_style_list**" access="hasAnyRole('ROLE_STYLES_UPDATE')" />
				<security:intercept-url pattern="/content/list_style_edit**" access="hasAnyRole('ROLE_STYLES_UPDATE')" />
				<security:intercept-url pattern="/content/text_style_transformation_list**" access="hasAnyRole('ROLE_STYLES_UPDATE')" />
				<security:intercept-url pattern="/content/text_style_transformation_edit**" access="hasAnyRole('ROLE_STYLES_UPDATE')" />
				<security:intercept-url pattern="/content/content_object_edit**" access="hasAnyRole('ROLE_MESSAGE_EDIT','ROLE_EMBEDDED_CONTENT_EDIT','ROLE_CONTENT_LIBRARY_EDIT')" />
				<security:intercept-url pattern="/content/content_object_view**" access="hasAnyRole('ROLE_MESSAGE_VIEW_ALL','ROLE_MESSAGE_VIEW_MY','ROLE_EMBEDDED_CONTENT_VIEW','ROLE_EMBEDDED_CONTENT_ADMIN')" />
				<security:intercept-url pattern="/content/content_object_review_popup**" access="hasAnyRole('ROLE_EMBEDDED_CONTENT_VIEW')" />
				<security:intercept-url pattern="/content/content_targeting_edit**" access="hasAnyRole('ROLE_MESSAGE_VIEW_ALL','ROLE_MESSAGE_VIEW_MY')" />
				<security:intercept-url pattern="/content/global_content_list**" access="hasAnyRole('ROLE_EMBEDDED_CONTENT_VIEW','ROLE_CONTENT_LIBRARY_VIEW','ROLE_EMBEDDED_CONTENT_ADMIN')" />
				<security:intercept-url pattern="/content/index.jsp" access="hasAnyRole('ROLE_SELECTABLE_CONSTANT_VIEW','ROLE_TOUCHPOINT_ADMIN','ROLE_MESSAGE_VIEW_MY','ROLE_MESSAGE_VIEW_ALL','ROLE_EMBEDDED_CONTENT_VIEW','ROLE_EMBEDDED_CONTENT_ADMIN','ROLE_CONTENT_LIBRARY_VIEW','ROLE_CONTENT_LIBRARY_ADMIN')" />
				<security:intercept-url pattern="/content/redirect.form" access="hasAnyRole('ROLE_SELECTABLE_CONSTANT_VIEW','ROLE_TOUCHPOINT_ADMIN','ROLE_MESSAGE_VIEW_MY','ROLE_MESSAGE_VIEW_ALL','ROLE_EMBEDDED_CONTENT_VIEW','ROLE_EMBEDDED_CONTENT_ADMIN','ROLE_CONTENT_LIBRARY_VIEW','ROLE_CONTENT_LIBRARY_ADMIN')" />
				<security:intercept-url pattern="/contextbar/**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/communication/**" access="hasAnyRole('ROLE_COMMUNICATIONS_VIEW')" />
				<security:intercept-url pattern="/connected/**" access="hasAnyRole('ROLE_COMMUNICATIONS_VIEW')" />
				<security:intercept-url pattern="/communication/communication_**" access="hasAnyRole('ROLE_COMMUNICATIONS_EDIT')" />
				<security:intercept-url pattern="/communication/connected_**" access="hasAnyRole('ROLE_COMMUNICATIONS_EDIT')" />
				<security:intercept-url pattern="/communication_external_validation**" access="hasAnyRole('ROLE_ANONYMOUS','ROLE_USER')" />
				<security:intercept-url pattern="/dataadmin/datasources**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_LIST')" />
				<security:intercept-url pattern="/dataadmin/datarecords**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_LIST')" />
				<security:intercept-url pattern="/dataadmin/dataelements**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_LIST')" />
				<security:intercept-url pattern="/dataadmin/dataelement**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_LIST')" />
				<security:intercept-url pattern="/dataadmin/variable_list**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_LIST')" />
				<security:intercept-url pattern="/dataadmin/datagroups**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_LIST')" />
				<security:intercept-url pattern="/dataadmin/constants**" access="hasAnyRole('ROLE_CONSTANT_VIEW')" />
				<security:intercept-url pattern="/dataadmin/constant_edit**" access="hasAnyRole('ROLE_CONSTANT_EDIT')" />
				<security:intercept-url pattern="/dataadmin/constant_delete**" access="hasAnyRole('ROLE_CONSTANT_EDIT')" />
				<security:intercept-url pattern="/dataadmin/data_source_associations**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_LIST')" />
				<security:intercept-url pattern="/dataadmin/parameter_list**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_LIST')" />
				<security:intercept-url pattern="/dataadmin/parameter_group_list**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_LIST')" />
				<security:intercept-url pattern="/dataadmin/data_source_association_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/data_source_association_delete**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/data_source_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/data_record_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/json_data_definition_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/json_data_element_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/json_data_key_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/xml_data_tag_definition_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/data_element_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/xml_data_element_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/data_group_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/data_group_delete**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/parameter_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/parameter_delete_confirmation**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/parameter_group_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/parameter_delete**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/target**" access="hasAnyRole('ROLE_TARGETING_EDIT')" />
				<security:intercept-url pattern="/dataadmin/conditionelements**" access="hasAnyRole('ROLE_RULE_VIEW')" />
				<security:intercept-url pattern="/dataadmin/condition_element_edit**" access="hasAnyRole('ROLE_RULE_EDIT')" />
				<security:intercept-url pattern="/dataadmin/condition_element_delete**" access="hasAnyRole('ROLE_RULE_EDIT')" />
				<security:intercept-url pattern="/dataadmin/data_record_delete**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/lookup_table**" access="hasAnyRole('ROLE_LOOKUP_TABLE_VIEW','ROLE_LOOKUP_TABLE_EDIT','ROLE_LOOKUP_TABLE_APPROVE','ROLE_LOOKUP_TABLE_SETUP')" />
				<security:intercept-url pattern="/dataadmin/variable_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/variable_delete**" access="hasAnyRole('ROLE_TOUCHPOINT_DATA_EDIT')" />
				<security:intercept-url pattern="/dataadmin/external_event_list**" access="hasAnyRole('ROLE_EXTERNAL_EVENT_VIEW')" />
				<security:intercept-url pattern="/dataadmin/index**" access="hasAnyRole('ROLE_TOUCHPOINT_ADMIN','ROLE_TARGETING_EDIT','ROLE_TOUCHPOINT_DATA_LIST','ROLE_TOUCHPOINT_DATA_EDIT','ROLE_RULE_VIEW','ROLE_RULE_EDIT','ROLE_CONSTANT_VIEW','ROLE_CONSTANT_EDIT','ROLE_EXTERNAL_EVENT_VIEW','ROLE_EXTERNAL_EVENT_EDIT')" />
				<security:intercept-url pattern="/dataadmin/redirect.form" access="hasAnyRole('ROLE_TOUCHPOINT_ADMIN','ROLE_TARGETING_EDIT','ROLE_TOUCHPOINT_DATA_LIST','ROLE_TOUCHPOINT_DATA_EDIT','ROLE_RULE_VIEW','ROLE_RULE_EDIT','ROLE_CONSTANT_VIEW','ROLE_CONSTANT_EDIT','ROLE_EXTERNAL_EVENT_VIEW','ROLE_EXTERNAL_EVENT_EDIT')" />
				<security:intercept-url pattern="/dataadmin/**" access="hasAnyRole('ROLE_TOUCHPOINT_ADMIN','ROLE_TARGETING_EDIT','ROLE_TOUCHPOINT_DATA_LIST','ROLE_TOUCHPOINT_DATA_EDIT','ROLE_RULE_VIEW','ROLE_RULE_EDIT','ROLE_CONSTANT_VIEW','ROLE_CONSTANT_EDIT','ROLE_EXTERNAL_EVENT_VIEW','ROLE_EXTERNAL_EVENT_EDIT')" />
				<security:intercept-url pattern="/datafile/**" access="hasAnyRole('ROLE_TOUCHPOINT_ADMIN')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_documents_list**" access="hasAnyRole('ROLE_ECATALOG_VIEW','ROLE_ECATALOG_EDIT')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_document_edit**" access="hasAnyRole('ROLE_ECATALOG_EDIT')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_metadata_edit**" access="hasAnyRole('ROLE_ECATALOG_EDIT')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_dc_fields**" access="hasAnyRole('ROLE_ECATALOG_EDIT')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_query_list**" access="hasAnyRole('ROLE_ECATALOG_VIEW','ROLE_ECATALOG_EDIT')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_query_edit**" access="hasAnyRole('ROLE_ECATALOG_EDIT')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_dashboard**" access="hasAnyRole('ROLE_ECATALOG_VIEW','ROLE_ECATALOG_EDIT')" />
				<security:intercept-url pattern="/reports/scenario_edit**" access="hasAnyRole('ROLE_REPORT_EDIT')" />
				<security:intercept-url pattern="/reports/**" access="hasAnyRole('ROLE_REPORT_VIEW')" />
				<security:intercept-url pattern="/simulations/index**" access="hasAnyRole('ROLE_SIMULATION_VIEW')" />
				<security:intercept-url pattern="/simulations/redirect**" access="hasAnyRole('ROLE_SIMULATION_VIEW')" />
				<security:intercept-url pattern="/simulations/simulation_action_confirm**" access="hasAnyRole('ROLE_SIMULATION_VIEW')" />
				<security:intercept-url pattern="/simulations/simulation_coverage_report**" access="hasAnyRole('ROLE_SIMULATION_VIEW')" />
				<security:intercept-url pattern="/simulations/simulation_messages_coverage_report**" access="hasAnyRole('ROLE_SIMULATION_VIEW')" />
				<security:intercept-url pattern="/simulations/simulation_view_inclusions**" access="hasAnyRole('ROLE_SIMULATION_VIEW')" />
				<security:intercept-url pattern="/simulations/simulations**" access="hasAnyRole('ROLE_SIMULATION_VIEW')" />
				<security:intercept-url pattern="/simulations/simulation_edit**" access="hasAnyRole('ROLE_SIMULATION_EDIT')" />
				<security:intercept-url pattern="/simulations/simulation_delete**" access="hasAnyRole('ROLE_SIMULATION_EDIT')" />
				<security:intercept-url pattern="/tasks/task_list**" access="hasAnyRole('ROLE_TASK_VIEW_MY','ROLE_TASK_VIEW_ALL','ROLE_TASK_EDIT','ROLE_TASK_SETUP')" />
				<security:intercept-url pattern="/tasks/task_edit**" access="hasAnyRole('ROLE_TASK_EDIT','ROLE_TASK_SETUP')" />
				<security:intercept-url pattern="/projects/**" access="hasAnyRole('ROLE_PROJECT_VIEW','ROLE_PROJECT_EDIT','ROLE_PROJECT_APPROVE','ROLE_PROJECT_SETUP')" />
				<security:intercept-url pattern="/tasks/**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/test/**" access="hasAnyRole('ROLE_SYSTEM_ADMIN')" />
				<security:intercept-url pattern="/testing/scenario_edit**" access="hasAnyRole('ROLE_TEST_EDIT')" />
				<security:intercept-url pattern="/testing/**" access="hasAnyRole('ROLE_TEST_VIEW')" />
				<security:intercept-url pattern="/metadata/meta_data_forms_list**" access="hasAnyRole('ROLE_METADATA_FORM_ADMIN')" />
				<security:intercept-url pattern="/touchpoints/touchpoint_content_list**" access="hasAnyRole('ROLE_TOUCHPOINT_CONTENT_VIEW','ROLE_TOUCHPOINT_CONTENT_ADMIN')" />
				<security:intercept-url pattern="/touchpoints/touchpoint_message_list**" access="hasAnyRole('ROLE_MESSAGE_VIEW_ALL','ROLE_MESSAGE_VIEW_MY','ROLE_TOUCHPOINT_SELECTIONS_VIEW','ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW')" />
				<security:intercept-url pattern="/touchpoints/touchpoint_variant_list**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_VIEW','ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW')" />
				<security:intercept-url pattern="/touchpoints/touchpoint_communications_list**" access="hasAnyRole('ROLE_COMMUNICATIONS_VIEW','ROLE_COMMUNICATIONS_EDIT')" />
				<security:intercept-url pattern="/touchpoints/zone_message_priority_edit**" access="hasAnyRole('ROLE_PRIORITY_ADMIN')" />
				<security:intercept-url pattern="/touchpoints/touchpoint_collection_list**" access="hasAnyRole('ROLE_COLLECTION_SETUP')" />
				<security:intercept-url pattern="/touchpoints/index**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_VIEW','ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW','ROLE_MESSAGE_VIEW_ALL','ROLE_MESSAGE_VIEW_MY','ROLE_TAG_VIEW','ROLE_TAG_EDIT','ROLE_COMMUNICATIONS_VIEW')" />
				<security:intercept-url pattern="/touchpoints/redirect.form" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_VIEW','ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW','ROLE_MESSAGE_VIEW_ALL','ROLE_MESSAGE_VIEW_MY','ROLE_TAG_VIEW','ROLE_TAG_EDIT','ROLE_COMMUNICATIONS_VIEW')" />
				<security:intercept-url pattern="/touchpoints/**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_VIEW','ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW','ROLE_MESSAGE_VIEW_ALL','ROLE_MESSAGE_VIEW_MY','ROLE_TAG_VIEW','ROLE_TAG_EDIT','ROLE_COMMUNICATIONS_VIEW')" />
				<security:intercept-url pattern="/tpadmin/touchpoint_approvals_list**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW')" />
				<security:intercept-url pattern="/tpadmin/touchpoint_selections_proofing_data_edit**" access="hasAnyRole('ROLE_PROOFING_DATA_UPDATE')" />
				<security:intercept-url pattern="/tpadmin/touchpoint_style_customizations**" access="hasAnyRole('ROLE_STYLES_UPDATE')" />
				<security:intercept-url pattern="/tpadmin/content_selection_view**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_VIEW','ROLE_TOUCHPOINT_SELECTIONS_EDIT')" />
				<security:intercept-url pattern="/tpadmin/content_selection_edit**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_VIEW','ROLE_TOUCHPOINT_SELECTIONS_EDIT')" />
				<security:intercept-url pattern="/tpadmin/tags_list**" access="hasAnyRole('ROLE_TAG_VIEW','ROLE_TAG_EDIT')" />
				<security:intercept-url pattern="/tpadmin/tag_edit**" access="hasAnyRole('ROLE_TAG_EDIT')" />
				<security:intercept-url pattern="/tpadmin/create_touchpoint**" access="hasAnyRole('ROLE_TOUCHPOINT_ADMIN')" />
				<security:intercept-url pattern="/tpadmin/touchpoint_layout_manager**" access="hasAnyRole('ROLE_TOUCHPOINT_ADMIN')" />
				<security:intercept-url pattern="/tpadmin/index**" access="hasAnyRole('ROLE_TOUCHPOINT_ADMIN','ROLE_TOUCHPOINT_SELECTIONS_VIEW','ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW','ROLE_TAG_VIEW','ROLE_TAG_EDIT','ROLE_MESSAGE_VIEW_ALL','ROLE_MESSAGE_VIEW_MY','ROLE_COMMUNICATIONS_VIEW')" />
				<security:intercept-url pattern="/tpadmin/redirect.form" access="hasAnyRole('ROLE_TOUCHPOINT_ADMIN','ROLE_TOUCHPOINT_SELECTIONS_VIEW','ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW','ROLE_TAG_VIEW','ROLE_TAG_EDIT','ROLE_MESSAGE_VIEW_ALL','ROLE_MESSAGE_VIEW_MY','ROLE_COMMUNICATIONS_VIEW')" />
				<security:intercept-url pattern="/tpadmin/**" access="hasAnyRole('ROLE_TOUCHPOINT_ADMIN','ROLE_TOUCHPOINT_SELECTIONS_VIEW','ROLE_TOUCHPOINT_SELECTIONS_ADMIN_VIEW','ROLE_TAG_VIEW','ROLE_TAG_EDIT','ROLE_MESSAGE_VIEW_ALL','ROLE_MESSAGE_VIEW_MY','ROLE_COMMUNICATIONS_VIEW')" />
				<security:intercept-url pattern="/tpadmin/touchpoint_exchange_list**" access="hasAnyRole('ROLE_TOUCHPOINT_EXCHANGE_VIEW')" />
				<security:intercept-url pattern="/tpadmin/variant_workflow_assignment_edit**" access="hasAnyRole('ROLE_WORKFLOW_ADMIN_EDIT')" />
				<security:intercept-url pattern="/user/**" access="hasAnyRole('ROLE_USER','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/licence_management**" access="hasAnyRole('ROLE_ADMIN_SETTINGS_EDIT','ROLE_MASTER_ADMIN')" />
				<security:intercept-url pattern="/admin/tenant_view**" access="hasAnyRole('ROLE_ADMIN_TENANT_VIEW','ROLE_ADMIN_TENANT_EDIT')" />
				<security:intercept-url pattern="/admin/tenant_edit**" access="hasAnyRole('ROLE_ADMIN_TENANT_EDIT')" />
				<security:intercept-url pattern="/admin/tenant_permissions_view**" access="hasAnyRole('ROLE_ADMIN_TENANT_VIEW','ROLE_ADMIN_TENANT_EDIT')" />
				<security:intercept-url pattern="/admin/tenant_permissions_edit**" access="hasAnyRole('ROLE_ADMIN_TENANT_EDIT')" />
				<security:intercept-url pattern="/admin/tenant_activate**" access="hasAnyRole('ROLE_ADMIN_TENANT_EDIT')" />
				<security:intercept-url pattern="/admin//tenant_activation_request**" access="hasAnyRole('ROLE_ADMIN_TENANT_EDIT')" />
				<security:intercept-url pattern="/admin/user_list**" access="hasAnyRole('ROLE_ADMIN_USER_VIEW','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/user_view**" access="hasAnyRole('ROLE_ADMIN_USER_VIEW','ROLE_ADMIN_USER_EDIT','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/user_edit**" access="hasAnyRole('ROLE_ADMIN_USER_EDIT','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/user_access_control**" access="hasAnyRole('ROLE_ADMIN_USER_EDIT','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/role_list**" access="hasAnyRole('ROLE_ADMIN_ROLE_VIEW','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/role_edit**" access="hasAnyRole('ROLE_ADMIN_ROLE_EDIT','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/role_view**" access="hasAnyRole('ROLE_ADMIN_ROLE_EDIT','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/services_list**" access="hasAnyRole('ROLE_ADMIN_USER_VIEW','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/maintenance_view**" access="hasAnyRole('ROLE_ADMIN_SETTINGS_EDIT','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/maintenance_edit**" access="hasAnyRole('ROLE_ADMIN_SETTINGS_EDIT','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/branch_list**" access="hasAnyRole('ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/branch_edit**" access="hasAnyRole('ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/node_edit**" access="hasAnyRole('ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/systemproperty_view**" access="hasAnyRole('ROLE_PROVIDER_SYSTEM_ADMIN','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/systemproperty_edit**" access="hasAnyRole('ROLE_PROVIDER_SYSTEM_ADMIN_EDIT','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/systemproperty_prinova_edit**" access="hasAnyRole('ROLE_MASTER_ADMIN')" />
				<security:intercept-url pattern="/admin/password_security_view**" access="hasAnyRole('ROLE_PROVIDER_SYSTEM_ADMIN')" />
				<security:intercept-url pattern="/admin/password_security_edit**" access="hasAnyRole('ROLE_PROVIDER_SYSTEM_ADMIN_EDIT')" />
				<security:intercept-url pattern="/admin/locale_settings_view**" access="hasAnyRole('ROLE_PROVIDER_SYSTEM_ADMIN')" />
				<security:intercept-url pattern="/admin/locale_settings_edit**" access="hasAnyRole('ROLE_PROVIDER_SYSTEM_ADMIN_EDIT')" />
				<security:intercept-url pattern="/admin/notification_settings_edit**" access="hasAnyRole('ROLE_ADMIN_USER_EDIT','ROLE_MASTER_USER','ROLE_USER')" />
				<security:intercept-url pattern="/admin/user_interface_edit**" access="hasAnyRole('ROLE_ADMIN_SETTINGS_EDIT')" />
				<security:intercept-url pattern="/admin/workgroup_view**" access="hasAnyRole('ROLE_WORKGROUP_VIEW')" />
				<security:intercept-url pattern="/admin/workgroup_edit**" access="hasAnyRole('ROLE_WORKGROUP_EDIT')" />
				<security:intercept-url pattern="/admin/workgroup_associations_view**" access="hasAnyRole('ROLE_WORKGROUP_VIEW')" />
				<security:intercept-url pattern="/admin/workgroup_associations_edit**" access="hasAnyRole('ROLE_WORKGROUP_EDIT')" />
				<security:intercept-url pattern="/admin/workgroup_delete_confirmation**" access="hasAnyRole('ROLE_WORKGROUP_EDIT')" />
				<security:intercept-url pattern="/admin/licence_list**" access="hasAnyRole('ROLE_MASTER_ADMIN','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/licence_edit**" access="hasAnyRole('ROLE_MASTER_ADMIN','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/admin/auditing_list**" access="hasAnyRole('ROLE_AUDITING_EDIT','ROLE_MASTER_ADMIN')" />
				<security:intercept-url pattern="/admin/redirect.form" access="hasAnyRole('ROLE_ADMIN_USER_EDIT','ROLE_ADMIN_USER_VIEW','ROLE_ADMIN_ROLE_VIEW','ROLE_ADMIN_ROLE_EDIT','ROLE_SYSTEM_ADMIN','ROLE_ADMIN_SETTINGS_EDIT','ROLE_ADMIN_TENANT_VIEW','ROLE_ADMIN_TENANT_EDIT','ROLE_PROVIDER_SYSTEM_ADMIN','ROLE_PROVIDER_SYSTEM_ADMIN_EDIT','ROLE_WORKGROUP_VIEW','ROLE_WORKGROUP_EDIT','ROLE_MASTER_USER','ROLE_WORKFLOW_ADMIN_EDIT')" />
				<security:intercept-url pattern="/admin/index.jsp" access="hasAnyRole('ROLE_ADMIN_USER_EDIT','ROLE_ADMIN_USER_VIEW','ROLE_ADMIN_ROLE_VIEW','ROLE_ADMIN_ROLE_EDIT','ROLE_SYSTEM_ADMIN','ROLE_ADMIN_SETTINGS_EDIT','ROLE_ADMIN_TENANT_VIEW','ROLE_ADMIN_TENANT_EDIT','ROLE_PROVIDER_SYSTEM_ADMIN','ROLE_PROVIDER_SYSTEM_ADMIN_EDIT','ROLE_WORKGROUP_VIEW','ROLE_WORKGROUP_EDIT','ROLE_MASTER_USER','ROLE_WORKFLOW_ADMIN_EDIT')" />
				<security:intercept-url pattern="/admin/**" access="hasAnyRole('ROLE_SYSTEM_ADMIN','ROLE_MASTER_USER')" />
				<security:intercept-url pattern="/workflow/workflow_edit**" access="hasAnyRole('ROLE_WORKFLOW_ADMIN_EDIT')" />
				<security:intercept-url pattern="/workflow/workflow_history**" access="hasAnyRole('ROLE_MESSAGE_VIEW_ALL','ROLE_EMBEDDED_CONTENT_VIEW','ROLE_CONTENT_LIBRARY_VIEW','ROLE_LICENCED_COMMUNICATIONS_VIEW','ROLE_LOOKUP_TABLE_VIEW','ROLE_LICENCED_LOOKUP_TABLE_VIEW','ROLE_PROJECT_VIEW','ROLE_WORKFLOW_ADMIN_EDIT')" />
				<security:intercept-url pattern="/workflow/workflow_library**" access="hasAnyRole('ROLE_WORKFLOW_ADMIN_EDIT')" />
				<security:intercept-url pattern="/dictionary/**" access="hasAnyRole('ROLE_DICTIONARY_EDIT')" />
				<security:intercept-url pattern="/dashboards/global**" access="hasAnyRole('ROLE_GLOBAL_DASHBOARD_VIEW')" />
				<security:intercept-url pattern="/pinc/**" access="hasAnyRole('ROLE_PINC_COMPANY_ADMIN', 'ROLE_PINC_COMPANY_READ', 'ROLE_PINC_COMPANY_TEST', 'ROLE_PINC_COMPANY_PRODUCTION', 'ROLE_PINC_COMPANY_AUTHOR')" />
				<security:intercept-url pattern="/stats/**" access="hasAnyRole('ROLE_MESSAGE_VIEW_ALL')" />
				<security:intercept-url pattern="/job_center/**" access="hasAnyRole('ROLE_JOB_CENTER_SETUP')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_application_visibility_edit**" access="hasAnyRole('ROLE_ECATALOGUE_ADMIN')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_application_**" access="hasAnyRole('ROLE_ECATALOG_EDIT')" />
				<security:intercept-url pattern="/link/configurable_links_edit**" access="hasAnyRole('ROLE_ADMIN_SETTINGS_EDIT')" />

				<!-- same as /admin/deserver_list.form in navworkflowmetadata.xml -->
				<security:intercept-url pattern="/bundleDeliveryTestConnection**" access="hasAnyRole('ROLE_ONPREM_SERVER_MANAGEMENT')" />
				<security:intercept-url pattern="/generateBundleDeliverySSHKey**" access="hasAnyRole('ROLE_ONPREM_SERVER_MANAGEMENT')" />

				<security:intercept-url pattern="/touchpoints/touchpoint_variant_template_edit**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/touchpoints/touchpoint_variant_template_modifiers_edit**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/tpadmin/compare_content_section**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/tpadmin/template_variants_edit**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/tpadmin/touchpoint_data_source_configuration_edit**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/tpadmin/touchpoint_sync_content_compare**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/tpadmin/touchpoint_sync_differences_list**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/tpadmin/touchpoint_sync_list**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/tpadmin/touchpoint_sync_message_priority**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/tpadmin/touchpoint_sync_project**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/tpadmin/touchpoint_template_modifier_list**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/content/bulk_upload_confirm**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/content/content_compare**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/content/content_history**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/content/content_import**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/content/content_object_sftp_image_upload**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/content/content_object_touchpoint_assignment**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/content/content_object_zone_priority_edit**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/content/content_power_edit**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/content/dynamic_variant_popup_content**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/content/font_delete**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/content/font_list**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/content/font_upload**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/content/global_content_workflow_edit**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/dashboards/my_dashboard**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/dashboards/my_dashboard_mobile**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/metadata/metadata_form_definition_edit**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/metadata/metadata_form_definition_list**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/metadata/metadata_form_definition_list_detail**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/metadata/metadata_points_of_interest_form**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/metadata/touchpoint_metadata_form_edit**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/metadata/touchpoint_selection_metadata_form_edit**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/search/global_search**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/simulation/simulation_lab**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/simulations/reports_message_sub**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/simulations/reports_message_sub_sub**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/simulations/scenario_customer_report_view**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/simulations/scenario_message_report_view**" access="hasAnyRole('ROLE_USER')" />
				<security:intercept-url pattern="/brand/brand_profile_edit**" access="hasAnyRole('ROLE_BRAND_VIEW')" />
				<security:intercept-url pattern="/brand/brand_profile_list**" access="hasAnyRole('ROLE_BRAND_VIEW')" />
				<security:intercept-url pattern="/brand/brand_profile_list_detail**" access="hasAnyRole('ROLE_BRAND_VIEW')" />
				<security:intercept-url pattern="/content/brand_check**" access="hasAnyRole('ROLE_BRAND_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_bulk_document_upload**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_checkbox_navigation_widget**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_consolidate**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_consolidate_edit**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_content_history**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_content_list**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_content_list_detail**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_export_to_messagepoint**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_metadata_history**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_navigation_widget**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_shared_content_edit**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_shared_content_list**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_shared_content_list_detail**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_task_list**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_touchpoint_upload**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/rationalizer/rationalizer_workflow_assignment_edit**" access="hasAnyRole('ROLE_ECATALOG_VIEW')" />
				<security:intercept-url pattern="/content/tinymce_barcodes**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_checkbox_radio_properties**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_connected_custom_list_properties**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_connected_document_history**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_content_menu**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_custom_list_properties**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_custom_paragraph_styles**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_image_properties**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_connected_image_properties**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_image_upload**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_links**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_mark_editable_properties**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_menu_properties**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_paragraph_properties**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_submit_button_properties**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_table_properties**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/tinymce_text_field_properties**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />
				<security:intercept-url pattern="/content/variant_content_compare**" access="hasAnyRole('ROLE_TOUCHPOINT_SELECTIONS_EDIT', 'ROLE_COMMUNICATIONS_EDIT', 'ROLE_MESSAGE_EDIT', 'ROLE_EMBEDDED_CONTENT_EDIT')" />

			</security:filter-security-metadata-source>
		</property>
	</bean>

	<bean id="authenticationManager" class="org.springframework.security.authentication.ProviderManager">
		<constructor-arg>
			<util:list list-class="java.util.ArrayList" value-type="org.springframework.security.authentication.AuthenticationProvider">
				<ref bean="daoAuthenticationProvider"/>
				<ref bean="preAuthenticatedAuthenticationProvider" />
				<ref bean="anonymousAuthenticationProvider"/>
			</util:list>
		</constructor-arg>
	</bean>

	<bean id="daoAuthenticationProvider" class="com.prinova.messagepoint.security.MessagepointDaoAuthenticationProvider">
		<property name="forcePrincipalAsString"><value>false</value></property>
		<property name="userDetailsService"><ref bean="mpDaoImpl"/></property>
		<property name="passwordEncoder"><ref bean="passwordEncoder"/></property>
		<property name="saltSource"><ref bean="saltSource"/></property>
	</bean>

	<bean id="preAuthenticatedAuthenticationProvider" class="org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationProvider">
		<property name="preAuthenticatedUserDetailsService"><ref bean="mpPreAuthUserDetailImpl"/></property>
	</bean>

	<bean id="anonymousAuthenticationProvider" class="org.springframework.security.authentication.AnonymousAuthenticationProvider">
		<constructor-arg value="anonymousProviderKeyForUMH"/>
	</bean>

	<!-- *******.2 -->

	<bean id="acegiHandler" class="org.springframework.ws.soap.security.wss4j2.callback.SpringSecurityPasswordValidationCallbackHandler">
		<property name="userDetailsService" ref="mpDaoImpl"/>
	</bean>

	<bean id="springSecurityHandler" class="org.springframework.ws.soap.security.wss4j2.callback.SpringSecurityPasswordValidationCallbackHandler">
		<property name="userDetailsService" ref="mpDaoImpl"/>
	</bean>

	<bean id="passwordEncoder" class="org.springframework.security.authentication.encoding.ShaPasswordEncoder">
		<constructor-arg><value>256</value></constructor-arg>
	</bean>

	<bean id="sha1Encoder" class="org.springframework.security.authentication.encoding.ShaPasswordEncoder">
		<constructor-arg><value>1</value></constructor-arg>
	</bean>


	<bean id="saltSource" class="org.springframework.security.authentication.dao.ReflectionSaltSource">
		<property name="userPropertyToUse" value="salt"/>
	</bean>

	<bean id="passwordValidator" class="com.prinova.messagepoint.platform.security.PasswordValidatorImpl" />

</beans>
