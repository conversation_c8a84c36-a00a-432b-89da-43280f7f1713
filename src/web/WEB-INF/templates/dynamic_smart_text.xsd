<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified">
	<xs:element name="MpBulkUploadDefinition">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="DynamicSmartTextVariants" />
			</xs:sequence>
			<xs:attribute name="version" use="required" type="xs:language" />
			<xs:attribute name="build" type="xs:string" />
			<xs:attribute name="User" type="xs:string" />
			<xs:attribute name="RequestDate" type="xs:string" />
			<xs:attribute name="name" type="xs:string" />
			<xs:attribute name="guid" type="xs:string" />
			<xs:attribute name="instanceGuid" type="xs:string" />
		</xs:complexType>
    </xs:element>
	<xs:element name="DynamicSmartTextVariants">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="Variant" minOccurs="0" maxOccurs="unbounded">
                    <xs:complexType>
                        <xs:sequence>
							<xs:element name="Name" type="xs:string" />
							<xs:element name="SelectionData" type="xs:string" />
							<xs:element name="Content_01">
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_02" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_03" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_04" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_05" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_06" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_07" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_08" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_09" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_10" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_11" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_12" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_13" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_14" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_15" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_16" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_17" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_18" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_19" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_20" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_21" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_22" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_23" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Content_24" minOccurs="0" >
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="id" type="xs:string" />
											<xs:attribute name="language" type="xs:string" />
											<xs:attribute name="locale" type="xs:string" />
											<xs:attribute name="sameasparent" type="xs:boolean" />
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
						<xs:attribute name="guid" type="xs:string" />
						<xs:attribute name="id" type="xs:string" />
						<xs:attribute name="refid" type="xs:string" />
					</xs:complexType>

                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>