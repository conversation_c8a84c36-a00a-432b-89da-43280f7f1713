<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified">
	<xs:element name="MpBulkUploadDefinition">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="TPVariantMetadataFormItems" />
			</xs:sequence>
			<xs:attribute name="version" use="required" type="xs:language" />
			<xs:attribute name="build" type="xs:string" />
			<xs:attribute name="User" type="xs:string" />
			<xs:attribute name="RequestDate" type="xs:string" />
			<xs:attribute name="name" type="xs:string" />
			<xs:attribute name="guid" type="xs:string" />
		</xs:complexType>
    </xs:element>
	<xs:element name="TPVariantMetadataFormItems">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="Variant" minOccurs="0" maxOccurs="unbounded">
                    <xs:complexType>
                        <xs:sequence>
							<xs:element name="Content_01">
  							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
							</xs:element>
							<xs:element name="Content_02" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_03" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_04" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_05" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_06" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_07" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_08" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_09" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_10" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_11" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_12" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_13" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_14" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_15" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_16" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_17" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_18" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_19" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_20" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_21" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_22" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_23" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_24" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_25" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_26" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_27" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_28" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_29" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_30" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_31" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_32" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_33" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_34" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_35" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_36" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_37" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_38" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_39" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_40" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_41" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_42" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_43" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_44" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_45" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_46" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_47" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_48" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_49" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
						 	</xs:element>
							<xs:element name="Content_50" minOccurs="0" >
							  <xs:complexType>
								<xs:simpleContent>
								  <xs:extension base="xs:string">
									  <xs:attribute name="id" type="xs:string" />
								  </xs:extension>
								</xs:simpleContent>
							  </xs:complexType>
							 </xs:element>
						</xs:sequence>
						<xs:attribute name="guid" type="xs:string" />
						<xs:attribute name="id" type="xs:string" />
						<xs:attribute name="refid" type="xs:string" />
						<xs:attribute name="name" type="xs:string" />
						<xs:attribute name="inherited" type="xs:string" />
					</xs:complexType>
				</xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>