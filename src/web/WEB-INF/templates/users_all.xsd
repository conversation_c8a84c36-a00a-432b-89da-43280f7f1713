<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified">
	<xs:element name="MpBulkUploadDefinition">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="Users" />
			</xs:sequence>
			<xs:attribute name="version" use="required" type="xs:language" />
			<xs:attribute name="build" type="xs:string" />
			<xs:attribute name="requestedby" type="xs:string" />
			<xs:attribute name="requestdate" type="xs:string" />
			<xs:attribute name="name" type="xs:string" />
			<xs:attribute name="guid" type="xs:string" />
		</xs:complexType>
    </xs:element>
	<xs:element name="Users">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="User" minOccurs="0" maxOccurs="unbounded">
                    <xs:complexType>
                        <xs:sequence>
							<xs:element name="GUID" type="xs:string" />
							<xs:element name="ID" type="xs:string" />
							<xs:element name="FirstName" type="xs:string" />
							<xs:element name="LastName" type="xs:string" />
							<xs:element name="UserName" type="xs:string" />
							<xs:element name="Email" type="xs:string" />
							<xs:element name="IDProvider" type="xs:string" />
							<xs:element name="EmailNotificationReceiving" type="xs:string" />
							<xs:element name="CurrentStatus" type="xs:string" />
                            <xs:element name="Action" type="xs:string" />
							<xs:element name="Domain_01" minOccurs="0">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="Name" type="xs:string" />
										<xs:element name="GUID" type="xs:string" />
										<xs:element name="Instance_01">
										  <xs:complexType>
											<xs:simpleContent>
											  <xs:extension base="xs:string">
												<xs:attribute name="roleid" type="xs:string" />
												<xs:attribute name="role" type="xs:string" />
											  </xs:extension>
											</xs:simpleContent>
										  </xs:complexType>
										</xs:element>
										<xs:element name="Instance_02" minOccurs="0">
										  <xs:complexType>
											<xs:simpleContent>
											  <xs:extension base="xs:string">
												<xs:attribute name="roleid" type="xs:string" />
												<xs:attribute name="role" type="xs:string" />
											  </xs:extension>
											</xs:simpleContent>
										  </xs:complexType>
										</xs:element>
										<xs:element name="Instance_03" minOccurs="0" >
										  <xs:complexType>
											<xs:simpleContent>
											  <xs:extension base="xs:string">
												<xs:attribute name="roleid" type="xs:string" />
												<xs:attribute name="role" type="xs:string" />
											  </xs:extension>
											</xs:simpleContent>
										  </xs:complexType>
										</xs:element>
										<xs:element name="Instance_04" minOccurs="0" >
										  <xs:complexType>
											<xs:simpleContent>
											  <xs:extension base="xs:string">
												<xs:attribute name="roleid" type="xs:string" />
												<xs:attribute name="role" type="xs:string" />
											  </xs:extension>
											</xs:simpleContent>
										  </xs:complexType>
										</xs:element>
										<xs:element name="Instance_05" minOccurs="0" >
										  <xs:complexType>
											<xs:simpleContent>
											  <xs:extension base="xs:string">
												<xs:attribute name="roleid" type="xs:string" />
												<xs:attribute name="role" type="xs:string" />
											  </xs:extension>
											</xs:simpleContent>
										  </xs:complexType>
										</xs:element>
										<xs:element name="Instance_06" minOccurs="0" >
										  <xs:complexType>
											<xs:simpleContent>
											  <xs:extension base="xs:string">
												<xs:attribute name="roleid" type="xs:string" />
												<xs:attribute name="role" type="xs:string" />
											  </xs:extension>
											</xs:simpleContent>
										  </xs:complexType>
										</xs:element>
										<xs:element name="Instance_07" minOccurs="0" >
										  <xs:complexType>
											<xs:simpleContent>
											  <xs:extension base="xs:string">
												<xs:attribute name="roleid" type="xs:string" />
												<xs:attribute name="role" type="xs:string" />
											  </xs:extension>
											</xs:simpleContent>
										  </xs:complexType>
										</xs:element>
										<xs:element name="Instance_08" minOccurs="0" >
										  <xs:complexType>
											<xs:simpleContent>
											  <xs:extension base="xs:string">
												<xs:attribute name="roleid" type="xs:string" />
												<xs:attribute name="role" type="xs:string" />
											  </xs:extension>
											</xs:simpleContent>
										  </xs:complexType>
										</xs:element>
										<xs:element name="Instance_09" minOccurs="0" >
										  <xs:complexType>
											<xs:simpleContent>
											  <xs:extension base="xs:string">
												<xs:attribute name="roleid" type="xs:string" />
												<xs:attribute name="role" type="xs:string" />
											  </xs:extension>
											</xs:simpleContent>
										  </xs:complexType>
										</xs:element>
										<xs:element name="Instance_10" minOccurs="0" >
										  <xs:complexType>
											<xs:simpleContent>
											  <xs:extension base="xs:string">
												<xs:attribute name="roleid" type="xs:string" />
												<xs:attribute name="role" type="xs:string" />
											  </xs:extension>
											</xs:simpleContent>
										  </xs:complexType>
										</xs:element>
			                            <xs:element name="Instance_11" minOccurs="0" >
			                                <xs:complexType>
			                                    <xs:simpleContent>
			                                        <xs:extension base="xs:string">
			                                            <xs:attribute name="roleid" type="xs:string" />
			                                            <xs:attribute name="role" type="xs:string" />
			                                        </xs:extension>
			                                    </xs:simpleContent>
			                                </xs:complexType>
			                            </xs:element>
			                            <xs:element name="Instance_12" minOccurs="0" >
			                                <xs:complexType>
			                                    <xs:simpleContent>
			                                        <xs:extension base="xs:string">
			                                            <xs:attribute name="roleid" type="xs:string" />
			                                            <xs:attribute name="role" type="xs:string" />
			                                        </xs:extension>
			                                    </xs:simpleContent>
			                                </xs:complexType>
			                            </xs:element>
			                            <xs:element name="Instance_13" minOccurs="0" >
			                                <xs:complexType>
			                                    <xs:simpleContent>
			                                        <xs:extension base="xs:string">
			                                            <xs:attribute name="roleid" type="xs:string" />
			                                            <xs:attribute name="role" type="xs:string" />
			                                        </xs:extension>
			                                    </xs:simpleContent>
			                                </xs:complexType>
			                            </xs:element>
			                            <xs:element name="Instance_14" minOccurs="0" >
			                                <xs:complexType>
			                                    <xs:simpleContent>
			                                        <xs:extension base="xs:string">
			                                            <xs:attribute name="roleid" type="xs:string" />
			                                            <xs:attribute name="role" type="xs:string" />
			                                        </xs:extension>
			                                    </xs:simpleContent>
			                                </xs:complexType>
			                            </xs:element>
			                            <xs:element name="Instance_15" minOccurs="0" >
			                                <xs:complexType>
			                                    <xs:simpleContent>
			                                        <xs:extension base="xs:string">
			                                            <xs:attribute name="roleid" type="xs:string" />
			                                            <xs:attribute name="role" type="xs:string" />
			                                        </xs:extension>
			                                    </xs:simpleContent>
			                                </xs:complexType>
			                            </xs:element>
			                            <xs:element name="Instance_16" minOccurs="0" >
			                                <xs:complexType>
			                                    <xs:simpleContent>
			                                        <xs:extension base="xs:string">
			                                            <xs:attribute name="roleid" type="xs:string" />
			                                            <xs:attribute name="role" type="xs:string" />
			                                        </xs:extension>
			                                    </xs:simpleContent>
			                                </xs:complexType>
			                            </xs:element>
			                            <xs:element name="Instance_17" minOccurs="0" >
			                                <xs:complexType>
			                                    <xs:simpleContent>
			                                        <xs:extension base="xs:string">
			                                            <xs:attribute name="roleid" type="xs:string" />
			                                            <xs:attribute name="role" type="xs:string" />
			                                        </xs:extension>
			                                    </xs:simpleContent>
			                                </xs:complexType>
			                            </xs:element>
			                            <xs:element name="Instance_18" minOccurs="0" >
			                                <xs:complexType>
			                                    <xs:simpleContent>
			                                        <xs:extension base="xs:string">
			                                            <xs:attribute name="roleid" type="xs:string" />
			                                            <xs:attribute name="role" type="xs:string" />
			                                        </xs:extension>
			                                    </xs:simpleContent>
			                                </xs:complexType>
			                            </xs:element>
			                            <xs:element name="Instance_19" minOccurs="0" >
			                                <xs:complexType>
			                                    <xs:simpleContent>
			                                        <xs:extension base="xs:string">
			                                            <xs:attribute name="roleid" type="xs:string" />
			                                            <xs:attribute name="role" type="xs:string" />
			                                        </xs:extension>
			                                    </xs:simpleContent>
			                                </xs:complexType>
			                            </xs:element>
			                            <xs:element name="Instance_20" minOccurs="0" >
			                                <xs:complexType>
			                                    <xs:simpleContent>
			                                        <xs:extension base="xs:string">
			                                            <xs:attribute name="roleid" type="xs:string" />
			                                            <xs:attribute name="role" type="xs:string" />
			                                        </xs:extension>
			                                    </xs:simpleContent>
			                                </xs:complexType>
			                            </xs:element>
									</xs:sequence>
						        </xs:complexType>
						    </xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>