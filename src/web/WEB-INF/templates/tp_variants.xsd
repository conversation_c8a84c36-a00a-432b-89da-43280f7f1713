<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified">
	<xs:element name="MpBulkUploadDefinition">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="TouchpointVariants" />
			</xs:sequence>
			<xs:attribute name="version" use="required" type="xs:language" />
			<xs:attribute name="build" type="xs:string" />
			<xs:attribute name="User" type="xs:string" />
			<xs:attribute name="RequestDate" type="xs:string" />
			<xs:attribute name="name" type="xs:string" />
			<xs:attribute name="guid" type="xs:string" />
		</xs:complexType>
    </xs:element>
	<xs:element name="TouchpointVariants">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="Variant" minOccurs="0" maxOccurs="unbounded">
                    <xs:complexType>
                        <xs:sequence>
							<xs:element name="Name" type="xs:string" />
							<xs:element name="SelectionData" type="xs:string" />
						</xs:sequence>
						<xs:attribute name="guid" type="xs:string" />
						<xs:attribute name="id" type="xs:string" />
						<xs:attribute name="refid" type="xs:string" />
					</xs:complexType>
				</xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>