action.button.label.compare.report=
action.button.label.notification.settings=
action.button.label.sync=
action.button.label.variants=
background.task.sub.type.calculate.hash=
background.task.sub.type.test.results.package=
background_task.sub.type.db.delete.schema=
background_task.sub.type.dynamic.message.import=
background_task.sub.type.export.rationalizer.to.touchpoint=
background_task.sub.type.image.variants.export=
background_task.sub.type.image.variants.import=
background_task.sub.type.job.performance.report.export=
background_task.sub.type.message.template.export=
background_task.sub.type.message.template.import=
background_task.sub.type.message.variants.export=
background_task.sub.type.message.variants.import=
background_task.sub.type.messagepoint.delete.brand=
background_task.sub.type.messagepoint.recompute.brand=
background_task.sub.type.messages.on.approval.export=
background_task.sub.type.push.content.to.elastic=
background_task.sub.type.rationalizer.app.clone=
background_task.sub.type.rationalizer.app.delete=
background_task.sub.type.rationalizer.dashboard.report=
background_task.sub.type.rationalizer.delete.brand=
background_task.sub.type.rationalizer.document.report=
background_task.sub.type.rationalizer.ingest.and.upload.xml=
background_task.sub.type.rationalizer.metadata.report=
background_task.sub.type.rationalizer.migrate.data=
background_task.sub.type.rationalizer.recompute.brand=
background_task.sub.type.rationalizer.reindex=
background_task.sub.type.rationalizer.smilarity.report=
background_task.sub.type.rationalizer.upload.xml=
background_task.sub.type.resave.content=
background_task.sub.type.revert.content.power.edit=
background_task.sub.type.roles.export=
background_task.sub.type.save.content.power.edit=
background_task.sub.type.send.to.qa.module=
background_task.sub.type.sftprepo.contentlib.import=
background_task.sub.type.smart.canvas.variants.export=
background_task.sub.type.smart.text.variants.export=
background_task.sub.type.smart.text.variants.import=
background_task.sub.type.toouchpoint.variants.export=
background_task.sub.type.toouchpoint.variants.import=
background_task.sub.type.tp.clone=
background_task.sub.type.tp.container.import=
background_task.sub.type.tp.delete=
background_task.sub.type.tp.export=
background_task.sub.type.tp.hide.commit=
background_task.sub.type.tp.hide.update=
background_task.sub.type.tp.import.to.update=
background_task.sub.type.tp.import=
background_task.sub.type.tp.project=
background_task.sub.type.tp.sync.commit=
background_task.sub.type.tp.sync.msg.priority.commit=
background_task.sub.type.tp.sync.msg.priority.update=
background_task.sub.type.tp.sync.report=
background_task.sub.type.tp.sync.update=
background_task.sub.type.upload.xml.tags=
background_task.sub.type.users.export=
background_task.sub.type.users.import=
background_task.sub.type.users.update.export=
background_task.sub.type.variant.metadata.export=
background_task.sub.type.variant.metadata.import=
blue.relay.response.status.ok=
blue.relay.task.completed=
blue.relay.task.starting=
blue.relay.test.scenario.no.result=
client_message.zone.template.title=
client_messages.alert.danger=
client_messages.alert.info=
client_messages.alert.success=
client_messages.alert.warning=
client_messages.consolidate_frame_title=
client_messages.content.no_production_content=
client_messages.content_editor.EMPTY=
client_messages.content_editor.alignment_abv=
client_messages.content_editor.alignment_center=
client_messages.content_editor.alignment_full=
client_messages.content_editor.alignment_left=
client_messages.content_editor.alignment_right=
client_messages.content_editor.alt_text=
client_messages.content_editor.apply=
client_messages.content_editor.attributes=
client_messages.content_editor.bold=
client_messages.content_editor.brand=
client_messages.content_editor.canvas_panel=
client_messages.content_editor.canvas_size=
client_messages.content_editor.centimeter_short=
client_messages.content_editor.clipboard=
client_messages.content_editor.clipboard_filter.any=
client_messages.content_editor.clipboard_filter.forms=
client_messages.content_editor.clipboard_filter.freeform=
client_messages.content_editor.clipboard_filter.lists=
client_messages.content_editor.clipboard_filter.tables=
client_messages.content_editor.color=
client_messages.content_editor.confirm=
client_messages.content_editor.confirm_remove_content=
client_messages.content_editor.content_duplicates_and_similarities=
client_messages.content_editor.content_intelligence=
client_messages.content_editor.content_language=
client_messages.content_editor.content_menu=
client_messages.content_editor.custom_value=
client_messages.content_editor.decrease_max_items=
client_messages.content_editor.default_content_compare=
client_messages.content_editor.default_value=
client_messages.content_editor.desired_range=
client_messages.content_editor.dropdown=
client_messages.content_editor.duplicates_and_similarities=
client_messages.content_editor.edit_image_properties=
client_messages.content_editor.edit_list_properties=
client_messages.content_editor.edit_paragraph_properties=
client_messages.content_editor.editable_content_properties=
client_messages.content_editor.elements=
client_messages.content_editor.error_min_canvas_size=
client_messages.content_editor.error_min_trim_size=
client_messages.content_editor.flesch_kincaid_grade_level=
client_messages.content_editor.flesch_readability=
client_messages.content_editor.good=
client_messages.content_editor.hide_toolbar=
client_messages.content_editor.history=
client_messages.content_editor.image=
client_messages.content_editor.image_alt_text=
client_messages.content_editor.image_properties=
client_messages.content_editor.inches_short=
client_messages.content_editor.indent=
client_messages.content_editor.inline_content_only=
client_messages.content_editor.insert_alt_text=
client_messages.content_editor.insert_anchor=
client_messages.content_editor.insert_bookmark=
client_messages.content_editor.insert_edit_alt_text=
client_messages.content_editor.insert_edit_anchor=
client_messages.content_editor.insert_edit_barcode=
client_messages.content_editor.insert_edit_bookmark=
client_messages.content_editor.insert_edit_link=
client_messages.content_editor.insert_link=
client_messages.content_editor.insert_placeholders=
client_messages.content_editor.invalid_table_relative_widths=
client_messages.content_editor.italic=
client_messages.content_editor.line_spacing_abv=
client_messages.content_editor.list=
client_messages.content_editor.list_styles=
client_messages.content_editor.local_content=
client_messages.content_editor.local_image_library=
client_messages.content_editor.local_smart_canvas=
client_messages.content_editor.lookup_expressions=
client_messages.content_editor.menu=
client_messages.content_editor.menu_properties=
client_messages.content_editor.mixed=
client_messages.content_editor.my_items=
client_messages.content_editor.negative=
client_messages.content_editor.neutral=
client_messages.content_editor.no_clipboard_items_to_display=
client_messages.content_editor.no_menu_items_to_display=
client_messages.content_editor.on_flow=
client_messages.content_editor.outdent=
client_messages.content_editor.pin_menu=
client_messages.content_editor.placeholders=
client_messages.content_editor.point_size_abv=
client_messages.content_editor.poor=
client_messages.content_editor.positive=
client_messages.content_editor.possible_violations_found=
client_messages.content_editor.readability=
client_messages.content_editor.readability_analysis=
client_messages.content_editor.referencing_local_image_library=
client_messages.content_editor.remove_link=
client_messages.content_editor.render_content=
client_messages.content_editor.repo_last_sync_date=
client_messages.content_editor.repo_last_update_date=
client_messages.content_editor.reset=
client_messages.content_editor.reset_workspace=
client_messages.content_editor.rotate_left=
client_messages.content_editor.rotate_right=
client_messages.content_editor.rulers=
client_messages.content_editor.save_content_changes=
client_messages.content_editor.save_required=
client_messages.content_editor.select_an_option=
client_messages.content_editor.sentiment=
client_messages.content_editor.sentiment_analysis=
client_messages.content_editor.sentiment_score=
client_messages.content_editor.shared_items=
client_messages.content_editor.show_variable_sample_values=
client_messages.content_editor.smart_canvas=
client_messages.content_editor.special_character=
client_messages.content_editor.submit_button=
client_messages.content_editor.submit_button_properties=
client_messages.content_editor.system=
client_messages.content_editor.tab_order=
client_messages.content_editor.table_spacing=
client_messages.content_editor.table_width=
client_messages.content_editor.tables=
client_messages.content_editor.tag_panel=
client_messages.content_editor.tags.bulleted_list=
client_messages.content_editor.tags.cell=
client_messages.content_editor.tags.checkbox=
client_messages.content_editor.tags.freeform_container=
client_messages.content_editor.tags.list_item=
client_messages.content_editor.tags.local_smart_text=
client_messages.content_editor.tags.menu=
client_messages.content_editor.tags.ordered_list=
client_messages.content_editor.tags.paragraph=
client_messages.content_editor.tags.radio=
client_messages.content_editor.tags.row=
client_messages.content_editor.tags.smart_text=
client_messages.content_editor.tags.submit_button=
client_messages.content_editor.tags.table=
client_messages.content_editor.tags.text_field=
client_messages.content_editor.targeting=
client_messages.content_editor.text=
client_messages.content_editor.text_date_field=
client_messages.content_editor.text_date_field_properties=
client_messages.content_editor.text_field_value=
client_messages.content_editor.toggle_panel_size=
client_messages.content_editor.toggle_table_grid_drag=
client_messages.content_editor.tooltips=
client_messages.content_editor.underline=
client_messages.content_editor.variants=
client_messages.content_editor.view_fullscreen=
client_messages.file_extension_not_supported=
client_messages.image.upload.current.image=
client_messages.image.upload.drag.and.drop.browse=
client_messages.image.upload.drag.and.drop.title=
client_messages.items_selected=
client_messages.label.toggle_columns=
client_messages.label.toggle_details=
client_messages.label.toggle_image_list=
client_messages.label.toggle_list_display=
client_messages.onpremise.component.downloads=
client_messages.onpremise.components=
client_messages.page.text.dashboard.stats.error.message=
client_messages.page.text.dashboard.stats.inProcess.message=
client_messages.page.text.dashboard.stats.inProcess.noProgressPercent.message=
client_messages.page.text.dashboard.stats.inProcess.pending.message=
client_messages.page.text.dashboard.stats.lastUpdated.message=
client_messages.page.text.rationalizer.dashboard.stats.error.message=
client_messages.page.text.rationalizer.dashboard.stats.inProcess.message=
client_messages.page.text.rationalizer.dashboard.stats.inProcess.noProgressPercent.message=
client_messages.page.text.rationalizer.dashboard.stats.inProcess.pending.message=
client_messages.page.text.rationalizer.dashboard.stats.lastUpdated.message=
client_messages.pinc.abort=
client_messages.pinc.abort_not_permitted=
client_messages.pinc.access_key_id=
client_messages.pinc.access_key_required=
client_messages.pinc.activate=
client_messages.pinc.activate_confirmation=
client_messages.pinc.active=
client_messages.pinc.add=
client_messages.pinc.add_application=
client_messages.pinc.add_child_step=
client_messages.pinc.add_step=
client_messages.pinc.all=
client_messages.pinc.and=
client_messages.pinc.any=
client_messages.pinc.api_token=
client_messages.pinc.api_tokens=
client_messages.pinc.app_ver_archive_not_permitted=
client_messages.pinc.application=
client_messages.pinc.application_properties=
client_messages.pinc.application_version=
client_messages.pinc.application_version_required=
client_messages.pinc.archive=
client_messages.pinc.archive_confirmation=
client_messages.pinc.archive_file=
client_messages.pinc.archived=
client_messages.pinc.attach_result=
client_messages.pinc.authentication_http_method=
client_messages.pinc.authentication_request_body=
client_messages.pinc.authentication_token_attribute=
client_messages.pinc.authentication_type=
client_messages.pinc.authentication_url=
client_messages.pinc.back_to_list=
client_messages.pinc.bcc=
client_messages.pinc.body=
client_messages.pinc.body_required=
client_messages.pinc.bucket=
client_messages.pinc.bucket_required=
client_messages.pinc.bundle_required=
client_messages.pinc.cancel=
client_messages.pinc.cc=
client_messages.pinc.channel=
client_messages.pinc.channel_required=
client_messages.pinc.channel_type_label_ftp=
client_messages.pinc.channel_type_label_gmc=
client_messages.pinc.channel_type_label_native=
client_messages.pinc.child_step=
client_messages.pinc.choose_file=
client_messages.pinc.clone=
client_messages.pinc.close=
client_messages.pinc.code=
client_messages.pinc.command_line_arguments=
client_messages.pinc.confirm=
client_messages.pinc.contains=
client_messages.pinc.continue_on_failure=
client_messages.pinc.copy_auth_token=
client_messages.pinc.created=
client_messages.pinc.current_production_version=
client_messages.pinc.deactivate=
client_messages.pinc.deactivate_confirmation=
client_messages.pinc.default=
client_messages.pinc.delete=
client_messages.pinc.delete_app_confirmation=
client_messages.pinc.delete_confirmation=
client_messages.pinc.delete_step_confirmation=
client_messages.pinc.description=
client_messages.pinc.description_required=
client_messages.pinc.details=
client_messages.pinc.disable_accessibility=
client_messages.pinc.docmill_compatible=
client_messages.pinc.download=
client_messages.pinc.driver_file=
client_messages.pinc.edit=
client_messages.pinc.encryption_key=
client_messages.pinc.encryption_keys=
client_messages.pinc.encryption_type=
client_messages.pinc.encryption_type_none=
client_messages.pinc.encryption_type_pgp=
client_messages.pinc.end_time=
client_messages.pinc.engine=
client_messages.pinc.engine_options=
client_messages.pinc.engine_options_required=
client_messages.pinc.engine_required=
client_messages.pinc.engine_version=
client_messages.pinc.engine_version_required=
client_messages.pinc.equals=
client_messages.pinc.executable=
client_messages.pinc.executable_required=
client_messages.pinc.executable_version=
client_messages.pinc.executable_version_required=
client_messages.pinc.executables=
client_messages.pinc.expiration_date=
client_messages.pinc.expired=
client_messages.pinc.failure_level=
client_messages.pinc.failure_level_error=
client_messages.pinc.failure_level_warning=
client_messages.pinc.file=
client_messages.pinc.file_contains_production_data=
client_messages.pinc.file_delete_not_permitted=
client_messages.pinc.file_encrypted_with_key=
client_messages.pinc.file_required=
client_messages.pinc.files=
client_messages.pinc.filter=
client_messages.pinc.fingerprint=
client_messages.pinc.for=
client_messages.pinc.frozen=
client_messages.pinc.host_key_md5=
client_messages.pinc.host_required=
client_messages.pinc.id=
client_messages.pinc.ignore_paths=
client_messages.pinc.in_production=
client_messages.pinc.instance=
client_messages.pinc.job=
client_messages.pinc.job_archive=
client_messages.pinc.job_result=
client_messages.pinc.job_status_aborted=
client_messages.pinc.job_status_completed=
client_messages.pinc.job_status_failed=
client_messages.pinc.job_status_initialized=
client_messages.pinc.job_status_processing=
client_messages.pinc.jobs=
client_messages.pinc.jobs_abort_confirmation=
client_messages.pinc.jobs_restart_confirmation=
client_messages.pinc.last_updated=
client_messages.pinc.last_updated_by=
client_messages.pinc.logging_level=
client_messages.pinc.logging_level_critical=
client_messages.pinc.logging_level_debug=
client_messages.pinc.logging_level_error=
client_messages.pinc.logging_level_info=
client_messages.pinc.logging_level_warning=
client_messages.pinc.message=
client_messages.pinc.messagepoint_instance=
client_messages.pinc.messagepoint_job_id=
client_messages.pinc.name=
client_messages.pinc.name_required=
client_messages.pinc.no=
client_messages.pinc.none=
client_messages.pinc.not_active=
client_messages.pinc.not_configured=
client_messages.pinc.not_in_production=
client_messages.pinc.of=
client_messages.pinc.other_parents=
client_messages.pinc.password=
client_messages.pinc.password_or_ssh_key_required=
client_messages.pinc.pgp_key_as_file=
client_messages.pinc.pgp_public_key=
client_messages.pinc.pgp_public_key_required=
client_messages.pinc.port=
client_messages.pinc.post=
client_messages.pinc.production=
client_messages.pinc.production_job_managers=
client_messages.pinc.production_test=
client_messages.pinc.production_version=
client_messages.pinc.promote=
client_messages.pinc.promote_confirmation=
client_messages.pinc.promoted=
client_messages.pinc.public_key=
client_messages.pinc.put=
client_messages.pinc.queue_size=
client_messages.pinc.reference_files=
client_messages.pinc.region=
client_messages.pinc.region_required=
client_messages.pinc.request_body_required=
client_messages.pinc.restart=
client_messages.pinc.restart_not_permitted=
client_messages.pinc.result_files=
client_messages.pinc.result_filters=
client_messages.pinc.revoke=
client_messages.pinc.revoke_confirmation=
client_messages.pinc.revoke_date=
client_messages.pinc.revoked=
client_messages.pinc.root_folder=
client_messages.pinc.save=
client_messages.pinc.scopes=
client_messages.pinc.scopes_required=
client_messages.pinc.secret_access_key=
client_messages.pinc.secret_key_required=
client_messages.pinc.select_one_or_more=
client_messages.pinc.select_step_type=
client_messages.pinc.server=
client_messages.pinc.splitting_batch_size=
client_messages.pinc.splitting_type=
client_messages.pinc.splitting_type_page=
client_messages.pinc.splitting_type_recipient=
client_messages.pinc.ssh_key=
client_messages.pinc.start_time=
client_messages.pinc.starts=
client_messages.pinc.status=
client_messages.pinc.step=
client_messages.pinc.step_type_delivery_s3=
client_messages.pinc.step_type_delivery_sftp=
client_messages.pinc.step_type_email=
client_messages.pinc.step_type_engine_de=
client_messages.pinc.step_type_engine_ede=
client_messages.pinc.step_type_engine_inspire=
client_messages.pinc.step_type_engine_mpcomp=
client_messages.pinc.step_type_engine_qepost=
client_messages.pinc.step_type_executable=
client_messages.pinc.step_type_get_bundle=
client_messages.pinc.step_type_webhook=
client_messages.pinc.steps=
client_messages.pinc.subject=
client_messages.pinc.subject_required=
client_messages.pinc.submission=
client_messages.pinc.synchronous=
client_messages.pinc.test=
client_messages.pinc.test_job_managers=
client_messages.pinc.to=
client_messages.pinc.to_required=
client_messages.pinc.token=
client_messages.pinc.tokens=
client_messages.pinc.touchpoint=
client_messages.pinc.type=
client_messages.pinc.user=
client_messages.pinc.user_data=
client_messages.pinc.username_required=
client_messages.pinc.version=
client_messages.pinc.version_required=
client_messages.pinc.versions=
client_messages.pinc.webhook_http_method=
client_messages.pinc.webhook_url=
client_messages.pinc.webhook_url_required=
client_messages.pinc.with=
client_messages.pinc.yes=
client_messages.rationalizer.dashboard.compare.all.to.all=
client_messages.rationalizer.dashboard.compare.all.to.filtered=
client_messages.rationalizer.dashboard.compare.filtered.to.all=
client_messages.rationalizer.dashboard.compare.filtered.to.filtered=
client_messages.response=
client_messages.shared_content.text=
client_messages.text.DAT_PUB_AND_ZIP=
client_messages.text.DAT_WFD_AND_ZIP=
client_messages.text.TTF_and_EOT=
client_messages.text._value=
client_messages.text.action=
client_messages.text.activated_on=
client_messages.text.active_copy=
client_messages.text.active_run_type=
client_messages.text.active_tasks=
client_messages.text.add_items_base16_entities=
client_messages.text.add_items_delimiter_separated_values=
client_messages.text.add_menu_item=
client_messages.text.add_to_my_clipboard=
client_messages.text.add_to_shared_clipboard=
client_messages.text.added=
client_messages.text.alt_text_value_required=
client_messages.text.application=
client_messages.text.approved=
client_messages.text.assignee=
client_messages.text.awaiting_approval=
client_messages.text.background_task=
client_messages.text.background_tasks=
client_messages.text.background_tasks_in_progress=
client_messages.text.background_tasks_notifications=
client_messages.text.barcode_value_required=
client_messages.text.barcode_width_height_required=
client_messages.text.brand_description=
client_messages.text.brand_violation=
client_messages.text.bullet_spacing=
client_messages.text.change=
client_messages.text.changed=
client_messages.text.changes=
client_messages.text.clear_selected_items=
client_messages.text.click_to_download_data_file=
client_messages.text.click_to_download_pdf=
client_messages.text.client_bundle=
client_messages.text.collection=
client_messages.text.collections=
client_messages.text.colors=
client_messages.text.communication.type=
client_messages.text.compare=
client_messages.text.compare_from=
client_messages.text.compare_report=
client_messages.text.compare_to=
client_messages.text.comparison_content=
client_messages.text.completed=
client_messages.text.compound_spaces_between=
client_messages.text.compound_spaces_lead=
client_messages.text.compress=
client_messages.text.confirm=
client_messages.text.confirm_clear_touchpoint_metadata=
client_messages.text.confirm_remove_layout=
client_messages.text.connected=
client_messages.text.connected_workflow=
client_messages.text.connector=
client_messages.text.container_dimensions=
client_messages.text.content_compare=
client_messages.text.content_good_readability=
client_messages.text.content_negative_sentiment=
client_messages.text.content_neutral_sentiment=
client_messages.text.content_poor_readability=
client_messages.text.content_positive_sentiment=
client_messages.text.content_status=
client_messages.text.continue_numbering=
client_messages.text.create_sub_project=
client_messages.text.create_touchpoint=
client_messages.text.created_by=
client_messages.text.created_on=
client_messages.text.current=
client_messages.text.custom=
client_messages.text.dashboard_brand_number=
client_messages.text.dashboard_compare_title=
client_messages.text.dashboard_duplicates_number_gt_zero=
client_messages.text.dashboard_duplicates_number_eq_zero=
client_messages.text.dashboard_duplicates_report=
client_messages.text.dashboard_metadata_number=
client_messages.text.global_dashboard_similarities_number=
client_messages.text.rationalizer_dashboard_similarities_number=
client_messages.text.dashboard_similarity_report=
client_messages.text.data=
client_messages.text.date_and_description=
client_messages.text.date_range=
client_messages.text.default_node=
client_messages.text.delivery_event_requester=
client_messages.text.dependencies=
client_messages.text.description=
client_messages.text.diagnostic_bundle=
client_messages.text.dictionary=
client_messages.text.disqualify_on_no_=
client_messages.text.disqualify_on_no_value=
client_messages.text.disqualify_smart_text=
client_messages.text.documentLink=
client_messages.text.document_name=
client_messages.text.domain.licence.type=
client_messages.text.double_click_to_add=
client_messages.text.download=
client_messages.text.download_error=
client_messages.text.drag_and_drop=
client_messages.text.drag_and_drop_not_applicable_for_text_style_font_upload=
client_messages.text.drop_decimals_for_round_numbers=
client_messages.text.drop_it_here=
client_messages.text.due=
client_messages.text.due_soon=
client_messages.text.dueby=
client_messages.text.duplicates_content=
client_messages.text.duplicates_found=
client_messages.text.duplicates_matches=
client_messages.text.element=
client_messages.text.enabled_validations=
client_messages.text.end_date=
client_messages.text.evelope_weight=
client_messages.text.event=
client_messages.text.exact_matches=
client_messages.text.execution.execution_times=
client_messages.text.execution.times.bundling=
client_messages.text.execution.times.connector=
client_messages.text.execution.times.dews=
client_messages.text.execution.times.overall.dews=
client_messages.text.execution.times.overall=
client_messages.text.execution.times.postprocessing=
client_messages.text.execution.times.preprocessing=
client_messages.text.execution.times.processing=
client_messages.text.execution.times.processing_report=
client_messages.text.execution.times.processing_result=
client_messages.text.execution.times.qepost=
client_messages.text.execution.times.qualification.engine=
client_messages.text.execution.times.receiving.result=
client_messages.text.execution.times.sending=
client_messages.text.execution.times.umh.finalizing=
client_messages.text.execution.times.umh.preprocessing=
client_messages.text.execution.times.waiting.for.result=
client_messages.text.execution.times.waiting.in.queue.time=
client_messages.text.expand=
client_messages.text.expression=
client_messages.text.feedback=
client_messages.text.file=
client_messages.text.file_not_found=
client_messages.text.filename.pattern=
client_messages.text.files.blocked.by.sysadmin=
client_messages.text.fix=
client_messages.text.fix_all=
client_messages.text.fixed=
client_messages.text.fixed_row=
client_messages.text.footer=
client_messages.text.footer_height=
client_messages.text.form_name_required=
client_messages.text.group_id=
client_messages.text.group_keep_together_threshold=
client_messages.text.header=
client_messages.text.header_height=
client_messages.text.height.inches=
client_messages.text.hide_changes=
client_messages.text.hide_changes_warning=
client_messages.text.homepage=
client_messages.text.how_many_content_to_add=
client_messages.text.id_provider=
client_messages.text.id_value_required=
client_messages.text.image=
client_messages.text.import_layout_from_xml=
client_messages.text.in_progress=
client_messages.text.info=
client_messages.text.insert_col_after=
client_messages.text.insert_col_before=
client_messages.text.insert_pargraph_after=
client_messages.text.insert_pargraph_before=
client_messages.text.insert_row_after=
client_messages.text.insert_row_before=
client_messages.text.insert_space_after=
client_messages.text.insert_space_before=
client_messages.text.instance.access=
client_messages.text.instance_last_login=
client_messages.text.job=
client_messages.text.job_center=
client_messages.text.job_id=
client_messages.text.keywords=
client_messages.text.label_value_required=
client_messages.text.language_code=
client_messages.text.last_login=
client_messages.text.last_run_date=
client_messages.text.legal_mark_superscripting=
client_messages.text.letter=
client_messages.text.link_value_required=
client_messages.text.list_spacing=
client_messages.text.loading_sftprepo_images=
client_messages.text.locale_code=
client_messages.text.location=
client_messages.text.log=
client_messages.text.marcie_diagnostic=
client_messages.text.margins=
client_messages.text.mark_task_complete=
client_messages.text.master=
client_messages.text.matches=
client_messages.text.max_sentence_length=
client_messages.text.merge_tables=
client_messages.text.message_workflow=
client_messages.text.messagepoint_exchange=
client_messages.text.metadata_description=
client_messages.text.metadata_matches=
client_messages.text.metadata_properties=
client_messages.text.modules=
client_messages.text.more_items=
client_messages.text.my_active_task=
client_messages.text.my_settings=
client_messages.text.name_for_text_style_font_required=
client_messages.text.new_task=
client_messages.text.next_action=
client_messages.text.no_access=
client_messages.text.no_actions=
client_messages.text.no_background_tasks=
client_messages.text.no_items_available=
client_messages.text.no_list_styles_match_search=
client_messages.text.no_list_styles_selected=
client_messages.text.no_matches_found=
client_messages.text.no_menu_value_items=
client_messages.text.no_override=
client_messages.text.no_selection=
client_messages.text.no_sftprepo_images_for_folder=
client_messages.text.no_targeting_unchecked_for_all_customers=
client_messages.text.no_tasks=
client_messages.text.no_values=
client_messages.text.none=
client_messages.text.notification.emails=
client_messages.text.object=
client_messages.text.object_assignee=
client_messages.text.object_name=
client_messages.text.ok=
client_messages.text.or=
client_messages.text.order=
client_messages.text.origin=
client_messages.text.origin_status=
client_messages.text.other_active_task=
client_messages.text.overdue=
client_messages.text.override_proof_inprocess=
client_messages.text.owner=
client_messages.text.page=
client_messages.text.parts=
client_messages.text.placeholder=
client_messages.text.point_sizes=
client_messages.text.pop_out=
client_messages.text.preferred_contraction=
client_messages.text.preferred_contractions=
client_messages.text.primary_data_file=
client_messages.text.project=
client_messages.text.project_requirement=
client_messages.text.project_status=
client_messages.text.properties=
client_messages.text.quit=
client_messages.text.rationalizer=
client_messages.text.rationalizer_documents_report=
client_messages.text.rationalizer_metadata_report=
client_messages.text.rationalizer_workflow=
client_messages.text.readability_info=
client_messages.text.reading_average=
client_messages.text.reading_content_objects=
client_messages.text.region_left=
client_messages.text.region_left_width=
client_messages.text.region_right=
client_messages.text.region_right_width=
client_messages.text.rejected=
client_messages.text.remake.hash=
client_messages.text.remake.hash_confirm.alltouchpoints=
client_messages.text.remake.hash_confirm.touchpoint=
client_messages.text.remove=
client_messages.text.remove_column=
client_messages.text.remove_item=
client_messages.text.remove_menu_item=
client_messages.text.remove_row=
client_messages.text.removed=
client_messages.text.rendering=
client_messages.text.repeat_on_flow=
client_messages.text.repeating=
client_messages.text.report=
client_messages.text.requested_by=
client_messages.text.requirement=
client_messages.text.response_date=
client_messages.text.restricted_contraction=
client_messages.text.restricted_contractions=
client_messages.text.restricted_term=
client_messages.text.restricted_terms=
client_messages.text.save_and_refresh=
client_messages.text.saving=
client_messages.text.schedule_id=
client_messages.text.section_order=
client_messages.text.select=
client_messages.text.select_all=
client_messages.text.select_all_items=
client_messages.text.select_nth_element=
client_messages.text.select_on_this_page=
client_messages.text.selector=
client_messages.text.sentiment_average=
client_messages.text.sentiment_content_objects=
client_messages.text.sentiment_info=
client_messages.text.sequence=é åº
client_messages.text.server_bundle=
client_messages.text.shared_content=
client_messages.text.show_content=
client_messages.text.show_details=
client_messages.text.show_only_selected=
client_messages.text.sibling_status=
client_messages.text.signout=
client_messages.text.similarities=
client_messages.text.similarities_content=
client_messages.text.similarities_found=
client_messages.text.similarity_report=
client_messages.text.single_bullet_list=
client_messages.text.single_number_list=
client_messages.text.skip_to_main_content=
client_messages.text.snap_out=
client_messages.text.snap_in=
client_messages.text.source=
client_messages.text.source_status=
client_messages.text.sql_query=
client_messages.text.start_date=
client_messages.text.starting.background.task.to.calculate.hash=
client_messages.text.starting_number=
client_messages.text.statistics=
client_messages.text.stock_id=
client_messages.text.suppressed=
client_messages.text.sync=
client_messages.text.sync_with_source=
client_messages.text.sync_with_target=
client_messages.text.table_cell=
client_messages.text.tag=
client_messages.text.tagging_override=
client_messages.text.target_status=
client_messages.text.target_zone=
client_messages.text.task=
client_messages.text.task_assets=
client_messages.text.task_details=
client_messages.text.task_in_workflow=
client_messages.text.tasks=
client_messages.text.thresholds=
client_messages.text.toggle_footer_overrides=
client_messages.text.toggle_header_overrides=
client_messages.text.toggle_margin_overrides=
client_messages.text.toggle_region_left_overrides=
client_messages.text.toggle_region_right_overrides=
client_messages.text.toggle_repeating_cells=
client_messages.text.toggle_section_order_override=
client_messages.text.toggle_section_overrides=
client_messages.text.toggle_zone_overrides=
client_messages.text.touchpoint=
client_messages.text.touchpoint_exchange=
client_messages.text.touchpoint_export_config=
client_messages.text.touchpoints=
client_messages.text.unable_to_generate_proof_view_log=
client_messages.text.update=
client_messages.text.updated_by=
client_messages.text.url=
client_messages.text.user_menu_actions=
client_messages.text.variant=
client_messages.text.version=
client_messages.text.view_AFP=
client_messages.text.view_tasks=
client_messages.text.warning_object_change_dynamic=
client_messages.text.warning_object_change_structured=
client_messages.text.widgets=
client_messages.text.width.inches=
client_messages.text.working_copy=
client_messages.text.x_inches=
client_messages.text.y_inches=
client_messages.text.yes_continue=
client_messages.text.you_are_almost_done=
client_messages.title.actions=
client_messages.title.add_content=
client_messages.title.add_layout=
client_messages.title.add_project=
client_messages.title.add_symbol=
client_messages.title.add_task=
client_messages.title.add_tasks=
client_messages.title.additional.information=
client_messages.title.are_you_sure_you_want_to_leave_this_page=
client_messages.title.background_processes=
client_messages.title.bulk_delete=
client_messages.title.clear_metadata=
client_messages.title.content_compare=
client_messages.title.content_history=
client_messages.title.content_metadata=
client_messages.title.content_metadata_template=
client_messages.title.content_targeting=
client_messages.title.customize_locale=
client_messages.title.datarecords_compare=
client_messages.title.default_item_selection_targeting=
client_messages.title.dependencies_list=
client_messages.title.differences_list=
client_messages.title.document_metadata_template=
client_messages.title.edit_data_tag=
client_messages.title.edit_document=
client_messages.title.edit_task=
client_messages.title.edit_text_style_font=
client_messages.title.exact_matches_and_similarities=
client_messages.title.expanded_content_view=
client_messages.title.footer_row=
client_messages.title.footer_row_fixed=
client_messages.title.footer_row_repeated_on_flow=
client_messages.title.form_setup=
client_messages.title.header_row=
client_messages.title.header_row_fixed=
client_messages.title.header_row_repeated_on_flow=
client_messages.title.import.pod.domains.information=
client_messages.title.instance=
client_messages.title.list_properties=
client_messages.title.list_styles=
client_messages.title.oops=
client_messages.title.orders=
client_messages.title.warning=
client_messages.title.override_proof=
client_messages.title.placeholder=
client_messages.title.placeholder_list_styles=
client_messages.title.placeholder_paragraph_styles=
client_messages.title.placeholder_text_styles=
client_messages.title.placeholder_visibility=
client_messages.title.rationalizer_application_visibility=
client_messages.title.rationalizer_change_structure=
client_messages.title.rationalizer_document_upload=
client_messages.title.rationalizer_touchpoint_export=
client_messages.title.referencedby.objects_list=
client_messages.title.references.objects_list=
client_messages.title.rejection_note=
client_messages.title.remove_layout=
client_messages.title.repeating_footer=
client_messages.title.repeating_header=
client_messages.title.repeating_row=
client_messages.title.request_proof=
client_messages.title.requirement_note=
client_messages.title.row_repeated_on_flow_omit_initial=
client_messages.title.row_repeated_on_flow_omit_last=
client_messages.title.section_properties=
client_messages.title.set_part_size_position=
client_messages.title.set_zone_size_position=
client_messages.title.shared_content_edit=
client_messages.title.shared_content_separate=
client_messages.title.similarity_lower_threshold=
client_messages.title.some_changes_have_not_been_saved=
client_messages.title.source_editor=
client_messages.title.style_connectors=
client_messages.title.suggested_values=
client_messages.title.summary=
client_messages.title.system_variable=
client_messages.title.tags=
client_messages.title.take_me_to=
client_messages.title.task_complete=
client_messages.title.task_in_workflow=
client_messages.title.task_reassignment=
client_messages.title.tasks=
client_messages.title.template_setup=
client_messages.title.toggle_layout_overrides=
client_messages.title.upload.users=
client_messages.title.upload.variant.metadata=
client_messages.title.user_panel=
client_messages.title.zone=
client_messages.title.zone_list_styles=
client_messages.title.zone_references=
code.text.authentication.failure=
code.text.company.redirection.pod.deactivated=
code.text.company.redirection.pod.offline=
code.text.no.web.service.permission=
code.text.password.invalid.redirection.info=
code.text.password.redirection.error=
code.text.password.redirection.info.missing=
code.text.password.redirection.pod.unavailable=
code.text.password.transition.redirection.info.missing=
code.text.security.settings.username.password.invalid=
code.text.soft.deactivated.error=
email.content.body.instance.branch.name=
email.content.body.intro=
email.content.company.and.instnace.name=
email.content.deliveryevent.failed=
email.content.footer=
email.content.label.touchpoint=
email.content.notifications.are.generated=
email.content.project.complete=
email.content.project.create=
email.content.project.overdue=
email.content.socialize.proof=
email.content.sso.user.activate=
email.content.task.create=
email.content.task.near.term=
email.content.task.overdue=
email.content.task.reassign=
email.digest.subject.your.messagepoint.notification=
email.digest.subject.your.messagepoint.notifications=
email.subheader.activated=
email.subheader.deliveryevent.failed=
email.subheader.message.activated=
email.subheader.message.working.copy.created=
email.subheader.password.change.notification=
email.subheader.password.forget=
email.subheader.password.reset.activate=
email.subheader.password.reset=
email.subheader.project.assigned.to.you=
email.subheader.project.complete=
email.subheader.project.create=
email.subheader.project.near.due=
email.subheader.project.overdue=
email.subheader.socialize.proof=
email.subheader.sso.user.activate=
email.subheader.system.task.create=
email.subheader.task.assigned=
email.subheader.task.create=
email.subheader.task.near.term=
email.subheader.task.overdue=
email.subheader.task.reassign=
email.subheader.workflow.approval.individual=
email.subheader.workflow.approval=
email.subheader.workflow.approved.individual=
email.subheader.workflow.approved=
email.subheader.workflow.approvedbyother.individual=
email.subheader.workflow.approvedbyother=
email.subheader.workflow.duebyautoapprove.individual=
email.subheader.workflow.duebyautoapprove=
email.subheader.workflow.duebynotify.individual=
email.subheader.workflow.duebynotify=
email.subheader.workflow.reassigned.individual=
email.subheader.workflow.reassigned=
email.subheader.workflow.rejected.individual=
email.subheader.workflow.rejected=
email.subheader.workflow.translation.individual=
email.subheader.workflow.translation=
email.subheader.working.copy.created=
email.subject.activated=
email.subject.deliveryevent.failed=
email.subject.message.activated=
email.subject.message.working.copy.created=
email.subject.project.complete=
email.subject.project.create=
email.subject.project.overdue=
email.subject.socialize.proof=
email.subject.sso.user.activate=
email.subject.task.create=
email.subject.task.near.term=
email.subject.task.overdue=
email.subject.task.reassign=
email.subject.workflow.approval=
email.subject.workflow.approved=
email.subject.workflow.approvedbyother=
email.subject.workflow.duebyautoapprove=
email.subject.workflow.duebynotify=
email.subject.workflow.reassigned=
email.subject.workflow.rejected=
email.subject.workflow.translation=
email.subject.working.copy.created=
email.subject.your.action.required=
email.workflow.approval.content=
error.aggregation.level.must.match.for.repeating.variables=
error.assets.local.global.reference.other.local.assets=
error.assets.local.global.referenced.by.other.global.assets=
error.assets.make.global.global.working.copy.exists=
error.assets.make.global.local.working.copy.exists=
error.assets.make.global.no.local.active.copy=
error.assets.make.local.image.library.references.another.image=
error.assets.make.local.local.asset.exists=
error.assets.make.local.no.global.active.copy=
error.assets.make.local.no.variant.selected=
error.assets.move.to.local.global.asset.local.reference=
error.assets.move.to.local.global.asset.touchpoint.reference=
error.assets.pull.image.library.references.another.image=
error.assets.pull.local.working.copy.exists=
error.assets.pull.no.global.active.copy=
error.assets.push.global.working.copy.exists=
error.assets.push.no.local.active.copy=
error.blue.relay.folder.id=
error.blue.relay.response.status.not.ok=
error.bundle.delivery.connection.required=
error.bundle.delivery.filename.pattern.must.be.entered=
error.bundle.delivery.invalid.server=
error.bundle.delivery.name.must.be.entered=
error.bundle.delivery.name.must.be.unique=
error.bundle.delivery.password.required=
error.bundle.delivery.port.must.be.entered=
error.bundle.delivery.sshkeys.file.upload.required=
error.bundle.delivery.sshkeys.invalid.privatekey=
error.bundle.delivery.url.must.be.entered=
error.bundle.delivery.username.required=
error.communication.activation.without.proof=
error.communication.approval.without.proof=
error.communication.mandatory.values=
error.communication.must.assign.valid.workflow=
error.communication.order.value.is.used=
error.connection.refused=
error.connection.timeout=
error.data.groups.must.match.for.repeating.variables=
error.delimiter.can.not.be.empty=
error.document.composition.version.is.not.available=
error.file.doesnt.exist=
error.file.doesnt.match.required.pod=
error.file.missing.domains.info=
error.file.should.include.master.pod=
error.global.asset.not.accessible=
error.global.asset.not.visible.to.approver=
error.id.mandatory.and.has.to.be.greater.than.0=
error.order.mandatory.and.has.to.be.greater.than.0=
error.idp.provider.type.is.not.valid=
error.import.file.touchpoint.different.dna=
error.improper.selector.configuration=
error.input.invalid.locale=
error.insert.id.notfound=
error.insertschedule.cannot.release.for.approval.selectors.combination.is.in.use.by.another.schedule.for.list=
error.insertschedule.cannot.release.for.approval.selectors.combination.is.in.use.by.another.schedule=
error.invalid.email=
error.level.not.set.for.row.variable=
error.level.not.set.for.variable=
error.level.of.variable.greater.than.zone.level=
error.local.content.is.being.referenced.on.archive.delete=
error.local.content.is.being.referenced.on.discard.working.copy=
error.local.image.is.being.referenced.on.archive=
error.lookup.table.file.structure.changed=
error.lookup.table.file.structure.column=
error.lookup.table.file.structure.values=
error.lookup.table.is.being.referenced.on.archive.delete=
error.lookup.table.is.being.referenced.on.archive=
error.message.admin.securitySettings.hardDeactivationLimitDays.mustgreaterthan.softdeactivationdays=
error.message.admin.securitySettings.hardDeactivationLimitDays.mustgreaterthanone=
error.message.admin.securitySettings.softDeactivationLimitDays.mustgreaterthanone=
error.message.block.content.not.permitted.for.inline=
error.message.can.not.mix.part.graphic.types.and.restrict.library.assets=
error.message.cannot.delete.reference.styles=
error.message.cannot.delete.referenced.alternate=
error.message.cannot.delete.referenced.data.files=
error.message.cannot.delete.referenced.data.resources=
error.message.cannot.delete.referenced.liststyles=
error.message.cannot.delete.referenced.tasks=
error.message.cannot.delete.referenced.workflows=
error.message.cannot.delete.referenced.zones=
error.message.cannot.delete.variant.active.copy.is.referenced=
error.message.cannot.delete.workflow.engaged.tasks=
error.message.cannot.reassign.asset.currently.being.edited.parameterized=
error.message.cannot.reassign.asset.currently.being.edited=
error.message.composition.package.must.be.uploaded.to.run.connected.bundle=
error.message.composition.package.must.be.uploaded.to.update.connected.bundle=
error.message.conflicting.inline.content=
error.message.connected.close.tab.to.exit=
error.message.connected.portal.cannot.edit.active.order=
error.message.connected.portal.missing.order.guid=
error.message.connected.portal.missing.touchpoint.guid=
error.message.connected.portal.no.reassign.perm=
error.message.connected.portal.order.assigned.to.another.user=
error.message.connected.portal.unknown.connectors=
error.message.connected.portal.unknown.touchpoint=
error.message.connected.portal.unknown.user=
error.message.connected.touchpoint.not.visible.to.user=
error.message.connected.zone.cannot.be.freeform.or.forms=
error.message.content.library.select.an.image=
error.message.content.not.valid=
error.message.content.required=
error.message.contenttype.only.print.documents.apply.markup=
error.message.date.parameter.must.be.lowest.level.in.group=
error.message.default.value.is.required.with.unique.value=
error.message.default.value.not.match.validation.type=
error.message.dictionary.not.available=
error.message.dictionary.user.not.available=
error.message.domain.controller.instance.role.missing=
error.message.domain.not.matching.or.doesnot.existing=
error.message.duplicate.layout.name=
error.message.duplicate.project.name=
error.message.ecatalog.not.matching=
error.message.end.date.must.be.after.start.date=
error.message.exceed.maximum.licence.level.of.home.domain.level=
error.message.exceed.maximum.number.of.connected.users=
error.message.exceed.maximum.number.of.workflow.users=
error.message.expected.object.not.matching=
error.message.fail.to.create.user=
error.message.given.instance.not.existing=
error.message.guid.missing=
error.message.hardware.key.required=
error.message.has.global.sync.target.parameter.group.cannot.be.changed=
error.message.has.global.sync.target.type.cannot.be.changed=
error.message.has.local.sync.target.parameter.group.cannot.be.changed=
error.message.has.local.sync.target.type.cannot.be.changed=
error.message.invalid.guid=
error.message.invalid.parent=
error.message.invalid.selection.data=
error.message.invalid.test.param=
error.message.label.required=
error.message.label.unique=
error.message.language.must.be.selected.for.translation.step=
error.message.language.must.be.used.in.all.touchponts=
error.message.line.spacing.values.required=
error.message.link.required=
error.message.link.valid.url=
error.message.lookuptable.referenced.cannot.restrict=
error.message.metadata.template.required.connector.value=
error.message.metadata.template.requires.at.least.one.input=
error.message.model.not.equal=
error.message.model.not.exsiting=
error.message.model.not.matching=
error.message.must.select.task.object=
error.message.must.select.task.workflow=
error.message.namerequired.for.selector=
error.message.order.digital.proof.email=
error.message.order.entry.display.criteria.required=
error.message.order.entry.unique.connector.values.required=
error.message.order.socialization.email.address.invalid=
error.message.parameter.cannot.disabled.when.referenced=
error.message.parameter.group.not.valid.with.selected.message=
error.message.parameter.groups.not.data.model.compatible=
error.message.parameter.groups.not.have.same.num.of.parameters=
error.message.rationalizer.ingestion.upload.ignore=
error.message.rationalizer.ingestion.upload.manifest.wrong.extension=
error.message.rationalizer.ingestion.upload.manifest.wrong.location=
error.message.rationalizer.ingestion.upload.manifest.wrong.name=
error.message.rationalizer.ingestion.upload.no.files=
error.message.rationalizer.ingestion.upload.no.manifest=
error.message.rationalizer.ingestion.upload.replace=
error.message.rationalizer.ingestion.upload.unique=
error.message.reference.source.required.for.reference.file.type=
error.message.right.bottom.range=
error.message.right.top.range=
error.message.role.is.not.valid.on.the.instance=
error.message.selectavariant=
error.message.selector.name.is.used.by.another.selector=
error.message.spacing.bottom.range=
error.message.spacing.left.range=
error.message.spacing.right.range=
error.message.spacing.top.range=
error.message.style.customization.tagging.override.format=
error.message.tagging.override.format=
error.message.text.style.color.required=
error.message.text.style.name.cannot.start.with.number=
error.message.text.style.name.pattern=
error.message.text.style.point.size.required=
error.message.text.style.tagging.override.format=
error.message.text.styles.cannot.wrap.lists=
error.message.touchpoint.message.content.type.not.compatible=
error.message.touchpoint.message.zone.imagefile.not.compatible=
error.message.touchpoint.must.be.selected=
error.message.touchpoint.or.collection.must.be.selected=
error.message.touchpoint.variation.not.enabled=
error.message.touchpoint.zone.data.group.missing=
error.message.ttf.font.mandatory=
error.message.variable.type.cannot.be.modified.when.referenced=
error.message.variables.in.content.not.data.model.compatible=
error.message.xml.parse_failed=
error.message.zip.does.not.contain.valid.composition.file=
error.message.zone.connector.not.set=
error.message.zone.default.canvas.width.required=
error.message.zone.does.not.exist=
error.message.zone.invalid.image.assigned.for.graphic.type=
error.message.zone.must.be.selected=
error.metadata.brand.profile.is.being.referenced.on.delete=
error.metadata.form.definition.is.being.referenced.on.delete=
error.metadata.form.item.value.is.used=
error.metadata.point.of.interest.label=
error.metadata.point.of.interest.value=
error.name.already.existing.name=
error.no.data.group.set.for.row.variable=
error.no.data.group.set.for.variable=
error.no.data.group.set.for.zone=
error.node.name.cannot.contain.anyspaces=
error.node.schema.guid.must.be.entered=
error.node.schema.guid.must.be.unique=
error.not.found=
error.one.or.more.variables.used.in.this.asset.are.not.mapped.properly=
error.parse.file=
error.pinc.server.https.messagepointpinc.subdomain.required=
error.pod.web.services.user.must.be.entered=
error.pod.web.services.user.pwd.must.be.entered=
error.previous.selected.version=
error.project.at.least.one.task=
error.project.task.at.least.one.step=
error.rationalizer.order.number.required=
error.rationalizer.query.search.value.required=
error.rationalizer.remove.content.from.shared.asset.before.adding=
error.rationalizer.select.at.least.one.content.item=
error.section.dimensions.invalid=
error.touchpoint.has.child.versions.cannot.be.removed=
error.touchpointselection.selection.name.already.exists.name=
error.touchpointselection.selectors.add.selection.not.valid=
error.upload.file=
error.user.account.notfound=
error.workflow.approvals.at.least.one.approval.step.for.project.workflow=
error.workflow.approvals.user.cannot.be.both.owner.and.translator=
error.workflow.assignment.workflow.must.be.assignment.to.master=
error.workflow.name.must.be.unique=
error.workflow.translation.cannot.apply.to.variant.workflow.touchpoint=
error.workflow.translator.needed.for.each.workflow.step=
info.message.rationalizer.upload.file.contains.no.xml=
info.message.rationalizer.upload.file.contains.xml=
info.message.rationalizer.upload.file.received=
licence.label.ECatalog=
licence.label.LookupTable=
licence.label.MessagepointPINC=
licence.label.NumberOfConnectedUsers=
licence.label.SFMCJBConnector=
licence.label.SefasConnector=
licence.label.TransactionalAPI=
licence.label.sorting.ECatalog=
licence.label.sorting.LookupTable=
licence.label.sorting.MessagepointPINC=
licence.label.sorting.NumberOfConnectedUsers=
licence.label.sorting.SFMCJBConnector=
licence.label.sorting.SefasConnector=
licence.label.sorting.TransactionalAPI=
page.flow.release=
page.label.AVAILABLE.LIST.STYLES=
page.label.CLEAR=
page.label.DEDigital=
page.label.DEServer=
page.label.DEWS=
page.label.EMPTY=
page.label.INSERT.AS.PARAGRAPH=
page.label.MARCIE=
page.label.METADATA=
page.label.MPComp=
page.label.MPComposer=
page.label.NO.VALUE=
page.label.PINC=
page.label.Proof=
page.label.SMART.TEXT.DYNAMIC.CONTENT=
page.label.SMART.TEXT=
page.label.UPLOAD=
page.label.VARIABLE.CONTENT.ENABLED=
page.label.XML.data.tag=
page.label.abbr.ascending=
page.label.abbr.descending=
page.label.accept=
page.label.access=
page.label.accessibility.do.not.read=
page.label.accessibility=
page.label.account=
page.label.action.history=
page.label.activate.all=
page.label.add.and.save=
page.label.add.bundle.delivery=
page.label.add.channel=
page.label.add.child=
page.label.add.condition=
page.label.add.content=
page.label.add.de.server=
page.label.add.dictionary=
page.label.add.document=
page.label.add.font=
page.label.add.form=
page.label.add.image.library=
page.label.add.list.style=
page.label.add.local.image=
page.label.add.local.smart.canvas=
page.label.add.local.smart.text=
page.label.add.lookup.table=
page.label.add.placeholder=
page.label.add.project.workflow=
page.label.add.project=
page.label.add.query=
page.label.add.smart.canvas=
page.label.add.template=
page.label.add.test.communication=
page.label.add.test.suite=
page.label.add.value=
page.label.add.workflow=
page.label.added.content=
page.label.admin.email.notification.settings.update=
page.label.admin.security.settings.hard.deactivation.enabled=
page.label.admin.security.settings.hard.deactivation.limit.days=
page.label.admin.security.settings.soft.deactivation.enabled=
page.label.admin.security.settings.soft.deactivation.limit.days=
page.label.admin.systemproperties.allow_upload_filetypes=
page.label.admin.systemproperties.analytics.amplitude.api.key=
page.label.admin.systemproperties.analytics.amplitude.enabled=
page.label.admin.systemproperties.analytics.fullstory.enabled=
page.label.admin.systemproperties.bundle.delivery.post.process.scripts.folder=
page.label.admin.systemproperties.composition_engines_dir=
page.label.admin.systemproperties.content.delivery.network.domain=
page.label.admin.systemproperties.content.delivery.network.enabled=
page.label.admin.systemproperties.content.delivery.network=
page.label.admin.systemproperties.delivery_event_support_notification=
page.label.admin.systemproperties.dictionary_dir=
page.label.admin.systemproperties.disallow_upload_filetypes=
page.label.admin.systemproperties.document_history_dir=
page.label.admin.systemproperties.marcie_bootstrap_api_key=
page.label.admin.systemproperties.marcie_bootstrap_endpoint=
page.label.admin.systemproperties.marcie_content_compare_endpoint=
page.label.admin.systemproperties.marcie_enable_content_compare=
page.label.admin.systemproperties.marcie_readability_endpoint=
page.label.admin.systemproperties.marcie_sentence_splitter_endpoint=
page.label.admin.systemproperties.marcie_sentiment_endpoint=
page.label.admin.systemproperties.metadata_report_dir=
page.label.admin.systemproperties.rationalizer_files=
page.label.admin.systemproperties.target.readability=
page.label.admin.systemproperties.task_log_dir=
page.label.admin.systemproperties.web.assets.service.url=
page.label.advanced.composition=
page.label.algorithm=
page.label.align=
page.label.all.actions=
page.label.all.assignees=
page.label.all.object.types=
page.label.all.of.these=
page.label.all.owners=
page.label.all.tasks=
page.label.all.touchpoints=
page.label.all.workgroups=
page.label.allow.edit.default.language=
page.label.allow.images=
page.label.alt.text=
page.label.alternate.layout.assignment=
page.label.alternative.text=
page.label.alternative_text=
page.label.anchor=
page.label.anonymization.type.custom=
page.label.any.date=
page.label.any.of.these=
page.label.any.or.no.placeholder=
page.label.any.placeholder=
page.label.any=
page.label.applicable.variants=
page.label.application.download.log=
page.label.application.metatags=
page.label.application.navigation=
page.label.application.not.sync=
page.label.application.report=
page.label.application.sync.status.never.synchronized=
page.label.application.sync.status.server.down=
page.label.application.sync.status.synchronisation.in.progress=
page.label.application.sync.status.synchronized=
page.label.application.visibility=
page.label.apply.cell.padding=
page.label.apply.compound.space.detection=
page.label.apply.notication.digest=
page.label.apply.preferred.contractions=
page.label.apply.restricted.contractions=
page.label.apply.restricted.terms=
page.label.apply.sentence.max.length=
page.label.apply.table.spacing=
page.label.apply.text.style.font.toggle=
page.label.approve.workflow.translation=
page.label.are.ranked.by.similarity=
page.label.assets=
page.label.assignee.s=
page.label.assignee=
page.label.assignees=
page.label.attachment.deleted=
page.label.attribute.active.copy.hash=
page.label.attribute.active.copy=
page.label.attribute.advanced=
page.label.attribute.aggregation.level=
page.label.attribute.aggregation.operator=
page.label.attribute.applicable.variants=
page.label.attribute.archived.copy.hash=
page.label.attribute.archived.copy=
page.label.attribute.canvas.max.height=
page.label.attribute.canvas.max.width=
page.label.attribute.canvas.trim.height=
page.label.attribute.canvas.trim.width=
page.label.attribute.columnar.indicators=
page.label.attribute.comments=
page.label.attribute.compound.variable.format.type=
page.label.attribute.condition.search.value=
page.label.attribute.condition.type=
page.label.attribute.connected.interview.enabled=
page.label.attribute.connected.interview.orders=
page.label.attribute.connected.interview.variant.selection=
page.label.attribute.connector.parameter=
page.label.attribute.connector=
page.label.attribute.content.hash=
page.label.attribute.content.selection.type=
page.label.attribute.content.specialization.type=
page.label.attribute.content.trim.type=
page.label.attribute.content.type=
page.label.attribute.customer.driver.file=
page.label.attribute.customer.primary.key=
page.label.attribute.data.element.name=
page.label.attribute.data.group.name=
page.label.attribute.data.privacy=
page.label.attribute.data.records=
page.label.attribute.data_collection=
page.label.attribute.data_element.anonymization_mask=
page.label.attribute.data_element.anonymization_max_length=
page.label.attribute.data_element.anonymization_min_length=
page.label.attribute.data_element.anonymization_type=
page.label.attribute.data_element.anonymized=
page.label.attribute.data_element.data_group=
page.label.attribute.data_element.data_record=
page.label.attribute.data_element.data_sub_type=
page.label.attribute.data_element.data_type=
page.label.attribute.data_element.decimal_places=
page.label.attribute.data_element.external_format_text=
page.label.attribute.data_element.external_id=
page.label.attribute.data_element.length=
page.label.attribute.data_element.name=
page.label.attribute.data_element.start_location=
page.label.attribute.data_element.variable_external_id=
page.label.attribute.data_element=
page.label.attribute.data_groups=
page.label.attribute.data_record.break_indicator=
page.label.attribute.data_record.data_group=
page.label.attribute.data_record.data_groups=
page.label.attribute.data_record.enabled=
page.label.attribute.data_record.indicator=
page.label.attribute.data_record.position=
page.label.attribute.data_record.repeating=
page.label.attribute.data_record.start_customer=
page.label.attribute.data_source=
page.label.attribute.default.value=
page.label.attribute.delimited.indicators=
page.label.attribute.delimiter=
page.label.attribute.delivery.type=
page.label.attribute.description=
page.label.attribute.display.criteria=
page.label.attribute.dna=
page.label.attribute.driver.aggregation.operator=
page.label.attribute.enabled_for_connected=
page.label.attribute.enabled_for_content=
page.label.attribute.enabled_for_rules=
page.label.attribute.encoding.type=
page.label.attribute.end.date=
page.label.attribute.excluded.target.groups.relationship=
page.label.attribute.excluded.target.groups.search.value=
page.label.attribute.excluded.target.groups=
page.label.attribute.expression=
page.label.attribute.extended.target.groups.relationship=
page.label.attribute.extended.target.groups.search.value=
page.label.attribute.extended.target.groups=
page.label.attribute.external.id=
page.label.attribute.field.max.length=
page.label.attribute.field.size=
page.label.attribute.file.name=
page.label.attribute.flow.type=
page.label.attribute.form.name=
page.label.attribute.friendly.name=
page.label.attribute.global.image.dna=
page.label.attribute.global.image.name=
page.label.attribute.global.smart.text.dna=
page.label.attribute.global.smart.text.name=
page.label.attribute.graphic.type=
page.label.attribute.hash=
page.label.attribute.headers=
page.label.attribute.included.target.groups.relationship=
page.label.attribute.included.target.groups.search.value=
page.label.attribute.included.target.groups=
page.label.attribute.input.validation=
page.label.attribute.input_character_encoding=
page.label.attribute.insert.as.block=
page.label.attribute.instance.content.hash=
page.label.attribute.instance.hash=
page.label.attribute.is.confirmation.needed=
page.label.attribute.is.indicator=
page.label.attribute.is.local=
page.label.attribute.is.mandatory=
page.label.attribute.is.one.of.separator=
page.label.attribute.is.primary=
page.label.attribute.is.reference.variable=
page.label.attribute.keep.together=
page.label.attribute.layout.type=
page.label.attribute.level=
page.label.attribute.linked.message.dna=
page.label.attribute.linked.message.name=
page.label.attribute.local.content.delivery.type=
page.label.attribute.lookup.values=
page.label.attribute.lookup_table=
page.label.attribute.menu.items=
page.label.attribute.metadata.form.name=
page.label.attribute.metatags=
page.label.attribute.model.content.hash=
page.label.attribute.model.hash=
page.label.attribute.name=
page.label.attribute.new.comment=
page.label.attribute.on.hold=
page.label.attribute.owning.touchpoint.variant.dna=
page.label.attribute.owning.touchpoint.variant.name=
page.label.attribute.parameter.group.name=
page.label.attribute.parameter.group=
page.label.attribute.parameter=
page.label.attribute.parent.item=
page.label.attribute.primary.compoundkey=
page.label.attribute.primary.data.source=
page.label.attribute.primary.variable=
page.label.attribute.priority=
page.label.attribute.ready.for.approval=
page.label.attribute.reference.compoundkey=
page.label.attribute.reference.connections=
page.label.attribute.reference.data.element.name=
page.label.attribute.reference.data.source=
page.label.attribute.reference.variable=
page.label.attribute.relationship=
page.label.attribute.removed=
page.label.attribute.repeat.dates.annually=
page.label.attribute.revision=
page.label.attribute.sample.value=
page.label.attribute.script=
page.label.attribute.segment.data=
page.label.attribute.selection.data=
page.label.attribute.source.type=
page.label.attribute.sql.expression=
page.label.attribute.start.date=
page.label.attribute.status=
page.label.attribute.supports.barcodes=
page.label.attribute.supports.forms=
page.label.attribute.supports.tables=
page.label.attribute.suppressed=
page.label.attribute.targetgroup.details=
page.label.attribute.type=
page.label.attribute.unique.value=
page.label.attribute.usage.type=
page.label.attribute.use.default.value=
page.label.attribute.variable.expression=
page.label.attribute.variable.script=
page.label.attribute.version.effect.end.date=
page.label.attribute.version.effect.start.date=
page.label.attribute.version.status=
page.label.attribute.working.copy.hash=
page.label.attribute.working.copy=
page.label.attribute.xml_data_element.attribute_name=
page.label.attribute.xml_data_element.is_attribute=
page.label.attribute.xml_data_element.name=
page.label.attribute.xml_data_element.tag=
page.label.attribute.xml_tag.break_indicator=
page.label.attribute.xml_tag.data_group=
page.label.attribute.xml_tag.name=
page.label.attribute.xml_tag.path=
page.label.attribute.xml_tag.repeating=
page.label.attribute.xml_tag.start_customer=
page.label.attribute.xml_tag.start_datagroup=
page.label.attribute.xml_tag_definitions=
page.label.attribute.zone.name=
page.label.attribute.zone=
page.label.attribute.zones.dnas=
page.label.attribute.zones.names=
page.label.audit.report.type.connected.report=
page.label.audit.report.type.image.library.audit.report=
page.label.audit.report.type.local.image.audit.report=
page.label.audit.report.type.local.smart.canvas.audit.report=
page.label.audit.report.type.local.smart.text.audit.report=
page.label.audit.report.type.project.audit.report=
page.label.audit.report.type.smart.canvas.audit.report=
page.label.audit.report.type.smart.text.audit.report=
page.label.audit.report.type.touchpoint.compare.report=
page.label.auditing.activation=
page.label.auditing.all.actions.where=
page.label.auditing.all.domains.for=
page.label.auditing.all.events.for=
page.label.auditing.all.instances.for=
page.label.auditing.any.user=
page.label.auditing.archived=
page.label.auditing.asset.change=
page.label.auditing.asset.update=
page.label.auditing.attachment.targeting.change=
page.label.auditing.audit.report.requested.and.successfully.generated=
page.label.auditing.audit.report.requested.but.failed=
page.label.auditing.audit.report=
page.label.auditing.authentication.failure=
page.label.auditing.authentication.success=
page.label.auditing.between=
page.label.auditing.changes=
page.label.auditing.cleanup=
page.label.auditing.connected=
page.label.auditing.content.targeting.change=
page.label.auditing.creation=
page.label.auditing.data.element=
page.label.auditing.de.license.content.edit=
page.label.auditing.de.license.list=
page.label.auditing.de.license=
page.label.auditing.deactivation=
page.label.auditing.delete.archive=
page.label.auditing.delete=
page.label.auditing.delivery.event=
page.label.auditing.disabling=
page.label.auditing.discard=
page.label.auditing.domain.license=
page.label.auditing.domain=
page.label.auditing.enabling=
page.label.auditing.ending=
page.label.auditing.events.initiated.by=
page.label.auditing.execute=
page.label.auditing.font.list=
page.label.auditing.image.library.content.change=
page.label.auditing.image.library.content.edit=
page.label.auditing.image.library.list=
page.label.auditing.insert.discard=
page.label.auditing.insert.edit=
page.label.auditing.insert.list=
page.label.auditing.insert.schedule.discard=
page.label.auditing.insert.schedule.edit=
page.label.auditing.insert.schedule.list=
page.label.auditing.insert.targeting.change=
page.label.auditing.instance.license=
page.label.auditing.instance=
page.label.auditing.license=
page.label.auditing.local.image.content.change=
page.label.auditing.local.image.content.edit=
page.label.auditing.local.image.list=
page.label.auditing.local.smart.canvas.content.change=
page.label.auditing.local.smart.canvas.targeting.change=
page.label.auditing.local.smart.text.content.change=
page.label.auditing.local.smart.text.content.edit=
page.label.auditing.local.smart.text.list=
page.label.auditing.local.smart.text.targeting.change=
page.label.auditing.message.activate=
page.label.auditing.message.content.change=
page.label.auditing.message.content.edit=
page.label.auditing.message.list=
page.label.auditing.message.targeting.change=
page.label.auditing.on.any.date=
page.label.auditing.order.round.trip=
page.label.auditing.output.tag.targeting.change=
page.label.auditing.page.access=
page.label.auditing.password.change=
page.label.auditing.password.reset=
page.label.auditing.password.setting=
page.label.auditing.permission.change=
page.label.auditing.power.edit.save=
page.label.auditing.proof.run=
page.label.auditing.purge.notification=
page.label.auditing.purge=
page.label.auditing.report.run=
page.label.auditing.reports.list=
page.label.auditing.requests=
page.label.auditing.role.change=
page.label.auditing.role=
page.label.auditing.signin=
page.label.auditing.simulation.list=
page.label.auditing.simulation.run=
page.label.auditing.smart.canvas.content.change=
page.label.auditing.smart.canvas.targeting.change=
page.label.auditing.smart.text.content.change=
page.label.auditing.smart.text.content.edit=
page.label.auditing.smart.text.list=
page.label.auditing.smart.text.targeting.change=
page.label.auditing.starting=
page.label.auditing.suppress=
page.label.auditing.sync.requested.and.successfully.generated=
page.label.auditing.sync.requested.but.failed=
page.label.auditing.sync=
page.label.auditing.target.group.edit=
page.label.auditing.target.groups.list=
page.label.auditing.target.rule.edit=
page.label.auditing.target.rules.list=
page.label.auditing.test.connection.requested.and.successfully.tested=
page.label.auditing.test.run=
page.label.auditing.tests.list=
page.label.auditing.text.connection.requested.but.failed=
page.label.auditing.text.style.list=
page.label.auditing.touchpoint.setting=
page.label.auditing.touchpoint.targeting.change=
page.label.auditing.tp.version.requested.and.successfully.generated=
page.label.auditing.tp.version.requested.but.failed=
page.label.auditing.umh.license.content.edit=
page.label.auditing.umh.license=
page.label.auditing.user=
page.label.auditing.variants.list=
page.label.auditing.version=
page.label.auditing.web.service=
page.label.auditing.workgroup.change=
page.label.auditing=
page.label.authentication=
page.label.available.connectors=
page.label.available.languages=
page.label.available.touchpoints=
page.label.avoid.consecutive.spaces.between.sentences=
page.label.avoid.consecutive.spaces.within.sentences=
page.label.avoid.single.item.bulleted.lists=
page.label.avoid.single.item.numbered.lists=
page.label.avoid.these.contractions=
page.label.back.single.step=
page.label.background.tasks=
page.label.barcode=
page.label.barcodes=
page.label.basic.info=
page.label.blue.relay.configuration=
page.label.blue.relay.endpoint=
page.label.blue.relay.target.folder=
page.label.blue.relay.token=
page.label.bold.toggle=
page.label.border.type.bottom=
page.label.border.type.full=
page.label.border.type.top=
page.label.branch.user.licence.stats=
page.label.brand.guidance=
page.label.brand.profile=
page.label.brand.profiles=
page.label.brand=
page.label.bulk.delete=
page.label.bullet.spacing.header=
page.label.bullet.spacing=
page.label.bullet.symbol.overrides=
page.label.bundle.combined.content=
page.label.bundle.delivery.var.description.company=
page.label.bundle.delivery.var.description.date_dd=
page.label.bundle.delivery.var.description.date_mm=
page.label.bundle.delivery.var.description.date_yy=
page.label.bundle.delivery.var.description.date_yyyy=
page.label.bundle.delivery.var.description.instance=
page.label.bundle.delivery.var.description.instancetype=
page.label.bundle.delivery.var.description.jobid=
page.label.bundle.delivery.var.description.jobtype=
page.label.bundle.delivery.var.description.podid=
page.label.bundle.delivery.var.description.time_hh=
page.label.bundle.delivery.var.description.time_mm=
page.label.bundle.delivery.var.description.time_ss=
page.label.bundle.delivery.var.description.touchpoint=
page.label.bundle.delivery=
page.label.bundle.images.applying.filename=
page.label.bundle=
page.label.bundled.composition.package=
page.label.bundled.data.resource=
page.label.button=
page.label.by.similarity.range=
page.label.calculate.hash.for.all=
page.label.calculate.hash=
page.label.cascade.password.change=
page.label.category.administrative=
page.label.category.connected=
page.label.category.data=
page.label.category.inserts=
page.label.category.messaging=
page.label.category.miscellaneous=
page.label.category.pinc=
page.label.category.projects=
page.label.category.rationalizer=
page.label.category.reporting=
page.label.category.shared.libraries=
page.label.category.targeting=
page.label.category.tasks=
page.label.category.test.proof.simulation=
page.label.category.touchpoint.setup=
page.label.category.versions=
page.label.category.workflow=
page.label.change.all=
page.label.change.domain=
page.label.change.owner=
page.label.changed.since.last.run=
page.label.changed=
page.label.channel.restriction=
page.label.checked=
page.label.child.linked.messages=
page.label.child=
page.label.clear.content.and.continue=
page.label.clear.date=
page.label.clear.metadata=
page.label.clear.touchpoint.metadata=
page.label.clear=
page.label.clone.interactive=
page.label.clone.interview=
page.label.clone.to.global=
page.label.clone.to.local=
page.label.clone.touchpoint=
page.label.cloned.from=
page.label.cloned=
page.label.collapse.all.results=
page.label.collapse.all=
page.label.collections=
page.label.color.toggle=
page.label.combo.box=
page.label.communications.orders.list=
page.label.compare.content.with=
page.label.compare.definition=
page.label.compare.production=
page.label.compare.to=
page.label.compare.wip=
page.label.compare=
page.label.comparison.content=
page.label.comparison=
page.label.components=
page.label.composition.packages.deleted=
page.label.composition.tags=
page.label.composition=
page.label.compound.format.type.semicolon=
page.label.conditional.enable=
page.label.configurable.links=
page.label.configuration=
page.label.confirm.activate.all.insert.schedules=
page.label.confirm.assets.to.be.removed=
page.label.confirm.bulk.delete=
page.label.confirm.change.owner=
page.label.confirm.clear.metadata=
page.label.confirm.clone.rationalizer.application=
page.label.confirm.clone.to.global=
page.label.confirm.clone.to.local=
page.label.confirm.create.local=
page.label.confirm.create.touchpoint.project=
page.label.confirm.deactivate.all.insert.schedules=
page.label.confirm.default.package.change=
page.label.confirm.delete.brand.profile=
page.label.confirm.delete.cloned.rationalizer.application=
page.label.confirm.delete.data.file=
page.label.confirm.delete.data.resource=
page.label.confirm.delete.de.server=
page.label.confirm.delete.dictionary=
page.label.confirm.delete.list.style=
page.label.confirm.delete.metadata.form=
page.label.confirm.delete.project=
page.label.confirm.delete.rationalizer.application=
page.label.confirm.delete.rationalizer.content=
page.label.confirm.delete.rationalizer.document=
page.label.confirm.delete.rationalizer.query=
page.label.confirm.delete.task=
page.label.confirm.delete.test.suite=
page.label.confirm.delete.user=
page.label.confirm.delete.workflow=
page.label.confirm.disable.dictionary=
page.label.confirm.download=
page.label.confirm.enable.dictionary=
page.label.confirm.generate.report=
page.label.confirm.initialize=
page.label.confirm.language.repair=
page.label.confirm.make.global=
page.label.confirm.make.local=
page.label.confirm.mark.task.complete=
page.label.confirm.move.to.global=
page.label.confirm.move.to.local=
page.label.confirm.pull.from.global=
page.label.confirm.push.to.global=
page.label.confirm.remove.additional.file=
page.label.confirm.reopen.task=
page.label.confirm.rerun.test.suite=
page.label.confirm.restore.locale=
page.label.confirm.scan.domain=
page.label.confirm.soft.deactivate.user=
page.label.confirm.unsubscribe=
page.label.confirm.upgrade.domain=
page.label.confirm.upgrade.instance=
page.label.confirm.upload.blue.relay=
page.label.conflict=
page.label.connected.mini.production=
page.label.connected.production=
page.label.connected.proof=
page.label.connected.setup=
page.label.connected.test.center=
page.label.connected.visibility=
page.label.connected.workflow=
page.label.connected.zone.content=
page.label.connection.error=
page.label.connection.status=
page.label.consolidate.combine.into.shared.object=
page.label.consolidate.content=
page.label.consolidate.duplicates=
page.label.consolidate.existing=
page.label.consolidate.find=
page.label.consolidate.into.existing=
page.label.consolidate.into.new.object=
page.label.consolidate.items.found=
page.label.consolidate.make.searched.content=
page.label.consolidate.new=
page.label.consolidate.searched.content=
page.label.consolidate.select.existing=
page.label.consolidate.selected.objects=
page.label.consolidate.selected.single.object=
page.label.consolidate.selected.shared.contents=
page.label.consolidate.selected.single.shared.content=
page.label.consolidate.similarities=
page.label.consolidate=
page.label.constant=
page.label.constants.none=
page.label.constants=
page.label.contact.email=
page.label.contains=
page.label.content.action.type.clone=
page.label.content.action.type.default=
page.label.content.action.type.import=
page.label.content.action.type.sync=
page.label.content.compare=
page.label.content.container=
page.label.content.history=
page.label.content.intelligence=
page.label.content.is.different=
page.label.content.link=
page.label.content.menus=
page.label.content.metadata=
page.label.content.metatags=
page.label.content.settings=
page.label.content.source=
page.label.content.tags=
page.label.content.targeting=
page.label.content.trim.type.after=
page.label.content.trim.type.before.and.after=
page.label.content.trim.type.before=
page.label.content.trim.type.none=
page.label.contractions=
page.label.control.style.attributes=
page.label.copies.input=
page.label.copy.to.clipboard=
page.label.copy=
page.label.count=
page.label.create.local=
page.label.create.touchpoint.project=
page.label.criteria=
page.label.current.schema=
page.label.current.workflow=
page.label.current=
page.label.custom.header=
page.label.custom.masks=
page.label.custom.value=
page.label.customer.production.files.dataresource=
page.label.customer.production.files.folder=
page.label.customer.smtp.override=
page.label.customer.smtp.settings=
page.label.customize.locale=
page.label.dashboard.all=
page.label.dashboard.brand.compound_space=
page.label.dashboard.brand.lead_compound_space=
page.label.dashboard.brand.legal_mark_superscripting=
page.label.dashboard.brand.max_sentence_length=
page.label.dashboard.brand.phone_number_format=
page.label.dashboard.brand.preferred_contraction=
page.label.dashboard.brand.restricted_contraction=
page.label.dashboard.brand.restricted_term=
page.label.dashboard.brand.single_bullet_list=
page.label.dashboard.brand.single_number_list=
page.label.dashboard.brand.subtitle=
page.label.dashboard.brand.title=
page.label.dashboard.brand.url_format=
page.label.dashboard.content.objects.of.total=
page.label.dashboard.content.objects=
page.label.dashboard.documents.of.total=
page.label.dashboard.documents=
page.label.dashboard.duplicates.report=
page.label.dashboard.duplicates=
page.label.dashboard.exact.matches=
page.label.dashboard.metadata.subtitle=
page.label.dashboard.metadata.title=
page.label.dashboard.reading.flesch.kincaid.score=
page.label.dashboard.reading.flesch.kincaid=
page.label.dashboard.reading.good=
page.label.dashboard.reading.poor=
page.label.dashboard.reading.subtitle=
page.label.dashboard.reading.title=
page.label.dashboard.sentiment.average.negative=
page.label.dashboard.sentiment.average.neutral=
page.label.dashboard.sentiment.average.positive=
page.label.dashboard.sentiment.negative=
page.label.dashboard.sentiment.neutral=
page.label.dashboard.sentiment.positive=
page.label.dashboard.sentiment.subtitle=
page.label.dashboard.sentiment.title=
page.label.dashboard.similarities.count=
page.label.dashboard.similarities.subtitle=
page.label.dashboard.similarities.title=
page.label.dashboard.smart.text=
page.label.dashboard.touchpoint=
page.label.dashboard=
page.label.data.anonymizer.delimeter=
page.label.data.anonymizer=
page.label.data.break.record=
page.label.data.feed=
page.label.data.file.encoding=
page.label.data.record=
page.label.data.web.service=
page.label.de.server.type=
page.label.de.server=
page.label.de.servers=
page.label.deactivate.all=
page.label.default.canvas.width=
page.label.default.content.compare=
page.label.default.customer.level=
page.label.default.exists.for=
page.label.default.list.style=
page.label.default.value.selection=
page.label.default.workflow=
page.label.delete.cloned=
page.label.delete.content=
page.label.delete.keypair=
page.label.delete.test.suite=
page.label.delete.user.detail=
page.label.delete.user=
page.label.deleted=
page.label.delivered.to=
page.label.delivery.events.of.run.type=
page.label.dependencies.list=
page.label.dependent=
page.label.deselectable=
page.label.diagnostics.hide.stacktrace=
page.label.diagnostics.marcie=
page.label.diagnostics.show.stacktrace=
page.label.dictionaries=
page.label.dictionary.type.global=
page.label.dictionary.type.system=
page.label.dictionary.type.user=
page.label.differences.list=
page.label.do.not.send.message=
page.label.document.export.history.all=
page.label.document.export.history.none=
page.label.document.export.history.original=
page.label.document.export.history=
page.label.document.metadata=
page.label.document.metatags=
page.label.document.name=
page.label.document.origination=
page.label.document.setting=
page.label.document.tags=
page.label.documents=
page.label.does.not.contain=
page.label.does.not.equal=
page.label.doesn.not.contain=
page.label.domain.admin.branch=
page.label.domain.admin.licences=
page.label.domain.admin.maintenance=
page.label.domain.admin.system.settings=
page.label.domain.admin=
page.label.domain.controller=
page.label.domain.email.notication=
page.label.domain.languages=
page.label.domain.visibility=
page.label.double=
page.label.download.imported.XML=
page.label.download.log.file=
page.label.download.message.on.approval=
page.label.download.metadata=
page.label.download.pod.statistics=
page.label.download.public.key=
page.label.download.result=
page.label.download.results=
page.label.download.role.template=
page.label.download.similarity.report=
page.label.download.user.template=
page.label.draft=
page.label.drop.decimals.for.round.numbers=
page.label.duplex.output=
page.label.duplicate=
page.label.duplicates.count=
page.label.duplicates=
page.label.duration=
page.label.dxf=
page.label.ecatalog.application.name=
page.label.ecatalog.application=
page.label.ecatalog.interview.form.data=
page.label.ecatalog.interview.form.reference.data=
page.label.ecatalog.letter.name=
page.label.ecatalog.number.of.letters=
page.label.ecatalog.number.of.questions=
page.label.ecatalog.number.of.sections=
page.label.ecatalog.queries=
page.label.ecatalog=
page.label.edit.condition=
page.label.edit.content.metadata.template=
page.label.edit.document.metadata.template=
page.label.edit.document.metadata=
page.label.edit.document=
page.label.edit.menu.item=
page.label.edit.settings=
page.label.edit.value=
page.label.editor=
page.label.elements=
page.label.email.notication.enabled=
page.label.email.proof.emailaddress.settings=
page.label.email.proof.server.settings=
page.label.email.proof=
page.label.embedded.content.and.canvas=
page.label.embedded.content.content.type.default=
page.label.embedded.content.content.type.markup=
page.label.embedded.content.content.type.shared.freeform=
page.label.embedded.opentype.font.EOT=
page.label.empty.value=
page.label.enable.as.selector=
page.label.enable.bulleted.lists=
page.label.enable.for.connected=
page.label.enforce.legal.mark.superscripting=
page.label.enforce.minimum.height=
page.label.eot.file=
page.label.equals=
page.label.escape.tags.in.driver.data=
page.label.exact.matches.and.similarities=
page.label.exact.matches.only=
page.label.exact.matches=
page.label.exact.word.or.phrase=
page.label.example=
page.label.excel=
page.label.exchange=
page.label.execute.incloud.simulation=
page.label.execute.test.suite=
page.label.exit=
page.label.expand.all.results=
page.label.expiry.duration.one.day=
page.label.expiry.duration.one.month=
page.label.expiry.duration.one.week=
page.label.expiry.duration.six.months=
page.label.expiry.duration.three.months=
page.label.expiry.duration.two.months=
page.label.expiry.duration.year=
page.label.expiry.duration.zero.day=
page.label.export.variant.metadata.template=
page.label.external.proof.validation=
page.label.external=
page.label.feature.activation.info=
page.label.feature.activation.title=
page.label.feature.list=
page.label.field=
page.label.file.size=
page.label.file.type=
page.label.filename.pattern=
page.label.filename.variables=
page.label.fileroot.management=
page.label.fillable.forms=
page.label.filter.assets.which.are=
page.label.filter.results=
page.label.filter.tags=
page.label.find.dependencies=
page.label.find.letters.where.the.value.of=
page.label.find.menu.items=
page.label.fit.to.container=
page.label.fit.to.section.size=
page.label.fix.all=
page.label.fix=
page.label.fixed=
page.label.flesch_kincaid.grade.level=
page.label.flesch_kincaid=
page.label.flesch_readability=
page.label.flow.type=
page.label.folder=
page.label.fonts=
page.label.footer=
page.label.force.sync=
page.label.forced.proofing.primary.value=
page.label.form.format.category.none=
page.label.form.format.category.number=
page.label.form.format.category.percentage=
page.label.form.format.category.special=
page.label.form.format.category=
page.label.form.format.currency=
page.label.form.format.decimal=
page.label.form.format.location.AfterNoSpace=
page.label.form.format.location.AfterSpace=
page.label.form.format.location.BeforeNoSpace=
page.label.form.format.location.BeforeSpace=
page.label.form.format.location=
page.label.form.format.negative.parentheses=
page.label.form.format.negative.redtxt=
page.label.form.format.negative.style=
page.label.form.format.separator=
page.label.form.format.special.custom=
page.label.form.format.special.mask=
page.label.form.format.special.option=
page.label.form.format.special.phone=
page.label.form.format.special.sin=
page.label.form.format.special.zip4=
page.label.form.format.special.zip=
page.label.form.format=
page.label.format=
page.label.full.width.table=
page.label.fuzzy=
page.label.generate.audit.report=
page.label.generate.documents.and.files.report=
page.label.generate.documents.report=
page.label.generate.documents.to.messagepoint=
page.label.generate.domain.report=
page.label.generate.local.image.report=
page.label.generate.local.smart.canvas.report=
page.label.generate.local.smart.text.report=
page.label.generate.metadata.report=
page.label.generate.project.report=
page.label.generate=
page.label.generating.key.pair=
page.label.global.asset=
page.label.global.copy=
page.label.global.dashboard=
page.label.global.results.by=
page.label.global.search.results=
page.label.global.search.subtile=
page.label.global.search=
page.label.go.to.login=
page.label.groove=
page.label.group.name=
page.label.hardware.key.a=
page.label.hardware.key.b=
page.label.hardware.key.c=
page.label.hardware.key.d=
page.label.hardware.key.e=
page.label.hardware.key.f=
page.label.has.any.value=
page.label.header=
page.label.hide.filter=
page.label.hide.variables=
page.label.hide=
page.label.hide_changes=
page.label.homepage=
page.label.horizontal.alignment=
page.label.host.name=
page.label.host=
page.label.hostname=
page.label.hours.before.due.date=
page.label.html.output=
page.label.html=
page.label.identifier=
page.label.ignore=
page.label.is_empty=
page.label.is_not_empty=
page.label.image.dimension.type.fit.to.height=
page.label.image.dimension.type.fit.to.width.and.height=
page.label.image.dimension.type.fit.to.width=
page.label.image.dimension.type.fixed.height=
page.label.image.dimension.type.fixed.width.and.height=
page.label.image.dimension.type.fixed.width=
page.label.image.dimension.type.none=
page.label.image.dimension.type.relative.width=
page.label.image.dimension.type.unaltered=
page.label.image.dimensions=
page.label.image.library=
page.label.image.link=
page.label.imap=
page.label.imaps=
page.label.implied.decimal.places=
page.label.import.and.update.data.source.and.variables=
page.label.import.domains=
page.label.import.only.shared.objects=
page.label.important.message=
page.label.imported.from.xml=
page.label.importing=
page.label.inches.short=
page.label.include.all.messages=
page.label.include.feature.activation.info=
page.label.include.license.management.info=
page.label.include.user.management.info=
page.label.independent=
page.label.indicators.changed=
page.label.information=
page.label.ingestion.log=
page.label.ingestion.upload.allowLowerCase=
page.label.ingestion.upload.allowSpaces=
page.label.ingestion.upload.maxVariableLength=
page.label.ingestion.upload.variable.notation=
page.label.ingestion.upload.variable.settings=
page.label.ingestion=
page.label.inherit.values=
page.label.initialize=
page.label.injection.location=
page.label.inline.spell.checking=
page.label.input.formatting=
page.label.input=
page.label.insert.in.place=
page.label.insert.schedules.setup=
page.label.inset=
page.label.interactive.content=
page.label.interview=
page.label.ip.address=
page.label.is.domain.admin=
page.label.is.pod.admin=
page.label.is.sso.user=
page.label.italic.toggle=
page.label.job.center=
page.label.keep.together=
page.label.keep.with.next=
page.label.keep.with.previous=
page.label.keep.with=
page.label.keep=
page.label.key.authentication=
page.label.key.pair=
page.label.language.override=
page.label.last.login=
page.label.last.message.workflow.action=
page.label.last.run.date=
page.label.last.sync.date=
page.label.legacy.dxf.mode=
page.label.legal.marks=
page.label.less=
page.label.letter.content=
page.label.letter.name=
page.label.letters=
page.label.library=
page.label.license.manager=
page.label.license.sfmcjb=
page.label.limited=
page.label.line.spacing.type=
page.label.line=
page.label.linked.object=
page.label.linked.objects=
page.label.linked.touchpoint=
page.label.links=
page.label.list.box=
page.label.list.content=
page.label.list.filter.type.anyone=
page.label.list.filter.type.association=
page.label.list.filter.type.collection=
page.label.list.filter.type.disabled=
page.label.list.filter.type.editable=
page.label.list.filter.type.edited=
page.label.list.filter.type.enabled=
page.label.list.filter.type.image.library=
page.label.list.filter.type.in.any.state.not.archive=
page.label.list.filter.type.in.any.state=
page.label.list.filter.type.insert.schedule=
page.label.list.filter.type.insert=
page.label.list.filter.type.items.asc=
page.label.list.filter.type.items.desc=
page.label.list.filter.type.latest=
page.label.list.filter.type.local.image=
page.label.list.filter.type.local.text=
page.label.list.filter.type.master=
page.label.list.filter.type.myself=
page.label.list.filter.type.name.asc=
page.label.list.filter.type.name.desc=
page.label.list.filter.type.new=
page.label.list.filter.type.others=
page.label.list.filter.type.rationalizer.content=
page.label.list.filter.type.rationalizer.shared=
page.label.list.filter.type.smart.text=
page.label.list.filter.type.target.group=
page.label.list.filter.type.touchpoint=
page.label.list.filter.type.unchangeable=
page.label.list.filter.type.unedited=
page.label.list.spacing.header=
page.label.list.spacing=
page.label.list.style.composition=
page.label.list.style.customization=
page.label.list.style=
page.label.list.styles.info=
page.label.list.styles=
page.label.list.tag.filter.type.collection=
page.label.list.tag.filter.type.output.file=
page.label.list.tag.filter.type.recipient=
page.label.lists=
page.label.loading.data.source=
page.label.loading.rules=
page.label.loading.target.groups=
page.label.local.domain=
page.label.local.global.management=
page.label.local.image.library=
page.label.local.image=
page.label.local.images=
page.label.local.smart.canvas=
page.label.local.smart.text=
page.label.local.sso=
page.label.locale.customization=
page.label.log.differences=
page.label.lookup.expression=
page.label.lookup.table.setup=
page.label.lookup.table=
page.label.lookup.tables=
page.label.make.global=
page.label.make.local=
page.label.manager=
page.label.map=
page.label.marcie.can.create.application=
page.label.marcie.can.run.job=
page.label.marcie.server.alive=
page.label.mark.complete=
page.label.markup=
page.label.max.Length=
page.label.message.and.content.targeting=
page.label.message.details=
page.label.message.expired=
page.label.message.flow.type.any=
page.label.message.flow.type.first.or.only=
page.label.message.flow.type.first=
page.label.message.flow.type.last.or.only=
page.label.message.flow.type.last=
page.label.message.flow.type.not.first.not.last=
page.label.message.flow.type.not.first=
page.label.message.flow.type.not.last=
page.label.message.flow.type.only=
page.label.message.for=
page.label.message.metadata=
page.label.message.workflow=
page.label.messagepoint.exchange=
page.label.messagepoint.pinc.apitoken=
page.label.messagepoint.pinc.application=
page.label.messagepoint.pinc.company=
page.label.messagepoint.pinc.encryption=
page.label.messagepoint.pinc.engine=
page.label.messagepoint.pinc.executable=
page.label.messagepoint.pinc.file=
page.label.messagepoint.pinc.job=
page.label.messagepoint.pinc.jobmanager=
page.label.messagepoint.pinc.mpinstance=
page.label.messagepoint.pinc.role=
page.label.messagepoint.pinc=
page.label.metadata.field.size.type.extra.large=
page.label.metadata.field.size.type.five.character=
page.label.metadata.field.size.type.full.width=
page.label.metadata.field.size.type.large=
page.label.metadata.field.size.type.medium=
page.label.metadata.field.size.type.one.character=
page.label.metadata.field.size.type.small=
page.label.metadata.field.size.type.two.character=
page.label.metadata.form.setup=
page.label.metadata.forms.setup=
page.label.metadata.forms=
page.label.metadata.input.validation.type.decimal=
page.label.metadata.input.validation.type.integer=
page.label.metadata.input.validation.type.none=
page.label.metadata.items=
page.label.metadata.optional=
page.label.metadata.properties=
page.label.metadata.refresh.type.on.ancestor.value.change=
page.label.metadata.refresh.type.on.any.value.change=
page.label.metadata.refresh.type.on.load=
page.label.metadata=
page.label.middle=
page.label.min=
page.label.minimize=
page.label.minimum.similarity.threshold=
page.label.mixed.dxf.tagged.text.mode=
page.label.modify.content.metadata=
page.label.modify.content=
page.label.modify.documents=
page.label.move.down=
page.label.move.to.global=
page.label.move.to.local=
page.label.move.up=
page.label.multi.recipient.identifier=
page.label.multiline=
page.label.multiple.selections=
page.label.multiple.values=
page.label.near.term.hours=
page.label.negative=
page.label.nest=
page.label.neutral=
page.label.never=
page.label.new.template.uploaded=
page.label.new.touchpoint.language.added=
page.label.no.access=
page.label.no.action=
page.label.no.applications=
page.label.no.assignment=
page.label.no.placeholder.content=
page.label.no.placeholder=
page.label.no.selected.items=
page.label.no.translator=
page.label.node.status.upgrade.error=
page.label.node.status.upgrade.in.progress=
page.label.none.of.these=
page.label.not.applicable=
page.label.not.enabled=
page.label.not.tested=
page.label.notification.deliveryevent=
page.label.notification.emails.cc=
page.label.notification.emails.to=
page.label.notification.emails=
page.label.notification.image.activated=
page.label.notification.image.working.copy.created=
page.label.notification.local.image.activated=
page.label.notification.local.image.working.copy.created=
page.label.notification.local.smart.activated=
page.label.notification.local.smart.text.working.copy.created=
page.label.notification.message.activated=
page.label.notification.message.working.copy.created=
page.label.notification.messages=
page.label.notification.project.complete=
page.label.notification.project.created=
page.label.notification.project.overdue=
page.label.notification.project=
page.label.notification.settings=
page.label.notification.smart.text.activated=
page.label.notification.smart.text.working.copy.created=
page.label.notification.system.task=
page.label.notification.task.created=
page.label.notification.task.near.term=
page.label.notification.task.overdue=
page.label.notification.task.reassigned=
page.label.notification.task=
page.label.notification.user.activation=
page.label.notification.user.deactivation=
page.label.notification.user.forgot.password=
page.label.notification.user.password.change=
page.label.notification.user.password.reset=
page.label.notification.user=
page.label.notification.workflow.approved.by.others=
page.label.notification.workflow.approved=
page.label.notification.workflow.due.by.auto.approval=
page.label.notification.workflow.due.by.notify=
page.label.notification.workflow.reassigned=
page.label.notification.workflow.rejected=
page.label.notification.workflow.released.for.approval=
page.label.notification.workflow.wait.for.approval=
page.label.notification.workflow.wait.for.translation=
page.label.notification.workflow=
page.label.notify.on.error.only=
page.label.number.local.msg=
page.label.number.of.duplicates=
page.label.number.of.similarities=
page.label.number.of.smart.texts=
page.label.number.users=
page.label.object.filter=
page.label.object.type.s=
page.label.object.types=
page.label.omit.first.header=
page.label.omit.last.footer=
page.label.on.approval.mini.bundle=
page.label.on.approval=
page.label.on.values.change=
page.label.one.time=
page.label.one=
page.label.only.include.selected.messages=
page.label.only.show=
page.label.onprem.servers=
page.label.operator=
page.label.option=
page.label.optional.metadata=
page.label.options=
page.label.order.entry.type.file=
page.label.order.entry.type.multiselect.menu=
page.label.order.entry.type.text.editor=
page.label.order.entry.type.web.service.multiselect.menu=
page.label.order.entry.type.web.service.text=
page.label.order.review=
page.label.origin=
page.label.original=
page.label.output.document.title=
page.label.output.file.type.afp=
page.label.output.file.type.html=
page.label.output.file.type.pdf=
page.label.output.file.type=
page.label.output.filename=
page.label.output.tags=
page.label.outset=
page.label.override.bundle.filename=
page.label.overwrite=
page.label.owner.s=
page.label.owner=
page.label.owners=
page.label.package.file.ZIP=
page.label.padding=
page.label.page.break.after=
page.label.page.break.before=
page.label.page.break.none=
page.label.page.break=
page.label.paragraph.style.composition=
page.label.paragraph.styles.default.left.indentation.info=
page.label.paragraph.styles.info=
page.label.paragraph.styles.multiple.line.spacing.info=
page.label.paragraph.styles.selectable.info=
page.label.parent.of.source=
page.label.parent.of.target=
page.label.passwordrecovery.check.your.email=
page.label.passwordrecovery=
page.label.path=
page.label.pdf.view=
page.label.pending.initial.task.complete=
page.label.pending.task.complete=
page.label.permissions=
page.label.pinc.api.tokens=
page.label.pinc.applications=
page.label.pinc.encryption.keys=
page.label.pinc.executables=
page.label.pinc.files=
page.label.pinc.jobs=
page.label.pinc=
page.label.ping.federate.sso=
page.label.ping.sso.adapter.id=
page.label.ping.sso.pickup.resolution.url=
page.label.ping.sso.start.url=
page.label.placeholder=
page.label.placeholders=
page.label.platform.32=
page.label.platform.64=
page.label.platform=
page.label.play.message.on.empty.variables=
page.label.pod.access.type.local=
page.label.pod.access.type.remote=
page.label.pod.access.type=
page.label.pod.adminn=
page.label.pod.domains.import.filename=
page.label.point.size.toggle=
page.label.pop3=
page.label.port=
page.label.portal.gateway.attributes=
page.label.positive=
page.label.possible.violations.found=
page.label.post.processing.script=
page.label.postprocessor.script1=
page.label.postprocessor.script2=
page.label.power.edit=
page.label.pre.proof=
page.label.preferred.contraction=
page.label.preferred.term=
page.label.preprocessor.script=
page.label.previous=
page.label.privacy=
page.label.private.key=
page.label.production.type=
page.label.profile.name=
page.label.project.metadata=
page.label.project.setup=
page.label.project.sync=
page.label.project.versioning=
page.label.project.workflow.manager=
page.label.project.workflows=
page.label.project=
page.label.projects=
page.label.promote=
page.label.proof.browser=
page.label.proof.desktop=
page.label.proof.mobile=
page.label.proof.recipient=
page.label.proofing.error=
page.label.proofing.general.error=
page.label.proofing.message.size.error=
page.label.public.key=
page.label.published=
page.label.pull.from.global=
page.label.purge.expired.connected.orders=
page.label.purge.expired.orphans=
page.label.purge.expired.prod.jobs=
page.label.purge.expired.proofs=
page.label.purge.expired.tests=
page.label.push.to.elastic=
page.label.push.to.global=
page.label.queries=
page.label.query.edit=
page.label.query.exact.matches=
page.label.query.filters=
page.label.query.settings=
page.label.query.similarities=
page.label.query=
page.label.question=
page.label.queued=
page.label.range=
page.label.rationalizer.application=
page.label.rationalizer.content.and.document=
page.label.rationalizer.content.f=
page.label.rationalizer.content.history=
page.label.rationalizer.content=
page.label.rationalizer.dashboard.compare.tooltip=
page.label.rationalizer.dashboard.compare=
page.label.rationalizer.dashboard=
page.label.rationalizer.document.content=
page.label.rationalizer.document.content.field.order=
page.label.rationalizer.document.content.field.messageName=
page.label.rationalizer.document.content.field.zoneConnector=
page.label.rationalizer.document=
page.label.rationalizer.export.configuration=
page.label.rationalizer.export.content=
page.label.rationalizer.export.input.file.invalid=
page.label.rationalizer.export.variation.disabled=
page.label.rationalizer.export.zones=
page.label.rationalizer.query=
page.label.rationalizer.query.consolidated.content.settings.label=
page.label.rationalizer.query.consolidated.content.settings.text=
page.label.rationalizer.query.consolidated.content.text=
page.label.rationalizer.shared.content.f=
page.label.rationalizer.shared.content=
page.label.rationalizer.shared.content.edit.content=
page.label.rationalizer.shared.content.edit.details=
page.label.rationalizer.shared=
page.label.rationalizer.upload.filename=
page.label.rationalizer.upload.info=
page.label.rationalizer.workflow=
page.label.rationalizer=
page.label.read=
page.label.realtime=
page.label.reassign.tasks=
page.label.reassigned.to=
page.label.reassignment=
page.label.receive.notification.on.deliveryevent.failure=
page.label.record.type=
page.label.recurring.content=
page.label.refresh.tree=
page.label.refreshed.segmentation.analysis=
page.label.region.left=
page.label.region.right=
page.label.regular=
page.label.reject.back.one.and.override=
page.label.reject.back.one.workflow.owner=
page.label.reject.back.one=
page.label.reject.single.bullet.lists=
page.label.reject.single.number.lists=
page.label.rejected=
page.label.relative.width=
page.label.release.date=
page.label.release.from.translation=
page.label.reminder=
page.label.remote=
page.label.remove.from.package=
page.label.remove.from.shared=
page.label.remove.item.plus.children=
page.label.rename.tp.if.duplicated=
page.label.render.content=
page.label.render.paragraph.only.when.variable.content.qualifies=
page.label.render.table.border.in.pt=
page.label.render.tagged.text=
page.label.render.variable.sample.values=
page.label.render.when.testing=
page.label.rendered.container.type.fixed.width.and.height=
page.label.rendered.container.type.fixed.width=
page.label.rendered.container.type.none=
page.label.rendered.container.type.relative.width=
page.label.rendering=
page.label.reopen=
page.label.repair.priority=
page.label.repeat.on.data.group=
page.label.repeating.zone.type.all.pages=
page.label.repeating.zone.type.on.flow=
page.label.replace.with=
page.label.replace=
page.label.requested.by=
page.label.required.fields.missing=
page.label.requirement=
page.label.rerun.all.test.scenarios=
page.label.rerun.test.suite=
page.label.resave.content=
page.label.reset.tp.content.changed.flag=
page.label.resolve.variable.values=
page.label.restore.locale=
page.label.restrict.use.of.these.words=
page.label.restricted.contraction=
page.label.restricted.contractions=
page.label.restricted.quotes=
page.label.restricted.term=
page.label.restricted.terms=
page.label.restricted.upper=
page.label.result=
page.label.resynchronization=
page.label.retry.connection=
page.label.return.to.home.page=
page.label.return.to.login=
page.label.revert.tooltip=
page.label.revert=
page.label.ridge=
page.label.role.id=
page.label.rotation=
page.label.row.height=
page.label.row=
page.label.rows=
page.label.rule.list=
page.label.rules.none.found=
page.label.run.in.background=
page.label.run.new.test.scenarios=
page.label.same.as.language=
page.label.same.as.variant=
page.label.sample.value=
page.label.save.and.add=
page.label.save.and.cascade=
page.label.save.and.go.to.list=
page.label.save.and.preview=
page.label.save.and.query=
page.label.save.and.submit=
page.label.save.and.view=
page.label.save.back=
page.label.save.previous=
page.label.saved.but.nothing.changed=
page.label.scan.domain=
page.label.schedule.has.expired=
page.label.schema.guid=
page.label.script=
page.label.search.and.replace=
page.label.search.automatic=
page.label.search.by=
page.label.search.content.similar.to=
page.label.search.for=
page.label.section.name=
page.label.sections.and.zones=
page.label.segmentation.enabled=
page.label.select.all.instances=
page.label.select.items.to.compare=
page.label.select.object=
page.label.select.one=
page.label.selectable=
page.label.selected.items=
page.label.selected.touchpoints=
page.label.send.message.anyway=
page.label.sentence.length=
page.label.sentences.should.be.shorter.than=
page.label.separate=
page.label.separator.for.bundled.image.filenames=
page.label.serach.with.input=
page.label.server.access=
page.label.server.hostname=
page.label.server.port=
page.label.set.condition=
page.label.set.usage=
page.label.set.value=
page.label.sftp.settings=
page.label.sftprepo.bulksize=
page.label.sftprepo.folder=
page.label.sftprepo.host=
page.label.sftprepo.password=
page.label.sftprepo.port=
page.label.sftprepo.settings=
page.label.sftprepo.sync=
page.label.sftprepo.syncfolder=
page.label.sftprepo.syncwith=
page.label.sftprepo.title.syncfolder=
page.label.sftprepo.upload=
page.label.sftprepo.user=
page.label.shared.clipboard=
page.label.shared.content.items.removed=
page.label.shared.content.object=
page.label.shared.content.tags=
page.label.shared.from=
page.label.shared.separate.message=
page.label.show.filter=
page.label.show.markup=
page.label.show.variables=
page.label.sign.in=
page.label.signin.marketing.url=
page.label.similarities.count=
page.label.similarities=
page.label.similarity.compare.all.content=
page.label.similarity.compare.results.to.all=
page.label.similarity.report=
page.label.similarity=
page.label.simple.search=
page.label.skip.interactive.if.no.connected.zones.exist=
page.label.skipped=
page.label.smart.canvas=
page.label.smart.object=
page.label.smart.text=
page.label.smart.texts=
page.label.socialize.proof=
page.label.socialize.proofs=
page.label.soft.deactivate=
page.label.soft.deactivated=
page.label.soft.deactivation.settings=
page.label.source.document=
page.label.source.domain=
page.label.source.instance=
page.label.spacing.bottom=
page.label.spacing.left=
page.label.spacing.right=
page.label.spacing.top=
page.label.specific.date=
page.label.split.tables=
page.label.sql.query=
page.label.sql=
page.label.ssh.key=
page.label.sso.user.auto.creation.enabled=
page.label.sso=
page.label.start.data.group=
page.label.start.on.odd.page=
page.label.state.bundling=
page.label.state.forced.packing=
page.label.state.postprocessing=
page.label.state.preprocessing=
page.label.state.processing.report=
page.label.state.processing.result=
page.label.state.processing=
page.label.state.receiving.result=
page.label.state.sending=
page.label.state.waiting.for.result=
page.label.state.waiting.in.queue=
page.label.state.warning=
page.label.state=
page.label.statistics=
page.label.stats.no_pr=
page.label.stats.pr=
page.label.stopped=
page.label.string.similarity.cosine.similarity.alternate=
page.label.string.similarity.cosine.similarity=
page.label.string.similarity.description.cosine.similarity=
page.label.string.similarity.description.jaccard.index=
page.label.string.similarity.description.jaro.winkler=
page.label.string.similarity.description.metric.longest.common.subsequence=
page.label.string.similarity.description.n.gram=
page.label.string.similarity.description.normalized.levenshtein=
page.label.string.similarity.description.sorensen.dice.coefficient=
page.label.string.similarity.jaccard.index=
page.label.string.similarity.jaro.winkler=
page.label.string.similarity.metric.longest.common.subsequence=
page.label.string.similarity.n.gram=
page.label.string.similarity.normalized.levenshtein=
page.label.string.similarity.sorensen.dice.coefficient=
page.label.structure=
page.label.structured.content=
page.label.style.composition.type.attributes=
page.label.style.composition.type.name.and.attributes=
page.label.style.composition.type.name=
page.label.submit.format=
page.label.submit.method=
page.label.subscribe=
page.label.subscribed=
page.label.suites=
page.label.superscript.all.legal.marks=
page.label.supported.languages=
page.label.suppress.noneditable.zones=
page.label.sync.compare.active.only=
page.label.sync.direction=
page.label.sync.multiway=
page.label.sync.priority.put.new.wc.after.ac=
page.label.sync.with=
page.label.sync=
page.label.synch.in.update=
page.label.synch.in=
page.label.synch.out.commit=
page.label.synch.out=
page.label.synch.with.parent=
page.label.synch.with.sibling=
page.label.system.default=
page.label.system.task=
page.label.system.tasks=
page.label.system.variable.type.bundle.date=
page.label.system.variable.type.index.0.based=
page.label.system.variable.type.index.1.based=
page.label.system.variable.type.page.count=
page.label.system.variable.type.page.number=
page.label.system.variable.type.processing.date=
page.label.system.variable.type.recipient.count=
page.label.system.variable.type.recipient.index=
page.label.system.variable.type.recipient.page.count=
page.label.system.variable.type.recipient.sheet.count=
page.label.system.variable.type.sheet.count=
page.label.system.variable.type.total.page.count=
page.label.system.variable.type.total.sheet.count=
page.label.tab.order=
page.label.table.spacing=
page.label.tag.cloud=
page.label.tag.content=
page.label.tag.documents=
page.label.tag.injection.location.type.bottom=
page.label.tag.injection.location.type.top=
page.label.tagged.text=
page.label.tagging.override=
page.label.tags.create.data.elements=
page.label.tags.create.variables=
page.label.tags.import.filename=
page.label.target.groups.none.found=
page.label.target.id=
page.label.target.rule=
page.label.targetgroup.group_details=
page.label.targetgroup.list=
page.label.task.assets=
page.label.task.created.for.me=
page.label.task.metadata=
page.label.task.setup=
page.label.task.status.type.active=
page.label.task.status.type.cancelled=
page.label.task.status.type.complete=
page.label.task.status.type.edit=
page.label.task.status.type.near.term=
page.label.task.status.type.overdue=
page.label.task.status.type.planning=
page.label.task.status.type.translation=
page.label.task.status.type.workflow=
page.label.test.connection=
page.label.test.order=
page.label.test.suite=
page.label.test.suites=
page.label.testing.connection=
page.label.text.style.composition=
page.label.text.to.display=
page.label.thumbnail=
page.label.times=
page.label.title=
page.label.todays.date=
page.label.together=
page.label.toggle.instance=
page.label.top=
page.label.touchpoint.access.control=
page.label.touchpoint.cloned=
page.label.touchpoint.combine.within.document=
page.label.touchpoint.content.setup=
page.label.touchpoint.content=
page.label.touchpoint.context.disabled=
page.label.touchpoint.context.view.type=
page.label.touchpoint.created=
page.label.touchpoint.default.language.changed=
page.label.touchpoint.deleted=
page.label.touchpoint.dot=
page.label.touchpoint.exchange.update=
page.label.touchpoint.exchange=
page.label.touchpoint.export.combine.to.single.message=
page.label.touchpoint.export.config=
page.label.touchpoint.export.matched.embedded.contents.no.collision=
page.label.touchpoint.export.matched.embedded.contents.with.collision=
page.label.touchpoint.export.matched.messages.no.collision=
page.label.touchpoint.export.matched.messages.with.collision=
page.label.touchpoint.export.matched.variables=
page.label.touchpoint.export.matched.variants=
page.label.touchpoint.export.messages.collision=
page.label.touchpoint.export.messagespoint.unmatched.variables.data.element=
page.label.touchpoint.export.missing.data.source=
page.label.touchpoint.export.path=
page.label.touchpoint.export.rationalizer.unmatched.messages.document.name=
page.label.touchpoint.export.rationalizer.unmatched.messages.document.path=
page.label.touchpoint.export.requested=
page.label.touchpoint.export.smart.texts.collision=
page.label.touchpoint.export.table.item.number.limitation=
page.label.touchpoint.export.toogle.new=
page.label.touchpoint.export.toogle.update=
page.label.touchpoint.export.unmatched.messagepoint.embedded.contents=
page.label.touchpoint.export.unmatched.messagepoint.messages=
page.label.touchpoint.export.unmatched.messagepoint.variables=
page.label.touchpoint.export.unmatched.messagepoint.variants=
page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.content=
page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.name=
page.label.touchpoint.export.unmatched.rationalizer.messages.table.header.order=
page.label.touchpoint.export.unmatched.rationalizer.messages=
page.label.touchpoint.export.unmatched.rationalizer.shared.objects=
page.label.touchpoint.export.unmatched.rationalizer.variables=
page.label.touchpoint.export.unmatched.rationalizer.variants=
page.label.touchpoint.export.unmatched.rationalizer.zoneconnector.unknown=
page.label.touchpoint.export.variables=
page.label.touchpoint.export=
page.label.touchpoint.info=
page.label.touchpoint.language=
page.label.touchpoint.languages.deleted=
page.label.touchpoint.metadata=
page.label.touchpoint.production.change.status=
page.label.touchpoint.project.sync=
page.label.touchpoint.project=
page.label.touchpoint.s=
page.label.touchpoint.section.toggle=
page.label.touchpoint.selection=
page.label.touchpoint.to.be.updated=
page.label.touchpoint.update=
page.label.touchpoint.variation=
page.label.touchpoint.versioning=
page.label.touchpoint.widget=
page.label.touchpoint.zone.toggle=
page.label.touchpoint.zone=
page.label.touchpoints.collections=
page.label.toucpoint.version.sync=
page.label.transient=
page.label.translation.from=
page.label.translation.needed=
page.label.translator=
page.label.tree.hide.unselected=
page.label.tree.show.all=
page.label.trim.empty.paragraphs=
page.label.truetype.font.OTF=
page.label.truetype.font.TTF=
page.label.ttf-otf.file=
page.label.two.factor.authentication=
page.label.unable.to.connect=
page.label.unavailable.connection=
page.label.unavailable.credentials=
page.label.unchanged.since.last.run=
page.label.unchecked=
page.label.underline.toggle=
page.label.undo=
page.label.unique.content=
page.label.unique.value=
page.label.uniques=
page.label.units=
page.label.unknown.error=
page.label.unpublished=
page.label.unrestricted.search=
page.label.unsubscribe=
page.label.update.content.library.content=
page.label.update.embedded.content.content=
page.label.update.existing.properties=
page.label.update.lookup.table.workflow=
page.label.update.project.workflow=
page.label.update.staged.production.bundle=
page.label.update.test.suite=
page.label.update.touchpoint.using.xml=
page.label.update.touchpoint=
page.label.update.workflow=
page.label.upgrade.domain=
page.label.upgrade.instance=
page.label.upload.blue.relay=
page.label.upload.complete.wait.for.indexing=
page.label.upload.complete=
page.label.upload.content.metadata.template=
page.label.upload.document.metadata.template=
page.label.upload.documents=
page.label.upload.existing.users=
page.label.upload.ignore=
page.label.upload.ingest.manifest=
page.label.upload.make.unique=
page.label.upload.new.users=
page.label.upload.policy=
page.label.upload.remove.and.replace=
page.label.upload.tags=
page.label.upload.users=
page.label.upload.variant.metadata=
page.label.uploaded.file.pass.through=
page.label.uploaded.file.type.driver=
page.label.uploaded.file.type.none=
page.label.uploaded.file.type.reference=
page.label.usage.options=
page.label.usage.type=
page.label.use.connected.beta=
page.label.use.fast.similarity.query=
page.label.use.name.to.find.shared.objects=
page.label.use.ssl=
page.label.use.these.contractions=
page.label.used.by=
page.label.user.has.no.access=
page.label.user.settings.for.current.instance=
page.label.v.align=
page.label.valid.connection=
page.label.validate.production.bundle=
page.label.validation=
page.label.value.alignment=
page.label.values=
page.label.allow.variables =
page.label.variable.type.driver=
page.label.variable.type.lookup.expression=
page.label.variable.type.script=
page.label.variable.type.system=
page.label.variant.hierarchy=
page.label.variant.metadata=
page.label.variant.selection=
page.label.variant.workflow=
page.label.version=
page.label.versioned=
page.label.versions=
page.label.vertical.align.type.bottom=
page.label.vertical.align.type.middle=
page.label.vertical.align.type.top=
page.label.vertical.alignment=
page.label.view.all.composition.tags=
page.label.view.compare.report=
page.label.view.item=
page.label.view.report=
page.label.web.font.name=
page.label.web.link=
page.label.web.service.audit.report=
page.label.web.service.client.bundle=
page.label.web.service.composition.package.update=
page.label.web.service.composition.package.upload=
page.label.web.service.connected.approve=
page.label.web.service.connected.clone=
page.label.web.service.connected.create=
page.label.web.service.connected.initiateproduction=
page.label.web.service.connected.order.list=
page.label.web.service.connected.release.for.approval.or.activate=
page.label.web.service.connected.remove=
page.label.web.service.content.search=
page.label.web.service.create.data.resource=
page.label.web.service.create.sandbox.instance=
page.label.web.service.data.anonymize=
page.label.web.service.data.source.list=
page.label.web.service.deactivate.user=
page.label.web.service.delete.file=
page.label.web.service.delivery.report=
page.label.web.service.diagnostics.bundle=
page.label.web.service.diagnostics=
page.label.web.service.domain.report=
page.label.web.service.download.background.image=
page.label.web.service.download.data.file=
page.label.web.service.download.file.chunk=
page.label.web.service.edit.task=
page.label.web.service.edit.test=
page.label.web.service.get.background.task.output.file=
page.label.web.service.get.background.task.status=
page.label.web.service.import.job.stats=
page.label.web.service.import.transactional.stats=
page.label.web.service.job.performance.report=
page.label.web.service.job.status=
page.label.web.service.list.composition.packages=
page.label.web.service.list.connectedorder=
page.label.web.service.list.data.files=
page.label.web.service.list.data.resources=
page.label.web.service.list.files=
page.label.web.service.list.tests=
page.label.web.service.list.touchpoint.collections=
page.label.web.service.list.touchpoints=
page.label.web.service.list.variants=
page.label.web.service.message.audit.report=
page.label.web.service.message.list=
page.label.web.service.metadata.report=
page.label.web.service.node.approval=
page.label.web.service.node.list=
page.label.web.service.node.to.node.copy=
page.label.web.service.pods.report=
page.label.web.service.project.audit.report=
page.label.web.service.project.list=
page.label.web.service.remove.task=
page.label.web.service.remove.test=
page.label.web.service.rerun.test=
page.label.web.service.server.bundle=
page.label.web.service.sftprepo.sync=
page.label.web.service.targeting.groups.search=
page.label.web.service.targeting.rules.search=
page.label.web.service.task.list=
page.label.web.service.touchpoint.delivery.event=
page.label.web.service.touchpoint.export.result=
page.label.web.service.touchpoint.export=
page.label.web.service.touchpoint.import=
page.label.web.service.touchpoint.production.job.list=
page.label.web.service.unified.login.report=
page.label.web.service.upload.data.file=
page.label.web.service.upload.file.chunk=
page.label.web.service.upload.job.report=
page.label.web.service.upload.processed.job=
page.label.web.service.users.report=
page.label.web.services.user.password=
page.label.web.services.user=
page.label.web=
page.label.whitespace=
page.label.widget.controls=
page.label.widget.type.content=
page.label.widget.type.metadata=
page.label.widget.type.my.image.approvals=
page.label.widget.type.my.images=
page.label.widget.type.my.message.approvals=
page.label.widget.type.my.messages=
page.label.widget.type.my.recently.completed.proofs=
page.label.widget.type.my.recently.completed.tests=
page.label.widget.type.my.smart.text.approvals=
page.label.widget.type.my.smart.text=
page.label.widget.type.my.task.approvals=
page.label.widget.type.my.tasks=
page.label.widget.type.my.translations=
page.label.widget.type.my.variant.approvals=
page.label.widget.type.my.variants=
page.label.with.next=
page.label.with.previous=
page.label.wizard.settings=
page.label.wizard=
page.label.workflow.assignment=
page.label.workflow.library=
page.label.workflow.tasks=
page.label.workflow.usage.type.connected=
page.label.workflow.usage.type.general=
page.label.workflow.usage.type.message=
page.label.workflow.usage.type.rationalizer=
page.label.workflow.usage.type.variant=
page.label.workflows=
page.label.xml=
page.label.zone.name=
page.lable.XML.data.attribute=
page.lable.XML.data.element=
page.lable.for.salesforce=
page.lable.messagepoint.connected=
page.lable.notification.deliveryevent.failure=
page.message.localcanvas.history.title=
page.message.localcontent.history.title=
page.message.localimage.history.title=
page.text.a.to.z=
page.text.a_target_group_is=
page.text.about.to.generate.audit.report=
page.text.activate.all.insert.schedules=
page.text.activate.assets=
page.text.activate.local.image=
page.text.activate.local.smart.canvas=
page.text.activate.local.smart.text=
page.text.add.application=
page.text.add.data.file=
page.text.add.data.resource=
page.text.add.document.note=
page.text.add.layout=
page.text.add.local.image=
page.text.add.local.smart.text=
page.text.add.metadata=
page.text.add.rationalizer.application=
page.text.add.rationalizer.document.content=
page.text.add.tags.to.rationalizer.content=
page.text.add.tags.to.rationalizer.documents=
page.text.ads.powered.by.messagepoint=
page.text.against.all.content=
page.text.all.domain.data.will.be.erased=
page.text.all.local.image.variants.have.data=
page.text.all.local.smart.text.variants.have.data=
page.text.all.messages.have.valid.priorities=
page.text.and.are.assigned.to=
page.text.and.are.created=
page.text.and.contains=
page.text.and.when.at=
page.text.anonymized.data=
page.text.any.date=
page.text.application.properties=
page.text.apply.default.brand=
page.text.applying=
page.text.archive.selected.active.local.image=
page.text.archive.selected.active.local.smart.canvas=
page.text.archive.selected.active.local.smart.text=
page.text.assign.local.image.to.user=
page.text.assign.local.smart.canvas.to.user=
page.text.assign.local.smart.text.to.user=
page.text.assigned.to=
page.text.attributes=
page.text.auto.release.on.expiry=
page.text.back.to.home.page=
page.text.belonging.to=
page.text.border.width=
page.text.bulk.delete.selected.items=
page.text.by.grouping=
page.text.cannot.sync.object.referenced=
page.text.case.sensitive=
page.text.chain.previous.component=
page.text.change.owner=
page.text.clear.row.keep.together=
page.text.click.plus.to.add.item=
page.text.click.to.add.conditional.enable.criteria=
page.text.click.to.add.value=
page.text.clone.selected.data.file=
page.text.clone.selected.local.image=
page.text.clone.selected.local.smart.canvas=
page.text.clone.selected.local.smart.text=
page.text.clone.selected.rate.schedule=
page.text.clone.selected.rationalizer.application=
page.text.clone.touchpoint=
page.text.communication.external.validation.feedback.submitted=
page.text.communication.external.validation.order.deleted=
page.text.compare.result.content.by.similarity=
page.text.compound.local.content=
page.text.confirm.touchpoint.exchange.unsubscribe=
page.text.conflict.cust.change=
page.text.conflict.model.and.cust.change=
page.text.conflict.model.change=
page.text.content.changes.see.history=
page.text.content.currently.being.edited=
page.text.content.history.is.empty=
page.text.content.metadata=
page.text.content.shared.in=
page.text.content.text=
page.text.content.which.are=
page.text.create.selected.smart.text.local=
page.text.create.touchpoint.info=
page.text.create.touchpoint.project=
page.text.create.working.copies.of.selected.local.image=
page.text.create.working.copies.of.selected.local.smart.canvas=
page.text.create.working.copies.of.selected.local.smart.text=
page.text.create.working.copy.of.selected.assets=
page.text.create.working.copy.of.selected.lookup.tables=
page.text.dashboard.brand.info=
page.text.dashboard.duplicates.info=
page.text.dashboard.metadata.info=
page.text.dashboard.reading.info=
page.text.dashboard.sentiment.info=
page.text.dashboard.similarities.info=
page.text.data.files.containing.pi.should.not.be.uploaded=
page.text.data.type=
page.text.deactivate.all.insert.schedules=
page.text.deactivate.assets=
page.text.delete.archived.lookup.table=
page.text.delete.placeholder=
page.text.delete.selected.archived.local.image=
page.text.delete.selected.archived.local.smart.canvas=
page.text.delete.selected.archived.local.smart.text=
page.text.delete.selected.brand.profile=
page.text.delete.selected.cloned.rationalizer.application=
page.text.delete.selected.data.files=
page.text.delete.selected.data.resources=
page.text.delete.selected.de.server=
page.text.delete.selected.dictionaries=
page.text.delete.selected.list.style=
page.text.delete.selected.metadata.form=
page.text.delete.selected.project=
page.text.delete.selected.rationalizer.application=
page.text.delete.selected.rationalizer.content=
page.text.delete.selected.rationalizer.document=
page.text.delete.selected.rationalizer.query=
page.text.delete.selected.task=
page.text.delete.selected.workflow=
page.text.delete.styles=
page.text.delete.test.suite=
page.text.delete.test.suites=
page.text.delete.touchpoint.part.one.a=
page.text.delete.touchpoint.part.one.b=
page.text.delete.touchpoint.part.two=
page.text.delivery.panel=
page.text.dictionaries.which.are=
page.text.disable.selected.dictionaries=
page.text.discard.local.image.working.copy=
page.text.discard.local.smart.canvas.working.copy=
page.text.discard.local.smart.text.working.copy=
page.text.discard.removed.variants.in.target.asset=
page.text.discard.working.copy.of.lookup.table=
page.text.do.you.want.to.delete.query=
page.text.document.blue.relay.not.configured=
page.text.document.metadata=
page.text.document.name=
page.text.documents=
page.text.double.click.button.to.add.tasks=
page.text.download.template=
page.text.download.test.results=
page.text.duplicate.tab.order=
page.text.dynamic.local.content=
page.text.edit.content.metadata=
page.text.edit.content.fields=
page.text.email.proof.selected.communication=
page.text.enable.selected.dictionaries=
page.text.enter.recipient.emial.for.proof.socialization=
page.text.export.datafiles.and.dataresources.for.all.other.purposes=
page.text.export.datafiles.and.dataresources.for.connected.interview=
page.text.export.datafiles.and.dataresources.for.segmentation.analysis=
page.text.export.datafiles.and.dataresources=
page.text.fast.auto.activate=
page.text.fast.edit.reassign=
page.text.fast.edit.working.copy.init=
page.text.field.name=
page.text.find.values=
page.text.fonts.upload.for=
page.text.forbidden=
page.text.gathering.result=
page.text.global.smart.text=
page.text.go.to.my.homepage=
page.text.has.completed.click.save=
page.text.highest.to.lowest=
page.text.image.library.which.are=
page.text.import.failed=
page.text.include.all.variables=
page.text.include.local.image.activated.after=
page.text.include.local.smart.canvas.activated.after=
page.text.include.local.smart.text.activated.after=
page.text.include.messages=
page.text.initialize.selected.branch=
page.text.input.part.size.position=
page.text.input.zone.size.position=
page.text.insert.schedules.which.are=
page.text.inserts.which.are=
page.text.job.center.jobs.are.stalled=
page.text.jobs.with.type.of=
page.text.layout.override.toggles=
page.text.library.items.only=
page.text.loading.data=
page.text.local.image.which.are=
page.text.local.smart.canvas.which.are=
page.text.local.smart.text.which.are=
page.text.location=
page.text.login.again=
page.text.lookup.tables.which.are=
page.text.lowest.to.highest=
page.text.main.navigation=
page.text.make.selected.image.local=
page.text.make.selected.local.image.global=
page.text.make.selected.local.smart.canvas.global=
page.text.make.selected.local.smart.text.global=
page.text.make.selected.smart.text.local=
page.text.manadatory.create.on.import=
page.text.mark.selected.task.complete=
page.text.message.out.of.sync=
page.text.modify.content.based.on.criteria=
page.text.modify.content.metadata=
page.text.modify.documents.based.on.criteria=
page.text.more=
page.text.move.selected.image.local=
page.text.move.selected.local.image.global=
page.text.move.selected.local.smart.canvas.global=
page.text.move.selected.local.smart.text.global=
page.text.move.selected.smart.text.local=
page.text.must.pass.for.target.group.to.qualify=
page.text.my.local.image.filter.does.not.apply=
page.text.my.local.smart.canvas.filter.does.not.apply=
page.text.my.local.smart.text.filter.does.not.apply=
page.text.no.applicable.paragraph.properties=
page.text.no.available.connected.data.elements=
page.text.no.available.connected.users=
page.text.no.available.data.elements=
page.text.no.collections.matching.search=
page.text.no.embedded.content.edit.permission=
page.text.no.fonts.list=
page.text.no.items.in.list.add.below=
page.text.no.licences.for.instance=
page.text.no.list.styles=
page.text.no.local.content.libraries.qualified.for.link=
page.text.no.project.workflows.available=
page.text.no.rationalizer.applications.exist=
page.text.no.rationalizer.document.content.exists=
page.text.no.tasks.assigned=
page.text.no.touchpoint.content.edit.permission=
page.text.no.unreferenced.items=
page.text.no.widgets.enabled.for.you=
page.text.no.workflow.history.actions=
page.text.no.workflow=
page.text.not.authorized.to.edit.structured.touchpoint.content=
page.text.now.you.need.to.provide.and.confirm.your.new.pasword=
page.text.of.type=
page.text.omit.first.header.on.initial.page=
page.text.omit.last.footer.on.final.page=
page.text.on.hold.local.image.not.used.in.production=
page.text.on.hold.local.smart.canvas.not.used.in.production=
page.text.on.hold.local.smart.text.not.used.in.production=
page.text.open=
page.text.order.entry.item.is.indicator=
page.text.override.part.name.toggle=
page.text.override.zone.background.toggle=
page.text.override.zone.connected.template.toggle=
page.text.override.zone.connector.toggle=
page.text.override.zone.default.list.style.toggle=
page.text.override.zone.default.paragraph.style.toggle=
page.text.override.zone.default.text.style.toggle=
page.text.override.zone.enable.toggle=
page.text.override.zone.list.styles.toggle=
page.text.override.zone.name.toggle=
page.text.override.zone.paragraph.styles.toggle=
page.text.override.zone.shared.assets.toggle=
page.text.override.zone.text.styles.toggle=
page.text.override.zone.workgroups.toggle=
page.text.passwordreset.confirmation1=
page.text.passwordreset.confirmation2=
page.text.pending.translation.from=
page.text.placeholders.which.are=
page.text.primary.navigation=
page.text.project.has.no.available.tasks=
page.text.project=
page.text.projects.which.are=
page.text.proof.validation.feedback=
page.text.pull.selected.local.image.from.global=
page.text.pull.selected.local.smart.canvas.from.global=
page.text.pull.selected.local.smart.text.from.global=
page.text.push.selected.local.image.to.global=
page.text.push.selected.local.smart.canvas.to.global=
page.text.push.selected.local.smart.text.to.global=
page.text.query.has.no.components=
page.text.rationalizer.application.being.cloned=
page.text.rationalizer.combine.as.shared=
page.text.rationalizer.indexing.in.progress=
page.text.rationalizer.navigation.no.metadata.items.selected=
page.text.rationalizer.no.navigation.structure.defined=
page.text.rationalizer.remove.from.shared=
page.text.reassign.asset.to.user=
page.text.redirecting.sessiontimeout=
page.text.remove.additional.file=
page.text.remove.layout=
page.text.remove.selected.tags=
page.text.remove.whitespace=
page.text.reopen.selected.task=
page.text.repair.lanugages=
page.text.replace.editor.content.with.history=
page.text.requester.configuration.required=
page.text.requester.has.unset.data.elements=
page.text.requires.translation.from=
page.text.rerun.test.suite.note=
page.text.rerun.test.suite=
page.text.rerun.test.suites=
page.text.restore.locale=
page.text.restore.selected.local.image=
page.text.restore.selected.local.smart.canvas=
page.text.restore.selected.local.smart.text=
page.text.save.and.preview.to.view=
page.text.scan.domain=
page.text.section.properties=
page.text.select.default.composition.package=
page.text.select.default.list.style=
page.text.select.history.items.for.compare=
page.text.select.image.to.apply=
page.text.select.local.content.library=
page.text.select.multiway.parents.and.click.apply=
page.text.select.multiway.source.parent.and.click.apply=
page.text.select.multiway.source.parent.mandatory.and.click.apply=
page.text.select.multiway.target.parent.and.click.apply=
page.text.select.only.one.asset.for.workflow=
page.text.select.sync.settings.and.click.apply=
page.text.select.touchpoint.context.to.add.test.suite=
page.text.select.touchpoint.context.to.add.test=
page.text.select.user.for.lookup.table.reassign=
page.text.select.user.for.task.reassign=
page.text.select.user.to.assign.deactivated.assets.to=
page.text.select.variable.usage.options=
page.text.select.variants.for.compare=
page.text.selected.local.image.now.used.in.production=
page.text.selected.local.smart.canvas.now.used.in.production=
page.text.selected.local.smart.text.now.used.in.production=
page.text.set.cell.horizontal.alignment=
page.text.set.cell.vertical.alignment=
page.text.set.cell.width=
page.text.set.new.password=
page.text.set.options.for.domain.report=
page.text.set.row.height=
page.text.set.row.keep.together=
page.text.set.row.keep.with=
page.text.signed.in.as=
page.text.single.selector.touchpoint.value.invalid=
page.text.smart.canvas.which.are=
page.text.soft.deactivate.selected.user=
page.text.sort.attributes.by=
page.text.sort.elements.by=
page.text.sorted.by=
page.text.specify.metadata.form.for.project=
page.text.specify.metadata.form.for.task=
page.text.specify.workflow.for.project=
page.text.static.local.content=
page.text.step.skipped=
page.text.structured.local.content=
page.text.summary.duplicates=
page.text.summary.similarities=
page.text.summary.uniques=
page.text.suppress.selected.local.image=
page.text.suppress.selected.local.smart.canvas=
page.text.suppress.selected.local.smart.text=
page.text.switch.to.master.to.edit=
page.text.sync.message.priority=
page.text.sync.retrieve.data.failed=
page.text.tag.associations.not.applicable=
page.text.tag.targeting.not.applicable=
page.text.targeting_rules_and_conditions=
page.text.tasks.which.are=
page.text.template.has.no.metadata.items=
page.text.test.suite.of=
page.text.tests.count=
page.text.the.following.named.inputs=
page.text.this.field.is.mandatory=
page.text.timing.no.end.date.info=
page.text.timing.starts.now.info=
page.text.tip.press.enter.to.search=
page.text.toggle.dropdown=
page.text.toggle.interactive.content.indicators=
page.text.touchpoint.content.not.assigned.to.user=
page.text.touchpoint.datasource.empty=
page.text.touchpoint.has.no.metadata.items=
page.text.touchpoint.not.applicable.for.shared.canvas=
page.text.touchpoint.template.downloaded=
page.text.transition=
page.text.translation.step=
page.text.translation.timeframe.is=
page.text.trash.touchpoint.does.not.apply=
page.text.turn.domain.off.line=
page.text.turn.domain.online=
page.text.upgrade.folder.locations.for.dcs=
page.text.upgrade.folder.locations.for.node=
page.text.upload.blue.relay=
page.text.upload.files.click.or.drag=
page.text.upload.files.ingestion=
page.text.upload.files=
page.text.upload.suite.blue.relay=
page.text.upload.template=
page.text.validate.selected.branch=
page.text.validator.alphanum.dot.dash.underscore.colon=
page.text.validator.alphanum.space.dot.dash.underscore.colon=
page.text.value=
page.text.variant.working.copy.required.to.edit.touchpoint.content=
page.text.view.attributes.as.a=
page.text.view.elements.as.a=
page.text.violation.description.text.bulletlist=
page.text.violation.description.text.leadspace=
page.text.violation.description.text.maxlength=
page.text.violation.description.text.numberlist=
page.text.violation.description.text.preferredcontractions=
page.text.violation.description.text.restrictedcontractions=
page.text.violation.description.text.restrictedterms=
page.text.violation.description.text.spaces=
page.text.violation.description.text.superscripting=
page.text.violation.description.title.bulletlist=
page.text.violation.description.title.leadspace=
page.text.violation.description.title.maxlength=
page.text.violation.description.title.numberlist=
page.text.violation.description.title.preferredcontractions=
page.text.violation.description.title.restrictedcontractions=
page.text.violation.description.title.restrictedterms=
page.text.violation.description.title.spaces=
page.text.violation.description.title.superscripting=
page.text.visit=
page.text.warning.no.master.variant.workflow.assignment=
page.text.warning.no.workflow.assigned=
page.text.warning.object.change=
page.text.wc.and.active=
page.text.welcome.back=
page.text.when.input.value.is=
page.text.where=
page.text.with=
page.text.workflow.owners.may.reject.back.one.step=
page.text.workflow.step.release.from.translation=
page.text.would.you.like.login.again=
page.text.would.you.like.reject.back.one=
page.text.would.you.like.to.activate.lookup.tables=
page.text.would.you.like.to.clear.metadata=
page.text.would.you.like.to.delete.selected.user=
page.text.would.you.like.to.download.role.template=
page.text.would.you.like.to.download.user.template=
page.text.would.you.like.to.download.variants.metadata.template=
page.text.would.you.like.to.generate.document.report=
page.text.would.you.like.to.generate.duplicates.report=
page.text.would.you.like.to.generate.metatada.report=
page.text.would.you.like.to.generate.similarity.report=
page.text.would.you.like.to.sftprepo.sync.folder=
page.text.would.you.like.to.sftprepo.sync=
page.text.you.are.almost.done=
page.text.yourpersonalsettings.ssoenabled=
page.text.z.to.a=
page.text.zone.absolute.position=
page.title.ecatalog.import.heading=
page.title.signin.marketing=
page.title.touchpoint.import.to.update.confirm.heading=
page.tooltip.add.content=
page.tooltip.compare.content=
page.tooltip.edit.content.metadata=
page.tooltip.edit.content=
page.tooltip.merge.content=
page.tooltip.move.down=
page.tooltip.move.up=
page.tooltip.split.content=
page.tooltip.view.content.metadata=
page.tooltip.view.content=
service.ecatatlog.application.not.valid=
service.email.alternate.introducing.new.zone.or.modifier=
service.import.domain.does.not.exist=
service.import.ecatalog.application.does.not.exist=
service.import.required.value.missing.with.missing.name=
service.import.required.value.missing=
service.import.touchpoint.guid.exists=
service.validation.message.cloneLookupTable.lookupTableDoesNotExist=
sftprepo.async.service.error.invalid.folder=
sftprepo.async.service.error.invalid.parameter=
sftprepo.async.service.error.listing.folder=
sftprepo.async.service.error.request.fail=
sftprepo.async.service.error.too.much.image=
static.ecatalog.question.type.menu=
static.ecatalog.question.type.multi.select.nested=
static.ecatalog.question.type.multi.select.text=
static.ecatalog.question.type.multi.select=
static.ecatalog.question.type.radio=
static.ecatalog.question.type.textfield=
static.type.connector.sefas=
static.type.connector.sfmc_journeybuilder=
static.type.content.selection.type.activated=
static.type.content.selection.type.archived=
static.type.content.selection.type.deleted=
static.type.content.selection.type.different_languages=
static.type.content.selection.type.different_usages=
static.type.content.selection.type.first_time_sync=
static.type.content.selection.type.not_applicable=
static.type.content.selection.type.same=
static.type.content.selection.type.unassociated=
static.type.content.selection.type.unknown=
static.type.content.type.markup=
static.type.content.type.shared.freeform=
static.type.content.type.text.or.graphic=
static.type.deserver.communication.type.dews=
static.type.deserver.communication.type.scp=
static.type.deserver.communication.type.sftp=
static.type.deserver.communication.type.stop=
static.type.line.spacing.type.fixed=
static.type.line.spacing.type.multiple=
static.type.qualification.output.HTML=
static.type.qualification.output.xHTML=
static.type.readability.12thgrade=
static.type.readability.5thgrade=
static.type.readability.6thgrade=
static.type.readability.7thgrade=
static.type.readability.9thgrade=
static.type.readability.college=
static.type.readability.description.12thgrade=
static.type.readability.description.5thgrade=
static.type.readability.description.6thgrade=
static.type.readability.description.7thgrade=
static.type.readability.description.9thgrade=
static.type.readability.description.college=
static.type.readability.description.graduate=
static.type.readability.graduate=
static.type.source.type.lookup.table=
static.type.sync.object.type.all=
static.type.sync.object.type.content.library=
static.type.sync.object.type.data_collection=
static.type.sync.object.type.data_source=
static.type.sync.object.type.document=
static.type.sync.object.type.document_setting=
static.type.sync.object.type.local.image=
static.type.sync.object.type.local.smart.canvas=
static.type.sync.object.type.local.smart.text=
static.type.sync.object.type.lookup_table=
static.type.sync.object.type.message=
static.type.sync.object.type.parameter.group=
static.type.sync.object.type.section=
static.type.sync.object.type.smart.text=
static.type.sync.object.type.target.group=
static.type.sync.object.type.target.rule=
static.type.sync.object.type.variable=
static.type.sync.object.type.variant=
static.type.sync.object.type.zone=
static.type.zone.type.footer.zone=
static.type.zone.type.header.zone=
static.type.zone.type.markup.zone=
static.type.zone.type.placeholder.zone=
verbiage.version.reason.imported.lookup.table=
verbiage.version.reason.new.lookup.table=
xslt_messages.active.connected.users=
xslt_messages.active.regular.users=
xslt_messages.active.workflow.users=
xslt_messages.approved.by=
xslt_messages.approvers=
xslt_messages.assingee=
xslt_messages.comments=
xslt_messages.commit=
xslt_messages.connected.details=
xslt_messages.connected.orders.index=
xslt_messages.connected.orders=
xslt_messages.connectors=
xslt_messages.created.by=
xslt_messages.createddate=
xslt_messages.current.touchpoint=
xslt_messages.details=
xslt_messages.direction=
xslt_messages.domain.specification=
xslt_messages.domain=
xslt_messages.domains=
xslt_messages.feature.activation=
xslt_messages.features=
xslt_messages.final.approver=
xslt_messages.first.name=
xslt_messages.friendly.name=
xslt_messages.guid=
xslt_messages.id=
xslt_messages.idp.type=
xslt_messages.image.libraries=
xslt_messages.image.library.index=
xslt_messages.images.libraries=
xslt_messages.include.all.messages=
xslt_messages.include.feature.activation.info=
xslt_messages.include.license.management.info=
xslt_messages.include.user.management.info=
xslt_messages.index.of.domains=
xslt_messages.inserts=
xslt_messages.instance.specification=
xslt_messages.instance=
xslt_messages.instances=
xslt_messages.languages=
xslt_messages.last.activation.date=
xslt_messages.last.login=
xslt_messages.last.name=
xslt_messages.last.production.date=
xslt_messages.licence.management=
xslt_messages.license.from=
xslt_messages.license.type=
xslt_messages.list.of.domains=
xslt_messages.list.of.hidden.users=
xslt_messages.list.of.pods=
xslt_messages.list.of.visible.users=
xslt_messages.list.style=
xslt_messages.local.image=
xslt_messages.local.images=
xslt_messages.local.smart.canvas=
xslt_messages.local.smart.text=
xslt_messages.local.smart.texts=
xslt_messages.mapping.of.domains=
xslt_messages.master.schema.name=
xslt_messages.maximum.connected.users=
xslt_messages.maximum.regular.users=
xslt_messages.maximum.workflow.users=
xslt_messages.metatags=
xslt_messages.next.action=
xslt_messages.no.direct.reference.for.this.asset=
xslt_messages.no.events.for.model=
xslt_messages.no.messages.for.this.selection=
xslt_messages.no.tasks.assigned=
xslt_messages.paragraph.style=
xslt_messages.parent.touchpoint=
xslt_messages.parent=
xslt_messages.pod.description=
xslt_messages.pod.type=
xslt_messages.prerelease=
xslt_messages.production=
xslt_messages.project.audit.report=
xslt_messages.project.content=
xslt_messages.project.details=
xslt_messages.project.duedate=
xslt_messages.project.id=
xslt_messages.project.index=
xslt_messages.project.name=
xslt_messages.project.or.projects=
xslt_messages.project.owner=
xslt_messages.project.requirement=
xslt_messages.project.status=
xslt_messages.project.task.index=
xslt_messages.project.tasks=
xslt_messages.project=
xslt_messages.projects=
xslt_messages.proof=
xslt_messages.recipient=
xslt_messages.rejected.by=
xslt_messages.requested.for.pod=
xslt_messages.same.as.english=
xslt_messages.schema.name=
xslt_messages.selected.local.images.index=
xslt_messages.selected.local.smart.canvas.index=
xslt_messages.selected.local.smart.texts.index=
xslt_messages.selected.objects.index=
xslt_messages.selected.objects=
xslt_messages.selections=
xslt_messages.siblingparent.touchpoint=
xslt_messages.smart.canvas=
xslt_messages.smart.texts=
xslt_messages.sso.id=
xslt_messages.sso.type=
xslt_messages.sub.domains=
xslt_messages.sync.with.source=
xslt_messages.sync.with.target=
xslt_messages.target.group=
xslt_messages.target.groups=
xslt_messages.text.style=
xslt_messages.type=
xslt_messages.update=
xslt_messages.url=
xslt_messages.user.access=
xslt_messages.user.management=
xslt_messages.username=
xslt_messages.variants=
xslt_messages.workflow.histories=
MPS0010001=äºæããªãã·ã¹ãã  ã¨ã©ã¼ã§ããè©³ç´°ã«ã¤ãã¦ã¯ã­ã°ãåç§ãã¦ãã ããã
MPS0010002=ãµã¼ãã¹åãç¡å¹ã§ãã
MPS0010003=ã¢ãã« ãªãã¸ã§ã¯ããå¿è¦ã§ããå­å¨ãã¾ããã
MPS0010004=ã¢ãããã¼ã¿ã¼ã¨ãã¦ä½¿ç¨ã§ããã¦ã¼ã¶ã¼ ID ãããã¾ããã
MPS0010005=ã¦ã¼ã¶ã¼ ID ã§ã¦ã¼ã¶ã¼ãè¦ã¤ãããã¨ãã§ãã¾ããã
MPS0010006=ãµã¼ãã¹ã®æ¤è¨¼ä¸­ã«äºæããªãä¾å¤ãçºçãã¾ããã
MPS0010007=ååãå¿è¦ã§ããå­å¨ãã¾ããã
MPS0010008=ååã¯è±æ°å­ã§ãªããã°ãªãã¾ããã
MPS0010009=ãµã¼ãã¹ã®å®è¡ä¸­ã«äºæããªãä¾å¤ãçºçãã¾ããã
MPS0010010=ãã¡ã¤ã«ãå­å¨ãã¾ããã
MPS0010011=ãã¡ã¤ã«ãéãã¨ãã«ä¾å¤ãçºçãã¾ããã
MPS0010012=ä½¿ç¨åã«ãµã¼ãã¹ãæ¤è¨¼ããã¦ãã¾ããã
MPS0010014=ãµã¼ãã¹ã«å¯¾ãããµã¼ãã¹è¦æ±ã®ã¿ã¤ããééã£ã¦ãã¾ãã
MPS0010015=æå®ããã¹ãã¼ã¿ã¹ãç¡å¹ã§ãã
MPS0010016=ãã¼ã¿ãã¼ã¹ ãã©ã³ã¶ã¯ã·ã§ã³ãã³ãããã§ãã¾ããã§ããã
MPS0010017=ãã¼ã¿ãã¼ã¹ ãã©ã³ã¶ã¯ã·ã§ã³ãéå§ã§ãã¾ããã§ããã
MPS0010018=ãã®æä½ã¯ãµãã¼ãããã¦ãã¾ããã
MPS0020001=ã¡ãã»ã¼ã¸ã®ãã¹ã¦ã®ã³ã³ãã³ãã®ã¡ãã»ã¼ã¸ ã³ã³ãã³ã ã¿ã¤ããä¸è´ãã¦ãã¾ããã
MPS0020002=ããã©ã«ãè¨èªã®ã³ã³ãã³ããå­å¨ãã¦ããå¿è¦ãããã¾ãã
MPS0020003=ã¢ããªã±ã¼ã·ã§ã³ã§ãµãã¼ãããã¦ããªãè¨èªã®ã³ã³ãã³ãã¯ç¡è¦ããã¾ãã
MPS0020004=ID ã§ã¾ã¼ã³ãè¦ã¤ãããã¨ãã§ãã¾ããã§ãã
MPS0020005=ååã«ã¯è±æ°å­ãã¹ãã¼ã¹ãããã·ã¥ãã¢ã³ãã¼ã¹ã³ã¢ã®ã¿ä½¿ç¨ãã¾ãã
MPS0020006=ã¡ãã»ã¼ã¸ã®ã¤ã³ãã¼ããæå¦ããã¾ããããã­ã¹ã ã³ã³ãã³ãã«ä»¥ä¸ã®ç¦æ­¢æå­ãå«ã¾ãã¦ãã¾ã: CRLFã%%ãã¾ãã¯ |
MPS0020008=ãã®ã¾ã¼ã³ã§ã®ã¡ãã»ã¼ã¸åªååº¦ãä»ã®åæã¦ã¼ã¶ã¼ã«ãã£ã¦æ´æ°ããã¦ãã¾ããå¤æ´åå®¹ãã³ãããã§ãã¾ãããããç´ãã¦ãã ããã
MPS0070001=è¦æ±ãªãã¸ã§ã¯ãã null ã«ãããã¨ã¯ã§ãã¾ãã
MPS0070002=ãã¡ã¤ã«åã null ã¾ãã¯ç©ºãããã¨ã¯ã§ãã¾ãã
MPS0090001=å®è¡ã®èªè¨¼ã­ã¼ãç¡å¹ã§ãã
MPS0090002=è¨ç»æ¸ã¿ã®å®è¡ã¦ã£ã³ãã¦ä»¥å¤ã§ã¯ãã¼ã¿ ã¤ã³ãã¼ãã®å®è¡ãéå§ã§ãã¾ããã
MPS0090003=åã®ã¸ã§ããå®äºãã¦ããªããããã¸ã§ããéå§ã§ãã¾ããã
MPS0090004=ã¸ã§ãã®é åºãæ­£ããããã¾ããã
MPS0090005=ãã®ããã³ãã§å¦çä¸­ã®ãã¼ã¿ ã¤ã³ãã¼ããä»ã«å­å¨ãã¾ããä¸åº¦ã«å¦çã§ãããã¼ã¿ ã¤ã³ãã¼ãã¯ããã³ããã¨ã« 1 ã¤ã®ã¿ã§ãã\t
MPS0090006=ãã¼ã¿ ã¤ã³ãã¼ããéå§ããæºåãã§ãã¦ãã¾ããããã¹ã¦ã® xml ãã¡ã¤ã«ãæ¤è¨¼ããå¿è¦ãããã¾ãã
MPS0090007=æ¢ã«å®äºãããã¼ã¿ ã¤ã³ãã¼ã ã¸ã§ãã¯åå®è¡ããã¾ããã
MPS0100001=ãã¼ã¿ ã¤ã³ãã¼ãã®ä½æä¸­ã«äºæããªãä¾å¤ãçºçãã¾ããã
MPS0100004=å¥åãã¡ã¤ã«æ°ãç¡å¹ã§ããå°ãªãã¨ã 1 ã¤ã®å¥åãã¡ã¤ã«ãæå®ããå¿è¦ãããã¾ãã
MPS0110001=è¦æ±ãªãã¸ã§ã¯ãã null ã«ãããã¨ã¯ã§ãã¾ãã
MPS0110003=è¦æ±ãªãã¸ã§ã¯ãã§ DataImport ãªãã¸ã§ã¯ãã null ã§ã
MPS0120001=ããã³ã ãã¡ã¤ã«ã®ã¾ã¼ã³ ID ã¸ã®æ¸ãè¾¼ã¿ä¸­ã«ã¨ã©ã¼ãçºçãã¾ãã  - IOException
MPS0120002=ã¤ã³ãã¼ã ãµã³ãã« zip ã§ä½¿ç¨ããã¦ããåºåãã¡ã¤ã«ã®å²ãå½ã¦ä¸­ã«ã¨ã©ã¼ãçºçãã¾ãã
MPS0120003=è¦ç´ ãã¤ã³ãã¼ã ãµã³ãã« zip ã«å§ç¸®ãã¦ããã¨ãã«ã¨ã©ã¼ãçºçãã¾ãã
MPS0140001=DataImport ã NULL ã«ãããã¨ã¯ã§ãã¾ãã
MPS0140002=ããã ã¸ã§ãã®ã³ãã³ã ã©ã¤ã³ã®çæä¸­ã«ã¨ã©ã¼ãçºçãã¾ãã
MPS0140003=ãã­ããã£ ãã¡ã¤ã«ã§åºåãã£ã¬ã¯ããªãå®ç¾©ããå¿è¦ãããã¾ã\t
MPS0140004=ãããæç¤ºãã¡ã¤ã«ã®æ¸ãè¾¼ã¿ä¸­ã«ã¨ã©ã¼ãçºçãã¾ãã
MPS0150001=ã¸ã§ã ããã«ã¼ - ãã¼ã¿ãã¼ã¹ã§éä¿¡ã¤ãã³ããè¦ã¤ããã¾ãã
MPS0150002=ã¸ã§ã ããã«ã¼ - éä¿¡ã¤ãã³ã ã¸ã§ã ã¹ãã¼ã¿ã¹ã®æ´æ°ä¸­ã«ã¨ã©ã¼ãçºçãã¾ããã
MPS0150003=ã¸ã§ã ããã«ã¼ - éä¿¡ã¤ãã³ã ã¸ã§ã ã¹ãã¼ã¿ã¹ã REQUESTED ã«å¤æ´ããã¾ããã
MPS0150004=ã¸ã§ã ããã«ã¼ - ã¸ã§ã ã¡ã¿ ãã¼ã¿ãã¬ãã¼ã ãã¼ã¿ãã¼ã¹ã«ä¿å­ããã¾ãã
MPS0150005=ã¸ã§ã ããã«ã¼ - zip ãã¡ã¤ã«ãä½¿ç¨ã§ãã¾ã
MPS0150006=ã¸ã§ã ããã«ã¼ - éä¿¡ã¤ãã³ã ã¸ã§ã ã¹ãã¼ã¿ã¹ã PACKED ã«å¤æ´ããã¾ããã
MPS0160001=ã¤ã³ãã¼ãéä¿¡ã¬ãã¼ã - ããã¯ã¨ã³ã ãã­ã»ã¹ããè¿ãããã¸ã§ã ãã³ãã«ã«éä¿¡ã¬ãã¼ã ãã¡ã¤ã«ãå«ã¾ãã¦ãã¾ããã
MPS0160002=ã¤ã³ãã¼ãéä¿¡ã¬ãã¼ã - ããã¯ã¨ã³ã ãã­ã»ã¹ããè¿ãããã¸ã§ã ãã³ãã«ã«æå¹ãªéä¿¡ã¤ãã³ã ID ãå«ã¾ãã¦ãã¾ããã
MPS0160003=ã¤ã³ãã¼ãéä¿¡ã¬ãã¼ã - ã¸ã§ã ãã³ãã« ID ãéä¿¡ã¤ãã³ã ã¸ã§ã ID ã¨ä¸è´ãã¾ããã
MPS0160004=ã¤ã³ãã¼ãéä¿¡ã¬ãã¼ã - éä¿¡ã¬ãã¼ãè§£æã¨ã©ã¼ã
MPS0170001=ã¬ãã¼ã ãã­ã»ããµã¼ - éä¿¡ã¹ãã¼ã¿ã¹ ãã¡ã¤ã« - CombinedMsgDelivery - ãã¸ã§ã ãã©ã«ãã¼åã«å­å¨ãã¾ãã
MPS0180001=å¤é¨ããªã¬ã¼ ã¤ãã³ãã®å¼ã³åºã - ãã¼ã¿ãã¼ã¹ã§ GUID ãä½¿ã£ã¦ã¤ãã³ããè¦ã¤ãããã¨ãã§ãã¾ãããã¤ãã³ãã¯å¼ã³åºããã¾ããã
MPS0180002=å¤é¨ããªã¬ã¼ ã¤ãã³ãã®å¼ã³åºã - å¤é¨ããªã¬ã¼ ã¤ãã³ãã¯ç¾å¨ç¡å¹ã¢ã¼ãã§ããã¤ãã³ãã¯å¼ã³åºããã¾ããã
MPS0180003=å¤é¨ããªã¬ã¼ ã¤ãã³ãã®å¼ã³åºã - å®è¡ä¸­ã®ã¦ã¼ã¶ã¼ ã¢ã«ã¦ã³ããã¢ã¯ãã£ãã§ã¯ããã¾ãããã¤ãã³ãã¯å¼ã³åºããã¾ããã
MPS0180004=å¤é¨ããªã¬ã¼ ã¤ãã³ãã®å¼ã³åºã - éä¿¡ã¤ãã³ããªãã¸ã§ã¯ããä½æããã¼ã¿ãã¼ã¹ã«æ¿å¥ããéã«ã¨ã©ã¼ãçºçãã¾ãããã¤ãã³ãã¯å¼ã³åºããã¾ããã
MPS0180005=å¤é¨ããªã¬ã¼ ã¤ãã³ãã®å¼ã³åºã - ãã¼ã¿ãã¼ã¹ã§ GUID ã«ããå¤é¨ããªã¬ã¼ ã¤ãã³ããè¦ã¤ããã¾ãããéä¿¡ã¤ãã³ãã®ä½æã«é²ã¿ã¾ãã
MPS0180006=å¤é¨ããªã¬ã¼ ã¤ãã³ãã®å¼ã³åºã - GUID ã«ããå¤é¨ããªã¬ã¼ ã¤ãã³ããæ­£å¸¸ã«ããªã¬ã¼ããã¾ããã
MPS0190001=é²è¡ä¸­ãã¼ã¸ã§ã³ã®ã¿ãã¢ã¯ãã£ãã«ãã§ãã¯ã¤ã³ã§ãã¾ãã
MPS0190002=ãã¼ã¸ã§ã³ä»ãã¢ãã« ãªãã¸ã§ã¯ãããã¼ã¿ãã¼ã¹ããåå¾ã§ãã¾ããã
MPS0190003=ãªãã¸ã§ã¯ãã¯æ¢ã«ãã§ãã¯ã¢ã¦ãããã¦ããããã¢ã¯ãã£ããããã§ãã¯ã¢ã¦ãã§ããç¶æã§ã¯ããã¾ããã
MPS0190004=ãã§ãã¯ ã¢ã¦ãã®ããã®ææ°ã®ãã­ãã¯ã·ã§ã³ ãã¼ã¸ã§ã³ãåå¾ã§ãã¾ããã
MPS0190005=è¤è£½ã¯ãµãã¼ãããã¦ãã¾ããã
MPS0200001=ãµã¼ãã¹è¦æ±ã null ã«ãããã¨ã¯ã§ãã¾ãã
MPS0200002=åå²ãå½ã¦ããã¡ãã»ã¼ã¸ãæå®ãã¦ãã ããã
MPS0200003=èª°ã«ä½æ¥­ãåå²å½ã¦ããããæå®ãã¦ãã ããã
MPS0200004=ã¦ã¼ã¶ã¼ã«å¥ã®ã¦ã¼ã¶ã¼ã¸ã®ä½æ¥­ã®åå²ãå½ã¦ãè¡ãæ¨©éãããã¾ããã
MPS0200005=modelInstance ã«é¢é£ä»ãããããã¼ã¸ã§ã³ ãããã³ã°ãããã¾ãã
MPS0210001=modelId ã¾ãã¯ modelInstanceId ã VersionServiceRequest ã«æ¸¡ãå¿è¦ãããã¾ã
MPS0210002=ä½æ¥­ã³ãã¼ã®ç ´æ£ã¯å²ãå½ã¦åã¦ã¼ã¶ã¼ã®ã¿ãå®è¡ã§ãã¾ãã
MPS0210003=ä½æ¥­ä¸­ã§ã¯ãªãã³ãã¼ãä¸­æ­¢ãããã¨ã¯ã§ãã¾ããã
MPS0220001=modelId ã¾ãã¯ modelInstanceId ã VersionServiceRequest ã«æ¸¡ãå¿è¦ãããã¾ãã
MPS0220002=ã¦ã¼ã¶ã¼ã«ã¢ã¼ã«ã¤ãããæ¨©éãããã¾ããã
MPS0220003=ã¡ãã»ã¼ã¸ãã¢ã¼ã«ã¤ãã§ãã¾ãããã¡ãã»ã¼ã¸ãã¢ã¯ãã£ãã§ã¯ããã¾ããã
MPS0230001=modelId ã¾ãã¯ modelInstanceId ã VersionServiceRequest ã«æ¸¡ãå¿è¦ãããã¾ãã
MPS0230002=ã¦ã¼ã¶ã¼ã«ã¢ã¼ã«ã¤ãã®åé¤ãè¡ãæ¨©éãããã¾ããã
MPS0230003=ã¡ãã»ã¼ã¸ã¯åé¤ããåã«ã¢ã¼ã«ã¤ãããå¿è¦ãããã¾ãã
MPS0240001=ã¯ã¼ã¯ã°ã«ã¼ãåã null ã«ãããã¨ã¯ã§ãã¾ããã
MPS0240002=è¦æ±ããã¯ã¼ã¯ã°ã«ã¼ãåã¯åãããã³ãåã«æ¢ã«å­å¨ãã¾ãã
MPS0240003=ã¯ã¼ã¯ã°ã«ã¼ãã UpdateWorkgroupServiceRequest ã«æ¸¡ãå¿è¦ãããã¾ãã
MPS0240004=è¦æ±ããã¾ã¼ã³ã®é¢é£ä»ããåé¤ããã¨ããã¹ã¦ã®ã·ã¹ãã  ã¦ã¼ã¶ã¼ãããã®ã¾ã¼ã³ãè¦ããªããªãã¾ãã
MPS0240005=workgroupId ã 0 ä»¥ä¸ã«ãããã¨ã¯ã§ãã¾ãã
MPS0240006=è¦æ±ããã¯ã¼ã¯ã°ã«ã¼ããå­å¨ãã¾ããã
MPS0240007=è¦æ±ããã¯ã¼ã¯ã°ã«ã¼ããåé¤ã§ãã¾ãããã¯ã¼ã¯ã°ã«ã¼ãã«ã¾ã¼ã³ãé¢é£ä»ãããã¦ãã¾ãã
MPS0240008=è¦æ±ããã¯ã¼ã¯ã°ã«ã¼ããåé¤ã§ãã¾ãããã¯ã¼ã¯ã°ã«ã¼ãã«ã¦ã¼ã¶ã¼ãé¢é£ä»ãããã¦ãã¾ãã
MPS0240009=ããã³ãã«ã¯ããã©ã«ãã®ã¯ã¼ã¯ã°ã«ã¼ããæ¢ã«å­å¨ãã¾ãã
MPS0250001=è¦æ±åã®ã¦ã¼ã¶ã¼ãæå¹ãª Messagepoint ã¦ã¼ã¶ã¼ã§ã¯ããã¾ããã
MPS0250002=è¦ªãã©ã«ãã¼ãæå¹ãªã¯ã¼ã¯ã°ã«ã¼ã ãã©ã«ãã¼ã§ã¯ããã¾ããã\t
MPS0250003=æå¤§ãã©ã«ãã¼ ã¬ãã«æ°ãè¶ãã¦ãã¾ããè¨±å®¹ãããã¬ãã«æ°ã¯ 5 ã¾ã§ã§ãã
MPS0250004=ã¯ã¼ã¯ã°ã«ã¼ã ãã©ã«ãã¼åã¯å¿é ãã£ã¼ã«ãã§ãã
MPS0250005=ã¯ã¼ã¯ã°ã«ã¼ã ãã©ã«ãã¼åãæ¢ã«ä½¿ç¨ããã¦ãã¾ããå¥ã®ååãæå®ãã¦ãã ããã
MPS0250006=æ´æ°ç¨ã«é¸æããåã®ãã©ã«ãã¼ããã¼ã¿ãã¼ã¹åã«è¦ã¤ããã¾ããã
MPS0250007=ãæ°ã«å¥ãããã³æçµè¡¨ç¤ºæ¥æã®ãã©ã«ãã¼åã¯å¤æ´ã§ãã¾ããã
MPS0250008=ãã©ã«ãã¼ãåé¤ããã«ã¯ãäºåã«ãã©ã«ãã¼ã®åå®¹ãåé¤ããå¿è¦ãããã¾ãã
MPS0250009=ã·ã¹ãã ç®¡çãã©ã«ãã¼ (ãæ°ã«å¥ããæçµè¡¨ç¤ºæ¥æãããã©ã«ã ã¦ã¼ã¶ã¼ ãã©ã«ãã¼ãªã©) ã¯åé¤ã§ãã¾ããã
MPS0250010=ã¦ã¼ã¶ã¼ç¨ã®æçµè¡¨ç¤ºæ¥æããã³ãæ°ã«å¥ããã©ã«ãã¼ã®ä½æä¸­ã«äºæããªãã¨ã©ã¼ãçºçãã¾ããã
MPS0250011=ã¯ã¼ã¯ã°ã«ã¼ãã«å¯¾ããæ°è¦ãã¼ã¿ ãã©ã«ãã¼ã®ä½æä¸­ã«äºæããªãã¨ã©ã¼ãçºçãã¾ããã
MPS0250012=ã¦ã¼ã¶ã¼ã«å¯¾ãã¦è¡¨ç¤ºããªããªã£ãã¡ãã»ã¼ã¸ãåé¤ããããã¦ã¼ã¶ã¼ ãã©ã«ãã¼ãã¯ãªã¼ã³ã¢ããããéã«äºæããªãã¨ã©ã¼ãçºçãã¾ããã
MPS0250013=ã¯ã¼ã¯ã°ã«ã¼ãã«å±ãããã¹ã¦ã®ãã©ã«ãã¼ã®ã¯ãªã¼ã³ã¢ããä¸­ã«äºæããªãã¨ã©ã¼ãçºçãã¾ããã
MPS0250014=ã¡ãã»ã¼ã¸ããã©ã«ãã¼ã«è¿½å ããéã«äºæããªãã¨ã©ã¼ãçºçãã¾ããã
MPS0260001=ãã¼ã¿ãã¼ã¹ããã¡ãã»ã¼ã¸ãåå¾ããéã«äºæããªãã¨ã©ã¼ãçºçãã¾ããã
MPS0260002=ãã¼ã¿ ãªã½ã¼ã¹ã®ãã¬ãã¥ã¼ãæ­£ããè¨­å®ããã¦ãã¾ãããã¿ãããã¤ã³ãã®ç®¡çèã«åãåããã¦ãã ããã
MPS0270001=ãã®ããã³ãã¯ä»ã®ã¦ã¼ã¶ã¼ã«ãã£ã¦ç®¡çããã¦ãã¾ãã
MPS0270002=ãã®ããã³ãã«ã¯ããã³ã ã¹ã¼ãã¼ãã¤ã¶ã¼ãå­å¨ãã¾ãã
MPS0270003=ãã®ããã³ããã­ã¼ãã§ãã¾ãã
MPS0270004=ãã®ããã³ãã¯ {0} ã«ãã£ã¦ç®¡çããã¦ãã¾ã
MPS0280001=ãã¼ã¿ãã¼ã¹ã«ãã©ã¡ã¼ã¿ã¼ ã°ã«ã¼ã ããªã¼ ãã¼ããè¦ã¤ããã¾ããã
MPS0280002=é¸æå¯è½ãªããªã¼ ãã¼ãåãæå®ããå¿è¦ãããã¾ããç©ºã«ãããã¨ã¯ã§ãã¾ããã
MPS0280003=2 ã¤ä»¥ä¸ã®ãã©ã¡ã¼ã¿ã¼ãæ®ãã¦ããå¿è¦ãããã¾ãã
MPS0280004=ãã¼ã¿å¤ {0} ãåé¤ã§ãã¾ãããå­ããªã¢ã³ã: {1} ã§ä½¿ç¨ããã¦ãã¾ãã
MPS0290001=ãã¼ã¿ãã¼ã¹ã«ã¡ãã»ã¼ã¸ ã³ã³ãã³ãã®é¢é£ä»ããè¦ã¤ããã¾ããã
MPS0290002=å­ãå­å¨ãããããã®ã³ã³ãã³ã ããªã¢ã³ããåé¤ã§ãã¾ãããæåã«å­ãåé¤ãã¦ãã ããã
MPS0290003=ãã­ãã¯ã·ã§ã³ã«ã¢ã¯ãã£ã ã³ãã¼ãå­å¨ããããããã®ã³ã³ãã³ã ããªã¢ã³ããåé¤ã§ãã¾ããã
MPS0300001=æ¿å¥ã®ééã¯æ°å¤ã§ããå¿è¦ãããã¾ãã
MPS0300002=æ¿å¥ã®ééãã¼ã­ã«ãããã¨ã¯ã§ãã¾ããã
MPS0300003=æ¿å¥ã³ã³ãã³ãã®æ­£é¢ç»åãè¦ã¤ããã¾ããã
MPS0300004=æ¿å¥ã³ã³ãã³ãã®èé¢ç»åãè¦ã¤ããã¾ããã
MPS0300005=ãã®æ¿å¥ãæ´æ°ããæ¨©éãããã¾ããã
MPS0300006=ååã¯å¿é ãã£ã¼ã«ãã§ãã
MPS0300007=ãã¼ã¿ãã¼ã¹åã«æ¿å¥ãªãã¸ã§ã¯ããè¦ã¤ããã¾ããã
MPS0300008=æ¿å¥ãªãã¸ã§ã¯ããæåºãã¦æ¿èªãåããæºåãã§ãã¦ãã¾ãããæ¿å¥ãªãã¸ã§ã¯ããéã¢ã¯ãã£ãç¶æã§ãããã¨ãç¢ºèªãã¦ãã ããã
MPS0300009=æ¿å¥ã¾ãã¯æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã¯é²è¡ä¸­ã§ãããæåºãã¦æ¿èªãåãã¦ãã¾ãããæåã«æåºãã¦æ¿èªãåãã¦ãã ããã
MPS0300010=ã¢ã¯ãã£ãåããã¦ããªãæ¿å¥ã¾ãã¯æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ãéã¢ã¯ãã£ãã«ãããã¨ã¯ã§ãã¾ããã
MPS0300011=æ¿å¥ã¾ãã¯æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã¯ã­ãã¯ããã¦ããªãããåå²ãå½ã¦ã§ãã¾ãã
MPS0300012=æ¿å¥ã¾ãã¯æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã®ç¶æãé©åã§ãªãããã¢ã¼ã«ã¤ãã§ãã¾ããã
MPS0300013=æ¿å¥ã¾ãã¯æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ãã¢ã¼ã«ã¤ãããæ¨©éãããã¾ããã
MPS0300014=åéã¯ã¢ã¼ã«ã¤ãæ¸ã¿ã®æ¿å¥ã¾ãã¯æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã«ã®ã¿é©ç¨ããã¾ãã
MPS0300015=æ¿å¥ã¾ãã¯æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã¯éã¢ã¯ãã£ãã¾ãã¯ã¢ã¼ã«ã¤ãæ¸ã¿ã®ç¶æã§ã®ã¿åé¤ã§ãã¾ãã
MPS0300016=æ¿å¥ãåå²ãå½ã¦ããæ¨©éãããã¾ããã
MPS0300017=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ãåå²ãå½ã¦ããæ¨©éãããã¾ããã
MPS0310001=1 ã¤ä»¥ä¸ã®ã¡ãã»ã¼ã¸ã§æ¿èªç¨ã«ãªãªã¼ã¹ä¸­ã®ã¿ãããã¤ã³ã ããªã¢ã³ãã®ã³ã³ãã³ãç¶æãå®ç¾©ããã¦ãã¾ãã
MPS0310002=åç§åã®ããªã¢ã³ããã¢ã¯ãã£ãã«ãªã£ã¦ããããã{0} ãã¢ã¯ãã£ãã«ã§ãã¾ãããæ¬¡ã®ããªã¢ã³ããæåã«ã¢ã¯ãã£ãã«ãã¦ãã ãã: {1}
MPS0310003=ããªãã¯æ¬¡ã®æ¿èªèãªã¹ãã«è¨è¼ããã¦ãã¾ãã : {0}
MPS0310004=é åºãå¤ãã¦æçµæ¿èªãè¦æ±ãããã¨ã¯ã§ãã¾ãã - ãã®ããªã¢ã³ãã¯ã¢ã¯ãã£ãåããã¦ããªãæ¬¡ã®è¦ªããã³ã³ãã³ããç¶æ¿ãã¾ã: {0}
MPS0310005=æ¬¡ã®ã¿ãããã¤ã³ã ããªã¢ã³ãã¯æ¿èªå¾ã¡ã§ã¯ããã¾ãã: {0}ã æ¿èªè¦æ±ãæå¦ããã¾ããã
MPS0310006=æ¬¡ã®ã¿ãããã¤ã³ã ããªã¢ã³ãã¯æ¿èªãã­ã»ã¹ã§ã¯ãªãããæå¦ã§ãã¾ãã: {0}ã
MPS0310007=ä½æ¥­ã³ãã¼ãä½æã§ãã¾ãããæ¬¡ã®ã¿ãããã¤ã³ã ããªã¢ã³ãã«æ¢ã«ä½æ¥­ã³ãã¼ãå­å¨ããããã¢ã¯ãã£ããªã³ãã¼ãå­å¨ãã¾ãã: {0}ã
MPS0310008=ã¿ãããã¤ã³ã ããªã¢ã³ããåé¤ããæ¨©éãããã¾ããã
MPS0310009=ãã®ããªã¢ã³ãã«ã¯å­ãå­å¨ãããããåé¤ã§ãã¾ããã
MPS0310010={0} ã®ä½æ¥­ã³ãã¼ãä½æããæ¨©éãããã¾ãããè¦æ±ã¯æå¦ããã¾ããã
MPS0310011=åé¤å¯¾è±¡ã«é¸æããããªã¢ã³ãããã¹ã¿ã¼ã§ããããã¾ãã¯æ¢ã«åé¤ããã¦ãã¾ããè¦æ±ã¯æå¦ããã¾ããã
MPS0310012=é åºãå¤ãã¦æçµæ¿èªãè¦æ±ãããã¨ã¯ã§ãã¾ãã - ä»ã®ããªã¢ã³ãã®åã«ããã©ã«ã ããªã¢ã³ããæ¿èªããå¿è¦ãããã¾ãã
MPS0310013=é åºãå¤ãã¦æçµæ¿èªãè¦æ±ãããã¨ã¯ã§ãã¾ãã - ããªã¢ã³ãã¯ 1 ã¬ãã«ãã¤æ¿èªããå¿è¦ãããã¾ãã
MPS0310014=1 ã¤ä»¥ä¸ã®ã¡ãã»ã¼ã¸ã§ã¢ã¯ãã£ãåä¸­ã®ã¿ãããã¤ã³ã ããªã¢ã³ãã®ã³ã³ãã³ãç¶æãå®ç¾©ããã¦ãã¾ãã
MPS0320001=ãã¼ã¿ãã¼ã¹åã«ã¿ã° ãªãã¸ã§ã¯ããè¦ã¤ããã¾ããã
MPS0320002=ã¿ã°ãåå²ãå½ã¦ããæ¨©éãããã¾ããã
MPS0330001=ã¿ãããã¤ã³ããæå¹ã«ãªã£ã¦ãã¾ããã
MPS0330002=ã¿ãããã¤ã³ããã³ã³ãã¸ã·ã§ã³ ãã£ãã«ã«å¯¾å¿ãã¦ãã¾ãã
MPS0330003=ãã³ãã¬ã¼ã ãã¡ã¤ã«ã«ã¯ \"cftemplate.dat\" ã¨ããååãä»ããå¿è¦ãããã¾ã
MPS0330004=ã³ã³ãã¸ã·ã§ã³ ãã¡ã¤ã«ã«ã¯ .pub ã¾ãã¯ .wfd ã¨ããæ¡å¼µå­ãå¿è¦ã§ã
MPS0340001=ããªãã¯æ¬¡ã®æ¿èªèãªã¹ãã«è¨è¼ããã¦ãã¾ãã : {0}
MPS0340002=æ¬¡ã®ã³ãã¥ãã±ã¼ã·ã§ã³ã¯æ¿èªãã­ã»ã¹ã§ã¯ãªãããæå¦ã§ãã¾ãã: {0}ã
action.button.label.update=ç·¨é
client_messages.button.add=è¿½å 
client_messages.button.cancel=ã­ã£ã³ã»ã«
client_messages.button.cancel_upper=ã­ã£ã³ã»ã«
client_messages.button.continue_txt=ç¶è¡
client_messages.button.copy=ã³ãã¼
client_messages.button.ok=OK
client_messages.button.ok_upper=OK
client_messages.button.resolve.variables=å¤æ°ã®è§£æ±º
client_messages.button.save=ä¿å­
client_messages.calendar.day_name.friday=éææ¥
client_messages.calendar.day_name.monday=æææ¥
client_messages.calendar.day_name.saturday=åææ¥
client_messages.calendar.day_name.sunday=æ¥ææ¥
client_messages.calendar.day_name.thursday=æ¨ææ¥
client_messages.calendar.day_name.tuesday=ç«ææ¥
client_messages.calendar.day_name.wednesday=æ°´ææ¥
client_messages.calendar.day_name_min.friday=é
client_messages.calendar.day_name_min.monday=æ
client_messages.calendar.day_name_min.saturday=å
client_messages.calendar.day_name_min.sunday=å
client_messages.calendar.day_name_min.thursday=æ¨
client_messages.calendar.day_name_min.tuesday=æ¨
client_messages.calendar.day_name_min.wednesday=æ°´
client_messages.calendar.day_name_short.friday=é
client_messages.calendar.day_name_short.monday=æ
client_messages.calendar.day_name_short.saturday=å
client_messages.calendar.day_name_short.sunday=æ¥
client_messages.calendar.day_name_short.thursday=æ¨
client_messages.calendar.day_name_short.tuesday=ç«
client_messages.calendar.day_name_short.wednesday=æ°´
client_messages.calendar.month_name.april=4 æ
client_messages.calendar.month_name.august=8 æ
client_messages.calendar.month_name.december=12 æ
client_messages.calendar.month_name.february=2 æ
client_messages.calendar.month_name.january=1 æ
client_messages.calendar.month_name.july=7 æ
client_messages.calendar.month_name.june=6 æ
client_messages.calendar.month_name.march=3 æ
client_messages.calendar.month_name.may=5 æ
client_messages.calendar.month_name.november=11 æ
client_messages.calendar.month_name.october=10 æ
client_messages.calendar.month_name.september=9 æ
client_messages.calendar.month_name_short.april=4 æ
client_messages.calendar.month_name_short.august=8 æ
client_messages.calendar.month_name_short.december=12 æ
client_messages.calendar.month_name_short.february=2 æ
client_messages.calendar.month_name_short.january=1 æ
client_messages.calendar.month_name_short.july=7 æ
client_messages.calendar.month_name_short.june=6 æ
client_messages.calendar.month_name_short.march=3 æ
client_messages.calendar.month_name_short.may=5 æ
client_messages.calendar.month_name_short.november=11 æ
client_messages.calendar.month_name_short.october=10 æ
client_messages.calendar.month_name_short.september=9 æ
client_messages.calendar.next=æ¬¡ã¸
client_messages.calendar.prev=åã¸
client_messages.compound_content_type.all=ãã¹ã¦ã®ä¸è´
client_messages.compound_content_type.distinct=éè¤ãªã
client_messages.compound_content_type.first_non_blank=æåã®ç©ºä»¥å¤ã®ä¸è´
client_messages.compound_content_type.non_blank=ç©ºä»¥å¤
client_messages.compound_content_type.non_blank_and_duplicate=ç©ºã¾ãã¯éè¤ä»¥å¤
client_messages.compound_fmt.and=ãã¤
client_messages.compound_fmt.none=ãªã
client_messages.compound_fmt.or=ã¾ãã¯
client_messages.content_editor.applied_image_name=é©ç¨ãããç»åå
client_messages.content_editor.bad_graphic_file_type=GIFãJPGãPDFãPNG ç»åã®ã¿ãã¢ããã­ã¼ãã§ãã¾ãã
client_messages.content_editor.bad_image_file_type=ãã®éä¿¡ç¨ã«ã¯ {0} ã®ç»åã®ã¿ãã¢ããã­ã¼ãã§ãã¾ãã
client_messages.content_editor.character_count=æå­æ°
client_messages.content_editor.checkbox=ãã§ãã¯ããã¯ã¹
client_messages.content_editor.checkbox_properties=ãã§ãã¯ããã¯ã¹ã®ãã­ããã£
client_messages.content_editor.clear=ã¯ãªã¢
client_messages.content_editor.click=ã¯ãªãã¯
client_messages.content_editor.click_to_download=é©ç¨æ¸ã¿ã³ã³ãã³ãããã¦ã³ã­ã¼ãããã«ã¯ã[ã³ã³ãã³ã ãã¡ã¤ã«] ãªã³ã¯ãã¯ãªãã¯ãã¾ã
client_messages.content_editor.cms_last_sync_date=CMS æçµåææ¥
client_messages.content_editor.cms_last_update_date=CMS æ´æ°æ¥
client_messages.content_editor.constants=å®æ°
client_messages.content_editor.content_file=ã³ã³ãã³ã ãã¡ã¤ã«
client_messages.content_editor.content_suppressed=ã³ã³ãã³ãéè¡¨ç¤º
client_messages.content_editor.copy=ã³ãã¼
client_messages.content_editor.copy_from=ã³ãã¼å
client_messages.content_editor.default_field_label_txt=ã©ãã«
client_messages.content_editor.divider=æ¨ªåºåãç·
client_messages.content_editor.edit=ç·¨é
client_messages.content_editor.font_size=ãã©ã³ã ãµã¤ãº
client_messages.content_editor.group=ã°ã«ã¼ã
client_messages.content_editor.horizontal=æ¨ª
client_messages.content_editor.image_library=ç»åã©ã¤ãã©ãª
client_messages.content_editor.image_link=ç»åãªã³ã¯
client_messages.content_editor.inherit_from_parent=è¦ªããç¶æ¿
client_messages.content_editor.insert_character=æå­ã®æ¿å¥
client_messages.content_editor.insert_image=ç»åã®æ¿å¥
client_messages.content_editor.insert_smart_text=ã¹ãã¼ã ãã­ã¹ãã®æ¿å¥
client_messages.content_editor.insert_variables=å¤æ°ã®æ¿å¥
client_messages.content_editor.leave_empty=ãã¼ããç©ºã®ã¾ã¾ã«ãã
client_messages.content_editor.max_length=æå¤§é·
client_messages.content_editor.new_guide=æ°è¦ã¬ã¤ã
client_messages.content_editor.new_image=æ°è¦ã¢ã»ãã
client_messages.content_editor.no_content=ã³ã³ãã³ããªã
client_messages.content_editor.no_image=ç»åãããã¾ãã
client_messages.content_editor.no_image_selected=é¸ææ¸ã¿ç»åãªã
client_messages.content_editor.no_limit=å¶éãªã
client_messages.content_editor.no_variant_content_to_copy=ããªã¢ã³ãã«ã³ãã¼ããã³ã³ãã³ããå«ã¾ãã¦ãã¾ãã
client_messages.content_editor.non_breaking_space=æ¹è¡ãªãã¹ãã¼ã¹
client_messages.content_editor.none=ãªã
client_messages.content_editor.oops=åé¡ãçºçãã¾ããã
client_messages.content_editor.orientation=åã
client_messages.content_editor.paragraph=æ®µè½
client_messages.content_editor.paragraph_styles=æ®µè½ã¹ã¿ã¤ã«
client_messages.content_editor.pending_save=ä¿çä¸­ã®ä¿å­
client_messages.content_editor.position=ä½ç½®
client_messages.content_editor.radio=ã©ã¸ãª
client_messages.content_editor.radio_properties=ã©ã¸ãªã®ãã­ããã£
client_messages.content_editor.referencing_image_library=ç»åã©ã¤ãã©ãªã®åç§
client_messages.content_editor.same_as_variant=ãã®ããªã¢ã³ããåç§ãã¦ããã³ã³ãã³ã
client_messages.content_editor.select_content_state=ã³ã³ãã³ãç¶æãé¸æãã¦ãã ãã
client_messages.content_editor.shared_content_reference=å±æã³ã³ãã³ãã®åç§
client_messages.content_editor.smart_text=ã¹ãã¼ã ãã­ã¹ã
client_messages.content_editor.spellchecker=ã¹ãã«ãã§ãã«ã¼
client_messages.content_editor.styles=ã¹ã¿ã¤ã«
client_messages.content_editor.symbols=è¨å·
client_messages.content_editor.table_properties=ãã¼ãã«ã®ãã­ããã£
client_messages.content_editor.text_field=ãã­ã¹ã ãã£ã¼ã«ã
client_messages.content_editor.text_field_properties=ãã­ã¹ã ãã£ã¼ã«ãã®ãã­ããã£
client_messages.content_editor.to_edit_content_file=ã³ã³ãã³ã ãã¡ã¤ã«ãå¤æ´
client_messages.content_editor.type=ã¿ã¤ã
client_messages.content_editor.unknown=ä¸æ
client_messages.content_editor.upload_date=ã¢ããã­ã¼ãæ¥ä»
client_messages.content_editor.uploaded=ã¢ããã­ã¼ãæ¸ã¿
client_messages.content_editor.use_content_library=ã³ã³ãã³ã ã©ã¤ãã©ãªã®ä½¿ç¨
client_messages.content_editor.use_same_as_default=ããã©ã«ãè¨èªã¨åãã³ã³ãã³ãã®ä½¿ç¨
client_messages.content_editor.use_same_as_system_default=ã·ã¹ãã  ããã©ã«ãè¨èªã¨åãã³ã³ãã³ãã®ä½¿ç¨
client_messages.content_editor.variables=å¤æ°
client_messages.content_editor.vertical=ç¸¦
client_messages.content_type=ã³ã³ãã³ã ã¿ã¤ã
client_messages.error.please_reload_page=ãã©ã¦ã¶ã¼ ã¨ã©ã¼: ãã¼ã¸ãåã­ã¼ããã¦ããç´ãã¦ãã ããã
client_messages.format.camel_case=ååèªã®æåã®æå­ãå¤§æå­ã«ãã
client_messages.format.lowercase=å°æå­
client_messages.format.no_trim=ããªãã³ã°ãªã
client_messages.format.trim_after=å¾ãããªãã³ã°
client_messages.format.trim_all=ãã¹ã¦ããªãã³ã°
client_messages.format.trim_before=åãããªãã³ã°
client_messages.format.unmodified=æªå¤æ´
client_messages.format.uppercase=å¤§æå­
client_messages.format.uppercase_first_character=æåã®æå­ãå¤§æå­ã«ãã
client_messages.language.english=è±èª
client_messages.language.french=ãã©ã³ã¹èª
client_messages.language.spanish=ã¹ãã¤ã³èª
client_messages.text.AND=ããã³
client_messages.text.DAT_and_DAT=PUB ã¨ DAT
client_messages.text.DAT_and_WFD=WFD ã¨ DAT
client_messages.text.DETAILS=è©³ç´°
client_messages.text.HIDE=éè¡¨ç¤º
client_messages.text.OR=ã¾ãã¯
client_messages.text.PDF_images_must_be_uploaded_separately=PDF ãã¡ã¤ã«ã¨ç»åãã¡ã¤ã«ã¯å¥ãã«ã¢ããã­ã¼ãããå¿è¦ãããã¾ãã
client_messages.text.Proof=æ ¡æ­£
client_messages.text.active=ã¢ã¯ãã£ã
client_messages.text.add=è¿½å 
client_messages.text.add_and_save=è¿½å ãã¦ä¿å­
client_messages.text.add_items_comma_separate_values=ã¢ã¤ãã : ã³ã³ã ',' åºåãã®è¤æ°å¤
client_messages.text.advanced_search=é«åº¦ãªæ¤ç´¢
client_messages.text.alignment=éç½®
client_messages.text.all=ãã¹ã¦
client_messages.text.alphanumeric=è±æ°å­
client_messages.text.and=ããã³
client_messages.text.assigned=å²ãå½ã¦æ¸ã¿
client_messages.text.audit_report=ç£æ»ã¬ãã¼ã
client_messages.text.available=ä½¿ç¨å¯è½
client_messages.text.bad_request_for_targeting_summary=ã¿ã¼ã²ããè¨­å®ãµããªã¼ã®è¦æ±ãä¸é©åã§ã
client_messages.text.bold=å¤ªå­
client_messages.text.border=æ ç·
client_messages.text.cancel=ã­ã£ã³ã»ã«
client_messages.text.clear_new_items=æ°è¦ã¢ã¤ãã ã®ã¯ãªã¢
client_messages.text.click_to_download_package=ã¯ãªãã¯ãã¦æ¢å­ã®ããã±ã¼ã¸ããã¦ã³ã­ã¼ããã¾ã
client_messages.text.click_to_view=ã¯ãªãã¯ãã¦è¡¨ç¤º
client_messages.text.clone_of=è¤è£½
client_messages.text.close=éãã
client_messages.text.color=ã«ã©ã¼
client_messages.text.complete=å®äº
client_messages.text.compound_values=è¤åå¤
client_messages.text.condition_is_referenced_by_target_group=ãã®æ¡ä»¶ã¯ã¿ã¼ã²ãã ã°ã«ã¼ãã«ãã£ã¦åç§ããã¦ãããããåé¤ã§ãã¾ããã
client_messages.text.conditions=æ¡ä»¶
client_messages.text.connector_name=ã³ãã¯ã¿ã¼å
client_messages.text.constant=å®æ°
client_messages.text.content=ã³ã³ãã³ã
client_messages.text.content_empty=ã³ã³ãã³ããç©ºã§ã
client_messages.text.content_suppressed=ã³ã³ãã³ãéè¡¨ç¤º
client_messages.text.content_type=ã³ã³ãã³ã ã¿ã¤ã
client_messages.text.copy_to_clipboard=ã¯ãªãããã¼ãã¸ã³ãã¼
client_messages.text.created=ä½ææ¸ã¿
client_messages.text.currency_symbol=éè²¨è¨å·
client_messages.text.date=æ¥ä»
client_messages.text.days=æ¥æ°
client_messages.text.decimal=å°æ°ç¹
client_messages.text.decimal_places=å°æ°ç¹ä»¥ä¸ã®æ¡æ°
client_messages.text.default_state=ããã©ã«ã
client_messages.text.delimiter=åºåãè¨å·
client_messages.text.delta=å·®å
client_messages.text.display=è¡¨ç¤º
client_messages.text.done=å®äº
client_messages.text.dont_ask_again=ä»å¾ããã®ã¡ãã»ã¼ã¸ãè¡¨ç¤ºããªã
client_messages.text.drag=ãã©ãã°
client_messages.text.drag_and_drop_not_applicable_for_composition_file_upload=ãã©ãã° ã¢ã³ã ãã­ãããä½¿ç¨ãã¦ã³ã³ãã¸ã·ã§ã³ ãã¡ã¤ã«ãã¢ããã­ã¼ããããã¨ã¯ã§ãã¾ããã[ãã¡ã¤ã«ã®è¿½å ...] ãã¯ãªãã¯ãã¦ãã ããã
client_messages.text.drag_selection_area=é¸æé åã®ä¸­ã¾ãã¯å¤ã¸
client_messages.text.email=é»å­ã¡ã¼ã«
client_messages.text.enabled=æå¹
client_messages.text.enabled_for_content=ã³ã³ãã³ãã§æå¹
client_messages.text.enabled_for_rules=ã«ã¼ã«ã§æå¹
client_messages.text.entries=ã¨ã³ããª
client_messages.text.error=ã¨ã©ã¼
client_messages.text.filter_condition.when=æ¥æ
client_messages.text.first=å
client_messages.text.font_name=ãã©ã³ãå
client_messages.text.format=å½¢å¼
client_messages.text.freeform=ããªã¼ãã©ã¼ã 
client_messages.text.friendly_name=ãã¬ã³ããªå
client_messages.text.hidden=éè¡¨ç¤ºã«ãã¾ãã?
client_messages.text.id=ID
client_messages.text.idptype=IDP ã¿ã¤ã
client_messages.text.imported=ã¤ã³ãã¼ãæ¸ã¿
client_messages.text.indent_left_right=ã¤ã³ãã³ã (å·¦ãå³)
client_messages.text.indentation=ã¤ã³ãã³ã
client_messages.text.inherit_from=ç¶æ¿å
client_messages.text.inherited=ç¶æ¿
client_messages.text.inline.first.value=æåã®å¤ãã¤ã³ã©ã¤ã³å
client_messages.text.insert.as.paragraphs=æ®µè½ã¨ãã¦æ¿å¥
client_messages.text.integer=æ´æ°
client_messages.text.invalid_data={0} ãã¼ã¿ãç¡å¹ã§ã
client_messages.text.invalid_proofing_data=æ ¡æ­£ä¸­ã®ãã¼ã¿ãç¡å¹ã§ã
client_messages.text.italic=æä½
client_messages.text.item=ã¢ã¤ãã 
client_messages.text.items=ã¢ã¤ãã 
client_messages.text.landscape=æ¨ª (11 x 8.5)
client_messages.text.last=å§
client_messages.text.layout=ã¬ã¤ã¢ã¦ã
client_messages.text.legal_landscape=ãªã¼ã¬ã«æ¨ª (14 x 8.5)
client_messages.text.legal_portrait=ãªã¼ã¬ã«ç¸¦ (8.5 x 14)
client_messages.text.legend=å¡ä¾
client_messages.text.less=æ¸å°
client_messages.text.letter_case=å¤§æå­/å°æå­
client_messages.text.line_spacing=è¡é
client_messages.text.list_end_delimiter=ãªã¹ãæ«å°¾ã®åºåãæå­
client_messages.text.loading=ã­ã¼ãä¸­...
client_messages.text.loading_CMS_images=CMS ãªãã¸ããªããç»åãã­ã¼ããã¦ãã¾ãããå¾ã¡ãã ãã...
client_messages.text.make=å¤æ´
client_messages.text.make_possessive=æææ ¼ã«ãã
client_messages.text.mandatory=å¿é 
client_messages.text.mandatory_insert=å¿é ã®æ¿å¥
client_messages.text.mandatory_inserts=å¿é ã®æ¿å¥
client_messages.text.metatags=ã¡ã¿ã¿ã°
client_messages.text.months=ææ°
client_messages.text.more=å¢å 
client_messages.text.move=ç§»å
client_messages.text.move_zone=ã¾ã¼ã³ã®ç§»å
client_messages.text.multi_language=å¤è¨èª
client_messages.text.name=åå
client_messages.text.name_for_composition_package_required=ãã®ã³ã³ãã¸ã·ã§ã³ ãã¡ã¤ã« ããã±ã¼ã¸ã®ååãæå®ããå¿è¦ãããã¾ã
client_messages.text.new_part=æ°è¦ãã¼ã
client_messages.text.new_zone=æ°è¦ã¾ã¼ã³
client_messages.text.next=æ¬¡ã¸
client_messages.text.no=ããã
client_messages.text.no_CMS_images_for_site=ç¾å¨ã®ãµã¤ãã®ç»åãè¦ã¤ããã¾ããã§ããã
client_messages.text.no_inserts_match_search=ç¾å¨ã®æ¤ç´¢æ¡ä»¶ã¨ä¸è´ããæ¿å¥ãããã¾ãã
client_messages.text.no_inserts_selected=æ¿å¥ãé¸æããã¦ãã¾ãã
client_messages.text.no_items=-- ã¢ã¤ãã ãå®ç¾©ããã¦ãã¾ãã --
client_messages.text.no_items_selected=ã¢ã¤ãã ãé¸æããã¦ãã¾ãã
client_messages.text.no_mandatory_inserts_match_search=ç¾å¨ã®æ¤ç´¢æ¡ä»¶ã¨ä¸è´ããå¿é ã®æ¿å¥ãããã¾ãã
client_messages.text.no_mandatory_inserts_selected=å¿é ã®æ¿å¥ãé¸æããã¦ãã¾ãã
client_messages.text.no_matching_approvers=ä¸è´ããæ¿èªèãå­å¨ãã¾ãã
client_messages.text.no_matching_entries=ä¸è´ããã¨ã³ããªãè¦ã¤ããã¾ãã
client_messages.text.no_matching_items=ç¾å¨ã®æ¤ç´¢æ¡ä»¶ã¨ä¸è´ããã¢ã¤ãã ãããã¾ãã
client_messages.text.no_messages_match_search=ç¾å¨ã®æ¤ç´¢æ¡ä»¶ã¨ä¸è´ããã¡ãã»ã¼ã¸ãããã¾ãã
client_messages.text.no_messages_selected=ã¡ãã»ã¼ã¸ãé¸æããã¦ãã¾ãã
client_messages.text.no_optional_inserts_match_search=ç¾å¨ã®æ¤ç´¢æ¡ä»¶ã¨ä¸è´ãããªãã·ã§ã³ã®æ¿å¥ãããã¾ãã
client_messages.text.no_optional_inserts_selected=ãªãã·ã§ã³ã®æ¿å¥ãé¸æããã¦ãã¾ãã
client_messages.text.no_paragraph_styles_match_search=ç¾å¨ã®æ¤ç´¢æ¡ä»¶ã¨ä¸è´ããæ®µè½ã¹ã¿ã¤ã«ãããã¾ãã
client_messages.text.no_paragraph_styles_selected=æ®µè½ã¹ã¿ã¤ã«ãé¸æããã¦ãã¾ãã
client_messages.text.no_tags=ã¿ã°ãããã¾ãã
client_messages.text.no_text_styles_match_search=ç¾å¨ã®æ¤ç´¢æ¡ä»¶ã¨ä¸è´ãããã­ã¹ã ã¹ã¿ã¤ã«ãããã¾ãã
client_messages.text.no_text_styles_selected=ãã­ã¹ã ã¹ã¿ã¤ã«ãé¸æããã¦ãã¾ãã
client_messages.text.no_touchpoints_match_criteria=ç¾å¨ã®æ¤ç´¢æ¡ä»¶ã¨ä¸è´ããã¿ãããã¤ã³ããããã¾ãã
client_messages.text.no_touchpoints_selected=ã¿ãããã¤ã³ããé¸æããã¦ãã¾ãã
client_messages.text.no_users_match_criteria=ç¾å¨ã®æ¤ç´¢æ¡ä»¶ã¨ä¸è´ããã¦ã¼ã¶ã¼ãå­å¨ãã¾ãã
client_messages.text.no_users_selected=ã¦ã¼ã¶ã¼ãé¸æããã¦ãã¾ãã
client_messages.text.no_variables_match_criteria=ç¾å¨ã®æ¤ç´¢æ¡ä»¶ã¨ä¸è´ããå¤æ°ãããã¾ãã
client_messages.text.no_variables_selected=å¤æ°ãé¸æããã¦ãã¾ãã
client_messages.text.no_variant_matches=ä¸è´ããããªã¢ã³ããããã¾ãã
client_messages.text.no_workgroups_match_criteria=ç¾å¨ã®æ¤ç´¢æ¡ä»¶ã¨ä¸è´ããã¯ã¼ã¯ã°ã«ã¼ããããã¾ãã
client_messages.text.no_workgroups_selected=ã¯ã¼ã¯ã°ã«ã¼ããé¸æããã¦ãã¾ãã
client_messages.text.nodes=ã¤ã³ã¹ã¿ã³ã¹
client_messages.text.non_selectable_insert=é¸æã§ããªãæ¿å¥
client_messages.text.numeric=æ°å¤
client_messages.text.of=/
client_messages.text.online=ãªã³ã©ã¤ã³
client_messages.text.only_zip_files_permitted=zip ãã¡ã¤ã«ã®ã¿ãã¢ããã­ã¼ãã§ãã¾ãã
client_messages.text.optional=ãªãã·ã§ã³
client_messages.text.optional_insert=ãªãã·ã§ã³ã®æ¿å¥
client_messages.text.optional_inserts=ãªãã·ã§ã³ã®æ¿å¥
client_messages.text.order_down=é åºãä¸ã¸
client_messages.text.order_up=é åºãä¸ã¸
client_messages.text.ordered=é åºä»ã
client_messages.text.package_file=ããã±ã¼ã¸ ãã¡ã¤ã«
client_messages.text.page_range=ãã¼ã¸ç¯å² (ãªãã·ã§ã³)
client_messages.text.part=ãã¼ã
client_messages.text.pending_save=ä¿çä¸­ã®ä¿å­
client_messages.text.per_page=ãã¼ã¸å½ãã
client_messages.text.permitted_characters=ãã®å¥åã«ã¯æ¬¡ã®æå­ãä½¿ç¨ã§ãã¾ã
client_messages.text.permitted_file_upload={0} ãã¡ã¤ã«ã®ã¿ãã¢ããã­ã¼ãã§ãã¾ãã
client_messages.text.pod_type=ããã ã¿ã¤ã
client_messages.text.pod_url=ããã URL
client_messages.text.point_size=ãã¤ã³ã ãµã¤ãº
client_messages.text.portrait=ç¸¦ (8.5 x 11)
client_messages.text.possessive=æææ ¼
client_messages.text.preview=ãã¬ãã¥ã¼
client_messages.text.previous=å
client_messages.text.priority=åªååº¦
client_messages.text.processing=å¦çä¸­
client_messages.text.proof=æ ¡æ­£
client_messages.text.range_to=ãã
client_messages.text.recipient=åä¿¡è
client_messages.text.referencing_image=åç§åç»å
client_messages.text.referencing_system_default_content=åç§åã·ã¹ãã  ããã©ã«ãè¨èªã³ã³ãã³ã
client_messages.text.referencing_touchpoint_default_content=åç§åããã©ã«ãè¨èªã³ã³ãã³ã
client_messages.text.remove_background_image=èæ¯ç»åã®åé¤
client_messages.text.remove_section=ã»ã¯ã·ã§ã³ã®åé¤
client_messages.text.request_date=è¦æ±æ¥
client_messages.text.result=çµæ
client_messages.text.results=çµæ
client_messages.text.role=å½¹å²
client_messages.text.rules=ã«ã¼ã«
client_messages.text.run_date=å®è¡æ¥
client_messages.text.save_and_edit=ä¿å­ãã¦ç·¨é
client_messages.text.save_to_file=ãã¡ã¤ã«ã«ä¿å­...
client_messages.text.search=æ¤ç´¢
client_messages.text.search_content=ã³ã³ãã³ãã®æ¤ç´¢
client_messages.text.search_for=å¯¾è±¡
client_messages.text.search_for_approvers=æ¿èªèã®æ¤ç´¢
client_messages.text.search_for_touchpoints=ã¿ãããã¤ã³ãã®æ¤ç´¢
client_messages.text.section=ã»ã¯ã·ã§ã³
client_messages.text.segmentation=è·é¢
client_messages.text.select_date=æ¥ä»ã®é¸æ
client_messages.text.select_single_item=é åºãå¤ããã¢ã¤ãã ã 1 ã¤é¸æãã
client_messages.text.select_target_section=ã¿ã¼ã²ãã ã»ã¯ã·ã§ã³ã®é¸æ
client_messages.text.selected=é¸ææ¸ã¿
client_messages.text.show_all=ãã¹ã¦è¡¨ç¤º
client_messages.text.showing=è¡¨ç¤ºä¸­
client_messages.text.showing_zero_entries=0 ï½ 0/0 ã®ã¨ã³ããªãè¡¨ç¤ºä¸­
client_messages.text.spacing_before_after=éé (åãå¾)
client_messages.text.state=ç¶æ
client_messages.text.status=ã¹ãã¼ã¿ã¹
client_messages.text.sub_branches=ãµããã¡ã¤ã³
client_messages.text.tag_cloud=ã¿ã° ã¯ã©ã¦ã
client_messages.text.target=ã¿ã¼ã²ãã
client_messages.text.targeted=ã¿ã¼ã²ããè¨­å®æ¸ã¿
client_messages.text.targeting.AND=ããã³
client_messages.text.targeting.OR=ã¾ãã¯
client_messages.text.targeting.between=ãã
client_messages.text.targeting.click_to_upload=[+] ãã¯ãªãã¯ãã¦ã¢ããã­ã¼ã
client_messages.text.targeting.days=æ¥æ°
client_messages.text.targeting.file=ãã¡ã¤ã«
client_messages.text.targeting.minus=ãã¤ãã¹
client_messages.text.targeting.months=ææ°
client_messages.text.targeting.plus=ãã©ã¹
client_messages.text.targeting.primary_source=ãã©ã¤ããª ã½ã¼ã¹
client_messages.text.targeting.reference_source=åç§ã½ã¼ã¹
client_messages.text.targeting.remote_source=ãªã¢ã¼ã ãªã½ã¼ã¹
client_messages.text.targeting.unknown_type=ä¸æãªã¿ã¤ã
client_messages.text.targeting.upload_data_file=ãã¼ã¿ ãã¡ã¤ã«ã®ã¢ããã­ã¼ã
client_messages.text.targeting.user_specified=ã¦ã¼ã¶ã¼æå®
client_messages.text.targeting.value=å¤
client_messages.text.targeting.value_from_file=ãã¼ã¿ ãã¡ã¤ã«ã®å¤
client_messages.text.targeting.variable=å¤æ°
client_messages.text.targeting.years=å¹´æ°
client_messages.text.template_file=ãã³ãã¬ã¼ã ãã¡ã¤ã«
client_messages.text.template_referenced_cannot_be_removed=ãã®ãã³ãã¬ã¼ãã¯ããªã¢ã³ãã«ãã£ã¦åç§ããã¦ããããåé¤ã§ãã¾ããã
client_messages.text.tenant_assignable_bin=ããã³ãã«å²ãå½ã¦å¯è½ãªãã³
client_messages.text.text_style_referenced=ãã®ãã­ã¹ã ã¹ã¿ã¤ã«ã¯åç§ããã¦ããããåé¤ã§ãã¾ããã
client_messages.text.thousands_separator=æ¡åºåãè¨å·
client_messages.text.timed=æéæå®
client_messages.text.too_many_files_selected=è¤æ°ã®ãã¡ã¤ã«ãã¢ããã­ã¼ãç¨ã«é¸æããã¾ããã
client_messages.text.touchpoint_health_check=ã¿ãããã¤ã³ãã®ç¶æç¢ºèª
client_messages.text.trim_on_empty=ç©ºã®å ´åã«ããªãã³ã°
client_messages.text.type=ã¿ã¤ã
client_messages.text.type_to_search=-- æ¤ç´¢ããã¿ã¤ã --
client_messages.text.unable_to_generate_preproof=ããªãã«ã¼ããçæã§ãã¾ãã:  ãã­ãã¯ã·ã§ã³ ã³ã³ãã³ããè¦ã¤ããã¾ãããç®¡çèã«åãåããã¦ãã ããã
client_messages.text.unable_to_generate_preproof_view_log=ããªãã«ã¼ããçæã§ãã¾ãã:  ã¯ãªãã¯ããã¨ã­ã°ãè¡¨ç¤ºããã¾ãã
client_messages.text.unable_to_generate_proof=ããªãã«ã¼ããçæã§ãã¾ãã:  æå®ãã ID ã®åä¿¡èãè¦ã¤ããã¾ããã§ããã ç®¡çèã«åãåããã¦ãã ããã
client_messages.text.unassignable_bin=å²ãå½ã¦ä¸å¯è½ãªãã³
client_messages.text.underline=ä¸ç·
client_messages.text.upload=ã¢ããã­ã¼ã
client_messages.text.upload_alternate_template=ä»£æ¿ãã³ãã¬ã¼ã ãã¡ã¤ã«ã®ã¢ããã­ã¼ã
client_messages.text.upload_background_images=è¿½å ã¢ã¤ã³ã³ãã¯ãªãã¯ãã¦èæ¯ç»åãã¢ããã­ã¼ããã¾ãã
client_messages.text.user=ã¦ã¼ã¶ã¼
client_messages.text.user_access=ã¦ã¼ã¶ã¼ ã¢ã¯ã»ã¹
client_messages.text.user_name=ã¦ã¼ã¶ã¼å
client_messages.text.users=ã¦ã¼ã¶ã¼
client_messages.text.valid_file_upload_types={0} ãã¡ã¤ã«ã®ã¿ãã¢ããã­ã¼ãã§ãã¾ã
client_messages.text.variable=å¤æ°
client_messages.text.variable_make_possessive=ãã®å¤æ°ãæææ ¼ã«ãããããã¢ãã¹ãã­ãã£ã¨ s ã¾ãã¯\nåè©ã®æ«å°¾ã s ã®å ´åã¯ã¢ãã¹ãã­ãã£ãèªåçã«\nè¿½å ãã¾ã (David's car ã¾ãã¯ Jess' car ãªã©)ã
client_messages.text.variable_trim=ãã®å¤æ°ã®å¤ãç©ºã®å ´åã«ãæ®ã£ãç©ºç½ã®\nå¦çæ¹æ³ãé¸æãã¾ãã
client_messages.text.variants=ããªã¢ã³ã
client_messages.text.view_PDF=PDF ã®è¡¨ç¤º
client_messages.text.view_data=ãã¼ã¿ã®è¡¨ç¤º
client_messages.text.view_item={0} ã®è¡¨ç¤º
client_messages.text.view_log=ã­ã°ã®è¡¨ç¤º
client_messages.text.view_logs=ã­ã°ã®è¡¨ç¤º
client_messages.text.viewing_proof=è¦æ±ãããæ ¡æ­£ã®è¡¨ç¤º
client_messages.text.web_font_name=Web ãã©ã³ãå
client_messages.text.years=å¹´
client_messages.text.yes=ã¯ã
client_messages.text.zone=ã¾ã¼ã³
client_messages.text.zone_properties_have_changed=ã¾ã¼ã³ã®ãã­ããã£ãå¤æ´ããã¦ãã¾ããç·¨éããåã«ä¿å­ãã¦ãã ããã
client_messages.title.PDF_upload=PDF ã®ã¢ããã­ã¼ã
client_messages.title.add_composition_package=ã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸ã®è¿½å 
client_messages.title.add_data_element=ãã¼ã¿è¦ç´ ã®è¿½å 
client_messages.title.add_data_record=ãã¼ã¿ ã¬ã³ã¼ãã®è¿½å 
client_messages.title.add_data_source=ãã¼ã¿ ã½ã¼ã¹ã®è¿½å 
client_messages.title.add_language=è¨èªã®è¿½å 
client_messages.title.add_menu_item=ã¡ãã¥ã¼é ç®ã®è¿½å 
client_messages.title.add_section=ã»ã¯ã·ã§ã³ã®è¿½å 
client_messages.title.add_xml_data_tag=XML ãã¼ã¿ ã¿ã°ã®è¿½å 
client_messages.title.alternate_templates=ä»£æ¿ãã³ãã¬ã¼ã
client_messages.title.compound_smart_text=è¤åã¹ãã¼ã ãã­ã¹ã
client_messages.title.continue_without_save=ä¿å­ããªãã§ç¶è¡ãã¾ãã?
client_messages.title.data_resource=ãã¼ã¿ ãªã½ã¼ã¹
client_messages.title.delete_data_element=ãã¼ã¿è¦ç´ ã®åé¤
client_messages.title.delete_data_source=ãã¼ã¿ ã½ã¼ã¹ã®åé¤
client_messages.title.delete_data_xml_element=XML ãã¼ã¿è¦ç´ ã®åé¤
client_messages.title.delete_deta_record=ãã¼ã¿ ã¬ã³ã¼ãã®åé¤
client_messages.title.edit_attribute=å±æ§ã®ç·¨é
client_messages.title.edit_composition_package=ã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸ã®ç·¨é
client_messages.title.edit_data_element=ãã¼ã¿è¦ç´ ã®ç·¨é
client_messages.title.edit_data_record=ãã¼ã¿ ã¬ã³ã¼ãã®ç·¨é
client_messages.title.edit_data_source=ãã¼ã¿ ã½ã¼ã¹ã®ç·¨é
client_messages.title.edit_data_xml_element=XML ãã¼ã¿è¦ç´ ã®ç·¨é
client_messages.title.edit_language=è¨èªã®ç·¨é
client_messages.title.edit_locale=ã­ã±ã¼ã«ã®ç·¨é
client_messages.title.external_reporting_data=å¤é¨ã¬ãã¼ã ãã¼ã¿
client_messages.title.image_library_upload=ç»åã©ã¤ãã©ãª ã¢ããã­ã¼ã
client_messages.title.input_filter=å¥åãã£ã«ã¿ã¼
client_messages.title.insert_attribute=å±æ§ã®æ¿å¥
client_messages.title.insert_data_element=ãã¼ã¿è¦ç´ ã®æ¿å¥
client_messages.title.insert_message=ã¡ãã»ã¼ã¸ã®æ¿å¥
client_messages.title.inserts=æ¿å¥
client_messages.title.language=è¨èª
client_messages.title.manage_zone_message_priority=ã¾ã¼ã³ ã¡ãã»ã¼ã¸åªååº¦ã®ç®¡ç
client_messages.title.messages=ã¡ãã»ã¼ã¸
client_messages.title.move=ç§»å
client_messages.title.move_messages_to=ã¡ãã»ã¼ã¸ã®ç§»å...
client_messages.title.move_zone_to_section=ã¾ã¼ã³ãã»ã¯ã·ã§ã³ã«ç§»å
client_messages.title.note=æ³¨
client_messages.title.paragraph_styles=æ®µè½ã¹ã¿ã¤ã«
client_messages.title.preview=ãã¬ãã¥ã¼
client_messages.title.referenced_text_style=åç§ããããã­ã¹ã ã¹ã¿ã¤ã«
client_messages.title.remove_message=ã¡ãã»ã¼ã¸ã®åé¤
client_messages.title.save_before_edit=ä¿å­ãã¦ç·¨é
client_messages.title.set_modifiers_and_attributes=ã¢ãã£ãã¡ã¤ã¢ã¼ã¨å±æ§ã®è¨­å®
client_messages.title.set_touchpoints=ã¿ãããã¤ã³ãã®è¨­å®
client_messages.title.tags_for=ã¿ã°
client_messages.title.targeting=ã¿ã¼ã²ããè¨­å®
client_messages.title.template_modifiers=ãã³ãã¬ã¼ã ã¢ãã£ãã¡ã¤ã¢ã¼
client_messages.title.template_variants=ãã³ãã¬ã¼ãã®ããªã¢ã³ã
client_messages.title.text_styles=ãã­ã¹ã ã¹ã¿ã¤ã«
client_messages.title.touchpoints=ã¿ãããã¤ã³ã
client_messages.title.upload.messages=ã¡ãã»ã¼ã¸ã®ã¢ããã­ã¼ã
client_messages.title.upload.variants=ããªã¢ã³ãã®ã¢ããã­ã¼ã
client_messages.title.variable=å¤æ°
client_messages.title.variables=å¤æ°
client_messages.title.variant_visibility=ããªã¢ã³ãã®è¡¨ç¤ºæ¨©é
client_messages.title.whats_this=èª¬æ
client_messages.title.which.languages=è¨èª
client_messages.title.workflow_history=ã¯ã¼ã¯ãã­ã¼å±¥æ­´
client_messages.title.workgroup=ã¯ã¼ã¯ã°ã«ã¼ã
client_messages.title.workgroups=ã¯ã¼ã¯ã°ã«ã¼ã
client_messages.title.zone_paragraph_styles=ã¾ã¼ã³ã®æ®µè½ã¹ã¿ã¤ã«
client_messages.title.zone_text_styles=ã¾ã¼ã³ã®ãã­ã¹ã ã¹ã¿ã¤ã«
client_messages.title.zone_visibility=ã¾ã¼ã³ã®è¡¨ç¤º
code.label.datagroup.customer=é¡§å®¢
code.log.invalidpassword=ãã¹ã¯ã¼ããç¡å¹ã§ãã
code.log.tenantnotactive=ããã³ããã¢ã¯ãã£ãã«ãªã£ã¦ãã¾ããã
code.log.tenantnotmatch=ãã®ã¦ã¼ã¶ã¼åã§ä½¿ç¨ããã¦ããããã³ããæ­£ããããã¾ããã
code.log.usermaxattemp=ãµã¤ã³ã¤ã³ã®æå¤§è©¦è¡åæ°ãè¶ãã¾ããã
code.log.usernotactive=ã¦ã¼ã¶ã¼ãã¢ã¯ãã£ãåããã¦ãã¾ããã
code.log.usernotexist=ã¦ã¼ã¶ã¼åãç¡å¹ã§ãã
code.text.accessdenied=ãã®ãªã½ã¼ã¹ã®ã¢ã¯ã»ã¹è¨±å¯ãããã¾ããã
code.text.accountlocked=ãµã¤ã³ã¤ã³ã®æå¤§è©¦è¡åæ°ãè¶ãã¾ãããç®¡çèã«åãåããã¦ãã ããã
code.text.authenticationfailed=ãµã¤ã³ã¤ã³ã«å¤±æãã¾ãããä¼ç¤¾ãã¦ã¼ã¶ã¼åãã¾ãã¯ãã¹ã¯ã¼ããæ­£ãããªãå¯è½æ§ãããã¾ããç®¡çèã«åãåããã¦ãã ããã
code.text.company.invalid.group=ãµã¤ã³ã¤ã³ã«å¤±æãã¾ãããè¦æ±ããä¼ç¤¾ã¸ã®ãµã¤ã³ã¤ã³ã¯è¨±å¯ããã¦ãã¾ãããç®¡çèã«åãåããã¦ãã ããã
code.text.company.invalid.redirection.info=ãµã¤ã³ã¤ã³ã«å¤±æãã¾ãããè¦æ±ããä¼ç¤¾ã®ãªãã¤ã¬ã¯ãè¨­å®ãç¡å¹ã§ããç®¡çèã«åãåããã¦ãã ããã
code.text.company.invalid=ãµã¤ã³ã¤ã³ã«å¤±æãã¾ãããè¦æ±ããä¼ç¤¾ãã·ã¹ãã åã«å­å¨ãã¾ãããç®¡çèã«åãåããã¦ãã ããã
code.text.company.node.invalid=ãµã¤ã³ã¤ã³ã«å¤±æãã¾ãããè¦æ±ããã¤ã³ã¹ã¿ã³ã¹ãã·ã¹ãã åã«å­å¨ãã¾ãããURL ãç¢ºèªãããç®¡çèã«åãåããã¦ãã ããã
code.text.company.node.offline.with.parameters=è¦æ±ããã¤ã³ã¹ã¿ã³ã¹ \"{0}.{1}\" ããªãã©ã¤ã³ã§ããå¾ã§ããç´ãããç®¡çèã«åãåããã¦ãã ããã
code.text.company.node.offline=è¦æ±ããã¤ã³ã¹ã¿ã³ã¹ããªãã©ã¤ã³ã§ãã<br>å¾ã§ããç´ãããç®¡çèã«åãåããã¦ãã ããã
code.text.company.redirection.error=ãªãã¤ã¬ã¯ã ã¨ã©ã¼ã«ãããµã¤ã³ã¤ã³ã«å¤±æãã¾ãããç®¡çèã«åãåããã¦ãã ããã
code.text.company.redirection.info.missing=ãµã¤ã³ã¤ã³ã«å¤±æãã¾ãããè¦æ±ããä¼ç¤¾ã®ãªãã¤ã¬ã¯ãè¨­å®ãæå®ããã¦ãã¾ãããç®¡çèã«åãåããã¦ãã ããã
code.text.company.redirection.pod.unavailable=ãµã¤ã³ã¤ã³ã«å¤±æãã¾ãããè¦æ±ããã·ã¹ãã ã¯ç¾å¨ä½¿ç¨ã§ãã¾ãããå¾ã§ããç´ãããç®¡çèã«åãåããã¦ãã ããã
code.text.company.transition.redirection.info.missing=ãµã¤ã³ã¤ã³ã«å¤±æãã¾ãããè¦æ±ããä¼ç¤¾ã®ãã¬ãªãªã¼ã¹ ãã¼ã¸ã§ã³ã¯å­å¨ãã¾ãããç®¡çèã«åãåããã¦ãã ããã
code.text.idpid.invalid=ãµã¤ã³ã¤ã³ã«å¤±æãã¾ãããè¦æ±ãã IdpID ãã·ã¹ãã åã«å­å¨ãã¾ãããç®¡çèã«åãåããã¦ãã ããã
code.text.nochanges=å¤æ´åå®¹ãè¦ã¤ããã¾ãããå¤æ´ãè¡ã£ã¦ãã [ä¿å­] ãã¿ã³ãã¯ãªãã¯ãã¦ãã ããã
code.text.session.timeout=ãã®ã»ãã·ã§ã³ã«å¿è¦ãªã¦ã¼ã¶ã¼åã§ãµã¤ã³ã¤ã³ãã¦ããªãããã»ãã·ã§ã³ãã¿ã¤ã ã¢ã¦ããã¾ããã
code.text.signin.unsecure=æ¥ç¶ãã»ã­ã¥ãªãã£ä¿è­·ããã¦ããªããããµã¤ã³ã¤ã³ã«å¤±æãã¾ãããã»ã­ã¥ãªãã£ä¿è­·ãããæ¥ç¶ãä½¿ç¨ãã¦ãã ããã  
code.text.unabletosavesettings=ç³ãè¨³ããã¾ãããå¤æ´åå®¹ãä¿å­ã§ãã¾ããã§ããã
code.text.usersettingssaved=å¤æ´åå®¹ãä¿å­ããã¾ããã
email.content.password.change.notification={0}\n\n{1} ã®ãã¹ã¯ã¼ããå¤æ´ããã¾ããã
email.content.password.forget={0}ããã¹ã¯ã¼ãããå¿ãã§ãã?\n\nä¼æ¥­ã¤ã³ã¹ã¿ã³ã¹ {2} ã®ãã¹ã¯ã¼ãã®ãªã»ããè¦æ±ãåé ãã¾ããã\n\nãã¹ã¯ã¼ãããªã»ããããã«ã¯ãä¸è¨ã®ãªã³ã¯ãã¯ãªãã¯ãã¦ãã ãã (ã¾ãã¯ãURL ããä½¿ãã®ãã©ã¦ã¶ã¼ã«ã³ãã¼ãã¦è²¼ãä»ãã¦ãã ãã):\n{1}\n\nä¸è¨ã®ãªã³ã¯ãã¯ãªãã¯ããã¨ããã¹ã¯ã¼ããå¤æ´ããããã®ã»ã­ã¥ã¢ãªãã¼ã¸ãéãã¾ãã \n\n\nç®¡çèãã
email.content.password.reset.activate={0}ã«ããããã{1}!\n\nã¢ã«ã¦ã³ããæå¹ã«ãªãã¾ãããè³æ ¼æå ±ã¯æ¬¡ã®ã¨ããã§ãã\n\nä¼ç¤¾å: {2}\nã¦ã¼ã¶ã¼å: {3}\n\nãã¹ã¯ã¼ããè¨­å®ããã«ã¯ãä¸è¨ã®ãªã³ã¯ãã¯ãªãã¯ãã¦ãã ãã (ã¾ãã¯ãURL ããä½¿ãã®ãã©ã¦ã¶ã¼ã«ã³ãã¼ãã¦è²¼ãä»ãã¦ãã ãã):\n\n{4}\n\nä¸è¨ã®ãªã³ã¯ãã¯ãªãã¯ããã¨ããã¹ã¯ã¼ããå¤æ´ããããã®ã»ã­ã¥ã¢ãªãã¼ã¸ãéãã¾ãã\n\n\nç®¡çèãã
email.content.password.reset={0}ãä¼æ¥­ã¤ã³ã¹ã¿ã³ã¹ {2} ã®ãã¹ã¯ã¼ãããªã»ããããã¾ããã\n\næ°è¦ãã¹ã¯ã¼ããä½æããã«ã¯ãä¸è¨ã®ãªã³ã¯ãã¯ãªãã¯ãã¦ãã ãã (ã¾ãã¯ãURL ããä½¿ãã®ãã©ã¦ã¶ã¼ã«ã³ãã¼ãã¦è²¼ãä»ãã¦ãã ãã):\n\n{1}\n\nä¸è¨ã®ãªã³ã¯ãã¯ãªãã¯ããã¨ãæ°è¦ãã¹ã¯ã¼ããä½æããããã®ã»ã­ã¥ã¢ãªãã¼ã¸ãéãã¾ãã \n\n\nç®¡çèãã
email.content.task.assigned=ä»¥ä¸ã®ã¿ã¹ã¯ãå²ãå½ã¦ããã¾ãã:\n\n\tåå: {0}\n\tå¿è¦ãªã¢ã¯ã·ã§ã³: {1}\n\tæé: {2}\n\nè©³ç´°ã«ã¤ãã¦ã¯ã{3} ã«ãµã¤ã³ã¤ã³ãã¦ã¿ã¹ã¯ãç¢ºèªãã¦ãã ããã
email.subject.password.change.notification={0} ãã¹ã¯ã¼ãå¤æ´éç¥
email.subject.password.forget={0} ã®ãã¹ã¯ã¼ããå¿ããå ´å
email.subject.password.reset.activate=ä¼æ¥­ã¤ã³ã¹ã¿ã³ã¹ {1} ç¨ã® {0} ã¢ã«ã¦ã³ããæå¹ã«ãªãã¾ããã
email.subject.password.reset={0} ãã¹ã¯ã¼ã ãªã»ãã
email.subject.task.assigned={1} ã¿ã¹ã¯å²ãå½ã¦æ¸ã¿: {0}
email.workflow.approval.content.detail={0}\n\næ¬¡ã® {1} ãæ¿èªå¾ã¡ã§ã:\n\nåå: {2}\nã¢ã¯ã·ã§ã³: {3}
email.workflow.approval.content.dueby=\næé: {0}
email.workflow.approval.subject={1}: {0} - æ¿èªå¾ã¡
email.workflow.approvedbyother.content={0}\n\næ¬¡ã® {1} ãæ¿èªããã¾ãã:\n\nåå: {2}\nã¢ã¯ã·ã§ã³: {3}\n
email.workflow.approvedbyother.subject={1}: {0} - æ¿èªæ¸ã¿
email.workflow.common.content.footer=è©³ç´°ã«ã¤ãã¦ã¯ã{0} ã«ãµã¤ã³ã¤ã³ãã¦ç¢ºèªãã¦ãã ããã
email.workflow.common.content.footerwithurl=\n\nè©³ç´°ã«ã¤ãã¦ã¯ãä¸è¨ã®ãªã³ã¯ãã¯ãªãã¯ (ã¾ãã¯ãURL ããä½¿ãã®ãã©ã¦ã¶ã¼ã«ã³ãã¼ãã¦è²¼ãä»ã) ãã¦ç¢ºèªãã¦ãã ãã:\n\n{0}\n\n
email.workflow.duebyautoapprove.content={0}\n\næ¬¡ã® {1} ãèªåæ¿èªããã¾ãã:\n\nåå: {2}\nã¢ã¯ã·ã§ã³: {3}\n
email.workflow.duebyautoapprove.subject={1}: {0} - æ¿èªæ¸ã¿
email.workflow.duebynotify.content={0}\n\næ¬¡ã® {1} ã®æ¿èªæéãæéåãã«ãªãã¾ãã:\n\nåå: {2}\nã¢ã¯ã·ã§ã³: {3}\n
email.workflow.duebynotify.subject={1}: {0} - æ¿èªã¹ãããã®ã¿ã¤ã ã¢ã¦ã
email.workflow.reassign.content={0}\n\næ¬¡ã® {1} ãåå²ãå½ã¦ããã¾ãã:\n\nåå: {2}\nã³ã¡ã³ã: {3}\n
email.workflow.reassign.subject={1}: {0} - åå²ãå½ã¦æ¸ã¿
email.workflow.reject.content={0}\n\næ¬¡ã® {1} ãæå¦ãããããªãã«å²ãå½ã¦ããã¾ãã:\n\nåå: {2}\nã¢ã¯ã·ã§ã³: {3}\n
email.workflow.reject.subject={1}: {0} - æ¿èªæå¦æ¸ã¿
error.branch.can.not.be.deleted=ãã¡ã¤ã³ã¯åé¤ã§ãã¾ãã
error.branch.code.must.be.entered=ãã¡ã¤ã³ ã³ã¼ããå¥åããå¿è¦ãããã¾ã
error.branch.code.must.be.max.4.long=ãã¡ã¤ã³ ã³ã¼ãã®é·ãã 4 ãè¶ãããã¨ã¯ã§ãã¾ãã
error.branch.code.must.be.unique=ãã¡ã¤ã³ ã³ã¼ãã¯ä¸æã§ããå¿è¦ãããã¾ã
error.branch.max.number.of.domain.exceeded.under.your.licence=ã©ã¤ã»ã³ã¹ã«åºã¥ããã¡ã¤ã³ã®æå¤§æ°ãè¶ãã¾ãããè©³ç´°ã«ã¤ãã¦ã¯ãç®¡çèã«åãåããã¦ãã ããã
error.branch.max.number.of.production.instances.exceeded.under.your.licence=ã©ã¤ã»ã³ã¹ã«åºã¥ããã­ãã¯ã·ã§ã³ ã¤ã³ã¹ã¿ã³ã¹ã®æå¤§æ°ãè¶ãã¾ããã
error.branch.max.number.of.sandbox.instances.exceeded.under.your.licence=ã©ã¤ã»ã³ã¹ã«åºã¥ããµã³ãããã¯ã¹ ã¤ã³ã¹ã¿ã³ã¹ã®æå¤§æ°ãè¶ãã¾ããã
error.branch.must.be.selected=ãã¡ã¤ã³ãé¸æããå¿è¦ãããã¾ã
error.branch.name.must.be.unique=ãã¡ã¤ã³åã¯ä¸æã§ããå¿è¦ãããã¾ã
error.branch.parent.domain.configuration.is.not.completed=è¦ªãã¡ã¤ã³ã®æ§æãã¾ã å®äºãã¦ãã¾ããã
error.branch.ssoidpid.must.be.entered=SSO IdP ID ãå¥åããå¿è¦ãããã¾ã
error.branch.ssosecretkey.must.be.entered=SSO ç§å¯éµãå¥åããå¿è¦ãããã¾ã 
error.cannot.change.insert.selector.document.in.use.by.insert.schedules=ã¿ãããã¤ã³ãæ¿å¥ã»ã¬ã¯ã¿ã¼ãå¤æ´ã§ãã¾ãããæ¬¡ã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã§ä½¿ç¨ããã¦ãã¾ã: {0}
error.cannot.change.insert.selector.document.in.use.by.inserts=ã¿ãããã¤ã³ãæ¿å¥ã»ã¬ã¯ã¿ã¼ãå¤æ´ã§ãã¾ãããæ¬¡ã®æ¿å¥ã§ä½¿ç¨ããã¦ãã¾ã: {0}
error.cannot.change.primary.driver.once.communications.authored=ã³ãã¥ãã±ã¼ã·ã§ã³ã®ä½æå¾ã«ãã©ã¤ããª ãã©ã¤ãã¼ ã¨ã³ããªãå¤æ´ãããã¨ã¯ã§ãã¾ããããã®ã¿ãããã¤ã³ãã®ã³ãã¥ãã±ã¼ã·ã§ã³ããã¹ã¦åé¤ããããä¸åº¦ããç´ãã¦ãã ããã
error.communication.must.specify.positive.integer.number.copies=å°ãªãã¨ã 1 ã®ã³ãã¼ãé åºä»ãããå¿è¦ãããã¾ãã
error.company.notfound=ç³ãè¨³ãããã¾ãããå¥åããä¼ç¤¾ã¯å­å¨ãã¾ãããããä¸åº¦ããç´ãã¦ãã ããã
error.composition.package.is.being.referenced.on.delete=é¸æããã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸ã¯åç§ããã¦ãããããåé¤ã§ãã¾ããã
error.condition.name.cannot.be.empty=æ¡ä»¶åãç©ºã«ãããã¨ã¯ã§ãã¾ããã
error.condition.variable.not.selected={0} ã§æ¯è¼ç¨ã®å¤æ°ãé¸æããã¦ãã¾ããã
error.constant.language.override.value.empty=è¨èªã®ä¸æ¸ãã®å¤ãå¥åãã¦ãã ãã
error.content.library.is.being.referenced.on.archive.delete=ãã®ç»åã©ã¤ãã©ãª ã¢ã¤ãã ã®ã¤ã³ã¹ã¿ã³ã¹ãåç§ããã¦ãã¾ããåé¤ãè¡ãåã«ãã¹ã¦ã®åç§ãåé¤ãã¦ãã ããã
error.content.library.is.being.referenced.on.archive=é¸æããç»åãã·ã¹ãã åã®ã¢ã»ããã«ãã£ã¦åç§ããã¦ãã¾ããåç§ãåé¤ãã¦ç»åãã¢ã¼ã«ã¤ããã¦ãã ããã
error.dataadmin.another.tag.start.customer=ãã¼ã¿ ã½ã¼ã¹ {1} ã§å¥ã® XML ãã¼ã¿ ã¿ã° {0} ãæ¢ã«ãé¡§å®¢ã®éå§ãã¨ãã¦ãã¼ã¯ããã¦ãã¾ãã
error.dataadmin.data.group.inconsistance=ãã¼ã¿ ã°ã«ã¼ã {0} ã¯ XML ä¸ä½ã¿ã°ã®ãããããå±ãããã¼ã¿ ã°ã«ã¼ã {1} ã®å­ã§ã¯ããã¾ããã
error.dataadmin.data.group.not.seleted=XML ã¿ã°ã§ãã¼ã¿ ã°ã«ã¼ããéå§ããå ´åã¯ããã¼ã¿ ã°ã«ã¼ããé¸æããå¿è¦ãããã¾ãã
error.dataadmin.data.group.startedbyanothertag=ãã¼ã¿ ã°ã«ã¼ã {0} ã¯ XML ã¿ã° {1} ã§éå§ããã¾ãã
error.dataadmin.data.tag.child.tag.exist=ãã®ã¿ã°ã«ã¯æ¢ã«é¢é£ä»ããããã¿ã°ãããã¾ãããã¼ã¿è¦ç´ ãè¿½å ãããã¨ã¯ã§ãã¾ããã
error.dataadmin.data.tag.value.data.exist=ãã®ã¿ã°ã«ã¯æ¢ã«ãã¼ã¿è¦ç´ ãå­å¨ãã¾ãããã¼ã¿è¦ç´ ã¾ãã¯ã¿ã°ãè¿½å ãããã¨ã¯ã§ãã¾ããã
error.dataadmin.not.descendent.start.customer=ãã®ã¿ã°ã®è¦ªè¦ç´ ããé¡§å®¢ã®éå§ãã¨ãã¦ãã¼ã¯ããã¦ãã¾ããããã®ã¿ã°ã¯ç¹°ãè¿ãã«ã¯ã§ãã¾ããã
error.document.cannot.be.shared.and.enabled.for.tp.selection.management=ã¿ãããã¤ã³ããå±æããåæã«ã¿ãããã¤ã³ã ããªã¢ã³ãç®¡çã§æå¹ã«ãããã¨ã¯ã§ãã¾ãã
error.document.cannot.change.tp.selection.management.selector=ã¿ãããã¤ã³ã ããªã¢ã³ãç®¡çã§ä½æããããªã¢ã³ã/ã¡ãã»ã¼ã¸ãå­å¨ãããããã¿ãããã¤ã³ã ããªã¢ã³ãç®¡çãå¤æ´ã§ãã¾ãã
error.document.cannot.turn.off.tp.selection.management=ã¿ãããã¤ã³ã ããªã¢ã³ãç®¡çã§ä½æããããªã¢ã³ã/ã¡ãã»ã¼ã¸ãå­å¨ãããããã¿ãããã¤ã³ã ããªã¢ã³ãç®¡çã§ã¿ãããã¤ã³ããç¡å¹ã«ã§ãã¾ãã
error.document.composition.version.invalid=ã³ã³ãã¸ã·ã§ã³ ãã¼ã¸ã§ã³ãç¡å¹ã§ã
error.document.composition.version.required=ã³ã³ãã¸ã·ã§ã³ ãã¼ã¸ã§ã³çªå·ãå¥åãã¦ãã ãã
error.document.connector.selection.required=ã³ãã¯ã¿ã¼ã®é¸æã¯å¿é ã§ã
error.document.has.optional.messages.cannot.change.to.message.selectable=ã¿ãããã¤ã³ãã«ãªãã·ã§ã³ã®ã°ã­ã¼ãã« ã¡ãã»ã¼ã¸ãå­å¨ããããããã¬ã­ã·ãã« ã¿ãããã¤ã³ãã«å¤æã§ãã¾ããã
error.document.in.use.by.insert.schedules=æ¿å¥ã§ã¿ãããã¤ã³ããç¡å¹ã«ã§ãã¾ãããæ¬¡ã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã«ãã£ã¦ä½¿ç¨ããã¦ãã¾ã: {0}
error.document.in.use.by.inserts=æ¿å¥ã§ã¿ãããã¤ã³ããç¡å¹ã«ã§ãã¾ãããæ¬¡ã®æ¿å¥ã«ãã£ã¦ä½¿ç¨ããã¦ãã¾ã: {0}
error.document.name.unique=ã¿ãããã¤ã³ãåãæ¢ã«ä½¿ç¨ããã¦ãã¾ããä¸æã®ååãå¥åãã¦ãã ããã
error.document.proof.data.range.too.large=ãã¼ã¿ãæ ¡æ­£ããé¡§å®¢ã®ç¯å²ãã·ã¹ãã  ãã­ããã£ã§è¨±å¯ãããç¯å²ãããå¤§ãããªã£ã¦ãã¾ã
error.document.proofing.data.customer.range.not.provided=å¤æ´ãä¿å­ããåã«ãé¸æãããåãã¼ã¿ ãªã½ã¼ã¹ã§æ ¡æ­£ããã®ã«ä½¿ç¨ããé¡§å®¢ã¬ã³ã¼ãã®ç¯å²ã®å¤ãæå®ããå¿è¦ãããã¾ãã
error.document.proofing.data.range.must.be.numbers.greater.than.zero=é¡§å®¢ã¬ã³ã¼ãã®ç¯å²ã¯ 0 ï½ 99,999,999 ã®éã®æ°å¤ã§æå®ãã¦ãã ãã
error.document.proofing.data.upper.range.must.be.greater.than.lower.range=é¡§å®¢ã¬ã³ã¼ãã®ä¸éã«ã¯ãä¸éä»¥ä¸ã®æ°å¤ãæå®ãã¦ãã ãã
error.email.template.file.format=ãã³ãã¬ã¼ãã®ã¢ããã­ã¼ãã«ã¯ ZIP å½¢å¼ã®ããã±ã¼ã¸ã®ã¿ä½¿ç¨ã§ãã¾ã
error.emailnotfound=ç³ãè¨³ãããã¾ãããå¥åããé»å­ã¡ã¼ã« ã¢ãã¬ã¹ãè¦ã¤ããã¾ããã§ãããããä¸åº¦ããç´ãã¦ãã ããã
error.embedded.content.has.advanced.content.cannot.make.global=ã°ã­ã¼ãã«ã¸ã®å¤æãè¡ãåã«ãã³ã³ãã³ãããã®ãã¹ã¦ã®å¤æ°ãã¹ãã¼ã ãã­ã¹ãããã­ã¹ã ã¹ã¿ã¤ã«ãããã³æ®µè½ã¹ã¿ã¤ã«ãåé¤ãã¦ãã ããã
error.embedded.content.has.targeting.cannot.make.global=ã¿ã¼ã²ããè¨­å®ãåé¤ãããã¾ã§ã¹ãã¼ã ãã­ã¹ããã°ã­ã¼ãã«ã«å¤æã§ãã¾ããã
error.embedded.content.has.timing.cannot.make.global=ã¿ã¤ãã³ã°ãåé¤ãããã¾ã§ã¹ãã¼ã ãã­ã¹ããã°ã­ã¼ãã«ã«å¤æã§ãã¾ããã
error.embedded.content.id.notfound=ç³ãè¨³ãããã¾ãããæå®ãããåãè¾¼ã¿ã³ã³ãã³ã ID ãè¦ã¤ããã¾ããã§ããã
error.embedded.content.is.being.referenced.on.archive.delete=ãã®ã¹ãã¼ã ãã­ã¹ãã®ã¤ã³ã¹ã¿ã³ã¹ãåç§ããã¦ãã¾ããåé¤ãè¡ãåã«ãã¹ã¦ã®åç§ãåé¤ãã¦ãã ããã
error.embedded.content.is.being.referenced.on.archive=é¸æããã¹ãã¼ã ãã­ã¹ããã·ã¹ãã åã®ã¢ã»ããã«ãã£ã¦åç§ããã¦ãã¾ããåç§ãåé¤ãã¦ã¢ã¼ã«ã¤ããã¦ãã ããã
error.embedded.content.selector.not.mapped.to.touchpoints=é¸æãããã¿ãããã¤ã³ãã«ãããã³ã°ãããªãã£ããããæå®ããã»ã¬ã¯ã¿ã¼ã§åçã¹ãã¼ã ãã­ã¹ããå¤åããããã¨ãã§ãã¾ãã
error.embedded.content.touchpoint.can.not.removed=ãã®ã¹ãã¼ã ãã­ã¹ãã§æ¢ã«åç§ããã¦ããããããªã¹ãè¡¨ç¤ºãããã¿ãããã¤ã³ãã¯é¸æããã¿ãããã¤ã³ã ãªã¹ãã«å«ã¾ãã¦ããå¿è¦ãããã¾ã:
error.embedded.content.variables.not.mapped.to.this.embedded.text.content=ãã®ã¹ãã¼ã ãã­ã¹ãã§ä½¿ç¨ããã¦ãã 1 ã¤ä»¥ä¸ã®å¤æ°ãæ­£ãããããã³ã°ããã¦ãã¾ãããå²ãå½ã¦ãããã¿ãããã¤ã³ãã®å¤æ°ã®ãããã³ã°ãè§£æ±ºãã¦ãã ããã\n\n
error.emptyemail=ç³ãè¨³ãããã¾ãããé»å­ã¡ã¼ã«ãç©ºã§ããããä¸åº¦ããç´ãã¦ãã ããã
error.enter.valid.value=æå¹ãªå¤ãå¥åãã¦ãã ãã
error.export.message.filter.date.cannot.be.in.future=æ¥ä»ãå°æ¥ã«ãããã¨ã¯ã§ãã¾ããã
error.export.message.filter.date.required=æ¥ä»ãå¿è¦ã§ã (ãã£ã«ã¿ã¼ãæå¹ã«ããå ´å)ã
error.export.touchpointselection.filter.date.cannot.be.in.future={0} ãå°æ¥ã«ãããã¨ã¯ã§ãã¾ããã
error.export.touchpointselection.filter.date.required={0} ãå¿è¦ã§ã ({0} ãã£ã«ã¿ã¼ãæå¹ã«ããå ´å)ã
error.export.touchpointselection.fromdate.cannot.be.after.todate=éå§æ¥ãçµäºæ¥ãããå¾ã«ãããã¨ã¯ã§ãã¾ãããæå¹ãªæ¥ä»ã®æéãé¸æãã¦ãã ããã
error.image.library.has.advanced.content.cannot.make.global=ã°ã­ã¼ãã«ã¸ã®å¤æãè¡ãåã«ãã³ã³ãã³ãããã®ãã¹ã¦ã®å¤æ°ãç»åã©ã¤ãã©ãªããã­ã¹ã ã¹ã¿ã¤ã«ãããã³æ®µè½ã¹ã¿ã¤ã«ãåé¤ãã¦ãã ããã
error.image.library.selector.not.mapped.to.touchpoints=é¸æãããã¿ãããã¤ã³ãã«ãããã³ã°ãããªãã£ããããæå®ããã»ã¬ã¯ã¿ã¼ã§ç»åã©ã¤ãã©ãªãå¤åããããã¨ãã§ãã¾ãã
error.image.library.touchpoint.can.not.removed=ãã®ç»åã©ã¤ãã©ãªã§æ¢ã«åç§ããã¦ããããããªã¹ãè¡¨ç¤ºãããã¿ãããã¤ã³ãã¯é¸æããã¿ãããã¤ã³ã ãªã¹ãã«å«ã¾ãã¦ããå¿è¦ãããã¾ã:
error.image.library.variables.not.mapped.to.this.embedded.text.content=ãã®ã¹ãã¼ã ãã­ã¹ãã§ä½¿ç¨ããã¦ãã 1 ã¤ä»¥ä¸ã®å¤æ°ãæ­£ãããããã³ã°ããã¦ãã¾ãããå²ãå½ã¦ãããã¿ãããã¤ã³ãã®å¤æ°ã®ãããã³ã°ãè§£æ±ºãã¦ãã ããã\n\n
error.image.library.variables.not.mapped.to.this.image.library.content=ãã®ç»åã§ä½¿ç¨ããã¦ãã 1 ã¤ä»¥ä¸ã®å¤æ°ãæ­£ãããããã³ã°ããã¦ãã¾ãããå²ãå½ã¦ãããã¿ãããã¤ã³ãã®å¤æ°ã®ãããã³ã°ãè§£æ±ºãã¦ãã ããã\n\n
error.image.library.variables.not.mapped=ãã®ç»åã§ä½¿ç¨ããã¦ãã 1 ã¤ä»¥ä¸ã®å¤æ°ãæ­£ãããããã³ã°ããã¦ãã¾ããã  å²ãå½ã¦ãããã¿ãããã¤ã³ãã®å¤æ°ã®ãããã³ã°ãè§£æ±ºãã¦ãã ããã
error.import.datasourceassociationisnull=ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³ã null ã§ãããã®ãã¡ã¤ã«ãã¤ã³ãã¼ãã§ãã¾ããã
error.import.datasourceassociationuniquename=ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³åãæ¢ã«ä½¿ç¨ããã¦ãã¾ãããã®ãã¡ã¤ã«ãã¤ã³ãã¼ãã§ãã¾ããã
error.import.file.is.a.container=ãã¡ã¤ã« \"{0}\" ã¯ã³ã³ããã¼ã®ã¨ã¯ã¹ãã¼ã ãã¡ã¤ã«ã§ããã¤ã³ãã¼ãããã¿ãããã¤ã³ã ãã¡ã¤ã«ãé¸æãã¦ãã ããã
error.import.file.is.an.old.version=ãã¡ã¤ã« \"{0}\" ã¯ã¿ãããã¤ã³ãã®ã¤ã³ãã¼ãããµãã¼ãããã¦ããªããã¼ã¸ã§ã³ã§ãã
error.import.file.is.empty=å¥åãã£ã¼ã«ããç©ºã§ããã¤ã³ãã¼ããããã¡ã¤ã«ãé¸æãã¦ãã ããã
error.import.file.is.not.valid=ãã¡ã¤ã« \"{0}\" ãç¡å¹ã§ãã
error.import.in.process=ã¤ã³ãã¼ãã¯æ¢ã«å®è¡ä¸­ã§ãã
error.input.charrestriction={0} ã§ã¯æ¬¡ã®æå­ã®ã¿ãä½¿ç¨ã§ãã¾ã: {1}
error.input.email.charrestriction={0} ã« <EMAIL> å½¢å¼ã®æå¹ãªé»å­ã¡ã¼ã« ã¢ãã¬ã¹ã§ã¯ãªãå¤ãå«ã¾ãã¦ãã¾ã
error.input.mandatory={0} ã¯å¿é ã§ã
error.input.maxlength={0} ã¯ {1} æå­ä»¥åã§ãªããã°ãªãã¾ãã
error.input.maxvalue={0} ã®æå¤§å¤ã¯ {1} ã§ã
error.input.minlength={0} ã¯ {1} æå­ä»¥ä¸ã§ãªããã°ãªãã¾ãã
error.input.minvalue={0} ã®æå°å¤ã¯ {1} ã§ã
error.input.numberofdecimals.range=å°æ°ç¹ä»¥ä¸ã®æ¡æ°ã®å¤ã«ã¯ 0 ï½ 6 ã®æ°ãæå®ã§ãã¾ã
error.input.script.injection={0} ã«ä¸æ­£ãªæå­ã·ã¼ã±ã³ã¹ãå«ã¾ãã¦ãã¾ã
error.insert.cannot.be.archived.in.use.by.active.schedules.for.list={0} ã¯ãã¢ã¯ãã£ããªæ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã¾ãã¯éããªãã¢ã¯ãã£ãã«ãªãæ¿å¥ã¹ã±ã¸ã¥ã¼ã«åã®ãã³ã«å²ãå½ã¦ããã¦ãããããã¢ã¼ã«ã¤ãã§ãã¾ãããã¢ã¼ã«ã¤ãããã«ã¯ãæåã«ç¾å¨ããã³å°æ¥ã®ã¿ã¤ãã³ã°ãå«ããã¹ã¦ã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ããæ¿å¥ããã¿ããããå¿è¦ãããã¾ãã
error.insert.cannot.be.archived.in.use.by.active.schedules=ãã®æ¿å¥ã¯ãã¢ã¯ãã£ããªæ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã¾ãã¯éããªãã¢ã¯ãã£ãã«ãªãæ¿å¥ã¹ã±ã¸ã¥ã¼ã«åã®ãã³ã«å²ãå½ã¦ããã¦ãããããã¢ã¼ã«ã¤ãã§ãã¾ãããã¢ã¼ã«ã¤ãããã«ã¯ãæåã«ç¾å¨ããã³å°æ¥ã®ã¿ã¤ãã³ã°ãå«ããã¹ã¦ã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ãããã®æ¿å¥ããã¿ããããå¿è¦ãããã¾ãã
error.insert.cannot.be.deactivated.in.use.by.active.schedules.for.list={0} ã¯éã¢ã¯ãã£ãã«ã§ãã¾ãããæ¬¡ã®ã¢ã¯ãã£ã/æ¿èªå¾ã¡ã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã§ä½¿ç¨ããã¦ãã¾ã: {1}
error.insert.cannot.be.deactivated.in.use.by.active.schedules=ãã®æ¿å¥ã¯éã¢ã¯ãã£ãã«ã§ãã¾ãããæ¬¡ã®ã¢ã¯ãã£ã/æ¿èªå¾ã¡ã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã§ä½¿ç¨ããã¦ãã¾ã: {0}
error.insert.cannot.be.discarded.in.use.by.schedules.for.list={0} ã¯ç ´æ£ã§ãã¾ãããæ¬¡ã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã§ä½¿ç¨ããã¦ãã¾ã: {1}
error.insert.cannot.be.discarded.in.use.by.schedules=ãã®æ¿å¥ã¯ç ´æ£ã§ãã¾ãããæ¬¡ã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã§ä½¿ç¨ããã¦ãã¾ã: {0}
error.insert.firstpage.weight.range=æåã®ãã¼ã¸ã®ééã«ã¯ 0.0001 ï½ 99.9999 ã®æ°å¤ãå¥åãã¦ãã ãã
error.insert.list.search.requirement=æ¤ç´¢ããã«ã¯ãååã¾ãã¯ ID ã®å¤ãæå®ããå¿è¦ãããã¾ã
error.insert.name.unique=ååãæ¢ã«ä½¿ç¨ããã¦ãã¾ããä¸æã®ååãå¥åãã¦ãã ããã
error.insert.otherpages.weight.range=ä»ã®ãã¼ã¸ã®ééã«ã¯ 0.0001 ï½ 99.9999 ã®æ°å¤ãå¥åãã¦ãã ãã
error.insert.selectdocument=ã¿ãããã¤ã³ããé¸æãã¦ãã ãã
error.insert.stockid.unique=ã¹ããã¯ ID ãæ¢ã«ä½¿ç¨ããã¦ãã¾ããä¸æã®ã¹ããã¯ ID ãå¥åãã¦ãã ããã
error.insert.weight.range=ééã«ã¯ 0.0001 ï½ 99.9999 ã®æ°å¤ãå¥åãã¦ãã ãã
error.insertschedule.cannot.roll.forward.selectors.combination.is.in.use.by.another.schedule.for.list=ã»ã¬ã¯ã¿ã¼ã® 1 ã¤ (\"{1}\") ã \"{2}\" ã¨ããååã®å¥ã®ã¹ã±ã¸ã¥ã¼ã«ã«ãã£ã¦æ¢ã«ä½¿ç¨ããã¦ããããã{0} ãã­ã¼ã« ãã©ã¯ã¼ãã§ãã¾ãã
error.insertschedule.cannot.roll.forward.selectors.combination.is.in.use.by.another.schedule=ã»ã¬ã¯ã¿ã¼ã® 1 ã¤ (\"{0}\") ã \"{1}\" ã¨ããååã®å¥ã®ã¹ã±ã¸ã¥ã¼ã«ã«ãã£ã¦æ¢ã«ä½¿ç¨ããã¦ããããããã®ã¹ã±ã¸ã¥ã¼ã«ãã­ã¼ã« ãã©ã¯ã¼ãã§ãã¾ãã
error.insertschedule.end.date.mandatory.for.release.for.approval.for.list=ãªãªã¼ã¹ãã¦æ¿èªãåããã«ã¯ãåãã£ã¦ {0} ã®æçµæ¥ãè¨­å®ããå¿è¦ãããã¾ã
error.insertschedule.end.date.mandatory.for.release.for.approval=ãªãªã¼ã¹ãã¦æ¿èªãåããã«ã¯ãåãã£ã¦ãã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã®æçµæ¥ãè¨­å®ããå¿è¦ãããã¾ã
error.insertschedule.end.date.mandatory.for.release.for.tenants.for.list=ããã³ãã«å¯¾ãã¦ãªãªã¼ã¹ããã«ã¯ãåãã£ã¦ {0} ã®æçµæ¥ãè¨­å®ããå¿è¦ãããã¾ã
error.insertschedule.end.date.mandatory.for.release.for.tenants=ããã³ãã«å¯¾ãã¦ãªãªã¼ã¹ã«ã¯ãåãã£ã¦ãã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã®æçµæ¥ãè¨­å®ããå¿è¦ãããã¾ã
error.insertschedule.enddate.after.next.schedule.start.date=çµäºæ¥ã¯æ¬¡ã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã®éå§æ¥ãããåã§ãªããã°ãªãã¾ãã
error.insertschedule.insert.timings.invalid={0} ã®çµäºæ¥ãéå§æ¥ãããåã«ãããã¨ã¯ã§ãã¾ãã
error.insertschedule.insert.timings.prior.to.schedule.timing=ã¹ã±ã¸ã¥ã¼ã«ã®ã¿ã¤ãã³ã°ãããåã§ãããã {0} ã®ã¿ã¤ãã³ã°ã¯ç¡å¹ã§ã
error.insertschedule.insert.timings.start.date={0} ã®éå§æ¥ãæå®ãã¦ãã ãã
error.insertschedule.list.search.requirement=æ¤ç´¢ããã«ã¯ãååãIDãã¾ãã¯ã­ã¼ã¯ã¼ãã®å¤ãæå®ããå¿è¦ãããã¾ã
error.insertschedule.no.inserts.for.list={0} ã«ã¯ãã³ã«å²ãå½ã¦ãããæ¿å¥ãå­å¨ããªãããããªãªã¼ã¹ãã¦æ¿èªãåãããã¨ã¯ã§ãã¾ãã
error.insertschedule.no.inserts=ãã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã«ã¯ãã³ã«å²ãå½ã¦ãããæ¿å¥ãå­å¨ããªãããããªãªã¼ã¹ãã¦æ¿èªãåãããã¨ã¯ã§ãã¾ãã
error.insertschedule.no.selector.provided.for.release.for.approval.for.list={0} ããªãªã¼ã¹ãã¦æ¿èªãåããåã«é¸æãã¼ã¿ãæå®ãã¦ãã ãã
error.insertschedule.no.selector.provided.for.release.for.approval=ãã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ããªãªã¼ã¹ãã¦æ¿èªãåããåã«é¸æãã¼ã¿ãæå®ãã¦ãã ãã
error.insertschedule.no.selector.provided.for.release.for.use.for.list={0} ããªãªã¼ã¹ãã¦ä½¿ç¨ããåã«é¸æãã¼ã¿ãæå®ãã¦ãã ãã
error.insertschedule.no.selector.provided.for.release.for.use=ãã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ããªãªã¼ã¹ãã¦ä½¿ç¨ããåã«é¸æãã¼ã¿ãæå®ãã¦ãã ãã
error.insertschedule.numberofbins.click.set=[è¨­å®] ãã¯ãªãã¯ãã¦ãã³ã®æ°ãæå®ãã¦ãã ãã
error.insertschedule.numberofbins.invalid=ãã³ã®æ°ã«ã¯ã¼ã­ä»¥å¤ã®æå¹ãªå¤ãå¥åãã¦ãã ãã
error.insertschedule.numberofsheets.invalid=ã·ã¼ãã®æ°ã«ã¯ã¼ã­ä»¥å¤ã®æå¹ãªå¤ãå¥åãã¦ãã ãã
error.insertschedule.rate.sheet.mandatory.for.release.for.approval.for.list=ãªãªã¼ã¹ãã¦æ¿èªãåããã«ã¯ã{0} ã«å°ãªãã¨ã 1 ã¤ã®æéè¡¨ãå¿è¦ã§ã
error.insertschedule.rate.sheet.mandatory.for.release.for.approval=ãªãªã¼ã¹ãã¦æ¿èªãåããã«ã¯ããã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã«å°ãªãã¨ã 1 ã¤ã®æéè¡¨ãå¿è¦ã§ã
error.insertschedule.rate.sheet.mandatory.for.release.to.tenants=ããã³ãã«å¯¾ãã¦ãªãªã¼ã¹ããã«ã¯ããã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã«å°ãªãã¨ã 1 ã¤ã®æéè¡¨ãå¿è¦ã§ã
error.insertschedule.rate.sheet.mandatory=å°ãªãã¨ã 1 ã¤ã®æéè¡¨ãè¿½å ãã¦ãã ãã
error.insertschedule.scheduleid.unique=ã¹ã±ã¸ã¥ã¼ã« ID ãæ¢ã«ä½¿ç¨ããã¦ãã¾ããä¸æã®ã¹ã±ã¸ã¥ã¼ã« ID ãå¥åãã¦ãã ããã
error.insertschedule.selectors.add.not.all.values.provided=ãã¹ã¦ã®ã»ã¬ã¯ã¿ã¼å¤ãæå®ãã¦ãã ãã
error.insertschedule.selectors.combination.is.added.before=\"{0}\" ã¯æ¢ã«è¿½å ããã¦ãã¾ã
error.insertschedule.selectors.combination.is.in.use.by.another.schedule=\"{0}\" ã¯ \"{1}\" ã¨ããååã®ä»ã®ã¹ã±ã¸ã¥ã¼ã«ã§æ¢ã«ä½¿ç¨ããã¦ãããããè¿½å ã§ãã¾ãã
error.insertschedule.selectors.remove.no.value.provided=å°ãªãã¨ã 1 ã¤ã®ã»ã¬ã¯ã¿ã¼å¤ãæå®ãã¦ãã ãã
error.insertschedule.selectors.remove.no.value.to.remove=ãã®ã¹ã±ã¸ã¥ã¼ã«ã«å¯¾ãã¦å®ç¾©ãããã»ã¬ã¯ã¿ã¼ ãã¼ã¿ãå­å¨ãã¾ãã
error.insertschedule.startdate.before.previous.schedule.end.date=éå§æ¥ã¯åã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã®çµäºæ¥ããå¾ã§ãªããã°ãªãã¾ãã
error.insertschedule.using.inactive.inserts.for.list=æ¬¡ã®éã¢ã¯ãã£ããªæ¿å¥ãä½¿ç¨ãã¦ããããã{0} ããªãªã¼ã¹ãã¦æ¿èªãåãããã¨ã¯ã§ãã¾ãã: {1}
error.insertschedule.using.inactive.inserts=æ¬¡ã®éã¢ã¯ãã£ããªæ¿å¥ãä½¿ç¨ãã¦ããããããã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ããªãªã¼ã¹ãã¦æ¿èªãåãããã¨ã¯ã§ãã¾ãã: {0}
error.invalidcompany=ç³ãè¨³ãããã¾ãããå¥åããä¼ç¤¾ãç¡å¹ã§ããããä¸åº¦ããç´ãã¦ãã ããã
error.invalidemail=ç³ãè¨³ãããã¾ãããå¥åããé»å­ã¡ã¼ã« ã¢ãã¬ã¹ãç¡å¹ã§ããããä¸åº¦ããç´ãã¦ãã ããã
error.invalidusername=ç³ãè¨³ãããã¾ãããå¥åããã¦ã¼ã¶ã¼åãç¡å¹ã§ããããä¸åº¦ããç´ãã¦ãã ããã
error.listed.touchpoints.must.be.selected.due.to.image.library.reference=ãã®ç»åã§æ¢ã«åç§ããã¦ããããããªã¹ãè¡¨ç¤ºãããã¿ãããã¤ã³ããé¸æããã¿ãããã¤ã³ã ãªã¹ãã«å«ããå¿è¦ãããã¾ã
error.listed.touchpoints.must.be.selected.due.to.reference=ãã®ã¹ãã¼ã ãã­ã¹ãã§æ¢ã«åç§ããã¦ããããããªã¹ãè¡¨ç¤ºãããã¿ãããã¤ã³ããé¸æå¯¾è±¡ã«å«ããå¿è¦ãããã¾ã
error.maintenance.enddate.must.be.entered=çµäºæ¥ãå¥åããå¿è¦ãããã¾ã
error.maintenance.endtime.cannot.be.same=éå§æ¥ã¨çµäºæéãåãã«ãããã¨ã¯ã§ãã¾ãã
error.maintenance.message.must.be.entered=ã¡ãã»ã¼ã¸ãå¥åããå¿è¦ãããã¾ã
error.maintenance.startdate.must.be.before.enddate=éå§æ¥ã¯çµäºæ¥ãããåã§ãªããã°ãªãã¾ãã
error.maintenance.startdate.must.be.entered=éå§æ¥ãå¥åããå¿è¦ãããã¾ã
error.maintenance.time.cannot.set.to.past=æ¥ä»ãéå»ã«è¨­å®ãããã¨ã¯ã§ãã¾ãã
error.message.admin.role.rolenameused=å½¹å²åãä½¿ç¨ããã¦ãã¾ãã
error.message.admin.securitySettings.maxattempts=[ã­ãã¯ã¢ã¦ãããã¾ã§ã®åæ°] ã¯ 3 ï½ 12 ã®æ°å¤ã«ãã¦ãã ããã
error.message.admin.securitySettings.maximumPasswordLength.lessthanminimum=[ãã¹ã¯ã¼ãã®æå¤§æå­æ°] ã¯ [ãã¹ã¯ã¼ãã®æå°æå­æ°] ãããå¤§ãããã¦ãã ããã
error.message.admin.securitySettings.maximumPasswordLength.outofrange=[ãã¹ã¯ã¼ãã®æå¤§æå­æ°] ã¯ 6 ï½ 32 ã®éã®æ°å¤ã«ãã¦ãã ããã
error.message.admin.securitySettings.maximumUserIdLength.lessthanminimum=[ã¦ã¼ã¶ã¼åã®æå¤§æå­æ°] ã¯ [ã¦ã¼ã¶ã¼åã®æå°æå­æ°] ãããå¤§ãããã¦ãã ããã
error.message.admin.securitySettings.maximumUserIdLength.outofrange=[ã¦ã¼ã¶ã¼åã®æå¤§æå­æ°] ã¯ 2 ï½ 80 ã®éã®æ°å¤ã«ãã¦ãã ããã
error.message.admin.securitySettings.minimumPasswordLength.outofrange=[ãã¹ã¯ã¼ãã®æå°æå­æ°] ã¯ 6 ï½ 32 ã®éã®æ°å¤ã«ãã¦ãã ããã
error.message.admin.securitySettings.minimumUserIdLength.outofrange=[ã¦ã¼ã¶ã¼åã®æå°æå­æ°] ã¯ 2 ï½ 80 ã®éã®æ°å¤ã«ãã¦ãã ããã
error.message.admin.securitySettings.pwExpireDays.mustgreaterthanone=[ãã¹ã¯ã¼ãã®æå¹æé] ã¯ 1 ããå¤§ãããã¦ãã ããã
error.message.admin.securitySettings.pwHistoryEntries.mustgreaterthanzero=[ãã¹ã¯ã¼ãå±¥æ­´ã¨ã³ããª] ã¯ 0 ããå¤§ãããã¦ãã ããã
error.message.admin.securitySettings.pwLimitMonths.mustbebetween=[ãã¹ã¯ã¼ãåå©ç¨æé (ææ°)] ã¯ 1 ï½ 24 ã®éã®æ°å¤ã«ãã¦ãã ããã
error.message.admin.securitySettings.pwResetKeepAlive.cannotbezero=[ãã¹ã¯ã¼ã ãªã»ãã ã­ã¼ãã¢ã©ã¤ã (å)] ã 0 ã«ãããã¨ã¯ã§ãã¾ããã
error.message.admin.securitySettings.sessionTimeout.outofrange=[ã»ãã·ã§ã³ ã¿ã¤ã ã¢ã¦ã] ã¯ 10 ï½ 120 ã®éã®æ°å¤ã«ãã¦ãã ããã
error.message.aggregated.variable.cannot.be.used.in.key={0} ã¯éç´ããã¦ããããã­ã¼ã¨ãã¦ä½¿ç¨ã§ãã¾ããã
error.message.all.multipart.content.is.empty=ä¿å­ããåã«å°ãªãã¨ã 1 ã¤ã®ã¡ãã»ã¼ã¸ ãã¼ãã«å¯¾ãã¦ã³ã³ãã³ããæå®ããå¿è¦ãããã¾ã
error.message.all.multipart.including.parent.content.is.empty=ä¿å­ããåã«å°ãªãã¨ã 1 ã¤ã®ã¡ãã»ã¼ã¸ ãã¼ãã¾ãã¯ [è¦ªããç¶æ¿ãã¾ãã?] ããªã³ã®å ´åã¯å°ãªãã¨ã 1 ã¤ã®è¦ªã³ã³ãã³ãã«å¯¾ãã¦ã³ã³ãã³ããæå®ããå¿è¦ãããã¾ã
error.message.approvaldatemustbeinfuture=å°æ¥ã®æ¥ä»ãé¸æãã¦ãã ãã
error.message.attribute.charinvalid=å±æ§åã¯å¿é ã§ã[a~z] ã¾ãã¯ [A~Z] ã®æå­ã§å§ããå¿è¦ãããã¾ãã
error.message.cannot.delete.referenced.constant={0} ã¯ã³ã³ãã³ãã«ãã£ã¦åç§ããã¦ããããåé¤ã§ãã¾ãã
error.message.cannot.delete.referenced.constants=æ¬¡ã®å®æ°ã¯åç§ããã¦ããããåé¤ã§ãã¾ãã: {0}
error.message.cannot.delete.referenced.paragraphstyles=æ¬¡ã®æ®µè½ã¹ã¿ã¤ã«ã¯åç§ããã¦ããããåé¤ã§ãã¾ãã: {0}
error.message.cannot.delete.referenced.targetgroups=æ¬¡ã®ã¿ã¼ã²ãã ã°ã«ã¼ãã¯åç§ããã¦ããããåé¤ã§ãã¾ãã: {0}
error.message.cannot.delete.referenced.targetingrules=æ¬¡ã®ã¿ã¼ã²ããè¨­å®ã«ã¼ã«ã¯åç§ããã¦ããããåé¤ã§ãã¾ãã: {0}
error.message.cannot.delete.referenced.variables=æ¬¡ã®å¤æ°ã¯åç§ããã¦ããããåé¤ã§ãã¾ãã: {0}
error.message.cannotchange.tpcs.delivery=ã¿ãããã¤ã³ã ã³ã³ãã³ãã®é¸æå¯è½ã¡ãã»ã¼ã¸ã®éä¿¡åã¾ã¼ã³ãå¤æ´ãããã¨ã¯ã§ãã¾ããã
error.message.cannotviewyourdefaultpage=ããã©ã«ã ã¯ã¼ã¯ã¹ãã¼ã¹ããªã»ããããå¿è¦ãããã¾ãããªã¹ãããã¢ã¤ãã ãé¸æãã[ä¿å­] ãã¯ãªãã¯ãã¦ãã ããã
error.message.categoryexists=ãã®ååã®ã«ãã´ãªãæ¢ã«å­å¨ãã¾ããå¥ã®ååãå¥åãã¦ãã ããã
error.message.codemustbeunique=å¥åãããããã³ã ã³ã¼ãã¯ä½¿ç¨ã§ãã¾ãããæ°ããããã³ã ã³ã¼ããå¥åãã¦ãã ããã
error.message.combination.is.used.in.other.node=æå®ããé¸æåºæº (\"{0}\") ã¯ããªã¢ã³ã \"{1}\" ã®æ¢å­ã®åºæºã¨éè¤ãããããè¿½å ã§ãã¾ãã
error.message.combition.is.duplicated.in.the.entered.value=æå®ããé¸æåºæº (\"{0}\") ã¯ãã®ããªã¢ã³ãã®æ¢å­ã®åºæºã¨éè¤ãããããè¿½å ã§ãã¾ãã
error.message.commentlengthexceeded=ã³ã¡ã³ãã®é·ãã 255 æå­ãè¶ãã¦ãã¾ã
error.message.condition.elements..not.data.model.compatible=é¸æããã«ã¼ã«ããã¼ã¿ ã¢ãã«ã«æºæ ãã¦ãã¾ãã
error.message.content.library.imagefile.not.compatible.with.active=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãã¢ã¯ãã£ã ã³ãã¼ã®å½¢å¼ã¨ä¸è´ãã¦ãã¾ãããåãå½¢å¼ãã¢ããã­ã¼ããã¦ãã ããã
error.message.content.library.imagefile.not.compatible=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¦ãã¾ãããåè¨èªã«åãå½¢å¼ãã¢ããã­ã¼ããã¦ãã ããã
error.message.content.library.model.not.matching=ã¤ã³ãã¼ãä¸­ã®ã¢ãã«ã XML ãã¡ã¤ã«ã¨ä¸è´ãã¦ãã¾ããã
error.message.content.spellchecker.invalid.config.file= dictionaryConfig.properties ãã¡ã¤ã«ãç¡å¹ã§ãã
error.message.content.spellchecker.no.config.file= dictionaryConfig.properties ãã¡ã¤ã«ãã­ã¼ãã§ãã¾ããã
error.message.content.spellchecker.no.file=ãã©ã«ãã¼ {0} åã«ãã¡ã¤ã«ãå­å¨ãã¾ãã
error.message.content.spellchecker.no.valid.dictionary=ãã®è¨èª {0} ã«å¯¾ãã¦æå¹ãªãã£ã¯ã·ã§ããªå½¢å¼ãã¡ã¤ã«ãæå®ããã¦ãã¾ãã
error.message.content.spellchecker.no.valid.folder={0} ãã£ã¯ã·ã§ããªãå­å¨ãã¾ãã
error.message.contenttypeinvalid=ã³ã³ãã³ã ã¿ã¤ããç¡å¹ã§ã
error.message.customernumberformat={0} è¨èªã®é¡§å®¢çªå·ã¨ãã¦æå¹ãªçªå·ãå¥åãã¦ãã ããã
error.message.customernumberrequired={0} è¨èªã®é¡§å®¢çªå·ãå¥åãã¦ãã ããã
error.message.dataresourcerequired=ãã¼ã¿ ãªã½ã¼ã¹ãé¸æãã¦ãã ãã
error.message.datasourceassociationuniquename=ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³åãæ¢ã«ä½¿ç¨ããã¦ãã¾ããä¸æã®ååãå¥åãã¦ãã ããã
error.message.datavalues.must.be.consecutive=ãã¼ã¿å¤ã¯é£ç¶ãã¦ããå¿è¦ãããã¾ã
error.message.dateoutofrange=æ¥ä»ãç¯å²å¤ã§ããæ¥ä»ã¯ 1753 å¹´ 1 æ 1 æ¥ ï½ 9999 å¹´ 12 æ 31 æ¥ã®éã§æå®ãã¦ãã ããã
error.message.deletevariable.exception=VariableDeleteValidator ã§äºæããªãä¾å¤ãçºçãã¾ããã
error.message.deletevariable.noparamenter=DeleteComand ã«ã¯ãã©ã¡ã¼ã¿ã¼ãå¿è¦ã§ãã
error.message.deletevariable.usedbymessagecontent=ãã®ã¢ã¤ãã ãåé¤ã§ãã¾ãããã¡ãã»ã¼ã¸ ã³ã³ãã³ãã§ä½¿ç¨ããã¦ãã¾ã
error.message.deletevariable.usedbyparameters= å¤æ°ãåé¤ã§ãã¾ããã1 ã¤ã¾ãã¯è¤æ°ã®ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ã§ä½¿ç¨ããã¦ãã¾ãã
error.message.deletevariable.usedbyrules= å¤æ°ãåé¤ã§ãã¾ãããã«ã¼ã«ã§ä½¿ç¨ããã¦ãã¾ãã
error.message.duplicateAppliedImageName=é©ç¨ãããç»åå {0} ãéè¤ãã¦ãã¾ããç°ãªãè¨èªã«åãé©ç¨ãããç»ååãå¥åãããã¨ã¯ã§ãã¾ãããéè¤ããå¤ãå¤æ´ããããä¸åº¦ããç´ãã¦ãã ããã
error.message.email.zone.parts.cannot.overlap=é»å­ã¡ã¼ã« ã¿ãããã¤ã³ãã§ã¯ã¾ã¼ã³ ãã¼ããéã­ããã¨ã¯ã§ãã¾ãã
error.message.emptyindicator=ããããã®ã¤ã³ã¸ã±ã¼ã¿ã¼ã®å¤ãæå®ããããä¸è¦ãªã¤ã³ã¸ã±ã¼ã¿ã¼ãåé¤ãã¦ãã ããã
error.message.emptyindicatorlength=ããããã®é·ãã®å¤ãæå®ããããä¸è¦ãªã¤ã³ã¸ã±ã¼ã¿ã¼ãåé¤ãã¦ãã ããã
error.message.end.date.before.start.date=çµäºæ¥ãéå§æ¥ãããåã«ãããã¨ã¯ã§ãã¾ããã
error.message.enddatebeforestart=éå§æ¥ããå¾ã®æå¹ãªçµäºæ¥ãå¥åãã¦ãã ãã
error.message.exceed.maximum.number.of.creating.restricted.users=ã©ã³ã»ã³ã¹ã«åºã¥ãå¶éä»ãã¢ã¯ã»ã¹ ã¦ã¼ã¶ã¼ã®æå¤§æ°ãè¶ãã¾ããã
error.message.exceed.maximum.number.of.limited.access.users=ãã¡ã¤ã³åã®ã©ã¤ã»ã³ã¹ã«åºã¥ãå¶éä»ãã¢ã¯ã»ã¹ ã¦ã¼ã¶ã¼ã®æå¤§æ°ãè¶ãã¾ãã: {0}ã
error.message.exceed.maximum.number.of.regular.users=ãã¡ã¤ã³åã®ã©ã¤ã»ã³ã¹ã«åºã¥ããã«ã¢ã¯ã»ã¹ ã¦ã¼ã¶ã¼ã®æå¤§æ°ãè¶ãã¾ãã: {0}ã
error.message.externalidmustbeunique=å¥åãã ID ã¯ç¾å¨ä½¿ç¨ããã¦ãã¾ããå¥ã®å¤ã® ID ãå¥åãã¦ãã ããã
error.message.global.message.creation.not.permitted.for.limited.visibility=ãã¹ã¦ã®ããªã¢ã³ãã«å¯¾ããå®å¨ãªè¡¨ç¤ºæ¨©éãä»ä¸ããã¦ããªãã¦ã¼ã¶ã¼ã¯ã°ã­ã¼ãã« ã¡ãã»ã¼ã¸ãä½æã§ãã¾ããã
error.message.global.messages.delivered.to.message.selectable.touchpoints.must.be.mandatory=ãã¬ã­ã·ãã« ã¿ãããã¤ã³ãã«éä¿¡ãããã°ã­ã¼ãã« ã¡ãã»ã¼ã¸ã¯å¿é ã§ãªããã°ãªãã¾ããã
error.message.graphicdeliverytype.not.match=é¸æããéä¿¡ã®ã³ã³ãã³ã ã¿ã¤ããæ¢å­ã®ã¡ãã»ã¼ã¸ ã³ã³ãã³ãã®ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ã¨ä¸è´ãã¾ãããå¥ã®éä¿¡ã¾ã¼ã³ãé¸æãã¦ãã ããã
error.message.graphicfilesize.too.large=é¸æããã°ã©ãã£ãã¯ {0} ããã®éä¿¡ã§è¨±å¯ãããæå¤§ãã¡ã¤ã« ãµã¤ãºãè¶ãã¦ãã¾ã ({1}kb)ã
error.message.graphicfiletype.not.match=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãé¸æããéä¿¡ã®ã³ã³ãã³ã ã¿ã¤ãã¨ä¸è´ãã¦ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ããã
error.message.id.notfound=ç³ãè¨³ãããã¾ãããæå®ãããã¡ãã»ã¼ã¸ ID ãè¦ã¤ããã¾ããã§ããã
error.message.imagefile.not.compatible.af=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã¢ããªã«ã¼ã³ã¹èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.al=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã¢ã«ããã¢èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.br=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ãã«ãã³èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.ca=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã«ã¿ã«ãã¢èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.da=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ãã³ãã¼ã¯èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.de=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ãã¤ãèªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.en=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: è±èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.es=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã¹ãã¤ã³èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.et=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã¨ã¹ããã¢èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.eu=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ãã¹ã¯èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.fi=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ãã£ã³ã©ã³ãèªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.fo=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ãã§ã­ã¼èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.fr=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ãã©ã³ã¹èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.ga=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã¢ã¤ã«ã©ã³ãèªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.gd=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã¹ã³ããã©ã³ã ã²ã¼ã«èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.gl=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã¬ãªã·ã¢èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.is=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã¢ã¤ã¹ã©ã³ãèªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.it=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã¤ã¿ãªã¢èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.lb=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã«ã¯ã»ã³ãã«ã¯èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.nl=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ãªã©ã³ãèªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.no=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ãã«ã¦ã§ã¼èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.pt=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ãã«ãã¬ã«èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.sv=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã¹ã¦ã§ã¼ãã³èªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.sw=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã¹ã¯ããªèªã®ã°ã©ãã£ãã¯ã
error.message.imagefile.not.compatible.wa=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¾ãããä»ã®ãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ãã: ã¯ã­ã³èªã®ã°ã©ãã£ãã¯ã
error.message.indentation.range=ã¤ã³ãã³ãã«ã¯ 0.001 ï½ 10 ã®æ°å¤ãå¥åãã¦ãã ãã
error.message.indicatorlength.is.zero=ã¤ã³ã¸ã±ã¼ã¿ã¼ã«é·ã 0 ãæå®ãããã¨ã¯ã§ãã¾ããã
error.message.invalid.content.library.reference={0} ã«å¯¾ãã¦ç»åã©ã¤ãã©ãª ã¢ã¤ãã ãé¸æããã¦ãã¾ãã
error.message.invalid.externalid=å¤é¨ ID ã«ã¯æ°å¤ãå¥åãã¦ãã ããã
error.message.invalid.text.content=ãã­ã¹ã ã³ã³ãã³ãã«æ¬¡ã®æå­ãå«ãããã¨ã¯ã§ãã¾ãã: %%ã|
error.message.languageexists=è¨èªã¯ãã®ã¿ãããã¤ã³ãåã«æ¢ã«å­å¨ãã¾ãã
error.message.left.margin.range=å·¦ä½ç½ã«ã¯ 0.001 ï½ 10 ã®æ°å¤ãå¥åãã¦ãã ãã
error.message.licence.cannot.be.larger.than={0} ã {1} ããå¤§ãããããã¨ã¯ã§ãã¾ãã
error.message.licence.cannot.be.smaller.than.one={0} ã 1 ããå°ãããããã¨ã¯ã§ãã¾ããã
error.message.licence.same.name.with.different.email=é»å­ã¡ã¼ã«: {0} ã§åãååã®ä¼ç¤¾ãæ¢ã«ä½¿ç¨ããã¦ãã¾ãã é¢é£ä»ããããé»å­ã¡ã¼ã«ã¨ä¸è´ãããããå¥ã®ä¼ç¤¾åãæå®ãã¦ãã ããã
error.message.linked.to.message.deleted=é¸æããã¡ãã»ã¼ã¸ \"{0}\" ãå­å¨ãã¾ããã
error.message.location.value.greater.than.zero=0 ããå¤§ããå ´æã®å¤ãå¥åãã¦ãã ããã
error.message.mandatory.attachments.cannot.have.targeting=å¿é éä¿¡ã®æ·»ä»ãã¡ã¤ã«ã«ã¿ã¼ã²ããè¨­å®ãå«ãããã¨ã¯ã§ãã¾ãããå¿é éä¿¡ã«åãæ¿ããåã«æ·»ä»ãã¡ã¤ã«ããã¿ã¼ã²ããè¨­å®ãåé¤ãã¦ãã ããã
error.message.master.message.require.approval=ãµãããªã¢ã³ããã¢ã¯ãã£ãåããã«ã¯ãäºåã«åç§åã®ãã¹ã¿ã¼ ã¡ãã»ã¼ã¸ãã¢ã¯ãã£ãåããå¿è¦ãããã¾ã - æ¬¡ã®ã¡ãã»ã¼ã¸ãã¢ã¯ãã£ãåãã¦ãã ãã: {0}
error.message.message.not.matching=ã¡ãã»ã¼ã¸ãä¸è´ãã¦ãã¾ãã
error.message.message.zone.and.target.groups.not.data.model.compatible=ã¡ãã»ã¼ã¸ ã¾ã¼ã³ã¨é¸æããã¿ã¼ã²ãã ã°ã«ã¼ãããã¼ã¿ ã¢ãã«ã«æºæ ãã¦ãã¾ãã
error.message.message.zone.has.empty.datagroup=ã¡ãã»ã¼ã¸ ã¾ã¼ã³ã® 1 ã¤ã«ç©ºã® DataGroup ãå­å¨ãã¾ããã¿ãããã¤ã³ã ã»ããã¢ããã§åé¡ãè§£æ±ºãã¦ãã ããã
error.message.missing.zone.part.sequence.values=ã¾ã¼ã³ ãã¼ãã®é åºãä¸é©åã§ããæ¬¡ã®é åºå¤ãå­å¨ãã¾ãã: {0}
error.message.multipledelivery.graphic.not.compatible=ã¡ãã»ã¼ã¸ã«ã°ã©ãã£ãã¯ ã¿ã¤ãã®ç°ãªãè¤æ°ã®ã¾ã¼ã³ãå«ãããã¨ã¯ã§ãã¾ããã
error.message.multipledelivery.selectable=ã¡ãã»ã¼ã¸ã«è¤æ°ã®éä¿¡ãå­å¨ãããããã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ã¾ãã¯ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ ã°ã«ã¼ãã«é¢é£ä»ãããã¨ãã§ãã¾ãã
error.message.must.enter.data=ããªã¢ã³ãã«å¯¾ãã¦è¿½å ã¾ãã¯åé¤ãããã¼ã¿ãæå®ãã¦ãã ãããããªã¢ã³ãã«è¿½å ããå ´åã{0} ã®å¤ã¯å¿é ã§ãã
error.message.must.select.at.least.one.touchpoint=å°ãªãã¨ã 1 ã¤ã®ã¿ãããã¤ã³ããé¸æãã¦ãã ããã
error.message.must.select.at.least.two.variables.for.compound.key=è¤åã­ã¼ãã¨ã«å°ãªãã¨ã 2 ã¤ã®å¤æ°ãé¸æãã¦ãã ããã
error.message.must.select.composition.package=ã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸ãé¸æãã¦ãã ãã
error.message.must.select.condition.for.rule=ã«ã¼ã«ã®æ¡ä»¶ãé¸æããå¿è¦ãããã¾ã: {0}
error.message.must.select.data.resource=ãã¼ã¿ ãªã½ã¼ã¹ãé¸æãã¦ãã ãã
error.message.must.select.shared.collection=å±æã³ã¬ã¯ã·ã§ã³ãé¸æããå¿è¦ãããã¾ãã
error.message.mustentercontactprovincestate=é£çµ¡åã®é½éåºçãå¥åãã¦ãã ããã
error.message.mustentercontent.for.language={0} è¨èªã®æå¹ãªã³ã³ãã³ããå¥åãã¦ãã ããã
error.message.mustentercontent=ããã©ã«ãè¨èªã®ã³ã³ãã³ããç¾å¨ç©ºç½ã«ãªã£ã¦ãã¾ããç¶è¡ããã«ã¯ã¡ãã»ã¼ã¸ ã³ã³ãã³ããå¥åãã¦ãã ããã
error.message.mustselectMessages=å°ãªãã¨ã 1 ã¤ã®ã¡ãã»ã¼ã¸ãé¸æããå¿è¦ãããã¾ãã
error.message.mustselectallprimaryvariables=ãã¹ã¦ã®åç§ãã¼ã¿ã®é¢é£ä»ãã«å¯¾ãã¦ãã©ã¤ããª ãã¼ã¿å¤æ°ãé¸æãã¦ãã ããã
error.message.mustselectallreferencedatasources=ãã¹ã¦ã®åç§ãã¼ã¿ã®é¢é£ä»ãã«å¯¾ãã¦åç§ãã¼ã¿ ã½ã¼ã¹ãé¸æãã¦ãã ããã
error.message.mustselectallreferencevariables=ãã¹ã¦ã®åç§ãã¼ã¿ã®é¢é£ä»ãã«å¯¾ãã¦åç§ãã¼ã¿å¤æ°ãé¸æãã¦ãã ããã
error.message.mustselectcontactcountry=é£çµ¡åã®å½ãå¥åãã¦ãã ããã
error.message.mustselectdailyfrequencytype=æ¯æ¥ã®é »åº¦ã¿ã¤ããé¸æãã¦ãã ããã
error.message.mustselectdatasourceassociation=ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³ãé¸æãã¦ãã ããã
error.message.mustselectdocument=å°ãªãã¨ã 1 ã¤ã®ãã­ã¥ã¡ã³ãã¨ã¡ãã»ã¼ã¸ãé¸æãã¦ãã ããã
error.message.mustselectenddate=çµäºæ¥ãå¥åãã¦ãã ãã
error.message.mustselectfile=ãã¡ã¤ã«ãé¸æãã¦ãã ãã
error.message.mustselectmessage=ã¡ãã»ã¼ã¸ãé¸æãã¦ãã ãã
error.message.mustselectprimarydatafile=ãã©ã¤ããª ãã¼ã¿ ãã¡ã¤ã«ãé¸æãã¦ãã ããã
error.message.mustselectprimarydatasource=ãã©ã¤ããª ãã¼ã¿ ã½ã¼ã¹ãé¸æãã¦ãã ããã
error.message.mustselectproductionruntype=ãã­ãã¯ã·ã§ã³å®è¡ã¿ã¤ããé¸æãã¦ãã ããã
error.message.mustselectreferencedatafile=åç§ãã¼ã¿ ã½ã¼ã¹ãã¨ã« 1 ã¤ã®ãã¼ã¿ ãã¡ã¤ã«ãé¸æãã¦ãã ããã
error.message.mustselectrepeatingfrequency=ç¹°ãè¿ãé »åº¦ãé¸æãã¦ãã ããã
error.message.mustselectrule=ãã®ã¿ã¼ã²ãã ã°ã«ã¼ãã«å¯¾ãã¦å°ãªãã¨ã 1 ã¤ã®ã«ã¼ã«ãé¸æãã¦ãã ãã
error.message.mustselectstartdate=éå§æ¥ãå¥åãã¦ãã ãã
error.message.mustselectstartdateifenddate=çµäºæ¥æå ±ãæå®ããå ´åã¯éå§æ¥ãå¥åãã¦ãã ãã
error.message.mustselecttestrundate=ãã¹ãå®è¡æ¥ãå¥åãã¦ãã ãã
error.message.mustselecttouchpoint=ã¿ãããã¤ã³ããé¸æãã¦ãã ãã
error.message.mustselecttouchpointorcollection=ã¿ãããã¤ã³ãã¾ãã¯ã¿ãããã¤ã³ã ã³ã¬ã¯ã·ã§ã³ãé¸æãã¦ãã ãã
error.message.mustselectuser=ã¦ã¼ã¶ã¼ãé¸æãã¦ãã ãã
error.message.mustselectzone=éä¿¡ã®ã¿ã¼ã²ãã ã¾ã¼ã³ãé¸æãã¦ãã ããã
error.message.name.is.used.by.another.node=ååãå¥ã®ãã¼ãã§ä½¿ç¨ããã¦ãã¾ã
error.message.name.is.used.by.another.shared.node=ååãå¥ã®å±æãã¼ãã§ä½¿ç¨ããã¦ãã¾ã
error.message.namemustbeunique=ååãæ¢ã«ä½¿ç¨ããã¦ãã¾ããä¸æã®ååãå¥åãã¦ãã ããã
error.message.namerequired=ååãå¥åãã¦ãã ãã
error.message.no.multidelivery.on.sms.zone=SMS ã¾ã¼ã³ã«éä¿¡ãããã¡ãã»ã¼ã¸ã¯è¤æ°éä¿¡ã®å¯¾è±¡å¤ã§ã
error.message.no.multidelivery.on.subject.line.zone=ãä»¶åè¡ãã¾ã¼ã³ã«éä¿¡ãããã¡ãã»ã¼ã¸ã¯è¤æ°éä¿¡ã®å¯¾è±¡å¤ã§ã
error.message.no.rules.in.system=ã·ã¹ãã åã«ä½¿ç¨ã§ããã«ã¼ã«ãããã¾ãããã¿ã¼ã²ãã ã°ã«ã¼ããä½æããåã«ã«ã¼ã«ãä½æãã¦ãã ããã
error.message.no.touchpoints.selected=1 ã¤ä»¥ä¸ã®ã¿ãããã¤ã³ããé¸æãã¦ãã ãã
error.message.node.domain.is.not.in.valid.state=ãã¡ã¤ã³ã®ããã©ã«ã ã¤ã³ã¹ã¿ã³ã¹ãç¡å¹ãªç¶æã§ããæåã«ããã©ã«ã ã¤ã³ã¹ã¿ã³ã¹ãåæåãã¦ãã ããã
error.message.non.unique.record.position=ä½ç½®ã®å¤ããã®ãã¼ã¿ ã½ã¼ã¹ã®å¥ã®ã¬ã³ã¼ãã§æ¢ã«å®ç¾©ããã¦ãã¾ããä¸æã®å¤ãå¥åãã¦ãã ããã
error.message.not.allowed.to.chain.message=ãã®ã¡ãã»ã¼ã¸ã«ã¯ãªã³ã¯ãããå­ã¡ãã»ã¼ã¸ãå«ã¾ãã¦ãã¾ããã¡ãã»ã¼ã¸ ãã§ã¼ã³ã¯è¨±å¯ããã¾ããã
error.message.not.exists=ãã®ã¡ãã»ã¼ã¸ã¯å­å¨ãã¾ããã
error.message.order.entry.data.element.required=ãã¼ã¿è¦ç´ ã¯ãªã¼ãã¼ ã¨ã³ããªæ§æãã¨ã«é¸æããå¿è¦ãããã¾ãã
error.message.order.entry.menu.items.required=åã¡ãã¥ã¼ ãã£ã¼ã«ã ã¿ã¤ãã«å¯¾ãã¦å°ãªãã¨ã 1 ã¤ã®ã¢ã¤ãã ãæ§æããå¿è¦ãããã¾ãã
error.message.order.entry.web.service.attributes.required=Web ãµã¼ãã¹é§ååã®ã¡ãã¥ã¼æ§æã«ã¯ãURLãã¦ã¼ã¶ã¼åãããã³ãã¹ã¯ã¼ããå¿è¦ã§ãã
error.message.overlapping.freeform.containers={0} ã³ã³ãã³ãã«éãªãåãã­ã£ã³ãã¹ ã³ã³ããã¼ãå«ã¾ãã¦ãã¾ãã
error.message.packaged.touchpoint.must.belong.to.same.family=ããã±ã¼ã¸ãããã¿ãããã¤ã³ãã¯åããã¡ããª (ããã±ã¼ã¸å®ç¾©ã®ãµãã»ãã) ã«å±ãã¦ããå¿è¦ãããã¾ã: {0}
error.message.parameter.group.at.least.two.parameters=å°ãªãã¨ã 2 ã¤ã®ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ãé¸æããå¿è¦ãããã¾ã
error.message.parameter.group.name.already.in.use=ååãæ¢ã«ä½¿ç¨ããã¦ãã¾ããå¥ã®ååãå¥åãã¦ãã ããã
error.message.parameter.group.required=åçã³ã³ãã³ããä½æããã«ã¯ã»ã¬ã¯ã¿ã¼ãå¿è¦ã§ã
error.message.parameter.zones.not.data.model.compatible=é¸æããã¾ã¼ã³ã§é¸æããã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ããã¼ã¿ ã¢ãã«ã«æºæ ãã¦ãã¾ããã
error.message.parameters.not.data.model.compatible=é¸æããã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ããã¼ã¿ ã¢ãã«ã«æºæ ãã¦ãã¾ãã
error.message.password.expired=ãã¹ã¯ã¼ãã®æå¹æéãåãã¦ãã¾ããç¶è¡ããã«ã¯ãã¹ã¯ã¼ããå¤æ´ãã¦ãã ããã
error.message.position.value.between.one.and.ninety.nine=1 ï½ 99 ã®ä½ç½®ã®å¤ãå¥åãã¦ãã ãã
error.message.priority.items.have.changed.since.page.load=1 ã¤ã¾ãã¯è¤æ°ã®ã¡ãã»ã¼ã¸ã®ç¶æã¾ãã¯ã¹ãã¼ã¿ã¹ãå¤æ´ããã¦ãã¾ããå¤æ´ãããã¡ãã»ã¼ã¸ ãªã¹ãã«åºãã¦åªååº¦ãç¢ºèªããè¨­å®ãã¦ãã ããã
error.message.recipient.attachment.location.is.mandatory=åä¿¡èã®æ·»ä»ãã¡ã¤ã«ã®å ´æãç©ºç½ã®ã¾ã¾ã«ãããã¨ã¯ã§ãã¾ãã
error.message.recipient.attachment.name.is.mandatory=åä¿¡èã®æ·»ä»ãã¡ã¤ã«ã®ååãç©ºç½ã®ã¾ã¾ã«ãããã¨ã¯ã§ãã¾ãã
error.message.recipient.location.contains.restricted.characters=åä¿¡èã®æ·»ä»ãã¡ã¤ã«ã®å ´æã«æ¬¡ã®æå­ãä½¿ç¨ãããã¨ã¯ã§ãã¾ãã: {0}
error.message.recipient.name.contains.restricted.characters=åä¿¡èã®æ·»ä»ãã¡ã¤ã«ã®ååã«æ¬¡ã®æå­ãä½¿ç¨ãããã¨ã¯ã§ãã¾ãã: {0}
error.message.reference.data.source.must.be.unique=ãã®ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³ã§ã¯ååç§ãã¼ã¿ ã½ã¼ã¹ã 1 åº¦ã ãé¸æããããã«ãã¦ãã ããã
error.message.relase.for.approval.parent.no.active=ãªã³ã¯ãããè¦ªã¡ãã»ã¼ã¸ãã¢ã¯ãã£ãã§ã¯ããã¾ããããªãªã¼ã¹ãã¦æ¿èªãåããåã«è¦ªã¡ãã»ã¼ã¸ãã¢ã¯ãã£ãã«ãã¦ãã ããã
error.message.right.margin.range=å³ä½ç½ã«ã¯ 0.001 ï½ 10 ã®æ°å¤ãå¥åãã¦ãã ãã
error.message.roleisinuse=å½¹å²ã¯ç¾å¨ä½¿ç¨ããã¦ããããåé¤ã§ãã¾ããã
error.message.ruleconditiontype.cannot.changed.when.referenced=åç§ãããã«ã¼ã«ã®æ¡ä»¶é¢ä¿æ§é ãå¤æ´ãããã¨ã¯ã§ãã¾ããã
error.message.rulemusthavecondition=ã«ã¼ã«ã«ã¯å°ãªãã¨ã 1 ã¤ã®æ¡ä»¶ãå¿è¦ã§ãã
error.message.select.at.least.one.data.element=å°ãªãã¨ã 1 ã¤ã®ãã¼ã¿è¦ç´ ãé¸æãã¦ãã ããã
error.message.select.data.element=ãã¼ã¿è¦ç´ ãé¸æãã¦ãã ããã
error.message.selectable.multipledelivery=ãã®ã¡ãã»ã¼ã¸ ã¿ã¤ãã¯ 1 ã¤ã®ã¿ãããã¤ã³ãåã® 1 ã¤ã®ã¾ã¼ã³ã«ã®ã¿éä¿¡ã§ãã¾ã
error.message.selectaparent=è¦ªãé¸æãã¦ãã ããã
error.message.selectarecord=å°ãªãã¨ã 1 ã¤ã®ãã¼ã¿ ã¬ã³ã¼ããé¸æãã¦ãã ããã
error.message.selectasource=ãã¼ã¿ ã½ã¼ã¹ãé¸æãã¦ãã ããã
error.message.selection.data.cannot.be.removed.because.of.being.used.in.other.nodes=\"{0}\" ã¨ããååã®å¥ã®ããªã¢ã³ãåã«å­ãå­å¨ãããããé¸æãã¼ã¿ãåé¤ã§ãã¾ãã
error.message.selection.final.approval.out.of.sequence=é åºãå¤ãã¦ã¢ã¯ãã£ãåãè¦æ±ãããã¨ã¯ã§ãã¾ãã - ãã®ããªã¢ã³ãã¯ã¢ã¯ãã£ãåããã¦ããªãæ¬¡ã®è¦ªããã³ã³ãã³ããç¶æ¿ãã¾ã: {0}
error.message.shared.graphicfilesize.too.large=å±æã³ã³ãã³ã {0} ããã®éä¿¡ã§è¨±å¯ãããæå¤§ãã¡ã¤ã« ãµã¤ãºããå¤§ããªã°ã©ãã£ãã¯ ãã¡ã¤ã«ãåç§ãã¦ãã¾ã ({1}kb)ã
error.message.shared.value.combination.is.used.in.other.node=å±æã®é¸æåºæº (\"{0}\") ã¯ããªã¢ã³ã \"{1}\" ã®æ¢å­ã®åºæºã¨éè¤ãããããé©ç¨ã§ãã¾ãã
error.message.sharedcontent.imagefiledoesnotmatch=ã°ã©ãã£ãã¯ ãã¡ã¤ã«å½¢å¼ãä¸è´ãã¦ãã¾ããããã¹ã¦ã®è¨èªã«å¯¾ãã¦åãå½¢å¼ã®ç»åãã¡ã¤ã«ãã¢ããã­ã¼ããã¦ãã ããã
error.message.sharedcontent.imagefilemustbejpgtiforrtf=ã°ã©ãã£ãã¯å±æã³ã³ãã³ãã§ã¯ã.jpgã.tifã.pdfãããã³ .rtf ãã¡ã¤ã«ã®ã¿ããµãã¼ãããã¾ãã
error.message.sharedmodeltenants=å°ãªãã¨ã 1 ã¤ã®ããã³ããé¸æãã¦ãã ãã
error.message.spacing.after.range=å¾ã®ééã«ã¯ 0 ï½ 720 ã®æ°å¤ãå¥åãã¦ãã ããã
error.message.spacing.before.range=åã®ééã«ã¯ 0 ï½ 720 ã®æ°å¤ãå¥åãã¦ãã ããã
error.message.startdateandtimemustbeinfuture=å°æ¥ã®éå§æ¥æãé¸æãã¦ãã ããã
error.message.tag.cannot.be.activated.due.to.associations.archived=ãã¹ã¦ã®é¢é£ã¡ãã»ã¼ã¸ã¾ãã¯æ¿å¥ãåé¤ã¾ãã¯ã¢ã¼ã«ã¤ãããã¦ããããã{0} ãã¢ã¯ãã£ãã«ã§ãã¾ããã
error.message.tag.cannot.be.activated=ã¿ã¼ã²ããè¨­å®ã¾ãã¯é¢é£ä»ããæå®ããã¦ããªãããã{0} ãã¢ã¯ãã£ãã«ã§ãã¾ããã
error.message.tag.name.exists=ãã®ååã®ã¿ã°ãæ¢ã«å­å¨ãã¾ããå¥ã®ååãå¥åãã¦ãã ããã
error.message.tag.type.cannot.be.discarded=1 ã¤ã¾ãã¯è¤æ°ã®ã¿ã°ããã®ã¿ã° ã¿ã¤ããåç§ãã¦ããããã{0} ãç ´æ£ã§ãã¾ããã
error.message.tag.type.name.exists=ãã®ååã®ã¿ã° ã¿ã¤ããæ¢ã«å­å¨ãã¾ããå¥ã®ååãå¥åãã¦ãã ããã
error.message.target.group.not.match.touchpoint.data.model=é¸æããã¿ãããã¤ã³ãå²ãå½ã¦ã§ã¿ã¼ã²ãã ã°ã«ã¼ãããã¼ã¿ ã¢ãã«ã«æºæ ãã¦ãã¾ããã
error.message.target.groups.not.data.model.compatible=é¸æããã¿ã¼ã²ãã ã°ã«ã¼ãããã¼ã¿ ã¢ãã«ã«æºæ ãã¦ãã¾ãã
error.message.target.groups.not.match.touchpoint.data.model=é¸æããã¿ãããã¤ã³ãå²ãå½ã¦ã§ 1 ã¤ã¾ãã¯è¤æ°ã®ã¿ã¼ã²ãã ã°ã«ã¼ãããã¼ã¿ ã¢ãã«ã«æºæ ãã¦ãã¾ããã
error.message.target.zone.must.be.selected.for.message.move=ã¾ã¼ã³ãé¸æããå¿è¦ãããã¾ãã
error.message.template.variant.each.template.variant.must.have.a.template.packate.uploaded=ä»£æ¿ãã³ãã¬ã¼ããã¨ã« 1 ã¤ã®ãã³ãã¬ã¼ã ããã±ã¼ã¸ãã¢ããã­ã¼ãããã¦ããå¿è¦ãããã¾ã
error.message.text.style.name.must.be.unique=åãã­ã¹ã ã¹ã¿ã¤ã«ã«ä¸æã®ååãæå®ããå¿è¦ãããã¾ãã
error.message.text.style.name.required=ãã­ã¹ã ã¹ã¿ã¤ã«åãå¿è¦ã§ãã
error.message.there.is.at.least.one.empty.value.before.parameter=ãã©ã¡ã¼ã¿ã¼ {0} ã®åã«å°ãªãã¨ã 1 ã¤ã®ç©ºã®å¤ãå­å¨ãã¾ã
error.message.touchpoint.collection.connector.not.dialogue=ããã±ã¼ã¸ãããã³ã¬ã¯ã·ã§ã³ã§ã¯å°å·ãã£ãã«ã®ã¿ãããã¤ã³ãã®ã¿ãè¨±å¯ããã¾ãã
error.message.touchpoint.not.matching=ã¿ãããã¤ã³ããä¸è´ãã¦ãã¾ãã
error.message.touchpoints.composition.package.are.not.the.same=ãã¹ã¦ã®ã¿ãããã¤ã³ããåãã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸ãä½¿ç¨ããããnull ã§ããå¿è¦ãããã¾ãã
error.message.touchpoints.connector.are.not.the.same=ãã¹ã¦ã®ã¿ãããã¤ã³ããåããã£ãã«/ã³ãã¯ã¿ã¼ãä½¿ç¨ããããnull ã§ããå¿è¦ãããã¾ãã
error.message.touchpoints.dsa.are.not.the.same=ãã¹ã¦ã®ã¿ãããã¤ã³ããåããã¼ã¿ ã½ã¼ã¹ã®é¢é£ä»ããä½¿ç¨ããããnull ã§ããå¿è¦ãããã¾ãã
error.message.toucpointselectable.notouchpointchanges=ã¿ãããã¤ã³ãã®é¸æå¯è½ã¡ãã»ã¼ã¸ã§ã¿ãããã¤ã³ããå¤æ´ãããã¨ã¯ã§ãã¾ããã
error.message.tp.content.selector.zones.not.data.model.compatible=é¸æããã¾ã¼ã³ã§ã¿ãããã¤ã³ã ã»ã¬ã¯ã¿ã¼ããã¼ã¿ ã¢ãã«ã«æºæ ãã¦ãã¾ããã
error.message.usagetype.cannot.changed.when.referenced=ãã®åç§åã¢ã»ããã®ä½¿ç¨æ³ã¿ã¤ããå¤æ´ãããã¨ã¯ã§ãã¾ããã
error.message.variable.cannot.be.aggregated.because.it.is.used.in.key=ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³ã§ã­ã¼ã¨ãã¦ä½¿ç¨ããã¦ãããããå¤æ°ãéç´ãããã¨ã¯ã§ãã¾ããã
error.message.variable.defaultvalue.invalid=ããã©ã«ãå¤ãç¡å¹ã§ã: {0}
error.message.variableedit.inconsistentdataelements=é¸æãããã¼ã¿è¦ç´ ã®ãã¼ã¿ ã¿ã¤ããä¸è´ãã¦ãã¾ãã
error.message.variableedit.usedbymessagecontent=å¤æ°ã 1 ã¤ã¾ãã¯è¤æ°ã®ã¡ãã»ã¼ã¸åã«å«ã¾ãã¦ãããããã³ã³ãã³ãã«å¯¾ãã¦ç¡å¹ã«ã§ãã¾ããã
error.message.variableedit.usedbyparameters=å¤æ°ã 1 ã¤ã¾ãã¯è¤æ°ã®ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ã§ä½¿ç¨ããã¦ãããããã«ã¼ã«ã«å¯¾ãã¦ç¡å¹ã«ã§ãã¾ããã
error.message.variableedit.usedbyrules=å¤æ°ã 1 ã¤ã¾ãã¯è¤æ°ã®ã«ã¼ã«ã§ä½¿ç¨ããã¦ãããããã«ã¼ã«ã«å¯¾ãã¦ç¡å¹ã«ã§ãã¾ããã
error.message.variablefriendlynameexists=ãã®ãã¬ã³ããªåã®å¤æ°ãæ¢ã«å­å¨ãã¾ããå¥ã®ãã¬ã³ããªåãå¥åãã¦ãã ããã
error.message.variablenameexists=ãã®ååã®å¤æ°ãæ¢ã«å­å¨ãã¾ããå¥ã®ååãå¥åãã¦ãã ããã
error.message.variables..not.data.model.compatible=é¸æããå¤æ°ããã¼ã¿ ã¢ãã«ã«æºæ ãã¦ãã¾ãã
error.message.xmldatatag.charinvalid=ååã«ã¯ [a~z] ã¾ãã¯ [A~Z] ã®æå­ã®ã¿ãä½¿ç¨ã§ãã¾ãã
error.message.zone.content.type.cannot.be.changed=ãã®æç¹ã§ã³ã³ãã³ã ã¿ã¤ããå¤æ´ãããã¨ã¯ã§ãã¾ããããã¹ã¦ã®ã³ã³ãã³ã ã¢ã»ããã¨ãã®ã¾ã¼ã³ã¨ã®é¢é£ä»ããè§£é¤ããå¾ã«ãããä¸åº¦ããç´ãã¦ãã ããã
error.message.zone.data.group.linked.cannot.be.changed=ãã®ã¾ã¼ã³ã«éä¿¡ããããªã³ã¯ãããã¡ãã»ã¼ã¸ãå­å¨ãã¾ãããªã³ã¯ãããã¡ãã»ã¼ã¸ã¨ãã®ã¾ã¼ã³ã¨ã®é¢é£ä»ããè§£é¤ããå¾ã«ãããä¸åº¦ããç´ãã¦ãã ããã
error.message.zone.freeform.toggle.cannot.be.changed=æ¢å­ã®ãã¹ã¦ã®ã³ã³ãã³ããåé¤ãããã¾ã§ããã®ã¾ã¼ã³ã®ããªã¼ãã©ã¼ã  ã¹ãã¼ã¿ã¹ãå¤æ´ãããã¨ã¯ã§ãã¾ããã
error.message.zone.multipart.atleastxxparts=è¤æ°ãã¼ã ã¾ã¼ã³ã«ã¯å°ãªãã¨ã {0} ã®ãã¼ããå¿è¦ã§ãã
error.message.zone.multipart.contenttypeofausedzonepartnochange=ä½¿ç¨ããã¦ããã¾ã¼ã³ ãã¼ãã®ã³ã³ãã³ã ã¿ã¤ããå¤æ´ã§ãã¾ããã{0}
error.message.zone.multipart.messagesRefCannotBeDeleted=ãã®ã¾ã¼ã³ã«é¢é£ä»ãããã¦ããã¡ãã»ã¼ã¸ãå­å¨ãããããã¾ã¼ã³ ãã¼ããåé¤ã§ãã¾ããã
error.message.zones.not.data.model.compatible=é¸æããã¾ã¼ã³ããã¼ã¿ ã¢ãã«ã«æºæ ãã¦ãã¾ããã
error.messages.associated.with.multipart.zone=ãã®ã¾ã¼ã³ ãã¼ãã«é¢é£ä»ãããããã³ã³ãã³ã ã¿ã¤ãã®ç°ãªããè¤æ°ãã¼ãã®ã¡ãã»ã¼ã¸ãå­å¨ãã¾ãã<br />ã¾ã¼ã³ ãã¼ãã®ã³ã³ãã³ã ã¿ã¤ããå¤æ´ããã«ã¯ãæåã«æ¬¡ã®ã¡ãã»ã¼ã¸ãå¤æ´ããå¿è¦ãããã¾ãã
error.name.cannot.be.empty=ååãç©ºã«ãããã¨ã¯ã§ãã¾ããã
error.node.can.not.be.empty=ã¤ã³ã¹ã¿ã³ã¹ãç©ºã«ãããã¨ã¯ã§ãã¾ãã
error.node.is.not.valid=ã¤ã³ã¹ã¿ã³ã¹ãç¡å¹ã§ã
error.node.name.must.be.entered=ã¤ã³ã¹ã¿ã³ã¹åãå¥åããå¿è¦ãããã¾ã
error.node.name.must.be.unique=ã¤ã³ã¹ã¿ã³ã¹åã¯ä¸æã§ãªããã°ãªãã¾ãã
error.node.only.production.type.can.be.create.at.this.time=ã¤ã³ã¹ã¿ã³ã¹ ã¿ã¤ããç¡å¹ã§ããæåã«ãã­ãã¯ã·ã§ã³ ã¿ã¤ã ã¤ã³ã¹ã¿ã³ã¹ãä½æããå¿è¦ãããã¾ãã
error.node.schema.name.must.be.entered=ã¹ã­ã¼ãåãå¥åããå¿è¦ãããã¾ã
error.node.schema.name.must.be.unique=ã¹ã­ã¼ãåã¯ä¸æã§ãªããã°ãªãã¾ãã
error.number.of.sections.to.add.must.be.more.than.one=è¿½å ãããã»ã¯ã·ã§ã³æ°ã¯ã¼ã­ããå¤§ããæ°ã§ãªããã°ãªãã¾ãã
error.order.entry.items.must.have.unique.data.element.mapping=è¤æ°ã®ãã¼ã¿ ãã¤ã³ãã«å¯¾ãã¦ 1 ã¤ã®ãã¼ã¿è¦ç´ ããããã³ã°ãããã¨ã¯ã§ãã¾ããããã¼ã¿è¦ç´ /è£å©ãã¼ã¿è¦ç´ ã®é¸æåå®¹ãå¤æ´ãã¦ãã ããã
error.order.entry.primary.driver.must.be.selected=ãã©ã¤ããª ãªã¼ãã¼ ã¨ã³ããª ã¢ã¤ãã ãé¸æããå¿è¦ãããã¾ãã
error.please.enter.all.long.month.names=ãã¹ã¦ã®æã®ååãå¥åãã¦ãã ãã
error.please.enter.all.short.month.names=ãã¹ã¦ã®æã®ååãå¥åãã¦ãã ãã
error.pod.master.pwd.must.be.entered=ããã ã¹ã­ã¼ã ãã¹ã¯ã¼ããå¥åããå¿è¦ãããã¾ã
error.pod.master.schema.must.be.entered=ããã ãã¹ã¿ã¼ ã¹ã­ã¼ãåãå¥åããå¿è¦ãããã¾ã
error.pod.name.must.be.entered=ãããåãå¥åããå¿è¦ãããã¾ã
error.pod.name.must.be.unique=ãããåã¯ä¸æã§ãªããã°ãªãã¾ãã
error.pod.url.must.be.entered=ããã URL ãå¥åããå¿è¦ãããã¾ã
error.pod.url.must.be.unique=ããã URL ã¯ä¸æã§ãªããã°ãªãã¾ãã
error.rate.sheet.default.not.covering.whole.period=ãã®ã¹ã±ã¸ã¥ã¼ã«ã®ããã©ã«ãæéè¡¨ã¯ã¹ã±ã¸ã¥ã¼ã«ã®å¨æéãã«ãã¼ãã¦ãã¾ãã
error.rate.sheet.discontinue.in.use.by.insert.schedule.for.list={0} ã®ä½¿ç¨ãåæ­¢ããã¨ãæ¬¡ã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã«å½±é¿ãã¾ã: {1}
error.rate.sheet.discontinue.in.use.by.insert.schedule=ãã®æéè¡¨ã®ä½¿ç¨ãåæ­¢ããã¨ãæ¬¡ã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã«å½±é¿ãã¾ã: {0}
error.rate.sheet.discontinue.start.date.in.future.for.list=éå§æ¥ãä»æ¥ã¾ãã¯å°æ¥ã«ãªã£ã¦ããããã{0} ã®ä½¿ç¨ãåæ­¢ã§ãã¾ãã
error.rate.sheet.discontinue.start.date.in.future=éå§æ¥ãä»æ¥ã¾ãã¯å°æ¥ã«ãªã£ã¦ããããããã®æéè¡¨ã®ä½¿ç¨ãåæ­¢ã§ãã¾ãã
error.rate.sheet.envelope.weight.range=å°ç­ã®ééã«ã¯ 0.0001 ï½ 99.9999 ã®æå¹ãªå¤ãå¥åãã¦ãã ãã
error.rate.sheet.in.use.by.document.for.list={0} ã¯æ¬¡ã®ã¿ãããã¤ã³ãã§ä½¿ç¨ããã¦ãã¾ã: {1}
error.rate.sheet.in.use.by.document=ãã®æéè¡¨ã¯æ¬¡ã®ã¿ãããã¤ã³ãã§ä½¿ç¨ããã¦ãã¾ã: {0}
error.rate.sheet.in.use.by.insert.schedule.for.list={0} ã¯æ¬¡ã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã§ä½¿ç¨ããã¦ãã¾ã: {1}
error.rate.sheet.in.use.by.insert.schedule=ãã®æéè¡¨ã¯æ¬¡ã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã§ä½¿ç¨ããã¦ãã¾ã: {0}
error.rate.sheet.name.unique=ååãæ¢ã«ä½¿ç¨ããã¦ãã¾ããä¸æã®ååãå¥åãã¦ãã ããã
error.rate.sheet.rate.range=ãããå¤ã®ã³ã¹ãã«ã¯ 0.01 ï½ 99.99 ã®æå¹ãªå¤ãå¥åãã¦ãã ãã
error.rate.sheet.specify.all.consts=ãã¹ã¦ã®ãããå¤ã®ã³ã¹ãã«æå¹ãªå¤ãå¥åãã¦ãã ãã
error.rate.sheet.startdate.before.end.date=éå§æ¥ãçµäºæ¥ããå¾ã«ãããã¨ã¯ã§ãã¾ãã
error.rate.sheet.startdate.before.previous.schedule.end.date=éå§æ¥ã¯åã®æéè¡¨ã®çµäºæ¥ããå¾ã§ãªããã°ãªãã¾ãã
error.rate.sheet.startdate.before.previous.schedule.start.date=éå§æ¥ã¯åã®æéè¡¨ã®éå§æ¥ããå¾ã§ãªããã°ãªãã¾ãã
error.rate.sheet.weight.range.gram=ééã®ãããå¤ã«ã¯ 0.0001 ï½ 1999.9999 (å°æ°ç¹ä»¥ä¸ç¬¬ 4 ä½ã¾ã§) ã®æå¹ãªå¤ãå¥åãã¦ãã ãã
error.rate.sheet.weight.range.ounce=ééã®ãããå¤ã«ã¯ 0.0001 ï½ 99.9999 (å°æ°ç¹ä»¥ä¸ç¬¬ 4 ä½ã¾ã§) ã®æå¹ãªå¤ãå¥åãã¦ãã ãã
error.rate.weights.and.rates.order.mismatch=ééã®ãããå¤ã¨æéã®ãããå¤ã®é åºä»ããä¸è´ãã¦ãã¾ãã
error.rationalizer.shared.content.name.unique=
error.same.pod.cannot.be.set.to.both.production.and.transition.for.a.domain=åä¸ã®ãããããã¡ã¤ã³ã®ãã­ãã¯ã·ã§ã³ ãã¼ã¸ã§ã³ã¨ãã¬ãªãªã¼ã¹ ãã¼ã¸ã§ã³ã®ä¸¡æ¹ã«è¨­å®ãããã¨ã¯ã§ãã¾ããã
error.section.containing.zones.cannot.be.deleted=é¸æããã»ã¯ã·ã§ã³ã« 1 ã¤ã¾ãã¯è¤æ°ã®ã¾ã¼ã³ãå«ã¾ãã¦ãã¾ããã»ã¯ã·ã§ã³ãåé¤ãããã¨ã¯ã§ãã¾ããã
error.section.must.specify.name=åã¿ãããã¤ã³ã ã»ã¯ã·ã§ã³ã«ã¯ååãå¿è¦ã§ããæ¬¡ã®ã»ã¯ã·ã§ã³ã®ååãæå®ãã¦ãã ãã: {0}
error.section.name.must.be.unique=åã¿ãããã¤ã³ã ã»ã¯ã·ã§ã³ã«ä¸æã®ååãæå®ããå¿è¦ãããã¾ã
error.section.name.required=ã»ã¯ã·ã§ã³åãå¿è¦ã§ã
error.section.name.too.long=ã»ã¯ã·ã§ã³åã¯ 96 æå­ä»¥åã§ãªããã°ãªãã¾ãã
error.select.file.to.import=å¥åãã£ã¼ã«ããç©ºã§ããã¤ã³ãã¼ããããã¡ã¤ã«ãé¸æãã¦ãã ããã
error.selected.import.file.empty=é¸æãããã¡ã¤ã« '{0}' ãç©ºã§ããã¤ã³ãã¼ãããæå¹ãªãã¡ã¤ã«ãé¸æãã¦ãã ããã
error.selected.import.file.not.xml=é¸æãããã¡ã¤ã« '{0}' ãæå¹ãª XML ãã¡ã¤ã«ã§ã¯ããã¾ããã
error.simulation.enterrundate=å®è¡æ¥ãå¥åãã¦ãã ãã
error.simulation.selectdataResource=ãã¼ã¿ ãªã½ã¼ã¹ãé¸æãã¦ãã ãã
error.simulation.selectdocument=ã¿ãããã¤ã³ããé¸æãã¦ãã ãã
error.simulation.selectmessage=ãã­ãã¯ã·ã§ã³ç¶æã®ã¡ãã»ã¼ã¸ãå­å¨ãã¾ãããå°ãªãã¨ã 1 ã¤ã®ã¡ãã»ã¼ã¸ãé¸æãã¦ãã ããã
error.smart.text.variables.not.mapped=ãã®ã¹ãã¼ã ãã­ã¹ãã§ä½¿ç¨ããã¦ãã 1 ã¤ä»¥ä¸ã®å¤æ°ãæ­£ãããããã³ã°ããã¦ãã¾ããã å²ãå½ã¦ãããã¿ãããã¤ã³ãã®å¤æ°ã®ãããã³ã°ãè§£æ±ºãã¦ãã ããã
error.tenant.management.cannot.duplicate.sheet.counts=ä»»æã®ããã³ãã®ç°ãªã 2 ã¤ã®æéè¡¨ã«å¯¾ãã¦ã·ã¼ãæ°ã®ãããå¤ãåãã«ãããã¨ã¯ã§ãã¾ãã
error.tenant.management.no.name={0} ã®ããã³ã ã¹ã±ã¸ã¥ã¼ã«åãæå®ãã¦ãã ãã
error.tenant.management.no.tenant.bin.assignment=ãªãªã¼ã¹ããåã«å°ãªãã¨ã 1 ã¤ã®ãã³ãããã³ãã«å²ãå½ã¦ã¦ãã ããã
error.tenant.management.tenant.window.closed=ããã³ã ã¦ã£ã³ãã¦ãéãã¦ãã¾ãããªãªã¼ã¹ããåã«ããã³ã ã¦ã£ã³ãã¦ãå±éãã¦ãã ããã
error.tenant.management.tenant.window.required={0} ã®ããã³ã ã¦ã£ã³ãã¦ãæå®ãã¦ãã ãã
error.tenant.management.touchpoint.not.shared=ãªãªã¼ã¹ããåã«å°ãªãã¨ã 1 ã¤ã®ããã³ãã«å¯¾ãã¦ã¿ãããã¤ã³ããå±æãã¦ãã ããã
error.tenant.manangement.end.date.after.insert.schedule.start.date=æä½ã®çµäºæ¥ã¯æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã®éå§æ¥ãããåã§ãªããã°ãªãã¾ãã
error.tenant.manangement.start.date.after.end.date=æä½ã®éå§æ¥ã¯çµäºæ¥ãããåã§ãªããã°ãªãã¾ãã
error.tenant.manangement.start.date.after.insert.schedule.start.date=æä½ã®éå§æ¥ã¯æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã®éå§æ¥ãããåã§ãªããã°ãªãã¾ãã
error.theme.cssfilemissing=æ¬¡ã®ååã® CSS ãã¡ã¤ã«ãå¿è¦ã§ã: {0}
error.theme.duplicate.logo.filename=é¸æããã­ã´ ãã¡ã¤ã«åã¯æ¢ã«ä½¿ç¨ããã¦ãã¾ããååãå¤æ´ãã¦ååº¦ã¢ããã­ã¼ããã¦ãã ããã
error.theme.propertiesfilemissing=æ¬¡ã®ååã®ãã­ããã£ ãã¡ã¤ã«ãå¿è¦ã§ã: {0}
error.touchpoint.collection.is.being.referenced.on.delete=é¸æããã¿ãããã¤ã³ã ã³ã¬ã¯ã·ã§ã³ã¯åç§ããã¦ãã¾ããåé¤ããåã«ãã¹ã¦ã®åç§ãåé¤ãã¦ãã ããã
error.touchpointselection.cannot.delete.has.been.previously.active=é¸æããã 1 ã¤ä»¥ä¸ã®ããªã¢ã³ã¯æ¢ã«æ¿èªããã¦ã¢ã¯ãã£ãã«ãªã£ã¦ãããããåé¤ã§ãã¾ãã
error.touchpointselection.cannot.delete.has.nonremoved.message=é¸æããã 1 ã¤ä»¥ä¸ã®ããªã¢ã³ãã¯ãä½æ¥­ã¡ãã»ã¼ã¸ãã¢ã¯ãã£ã ã¡ãã»ã¼ã¸ãã¾ãã¯ã¢ã¼ã«ã¤ãæ¸ã¿ã¡ãã»ã¼ã¸ãé¢é£ä»ãããã¦ãããããåé¤ã§ãã¾ãã
error.touchpointselection.cannot.delete.has.selection=ãã®ããªã¢ã³ãã«ã¯å­ãå­å¨ããããåé¤ã§ãã¾ãã
error.touchpointselection.cannot.delete.is.master=ãã¹ã¿ã¼ ããªã¢ã³ãã¯åé¤ã§ãã¾ãã
error.touchpointselection.content.custom.content.must.be.provided=å¤æ´ãä¿å­ããåã«ãå°ãªãã¨ã 1 ã¤ã®è¨èªã«å¯¾ãã¦ã«ã¹ã¿ã  ã³ã³ãã³ããæå®ããå¿è¦ãããã¾ã
error.touchpointselection.content.default.lang.content.must.be.provided=ããã©ã«ãè¨èªã®ã³ã³ãã³ããæå®ãã¦ãã ãã
error.touchpointselection.content.type.must.be.selected=å¤æ´ãä¿å­ããåã«ãæåã«ã«ã¹ã¿ã  ã³ã³ãã³ããä½æããããéè¡¨ç¤ºã«ããããã¾ãã¯æ¢å­ã®ã³ã³ãã³ããåå©ç¨ããããé¸æããå¿è¦ãããã¾ã
error.touchpointselection.matching.variant.no.visibility=ä¸è´ããããªã¢ã³ããè¦ã¤ããã¾ãããããã®ãã¼ããè¡¨ç¤ºããæ¨©éãããã¾ãã
error.touchpointselection.selection.hierarchy.may.not.exceed.x=ããªã¢ã³ãéå±¤ã {0} ã¬ãã«ãè¶ãããã¨ã¯ã§ããªããããããã«ããªã¢ã³ããè¿½å ãããã¨ã¯ã§ãã¾ãã
error.touchpointselection.selection.name.already.exists=ãã®ååã®ããªã¢ã³ãã¯åãåå²ã«æ¢ã«å­å¨ãã¦ãã¾ã
error.touchpointselection.selectors.add.no.value.provided=è¿½å ããé¸æåºæºãæå®ãã¦ãã ãã
error.touchpointselection.selectors.add.selection.criteria.conflicts.with.an.existing.selection=æå®ããé¸æåºæº (\"{0}\") ã¯ããªã¢ã³ã {1} ã®åºæºã¨éè¤ãããããè¿½å ã§ãã¾ãã
error.touchpointselection.selectors.add.selection.criteria.falls.outside.criteria.for.its.parent.selection=æå®ããé¸æå¤ (\"{0}\") ãã«ã¼ãã®è¦ªã®å¤ããå¤ãã¦ãã¾ã: {1}
error.touchpointselection.selectors.add.selection.redefined.selection.criteria.would.result.in.orphaning.the.criteria.for.the.child.selection=è¡¨ç¤ºæ¨©éã®ãããæåã«ããªã¢ã³ã {1} ãããã¼ã¿ {0} ãåé¤ããå¿è¦ãããã¾ãã
error.touchpointselection.selectors.combination.is.added.before=æå®ããé¸æåºæº (\"{0}\") ã¯ãã®ããªã¢ã³ãã®æ¢å­ã®åºæºã¨éè¤ãããããè¿½å ã§ãã¾ãã
error.touchpointselection.selectors.remove.no.value.provided=åé¤ããé¸æåºæºãæå®ãã¦ãã ãã
error.touchpointselection.selectors.remove.no.value.to.remove=å®ç¾©ãããé¸æåºæºãå­å¨ãã¾ãã
error.touchpointselection.visibility.current.user.not.selected=ç¾å¨ã®ã¦ã¼ã¶ã¼ã®è¡¨ç¤ºæ¨©éãå¶éãããã¨ã¯ã§ãã¾ãã
error.touchpointselection.visibility.no.user.selected=å°ãªãã¨ã 1 äººã®ã¦ã¼ã¶ã¼ãè¡¨ç¤ºæ¨©éã«é¸æããå¿è¦ãããã¾ã
error.upload.file.is.empty={0} ã«ã¢ããã­ã¼ããããã¼ã¿ ãã¡ã¤ã«ãå¿è¦ã§ã
error.user.morethanonefound=å¥åããé»å­ã¡ã¼ã«ã§è¤æ°ã®ã¦ã¼ã¶ã¼ãè¦ã¤ããã¾ãããã¦ã¼ã¶ã¼åãå¥åãã¦ãã ããã
error.user.notfound=ç³ãè¨³ãããã¾ãããå¥åããã¦ã¼ã¶ã¼åã¯å­å¨ãã¾ãããããä¸åº¦ããç´ãã¦ãã ããã
error.user.settings.incorrectpassword=ç¾å¨ã®ãã¹ã¯ã¼ããæ­£ããããã¾ãã
error.user.settings.passwordsdonotmatch=ãã¹ã¯ã¼ããä¸è´ãã¾ãã
error.user.settings.resetKeynotvalid=ãã®ãªã³ã¯ã¯ã¢ã¯ãã£ãã§ã¯ããã¾ãããMessagepoint ç®¡çèã«åãåããã¦ãæ°ãããªã³ã¯ãè¦æ±ãã¦ãã ããã
error.user.settings.resetKeynotvalidOr=ãã®ãªã³ã¯ã¯ã¢ã¯ãã£ãã§ã¯ããã¾ãããMessagepoint ç®¡çèã«åãåãããã 
error.variable.selection.required={0} ã§ã¯å¤æ°ã®é¸æãå¿è¦ã§ãã
error.variant.name.already.existing=ããªã¢ã³ãåãæ¢ã«å­å¨ãã¾ãã
error.variant.name.can.not.be.empty=ååãç©ºã«ãããã¨ã¯ã§ãã¾ããã
error.workflow.approvals.approver.is.not.authorized=ãã®ã¯ã¼ã¯ãã­ã¼ã®ç·¨éä¸­ã« 1 äººä»¥ä¸ã®ã¦ã¼ã¶ã¼ã®ã¢ã¯ã»ã¹æ¨©ãå¤æ´ããã¾ããããã®ãããã¯ã¼ã¯ãã­ã¼ãæ´æ°ããã¾ãããç¢ºèªãã¦ããä¸åº¦ [ä¿å­] ãã¯ãªãã¯ãã¦ãã ããã
error.workflow.approvals.at.least.one.user.needed.for.each.approval.step=å°ãªãã¨ã 1 äººã®æ¿èªèãåã¯ã¼ã¯ãã­ã¼ ã¹ãããã«å²ãå½ã¦ãå¿è¦ãããã¾ãã
error.workflow.approvals.owner.is.mandatory.for.workflow.has.step=ã¯ã¼ã¯ãã­ã¼ææèãé¸æããå¿è¦ãããã¾ãã
error.workflow.approvals.owner.is.not.authorized=ãã®ã¯ã¼ã¯ãã­ã¼ææèã¯æ¿èªããã¦ãã¾ãããååº¦æ¿èªããããæ°ããã¯ã¼ã¯ãã­ã¼ææèãå²ãå½ã¦ã¦ãã ãããæ³¨æ: ã¯ã¼ã¯ãã­ã¼ææèãã¢ã¯ãã£ããªæ¿èªèã«ãããã¨ã¯ã§ãã¾ããã
error.workflow.approvals.step.name.cannot.be.empty=ã¯ã¼ã¯ãã­ã¼ ã¹ãããã®ååãç©ºç½ã®ã¾ã¾ã«ãããã¨ã¯ã§ãã¾ããã
error.workflow.approvals.timeframe.hour.too.large=æéã®æéã«ã¯ 23 ããå¤§ããªå¤ãæå®ã§ãã¾ããã
error.workflow.approvals.timeframe.not.specified=å°ãªãã¨ã 1 æéã®æéãå¥åããå¿è¦ãããã¾ãã
error.workflow.approvals.user.cannot.be.both.owner.and.approver=1 ã¤ã®ã¹ãããã§ 1 äººã®ã¦ã¼ã¶ã¼ãã¯ã¼ã¯ãã­ã¼ææèã¨æ¿èªèã®ä¸¡æ¹ã«ãããã¨ã¯ã§ãã¾ããã
licence.label.ClickatellConnector=Clickatell
licence.label.DataAnonymizer=ãã¼ã¿ ã¢ãããã¤ã¶ã¼
licence.label.DialogueConnector=HP Exstream
licence.label.DialogueDXF=DXF
licence.label.ExactTargetConnector=Exact Target
licence.label.FTPWebConnector=FTP Web
licence.label.GMCConnector=GMC
licence.label.InsertManagement=æ¿å¥ã®ç®¡ç
licence.label.MessagepointInteractive=Messagepoint Connected
licence.label.NativePrintConnector=ãã¤ãã£ã ã³ã³ãã¸ã·ã§ã³
licence.label.NumberOfFullUsers=éå¸¸ã¦ã¼ã¶ã¼
licence.label.NumberOfRestrictedUsers=å¶éã¦ã¼ã¶ã¼
licence.label.NumberOfSandboxes=ãµã³ãããã¯ã¹ 
licence.label.NumberOfTenants=ãã­ãã¯ã·ã§ã³
licence.label.SendMailConnector=SendMail ã³ãã¯ã¿ã¼
licence.label.Simulation=ã·ãã¥ã¬ã¼ã·ã§ã³
licence.label.VariantManagement=ããªã¢ã³ãç®¡ç
licence.label.eMessagingEmailConnector=e-Messaging é»å­ã¡ã¼ã«
licence.label.eMessagingSMSConnector=e-Messaging SMS
licence.label.sorting.ClickatellConnector=Clickatell
licence.label.sorting.DataAnonymizer=ãã¼ã¿ ã¢ãããã¤ã¶ã¼
licence.label.sorting.DialogueConnector=HP Exstream
licence.label.sorting.DialogueDXF=DXF
licence.label.sorting.ExactTargetConnector=Exact Target
licence.label.sorting.FTPWebConnector=FTP Web
licence.label.sorting.GMCConnector=GMC
licence.label.sorting.InsertManagement=æ¿å¥ã®ç®¡ç
licence.label.sorting.MessagepointInteractive=Messagepoint Connected
licence.label.sorting.NativePrintConnector=ãã¤ãã£ã ã³ã³ãã¸ã·ã§ã³
licence.label.sorting.NumberOfFullUsers=1
licence.label.sorting.NumberOfRestrictedUsers=2
licence.label.sorting.NumberOfSandboxes=4 
licence.label.sorting.NumberOfTenants=3
licence.label.sorting.SendMailConnector=SendMail ã³ãã¯ã¿ã¼
licence.label.sorting.Simulation=ã·ãã¥ã¬ã¼ã·ã§ã³
licence.label.sorting.VariantManagement=ããªã¢ã³ãç®¡ç
licence.label.sorting.eMessagingEmailConnector=e-Messaging é»å­ã¡ã¼ã«
licence.label.sorting.eMessagingSMSConnector=e-Messaging SMS
page.error.web.service.failed=ãã¼ã¿ãåå¾ã§ãã¾ãããWeb ãµã¼ãã¹ãå¤±æãã¾ãããç®¡çèã«åãåããã¦ãã ããã
page.flow.approve=æ¿èª
page.flow.back=å
page.flow.cancel=ã­ã£ã³ã»ã«
page.flow.confirm=ç¢ºèª
page.flow.discard=ç ´æ£
page.flow.forward=ç¶è¡
page.flow.reject.toauthoring=æå¦ãã¦ä½æã¸
page.flow.reject=æå¦
page.flow.requestpreview=ãã¬ãã¥ã¼ã®è¦æ±
page.flow.save=ä¿å­
page.flow.saveandback=å
page.flow.saveandforward=ç¶è¡
page.label.404error=404 ã¨ã©ã¼
page.label.ACTIVE=ã¢ã¯ãã£ã
page.label.ADD.TOUCHPOINT=ã¿ãããã¤ã³ãã®è¿½å 
page.label.ADD=è¿½å 
page.label.ALL=ãã¹ã¦
page.label.APPLICATION.SIGN.IN=ã¢ããªã±ã¼ã·ã§ã³ ãµã¤ã³ã¤ã³
page.label.ARCHIVE=ã¢ã¼ã«ã¤ã
page.label.ASSIGNED.TO=å²ãå½ã¦å
page.label.ASSIGNED=å²ãå½ã¦æ¸ã¿
page.label.ASSIGNMENT.WINDOW=å²ãå½ã¦ã¦ã£ã³ãã¦
page.label.AUDIT=ç£æ»
page.label.AVAILABLE.INSERTS=ä½¿ç¨å¯è½ãªæ¿å¥
page.label.AVAILABLE.MANDATORY.INSERTS=ä½¿ç¨å¯è½ãªå¿é ã®æ¿å¥
page.label.AVAILABLE.MESSAGES=ä½¿ç¨å¯è½ãªã¡ãã»ã¼ã¸
page.label.AVAILABLE.OPTIONAL.INSERTS=ä½¿ç¨å¯è½ãªãªãã·ã§ã³ã®æ¿å¥
page.label.AVAILABLE.PARAGRAPH.STYLES=ä½¿ç¨å¯è½ãªæ®µè½ã¹ã¿ã¤ã«
page.label.AVAILABLE.TEXT.STYLES=ä½¿ç¨å¯è½ãªãã­ã¹ã ã¹ã¿ã¤ã«
page.label.AVAILABLE.TOUCHPOINTS=ä½¿ç¨å¯è½ãªã¿ãããã¤ã³ã
page.label.AVAILABLE.USERS=ä½¿ç¨å¯è½ãªã¦ã¼ã¶ã¼
page.label.AVAILABLE.VARIABLES=ä½¿ç¨å¯è½ãªå¤æ°
page.label.AVAILABLE.WORKGROUPS=ä½¿ç¨å¯è½ãªã¯ã¼ã¯ã°ã«ã¼ã
page.label.Add.message=ã¡ãã»ã¼ã¸ã®è¿½å 
page.label.Add.target.group=ã¿ã¼ã²ãã ã°ã«ã¼ãã®è¿½å 
page.label.BACK.TO.MESSAGE=<< ã¡ãã»ã¼ã¸ã«æ»ã
page.label.BACK=æ»ã
page.label.CANCEL=ã­ã£ã³ã»ã«
page.label.CATEGORY=ã«ãã´ãª
page.label.CLONE=è¤è£½
page.label.CLOSE=éãã
page.label.COLLECTION.DEFAULT=ã³ã¬ã¯ã·ã§ã³ã®ããã©ã«ã
page.label.COMPOUND.KEY.VAR=è¤åã­ã¼å¤æ°
page.label.COST=ã³ã¹ã
page.label.DATE.RANGE=æ¥ä»ã®ç¯å²
page.label.DELIVERY=éä¿¡
page.label.DESCRIPTION=èª¬æ
page.label.DESELECT.ALL=ãã¹ã¦é¸æè§£é¤
page.label.DETAILS=è©³ç´°
page.label.DOWNLOAD.VARIANTS=ããªã¢ã³ãã®ãã¦ã³ã­ã¼ã
page.label.Delivery=éä¿¡
page.label.EBCDIC=EBCDIC
page.label.FRONT=åé¢
page.label.GLOBAL=ã°ã­ã¼ãã«
page.label.HP=HP
page.label.ID=ID
page.label.IMAGE.SELECTION=ç»åã®é¸æ
page.label.IMAGES.DISPLAYED.HERE=ç»åãããã«è¡¨ç¤º
page.label.IMPORT.IMAGES=ç»åã®ã¤ã³ãã¼ã
page.label.IMPORT.TOUCHPOINT=ã¿ãããã¤ã³ãã®ã¤ã³ãã¼ã
page.label.INSERT.SCHEDULE=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«
page.label.INSERTS.SELECTED=é¸æãããæ¿å¥
page.label.If=æ¡ä»¶
page.label.KEYWORDS=ã­ã¼ã¯ã¼ã
page.label.LANGUAGE=è¨èª
page.label.Location=å ´æ
page.label.MAKE.LOCAL.COPY=ã­ã¼ã«ã« ã³ãã¼ã®ä½æ
page.label.METATAGS=ã¡ã¿ã¿ã°
page.label.NA=N/A
page.label.NAME=åå
page.label.NEXT.ACTION=æ¬¡ã®ã¢ã¯ã·ã§ã³
page.label.NEXT=æ¬¡ã¸ >>
page.label.ONE=1
page.label.ORGANIZE=æ´ç
page.label.OWNER=ææè
page.label.PART=ãã¼ã
page.label.PARTS=ãã¼ã
page.label.POD.MASTER=ããã ãã¹ã¿ã¼
page.label.PREV=<< åã¸
page.label.PREVIEW=ãã¬ãã¥ã¼
page.label.PREVIOUS=<< åã¸
page.label.PRINT=å°å·
page.label.PRIORITIZE=åªååº¦è¨­å®
page.label.PRIORITY=åªååº¦
page.label.PROOF=æ ¡æ­£
page.label.PostProcess.XSLT.File=XSLT ãã¡ã¤ã«ã®å¾å¦ç
page.label.PreProcess.XSLT.File=XSLT ãã¡ã¤ã«ã®åå¦ç
page.label.REMOVE=åé¤
page.label.RESET=ãªã»ãã
page.label.RESOLVE.DATA.GROUPS=ãã¼ã¿ ã°ã«ã¼ãã®è§£æ±º
page.label.SAVE=ä¿å­
page.label.SCHEDULE.ID=ã¹ã±ã¸ã¥ã¼ã« ID
page.label.SECTIONS=ã»ã¯ã·ã§ã³
page.label.SEGMENTATION=ã»ã°ã¡ã³ãå
page.label.SELECT.ALL=ãã¹ã¦é¸æ
page.label.SELECTED.VARIABLES=é¸æãããå¤æ°
page.label.SESSION.TIMED.OUT=ã»ãã·ã§ã³ ã¿ã¤ã ã¢ã¦ã
page.label.SET=è¨­å®
page.label.SHARED.AS=å±æ
page.label.SHOW.AND.MAP.VARIABLES=ãã¹ã¦ã®å¤æ°ãè¡¨ç¤ºãã¦ããããã
page.label.SHOW=è¡¨ç¤º
page.label.SITE=ãµã¤ã
page.label.STARTTLS=STARTTLS
page.label.STATUS=ã¹ãã¼ã¿ã¹
page.label.STOCK.ID=ã¹ããã¯ ID
page.label.SUBMIT=æåº
page.label.TARGET.ZONE=ã¿ã¼ã²ãã ã¾ã¼ã³
page.label.TENANT.WINDOD=ããã³ã ã¦ã£ã³ãã¦
page.label.TENANT=ããã³ã
page.label.THROTTLE=èª¿æ´
page.label.TLS=TLS
page.label.TOUCHPOINT.DEFAULT=ã¿ãããã¤ã³ãã®ããã©ã«ã
page.label.TOUCHPOINT=ã¿ãããã¤ã³ã
page.label.TOUCHPOINTS=ã¿ãããã¤ã³ã
page.label.TYPE=ã¿ã¤ã
page.label.UPLOAD.VARIANTS=ããªã¢ã³ãã®ã¢ããã­ã¼ã
page.label.USAGE=ä½¿ç¨æ³
page.label.USED.BY=ä½¿ç¨è
page.label.VARIABLE=å¤æ°
page.label.VIEW.DATA=ãã¼ã¿ã®è¡¨ç¤º
page.label.VIEW=è¡¨ç¤º
page.label.WEIGHT=éé
page.label.WHEN=æ¥æ
page.label.WORKING.COPY=ä½æ¥­ã³ãã¼
page.label.XML.format.type=XML å½¢å¼ã¿ã¤ã
page.label.XML=XML
page.label.ZONES=ã¾ã¼ã³
page.label.above=ä¸
page.label.access.control=ã¢ã¯ã»ã¹å¶å¾¡
page.label.action.item=ã¢ã¯ã·ã§ã³ ã¢ã¤ãã 
page.label.action.required=å¿è¦ãªã¢ã¯ã·ã§ã³
page.label.action=ã¢ã¯ã·ã§ã³
page.label.actions=ã¢ã¯ã·ã§ã³
page.label.activate=ã¢ã¯ãã£ãå
page.label.activation.pending=ã¢ã¯ãã£ãåä¿çä¸­
page.label.active.tag=ã¢ã¯ãã£ã ã¿ã°
page.label.active=ã¢ã¯ãã£ã
page.label.activeusers=ã¢ã¯ãã£ã ã¦ã¼ã¶ã¼
page.label.actual.deliveries=å®éã®éä¿¡
page.label.add.attribute=å±æ§ã®è¿½å 
page.label.add.batch.report=æ°è¦ããã
page.label.add.branch=ãã¡ã¤ã³ã®è¿½å 
page.label.add.collection=ã³ã¬ã¯ã·ã§ã³ã®è¿½å 
page.label.add.communication=ã³ãã¥ãã±ã¼ã·ã§ã³ã®è¿½å 
page.label.add.connection=æ¥ç¶ã®è¿½å 
page.label.add.constant=æ°è¦å®æ°ã®è¿½å 
page.label.add.content.selector.group=ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ ã°ã«ã¼ãã®è¿½å 
page.label.add.content.selector=ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ã®è¿½å 
page.label.add.data.collection=ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³ã®è¿½å 
page.label.add.data.element=ãã¼ã¿è¦ç´ ã®è¿½å 
page.label.add.data.file=ãã¼ã¿ ãã¡ã¤ã«ã®è¿½å 
page.label.add.data.group=ãã¼ã¿ ã°ã«ã¼ãã®è¿½å 
page.label.add.data.record=ãã¼ã¿ ã¬ã³ã¼ãã®è¿½å 
page.label.add.data.resource=ãã¼ã¿ ãªã½ã¼ã¹ã®è¿½å 
page.label.add.data.source=ãã¼ã¿ ã½ã¼ã¹ã®è¿½å 
page.label.add.data=ãã¼ã¿ã®è¿½å 
page.label.add.delivery.event=éä¿¡ã¤ãã³ãã®è¿½å 
page.label.add.element=è¦ç´ ã®è¿½å 
page.label.add.event=ã¤ãã³ãã®è¿½å 
page.label.add.files=ãã¡ã¤ã«ã®è¿½å ...
page.label.add.filter=ãã£ã«ã¿ã¼ã®è¿½å 
page.label.add.image=ç»åã®è¿½å 
page.label.add.indicator=ã¤ã³ã¸ã±ã¼ã¿ã¼ã®è¿½å 
page.label.add.insert.schedule=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã®è¿½å 
page.label.add.insert=æ¿å¥ã®è¿½å 
page.label.add.item=ã¢ã¤ãã ã®è¿½å 
page.label.add.language=è¨èªã®è¿½å 
page.label.add.licence=ã©ã¤ã»ã³ã¹ã®è¿½å 
page.label.add.menu.item=ã¡ãã¥ã¼é ç®ã®è¿½å 
page.label.add.message.delivery.report=æ°è¦ã¡ãã»ã¼ã¸éä¿¡
page.label.add.message=ã¡ãã»ã¼ã¸ã®è¿½å 
page.label.add.node=ã¤ã³ã¹ã¿ã³ã¹ã®è¿½å 
page.label.add.paragraph.style=æ®µè½ã¹ã¿ã¤ã«ã®è¿½å 
page.label.add.pod=ãããã®è¿½å 
page.label.add.rate.sheet=æéè¡¨ã®è¿½å 
page.label.add.reference.data=åç§ãã¼ã¿ã®è¿½å 
page.label.add.role=å½¹å²ã®è¿½å 
page.label.add.rule.condition=ã«ã¼ã«æ¡ä»¶ã®è¿½å 
page.label.add.rule=ã«ã¼ã«ã®è¿½å 
page.label.add.selection=é¸æã®è¿½å 
page.label.add.simulation=æ°è¦ã·ãã¥ã¬ã¼ã·ã§ã³
page.label.add.smart.text=ã¹ãã¼ã ãã­ã¹ãã®è¿½å 
page.label.add.step=ã¹ãããã®è¿½å 
page.label.add.style.family=ã¹ã¿ã¤ã« ãã¡ããªã®è¿½å 
page.label.add.style=ã¹ã¿ã¤ã«ã®è¿½å 
page.label.add.sub.branch=ãµããã¡ã¤ã³ã®è¿½å 
page.label.add.sub.variant=ãµãããªã¢ã³ãã®è¿½å 
page.label.add.tag.type=ã¿ã° ã¿ã¤ãã®è¿½å 
page.label.add.tag=ã¿ã°ã®è¿½å 
page.label.add.target.group=ã¿ã¼ã²ãã ã°ã«ã¼ãã®è¿½å 
page.label.add.task=ã¿ã¹ã¯ã®è¿½å 
page.label.add.test=æ°è¦ãã¹ã
page.label.add.to.favorites=ãæ°ã«å¥ãã¸ã®è¿½å 
page.label.add.touchpoint.delivery.report=æ°è¦ã¿ãããã¤ã³ãéä¿¡
page.label.add.touchpoint=ã¿ãããã¤ã³ãã®è¿½å 
page.label.add.variable=å¤æ°ã®è¿½å 
page.label.add.variant=ããªã¢ã³ãã®è¿½å 
page.label.add=è¿½å 
page.label.admin.role.createnewrole=å½¹å²ã®è¿½å 
page.label.admin.role.updaterole=å½¹å²ã®ç·¨é - {0}
page.label.admin.systemproperties.audit_report_dir=ç£æ»ã¬ãã¼ã
page.label.admin.systemproperties.connected_composition_delivery=ã³ã³ãã¸ã·ã§ã³ã®éä¿¡
page.label.admin.systemproperties.csrf_domain_strict=CSRF ãã¡ã¤ã³ ã¹ããªã¯ã
page.label.admin.systemproperties.de_versions=DE ãã¼ã¸ã§ã³
page.label.admin.systemproperties.default_boolean_format_false=False ãã¼ã«å¤
page.label.admin.systemproperties.default_boolean_format_true=True ãã¼ã«å¤
page.label.admin.systemproperties.default_date_format=æ¥ä»ã®å½¢å¼
page.label.admin.systemproperties.default_page=è¡¨ç¤ºããæåã®ãã¼ã¸
page.label.admin.systemproperties.default_page_size=ãªã¹ããã¨ã®ã¨ã³ããªæ°
page.label.admin.systemproperties.default_system_language=ã·ã¹ãã ã®ããã©ã«ãè¨èª (ã­ã±ã¼ã«)
page.label.admin.systemproperties.diagnostics_report_dir=è¨ºæ­ã¬ãã¼ã
page.label.admin.systemproperties.email_cfg_appFromAddress=è¿ä¿¡åã¢ãã¬ã¹
page.label.admin.systemproperties.email_connection_security=æ¥ç¶ã®ã»ã­ã¥ãªãã£
page.label.admin.systemproperties.email_pop3_host=POP3 ãã¹ã
page.label.admin.systemproperties.email_pop3_password=POP3 ãã¹ã¯ã¼ã
page.label.admin.systemproperties.email_pop3_port=POP3 ãã¼ã
page.label.admin.systemproperties.email_pop3_user=POP3 ã¦ã¼ã¶ã¼
page.label.admin.systemproperties.email_preview_dir=é»å­ã¡ã¼ã«ã®ãã¬ãã¥ã¼
page.label.admin.systemproperties.email_server_outgoing=ãµã¼ãã¼
page.label.admin.systemproperties.email_server_outgoing_password=ã¢ã«ã¦ã³ã ãã¹ã¯ã¼ã
page.label.admin.systemproperties.email_server_outgoing_port=ãã¼ã
page.label.admin.systemproperties.email_server_outgoing_user=ã¢ã«ã¦ã³ãå
page.label.admin.systemproperties.email_template_dir=é»å­ã¡ã¼ã« ãã³ãã¬ã¼ã
page.label.admin.systemproperties.email_webroot_dir=é»å­ã¡ã¼ã«ã® Webroot
page.label.admin.systemproperties.insert_startingBinNo=éå§ãã³çªå·
page.label.admin.systemproperties.insert_weightUnits=ééåä½
page.label.admin.systemproperties.insertschedule_export_dir=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã®ã¨ã¯ã¹ãã¼ã
page.label.admin.systemproperties.job_folder_incoming=åä¿¡ã¸ã§ã
page.label.admin.systemproperties.job_folder_incoming_failure=å¤±æã¸ã§ã
page.label.admin.systemproperties.job_folder_incoming_sucess=æåã¸ã§ã
page.label.admin.systemproperties.job_folder_outgoing=éä¿¡ã¸ã§ã
page.label.admin.systemproperties.job_folder_working=é²è¡ä¸­
page.label.admin.systemproperties.job_proof_customers_max=æ ¡æ­£å½ããã®æå¤§é¡§å®¢æ°
page.label.admin.systemproperties.job_test_customers_max=ãã¹ãå½ããã®æå¤§é¡§å®¢æ°
page.label.admin.systemproperties.message_folder_application=ã¢ããªã±ã¼ã·ã§ã³ã® Webroot
page.label.admin.systemproperties.message_folder_applicationcache=ã­ã£ãã·ã¥
page.label.admin.systemproperties.message_folder_applicationreport=ã¢ããªã±ã¼ã·ã§ã³ ã¬ãã¼ã
page.label.admin.systemproperties.message_folder_compositionfileset=ã³ã³ãã¸ã·ã§ã³ ãã¡ã¤ã«
page.label.admin.systemproperties.message_folder_datafiles=ãã¼ã¿ ãã¡ã¤ã«
page.label.admin.systemproperties.message_folder_dispatch=çºé
page.label.admin.systemproperties.message_folder_export=ã¡ãã»ã¼ã¸ã®ã¨ã¯ã¹ãã¼ã
page.label.admin.systemproperties.message_folder_images=ç»å
page.label.admin.systemproperties.message_folder_output=ã¡ãã»ã¼ã¸åºå
page.label.admin.systemproperties.message_folder_targetingdatafiles=ã¿ã¼ã²ããè¨­å®ãã¼ã¿ ãã¡ã¤ã«
page.label.admin.systemproperties.message_folder_xmlconnector=XML æ±ç¨ã³ãã¯ã¿ã¼ ãã¡ã¤ã«
page.label.admin.systemproperties.message_messageContent_history_dir=ç»åã³ã³ãã³ãå±¥æ­´
page.label.admin.systemproperties.messagepoint.qe.fileroot=ã¯ã©ã¤ã¢ã³ã DE
page.label.admin.systemproperties.messagepoint_object_export_dir=ãªãã¸ã§ã¯ãã®ã¨ã¯ã¹ãã¼ã
page.label.admin.systemproperties.password_expire_after=ãã¹ã¯ã¼ãã®æå¹æ¥æ°
page.label.admin.systemproperties.password_expires=ãã¹ã¯ã¼ãã®æå¹æé
page.label.admin.systemproperties.password_history_entries=ãã¹ã¯ã¼ãå±¥æ­´ã¨ã³ããª (ããã³ç¾å¨ã®ãã¹ã¯ã¼ã) 
page.label.admin.systemproperties.password_limit_reuse_period=ãã¹ã¯ã¼ãåå©ç¨å¶éã®æé
page.label.admin.systemproperties.password_limit_reuse_period_months=ãã¹ã¯ã¼ãåå©ç¨æé (æ)
page.label.admin.systemproperties.password_reset_keepalive=ãã¹ã¯ã¼ã ãªã»ãã ã­ã¼ãã¢ã©ã¤ã (å)
page.label.admin.systemproperties.platform_encodingtype_preview=ã¨ã³ã³ã¼ãã£ã³ã°ã®ãã¬ãã¥ã¼
page.label.admin.systemproperties.platform_encodingtype_production=ã¢ã¯ãã£ããªã¨ã³ã³ã¼ãã£ã³ã°
page.label.admin.systemproperties.platform_encodingtype_test=ã¨ã³ã³ã¼ãã£ã³ã°ã®ãã¹ã
page.label.admin.systemproperties.prevent_repeated_password=ãã¹ã¯ã¼ãã®åå©ç¨ã®ç¦æ­¢
page.label.admin.systemproperties.report_dir=ã¬ãã¼ã
page.label.admin.systemproperties.restrict_signin_autocomplete=ãµã¤ã³ã¤ã³ã®ãªã¼ãã³ã³ããªã¼ãã®å¶é
page.label.admin.systemproperties.session_timeout=ã»ãã·ã§ã³ ã¿ã¤ã ã¢ã¦ã (å)
page.label.admin.systemproperties.simulation_export_dir=ã·ãã¥ã¬ã¼ã·ã§ã³ã®ã¨ã¯ã¹ãã¼ã
page.label.admin.systemproperties.sso_url_redirect=ãçµäºãURL
page.label.admin.systemproperties.system_date_format=æ¥ä»ã®å½¢å¼
page.label.admin.systemproperties.system_server_port=ãã¼ã
page.label.admin.systemproperties.system_server_protocol=ãã­ãã³ã«
page.label.admin.systemproperties.system_server_webroot=Webroot
page.label.admin.systemproperties.target_rule_separator_is_one_of=ãä¸é¨ã§ãããã«å¯¾ããã¿ã¼ã²ãã ã«ã¼ã«åºåã
page.label.admin.systemproperties.test_mode=ãã¹ã ã¢ã¼ã
page.label.admin.systemproperties.where_used_report_dir=ä½¿ç¨å ´æã¬ãã¼ã
page.label.admin.systemproperties_global_sendemail_booleanflag=é»å­ã¡ã¼ã«ãæå¹ã«ãã
page.label.admin.user.createnewuser=æ°è¦ã¦ã¼ã¶ã¼ã®ä½æ
page.label.admin.user.updateuser=ã¦ã¼ã¶ã¼ã®ç·¨é - {0}
page.label.admin=ç®¡çè
page.label.advanced.search=é«åº¦ãªæ¤ç´¢
page.label.advanced=é«åº¦
page.label.aggregation.level=éç´ã¬ãã«
page.label.aggregation=éç´
page.label.alignment=éç½®
page.label.all.categories=ãã¹ã¦ã®ã«ãã´ãª
page.label.all.content=ãã¹ã¦ã®ã³ã³ãã³ã
page.label.all.messages=ãã¹ã¦ã®ã¡ãã»ã¼ã¸
page.label.all.sections=ãã¹ã¦ã®ã»ã¯ã·ã§ã³
page.label.all.shared.content=ãã¹ã¦ã®å±æã³ã³ãã³ã
page.label.all.sites=ãã¹ã¦ã®ãµã¤ã
page.label.all.zones=ãã¹ã¦ã®ã¾ã¼ã³
page.label.all=ãã¹ã¦
page.label.alphanumeric.only=è±æ°å­ã®ã¿
page.label.alternate.templates=ä»£æ¿ãã³ãã¬ã¼ã
page.label.alternate_templates=ä»£æ¿ãã³ãã¬ã¼ã
page.label.analysis.enabled=åæãæå¹ã«ãã
page.label.analytics=åæ
page.label.ancestors=è¦ªè¦ç´ 
page.label.and.include=ããã«å«ãã
page.label.and.of.these.include.groups=ãããã®åãã°ã«ã¼ããå«ãã
page.label.and=ããã³
page.label.anonymization.type.address.apt=ä½æ 2 (ã¢ãã¼ããé¨å±ãã¦ããã)
page.label.anonymization.type.address.street=ä½æ 1 (çªå°)
page.label.anonymization.type.age=å¹´é½¢
page.label.anonymization.type.city=å¸åºçºæ
page.label.anonymization.type.currency=éè²¨
page.label.anonymization.type.date=æ¥ä»
page.label.anonymization.type.decimal=å°æ°ç¹
page.label.anonymization.type.first.name=å
page.label.anonymization.type.integer=æ´æ°
page.label.anonymization.type.last.name=å§
page.label.anonymization.type.name=åå
page.label.anonymization.type.num.text=æ°
page.label.anonymization.type.post.zip=éµä¾¿çªå·
page.label.anonymization.type.post=éµä¾¿çªå·
page.label.anonymization.type.prov.state=é½éåºç
page.label.anonymization.type.prov=å·
page.label.anonymization.type.sequence=é åº
page.label.anonymization.type.state=ç¶æ
page.label.anonymization.type.telephone=é»è©±çªå·
page.label.anonymization.type.text=ãã­ã¹ã
page.label.anonymization.type.yes.no=ã¯ã/ããã
page.label.anonymization.type.zip=éµä¾¿çªå·
page.label.anonymized=å¿åå
page.label.any.status=ä»»æã®ã¹ãã¼ã¿ã¹
page.label.apply.border=æ ç·ã®è¨­å®
page.label.apply.default=ããã©ã«ãã®é©ç¨
page.label.apply.to=é©ç¨å
page.label.apply=é©ç¨
page.label.approval.type.all.of=ãã¹ã¦
page.label.approval.type.any.of=ãããã
page.label.approval.type=æ¿èªã¿ã¤ã
page.label.approval=æ¿èª
page.label.approvals=æ¿èª
page.label.approve.and.override=æ¿èªãã¦ä¸æ¸ã
page.label.approve.reject=æ¿èª/æå¦
page.label.approve.workflow.owner=æ¿èª: ã¯ã¼ã¯ãã­ã¼ææè
page.label.approve=æ¿èª
page.label.approved=æ¿èªæ¸ã¿
page.label.approvers=æ¿èªè
page.label.archive.inserts=æ¿å¥ã®ã¢ã¼ã«ã¤ã
page.label.archive.messages=ã¡ãã»ã¼ã¸ã®ã¢ã¼ã«ã¤ã
page.label.archive=ã¢ã¼ã«ã¤ã
page.label.as.embedded=åãè¾¼ã¿
page.label.as.landing.page=ã©ã³ãã£ã³ã° ãã¼ã¸
page.label.ascii=ASCII
page.label.assign=å²ãå½ã¦
page.label.assigned.to=å²ãå½ã¦å
page.label.assigned=å²ãå½ã¦æ¸ã¿
page.label.assignments=å²ãå½ã¦
page.label.associate.touchpoints=ã¿ãããã¤ã³ãã®é¢é£ä»ã
page.label.associated.data.records=é¢é£ãããã¼ã¿ ã¬ã³ã¼ã
page.label.association.key=é¢é£ä»ãã­ã¼
page.label.association.properties=é¢é£ä»ãã®ãã­ããã£
page.label.associations=é¢é£ä»ã
page.label.at.least.one=å°ãªãã¨ã 1 ã¤
page.label.attachment.delivery.type.mandatory=å¿é 
page.label.attachment.delivery.type.optional=ãªãã·ã§ã³
page.label.attachment.management=æ·»ä»ãã¡ã¤ã«ç®¡ç
page.label.attachment.targeting=æ·»ä»ãã¡ã¤ã«ã®ã¿ã¼ã²ããè¨­å®
page.label.attachment=æ·»ä»ãã¡ã¤ã«
page.label.attempts=è©¦è¡åæ°
page.label.attribute=å±æ§
page.label.attributes=å±æ§
page.label.audit.report.status.complete=å®äº
page.label.audit.report.status.error=ã¨ã©ã¼
page.label.audit.report.status.expired=æéåã
page.label.audit.report.status.in.process=é²è¡ä¸­
page.label.audit.report.type.insert.audit.report=æ¿å¥ç£æ»ã¬ãã¼ã
page.label.audit.report.type.message.audit.report=ã¡ãã»ã¼ã¸ç£æ»ã¬ãã¼ã
page.label.audit.report.type.touchpoint.audit.report=ã¿ãããã¤ã³ãç£æ»ã¬ãã¼ã
page.label.audit.report=ç£æ»ã¬ãã¼ã...
page.label.audit=ç£æ»
page.label.authentication.failed=èªè¨¼ã«å¤±æãã¾ãã
page.label.auto.detect=èªåæ¤åº
page.label.auto.generated=èªåçæ
page.label.autorefresh=èªåæ´æ°
page.label.auxiliary.data.connections=è£å©ãã¼ã¿æ¥ç¶
page.label.available=ä½¿ç¨å¯è½
page.label.axis2.repository=AXIS2 ãªãã¸ããª
page.label.back.to.insert.schedule=<< æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã«æ»ã
page.label.back.to.insert=<< æ¿å¥ã«æ»ã
page.label.back.to.list=<< ãªã¹ãã«æ»ã
page.label.back=æ»ã
page.label.background.color=èæ¯è²
page.label.background.images=èæ¯ç»å
page.label.background=èæ¯
page.label.basic.sso=åºæ¬ SSO
page.label.batch.report=ããã ã¬ãã¼ã
page.label.batch=ããã
page.label.between=ãã
page.label.bin.assignments=ãã³å²ãå½ã¦
page.label.bin.no=ãã³çªå·
page.label.bin.number=ãã³çªå·
page.label.bin.properties=ãã³ã®ãã­ããã£
page.label.bin.reservations=ãã³ã®äºç´
page.label.bold=å¤ªå­
page.label.booleansymbolfalse=ãã¼ã«è¨å· (False)
page.label.booleansymboltrue=ãã¼ã«è¨å· (True)
page.label.border.color=æ ç·ã®è²
page.label.border.width=æ ç·ã®å¹
page.label.border=æ ç·
page.label.bottom=ä¸
page.label.box=ããã¯ã¹
page.label.branch.actions=ãã¡ã¤ã³ã®ã¢ã¯ã·ã§ã³
page.label.branch.code=ãã¡ã¤ã³ ã³ã¼ã
page.label.branch.domain=ãã¡ã¤ã³
page.label.branch.edit=ãã¡ã¤ã³ç·¨é
page.label.branch.finder=ãã¡ã¤ã³æ¤ç´¢
page.label.branch.settings=ãã¡ã¤ã³è¨­å®
page.label.branch.ssotype=ãã¡ã¤ã³ SSO ã¿ã¤ã
page.label.branch.type=ãã¡ã¤ã³ ã¿ã¤ã
page.label.branches=ãã¡ã¤ã³
page.label.breakindicator=åºåãã¤ã³ã¸ã±ã¼ã¿ã¼
page.label.browse=åç§
page.label.bulk.upload=ä¸æ¬ã¢ããã­ã¼ã
page.label.bulleted.lists=ç®æ¡æ¸ããªã¹ã
page.label.button.add.variant=ããªã¢ã³ãã®è¿½å 
page.label.by=æ¹æ³
page.label.canada=ã«ãã
page.label.cancel.simulation=ã·ãã¥ã¬ã¼ã·ã§ã³ã®ã­ã£ã³ã»ã«
page.label.cancel.upload=ã¢ããã­ã¼ãã®ã­ã£ã³ã»ã«
page.label.cancel=ã­ã£ã³ã»ã«
page.label.category=ã«ãã´ãª
page.label.cell.padding=ã»ã«åã®ã¹ãã¼ã¹
page.label.cell.width=ã»ã«ã®å¹
page.label.cell=ã»ã«
page.label.cells=ã»ã«
page.label.center=ä¸­å¤®
page.label.change.language=è¨èªã®å¤æ´
page.label.change.touchpoint.context=ã¿ãããã¤ã³ã ã³ã³ãã­ã¹ãã®å¤æ´
page.label.change.user.workgroup=ã¦ã¼ã¶ã¼ ã¯ã¼ã¯ã°ã«ã¼ãã®å¤æ´
page.label.change.variant=ããªã¢ã³ãã®å¤æ´
page.label.change=å¤æ´
page.label.channel.configuration=ãã£ãã«æ§æ
page.label.channel=ãã£ãã«
page.label.chars=æå­æ°
page.label.check.type.check=ãã§ãã¯
page.label.check.type.circle=å
page.label.check.type.cross=åå­
page.label.check.type.diamond=ã²ãå½¢
page.label.check.type.square=åè§
page.label.check.type.star=æ
page.label.checkbox=ãã§ãã¯ããã¯ã¹
page.label.class=ã¯ã©ã¹
page.label.click.add.files=[ãã¡ã¤ã«ã®è¿½å ] ãã¯ãªãã¯...
page.label.clickatell.appid=ã¢ããª ID
page.label.clickatell.auth.api.url=AUTH API URL
page.label.clickatell.password=ãã¹ã¯ã¼ã
page.label.clickatell.send.api.url=SEND API URL
page.label.clickatell.senderid=éä¿¡è ID
page.label.clickatell.settings=Clickatell è¨­å®
page.label.clickatell.user=ã¦ã¼ã¶ã¼
page.label.clone.message=ã¡ãã»ã¼ã¸ã®è¤è£½
page.label.clone=è¤è£½
page.label.close=éãã
page.label.cloud=ã¯ã©ã¦ã
page.label.cms.host=CMS ãã¹ã
page.label.cms.image.asset.definition=CMS ç»åã¢ã»ããå®ç¾©
page.label.cms.image.category.definition=CMS ç»åã«ãã´ãªå®ç¾©
page.label.cms.password=CMS ãã¹ã¯ã¼ã
page.label.cms.settings=CMS è¨­å®
page.label.cms.sync=CMS ã¨ã®åæ
page.label.cms.upload=CMS ã¢ããã­ã¼ã
page.label.cms.user=CMS ã¦ã¼ã¶ã¼
page.label.code=ã³ã¼ã
page.label.cofirm.add.variant=ç¢ºèª - ããªã¢ã³ãã®è¿½å 
page.label.collection.setup=ã³ã¬ã¯ã·ã§ã³ ã»ããã¢ãã
page.label.collection=ã³ã¬ã¯ã·ã§ã³
page.label.color=ã«ã©ã¼
page.label.comb=ã³ã¼ã 
page.label.comment.history=ã³ã¡ã³ãå±¥æ­´
page.label.comment=ã³ã¡ã³ã
page.label.comments=ã³ã¡ã³ã
page.label.communication=ã³ãã¥ãã±ã¼ã·ã§ã³
page.label.communications.management=æ¥ç¶æ¸ã¿ç®¡ç
page.label.communications.setup=ã³ãã¥ãã±ã¼ã·ã§ã³ ã»ããã¢ãã
page.label.communications=ã³ãã¥ãã±ã¼ã·ã§ã³
page.label.company=ä¼ç¤¾
page.label.complete.assignments=å²ãå½ã¦ãå®äº
page.label.complete.brackets=(å®äº)
page.label.complete=å®äº
page.label.composition.package=ã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸
page.label.composition.packages.zero=ã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸ (0)
page.label.composition.packages=ã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸
page.label.composition.results=ã³ã³ãã¸ã·ã§ã³çµæ
page.label.composition.version=ã³ã³ãã¸ã·ã§ã³ ãã¼ã¸ã§ã³
page.label.compound.format.type.and=ãã¤
page.label.compound.format.type.comma=ã³ã³ã
page.label.compound.format.type.none=ãªã
page.label.compound.format.type.or=ã¾ãã¯
page.label.compound.key=è¤åã­ã¼
page.label.compound.variable.format=è¤åå¤æ°å½¢å¼
page.label.condition=æ¡ä»¶
page.label.configurable.workflow.action.state.type.final.approval=æçµæ¿èª
page.label.configurable.workflow.action.state.type.intermediate.approval=ä¸­éæ¿èª
page.label.configurable.workflow.action.state.type.none=ãªã
page.label.configurable.workflow.action.state.type.release.for.approval=ãªãªã¼ã¹ãã¦æ¿èªãåãã
page.label.configurable.workflow.action.state.type.working.copy=ä½æ¥­ã³ãã¼
page.label.configurable.workflow.action.type.approved=æ¿èªæ¸ã¿
page.label.configurable.workflow.action.type.created=ä½ææ¸ã¿
page.label.configurable.workflow.action.type.reassigned=åå²ãå½ã¦æ¸ã¿
page.label.configurable.workflow.action.type.rejected=æå¦æ¸ã¿
page.label.configure=æ§æ
page.label.confirm.activate=ç¢ºèª - ã¢ã¯ãã£ãå
page.label.confirm.add.communication=ç¢ºèª - ã³ãã¥ãã±ã¼ã·ã§ã³ã®è¿½å 
page.label.confirm.add.external.event=ç¢ºèª - å¤é¨ã¤ãã³ãã®è¿½å 
page.label.confirm.add.selection=ç¢ºèª - é¸æåå®¹ã®è¿½å 
page.label.confirm.add.to.favorites=ç¢ºèª - ãæ°ã«å¥ãã¸ã®è¿½å 
page.label.confirm.add.touchpoint=ç¢ºèª - ã¿ãããã¤ã³ãã®è¿½å 
page.label.confirm.approve.reject=ç¢ºèª - æ¿èª/æå¦
page.label.confirm.archive=ç¢ºèª - ã¢ã¼ã«ã¤ã
page.label.confirm.cancel.simulation=ç¢ºèª - ã·ãã¥ã¬ã¼ã·ã§ã³ã®ã­ã£ã³ã»ã«
page.label.confirm.clone=ç¢ºèª - è¤è£½
page.label.confirm.complete.assignments=ç¢ºèª - å²ãå½ã¦ãå®äº
page.label.confirm.copy.data=ç¢ºèª - ãã¼ã¿ã®ã³ãã¼
page.label.confirm.create.working.copy=ç¢ºèª - ä½æ¥­ã³ãã¼ã®ä½æ
page.label.confirm.deactivate.user=ç¢ºèª - ã¦ã¼ã¶ã¼ã®éã¢ã¯ãã£ãå
page.label.confirm.deactivate=ç¢ºèª - éã¢ã¯ãã£ãå
page.label.confirm.default.language.change=ç¢ºèª - ããã©ã«ãè¨èªã®å¤æ´
page.label.confirm.delete.archive=ç¢ºèª - ã¢ã¼ã«ã¤ãã®åé¤
page.label.confirm.delete.attachment=ç¢ºèª - æ·»ä»ãã¡ã¤ã«ã®åé¤
page.label.confirm.delete.composition.packages=ç¢ºèª - ã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸ã®åé¤
page.label.confirm.delete.constant=ç¢ºèª - å®æ°ã®åé¤
page.label.confirm.delete.external.event=ç¢ºèª - å¤é¨ã¤ãã³ãã®åé¤
page.label.confirm.delete.language=ç¢ºèª - è¨èªã®åé¤
page.label.confirm.delete.licence=ç¢ºèª - ã©ã¤ã»ã³ã¹ã®åé¤
page.label.confirm.delete.paragraph.style=ç¢ºèª - æ®µè½ã¹ã¿ã¤ã«ã®åé¤
page.label.confirm.delete.pod=ç¢ºèª - ãããã®åé¤
page.label.confirm.delete.report=ç¢ºèª - ã¬ãã¼ã ã·ããªãªã®åé¤
page.label.confirm.delete.simulation=ç¢ºèª - ã·ãã¥ã¬ã¼ã·ã§ã³ã®åé¤
page.label.confirm.delete.target.group=ç¢ºèª - ã¿ã¼ã²ãã ã°ã«ã¼ãã®åé¤
page.label.confirm.delete.targeting.rule=ç¢ºèª - ã¿ã¼ã²ããè¨­å®ã«ã¼ã«ã®åé¤
page.label.confirm.delete.test=ç¢ºèª - ãã¹ã ã·ããªãªã®åé¤
page.label.confirm.delete.variable=ç¢ºèª - å¤æ°ã®åé¤
page.label.confirm.delete.variant=ç¢ºèª - ããªã¢ã³ãã®åé¤
page.label.confirm.delete=ç¢ºèª - åé¤
page.label.confirm.discard.tag.type=ç¢ºèª: ã¿ã° ã¿ã¤ãã®ç ´æ£
page.label.confirm.discard.working.copy=ç¢ºèª - ä½æ¥­ã³ãã¼ã®ç ´æ£
page.label.confirm.discard=ç¢ºèª - ç ´æ£
page.label.confirm.export.touchpoint=ç¢ºèª - ã¿ãããã¤ã³ãã®ã¨ã¯ã¹ãã¼ã
page.label.confirm.export.variants=ç¢ºèª - ããªã¢ã³ãã®ã¨ã¯ã¹ãã¼ã
page.label.confirm.generate.diagnostics.report=ç¢ºèª - 'è¨ºæ­' ã¬ãã¼ãã®çæ
page.label.confirm.generate.where.used=ç¢ºèª - ãä½¿ç¨å ´æãã¬ãã¼ãã®çæ
page.label.confirm.hold=ç¢ºèª - ä¿ç
page.label.confirm.migration=ç¢ºèª - ã¦ã¼ã¶ã¼ã¨è¨­å®ã®ç§»è¡
page.label.confirm.modify.language=ç¢ºèª - è¨èªã®å¤æ´
page.label.confirm.new.password=æ°è¦ãã¹ã¯ã¼ãã®ç¢ºèª
page.label.confirm.off.line=ç¢ºèª - ãªãã©ã¤ã³ã«ãã
page.label.confirm.on.line=ç¢ºèª - ãªã³ã©ã¤ã³ã«ãã
page.label.confirm.password.reset=ç¢ºèª - ãã¹ã¯ã¼ã ãªã»ãã
page.label.confirm.password=ç¾å¨ã®ãã¹ã¯ã¼ã
page.label.confirm.production.batch=ç¢ºèª - ãã­ãã¯ã·ã§ã³ ããã
page.label.confirm.proof=ç¢ºèª - æ ¡æ­£
page.label.confirm.reassign.to.user=ç¢ºèª - ã¦ã¼ã¶ã¼ã«åå²ãå½ã¦
page.label.confirm.refresh.segmentation.analysis=ç¢ºèª - ã»ã°ã¡ã³ãååæã®æ´æ°
page.label.confirm.reinstate=ç¢ºèª - åé
page.label.confirm.release.for.approval=ç¢ºèª - ãªãªã¼ã¹ãã¦æ¿èªãåãã
page.label.confirm.release.for.use=ç¢ºèª - ãªãªã¼ã¹ãã¦ä½¿ç¨ãã
page.label.confirm.release.to.tenants=ç¢ºèª - ããã³ãã«å¯¾ãã¦ãªãªã¼ã¹ãã
page.label.confirm.remove.customization=ç¢ºèª - ã«ã¹ã¿ãã¤ãºã®åé¤
page.label.confirm.remove.from.favorites=ç¢ºèª - ãæ°ã«å¥ãããåé¤
page.label.confirm.remove=ç¢ºèª - åé¤
page.label.confirm.rename.branch=ç¢ºèª - ãã¡ã¤ã³åã®å¤æ´
page.label.confirm.rename.selection=ç¢ºèª - é¸æåã®å¤æ´
page.label.confirm.rename.variant=ç¢ºèª - ããªã¢ã³ãåã®å¤æ´
page.label.confirm.rename=ç¢ºèª - ååã®å¤æ´
page.label.confirm.reopen.tenant.window=ç¢ºèª - ããã³ã ã¦ã£ã³ãã¦ã®åãªã¼ãã³
page.label.confirm.reset.default.logo=ç¢ºèª - ããã©ã«ã ã­ã´ã®ãªã»ãã
page.label.confirm.restore=ç¢ºèª - å¾©å
page.label.confirm.scan.domains=ç¢ºèª - ãã¡ã¤ã³ã®ã¹ã­ã£ã³
page.label.confirm.suppress=ç¢ºèª - éè¡¨ç¤º
page.label.confirm.unhold=ç¢ºèª - ä¿çè§£é¤
page.label.confirm.upload.template=ç¢ºèª - ãã³ãã¬ã¼ãã®ã¢ããã­ã¼ã
page.label.confirm.validate.and.initialize=ç¢ºèª - æ¤è¨¼ã¨åæå
page.label.confirm=ç¢ºèª
page.label.confirmation.required=ç¢ºèªãå¿è¦
page.label.confirmpassword=ãã¹ã¯ã¼ãã®ç¢ºèª
page.label.connected.enabled=æ¥ç¶æ¸ã¿æå¹
page.label.connected.management=æ¥ç¶æ¸ã¿ç®¡ç
page.label.connected=æ¥ç¶æ¸ã¿
page.label.connections=æ¥ç¶
page.label.connector.name=ã³ãã¯ã¿ã¼å
page.label.connector.parameter=ã³ãã¯ã¿ã¼ ãã©ã¡ã¼ã¿ã¼
page.label.connector.string=ã³ãã¯ã¿ã¼æå­å
page.label.connector=ã³ãã¯ã¿ã¼
page.label.connectors.licensed=ã©ã¤ã»ã³ã¹ä»ãã³ãã¯ã¿ã¼
page.label.connectors=ã³ãã¯ã¿ã¼
page.label.consequences=çµæ
page.label.contact.name=é£çµ¡åå
page.label.content.defaults=ã³ã³ãã³ãã®ããã©ã«ã
page.label.content.file=ã³ã³ãã³ã ãã¡ã¤ã«
page.label.content.last.edited=ã³ã³ãã³ãã®æçµç·¨éæ¥æ
page.label.content.library.delivery.type.dependant=ä¾å­
page.label.content.library.delivery.type.independent=ç¬ç«
page.label.content.library.setup=ç»åã©ã¤ãã©ãª ã»ããã¢ãã
page.label.content.library=ç»åã©ã¤ãã©ãª
page.label.content.name=ã³ã³ãã³ãå
page.label.content.search=ã³ã³ãã³ãæ¤ç´¢
page.label.content.selections=ã³ã³ãã³ãã®ããªã¢ã³ã
page.label.content.type=ã³ã³ãã³ã ã¿ã¤ã
page.label.content=ã³ã³ãã³ã
page.label.contentapproval=ã³ã³ãã³ãã®æ¿èª
page.label.continue=ç¶è¡
page.label.contract.all=ãã¹ã¦ç¸®å°
page.label.contract.email=å¥ç´é»å­ã¡ã¼ã«
page.label.controlclick=Ctrl ã­ã¼ãæ¼ããªããã¯ãªãã¯ãã¦é¸æè§£é¤
page.label.consolidate.automatically.content.settings=
page.label.consolidate.automatically.consolidate.all=
page.label.consolidate.automatically.prefix.description=
page.label.consolidate.automatically.minWordCount.prefix=
page.label.consolidate.automatically.minWordCount.suffix=
page.label.consolidate.automatically.content.characters.description=
page.text.consolidate.automatically.description=
page.text.consolidate.automatically.notes=
client_messages.consolidate.automatically.sharedContent.naming.error.title=
client_messages.consolidate.automatically.sharedContent.naming.error.message=
client_messages.consolidate.automatically.minWordCount.error=
page.label.convert=å¤æ
page.label.copy.content.to=ã³ã³ãã³ãã®ã³ãã¼å...
page.label.copy.data.from=ãã¼ã¿ã®ã³ãã¼å...
page.label.corporation=ä¼æ¥­
page.label.count.eight=8
page.label.count.five=5
page.label.count.four=4
page.label.count.nine=9
page.label.count.one=1
page.label.count.seven=7
page.label.count.six=6
page.label.count.ten=10
page.label.count.three=3
page.label.count.two=2
page.label.coverage.analysis=ã«ãã¬ãã¸åæ
page.label.create.new.branch=æ°è¦ãã¡ã¤ã³ã®ä½æ
page.label.create.working.copy=ä½æ¥­ã³ãã¼ã®ä½æ
page.label.create=ä½æ
page.label.created.by=ä½æè
page.label.created.date=ä½ææ¥
page.label.created.on=ä½ææ¥
page.label.created=ä½ææ¸ã¿
page.label.createnew=æ°è¦è¿½å 
page.label.currencysymbol=éè²¨è¨å·
page.label.current.element.name=ç¾å¨ã®è¦ç´ å
page.label.current.status=ç¾å¨ã®ã¹ãã¼ã¿ã¹
page.label.current.view.global=ç¾å¨ã®ãã¥ã¼: ã°ã­ã¼ãã«
page.label.current.view.touchpoint=ç¾å¨ã®ãã¥ã¼: ã¿ãããã¤ã³ã
page.label.currently.varied.by=ç¾å¨ã®å¤åè¦å 
page.label.custom.content=ã«ã¹ã¿ã  ã³ã³ãã³ã
page.label.custom=ã«ã¹ã¿ã 
page.label.customer.driver.input.filename=åä¿¡èã®ãã©ã¤ãã¼å¥åãã¡ã¤ã«å
page.label.customer.email.address.variable=åä¿¡èã®é»å­ã¡ã¼ã« ã¢ãã¬ã¹å¤æ°
page.label.customer.filename=åä¿¡èã®ãã¡ã¤ã«å
page.label.customer.input.files.encoding=åä¿¡èã®å¥åãã¡ã¤ã« ã¨ã³ã³ã¼ãã£ã³ã°
page.label.customer.key.variable=åä¿¡èã®ä¸æã­ã¼å¤æ°
page.label.customer.output.files.encoding=åä¿¡èã®åºåãã¡ã¤ã« ã¨ã³ã³ã¼ãã£ã³ã°
page.label.customer.phone.number.variable=é¡§å®¢ã®é»è©±çªå·å¤æ°
page.label.customer.primary.key=é¡§å®¢ãã©ã¤ããªã­ã¼
page.label.customer.report.details=é¡§å®¢ã¬ãã¼ãè©³ç´°
page.label.customer.report.summary=é¡§å®¢ã¬ãã¼ã ãµããªã¼
page.label.customer=é¡§å®¢
page.label.customerId=é¡§å®¢ ID
page.label.customerfield.first=ç¬¬ 1 é¡§å®¢ãã£ã¼ã«ã
page.label.customerfield.second=ç¬¬ 2 é¡§å®¢ãã£ã¼ã«ã
page.label.customers=é¡§å®¢
page.label.customizations=ã«ã¹ã¿ãã¤ãº
page.label.customize=ã«ã¹ã¿ãã¤ãº
page.label.dark=æ¿è²
page.label.dashed=ç ´ç·
page.label.data.collection.configuration=ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³æ§æ
page.label.data.element=ãã¼ã¿è¦ç´ 
page.label.data.file=ãã¼ã¿ ãã¡ã¤ã«
page.label.data.files=ãã¼ã¿ ãã¡ã¤ã«
page.label.data.group=ãã¼ã¿ ã°ã«ã¼ã
page.label.data.groups=ãã¼ã¿ ã°ã«ã¼ã
page.label.data.resource=ãã¼ã¿ ãªã½ã¼ã¹
page.label.data.resources=ãã¼ã¿ ãªã½ã¼ã¹
page.label.data.source.association.name=ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³å
page.label.data.source.association=ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³
page.label.data.source.associations=ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³
page.label.data.source.name=ãã¼ã¿ ã½ã¼ã¹å
page.label.data.source=ãã¼ã¿ ã½ã¼ã¹
page.label.data=ãã¼ã¿
page.label.dataelement=ãã¼ã¿è¦ç´ .
page.label.dataelements=ãã¼ã¿è¦ç´ 
page.label.datarecords=ãã¼ã¿ ã¬ã³ã¼ã
page.label.datasources=ãã¼ã¿ ã½ã¼ã¹
page.label.datatype=ãã¼ã¿ ã¿ã¤ã
page.label.date.and.time=æ¥ä»ã¨æå»
page.label.date.range=æ¥ä»ã®ç¯å²
page.label.date.selection=æ¥ä»ã®é¸æ
page.label.date=æ¥ä»
page.label.days=æ¥æ°
page.label.daysofweek=ææ¥
page.label.de.server.settings=DE(æææ±ºå®ã¨ã³ã¸ã³) ã®è¨­å®
page.label.deactivate=éã¢ã¯ãã£ãå
page.label.deactive= éã¢ã¯ãã£ã
page.label.debug.mode=åæã¢ã¼ã
page.label.debug=ãããã°
page.label.decimal.places=å°æ°ç¹ä»¥ä¸ã®æ¡æ°
page.label.decimalsymbol=å°æ°ç¹è¨å·
page.label.decisioning.engine.version=æææ±ºå®ã¨ã³ã¸ã³ ãã¼ã¸ã§ã³
page.label.default.content=ããã©ã«ã ã³ã³ãã³ã
page.label.default.instance=ããã©ã«ã ã¤ã³ã¹ã¿ã³ã¹
page.label.default.language=ããã©ã«ãè¨èª
page.label.default.locale.for.language=è¨èªã®ããã©ã«ã ã­ã±ã¼ã«
page.label.default.locale.note=(ããã©ã«ã ã­ã±ã¼ã«)
page.label.default.locale=ããã©ã«ã ã­ã±ã¼ã«
page.label.default.month.name.override=ããã©ã«ãæåã®ä¸æ¸ã
page.label.default.paragraph.style=ããã©ã«ãæ®µè½ã¹ã¿ã¤ã«
page.label.default.rate.sheet=ããã©ã«ãæéè¡¨
page.label.default.schedule.layout=ããã©ã«ã ã¹ã±ã¸ã¥ã¼ã« ã¬ã¤ã¢ã¦ã
page.label.default.text.style=ããã©ã«ã ãã­ã¹ã ã¹ã¿ã¤ã«
page.label.default.thresholds=ããã©ã«ããããå¤
page.label.default.value=ããã©ã«ãå¤
page.label.default.workspace=ããã©ã«ã ã¯ã¼ã¯ã¹ãã¼ã¹
page.label.default=ããã©ã«ã
page.label.defaultdateformat=ããã©ã«ãæ¥ä»å½¢å¼
page.label.defaultvalue=ããã©ã«ãå¤
page.label.defaultworkgroup=ããã©ã«ã ã¯ã¼ã¯ã°ã«ã¼ã
page.label.delete.DEL=åé¤
page.label.delete.archive=ã¢ã¼ã«ã¤ãã®åé¤
page.label.delete.attachment=æ·»ä»ãã¡ã¤ã«ã®åé¤
page.label.delete.constant=å®æ°ã®åé¤
page.label.delete.event=ã¤ãã³ãã®åé¤
page.label.delete.language=è¨èªã®åé¤
page.label.delete.parameter=ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ã®åé¤
page.label.delete.selected.target.group=é¸æããã¿ã¼ã²ãã ã°ã«ã¼ããåé¤ãã¾ãã?
page.label.delete.selected=é¸æåå®¹ã®åé¤
page.label.delete.selection=é¸æåå®¹ã®åé¤
page.label.delete.simulation=ã·ãã¥ã¬ã¼ã·ã§ã³ã®åé¤
page.label.delete.test.scenario=ãã¹ãã®åé¤
page.label.delete.touchpoints=ç¢ºèª - ã¿ãããã¤ã³ãã®åé¤
page.label.delete.variant=ããªã¢ã³ãã®åé¤
page.label.delete.workgroup=ã¯ã¼ã¯ã°ã«ã¼ãã®åé¤
page.label.delete=åé¤
page.label.delimiter=åºåãè¨å·
page.label.delivery.date=éä¿¡æ¥
page.label.delivery.logs=éä¿¡ã­ã°
page.label.delivery.report=éä¿¡ã¬ãã¼ã
page.label.delivery.status=éä¿¡ã¹ãã¼ã¿ã¹
page.label.delivery.type=éä¿¡ã¿ã¤ã
page.label.delivery=åªååº¦è¨­å®
page.label.deliveryevents=éä¿¡ã¤ãã³ã
page.label.description=èª¬æ
page.label.detailed=è©³ç´°
page.label.details=è©³ç´°
page.label.diagnostics.package=è¨ºæ­ããã±ã¼ã¸
page.label.dialogue.output=HP Exstream åºå
page.label.dictionary=ãã£ã¯ã·ã§ããª
page.label.disable.deactivate=ç¡å¹ (éã¢ã¯ãã£ãå)
page.label.disable.offline=ç¡å¹ (ãªãã©ã¤ã³)
page.label.disable=ç¡å¹
page.label.disabled=ç¡å¹
page.label.discard.rate.scheet=æéè¡¨ã®ç ´æ£
page.label.discard.working.copy=ä½æ¥­ã³ãã¼ã®ç ´æ£
page.label.discard=ç ´æ£
page.label.discontinue.rate.sheet=æéè¡¨ã®ä½¿ç¨åæ­¢
page.label.discontinue=åæ­¢
page.label.dispatch.settings=çºéè¨­å®
page.label.display.settings=è¡¨ç¤ºè¨­å®
page.label.display=è¡¨ç¤º
page.label.disqualified=å¤±æ ¼
page.label.distinct.customers=é¡§å®¢ã®åºå
page.label.divider=åºåãç·
page.label.division=åºåã
page.label.document.insert.selector=æ¿å¥ã»ã¬ã¯ã¿ã¼
page.label.document.touchpoint.selections.selector=ã¿ãããã¤ã³ã ããªã¢ã³ã ã»ã¬ã¯ã¿ã¼
page.label.document=ãã­ã¥ã¡ã³ã
page.label.domain=ãã¡ã¤ã³
page.label.domains=ãã¡ã¤ã³
page.label.done=å®äº
page.label.dotted=ç¹ç·
page.label.download.exported.XML=ã¨ã¯ã¹ãã¼ããã XML ã®ãã¦ã³ã­ã¼ã
page.label.download.message.template=ã¡ãã»ã¼ã¸ ãã³ãã¬ã¼ãã®ãã¦ã³ã­ã¼ã
page.label.download.variants=ããªã¢ã³ãã®ãã¦ã³ã­ã¼ã
page.label.download=ãã¦ã³ã­ã¼ã
page.label.due.by=æé
page.label.due.date=æéæ¥
page.label.due=æé
page.label.dxf.output=DXF åºå
page.label.dynamic=åç
page.label.edit.branch.settings=ãã¡ã¤ã³è¨­å®ã®ç·¨é
page.label.edit.constant=å®æ°ã®ç·¨é
page.label.edit.data.element=ãã¼ã¿è¦ç´ ã®ç·¨é
page.label.edit.data.record=ãã¼ã¿ ã¬ã³ã¼ãã®ç·¨é
page.label.edit.data.source=ãã¼ã¿ ã½ã¼ã¹ã®ç·¨é
page.label.edit.target.group=ã¿ã¼ã²ãã ã°ã«ã¼ãã®ç·¨é
page.label.edit.xml.element.attribute.name=å±æ§å
page.label.edit.xml.element.name=è¦ç´ å
page.label.edit.xml.element.value=è¦ç´ å¤
page.label.edit.xml.element=XML ãã¼ã¿è¦ç´ ã®ç·¨é
page.label.edit=ç·¨é
page.label.edited.by=ç·¨éè
page.label.edited=ç·¨éæ¸ã¿
page.label.editworkgroup=ã¯ã¼ã¯ã°ã«ã¼ãã®ç·¨é
page.label.element=è¦ç´ 
page.label.elements.disk=è¦ç´  (ãã£ã¹ã¯)
page.label.elements.memory=è¦ç´  (ã¡ã¢ãª)
page.label.email.notication=ã¯ã¼ã¯ãã­ã¼ãé»å­ã¡ã¼ã«ã§éç¥ãã
page.label.email.server.settings=é»å­ã¡ã¼ã« ãµã¼ãã¼è¨­å®
page.label.email.templates=é»å­ã¡ã¼ã« ãã³ãã¬ã¼ã
page.label.email.web=é»å­ã¡ã¼ã« Web
page.label.email=é»å­ã¡ã¼ã«
page.label.embedded.content.delivery.type.dependant=ä¾å­
page.label.embedded.content.delivery.type.independent=ç¬ç«
page.label.embedded.content.setup=ã¹ãã¼ã ãã­ã¹ã ã»ããã¢ãã
page.label.embedded.content=ã¹ãã¼ã ãã­ã¹ã
page.label.en=è±èª
page.label.enable.activate=æå¹ (ã¢ã¯ãã£ãå)
page.label.enable.for.content=ã³ã³ãã³ãã§ä½¿ç¨ãã
page.label.enable.for.rules=ã«ã¼ã«ã§ä½¿ç¨ãã
page.label.enable.online=æå¹ (ãªã³ã©ã¤ã³)
page.label.enable=æå¹
page.label.enabled=æå¹
page.label.encoding.type=ã¨ã³ã³ã¼ãã£ã³ã° ã¿ã¤ã
page.label.end.date=çµäºæ¥
page.label.end=çµäº
page.label.envelope.name=å°ç­å
page.label.envelope.weight=å°ç­ã®ã¦ã§ã¤ã
page.label.envelope=å°ç­
page.label.error=ã¨ã©ã¼
page.label.es=ã¹ãã¤ã³èª
page.label.evaluations=è©ä¾¡
page.label.evelope.weight=å°ç­ (éé)
page.label.event=ã¤ãã³ã
page.label.exact.match=å®å¨ä¸è´
page.label.exact.target.ant.path=Ant å®è¡ãã¡ã¤ã«ã®å ´æ
page.label.exact.target.build.xml.path=Build XML ã®å ´æ
page.label.exact.target.ftp.host=FTP ãã¹ã
page.label.exact.target.ftp.import.location=FTP ã¤ã³ãã¼ãã®å ´æ
page.label.exact.target.ftp.password=FTP ãã¹ã¯ã¼ã
page.label.exact.target.ftp.user=FTP ã¦ã¼ã¶ã¼
page.label.exact.target.settings=ExactTarget è¨­å®
page.label.exact.target.web.service.file.location=Web ãµã¼ãã¹ ãã¡ã¤ã«ã®å ´æ
page.label.exact.target.web.service.password=Web ãµã¼ãã¹ ãã¹ã¯ã¼ã
page.label.exact.target.web.service.user=Web ãµã¼ãã¹ ã¦ã¼ã¶ã¼
page.label.exact.target.wsdl.location=WSDL ã®å ´æ
page.label.exclude.customers=é¡§å®¢ã®é¤å¤
page.label.exclude=é¤å¤
page.label.execute.incloud.preview=ã¯ã©ã¦ãã§ãã¬ãã¥ã¼ãå®è¡ãã
page.label.execute.incloud.proof=ã¯ã©ã¦ãã§æ ¡æ­£ãå®è¡ãã
page.label.execute.incloud.test=ã¯ã©ã¦ãã§ãã¹ããå®è¡ãã
page.label.execute.test=ãã¹ãã®å®è¡
page.label.expand.all=ãã¹ã¦å±é
page.label.export.CSV.data=CSV ãã¼ã¿ã®ã¨ã¯ã¹ãã¼ã
page.label.export.report=ã¬ãã¼ãã®ã¨ã¯ã¹ãã¼ã
page.label.export.touchpoint=ã¿ãããã¤ã³ãã®ã¨ã¯ã¹ãã¼ã
page.label.export.variants=ããªã¢ã³ãã®ã¨ã¯ã¹ãã¼ã
page.label.export=ã¨ã¯ã¹ãã¼ã
page.label.external.domain=å¤é¨ãã¡ã¤ã³
page.label.external.event.key=ã¤ãã³ã ã­ã¼
page.label.external.event.none=ã·ã¹ãã åã«å¤é¨ã¤ãã³ããããã¾ãã
page.label.external.event=å¤é¨ã¤ãã³ã
page.label.external.events=å¤é¨ã¤ãã³ã
page.label.external.reporting.data=å¤é¨ã¬ãã¼ã ãã¼ã¿
page.label.external.sso=å¤é¨ SSO
page.label.externalID=å¤é¨ ID
page.label.externalid=å¤é¨ ID
page.label.failed=å¤±æ
page.label.false=False
page.label.favourite=ãæ°ã«å¥ã
page.label.feature.activation=æ©è½ã®ã¢ã¯ãã£ãå
page.label.features=æ©è½
page.label.file.name=ãã¡ã¤ã«å
page.label.file=ãã¡ã¤ã«
page.label.filter.customers=é¡§å®¢ã®çµãè¾¼ã¿
page.label.filter=ãã£ã«ã¿ã¼
page.label.find=æ¤ç´¢
page.label.first.page.weight=æåã®ãã¼ã¸ã®éé
page.label.firstname=å
page.label.fiscal.year.start.month=å¹´åº¦éå§æ
page.label.fivesecs=5 ç§
page.label.fixed.record.count=åºå®ã¬ã³ã¼ãæ°
page.label.folder.favorites=ãæ°ã«å¥ã
page.label.folder.last.visited=æçµè¡¨ç¤ºæ¥æ
page.label.folder.locations=ãã©ã«ãã¼ã®å ´æ
page.label.folder.overview=ãã©ã«ãã¼ã®æ¦è¦
page.label.font.name=ãã©ã³ãå
page.label.font.size=ãã©ã³ã ãµã¤ãº
page.label.font=ãã©ã³ã
page.label.forget.password.pending=ãã¹ã¯ã¼ããå¿ããå ´åä¿çä¸­
page.label.forms=ãã©ã¼ã 
page.label.fr=ãã©ã³ã¹èª
page.label.freeform=ããªã¼ãã©ã¼ã 
page.label.frequency=é »åº¦
page.label.friendly.name=ãã¬ã³ããªå
page.label.friendlyname=ãã¬ã³ããªå
page.label.from.date=éå§æ¥
page.label.from.path=åã®ãã¹
page.label.from=éå§
page.label.ftp.location=FTP ã¢ããã­ã¼ãå ´æ/ãã¹
page.label.ftp.server=FTP ãµã¼ãã¼
page.label.ftp1.settings=FTP 1 è¨­å®
page.label.ftp2.settings=FTP 2 è¨­å®
page.label.ftp3.settings=FTP 3 è¨­å®
page.label.full.job.logs=ãã¹ã¦ã®ã¸ã§ãã®ã­ã°
page.label.full=ãã¹ã¦
page.label.general.settings=ä¸è¬è¨­å®
page.label.general.specification=ä¸è¬æå®
page.label.general=ä¸è¬
page.label.generalerror=ä¸è¬ã¨ã©ã¼
page.label.generate.key=ã­ã¼ã®çæ
page.label.generate.message.report=ã¡ãã»ã¼ã¸ ã¬ãã¼ãã®çæ
page.label.generate.report=ã¬ãã¼ãã®çæ
page.label.generate.reports=å®è¡
page.label.generate.touchpoint.report=ã¿ãããã¤ã³ã ã¬ãã¼ãã®çæ
page.label.generated.by=çæè
page.label.generated=çææ¸ã¿
page.label.generic=ä¸è¬
page.label.global.all.touchpoints=ã°ã­ã¼ãã«: ãã¹ã¦ã®ã¿ãããã¤ã³ã
page.label.global.folders=ã°ã­ã¼ãã« ãã©ã«ãã¼
page.label.global=ã°ã­ã¼ãã«
page.label.graphic=ã°ã©ãã£ãã¯
page.label.group.level=ã°ã«ã¼ã ã¬ãã«
page.label.group=ã°ã«ã¼ã
page.label.guid=ä¸æè­å¥å­ (GUID)
page.label.hanging=ã¶ãä¸ã
page.label.header.description=ãããã¼èª¬æ
page.label.header.logo.filename=ãããã¼ ã­ã´ã®ãã¡ã¤ã«å
page.label.header.logo.filepath=ãããã¼ ã­ã´ã®å®å¨ãã¡ã¤ã« ãã¹ã¨åå
page.label.header.logo=ãããã¼ ã­ã´
page.label.header.records=ãããã¼ ã¬ã³ã¼ã
page.label.header.settings=ãããã¼è¨­å®
page.label.header.theme.type.black=æ¿è²
page.label.header.theme.type.white=æ·¡è²
page.label.header.theme.type=ã­ã´ã®èæ¯
page.label.health.check=ç¶æç¢ºèª
page.label.health.checks=ç¶æç¢ºèª
page.label.height=é«ã
page.label.hibernatecachemonitor=Hibernate ã­ã£ãã·ã¥ ã¢ãã¿ã¼
page.label.hidden.user=éè¡¨ç¤ºã¦ã¼ã¶ã¼
page.label.hierarchy=éå±¤
page.label.history=å±¥æ­´
page.label.hits=ãããæ°
page.label.hold=ä¿ç
page.label.hour=æé
page.label.id=ID
page.label.image=ç»å
page.label.import.complete=ã¤ã³ãã¼ãå®äº
page.label.import.dot=ã¤ã³ãã¼ã...
page.label.import.lookup.values=åç§å¤ã®ã¤ã³ãã¼ã
page.label.import.only.data.collection=ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³ã®ã¿ã®ã¤ã³ãã¼ã
page.label.import=ã¤ã³ãã¼ã
page.label.importing.image=ç»åã®ã¤ã³ãã¼ãä¸­
page.label.in.process.items.included=å«ã¾ããä½æ¥­ã³ãã¼
page.label.in.process.items.to.include=å«ããä½æ¥­ã³ãã¼
page.label.in.process.message.details=ä½æ¥­ã³ãã¼ ã¡ãã»ã¼ã¸ ã¬ãã¼ãã®è©³ç´°
page.label.in.process=ä½æ¥­ã³ãã¼
page.label.in=å ´æ
page.label.inactive.tag=éã¢ã¯ãã£ã ã¿ã°
page.label.inactive=éã¢ã¯ãã£ã
page.label.inches=ã¤ã³ã
page.label.include.content=ã³ã³ãã³ããå«ãã
page.label.include.groups=ã°ã«ã¼ããå«ãã
page.label.include.headers=ãããã¼ãå«ãã
page.label.include.insert.targeting=æ¿å¥ã®ã¿ã¼ã²ããè¨­å®ãå«ãã
page.label.include.targeting=ã¿ã¼ã²ããè¨­å®ãå«ãã
page.label.include=å«ãã
page.label.incomplete=ä¸å®å¨
page.label.indefinite=ä¸å®
page.label.indentation=ã¤ã³ãã³ã
page.label.indicator.location=ã¤ã³ã¸ã±ã¼ã¿ã¼ã®å ´æ
page.label.indicator=ã¤ã³ã¸ã±ã¼ã¿ã¼
page.label.info=æå ±
page.label.inherited=ç¶æ¿
page.label.input.filter=å¥åãã£ã«ã¿ã¼
page.label.insert.ID=ID
page.label.insert.as.paragraph=æ®µè½ã¨ãã¦æ¿å¥ãã
page.label.insert.assignment=æ¿å¥ã®å²ãå½ã¦
page.label.insert.associations=æ¿å¥ã®é¢é£ä»ã
page.label.insert.deleted=(æ¿å¥ãåé¤ããã¾ãã)
page.label.insert.delivery.details.customers=æ¿å¥ééã®è©³ç´° - é¡§å®¢ ID é 
page.label.insert.delivery.ordered.customer=æ¿å¥ééã®è©³ç´° - é¡§å®¢ ID é 
page.label.insert.delivery.summary=æ¿å¥ééã¬ãã¼ã
page.label.insert.delivery.summery.customers=æ¿å¥ééãµããªã¼ - é¡§å®¢å¥
page.label.insert.delivery.type.mandatory=å¿é 
page.label.insert.delivery.type.non.selectable=é¸æå¯è½ã§ãªã
page.label.insert.delivery.type.optional=ãªãã·ã§ã³
page.label.insert.end.date=æ¿å¥ã®çµäºæ¥
page.label.insert.management=æ¿å¥ã®ç®¡ç
page.label.insert.message=æ¿å¥ã¡ãã»ã¼ã¸
page.label.insert.name=æ¿å¥å
page.label.insert.recipients=æ¿å¥åä¿¡è
page.label.insert.report.details=æ¿å¥ã¬ãã¼ãã®è©³ç´°
page.label.insert.report.summary=æ¿å¥ã¬ãã¼ã ãµããªã¼
page.label.insert.report=æ¿å¥ã¬ãã¼ãã®è©³ç´°
page.label.insert.schedule.setup=æ¿å¥ã¹ã±ã¸ã¥ã¼ã« ã»ããã¢ãã
page.label.insert.schedule=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«
page.label.insert.schedules=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«
page.label.insert.search=æ¤ç´¢
page.label.insert.settings=æ¿å¥è¨­å®
page.label.insert.start.date=æ¿å¥éå§æ¥
page.label.insert.timing=æ¿å¥ã¿ã¤ãã³ã°
page.label.insert=æ¿å¥
page.label.inserts.enabled=æ¿å¥ãæå¹ã«ãã
page.label.inserts=æ¿å¥
page.label.instance=ã¤ã³ã¹ã¿ã³ã¹
page.label.instances=ã¤ã³ã¹ã¿ã³ã¹
page.label.instructions=æé 
page.label.interactive.zone=æ¥ç¶æ¸ã¿ã¾ã¼ã³
page.label.interactive=æ¥ç¶æ¸ã¿
page.label.internal.pod.db.ip=åé¨ããã DB IP
page.label.internal.umh.ip=åé¨ UMH IP
page.label.invalid.proofing.data=æ ¡æ­£ä¸­ã®ãã¼ã¿ãç¡å¹ã§ã
page.label.invalidsigninattemps=ç¡å¹ãªãµã¤ã³ã¤ã³åæ°
page.label.is.master.admin=ãã¹ã¿ã¼ç®¡çè
page.label.is.master.user=ãã¹ã¿ã¼ ã¦ã¼ã¶ã¼
page.label.italic=æä½
page.label.item=ã¢ã¤ãã 
page.label.items.included=å«ã¾ããã¢ã¤ãã 
page.label.items.to.include.in.reports=ã¬ãã¼ãã«å«ããã¢ã¤ãã 
page.label.items=ã¢ã¤ãã 
page.label.job.folders=ã¸ã§ã ãã©ã«ãã¼
page.label.job=ã¸ã§ã
page.label.key.end_date=END_DATE
page.label.key.end_date_time=END_DATE_TIME
page.label.key.end_time=END_TIME
page.label.key.start_date=START_DATE
page.label.key.start_date_time=START_DATE_TIME
page.label.key.start_time=START_TIME
page.label.key.time_delta=TIME_DELTA
page.label.key.time_zone=TIME_ZONE
page.label.keywords=ã­ã¼ã¯ã¼ã
page.label.label=ã©ãã«
page.label.landscape=æ¨ª (11 x 8.5)
page.label.language.code=è¨èªã³ã¼ã
page.label.language.management=è¨èªç®¡ç
page.label.language.selections=è¨èªã®é¸æ
page.label.language=è¨èª
page.label.languages=è¨èª
page.label.last.date.ran=æçµå®è¡æ¥
page.label.last.edited.by=æçµç·¨éè
page.label.last.edited=æçµç·¨éæ¥æ
page.label.last.modified.by=æçµå¤æ´è
page.label.last.modified=æçµå¤æ´æ¥æ
page.label.last.preview=æå¾ã®ãã¬ãã¥ã¼
page.label.last.proof=æå¾ã®æ ¡æ­£
page.label.last=å§
page.label.lastname=å§
page.label.latest.preview=ææ°ãã¬ãã¥ã¼
page.label.latest.proof=ææ°æ ¡æ­£
page.label.layout.manager=ã¬ã¤ã¢ã¦ã ããã¼ã¸ã£ã¼
page.label.layout=ã¬ã¤ã¢ã¦ã
page.label.layouttype=ã¬ã¤ã¢ã¦ã ã¿ã¤ã
page.label.leave.empty=ç©ºã®ã¾ã¾ã«ãã
page.label.left=å·¦
page.label.legal_landscape=ãªã¼ã¬ã«æ¨ª (14 x 8.5)
page.label.legal_portrait=ãªã¼ã¬ã«ç¸¦ (8.5 x 14)
page.label.length=é·ã
page.label.level=ã¬ãã«
page.label.licence.information=ã©ã¤ã»ã³ã¹æå ±
page.label.licence.management=ã©ã¤ã»ã³ã¹ç®¡ç
page.label.licence.manager=ã©ã¤ã»ã³ã¹ ããã¼ã¸ã£ã¼
page.label.licence=ã©ã¤ã»ã³ã¹
page.label.licences=ã©ã¤ã»ã³ã¹
page.label.license.batch_dialogue=HP Exstream
page.label.license.batch_gmc=GMC Inspire
page.label.license.clickatell=Clickatell
page.label.license.emessaging_email=e-Messaging é»å­ã¡ã¼ã«
page.label.license.emessaging_sms=e-Messaging SMS
page.label.license.exacttarget=ExactTarget
page.label.license.ftpweb=FTP Web
page.label.license.information=ã©ã¤ã»ã³ã¹æå ±
page.label.license.insertmanagement_support=æ¿å¥ç®¡çãµãã¼ã
page.label.license.msg_mgmt_workflow=ã¡ãã»ã¼ã¸ã®ç®¡çã¨ã¯ã¼ã¯ãã­ã¼ (ã³ã¢)
page.label.license.msg_reporting_analytics=ã¡ãã»ã¼ã¸ã®ã¬ãã¼ãã¨åæ (ã³ã¢)
page.label.license.native=ãã¤ãã£ã ã³ã³ãã¸ã·ã§ã³
page.label.license.provisions=ã©ã¤ã»ã³ã¹æ¡é 
page.label.license.sendmail=Sendmail
page.label.license.simulation_support=ã·ãã¥ã¬ã¼ã·ã§ã³ ãµãã¼ã
page.label.license.tm=TM
page.label.license.workgroup_support=ã¯ã¼ã¯ã°ã«ã¼ã ãµãã¼ã (ã³ã¢)
page.label.light=æ·¡è²
page.label.limit.available=ä½¿ç¨å¯è½ãªä¸é
page.label.limit.global.description=* ã¼ã­ã¯ã©ã¤ã»ã³ã¹ããä½¿ç¨å¯è½ãªä¸éã(ãã¤ã§ãå¤æ´å¯è½) ã¾ã§å¢å ã§ãããã¨ãæå³ãã¾ã  
page.label.limit.global=äºç´æ¸ã¿ãªãã·ã§ã³*
page.label.limit.local=ã­ã¼ã«ã« ãã¡ã¤ã³
page.label.limit.subdomains=ãµããã¡ã¤ã³
page.label.line.spacing=è¡é
page.label.link=ãªã³ã¯
page.label.list.filter.my.working.copies=ãã¤ä½æ¥­ã³ãã¼
page.label.list.filter.type.active=ã¢ã¯ãã£ã
page.label.list.filter.type.all=ãã¹ã¦
page.label.list.filter.type.any.status=ä»»æã®ã¹ãã¼ã¿ã¹
page.label.list.filter.type.any=ãããã
page.label.list.filter.type.archived=ã¢ã¼ã«ã¤ãæ¸ã¿
page.label.list.filter.type.batch=ããã
page.label.list.filter.type.changed=å¤æ´æ¸ã¿
page.label.list.filter.type.complete=å®äº
page.label.list.filter.type.custom=ã«ã¹ã¿ã 
page.label.list.filter.type.error=ã¨ã©ã¼
page.label.list.filter.type.global=ã°ã­ã¼ãã«
page.label.list.filter.type.inactive=éã¢ã¯ãã£ã
page.label.list.filter.type.interactive=æ¥ç¶æ¸ã¿
page.label.list.filter.type.message.delivery=ã¡ãã»ã¼ã¸éä¿¡
page.label.list.filter.type.message=ã¡ãã»ã¼ã¸
page.label.list.filter.type.my.active=ãã¤ ã¢ã¯ãã£ã
page.label.list.filter.type.my.inactive=ãã¤éã¢ã¯ãã£ã
page.label.list.filter.type.my=ãã¤
page.label.list.filter.type.referenced=åç§ã®ã¿
page.label.list.filter.type.sap=ç¶æ¿
page.label.list.filter.type.setup=ã»ããã¢ãã
page.label.list.filter.type.suppress=éè¡¨ç¤º
page.label.list.filter.type.touchpoint.delivery=ã¿ãããã¤ã³ãéä¿¡
page.label.list.filter.type.unchanged=å¤æ´ãªã
page.label.list.filter.type.unreferenced=æªåç§ã®ã¿
page.label.list.filter.type.variant=ããªã¢ã³ã
page.label.list.filter.type.working.copy=ä½æ¥­ã³ãã¼
page.label.list=ãªã¹ã
page.label.local.number.plus.subdomains=ã­ã¼ã«ã« ãã¡ã¤ã³çªå· + ãµããã¡ã¤ã³çªå· 
page.label.local.number=ã­ã¼ã«ã« ãã¡ã¤ã³çªå·
page.label.local.productions.number=ã­ã¼ã«ã« ãã¡ã¤ã³ ãã­ãã¯ã·ã§ã³çªå· 
page.label.local=ã­ã¼ã«ã«
page.label.locale.settings=ã­ã±ã¼ã«è¨­å®
page.label.locale=ã­ã±ã¼ã«
page.label.location=å ´æ/ãã¹
page.label.lock.out.after=ã­ãã¯ã¢ã¦ãããã¾ã§ã®åæ°
page.label.log.message=ã­ã° ã¡ãã»ã¼ã¸
page.label.login.theme=ã­ã°ã¤ã³ ãã¼ã
page.label.logs=ã­ã°
page.label.long.month.name=æã®åå
page.label.long.month.names=æã®åå
page.label.mailing.weight=éµä¾¿ç©ã®éé
page.label.main.folders=ã¡ã¤ã³ ãã©ã«ãã¼
page.label.maintenance.edit=ã¡ã³ããã³ã¹è¨­å®ã®ç·¨é
page.label.maintenance.settings=ã¡ã³ããã³ã¹è¨­å®
page.label.maintenance.view=ã¡ã³ããã³ã¹è¨­å®ã®è¡¨ç¤º
page.label.maintenance=ã¡ã³ããã³ã¹
page.label.make.default=ããã©ã«ãã«ãã
page.label.mandatory=å¿é 
page.label.manual=æå
page.label.map.domains=ãã¡ã¤ã³ã®ãããã³ã°
page.label.map.to.data.source=ãã¼ã¿ ã½ã¼ã¹ã¸ã®ãããã³ã°
page.label.map.variables=å¤æ°ã®ãããã³ã°
page.label.mask=ãã¹ã¯
page.label.master.admin.branch=ãã¹ã¿ã¼ç®¡çè: ãã¡ã¤ã³
page.label.master.admin.licences=ãã¹ã¿ã¼ç®¡çè: ã©ã¤ã»ã³ã¹
page.label.master.admin.maintenance=ãã¹ã¿ã¼ç®¡çè: ã¡ã³ããã³ã¹
page.label.master.admin.system.settings=ãã¹ã¿ã¼ç®¡çè: ã·ã¹ãã è¨­å®
page.label.master.admin.users=ãã¹ã¿ã¼ç®¡çè: ã¦ã¼ã¶ã¼
page.label.master.bin.layout=ãã¹ã¿ã¼ ãã³ ã¬ã¤ã¢ã¦ã
page.label.master.schema.name=ãã¹ã¿ã¼ ã¹ã­ã¼ãå
page.label.master.unified.login=ãã¹ã¿ã¼ç®¡çè: çµ±åã­ã°ã¤ã³
page.label.master=ãã¹ã¿ã¼
page.label.max.length=æå¤§é·
page.label.maximum.available.number=ä½¿ç¨å¯è½ãªæå¤§æ°
page.label.maximum.length=æå¤§é·
page.label.maximum=æå¤§
page.label.message.associations=ã¡ãã»ã¼ã¸ã®é¢é£ä»ã
page.label.message.content=ã¡ãã»ã¼ã¸ ã³ã³ãã³ã
page.label.message.deleted=(ã¡ãã»ã¼ã¸ãåé¤ããã¾ãã)
page.label.message.delivery.ordered.customer=ã¡ãã»ã¼ã¸éä¿¡ã®è©³ç´° - é¡§å®¢ ID é 
page.label.message.delivery.report=ã¡ãã»ã¼ã¸éä¿¡ã¬ãã¼ã
page.label.message.delivery.summary.customer.id=ã¡ãã»ã¼ã¸éä¿¡ãµããªã¼ - é¡§å®¢ ID
page.label.message.delivery.summary.customers=ã¡ãã»ã¼ã¸éä¿¡ãµããªã¼-é¡§å®¢å¥
page.label.message.filter.all=ãã¹ã¦
page.label.message.filter.archived"=ãã¹ã¦ã®ã¢ã¼ã«ã¤ãæ¸ã¿ã¡ãã»ã¼ã¸
page.label.message.filter.archived=ã¢ã¼ã«ã¤ãæ¸ã¿
page.label.message.filter.my.working.copies=ãã¤ä½æ¥­ã³ãã¼
page.label.message.filter.working.copies=ä½æ¥­ã³ãã¼
page.label.message.formatting.defaults=ã¡ãã»ã¼ã¸æ¸å¼è¨­å®ã®ããã©ã«ã
page.label.message.formatting.overwrite.defaults=ã¡ãã»ã¼ã¸æ¸å¼è¨­å®ã®ä¸æ¸ãå¤
page.label.message.name=ã¡ãã»ã¼ã¸å
page.label.message.priority=ã¡ãã»ã¼ã¸åªååº¦
page.label.message.report.details=ã¡ãã»ã¼ã¸ ã¬ãã¼ãã®è©³ç´°
page.label.message.report.summary=ã¡ãã»ã¼ã¸ ã¬ãã¼ã ãµããªã¼
page.label.message.report.tool=ã¡ãã»ã¼ã¸ ã¬ãã¼ã ãã¼ã«
page.label.message.targeting.analysis=ã¡ãã»ã¼ã¸ ã¿ã¼ã²ããè¨­å®åæ
page.label.message.targeting=ã¡ãã»ã¼ã¸ ã¿ã¼ã²ããè¨­å®
page.label.message=ã¡ãã»ã¼ã¸
page.label.messagepoint.application.error=Messagepoint: ã¢ããªã±ã¼ã·ã§ã³ ã¨ã©ã¼
page.label.messagepoint.hp=HP Exstream Messagepoint
page.label.messagepoint=Messagepoint
page.label.messages=ã¡ãã»ã¼ã¸
page.label.metatags=ã¡ã¿ã¿ã°
page.label.migrate.settings=è¨­å®ã®ç§»è¡
page.label.minimum.length=æå°é·
page.label.misses=ãã¹
page.label.mixed=æ··å¨
page.label.modified=å¤æ´æ¸ã¿
page.label.modify.language=è¨èªã®å¤æ´
page.label.modify.selector.values=ã»ã¬ã¯ã¿ã¼å¤ã®å¤æ´
page.label.modules.licensed=ã©ã¤ã»ã³ã¹ä»ãã¢ã¸ã¥ã¼ã«
page.label.month.april=4 æ
page.label.month.august=8 æ
page.label.month.december=12 æ
page.label.month.february=2 æ
page.label.month.january=1 æ
page.label.month.july=7 æ
page.label.month.june=6 æ
page.label.month.march=3 æ
page.label.month.may=5 æ
page.label.month.november=11 æ
page.label.month.october=10 æ
page.label.month.september=9 æ
page.label.month=æ
page.label.more=å¢å 
page.label.move.from.zone=ã¾ã¼ã³ããç§»å
page.label.move.messages.to=ã¡ãã»ã¼ã¸ã®ç§»å...
page.label.move.to.zone=ã¾ã¼ã³ã¸ç§»å
page.label.move=ç§»å
page.label.multipart.content.category=å±æã³ã³ãã³ã ã«ãã´ãª
page.label.multipart.content=å±æã³ã³ãã³ã
page.label.multipart.message.content=è¤æ°ãã¼ã ã¡ãã»ã¼ã¸ ã³ã³ãã³ã
page.label.multipart=è¤æ°ãã¼ã
page.label.multiple=è¤æ°
page.label.my.shared.content=ãã¤å±æã³ã³ãã³ã
page.label.my.tasks.by.due.date=ãã¤ ã¿ã¹ã¯ (æéæ¥å¥)
page.label.my.tasks=ãã¤ ã¿ã¹ã¯
page.label.mysettings=ãã¤è¨­å®
page.label.na=N/A
page.label.name=åå
page.label.native=ãã¤ãã£ã
page.label.new.condition=æ°è¦æ¡ä»¶
page.label.new.content=æ°è¦ã³ã³ãã³ã
page.label.new.message=æ°è¦ã¡ãã»ã¼ã¸
page.label.new.password=æ°è¦ãã¹ã¯ã¼ã
page.label.new.value=æ°ããå¤
page.label.new.variant=æ°è¦ããªã¢ã³ã
page.label.new=æ°è¦
page.label.next.action=æ¬¡ã®ã¢ã¯ã·ã§ã³
page.label.next=æ¬¡ã¸
page.label.no.active.copy=ã¢ã¯ãã£ã ã³ãã¼ãªã
page.label.no.active.proofs=ã¢ã¯ãã£ããªæ ¡æ­£ãªã
page.label.no.available.touchpoints=ä½¿ç¨å¯è½ãªã¿ãããã¤ã³ããªã
page.label.no.border=æ ç·ãªã
page.label.no.category=ã«ãã´ãªãªã
page.label.no.collection.filter=ã³ã¬ã¯ã·ã§ã³ ãã£ã«ã¿ã¼ãªã
page.label.no.content.selector=ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ãªã
page.label.no.customers.report=ã¬ãã¼ãå¯¾è±¡ã®é¡§å®¢ãªã
page.label.no.default=ããã©ã«ããªã
page.label.no.due.date=æéæ¥ãªã
page.label.no.insert.schedules=ä½¿ç¨ããããã¼ã¿ã«ãã£ã¦é¸æãããæ¿å¥ã¹ã±ã¸ã¥ã¼ã«ãªã
page.label.no.inserts.assigned=å²ãå½ã¦ãããæ¿å¥ãªã
page.label.no.label=ã©ãã«ãããã¾ãã
page.label.no.owner=ææèãããã¾ãã
page.label.no.previews=ãã¬ãã¥ã¼ãããã¾ãã
page.label.no.primary.data.source=ä½¿ç¨å¯è½ãªãã©ã¤ããª ãã¼ã¿ ã½ã¼ã¹ãããã¾ãã
page.label.no.selected.inserts=é¸æããæ¿å¥ãªã
page.label.no.selection=é¸æé ç®ãããã¾ãã
page.label.no.short=ããã
page.label.no.touchpoint.association=ã¿ãããã¤ã³ãã®é¢é£ä»ããããã¾ãã
page.label.no.touchpoints=ã¿ãããã¤ã³ããããã¾ãã
page.label.no.working.copy=ä½æ¥­ã³ãã¼ãããã¾ãã
page.label.no=ããã
page.label.node.name=ã¤ã³ã¹ã¿ã³ã¹å
page.label.node.settings=ã¤ã³ã¹ã¿ã³ã¹è¨­å®
page.label.node.status.cannot.open.db.schema=DB ã¹ã­ã¼ããéããã¨ãã§ãã¾ãã
page.label.node.status.db.schema.empty=DB ã¹ã­ã¼ããç©ºã§ã
page.label.node.status.incorrect.version=ãã¼ã¸ã§ã³ãæ­£ããããã¾ãã
page.label.node.status.initialization.error=åæåã¨ã©ã¼
page.label.node.status.initialization.in.progress=åæåä¸­
page.label.node.status.migration.error=ç§»è¡ã¨ã©ã¼
page.label.node.status.migration.in.progress=ç§»è¡ä¸­
page.label.node.status.not.validated=æªæ¤è¨¼
page.label.node.status.off.line=ãªãã©ã¤ã³
page.label.node.status.on.line=ãªã³ã©ã¤ã³
page.label.node.status.unknown.database.error=ä¸æãªãã¼ã¿ãã¼ã¹ ã¨ã©ã¼
page.label.node.status.unknown=ä¸æ
page.label.node.status=ã¤ã³ã¹ã¿ã³ã¹ã®ã¹ãã¼ã¿ã¹
page.label.node.type=ã¤ã³ã¹ã¿ã³ã¹ ã¿ã¤ã
page.label.node=ã¤ã³ã¹ã¿ã³ã¹
page.label.nodes=ã¤ã³ã¹ã¿ã³ã¹
page.label.none=ãªã
page.label.not.applied=æªé©ç¨
page.label.not.specified=æªæå®
page.label.not.tracked=è¿½è·¡ãªã
page.label.note=æ³¨
page.label.notes=æ³¨
page.label.number.content.library=ç»åã©ã¤ãã©ãª ã¢ã¤ãã ã®æ°
page.label.number.embedded.content=ã¹ãã¼ã ãã­ã¹ãã®æ°
page.label.number.of.bins=ãã³ã®æ°
page.label.number.of.copies=ã³ãã¼æ°
page.label.number.of.prompts=ãã­ã³ããã®æ°
page.label.number.regular.msg=éçã¡ãã»ã¼ã¸ã®æ°
page.label.number.sections=ã»ã¯ã·ã§ã³ã®æ°
page.label.number.selectable.msg=åçã¡ãã»ã¼ã¸ã®æ°
page.label.number.tp.selections=ããªã¢ã³ãã®æ°
page.label.number.tpcs.msg=æ§é åãããã¡ãã»ã¼ã¸ã®æ°
page.label.number.tpms.msg=ãã¬ã­ã·ãã« ã¡ãã»ã¼ã¸ã®æ°
page.label.number.zones=ã¾ã¼ã³ã®æ°
page.label.number=æ°
page.label.numberofdecimals=å°æ°ç¹ä»¥ä¸ã®æ¡æ°
page.label.numeric=æ°å¤
page.label.occurrences=åæ°
page.label.of=/
page.label.off=ãªã
page.label.ok=OK
page.label.on=ãªã³
page.label.one.based=1 ãã¼ã¹
page.label.online=ãªã³ã©ã¤ã³
page.label.open=ãªã¼ãã³
page.label.optimized=æé©åæ¸ã¿
page.label.optional.inserts=ãªãã·ã§ã³ã®æ¿å¥
page.label.optional=ãªãã·ã§ã³
page.label.or=ã¾ãã¯
page.label.order.entry.enabled=è¦æ±èæå¹
page.label.order.entry.setup=è¦æ±èã»ããã¢ãã
page.label.order.entry.type.date.day.month.year=æ¥ä» (æ¥/æ/å¹´)
page.label.order.entry.type.date.month.year=æ¥ä» (æ/å¹´)
page.label.order.entry.type.select.menu=ã¡ãã¥ã¼
page.label.order.entry.type.text=ãã­ã¹ã
page.label.order.entry.type.textarea=ãã­ã¹ã (å¤§)
page.label.order.entry.type.web.service.menu=ã¡ãã¥ã¼ (Web ãµã¼ãã¹ã®å¤)
page.label.order.entry=è¦æ±è
page.label.order=é åº
page.label.other.customer.reporting.variable.a=é¡§å®¢ã¬ãã¼ãå¤æ° A
page.label.other.customer.reporting.variable.b=é¡§å®¢ã¬ãã¼ãå¤æ° B
page.label.other.page.weights=ä»ã®ãã¼ã¸ã®éé
page.label.output=åºå
page.label.override.default=ããã©ã«ããä¸æ¸ããã
page.label.override.remote.server.settings=ãªã¢ã¼ã ãµã¼ãã¼è¨­å®ãä¸æ¸ããã
page.label.overview.approval=æ¦è¦ã®æ¿èª
page.label.overview=æ¦è¦
page.label.overwritedateformat=æ¥ä»ã®å½¢å¼
page.label.package.file.PUB=ããã±ã¼ã¸ ãã¡ã¤ã« (PUB)
page.label.packaged=ããã±ã¼ã¸æ¸ã¿
page.label.page.weight.first.other=ãã¼ã¸ã®éé (æå/ãã®ä»)
page.label.page=ãã¼ã¸
page.label.pages=ãã¼ã¸
page.label.paragraph.alignment.center=ä¸­å¤®
page.label.paragraph.alignment.full=ä¸¡ç«¯æã
page.label.paragraph.alignment.left=å·¦
page.label.paragraph.alignment.right=å³
page.label.paragraph.spacing=æ®µè½ã®éé
page.label.paragraph.style.customization=æ®µè½ã¹ã¿ã¤ã«ã®ã«ã¹ã¿ãã¤ãº
page.label.paragraph.style=æ®µè½ã¹ã¿ã¤ã«
page.label.paragraph.styles=æ®µè½ã¹ã¿ã¤ã«
page.label.paragraph=æ®µè½
page.label.parameter.group=ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ ã°ã«ã¼ã
page.label.parameter.groups=ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ ã°ã«ã¼ã
page.label.parameter=ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼
page.label.parameterdetails=ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼è©³ç´°
page.label.parameteroverview=ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼æ¦è¦
page.label.parameters=ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼
page.label.parent=è¦ª
page.label.part.content=ã³ã³ãã³ã
page.label.part=ãã¼ã
page.label.parts=ãã¼ã
page.label.passed=åæ ¼
page.label.password.properties=ãã¹ã¯ã¼ãã®ãã­ããã£
page.label.password.settings=ãã¹ã¯ã¼ãè¨­å®
page.label.password.special.avoid.repeats=åãæå­ã®é£ç¶ãä½¿ç¨ä¸å¯ã«ãã
page.label.password.special.lowercase=å°æå­ãå¿é ã«ãã
page.label.password.special.numeral=æ°å­ãå¿é ã«ãã
page.label.password.special.symbol=ç¹æ®è¨å·ãå¿é ã«ãã
page.label.password.special.uppercase=å¤§æå­ãå¿é ã«ãã
page.label.password=ãã¹ã¯ã¼ã
page.label.pending=ä¿çä¸­
page.label.percentage=å²å
page.label.personal.settings=åäººè¨­å®
page.label.personal=åäººç¨
page.label.phone=é»è©±
page.label.ping.sso.init.url=Ping SSO åæå URL
page.label.ping.sso.rest.api.client.id=Ping REST API ã¯ã©ã¤ã¢ã³ã ID
page.label.ping.sso.rest.api.client.key=Ping REST API ã¯ã©ã¤ã¢ã³ã ã·ã¼ã¯ã¬ãã
page.label.ping.sso.saas.id=Ping SaaS ID
page.label.ping.sso.settings=Ping SSO è¨­å®
page.label.ping.sso.token.resolution.url=Ping SSO ãã¼ã¯ã³ URL
page.label.ping.sso=Ping SSO
page.label.plain=ãã¬ã¼ã³
page.label.played=åçæ¸ã¿
page.label.please.select=--- é¸æãã¦ãã ãã ---
page.label.pod.type=ããã ã¿ã¤ã
page.label.pod=ããã
page.label.pods=ããã
page.label.point.size=ãã¤ã³ã ãµã¤ãº
page.label.portrait=ç¸¦ (8.5 x 11)
page.label.position=ä½ç½®
page.label.prepare.output=åºåã®æºåä¸­...
page.label.prepare.report=ã¬ãã¼ãã®æºåä¸­...
page.label.preview.data.resource=ãã¼ã¿ ãªã½ã¼ã¹ã®ãã¬ãã¥ã¼
page.label.preview.language.configuration=ãã¬ãã¥ã¼è¨èªæ§æ
page.label.preview.zone=ãã¬ãã¥ã¼ ã¾ã¼ã³
page.label.preview=ãã¬ãã¥ã¼
page.label.primary.data.file=ãã©ã¤ããª ãã¼ã¿ ãã¡ã¤ã«
page.label.primary.data.input=ãã©ã¤ããª ãã¼ã¿å¥å
page.label.primary.data.source=ãã©ã¤ããª ãã¼ã¿ ã½ã¼ã¹
page.label.primary.data.variable=ãã©ã¤ããª ãã¼ã¿å¤æ°
page.label.primary=ãã©ã¤ããª
page.label.print=å°å·
page.label.prioritize.messages=ã¡ãã»ã¼ã¸ã®åªååº¦è¨­å®
page.label.prioritize.optional.inserts=ãªãã·ã§ã³æ¿å¥ã®åªååº¦è¨­å®
page.label.prioritize=åªååº¦è¨­å®
page.label.priority=åªååº¦
page.label.processing=å¦çä¸­
page.label.product=è£½å
page.label.production.message.report=ã¢ã¯ãã£ã ã¡ãã»ã¼ã¸ ã¬ãã¼ãè©³ç´°
page.label.production.server.settings=ãã­ãã¯ã·ã§ã³ ãµã¼ãã¼è¨­å®
page.label.production.status=ãã­ãã¯ã·ã§ã³ ã¹ãã¼ã¿ã¹
page.label.production=ãã­ãã¯ã·ã§ã³
page.label.proof.active=æ ¡æ­£ (ã¢ã¯ãã£ã)
page.label.proof=æ ¡æ­£
page.label.proofing.data.resource=æ ¡æ­£ãã¼ã¿ ãªã½ã¼ã¹
page.label.proofing.data=æ ¡æ­£ãã¼ã¿
page.label.proofs=æ ¡æ­£...
page.label.properties=ãã­ããã£
page.label.property=ãã­ããã£
page.label.provider.description=ãã­ãã¤ãã¼ã®èª¬æ
page.label.provider.logo.filename=ãã­ãã¤ãã¼ ã­ã´ ãã¡ã¤ã«å
page.label.provider.logo.filepath=ãã­ãã¤ãã¼ ã­ã´ã®ãã¡ã¤ã« ãã¹ã¨åå
page.label.provider.logo=ãã­ãã¤ãã¼ ã­ã´
page.label.provider.settings=ãã­ãã¤ãã¼è¨­å®
page.label.proxy.sso.idpid=ããã ID
page.label.proxy.sso.masterpageurl=ãã¹ã¿ã¼ URL
page.label.proxy.sso.pod.role.master=ãã¹ã¿ã¼
page.label.proxy.sso.pod.role.slave=ã¹ã¬ã¼ã
page.label.proxy.sso.pod.role=ãããã®å½¹å²
page.label.proxy.sso.secretkey=ç§å¯éµ
page.label.proxy.sso.settings=çµ±åã­ã°ã¤ã³è¨­å®
page.label.puts=ã³ããã
page.label.qualification.output=é©æ ¼åºå
page.label.qualification=é©æ ¼
page.label.qualified=é©æ ¼
page.label.quit=çµäº
page.label.radio=ã©ã¸ãª
page.label.rate.sheet.coverage=æéè¡¨ã®é©ç¨ç¯å²
page.label.rate.sheet=æéè¡¨
page.label.rate.sheets=æéè¡¨
page.label.reach=è·é¢
page.label.reassign.insert.schedules=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã®åå²ãå½ã¦
page.label.reassign.inserts=æ¿å¥ã®åå²ãå½ã¦
page.label.reassign.messages=ã¡ãã»ã¼ã¸ã®åå²ãå½ã¦
page.label.reassign.to.user=ã¦ã¼ã¶ã¼ã«åå²ãå½ã¦
page.label.reassign.touchpoint.variants=ã¿ãããã¤ã³ã ããªã¢ã³ãã®åå²ãå½ã¦
page.label.reassign=åå²ãå½ã¦
page.label.recipient.attachment.location=åä¿¡èã®æ·»ä»ãã¡ã¤ã«ã®å ´æ
page.label.recipient.attachment.name=åä¿¡èã®æ·»ä»ãã¡ã¤ã«å
page.label.recipient.details=åä¿¡èã®è©³ç´°
page.label.recipient.range=åä¿¡èã®ç¯å²
page.label.recipient=åä¿¡è
page.label.record.level=ã¬ã³ã¼ã ã¬ãã«
page.label.record=ã¬ã³ã¼ã
page.label.recordindicator=ã¬ã³ã¼ã ã¤ã³ã¸ã±ã¼ã¿ã¼
page.label.records=ã¬ã³ã¼ã
page.label.recordtype=ã¬ã³ã¼ã ã¿ã¤ã
page.label.reference.data.file=åç§ãã¼ã¿ ãã¡ã¤ã«
page.label.reference.data.source=åç§ãã¼ã¿ ã½ã¼ã¹
page.label.reference.data.sources=åç§ãã¼ã¿ ã½ã¼ã¹
page.label.reference.data.variable=åç§ãã¼ã¿å¤æ°
page.label.reference.data=åç§ãã¼ã¿
page.label.reference=åç§
page.label.referenced=åç§å¯¾è±¡
page.label.refresh.segmentation.analysis=ã»ã°ã¡ã³ãååæã®æ´æ°
page.label.reinstate=åé
page.label.reject.and.override=æå¦ãã¦ä¸æ¸ã
page.label.reject.workflow.owner=æå¦: ã¯ã¼ã¯ãã­ã¼ææè
page.label.reject=æå¦
page.label.related.data.has.been.deleted=ã¢ã¤ãã ãåé¤ããã¦ãã¾ã
page.label.related.item=é¢é£ã¢ã¤ãã 
page.label.related=é¢é£
page.label.release.for.approval=ãªãªã¼ã¹ãã¦æ¿èªãåãã
page.label.release.for.use=ãªãªã¼ã¹ãã¦ä½¿ç¨ãã
page.label.release.to.tenants=ããã³ãã«å¯¾ãã¦ãªãªã¼ã¹ãã
page.label.release=ãªãªã¼ã¹
page.label.remote.server.ip=ãªã¢ã¼ã ãµã¼ãã¼ IP
page.label.remote.server.password=ãªã¢ã¼ã ãµã¼ãã¼ ãã¹ã¯ã¼ã
page.label.remote.server.port=ãªã¢ã¼ã ãµã¼ãã¼ ãã¼ã
page.label.remote.server.user=ãªã¢ã¼ã ãµã¼ãã¼ ã¦ã¼ã¶ã¼
page.label.remove.connection=æ¥ç¶ã®åé¤
page.label.remove.data=ãã¼ã¿ã®åé¤
page.label.remove.filter=ãã£ã«ã¿ã¼ã®åé¤
page.label.remove.from.favorites=ãæ°ã«å¥ãããåé¤
page.label.remove.menu.item=ã¡ãã¥ã¼é ç®ã®åé¤
page.label.remove.message=ã¡ãã»ã¼ã¸ã®åé¤
page.label.remove.touchpoint.variants=ã¿ãããã¤ã³ã ããªã¢ã³ãã®åé¤
page.label.remove.variant=ããªã¢ã³ãã®åé¤
page.label.remove=åé¤
page.label.rename.selection=é¸æåã®å¤æ´
page.label.rename.variant=ããªã¢ã³ãåã®å¤æ´
page.label.rename=ååã®å¤æ´
page.label.reopen.tenant.window=ããã³ã ã¦ã£ã³ãã¦ã®åãªã¼ãã³
page.label.repair=ä¿®å¾©
page.label.repeat.annually=æ¯å¹´ç¹°ãè¿ã
page.label.repeating=ç¹°ãè¿ã
page.label.repeatingevents=ç¹°ãè¿ãã¤ãã³ã?
page.label.repeatingrecord=ç¹°ãè¿ãã¬ã³ã¼ã
page.label.repeats=ç¹°ãè¿ã
page.label.reply.to.email=é»å­ã¡ã¼ã«ã«è¿ä¿¡
page.label.report=ã¬ãã¼ã
page.label.reports=ã¬ãã¼ã
page.label.request.date=è¦æ±æ¥
page.label.request.password.reset=ãã¹ã¯ã¼ã ãªã»ããã®è¦æ±
page.label.request.touchpoint.audit.report=ã¿ãããã¤ã³ãç£æ»ã¬ãã¼ãã®è¦æ±
page.label.request=è¦æ±
page.label.requestedaction=è¦æ±ãããã¢ã¯ã·ã§ã³
page.label.rerun=åå®è¡
page.label.resend.activation=ã¢ã¯ãã£ãåã®åéä¿¡
page.label.reservations=äºç´
page.label.reserved.number=äºç´çªå·
page.label.reserved=äºç´æ¸ã¿
page.label.reset.password.pending=ãã¹ã¯ã¼ãã®ãªã»ããä¿çä¸­
page.label.reset.password=ãã¹ã¯ã¼ãã®ãªã»ãã
page.label.reset.template=ãã³ãã¬ã¼ãã®ãªã»ãã
page.label.reset=ãªã»ãã
page.label.resetpassword=ãã¹ã¯ã¼ãã®ãªã»ãã
page.label.resettodefault=ããã©ã«ãã«ãªã»ãã
page.label.restore=å¾©å
page.label.restricted=å¶éãã
page.label.results=çµæ
page.label.right=å³
page.label.role.description=å½¹å²ã®èª¬æ
page.label.role.view=å½¹å²ã®è¡¨ç¤º
page.label.role.visibility.private=ãã©ã¤ãã¼ã
page.label.role.visibility.shared=å±æ
page.label.role.visibility=å½¹å²ã®è¡¨ç¤ºæ¨©é
page.label.role=å½¹å²
page.label.rolename=å½¹å²å
page.label.roles=å½¹å²
page.label.roll.forward=ã­ã¼ã« ãã©ã¯ã¼ã
page.label.root.tags=ã«ã¼ã ã¿ã°
page.label.rule.conditions=ã«ã¼ã«æ¡ä»¶
page.label.rule.name=ã«ã¼ã«å
page.label.rule=ã«ã¼ã«
page.label.rules=ã«ã¼ã«
page.label.run.all=ãã¹ã¦å®è¡
page.label.run.date=å®è¡æ¥
page.label.run.health.check.and.resolve=ç¶æç¢ºèªãå®è¡ãã¦è§£æ±º
page.label.run=å®è¡
page.label.same.as=æ¬¡ã¨åã
page.label.sample=ãµã³ãã«
page.label.sandbox=ãµã³ãããã¯ã¹
page.label.save.and.proof=ä¿å­ãã¦æ ¡æ­£
page.label.save.complete=ä¿å­ãå®äºãã¾ãã
page.label.save.continue=ä¿å­ãã¦ç¶è¡
page.label.save=ä¿å­
page.label.scan.domains=ãã¡ã¤ã³ã®ã¹ã­ã£ã³
page.label.schedule.ID=ã¹ã±ã¸ã¥ã¼ã« ID
page.label.schedule.selector.data=ã¹ã±ã¸ã¥ã¼ã« ã»ã¬ã¯ã¿ã¼ ãã¼ã¿
page.label.schema.name=ã¹ã­ã¼ãå
page.label.schema.password=ã¹ã­ã¼ã ãã¹ã¯ã¼ã
page.label.search.content=ã³ã³ãã³ãã®æ¤ç´¢
page.label.search=æ¤ç´¢
page.label.section=ã»ã¯ã·ã§ã³
page.label.sections=ã»ã¯ã·ã§ã³
page.label.secure.signin=ã»ã­ã¥ã¢ ãµã¤ã³ã¤ã³
page.label.secure=ã»ã­ã¥ã¢
page.label.security.settings=ã»ã­ã¥ãªãã£è¨­å®
page.label.security=ã»ã­ã¥ãªãã£
page.label.segmentation.analysis=ã»ã°ã¡ã³ãååæ
page.label.segmentation=ã»ã°ã¡ã³ãå
page.label.select.all=ãã¹ã¦é¸æ
page.label.select.by=é¸æåºæº
page.label.select.connector=ã³ãã¯ã¿ã¼ã®é¸æ
page.label.select.content.by=ã³ã³ãã³ãã®é¸æåºæº
page.label.select.image=ç»åã®é¸æ
page.label.select=é¸æ
page.label.selectable.constant=é¸æå¯è½ãªå®æ°
page.label.selected.by=é¸æè
page.label.selected.inserts=é¸æããæ¿å¥
page.label.selected=é¸ææ¸ã¿
page.label.selection.data=é¸æãã¼ã¿
page.label.selection.status=é¸æã¹ãã¼ã¿ã¹
page.label.selections=é¸æ
page.label.selector=ã»ã¬ã¯ã¿ã¼
page.label.selectors=ã»ã¬ã¯ã¿ã¼
page.label.self=èªå·±
page.label.sender.email=éä¿¡èé»å­ã¡ã¼ã«
page.label.sender.name=éä¿¡èå
page.label.sendmail.server.settings=Sendmail ã³ãã¯ã¿ã¼ ãµã¼ãã¼è¨­å®
page.label.sequence=é åº
page.label.server=ãµã¼ãã¼
page.label.service.provider=ãµã¼ãã¹ ãã­ãã¤ãã¼
page.label.session.timeout=ã»ãã·ã§ã³ ã¿ã¤ã ã¢ã¦ã
page.label.set.background=èæ¯ã®è¨­å®
page.label.set.modifiers.and.attributes=ã¢ãã£ãã¡ã¤ã¢ã¼ã¨å±æ§ã®è¨­å®
page.label.set.modifiers.attributes=ã¢ãã£ãã¡ã¤ã¢ã¼ã¨å±æ§ã®è¨­å®
page.label.set.modifiers=ã¢ãã£ãã¡ã¤ã¢ã¼ã®è¨­å®
page.label.set.template=ãã³ãã¬ã¼ãã®è¨­å®
page.label.set.visibility=è¡¨ç¤ºè¨­å®
page.label.settings=è¨­å®
page.label.setup.insert.schedules=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã®ã»ããã¢ãã
page.label.setup.required=ã»ããã¢ãããå¿è¦
page.label.setup.touchpoint.variants=ã¿ãããã¤ã³ã ããªã¢ã³ãã®ã»ããã¢ãã
page.label.setup.touchpoint=ã¿ãããã¤ã³ãã®ã»ããã¢ãã
page.label.setup.workflow=ã¯ã¼ã¯ãã­ã¼ã®ã»ããã¢ãã
page.label.setup=ã»ããã¢ãã
page.label.shared.brackets=[å±æ]
page.label.shared.content.workflow=å±æã³ã³ãã³ã ã¯ã¼ã¯ãã­ã¼
page.label.shared.content=å±æã³ã³ãã³ã
page.label.shared=å±æ
page.label.sharing.properties=ãã­ããã£ã®å±æ
page.label.sheets=ã·ã¼ã
page.label.short.month.name=æã®åå
page.label.short.month.names=æã®åå
page.label.show.items=ã¢ã¤ãã ã®è¡¨ç¤º
page.label.show=è¡¨ç¤º
page.label.showing=è¡¨ç¤ºä¸­
page.label.siblings=åå¼
page.label.sign.in.settings=ãµã¤ã³ã¤ã³è¨­å®
page.label.signout=ãµã¤ã³ã¢ã¦ã
page.label.simulation.customer.flag=é¡§å®¢ã®è©³ç´°ãå«ãã
page.label.simulation.data=ã·ãã¥ã¬ã¼ã·ã§ã³ ãã¼ã¿
page.label.simulation.date=ã·ãã¥ã¬ã¼ã·ã§ã³æ¥
page.label.simulation.run.date=ã·ãã¥ã¬ã¼ã·ã§ã³å®è¡æ¥
page.label.simulation=ã·ãã¥ã¬ã¼ã·ã§ã³
page.label.simulations=ã·ãã¥ã¬ã¼ã·ã§ã³
page.label.single.sign.out=ã·ã³ã°ã« ãµã¤ã³ã¢ã¦ã
page.label.single=åä¸
page.label.size.memory=ãµã¤ãº (ã¡ã¢ãª)
page.label.size=ãµã¤ãº
page.label.solid=å®ç·
page.label.source.type=ã½ã¼ã¹ ã¿ã¤ã
page.label.source=ã½ã¼ã¹
page.label.spacing.after=å¾ãã®éé
page.label.spacing.before=åã®éé
page.label.spacing=éé
page.label.special=ç¹æ®
page.label.specify.new.variant.name=æ°è¦ããªã¢ã³ãã®ååã®æå®
page.label.ssoerrorpageurl=SSO ã¨ã©ã¼ ãã¼ã¸ URL
page.label.ssoidpid=SSO IdP ID
page.label.ssologoutpageurl=SSO ã­ã°ã¢ã¦ã ãã¼ã¸ URL
page.label.ssosecretkey=SSO ç§å¯éµ
page.label.stale.brackets=(å¤ã)
page.label.stale=å¤ã
page.label.start.data.records=éå§ãã¼ã¿ ã¬ã³ã¼ã
page.label.start.date=éå§æ¥
page.label.start.upload=ã¢ããã­ã¼ãã®éå§
page.label.start=éå§
page.label.startcustomer=é¡§å®¢ã®éå§
page.label.state.approved=é²è¡ä¸­
page.label.state.completed=å®äº
page.label.state.error=ã¨ã©ã¼
page.label.state.exported=é²è¡ä¸­
page.label.state.frozen=é²è¡ä¸­
page.label.state.inprocess=é²è¡ä¸­
page.label.state.new=æ°è¦
page.label.state.notapplicable=N/A
page.label.state.notstarted=æªéå§
page.label.state.packed=é²è¡ä¸­
page.label.state.pendingpacking=é²è¡ä¸­
page.label.state.pendingreportprocessing=é²è¡ä¸­
page.label.state.pendingscenarioprocessing=é²è¡ä¸­
page.label.state.pendingscenarioprocessingfrozen=é²è¡ä¸­
page.label.state.readytostart=éå§æºåå®äº
page.label.state.requested=è¦æ±æ¸ã¿
page.label.state.started=éå§æ¸ã¿
page.label.static=éç
page.label.status.archived=ã¢ã¼ã«ã¤ãæ¸ã¿
page.label.status.inactive=éã¢ã¯ãã£ã
page.label.status.new=æ°è¦
page.label.status.production=ã¢ã¯ãã£ã
page.label.status.removed=åé¤æ¸ã¿
page.label.status.setup=ã»ããã¢ãã
page.label.status.waiting.approval=æ¿èªå¾ã¡
page.label.status.wip=ä½æ¥­ã³ãã¼
page.label.status=ã¹ãã¼ã¿ã¹
page.label.stock.id=ã¹ããã¯ ID
page.label.structured=æ§é å
page.label.style.customizations=ã¹ã¿ã¤ã«ã®ã«ã¹ã¿ãã¤ãº
page.label.style=ã¹ã¿ã¤ã«
page.label.styles=ã¹ã¿ã¤ã«
page.label.submit=æåº
page.label.submitted=æåºæ¸ã¿
page.label.succeess=æå
page.label.success=æå
page.label.suffix.currency.symbol=ãµãã£ãã¯ã¹éè²¨è¨å·
page.label.suggested.schema.name=åè£ã®ã¹ã­ã¼ãå
page.label.support=ãµãã¼ã
page.label.supports.styles=ãµãã¼ããããã¹ã¿ã¤ã«
page.label.suppress.content=ã³ã³ãã³ãã®éè¡¨ç¤º
page.label.suppress=éè¡¨ç¤º
page.label.suppressed=éè¡¨ç¤º
page.label.switch.tenants=ããã³ãã®åãæ¿ã
page.label.switch.to.node.instance=ã¤ã³ã¹ã¿ã³ã¹ã¸ã®åãæ¿ã
page.label.switch.to.tenant.user=ããã³ã ã¦ã¼ã¶ã¼ã¸ã®åãæ¿ã
page.label.system.settings=ã·ã¹ãã è¨­å®
page.label.system_view.page=system_view ãã¼ã¸
page.label.table.alignment=ãã¼ãã«éç½®
page.label.table=ãã¼ãã«
page.label.tables=ãã¼ãã«
page.label.tag.name=ã¿ã°å
page.label.tag.type=ã¿ã° ã¿ã¤ã
page.label.tag.types=ã¿ã° ã¿ã¤ã
page.label.tag=ã¿ã°
page.label.tags=ã¿ã°
page.label.target.message=ã¿ã¼ã²ãã ã¡ãã»ã¼ã¸
page.label.target=ã¿ã¼ã²ãã
page.label.targetgroup.details=ã¿ã¼ã²ããè¨­å®ã«ã¼ã«
page.label.targetgroup.name=ã¿ã¼ã²ãã ã°ã«ã¼ãå
page.label.targetgroup=ã¿ã¼ã²ãã ã°ã«ã¼ã
page.label.targetgroups=ã¿ã¼ã²ãã ã°ã«ã¼ã
page.label.targeting.summary=ã¿ã¼ã²ããè¨­å®ãµããªã¼
page.label.targeting=ã¿ã¼ã²ããè¨­å®
page.label.task.name=ã¿ã¹ã¯å
page.label.task.overview=ã¿ã¹ã¯æ¦è¦
page.label.task.summary=ã¿ã¹ã¯ ãµããªã¼
page.label.task=ã¿ã¹ã¯
page.label.tasks.by.due.date=æéæ¥ãã¨ã®ã¿ã¹ã¯
page.label.tasks.by.user=ã¦ã¼ã¶ã¼ãã¨ã®ã¿ã¹ã¯
page.label.tasks=ã¿ã¹ã¯
page.label.template.attributes=å±æ§
page.label.template.modifier.list=ãã³ãã¬ã¼ã ã¢ãã£ãã¡ã¤ã¢ã¼ ãªã¹ã
page.label.template.modifier.setup=ãã³ãã¬ã¼ã ã¢ãã£ãã¡ã¤ã¢ã¼ã®ã»ããã¢ãã
page.label.template.modifiers=ãã³ãã¬ã¼ã ã¢ãã£ãã¡ã¤ã¢ã¼
page.label.template.package=ãã³ãã¬ã¼ã ããã±ã¼ã¸
page.label.template=ãã³ãã¬ã¼ã
page.label.tenant.access=ããã³ã ã¢ã¯ã»ã¹
page.label.tenant.error.correctiveaction=ä¿®æ­£ã¢ã¯ã·ã§ã³
page.label.tenant.error.errordetails=ã¨ã©ã¼è©³ç´°
page.label.tenant.error.limitexceed=ã¨ã©ã¼ - ã©ã¤ã»ã³ã¹ãããããã³ãã®ä¸éãè¶ãã¾ãã
page.label.tenant.insert.schedules=ããã³ãæ¿å¥ã¹ã±ã¸ã¥ã¼ã«
page.label.tenant.management.schedule.setup=æ¿å¥ã¹ã±ã¸ã¥ã¼ã« ããã³ã ã»ããã¢ãã
page.label.tenant.management.schedule.state.in.process=é²è¡ä¸­
page.label.tenant.management.schedule.state.released=ããã³ãã«å¯¾ãã¦ãªãªã¼ã¹æ¸ã¿
page.label.tenant.management.schedule=ããã³ãç®¡çã¹ã±ã¸ã¥ã¼ã«
page.label.tenant.management=ããã³ãç®¡ç
page.label.tenant.overview=ããã³ãæ¦è¦
page.label.tenant.permission.settings=ããã³ãæ¨©éè¨­å®
page.label.tenant.permissions=ããã³ãæ¨©é
page.label.tenant.schedule.assignment.setup=ããã³ã ã¹ã±ã¸ã¥ã¼ã«ã®ç·¨é
page.label.tenant.schedule.assignment.state.complete=å®äº
page.label.tenant.schedule.assignment.state.in.process=é²è¡ä¸­
page.label.tenant.schedule.assignment=ããã³ã ã¹ã±ã¸ã¥ã¼ã«
page.label.tenant.schedules=ããã³ã ã¹ã±ã¸ã¥ã¼ã«
page.label.tenant.settings.code=ããã³ã ã³ã¼ã
page.label.tenant.settings.contact.address=çªå°
page.label.tenant.settings.contact.city=å¸åºçºæ
page.label.tenant.settings.contact.country=å½
page.label.tenant.settings.contact.email=é»å­ã¡ã¼ã«
page.label.tenant.settings.contact.fax=Fax
page.label.tenant.settings.contact.mobile=ã¢ãã¤ã«
page.label.tenant.settings.contact.name=åå
page.label.tenant.settings.contact.postalcode=éµä¾¿çªå·
page.label.tenant.settings.contact.province=é½éåºç
page.label.tenant.settings.contact.suite=é¨å±/ã¦ããã
page.label.tenant.settings.contact.title=ã¿ã¤ãã«
page.label.tenant.settings.contact.web=Web ãµã¤ã
page.label.tenant.settings.edit.title=ããã³ãã®ç·¨é
page.label.tenant.settings.name=ããã³ãå
page.label.tenant.settings.overview=ããã³ãæ¦è¦
page.label.tenant.settings.primary.contact=ãã©ã¤ããªé£çµ¡å
page.label.tenant.settings.provider=ããã³ãã®ãã­ãã¤ãã¼
page.label.tenant.settings.status=ã¹ãã¼ã¿ã¹
page.label.tenant.settings.title=ããã³ãè¨­å®ã®è¡¨ç¤º
page.label.tenant.settings=ããã³ãè¨­å®
page.label.tenant.threshold.overrides=ããã³ããããå¤ã®ãªã¼ãã¼ã©ã¤ã
page.label.tenant.window=ããã³ã ã¦ã£ã³ãã¦
page.label.tenant=ããã³ã
page.label.tenant_label=ããã³ã
page.label.tenantconfirm.action=è¦æ±ãããã¢ã¯ã·ã§ã³
page.label.tenantconfirm.activeusers=ã¢ã¯ãã£ã ã¦ã¼ã¶ã¼
page.label.tenantconfirm.consequence=çµæ
page.label.tenantconfirm.instruction=æé 
page.label.tenantconfirm.name=ããã³ãå
page.label.tenantconfirm.provider=ããã³ãã®ãã­ãã¤ãã¼
page.label.tenantconfirm.status=ã¹ãã¼ã¿ã¹
page.label.tenants=ããã³ã
page.label.term=ç¨èª
page.label.test.data=ãã¹ã ãã¼ã¿
page.label.test.date=ãã¹ãæ¥
page.label.test.run.date=ãã¹ãå®è¡æ¥
page.label.test=ãã¹ã
page.label.testing=ãã¹ãä¸­
page.label.tests=ãã¹ã
page.label.text.field=ãã­ã¹ã ãã£ã¼ã«ã
page.label.text.formatting=ãã­ã¹ãæ¸å¼è¨­å®
page.label.text.style.customization=ãã­ã¹ã ã¹ã¿ã¤ã«ã®ã«ã¹ã¿ãã¤ãº
page.label.text.style=ãã­ã¹ã ã¹ã¿ã¤ã«
page.label.text.styles=ãã­ã¹ã ã¹ã¿ã¤ã«
page.label.text=ãã­ã¹ã
page.label.theme.color=ãã¼ãè²
page.label.theme.settings=ãã¼ãè¨­å®
page.label.theme=ãã¼ã
page.label.thousandsseparator=æ¡åºåãè¨å·
page.label.throttle=èª¿æ´
page.label.time=æé
page.label.timing.summary=ã¿ã¤ãã³ã° ãµããªã¼
page.label.timing=ã¿ã¤ãã³ã°
page.label.to.date=çµäºæ¥
page.label.to.path=å®åãã¹
page.label.to=çµäº
page.label.touchpoint.assignments=ã¿ãããã¤ã³ãå²ãå½ã¦
page.label.touchpoint.channel.configuration=ã¿ãããã¤ã³ã ãã£ãã«æ§æ
page.label.touchpoint.collection=ã¿ãããã¤ã³ã ã³ã¬ã¯ã·ã§ã³
page.label.touchpoint.collections=ã¿ãããã¤ã³ã ã³ã¬ã¯ã·ã§ã³
page.label.touchpoint.container.publication.file.path=ãããªã±ã¼ã·ã§ã³ ãã¡ã¤ã« ãã¹
page.label.touchpoint.container.workflow.file.path=ã¯ã¼ã¯ãã­ã¼ ãã¡ã¤ã« ãã¹
page.label.touchpoint.container.zone.attributes=å±æ§
page.label.touchpoint.container.zone.datagroup=ãã¼ã¿ ã°ã«ã¼ã
page.label.touchpoint.container.zone.tenants=ããã³ã
page.label.touchpoint.container.zoneconnector=ã³ãã¯ã¿ã¼å
page.label.touchpoint.container.zonename=ã¾ã¼ã³å
page.label.touchpoint.container=ã¿ãããã¤ã³ã ã³ã³ããã¼
page.label.touchpoint.delivery.report=ã¿ãããã¤ã³ãéä¿¡ã¬ãã¼ã
page.label.touchpoint.deliveryevent.creationtime=ã¤ãã³ãä½ææ¥
page.label.touchpoint.deliveryevent.deliverystatus=éä¿¡ã¹ãã¼ã¿ã¹
page.label.touchpoint.deliveryevent.jobid=ã¸ã§ã ID
page.label.touchpoint.deliveryevent.production.runtype=ã¢ã¯ãã£ãå®è¡ã¿ã¤ã
page.label.touchpoint.deliveryevent.requestor=è¦æ±è
page.label.touchpoint.deliveryevent.scheduledruntime=ã¹ã±ã¸ã¥ã¼ã«ãããå®è¡æ¥
page.label.touchpoint.deliveryevent.updatetime=æçµæ´æ°æ¥
page.label.touchpoint.deliveryeventhistory=ã¤ãã³ãå±¥æ­´
page.label.touchpoint.health.check=ã¿ãããã¤ã³ãã®ç¶æç¢ºèª
page.label.touchpoint.import.filename=XML ãã¡ã¤ã«ã®ã¤ã³ãã¼ã
page.label.touchpoint.management=ã¿ãããã¤ã³ãç®¡ç
page.label.touchpoint.name=ã¿ãããã¤ã³ãå
page.label.touchpoint.no.deliveryevent.found=ãã®ã¿ãããã¤ã³ãã®éä¿¡ã¤ãã³ããè¦ã¤ããã¾ããã
page.label.touchpoint.no.deliveryeventhistory.found=ã¤ãã³ãå±¥æ­´ã¢ã¤ãã ãè¦ã¤ããã¾ããã
page.label.touchpoint.properties=ã¿ãããã¤ã³ãã®ãã­ããã£
page.label.touchpoint.setup=ã¿ãããã¤ã³ã ã»ããã¢ãã
page.label.touchpoint.status=ã¿ãããã¤ã³ã ã¹ãã¼ã¿ã¹
page.label.touchpoint.targeting=ã¿ãããã¤ã³ãã®ã¿ã¼ã²ããè¨­å®
page.label.touchpoint.variant.content=ã¿ãããã¤ã³ã ããªã¢ã³ãã®ã³ã³ãã³ã
page.label.touchpoint.variants=ã¿ãããã¤ã³ã ããªã¢ã³ã
page.label.touchpoint=ã¿ãããã¤ã³ã
page.label.touchpointadmin=ã¿ãããã¤ã³ã
page.label.touchpoints=ã¿ãããã¤ã³ã
page.label.track.invalid.sign.ins=ç¡å¹ãªãµã¤ã³ã¤ã³åæ°ã®è¿½è·¡
page.label.transactional=ãã©ã³ã¶ã¯ã·ã§ã³
page.label.transition.environment=ãã¬ãªãªã¼ã¹ ãã¼ã¸ã§ã³
page.label.transition=ãã¬ãªãªã¼ã¹
page.label.true=True
page.label.twosecs=2 ç§
page.label.type=ã¿ã¤ã
page.label.unassigned.brackets=(æªå²ãå½ã¦)
page.label.unavailable=ä½¿ç¨ä¸å¯
page.label.underline=ä¸ç·
page.label.unexpected.error=åé¡ãçºçãã¾ããã
page.label.unhold=ä¿çè§£é¤
page.label.united.states=ç±³å½
page.label.unlimted=ç¡å¶é?
page.label.unrestricted=å¶éãªã
page.label.update.channel.configuration=ã¿ãããã¤ã³ã ãã£ãã«æ§æã®ç·¨é
page.label.update.communications.workflow=ã³ãã¥ãã±ã¼ã·ã§ã³ ã¯ã¼ã¯ãã­ã¼ã®ç·¨é
page.label.update.content.library.workflow=ç»åã©ã¤ãã©ãª ã¯ã¼ã¯ãã­ã¼ã®ç·¨é
page.label.update.embedded.content.workflow=ã¹ãã¼ã ãã­ã¹ã ã¯ã¼ã¯ãã­ã¼ã®ç·¨é
page.label.update.language.settings=è¨èªè¨­å®ã®ç·¨é
page.label.update.locale.settings=ã­ã±ã¼ã«è¨­å®ã®ç·¨é
page.label.update.master.system.settings=ãã¹ã¿ã¼ç®¡çè: ã·ã¹ãã è¨­å®
page.label.update.message.workflow=ã¡ãã»ã¼ã¸ ã¯ã¼ã¯ãã­ã¼ã®ç·¨é
page.label.update.selectors=ã»ã¬ã¯ã¿ã¼ã®ç·¨é
page.label.update.shared.content.workflow=å±æã³ã³ãã³ã ã¯ã¼ã¯ãã­ã¼ã®ç·¨é
page.label.update.system.settings=ã·ã¹ãã è¨­å®ã®ç·¨é
page.label.update.test.scenario=ãã¹ãã®ç·¨é
page.label.update.test=ãã¹ã ã·ããªãªã®æ´æ°
page.label.update.touchpoint.selection.workflow=ã¿ãããã¤ã³ã ããªã¢ã³ã ã¯ã¼ã¯ãã­ã¼ã®ç·¨é
page.label.update=æ´æ°
page.label.updated=æ´æ°æ¸ã¿
page.label.updateworkgroup=ã¯ã¼ã¯ã°ã«ã¼ãã®ç·¨é
page.label.updateworkgroupzoneassociations=ã¯ã¼ã¯ã°ã«ã¼ã ã¾ã¼ã³ã®é¢é£ä»ãã®ç·¨é
page.label.upload.bulk.variants=ããªã¢ã³ãã®ã¢ããã­ã¼ã
page.label.upload.folders=ãã©ã«ãã¼ã®ã¢ããã­ã¼ã
page.label.upload.messages=ã¡ãã»ã¼ã¸ã®ã¢ããã­ã¼ã
page.label.upload.theme=ãã¼ãã®ã¢ããã­ã¼ã
page.label.upload.variants=ããªã¢ã³ãã®ã¢ããã­ã¼ã
page.label.upload=ã¢ããã­ã¼ã
page.label.url=URL
page.label.usage=ä½¿ç¨æ³
page.label.use.all.in.process.library.assets=ã©ã¤ãã©ãª ã¢ã»ããã®ãã¹ã¦ã®ä½æ¥­ã³ãã¼ã®ä½¿ç¨
page.label.use.all.in.process=ãã¹ã¦ã®ä½æ¥­ã³ãã¼ã®ä½¿ç¨
page.label.use.default.template=ããã©ã«ã ãã³ãã¬ã¼ãã®ä½¿ç¨
page.label.use=ä½¿ç¨
page.label.user.by.schedules=ä½¿ç¨è - ã¹ã±ã¸ã¥ã¼ã«
page.label.user.id.properties=ã¦ã¼ã¶ã¼åã®ãã­ããã£
page.label.user.id.settings=ã¦ã¼ã¶ã¼åã®è¨­å®
page.label.user.interface.settings=ã¦ã¼ã¶ã¼ ã¤ã³ã¿ã¼ãã§ã¤ã¹ã®è¨­å®
page.label.user.management=ã¦ã¼ã¶ã¼ç®¡ç
page.label.user.overview=ã¦ã¼ã¶ã¼æ¦è¦
page.label.user.settings.saved=è¨­å®åå®¹ãæ­£å¸¸ã«æ´æ°ããã¾ããã[å®äº] ãã¯ãªãã¯ãã¦ã¦ã£ã³ãã¦ãéãã¦ãã ããã
page.label.user.settings=ã¦ã¼ã¶ã¼è¨­å®
page.label.user.specified.values=ã¦ã¼ã¶ã¼æå®å¤
page.label.user.view=ã¦ã¼ã¶ã¼ã®è¡¨ç¤º
page.label.user=ã¦ã¼ã¶ã¼
page.label.username=ã¦ã¼ã¶ã¼å
page.label.users=ã¦ã¼ã¶ã¼
page.label.uses=ã¦ã¼ã¶ã¼
page.label.utf8=UTF-8
page.label.validate.initialize=æ¤è¨¼ (åæå)
page.label.validated=æ¤è¨¼æ¸ã¿
page.label.value=å¤
page.label.variable.details=å¤æ°è©³ç´°
page.label.variable=å¤æ°
page.label.variables.map.to=å¤æ°ã®ãããã³ã°å
page.label.variables.none=å¤æ°ãªã
page.label.variables=å¤æ°
page.label.variant.actions=ããªã¢ã³ã ã¢ã¯ã·ã§ã³
page.label.variant.finder=ããªã¢ã³ãæ¤ç´¢
page.label.variant.selector.data=ããªã¢ã³ã ã»ã¬ã¯ã¿ã¼ ãã¼ã¿
page.label.variant=ããªã¢ã³ã
page.label.variants.fully.visible.by.default=ããã©ã«ãã§å®å¨ãªè¡¨ç¤ºæ¨©éãè¨­å®ãã
page.label.variants=ããªã¢ã³ã
page.label.variation.enabled=ããªã¨ã¼ã·ã§ã³æå¹
page.label.varibale.lookup.items=å¤æ°åç§ã¢ã¤ãã 
page.label.varied.by=å¤åè¦å 
page.label.verbose=è©³ç´°
page.label.version.activity.aborted=ä¸­æ­¢æ¸ã¿
page.label.version.activity.archived=ã¢ã¼ã«ã¤ãæ¸ã¿
page.label.version.activity.checkout=ãã¼ã¸ã§ã³ ãã§ãã¯ã¢ã¦ã
page.label.version.activity.clone=æ¢å­ããè¤è£½æ¸ã¿
page.label.version.activity.new=æ°è¦
page.label.version.activity.newversioncheckedin=æ°ãããã¼ã¸ã§ã³ããã§ãã¯ã¤ã³ããã¾ãã
page.label.video=ãããª
page.label.view.all.tags=ãã¹ã¦ã®ã¿ã°ãè¡¨ç¤º
page.label.view.audit.report=ç£æ»ã¬ãã¼ãã®è¡¨ç¤º
page.label.view.data.source=ãã¼ã¿ ã½ã¼ã¹ã®è¡¨ç¤º
page.label.view.data=ãã¼ã¿ã®è¡¨ç¤º
page.label.view.inclusions=å«ã¾ããåå®¹ã®è¡¨ç¤º
page.label.view.language.settings=è¨èªè¨­å®ã®è¡¨ç¤º
page.label.view.locale.settings=ã­ã±ã¼ã«è¨­å®ã®è¡¨ç¤º
page.label.view.log=ã­ã°ã®è¡¨ç¤º
page.label.view.multipart.content=å±æã³ã³ãã³ãã®è¡¨ç¤º
page.label.view.preview=ãã¬ãã¥ã¼ã®è¡¨ç¤º
page.label.view.proof=æ ¡æ­£ã®è¡¨ç¤º
page.label.view.selection.data=é¸æãã¼ã¿ã®è¡¨ç¤º
page.label.view.system.properties=ã·ã¹ãã  ãã­ããã£ã®è¡¨ç¤º
page.label.view.system.settings=ã·ã¹ãã è¨­å®ã®è¡¨ç¤º
page.label.view=è¡¨ç¤º
page.label.viewdataelement=ãã¼ã¿è¦ç´ ã®è¡¨ç¤º
page.label.viewdatasource=ãã¼ã¿ ã½ã¼ã¹ã®è¡¨ç¤º
page.label.viewing=è¡¨ç¤ºä¸­
page.label.viewtask=ã¿ã¹ã¯æ¦è¦
page.label.viewworkgroup=ã¯ã¼ã¯ã°ã«ã¼ãã®è¡¨ç¤º
page.label.viewworkgroupzoneassociations=ã¯ã¼ã¯ã°ã«ã¼ã ã¾ã¼ã³ã®é¢é£ä»ãã®è¡¨ç¤º
page.label.visibility.unrestricted=å¶éãªã
page.label.visibility=è¡¨ç¤ºè¨­å®
page.label.visible=è¡¨ç¤ºå¯è½
page.label.warning=è­¦å
page.label.web.folders=Web ãã©ã«ãã¼
page.label.web.font=Web ãã©ã³ã
page.label.web.page=Web ãã¼ã¸
page.label.web.server.settings=Web ãµã¼ãã¼è¨­å®
page.label.web.service=Web ãµã¼ãã¹
page.label.web.url=Web URL
page.label.weight.unit.grams=ã°ã©ã 
page.label.weight.unit.ounces=ãªã³ã¹
page.label.weight.unit=ééåä½
page.label.weight=éé
page.label.welcome.signin=æ¬¡ã®ã¦ã¼ã¶ã¼ã¨ãã¦ãµã¤ã³ã¤ã³ãã¦ãã¾ã
page.label.where.used=ä½¿ç¨å ´æ
page.label.width=å¹
page.label.windows1250=Windows-1250
page.label.windows1252=Windows-1252
page.label.workflow.definition.WFD=ã¯ã¼ã¯ãã­ã¼å®ç¾© (WFD)
page.label.workflow.due.by.type.notify.and.approve=éç¥ãã¦æ¿èª
page.label.workflow.due.by.type.notify=éç¥
page.label.workflow.history=ã¯ã¼ã¯ãã­ã¼å±¥æ­´
page.label.workflow=ã¯ã¼ã¯ãã­ã¼
page.label.workgroup=ã¯ã¼ã¯ã°ã«ã¼ã
page.label.workgroupoverview=ã¯ã¼ã¯ã°ã«ã¼ãæ¦è¦
page.label.workgroups=ã¯ã¼ã¯ã°ã«ã¼ã
page.label.working.copy=ä½æ¥­ã³ãã¼
page.label.working=ä½æ¥­ä¸­
page.label.xml.file.name=XML ãã¡ã¤ã«å
page.label.xml.tag.break.indicator=åºåãã¤ã³ã¸ã±ã¼ã¿ã¼
page.label.xml.tag.start.data.group=ãã¼ã¿ ã°ã«ã¼ãã®éå§
page.label.yes.short=ã¯ã
page.label.yes=ã¯ã
page.label.zero.based=0 ãã¼ã¹
page.label.zone.marker.style=ã¾ã¼ã³ ãã¼ã«ã¼ ã¹ã¿ã¤ã«
page.label.zone=ã¾ã¼ã³
page.label.zoneassociations=ã¾ã¼ã³ã®é¢é£ä»ã
page.label.zonepartlabel=ã¾ã¼ã³ ãã¼ã ã©ãã«
page.label.zones.without.datagroup.definition=DataGroup å®ç¾©ã®ãªãã¾ã¼ã³
page.label.zones=ã¾ã¼ã³
page.lable.always.create.duplicates=Â Â Â Â Â Â å¸¸ã«è¤è£½ãä½æãã
page.lable.job.number=ã¸ã§ãçªå·
page.lable.run.diagnostics=è¨ºæ­ã®å®è¡
page.locale.afrikaans.za=ã¢ããªã«ã¼ã³ã¹èª (åã¢ããªã«)
page.locale.afrikaans=ã¢ããªã«ã¼ã³ã¹èª
page.locale.albanian.al=ã¢ã«ããã¢èª (ã¢ã«ããã¢)
page.locale.albanian=ã¢ã«ããã¢èª
page.locale.arabic.ae=ã¢ã©ãã¢èª (ã¢ã©ãé¦é·å½é£é¦)
page.locale.arabic.bh=ã¢ã©ãã¢èª (ãã¼ã¬ã¼ã³)
page.locale.arabic.dz=ã¢ã©ãã¢èª (ã¢ã«ã¸ã§ãªã¢)
page.locale.arabic.eg=ã¢ã©ãã¢èª (ã¨ã¸ãã)
page.locale.arabic.iq=ã¢ã©ãã¢èª (ã¤ã©ã¯)
page.locale.arabic.jo=ã¢ã©ãã¢èª (ã¨ã«ãã³)
page.locale.arabic.kw=ã¢ã©ãã¢èª (ã¯ã¦ã§ã¼ã)
page.locale.arabic.lb=ã¢ã©ãã¢èª (ã¬ããã³)
page.locale.arabic.ly=ã¢ã©ãã¢èª (ãªãã¢)
page.locale.arabic.ma=ã¢ã©ãã¢èª (ã¢ã­ãã³)
page.locale.arabic.om=ã¢ã©ãã¢èª (ãªãã¼ã³)
page.locale.arabic.qa=ã¢ã©ãã¢èª (ã«ã¿ã¼ã«)
page.locale.arabic.sa=ã¢ã©ãã¢èª (ãµã¦ã¸ã¢ã©ãã¢)
page.locale.arabic.sy=ã¢ã©ãã¢èª (ã·ãªã¢)
page.locale.arabic.tn=ã¢ã©ãã¢èª (ãã¥ãã¸ã¢)
page.locale.arabic.ye=ã¢ã©ãã¢èª (ã¤ã¨ã¡ã³)
page.locale.arabic=ã¢ã©ãã¢èª
page.locale.basque.eu=ãã¹ã¯èª (ãã¹ã¯)
page.locale.basque=ãã¹ã¯èª
page.locale.bosnian.ba=ãã¹ãã¢èª (ãã¹ãã¢ - ã©ãã³)
page.locale.bosnian=ãã¹ãã¢èª
page.locale.breton.fr=ãã«ãã³èª (ãã©ã³ã¹)
page.locale.breton=ãã«ãã³èª
page.locale.cantonese.hk=åºæ±èª (é¦æ¸¯)
page.locale.cantonese.mo=åºæ±èª (ãã«ãª)
page.locale.cantonese=åºæ±èª
page.locale.catalan.ad=ã«ã¿ã­ãã¢èª (ã«ã¿ã­ãã¢)
page.locale.catalan=ã«ã¿ã­ãã¢èª
page.locale.chinese.cn=ä¸­å½èª (ä¸­å½)
page.locale.chinese.hk=ä¸­å½èª (é¦æ¸¯)
page.locale.chinese.mo=ä¸­å½èª (ãã«ãª)
page.locale.chinese.sg=ä¸­å½èª (ã·ã³ã¬ãã¼ã«)
page.locale.chinese.tw=ä¸­å½èª (å°æ¹¾)
page.locale.chinese=ä¸­å½èª
page.locale.croatian.hr=ã¯ã­ã¢ãã¢èª (ã¯ã­ã¢ãã¢)
page.locale.croatian=ã¯ã­ã¢ãã¢èª
page.locale.czech.cz=ãã§ã³èª (ãã§ã³å±åå½)
page.locale.czech=ãã§ã³èª
page.locale.danish.dk=ãã³ãã¼ã¯èª (ãã³ãã¼ã¯)
page.locale.danish=ãã³ãã¼ã¯èª
page.locale.dutch.be=ãªã©ã³ãèª (ãã«ã®ã¼)
page.locale.dutch.nl=ãªã©ã³ãèª (ãªã©ã³ã)
page.locale.dutch=ãªã©ã³ãèª
page.locale.english.au=è±èª (ãªã¼ã¹ãã©ãªã¢)
page.locale.english.bs=è±èª (ã«ãªã)
page.locale.english.bz=è±èª (ããªã¼ãº)
page.locale.english.ca=è±èª (ã«ãã)
page.locale.english.ie=è±èª (ã¢ã¤ã«ã©ã³ã)
page.locale.english.in=è±èª (ã¤ã³ã)
page.locale.english.jm=è±èª (ã¸ã£ãã¤ã«)
page.locale.english.my=è±èª (ãã¬ã¼ã·ã¢)
page.locale.english.nz=è±èª (ãã¥ã¼ã¸ã¼ã©ã³ã)
page.locale.english.ph=è±èª (ãã£ãªãã³)
page.locale.english.sg=è±èª (ã·ã³ã¬ãã¼ã«)
page.locale.english.tt=è±èª (ããªããã¼ãã»ããã´)
page.locale.english.uk=è±èª (UK)
page.locale.english.us=è±èª (US)
page.locale.english.za=è±èª (åã¢ããªã«)
page.locale.english.zw=è±èª (ã¸ã³ããã¨)
page.locale.english=è±èª
page.locale.estonian.ee=ã¨ã¹ããã¢èª (ã¨ã¹ããã¢)
page.locale.estonian=ã¨ã¹ããã¢èª
page.locale.faroese.fo=ãã§ã­ã¼èª (ãã§ã­ã¼è«¸å³¶)
page.locale.faroese=ãã§ã­ã¼èª
page.locale.finnish.fi=ãã£ã³ã©ã³ãèª (ãã£ã³ã©ã³ã)
page.locale.finnish=ãã£ã³ã©ã³ãèª
page.locale.french.be=ãã©ã³ã¹èª (ãã«ã®ã¼)
page.locale.french.ca=ãã©ã³ã¹èª (ã«ãã)
page.locale.french.ch=ãã©ã³ã¹èª (ã¹ã¤ã¹)
page.locale.french.fr=ãã©ã³ã¹èª (ãã©ã³ã¹)
page.locale.french.lu=ãã©ã³ã¹èª (ã«ã¯ã»ã³ãã«ã¯)
page.locale.french.mc=ãã©ã³ã¹èª (ã¢ãã³)
page.locale.french.nl=ãã©ã³ã¹èª (ãªã©ã³ã)
page.locale.french=ãã©ã³ã¹èª
page.locale.galician.gl=ã¬ãªã·ã¢èª (ã¬ãªã·ã¢)
page.locale.galician=ã¬ãªã·ã¢èª
page.locale.german.at=ãã¤ãèª (ãªã¼ã¹ããªã¢)
page.locale.german.ch=ãã¤ãèª (ã¹ã¤ã¹)
page.locale.german.de=ãã¤ãèª (ãã¤ã)
page.locale.german.li=ãã¤ãèª (ãªããã³ã·ã¥ã¿ã¤ã³)
page.locale.german.lu=ãã¤ãèª (ã«ã¯ã»ã³ãã«ã°)
page.locale.german=ãã¤ãèª
page.locale.hebrew.il=ããã©ã¤èª (ã¤ã¹ã©ã¨ã«)
page.locale.hebrew=ããã©ã¤èª
page.locale.hindi.in=ãã³ãã£ã¼èª (ã¤ã³ã)]
page.locale.hindi=ãã³ãã£ã¼èª
page.locale.hungarian.hu=ãã³ã¬ãªã¼èª (ãã³ã¬ãªã¼)
page.locale.hungarian=ãã³ã¬ãªã¼èª
page.locale.icelandic.is=ã¢ã¤ã¹ã©ã³ãèª (ã¢ã¤ã¹ã©ã³ã)
page.locale.icelandic=ã¢ã¤ã¹ã©ã³ãèª
page.locale.ilokano.ph=ã¤ã­ã«ãèª (ãã£ãªãã³)
page.locale.ilokano=ã¤ã­ã«ãèª
page.locale.indonesian.id=ã¤ã³ããã·ã¢èª (ã¤ã³ããã·ã¢)
page.locale.indonesian=ã¤ã³ããã·ã¢èª
page.locale.irish.ie=ã¢ã¤ã«ã©ã³ãèª (ã¢ã¤ã«ã©ã³ã)
page.locale.irish=ã¢ã¤ã«ã©ã³ãèª 
page.locale.italian.ch=ã¤ã¿ãªã¢èª (ã¹ã¤ã¹)
page.locale.italian.it=ã¤ã¿ãªã¢èª (ã¤ã¿ãªã¢)
page.locale.italian=ã¤ã¿ãªã¢èª
page.locale.japanese.jp=æ¥æ¬èª (æ¥æ¬)
page.locale.japanese=æ¥æ¬èª
page.locale.korean.kr=éå½èª (éå½)
page.locale.korean=éå½èª
page.locale.luxembourgish.lu=ã«ã¯ã»ã³ãã«ã¯èª (ã«ã¯ã»ã³ãã«ã¯)
page.locale.luxembourgish=ã«ã¯ã»ã³ãã«ã¯èª
page.locale.malay.bn=ãã¬ã¼èª (ãã«ãã¤)
page.locale.malay.my=ãã¬ã¼èª (ãã¬ã¼ã·ã¢)
page.locale.malay=ãã¬ã¼èª
page.locale.norwegian.nb=ãã«ã¦ã§ã¼èª (ãã¼ã¯ã¢ã¼ã«)
page.locale.norwegian.nn=ãã«ã¦ã§ã¼èª (ãã¼ãã·ã¥ã¯)
page.locale.norwegian=ãã«ã¦ã§ã¼èª
page.locale.polish.pl=ãã¼ã©ã³ãèª (ãã¼ã©ã³ã)
page.locale.polish=ãã¼ã©ã³ãèª
page.locale.portuguese.br=ãã«ãã¬ã«èª (ãã©ã¸ã«)
page.locale.portuguese.pt=ãã«ãã¬ã«èª (ãã«ãã¬ã«)
page.locale.portuguese=ãã«ãã¬ã«èª
page.locale.punjabi.in=ãã³ã¸ã£ãèª (ã¤ã³ã)
page.locale.punjabi=ãã³ã¸ã£ãèª
page.locale.romanian.ro=ã«ã¼ããã¢èª (ã«ã¼ããã¢)
page.locale.romanian=ã«ã¼ããã¢èª
page.locale.russian.ru=ã­ã·ã¢èª (ã­ã·ã¢)
page.locale.russian=ã­ã·ã¢èª
page.locale.scottish.gaelic.bg=ã¹ã³ããã©ã³ã ã²ã¼ã«èª (ã¤ã®ãªã¹)
page.locale.scottish.gaelic=ã¹ã³ããã©ã³ã ã²ã¼ã«èª
page.locale.serbian.ba=ã»ã«ãã¢èª (ãã¹ãã¢ - ã©ãã³)
page.locale.serbian.cs=ã»ã«ãã¢èª (ã¢ã³ããã°ã­ - ã©ãã³)
page.locale.serbian.sp=ã»ã«ãã¢èª (ã»ã«ãã¢ - ã©ãã³)
page.locale.serbian=ã»ã«ãã¢èª
page.locale.slovak.sk=ã¹ã­ãã­ã¢èª (ã¹ã­ãã­ã¢)
page.locale.slovak=ã¹ã­ãã­ã¢èª
page.locale.slovenian.si=ã¹ã­ããã¢èª (ã¹ã­ããã¢)
page.locale.slovenian=ã¹ã­ããã¢èª
page.locale.spanish.ar=ã¹ãã¤ã³èª (ã¢ã«ã¼ã³ãã³)
page.locale.spanish.bo=ã¹ãã¤ã³èª (ããªãã¢)
page.locale.spanish.cl=ã¹ãã¤ã³èª (ããª)
page.locale.spanish.co=ã¹ãã¤ã³èª (ã³ã­ã³ãã¢)
page.locale.spanish.cr=ã¹ãã¤ã³èª (ã³ã¹ã¿ãªã«)
page.locale.spanish.do=ã¹ãã¤ã³èª (ãããã«å±åå½)
page.locale.spanish.ec=ã¹ãã¤ã³èª (ã¨ã¯ã¢ãã«)
page.locale.spanish.es=ã¹ãã¤ã³èª (ã¹ãã¤ã³)
page.locale.spanish.gt=ã¹ãã¤ã³èª (ã°ã¢ããã©)
page.locale.spanish.hn=ã¹ãã¤ã³èª (ãã³ã¸ã¥ã©ã¹)
page.locale.spanish.mx=ã¹ãã¤ã³èª (ã¡ã­ã·ã³)
page.locale.spanish.ni=ã¹ãã¤ã³èª (ãã«ã©ã°ã¢)
page.locale.spanish.pa=ã¹ãã¤ã³èª (ããã)
page.locale.spanish.pe=ã¹ãã¤ã³èª (ãã«ã¼)
page.locale.spanish.pr=ã¹ãã¤ã³èª (ãã¨ã«ããªã³)
page.locale.spanish.py=ã¹ãã¤ã³èª (ãã©ã°ã¢ã¤)
page.locale.spanish.sv=ã¹ãã¤ã³èª (ã¨ã«ãµã«ããã«)
page.locale.spanish.us=ã¹ãã¤ã³èª (ç±³å½)
page.locale.spanish.uy=ã¹ãã¤ã³èª (ã¦ã«ã°ã¢ã¤)
page.locale.spanish.ve=ã¹ãã¤ã³èª (ãããºã¨ã©)
page.locale.spanish=ã¹ãã¤ã³èª
page.locale.swahili.ke=ã¹ã¯ããªèª (ã±ãã¢)
page.locale.swahili.km=ã¹ã¯ããªèª (ã³ã¢ã­)
page.locale.swahili.tz=ã¹ã¯ããªèª (ã¿ã³ã¶ãã¢)
page.locale.swahili.ug=ã¹ã¯ããªèª (ã¦ã¬ã³ã)
page.locale.swahili=ã¹ã¯ããªèª
page.locale.swedish.fi=ã¹ã¦ã§ã¼ãã³èª (ãã£ã³ã©ã³ã)
page.locale.swedish.se=ã¹ã¦ã§ã¼ãã³èª (ã¹ã¦ã§ã¼ãã³)
page.locale.swedish=ã¹ã¦ã§ã¼ãã³èª
page.locale.tamil.in=ã¿ãã¼ã«èª (ã¤ã³ã)
page.locale.tamil=ã¿ãã¼ã«èª
page.locale.thai.th=ã¿ã¤èª (ã¿ã¤)
page.locale.thai=ã¿ã¤èª
page.locale.ukrainian.ua=ã¦ã¯ã©ã¤ãèª (ã¦ã¯ã©ã¤ã)
page.locale.ukrainian=ã¦ã¯ã©ã¤ãèª
page.locale.vietnamese.vn=ãããã èª (ãããã )
page.locale.vietnamese=ãããã èª
page.locale.wallon.be=ã¯ã­ã³èª (ãã«ã®ã¼)
page.locale.wallon.fr=ã¯ã­ã³èª (ãã©ã³ã¹)
page.locale.wallon=ã¯ã­ã³èª
page.message.messagecontent.history.title=ã¡ãã»ã¼ã¸ ã³ã³ãã³ãå±¥æ­´
page.option.select.data.source=-- ãã¼ã¿ ã½ã¼ã¹ã®é¸æ
page.table.newpassword.information=æ°è¦ãã¹ã¯ã¼ãã®æå ±
page.table.newuserid.information=æ°è¦ã¦ã¼ã¶ã¼åã®æå ±
page.table.newuseridpassword.information=æ°è¦ã¦ã¼ã¶ã¼åã¨ãã¹ã¯ã¼ãã®æå ±
page.text.0.lowest.9999.highest=(0 æä½ : 9999 æé«)
page.text.APPLICATION.SIGN.OUT=ã¢ããªã±ã¼ã·ã§ã³ ãµã¤ã³ã¢ã¦ã
page.text.CHILD.LINKED.MESSAGES=ãªã³ã¯ãããå­ã¡ãã»ã¼ã¸
page.text.DEFAULT.SCHEDULE.LAYOUT=ã¿ãããã¤ã³ãã®ããã©ã«ã ã¹ã±ã¸ã¥ã¼ã« ã¬ã¤ã¢ã¦ã
page.text.KEYWORDS=ã­ã¼ã¯ã¼ã
page.text.NAME=åå
page.text.ON.HOLD=(ä¿çä¸­)
page.text.OTHERWISE=ããä»¥å¤ã®å ´å
page.text.SEARCH=æ¤ç´¢
page.text.SHARED.FROM=å±æå
page.text.SUPPRESSED=(éè¡¨ç¤º)
page.text.TIMING=ã¿ã¤ãã³ã°
page.text.Unlimted=ç¡å¶é
page.text.activate.messages=ã¡ãã»ã¼ã¸ãã¢ã¯ãã£ãã«ãã¾ãã?
page.text.activate.selected.tags=é¸æããã¿ã°ãã¢ã¯ãã£ãã«ãã¾ãã?
page.text.activate.smart.texts=ã¹ãã¼ã ãã­ã¹ããã¢ã¯ãã£ãã«ãã¾ãã?
page.text.activate.this.message=ãã®ã¡ãã»ã¼ã¸ãã¢ã¯ãã£ãã«ãã¾ãã?
page.text.activate.variants=ããªã¢ã³ããã¢ã¯ãã£ãã«ãã¾ãã?
page.text.active.insert.schedules=ã¢ã¯ãã£ãæ¿å¥ã¹ã±ã¸ã¥ã¼ã«
page.text.active.inserts=ã¢ã¯ãã£ãæ¿å¥
page.text.active.messages=ã¢ã¯ãã£ã ã¡ãã»ã¼ã¸
page.text.active.tags=ã¢ã¯ãã£ã ã¿ã°
page.text.add.insert.to.favorites.folder=ãæ°ã«å¥ããã©ã«ãã¼ã«é¸æããæ¿å¥ãè¿½å ãã¾ãã?
page.text.add.inserts.to.favorites.folder=ãæ°ã«å¥ããã©ã«ãã¼ã«é¸æããæ¿å¥ãè¿½å ãã¾ãã?
page.text.add.or.import.touchpoint=ã·ã¹ãã åã«ã¿ãããã¤ã³ããå­å¨ãã¾ãããæåã«ã¿ãããã¤ã³ããè¿½å ã¾ãã¯ã¤ã³ãã¼ããã¦ãã ããã
page.text.add.rate.sheet.to.collection=ç¾å¨é¸æãã¦ããæéè¡¨ã«é¢é£ä»ããããã³ã¬ã¯ã·ã§ã³ã«æéè¡¨ãè¿½å ãã¾ãã?
page.text.add.rate.sheets=1 ã¾ãã¯è¤æ°ã®æéè¡¨ã®è¿½å 
page.text.add.section=ã»ã¯ã·ã§ã³ã®è¿½å 
page.text.add.selection.under.selected.item=é¸æããã¢ã¤ãã ã®ä¸ã«æ°è¦é¸æãè¿½å ãã¾ãã?
page.text.add.variant.data=ããªã¢ã³ã ãã¼ã¿ã®è¿½å 
page.text.add.variant.under.selected.item=é¸æããã¢ã¤ãã ã®ä¸ã«æ°è¦ããªã¢ã³ããè¿½å ãã¾ãã?
page.text.advanced.content.search=é«åº¦ãªã³ã³ãã³ãæ¤ç´¢
page.text.all.but.first.line.indent=æåã®è¡ãé¤ããã¹ã¦ (ã¶ãä¸ãã¤ã³ãã³ã)
page.text.all.image.variants.have.data=ãã¹ã¦ã®åçç»åããªã¢ã³ãã«ã»ã¬ã¯ã¿ã¼ ãã¼ã¿ãããã¾ãã?
page.text.all.message.variants.have.data=ãã¹ã¦ã®åçã¡ãã»ã¼ã¸ ããªã¢ã³ãã«ã»ã¬ã¯ã¿ã¼ ãã¼ã¿ãããã¾ãã?
page.text.all.node.data.will.be.erased=éè¦! ä¸è¨ã®ãªãã·ã§ã³ãé¸æããå ´åãã¤ã³ã¹ã¿ã³ã¹ã®ãã¼ã¿ã¯æ¶å»ããã¾ãããã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.all.of=ãã¹ã¦
page.text.all.primary.variables=ãã¹ã¦ã®ãã©ã¤ããªå¤æ° (ãããã³ã°ã«ç¡é¢ä¿)
page.text.all.records.have.data.group=ãã¹ã¦ã®ãã¼ã¿ ã½ã¼ã¹ ã¬ã³ã¼ãã§ãã¼ã¿ ã°ã«ã¼ããé¸ææ¸ã¿ã§ãã?
page.text.all.records.have.starting.record=ãã¹ã¦ã®ãã¼ã¿ ã°ã«ã¼ãã§éå§ã¬ã³ã¼ããé¸ææ¸ã¿ã§ãã?
page.text.all.reference.variables=ãã¹ã¦ã®åç§å¤æ° (ãããã³ã°ã«ç¡é¢ä¿)
page.text.all.referenced.variables.have.data.element=åç§ããããã¹ã¦ã®å¤æ°ã§ãã¼ã¿è¦ç´ ãé¸ææ¸ã¿ã§ãã?
page.text.all.smart.text.variants.have.data=ãã¹ã¦ã®åçã¹ãã¼ã ãã­ã¹ã ããªã¢ã³ãã«ã»ã¬ã¯ã¿ã¼ ãã¼ã¿ãããã¾ãã?
page.text.all.touchpoint.variants.have.data=ãã¹ã¦ã®åçã¿ãããã¤ã³ã ããªã¢ã³ãã«ã»ã¬ã¯ã¿ã¼ ãã¼ã¿ãããã¾ãã?
page.text.all.touchpoint.variants.have.visibility=ãã¹ã¦ã®ã¿ãããã¤ã³ã ããªã¢ã³ããå°ãªãã¨ã 1 äººã®ã¦ã¼ã¶ã¼è¡¨ç¤ºããã¾ãã?
page.text.all.used.primary.variables=ä½¿ç¨ä¸­ã®ãããã³ã°ããã¦ããªããã©ã¤ããªå¤æ°
page.text.all.used.reference.variables=ä½¿ç¨ä¸­ã®ãããã³ã°ããã¦ããªãåç§å¤æ°
page.text.all.variant.content.will.be.replaced=æ³¨: ã¿ã¼ã²ãã ããªã¢ã³ãã®ãã¹ã¦ã®ã³ã³ãã³ããç½®ãæãããã¾ãã
page.text.all.zone.have.data.group=ãã¹ã¦ã®ã¾ã¼ã³ã§ãã¼ã¿ ã°ã«ã¼ããé¸ææ¸ã¿ã§ãã?
page.text.all.zone.have.visibility=ãã¹ã¦ã®ã¢ã¯ãã£ã ã¾ã¼ã³ãå°ãªãã¨ã 1 ã¤ã®ã¯ã¼ã¯ã°ã«ã¼ãã«è¡¨ç¤ºããã¾ãã?
page.text.all.zones=ãã¹ã¦ã®ã¾ã¼ã³
page.text.and.of.these.include=ãããã®åãä»¥ä¸ãå«ãã
page.text.any.of=ãããã§ã
page.text.applies.alternate.template=ä»£æ¿ãã³ãã¬ã¼ãã®é©ç¨
page.text.applies.template.modifiers=ãã³ãã¬ã¼ã ã¢ãã£ãã¡ã¤ã¢ã¼ã®é©ç¨
page.text.apply.default=(ããã©ã«ãã®é©ç¨)
page.text.approval.from=æ¿èªå
page.text.approval.required.from=æ¿èªè¦æ±å
page.text.approval.timeframe.is=æ¿èªæé
page.text.approve.asset=ãã®ã¢ã»ãããæ¿èªãã¾ãã?
page.text.approve.insert.schedule=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ãæ¿èªãã¾ãã?
page.text.approve.insert=æ¿å¥ãæ¿èªãã¾ãã?
page.text.approve.this.asset=ãã®ã¢ã»ãããæ¿èªãã¾ãã?
page.text.approve.this.image=ãã®ç»åãæ¿èªãã¾ãã?
page.text.archive.active.asset=é¸æããã¢ã¯ãã£ã ã¢ã»ããã®ã¢ã¼ã«ã¤ããè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã«ãã£ã¦é¢é£ããä½æ¥­ã³ãã¼ãå¤æ´ããããã¨ã¯ããã¾ããã
page.text.archive.active.image=é¸æããã¢ã¯ãã£ãç»åã®ã¢ã¼ã«ã¤ããè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã«ãã£ã¦é¢é£ããä½æ¥­ã³ãã¼ãå¤æ´ããããã¨ã¯ããã¾ããã
page.text.archive.active.smart.text=é¸æããã¢ã¯ãã£ã ã¹ãã¼ã ãã­ã¹ãã®ã¢ã¼ã«ã¤ããè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã«ãã£ã¦é¢é£ããä½æ¥­ã³ãã¼ãå¤æ´ããããã¨ã¯ããã¾ããã
page.text.archive.message.active=é¸æããã¢ã¯ãã£ã ã¢ã»ããã®ã¢ã¼ã«ã¤ããè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã«ãã£ã¦é¢é£ããä½æ¥­ã³ãã¼ãå¤æ´ããããã¨ã¯ããã¾ããããªã³ã¯ãããã¡ãã»ã¼ã¸ (å­ã¾ãã¯è¦ª) ãå«ãé¢é£ãããªã³ã¯ã¯åé¤ããã¾ãã
page.text.archive.selected.active.inserts=é¸æããã¢ã¯ãã£ãæ¿å¥ã®ã¢ã¼ã«ã¤ããè¦æ±ããã¾ããã
page.text.archive.selected.active.messages=é¸æããã¢ã¯ãã£ã ã¡ãã»ã¼ã¸ã®ã¢ã¼ã«ã¤ããè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã«ãã£ã¦é¢é£ããä½æ¥­ã³ãã¼ãå¤æ´ããããã¨ã¯ããã¾ããããªã³ã¯ãããã¡ãã»ã¼ã¸ (å­ã¾ãã¯è¦ª) ãå«ãé¢é£ãããªã³ã¯ã¯åé¤ããã¾ãã
page.text.are.you.sure.you.want.to.delete.brach=ãã®ãã¡ã¤ã³ãåé¤ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.are.you.sure=ããããã§ãã?
page.text.asked.to.discard.working.copy.with.no.active.copy=é¸æããããªã¢ã³ãã®ä½æ¥­ã³ãã¼ã®å»æ£ãè¦æ±ããã¾ãããããã«ã¯ã¢ã¯ãã£ã ã³ãã¼ãå­å¨ãã¾ããã
page.text.asset=ã¢ã»ãã
page.text.assign.a.insert.schedule.to.user=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ãå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æããå¿è¦ãªã¢ã¯ã·ã§ã³ã®èª¬æãå¥åãã¾ãã
page.text.assign.deactivated.insert.schedule.to.user=éã¢ã¯ãã£ãåãããæ¿å¥ã¹ã±ã¸ã¥ã¼ã«ãå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æãã¾ãã
page.text.assign.deactivated.insert.schedule=éã¢ã¯ãã£ãåãããæ¿å¥ã¹ã±ã¸ã¥ã¼ã«ãå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æãã¾ãã
page.text.assign.insert.schedule.to.user=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ãå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æããå¿è¦ãªã¢ã¯ã·ã§ã³ã®èª¬æãå¥åãã¾ãã
page.text.assign.insert.to.user=æ¿å¥ãå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æããå¿è¦ãªã¢ã¯ã·ã§ã³ã®èª¬æãå¥åãã¾ãã
page.text.assign.message.to.user=ãã®ã¡ãã»ã¼ã¸ãå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æããå¿è¦ãªã¢ã¯ã·ã§ã³ã®èª¬æãå¥åãã¾ãã
page.text.assign.tag.to.user=ã¿ã°ãå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æããå¿è¦ãªã¢ã¯ã·ã§ã³ã®èª¬æãå¥åãã¾ãã
page.text.assign.user.to.tags=ã¿ã°ãå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æããå¿è¦ãªã¢ã¯ã·ã§ã³ã®èª¬æãå¥åãã¾ãã
page.text.attachment.has.not.been.specified=æ·»ä»ãã¡ã¤ã«ãæå®ããã¦ãã¾ãã
page.text.attachment.name.should.include.file.extension=æ·»ä»ãã¡ã¤ã«åã«ã¯ãã¡ã¤ã«æ¡å¼µå­ãå«ãã¦ãã ãã (ä¾: clientAttachment.pdf)
page.text.auto.approval.on.timeout=ã¿ã¤ã ã¢ã¦ãæã«èªåæ¿èª
page.text.auto.approve.on.expiry=æå¹æéãããã¨ãã«èªåçã«æ¿èª
page.text.bin.assignments.not.permitted.for.tenant.managed.schedule=ããã³ããç®¡çããã¹ã±ã¸ã¥ã¼ã«ã§ãã³å²ãå½ã¦ãè¨±å¯ããã¦ãã¾ãã
page.text.bins=ãã³
page.text.by=æ¹æ³
page.text.cancel.simulations=é¸æããã·ãã¥ã¬ã¼ã·ã§ã³ãã­ã£ã³ã»ã«ãã¦ãããããã§ãã?
page.text.channel.selection.required=ãã£ãã«é¸æãå¿è¦
page.text.clear.background.image=èæ¯ç»åã®ã¯ãªã¢
page.text.click.bin.to.toggle.availability=(ãã³ãã¯ãªãã¯ãã¦ãå¯ç¨æ§ãã¹ãã¼ã¿ã¹ãåãæ¿ãã¾ã)
page.text.click.bin.to.toggle.tenant.access=(ãã³ãã¯ãªãã¯ãã¦ãããã³ããã¢ã¯ã»ã¹ãåãæ¿ãã¾ã)
page.text.click.button.to.add.items=ä¸ã®ãã¿ã³ãã¯ãªãã¯ãã¦ã¢ã¤ãã ãè¿½å ãã¾ã
page.text.click.button.to.add.steps=ä¸ã®ãã¿ã³ãã¯ãªãã¯ãã¦ã¹ããããè¿½å ãã¾ã
page.text.click.here.small=ãããã¯ãªãã¯
page.text.click.here=ãããã¯ãªãã¯
page.text.click.to.view=ã¯ãªãã¯ãã¦è¡¨ç¤º
page.text.click=ã¯ãªãã¯
page.text.clone.communication=ç¾å¨é¸æãã¦ããã³ãã¥ãã±ã¼ã·ã§ã³ãè¤è£½ãã¾ãã?
page.text.clone.selected.message=ç¾å¨é¸æãã¦ããã¡ãã»ã¼ã¸ãè¤è£½ãã¾ãã?
page.text.comments.and.suggestions.HP=ãæè¦ããã³ãææ¡ã¯ <a href=\"mailto:<EMAIL>\"><EMAIL></a> å®ã«é»å­ã¡ã¼ã«ã§ãéããã ãã
page.text.comments.and.suggestions=ãæè¦ããã³ãææ¡ã¯ <a href=\"mailto:<EMAIL>\"><EMAIL></a> å®ã«é»å­ã¡ã¼ã«ã§ãéããã ãã
page.text.communication.data.resource.selected=ã³ãã¥ãã±ã¼ã·ã§ã³ã®ãã¼ã¿ ãªã½ã¼ã¹ã¯é¸ææ¸ã¿ã§ãã?
page.text.communications.which.are=æ¬¡ã®ç¶æã®ã³ãã¥ãã±ã¼ã·ã§ã³
page.text.composition.package.set.for.touchpoint=ã¿ãããã¤ã³ãã«å¯¾ããã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸ã¯é¸ææ¸ã¿ã§ãã?
page.text.compound.smart.text=è¤åã¹ãã¼ã ãã­ã¹ã
page.text.conditions.from.this.rule=ã¿ã¼ã²ãã ã°ã«ã¼ããä½æããéã®ãã®ã«ã¼ã«ã®æ¡ä»¶ã
page.text.conditions.must.pass.for.rule.to.qualify=ãã®ã«ã¼ã«ãé©æ ¼ã¨ãªãã«ã¯ããããã®æ¡ä»¶ããã¹ããå¿è¦ãããã¾ã
page.text.confirm.cancel=ã­ã£ã³ã»ã«ãã¦ãããããã§ãã
page.text.confirm.delete=åé¤ãã¦ãããããã§ãã
page.text.confirm.user.workgroup.change=é¸æããã¦ã¼ã¶ã¼ãè¦æ±ããã¯ã¼ã¯ã°ã«ã¼ãã«ç§»åããã¨ãåã®ã¯ã¼ã¯ã°ã«ã¼ãã«ã¦ã¼ã¶ã¼ãå­å¨ããªããªãã¾ããç¢ºèªãã¦ãã ããã
page.text.connected.not.enabled=ã¿ãããã¤ã³ããæ¥ç¶æ¸ã¿ç¨ã«æå¹ã«ãªã£ã¦ãã¾ããã
page.text.content.for=ã³ã³ãã³ãå¯¾è±¡
page.text.content.suppressed=ã³ã³ãã³ãéè¡¨ç¤º
page.text.content.type=ã³ã³ãã³ã ã¿ã¤ã
page.text.content=ã³ã³ãã³ã
page.text.copy.data.from.node.warning=éè¦! ç¾å¨å¼·èª¿è¡¨ç¤ºããã¦ããã¤ã³ã¹ã¿ã³ã¹åã®ãã¹ã¦ã®ãã¼ã¿ãåé¤ããã¾ãããã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.copy.data.from.node=ãã¼ã¿ã®ã³ãã¼åã®ã¤ã³ã¹ã¿ã³ã¹ãé¸æãã¾ãã
page.text.copy.images.to.path.location.specified=éè¦! ä¸è¨ã®ãªãã·ã§ã³ãé¸æããã¦ããå ´åãç»åãæå®ããããã¹ã«ã³ãã¼ããå¿è¦ãããã¾ãã
page.text.copyright.signin=MESSAGEPOINT<sup>Â®</sup> IS A PRODUCT OF PRINOVA INC.  ALL RIGHTS RESERVED Â© 2006-2014
page.text.copyright=Copyright Â© 2006-2014 Prinova Inc. All rights reserved.
page.text.create.message.working.copy=ãã®ã¢ã»ããã®ä½æ¥­ã³ãã¼ã®ä½æãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã«ãã£ã¦é¢é£ããã¢ã¯ãã£ã ã¡ãã»ã¼ã¸ãå¤æ´ããããã¨ã¯ããã¾ããã
page.text.create.variant.working.copies=ããªã¢ã³ãã®ä½æ¥­ã³ãã¼ãä½æãã¾ãã?
page.text.create.working.copies.of.selected.messages=é¸æããã¡ãã»ã¼ã¸ã®ä½æ¥­ã³ãã¼ã®ä½æãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã«ãã£ã¦é¢é£ããã¢ã¯ãã£ã ã¡ãã»ã¼ã¸ãå¤æ´ããããã¨ã¯ããã¾ããã
page.text.create.working.copies.of.selected.variants=é¸æããããªã¢ã³ãã®ä½æ¥­ã³ãã¼ã®ä½æãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã«ãã£ã¦é¢é£ããã¢ã¯ãã£ããªã¿ãããã¤ã³ãã®é¸æåå®¹ãå¤æ´ããããã¨ã¯ããã¾ããã
page.text.create.working.copy.of.selected.asset=é¸æããã¢ã»ããã®ä½æ¥­ã³ãã¼ã®ä½æãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã«ãã£ã¦é¢é£ããã¢ã¯ãã£ã ã¢ã»ãããå¤æ´ããããã¨ã¯ããã¾ããã
page.text.create.working.copy.of.selected.image=é¸æããç»åã®ä½æ¥­ã³ãã¼ã®ä½æãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã«ãã£ã¦é¢é£ããã¢ã¯ãã£ãç»åãå¤æ´ããããã¨ã¯ããã¾ããã
page.text.create.working.copy.of.selected.images=é¸æããç»åã®ä½æ¥­ã³ãã¼ã®ä½æãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã«ãã£ã¦é¢é£ããã¢ã¯ãã£ãç»åãå¤æ´ããããã¨ã¯ããã¾ããã
page.text.create.working.copy.of.selected.smart.texts=é¸æããã¹ãã¼ã ãã­ã¹ãã®ä½æ¥­ã³ãã¼ã®ä½æãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã«ãã£ã¦é¢é£ããã¢ã¯ãã£ã ã¹ãã¼ã ãã­ã¹ããå¤æ´ããããã¨ã¯ããã¾ããã
page.text.ctrl.click.to.select.multiple.values=(åãã¬ãã«ããè¤æ°ã®å¤ãé¸æããå ´åã¯ Ctrl ã­ã¼ãæ¼ããªããã¯ãªãã¯ãã¾ã)
page.text.currentlysignedinas=æ¬¡ã®ã¦ã¼ã¶ã¼ã¨ãã¦ãµã¤ã³ã¤ã³ãã¦ãã¾ã
page.text.custom=ã«ã¹ã¿ã 
page.text.customer_identifiers=é¡§å®¢ ID
page.text.data.collection.configurated.for.touchpoint=ã¿ãããã¤ã³ãã«å¯¾ãããã¼ã¿ ã³ã¬ã¯ã·ã§ã³ã¯æ§ææ¸ã¿ã§ãã?
page.text.data.file=ãã¼ã¿ ãã¡ã¤ã«
page.text.data.source.association.required=é¸æããã¿ãããã¤ã³ãã«å¯¾ãã¦ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³ãå®ç¾©ããã¦ãã¾ããã
page.text.days=æ¥æ°
page.text.deactivate.selected.branch=é¸æãããã¡ã¤ã³ã¯éã¢ã¯ãã£ãã«ãªãã¾ãã
page.text.deactivate.selected.tags=é¸æããã¿ã°ãéã¢ã¯ãã£ãã«ãã¾ãã?
page.text.deactivate.selected.user=é¸æããã¦ã¼ã¶ã¼ãéã¢ã¯ãã£ãã«ãã¾ãã?
page.text.default.content=ããã©ã«ã ã³ã³ãã³ã
page.text.default.schedule.layout.for.touchpoint=ã¿ãããã¤ã³ãã®ããã©ã«ã ã¹ã±ã¸ã¥ã¼ã« ã¬ã¤ã¢ã¦ã
page.text.default.select.and.edit.to.override=ããã©ã«ã: é¸æãã¦ç·¨éããªã¼ãã¼ã©ã¤ããã
page.text.default.task.list.empty=ããã©ã«ã ã¿ã¹ã¯ ãªã¹ããç©ºããã¢ã¯ã»ã¹ã§ãã¾ããã[ãªã¹ã] ã¡ãã¥ã¼ã§å¥ã®ãªãã·ã§ã³ãé¸æãã¦ãã ããã[ãªã¹ã] ã¡ãã¥ã¼ãè¡¨ç¤ºãããªãå ´åã¯ãç®¡çèã«åãåãããããå¥ã®ã¤ã³ã¹ã¿ã³ã¹ã«ã­ã°ã¤ã³ãã¦ãã ããã
page.text.delete.archived.asset=é¸æããã¢ã¼ã«ã¤ãæ¸ã¿ã¢ã»ãããåé¤ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.delete.archived.image=é¸æããã¢ã¼ã«ã¤ãæ¸ã¿ç»åãåé¤ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.delete.archived.smart.text=é¸æããã¢ã¼ã«ã¤ãæ¸ã¿ã¹ãã¼ã ãã­ã¹ããåé¤ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.delete.db.schema=DB ã¹ã­ã¼ããåé¤ãã
page.text.delete.language=é¸æããè¨èªãåé¤ãã¾ãã?
page.text.delete.message.archive=é¸æããã¢ã¼ã«ã¤ãæ¸ã¿ã¢ã»ãããåé¤ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.delete.part=ãã¼ãã®åé¤
page.text.delete.report=ãã®ã¬ãã¼ã ã·ããªãªãåé¤ãã¦ãããããã§ãã?
page.text.delete.reports=é¸æããã¬ãã¼ã ã·ããªãªãåé¤ãã¦ãããããã§ãã?
page.text.delete.selected.archived.messages=é¸æããã¢ã¼ã«ã¤ãæ¸ã¿ã¡ãã»ã¼ã¸ãåé¤ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.delete.selected.communications=é¸æããã³ãã¥ãã±ã¼ã·ã§ã³ãåé¤ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.delete.selected.composition.packages=é¸æããã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸ãåé¤ãã¾ãã?
page.text.delete.selected.constant=é¸æããå®æ°ãåé¤ãã¦ãããããã§ãã?
page.text.delete.selected.external.events=é¸æããå¤é¨ã¤ãã³ããåé¤ãã¦ãããããã§ãã?
page.text.delete.selected.inserts=é¸æããæ¿å¥ãåé¤ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.delete.selected.language.variant=é¸æããè¨èªã®é¸æãåé¤ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.delete.selected.licence=é¸æããã©ã¤ã»ã³ã¹ãåé¤ãã¦ãããããã§ãã?
page.text.delete.selected.paragraph.style=é¸æããæ®µè½ã¹ã¿ã¤ã«ãåé¤ãã¦ãããããã§ãã?
page.text.delete.selected.pod=é¸æããããããåé¤ãã¦ãããããã§ãã?
page.text.delete.selected.touchpoint.collections=é¸æããã¿ãããã¤ã³ã ã³ã¬ã¯ã·ã§ã³ãåé¤ãã¾ãã?
page.text.delete.selected.variable=é¸æããå¤æ°ãåé¤ãã¦ãããããã§ãã?
page.text.delete.selected.variants=é¸æããããªã¢ã³ããåé¤ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.delete.simulations=é¸æããã·ãã¥ã¬ã¼ã·ã§ã³ãåé¤ãã¦ãããããã§ãã?
page.text.delete.targeting.rule=é¸æããã¿ã¼ã²ããè¨­å®ã«ã¼ã«ãåé¤ãã¦ãããããã§ãã?
page.text.delete.test=ãã®ãã¹ã ã·ããªãªãåé¤ãã¦ãããããã§ãã?
page.text.delete.tests=é¸æãããã¹ã ã·ããªãªãåé¤ãã¦ãããããã§ãã?
page.text.delete.the.selected.variant=é¸æããããªã¢ã³ããåé¤ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.delete.this.insert=ãã®æ¿å¥ãåé¤ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.delete.touchpoint.attachment=ãã®ã¿ãããã¤ã³ãã®æ·»ä»ãã¡ã¤ã«ãåé¤ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.delete.variant=ããªã¢ã³ããåé¤ãã¾ãã?  é¢é£ä»ãããããã¹ã¦ã®ã³ã³ãã³ããå¤±ããã¾ãã
page.text.delete.zone=ã¾ã¼ã³ã®åé¤
page.text.delivery.error.part1=ãã®éä¿¡ã§ã¨ã©ã¼ãçºçãã¾ãã (ãªã¿ã¼ã³ ã³ã¼ã:Â 
page.text.delivery.error.part2=)ã<br />å¾ã§ããä¸åº¦ããç´ãã¦ãã ãã...<br />
page.text.delivery=éä¿¡
page.text.discard.insert.schedule=é¸æããæ¿å¥ã¹ã±ã¸ã¥ã¼ã«ãç ´æ£ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.discard.insert.schedules=é¸æããæ¿å¥ã¹ã±ã¸ã¥ã¼ã«ãç ´æ£ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã<br /><br />ããã³ãã®åå ãåé¤ããã¾ããé¢é£ããããã³ããããã®ã¹ã±ã¸ã¥ã¼ã«ã®è¡¨ç¤ºããã®ã¹ã±ã¸ã¥ã¼ã«ã¸ã®åå ã¯ã§ããªããªãã¾ãã
page.text.discard.message.working.copy=ã¡ãã»ã¼ã¸ã®ä½æ¥­ã³ãã¼ã®ç ´æ£ãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.discard.rate.sheet=æéè¡¨ãç ´æ£ãã¾ãã?
page.text.discard.rate.sheets=é¸æããæéè¡¨ãç ´æ£ãã¾ãã?
page.text.discard.selected.inserts=é¸æããæ¿å¥ãç ´æ£ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.discard.selected.tag.types=é¸æããã¿ã° ã¿ã¤ããç ´æ£ãã¾ãã?
page.text.discard.this.insert=ãã®æ¿å¥ãç ´æ£ãã¦ãããããã§ãã? ãã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.discard.variant.working.copies.with.no.active.copy=é¸æããããªã¢ã³ãã®ä½æ¥­ã³ãã¼ã®ç ´æ£ãè¦æ±ããã¾ãããããã«ã¯ã¢ã¯ãã£ã ã³ãã¼ãå­å¨ãã¾ããã
page.text.discard.variant.working.copies=é¸æããããªã¢ã³ãã®ä½æ¥­ã³ãã¼ã®ç ´æ£ãè¦æ±ããã¾ãããç¶è¡ãã¾ãã?
page.text.discard.working.copy.of.asset=ã¢ã»ããã®ä½æ¥­ã³ãã¼ã®ç ´æ£ãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.discard.working.copy.of.image=ç»åã®ä½æ¥­ã³ãã¼ã®ç ´æ£ãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.discard.working.copy.of.smart.text=ã¹ãã¼ã ãã­ã¹ãã®ä½æ¥­ã³ãã¼ã®ç ´æ£ãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.discontinue.rate.sheet=æéè¡¨ã®ä½¿ç¨ãåæ­¢ãã¾ãã?  [ææ°ã®æéè¡¨ãæ¨æ¥ä»ãã§çµäºã«è¨­å®ããã¾ã]
page.text.discontinue.rate.sheets=é¸æããæéè¡¨ã®ä½¿ç¨ãåæ­¢ãã¾ãã?  [åã·ãªã¼ãºã®ææ°ã®æéè¡¨ãæ¨æ¥ä»ãã§çµäºã«è¨­å®ããã¾ã]
page.text.disqualify=å¤±æ ¼ã«ãã
page.text.do.you.want.to.delete.branch=é¸æãããã¡ã¤ã³ã®å®å¨ãªåé¤ãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.do.you.want.to.delete.node=ãã®ã¤ã³ã¹ã¿ã³ã¹ãåé¤ãã¦ãããããã§ãã?
page.text.does.not.contain.any.data=ãã¼ã¿ãå«ã¾ãã¦ããªãããå­å¨ãã¾ãã
page.text.double.click.insert.to.jump.to.page.view=(æ¿å¥ã¢ã¤ã³ã³ãããã«ã¯ãªãã¯ããã¨ãé¢é£ããè¡¨ç¤ºãã¼ã¸ã®ã¸ã£ã³ããã¾ã)
page.text.drag.and.drop.rule.into.target.group=ããã«ã¯ãªãã¯ããããã«ã¼ã«ããã®ããã«ã«ãã©ãã°ã¢ã³ããã­ãããã¦ã¿ã¼ã²ãã ã°ã«ã¼ããä½æãã¾ãã
page.text.drag.and.drop.target.group.to.extend.targeting=ã¿ã¼ã²ãã ã°ã«ã¼ãããã®ããã«ä¸ã«ãã©ãã°ã¢ã³ããã­ãããã¦ããã®ã¢ã»ããã®ã¿ã¼ã²ããè¨­å®æ¡ä»¶ãæ¡å¼µãã¾ãã
page.text.drag.insert.to.or.from.bin=(æ¿å¥ãç®çã®ãã³åã¾ãã¯ãã³å¤ã«ãã©ãã°ãã)
page.text.draw.parts=ãã¼ãã®æç»
page.text.draw.zone=ã¾ã¼ã³ã®æç»
page.text.dynamic.smart.text=åçã¹ãã¼ã ãã­ã¹ã
page.text.edit.content.for=ã³ã³ãã³ãã®ç·¨éå¯¾è±¡
page.text.edit.zone=ã¾ã¼ã³ã®ç·¨é
page.text.email.server.settings.configured=é»å­ã¡ã¼ã« ãµã¼ãã¼è¨­å®ã¯æ§ææ¸ã¿ã§ãã?
page.text.empty.value=ç©ºã®ã¾ã¾ã«ãã
page.text.empty=ç©º
page.text.enabled=æå¹?
page.text.ending=çµäº
page.text.erase.db.schema=DB ã¹ã­ã¼ãã®ä½æã¾ãã¯åæ§ç¯ (å­å¨ããå ´å)
page.text.error.message=åé¡ãçºçããã¨ã©ã¼ãè¨é²ããã¾ãããåé¡ãè§£æ±ºããªãå ´åã¯ãç®¡çèã«åãåããã¦ãã ããã<br><br>ã¢ããªã±ã¼ã·ã§ã³ã«æ»ãã«ã¯ã<a href=\"javascript:window.top.location.href=''{0}''\">ãããã¯ãªãã¯</a>ãã¦ãã ããã
page.text.error=ã¨ã©ã¼
page.text.exact.target.settings.configured=Exact Target è¨­å®ã¯æ§ææ¸ã¿ã§ãã?
page.text.example.end.date.time=2013 å¹´ 10 æ 21 æ¥åå 10:00
page.text.example.end.date=10 æ 21 æ¥ (æææ¥)
page.text.example.end.time=åå 10:00
page.text.example.start.date.time=2013 å¹´ 10 æ 21 æ¥åå 10:00
page.text.example.start.date=10 æ 21 æ¥ (æææ¥)
page.text.example.start.time=åå 10:00
page.text.example.time.delta=2:45
page.text.example.time.zone=(EST)
page.text.exclude.groups=ã°ã«ã¼ãã®é¤å¤
page.text.existing.assignment=æ¢å­ã®å²ãå½ã¦
page.text.existing.modifiers.detected=æ¢å­ã®ã¢ãã£ãã¡ã¤ã¢ã¼ãæ¤åºããã¾ãã
page.text.existing.modifiers.not.detect.and.will.be.treated.as.removed=ã¢ããã­ã¼ãããããã±ã¼ã¸ã§æ¢å­ã®ã¢ãã£ãã¡ã¤ã¢ã¼ãæ¤åºã§ãã¾ããã§ããããããã¯åé¤ããããã®ã¨ãã¦å¦çããã¾ããMessagepoint åã§æ¬¡ã®ã¢ãã£ãã¡ã¤ã¢ã¼ã¯ç¡å¹ã«ãªãã¾ã:
page.text.existing.zones.detected=æ¢å­ã®ã¾ã¼ã³ãæ¤åºããã¾ãã
page.text.existing.zones.not.detect.and.will.be.treated.as.removed=ã¢ããã­ã¼ãããããã±ã¼ã¸ã§æ¢å­ã®ã¾ã¼ã³ãæ¤åºã§ãã¾ããã§ããããããã¯åé¤ããããã®ã¨ãã¦å¦çããã¾ããMessagepoint åã§æ¬¡ã®ã¾ã¼ã³ã¯ç¡å¹ã«ãªãã¾ã:
page.text.export.complete=ã¨ã¯ã¹ãã¼ããå®äºãã¾ããã[ä¿å­] ãã¯ãªãã¯ãã¦çµæãåå¾ãã¦ãã ããã
page.text.export.error=ã¨ã¯ã¹ãã¼ã ã¨ã©ã¼!
page.text.export.image.path=ç»åãã¹ã®ã¿ãã¨ã¯ã¹ãã¼ã
page.text.exported.simulation=ã¨ã¯ã¹ãã¼ãããã·ãã¥ã¬ã¼ã·ã§ã³
page.text.exporting.simulation.please.wait=ã·ãã¥ã¬ã¼ã·ã§ã³ãã¨ã¯ã¹ãã¼ããã¦ãã¾ãããå¾ã¡ãã ãã...
page.text.file.package.upload.for=ãã¡ã¤ã« ããã±ã¼ã¸ ã¢ããã­ã¼ãã®å¯¾è±¡
page.text.first.line.indent=æåã®è¡ (ã¤ã³ãã³ã)
page.text.fixed=åºå®
page.text.fixed_record_count=åºå®ã¬ã³ã¼ãæ°
page.text.flexible.touchpoints.do.not.permit.optional.delivery=ãã¬ã­ã·ãã« ã¿ãããã¤ã³ãã§ã¯ãã¹ã¿ã¼ ã¡ãã»ã¼ã¸ã®ãªãã·ã§ã³éä¿¡ã¯è¨±å¯ããã¾ããã
page.text.for.sheets.greater.than=ã·ã¼ãæ°ãæ¬¡ããå¤§ããå ´å
page.text.for.upload=ã¢ããã­ã¼ãç¨
page.text.for.you=ã¦ã¼ã¶ã¼ç¨
page.text.for=å¯¾è±¡
page.text.forgotyourpassword=ãã¹ã¯ã¼ãããå¿ãã§ãã?
page.text.freeform.canvas=ããªã¼ãã©ã¼ã  ã­ã£ã³ãã¹
page.text.generating.export.data=ã¨ã¯ã¹ãã¼ã ãã¼ã¿ãçæãã¦ãã¾ãããå¾ã¡ãã ãã...
page.text.has.completed.click.close=ãå®äºãã¾ããã[éãã] ãã¯ãªãã¯ãã¦ç¶è¡ãã¦ãã ããã
page.text.has.multiple.values.comma=ã«è¤æ°ã®å¤ãããã¾ãã
page.text.here=ãã
page.text.hours=æé
page.text.if.timing.or.targeting.of.asset.does.not.apply=ãã®ã¢ã»ããã®ã¿ã¼ã²ããè¨­å®ã¾ãã¯ã¿ã¤ãã³ã°ãåä¿¡èã«é©ç¨ãããªãå ´åããããå«ãã¡ãã»ã¼ã¸ã«ã©ã®ãããªå½±é¿ãçããã?
page.text.images.which.are=æ¬¡ã®ç¶æã®ç»å
page.text.import.is.complete=ã¤ã³ãã¼ãã¯å®äºãã¦ãã¾ã
page.text.importing.please.wait=ã¤ã³ãã¼ããã¦ãã¾ã... ãå¾ã¡ãã ããã
page.text.in.priority.order=(åªååº¦é )
page.text.in.process=é²è¡ä¸­
page.text.inactive.insert.schedules=éã¢ã¯ãã£ãæ¿å¥ã¹ã±ã¸ã¥ã¼ã«
page.text.inactive.inserts=éã¢ã¯ãã£ãæ¿å¥
page.text.inactive.tags=éã¢ã¯ãã£ã ã¿ã°
page.text.include.active.only=ã¢ã¯ãã£ããªã¢ã¤ãã ã®ã¿ãå«ãã
page.text.include.groups=ã°ã«ã¼ããå«ãã
page.text.include.messages.activated.after=æ¬¡ã®æç¹ã¾ãã¯å¾ã«ã¢ã¯ãã£ãåãããã¡ãã»ã¼ã¸ã®ã¿ãå«ãã
page.text.incomplete.rate.sheet.coverage=ä¸å®å¨ãªæéè¡¨ã«ãã¬ãã¸: ãã®ææ¸çºéã¹ã±ã¸ã¥ã¼ã«ã®ä¸é¨æéã§æéè¡¨ã³ã¬ã¯ã·ã§ã³ãå®ç¾©ããã¦ãã¾ããã
page.text.indirect.references=éæ¥åç§
page.text.inherited.from=ç¶æ¿å
page.text.insert.not.referenced=åç§ããã¦ããªãæ¿å¥
page.text.insert.schedules=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«
page.text.language.selector.not.defined.for.touchpoint=ãã®ã¿ãããã¤ã³ãã«å¯¾ãã¦è¨èªã»ã¬ã¯ã¿ã¼ãå®ç¾©ããã¦ãã¾ãããã¿ãããã¤ã³ã ã»ããã¢ããã§ã»ã¬ã¯ã¿ã¼ãæå®ãã¦ãã ããã
page.text.less.than=æ¬¡ã®å¤ããå°ãã
page.text.limited=å¶éãã
page.text.link.to.download.touchpoint.will.popup=ã¨ã¯ã¹ãã¼ãããã¿ãããã¤ã³ã XML ããã¦ã³ã­ã¼ãããããã®ãªã³ã¯ã¯ãå¥ã®ãããã¢ãã ã¦ã£ã³ãã¦ã«è¡¨ç¤ºããã¾ãã
page.text.loading.communication.data=ã³ãã¥ãã±ã¼ã·ã§ã³ ãã¼ã¿ã®ã­ã¼ãä¸­...
page.text.loading=ã­ã¼ãä¸­...
page.text.locked.referenced.target.group=ã­ãã¯æ¸ã¿: åç§åã®ã¿ã¼ã²ãã ã°ã«ã¼ã
page.text.logged.out=ãµã¤ã³ã¢ã¦ããã¾ããã
page.text.mark.tenant.schedule.complete=ãã®ããã³ã ã¹ã±ã¸ã¥ã¼ã«ãå®äºã«ãã¾ãã?
page.text.message.containing.smart.text.will.only.play.if=ãã®ã¹ãã¼ã ãã­ã¹ããå«ãã¡ãã»ã¼ã¸ã¯ããã®ã¹ãã¼ã ãã­ã¹ãã®ã¿ã¼ã²ããè¨­å®ã¨ã¿ã¤ãã³ã°ãé©ç¨ãããå ´åã«ã®ã¿åçããã¾ãã
page.text.message.inherited.targeting.from.link=ãã®ã¡ãã»ã¼ã¸ã®ã¿ã¼ã²ããè¨­å®ã®ç¶æ¿å
page.text.message.inherited.timing.from.link=ãã®ã¡ãã»ã¼ã¸ã®ã¿ã¤ãã³ã°ã®ç¶æ¿å
page.text.message.now.used.in.production=ããªã¢ã³ããã¢ã¯ãã£ãåããã¦ããå ´åããã®ã¡ãã»ã¼ã¸ã¯ãã­ãã¯ã·ã§ã³ã§ä½¿ç¨ããã¾ãã
page.text.message.report.tool=ã¡ãã»ã¼ã¸ ã¬ãã¼ã ãã¼ã«
page.text.messagepoint.tm=Messagepoint<sup>Â®</sup>
page.text.messages.which.are=æ¬¡ã®ç¶æã®ã¡ãã»ã¼ã¸
page.text.messages.with=æ¬¡ãå«ãã¡ãã»ã¼ã¸
page.text.migrate.folder.locations.for.node=é¸æããã¤ã³ã¹ã¿ã³ã¹ã®ãã©ã«ãã¼ã®å ´æãç§»è¡ãã¾ãã
page.text.modify.language.for.selected.variant=é¸æããè¨èªã®é¸æã§è¨èªãå¤æ´ãã¾ãã?
page.text.move.resize.part=ãã¼ãã®ç§»å/ãµã¤ãºå¤æ´
page.text.move.resize.zone=ã¾ã¼ã³ã®ç§»å/ãµã¤ãºå¤æ´
page.text.move.zone.to.section=ã¾ã¼ã³ãã»ã¯ã·ã§ã³ã«ç§»å
page.text.multipart.no.categories=ç¾å¨ã·ã¹ãã åã«å®ç¾©ãããè¤æ°ãã¼ã ã³ã³ãã³ã ã«ãã´ãªãå­å¨ãã¾ããã
page.text.multipart.zone.repeats=è¤æ°ãã¼ã ã¾ã¼ã³ã¯ä»¥ä¸ãç¹°ãè¿ãã¾ã
page.text.multiple=è¤æ°
page.text.my.filter.does.not.apply.to.active.communications=ããã¤ããã£ã«ã¿ã¼ã¯ã¢ã¯ãã£ã ã³ãã¥ãã±ã¼ã·ã§ã³ã«ã¯é©ç¨ããã¾ããã
page.text.my.messages.filter.does.not.apply=ããã¤ãã¡ãã»ã¼ã¸ ãã£ã«ã¿ã¼ã¯ã¢ã¯ãã£ãã¾ãã¯ã¢ã¼ã«ã¤ãæ¸ã¿ã¡ãã»ã¼ã¸ã«ã¯é©ç¨ããã¾ããã
page.text.my.variants.filter.does.not.apply=ããã¤ãããªã¢ã³ã ãã£ã«ã¿ã¼ã¯ã¢ã¯ãã£ã ããªã¢ã³ãã«ã¯é©ç¨ããã¾ããã
page.text.new.modifiers.detected=æ°ããã¢ãã£ãã¡ã¤ã¢ã¼ãæ¤åºããã¾ãã
page.text.new.name=æ°ããåå
page.text.new.part=æ°è¦ãã¼ã
page.text.new.zone=æ°è¦ã¾ã¼ã³
page.text.new.zones.detected=æ°ããã¾ã¼ã³ãæ¤åºããã¾ãã
page.text.newpassword.action=ã·ã¹ãã åã§å®ç¾©ããã¦ããç¾å¨ã®ãã¹ã¯ã¼ãè¦ä»¶ã«åããã¦æ°è¦ãã¹ã¯ã¼ããé¸æãã¾ãã
page.text.newpassword.consequence1=ãã¹ã¯ã¼ãã®æ´æ°ã¯ç´ã¡ã«åæ ããã¾ãã
page.text.newpassword.consequence2=ãã¹ã¯ã¼ãã®æ´æ°ãè¡ããªãã¨ãMessagepoint ã«ãµã¤ã³ã¤ã³ã§ããªããªãã¾ãã
page.text.newpassword.instructions=ãã®ãã¼ã¸ã®ä¸é¨ã«ããè©²å½ãããã¿ã³ãä½¿ç¨ãããã¹ã¯ã¼ãæ´æ°ãç¢ºèªã¾ãã¯ã­ã£ã³ã»ã«ãã¦ãã ããã
page.text.newuserid.action=ã·ã¹ãã åã§å®ç¾©ããã¦ããç¾å¨ã®ã¦ã¼ã¶ã¼åè¦ä»¶ã«åããã¦æ°è¦ã¦ã¼ã¶ã¼åãé¸æãã¾ãã
page.text.newuserid.consequence1=ã¦ã¼ã¶ã¼åã®æ´æ°ã¯ç´ã¡ã«åæ ããã¾ãã
page.text.newuserid.consequence2=ã¦ã¼ã¶ã¼åã®æ´æ°ãè¡ããªãã¨ãMessagepoint ã«ãµã¤ã³ã¤ã³ã§ããªããªãã¾ãã
page.text.newuserid.instructions=ãã®ãã¼ã¸ã®ä¸é¨ã«ããè©²å½ãããã¿ã³ãä½¿ç¨ããã¦ã¼ã¶ã¼åæ´æ°ãç¢ºèªã¾ãã¯ã­ã£ã³ã»ã«ãã¦ãã ããã
page.text.newuseridpassword.action=ã·ã¹ãã åã§å®ç¾©ããã¦ããç¾å¨ã®ã¦ã¼ã¶ã¼åã¨ãã¹ã¯ã¼ãã®è¦ä»¶ã«åããã¦æ°è¦ã¦ã¼ã¶ã¼åã¨æ°è¦ãã¹ã¯ã¼ããé¸æãã¾ãã
page.text.newuseridpassword.consequence1=ã¦ã¼ã¶ã¼åã¨ãã¹ã¯ã¼ãã®æ´æ°ã¯ç´ã¡ã«åæ ããã¾ãã
page.text.newuseridpassword.consequence2=ã¦ã¼ã¶ã¼åã¨ãã¹ã¯ã¼ãã®æ´æ°ãè¡ããªãã¨ãMessagepoint ã«ãµã¤ã³ã¤ã³ã§ããªããªãã¾ãã
page.text.newuseridpassword.instructions=ãã®ãã¼ã¸ã®ä¸é¨ã«ããè©²å½ãããã¿ã³ãä½¿ç¨ããã¦ã¼ã¶ã¼åã¨ãã¹ã¯ã¼ãã®æ´æ°ãç¢ºèªã¾ãã¯ã­ã£ã³ã»ã«ãã¦ãã ããã
page.text.next.section=æ¬¡ã®ã»ã¯ã·ã§ã³
page.text.no.active.content=ã¢ã¯ãã£ã ã³ã³ãã³ããããã¾ããã
page.text.no.active.messages=ç¾å¨ã¢ã¯ãã£ããªã¡ãã»ã¼ã¸ãããã¾ããã
page.text.no.alternate.templates=ãã³ãã¬ã¼ããã¢ããã­ã¼ãããã¦ãã¾ããã[+] ãã¿ã³ãã¯ãªãã¯ãã¦ãã³ãã¬ã¼ãå®ç¾©ãè¿½å ãã¦ãã ããã
page.text.no.applicable.inserts.for.touchpoint=é¢é£ããã¿ãããã¤ã³ãã«å¯¾ãã¦é©ç¨å¯è½ãªæ¿å¥ãå®ç¾©ããã¦ãã¾ããã
page.text.no.assignable.user=å²ãå½ã¦å¯è½ãªã¦ã¼ã¶ã¼ãããã¾ãã
page.text.no.associations=é¢é£ä»ããããã¾ãã
page.text.no.auxiliary.data.connections=æ¥ç¶ãå®ç¾©ããã¦ãã¾ããã[+] ã¢ã¤ã³ã³ãã¯ãªãã¯ãã¦æ¥ç¶ãè¿½å ãã¦ãã ããã 
page.text.no.available.content=ä½¿ç¨å¯è½ãªã³ã³ãã³ããããã¾ããã
page.text.no.available.instance.to.copy.from=ã³ãã¼åã¨ãã¦ä½¿ç¨å¯è½ãªã¤ã³ã¹ã¿ã³ã¹ãªã
page.text.no.available.text.styles=ä½¿ç¨å¯è½ãªãã­ã¹ã ã¹ã¿ã¤ã«ãããã¾ãã
page.text.no.available.users=ä½¿ç¨å¯è½ãªã¦ã¼ã¶ã¼ãããã¾ãã
page.text.no.available.workgroups=ä½¿ç¨å¯è½ãªã¯ã¼ã¯ã°ã«ã¼ããããã¾ãã
page.text.no.bins.specified=ãã³ãæå®ããã¦ãã¾ãããå¿è¦ãªãã³ã®æ°ãè¨­å®ãã¦ãã ãã
page.text.no.child.tags.or.attributes.for.tag=ãã®ã¿ã°ã®å­ã¿ã°ã¾ãã¯å±æ§ãããã¾ããã
page.text.no.composition.package.uploaded=ã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸ãã¢ããã­ã¼ãããã¦ãã¾ãã
page.text.no.composition.packages.for.touchpoint.collection=ã¿ãããã¤ã³ã ã³ã¬ã¯ã·ã§ã³ã«å¯¾ããã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸ãããã¾ãã
page.text.no.composition.packages.for.touchpoint=ã¿ãããã¤ã³ãã«å¯¾ããã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸ãããã¾ãã
page.text.no.content.libraries.qualified.for.link=ã¢ã¯ãã£ãç»åã©ã¤ãã©ãª ã¢ã»ãããããã¾ãã
page.text.no.content.selector=ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ãããã¾ãã
page.text.no.data.definitions.shared=ãã¼ã¿å®ç¾©ãå±æããã¦ãã¾ããã
page.text.no.data.files.list=ãã¼ã¿ ãã¡ã¤ã«ãããã¾ãã
page.text.no.data.files=ãã®è¨èªã®ãã¼ã¿ ãã¡ã¤ã«ãããã¾ãã
page.text.no.data.resources=ãã¼ã¿ ãªã½ã¼ã¹ãããã¾ãã
page.text.no.data.source.associations=ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³ãããã¾ãã
page.text.no.data.sources=ãã¼ã¿ ã½ã¼ã¹ãè¦ã¤ããã¾ããã§ããã
page.text.no.default.content=ããã©ã«ã ã³ã³ãã³ããããã¾ãã
page.text.no.deliveries=ã¬ãã¼ãå¯¾è±¡ã®éä¿¡ãªã
page.text.no.domains.to.map=ãããã³ã°å¯¾è±¡ã®ãã¡ã¤ã³ãªã
page.text.no.external.data.captured=ã¬ãã¼ãç¨ã®å¤é¨ãã¼ã¿ãåå¾ããã¦ãã¾ãã
page.text.no.history.items=å±¥æ­´ã¢ã¤ãã ãè¦ã¤ããã¾ããã
page.text.no.image=ç»åãããã¾ãã
page.text.no.images.select.file.to.upload=ç»åãããã¾ãã: ã¢ããã­ã¼ããããã¡ã¤ã«ãé¸æãã¦ãã ãã
page.text.no.inserts.assigned=æ¿å¥ãå²ãå½ã¦ããã¦ãã¾ããã
page.text.no.inserts.report=ã¬ãã¼ãå¯¾è±¡ã®æ¿å¥ãªã
page.text.no.inserts.selected=æ¿å¥ãé¸æããã¦ãã¾ããã
page.text.no.inserts.with.optional.delivery=ç¾å¨æ¿å¥å²ãå½ã¦ã«ãªãã·ã§ã³éä¿¡ã¹ãã¼ã¿ã¹ã®æ¿å¥ããå­å¨ãã¾ããã
page.text.no.licences.list=ã©ã¤ã»ã³ã¹ãªã
page.text.no.locked.users=ã·ã¹ãã ããã­ãã¯ã¢ã¦ãããã¦ããã¦ã¼ã¶ã¼ãªã?
page.text.no.log.messages=ã­ã° ã¡ãã»ã¼ã¸ãªã
page.text.no.mandatory.messages=ãã®ã¾ã¼ã³ã«éä¿¡ãããå¿é ã¡ãã»ã¼ã¸ãããã¾ããã
page.text.no.matches.found=ä¸è´ããã¢ã¤ãã ãè¦ã¤ããã¾ããã
page.text.no.matching.inserts=ç¾å¨ã®é¸ææ¡ä»¶ã¨ä¸è´ããæ¿å¥ãããã¾ããã
page.text.no.matching.items=ç¾å¨ã®é¸ææ¡ä»¶ã¨ä¸è´ããã¢ã¤ãã ãããã¾ããã
page.text.no.matching.language.selections=ç¾å¨ã®é¸ææ¡ä»¶ã¨ä¸è´ããè¨èªã®é¸æãããã¾ããã
page.text.no.matching.ratesheets=ã·ã¹ãã åã«å®ç¾©ãããæéè¡¨ãããã¾ãã
page.text.no.matching.tags=ç¾å¨ã®é¸ææ¡ä»¶ã¨ä¸è´ããã¿ã°ãããã¾ããã
page.text.no.matching.variants=ä¸è´ããããªã¢ã³ããè¦ã¤ããã¾ããã
page.text.no.menu.value.items=ãã®ã¡ãã¥ã¼ã«å¯¾ãã¦ã¢ã¤ãã ãå®ç¾©ããã¦ãã¾ããã[+] ã¢ã¤ã³ã³ãã¯ãªãã¯ãã¦ã¢ã¤ãã ãè¿½å ãã¦ãã ããã
page.text.no.messages.for.collection=é¸æããã³ã¬ã¯ã·ã§ã³ã«å¯¾ããã¡ãã»ã¼ã¸ãããã¾ãã
page.text.no.messages.for.touchpoint=é¸æããã¿ãããã¤ã³ãã«å¯¾ããã¡ãã»ã¼ã¸ãããã¾ãã
page.text.no.messages.qualified.for.link=ãªã³ã¯ã«å¯¾ãã¦é©æ ¼ãªã¡ãã»ã¼ã¸ãããã¾ãã
page.text.no.messages.qualified=è¦æ±ããã·ãã¥ã¬ã¼ã·ã§ã³ã«å¯¾ãã¦é©æ ¼ãªã¡ãã»ã¼ã¸ãããã¾ããã
page.text.no.messages.report=ã¬ãã¼ãå¯¾è±¡ã®ã¡ãã»ã¼ã¸ãããã¾ããã
page.text.no.nodes.defined.for.branch=ãã®ãã¡ã¤ã³ã«å¯¾ãã¦å®ç¾©ãããã¤ã³ã¹ã¿ã³ã¹ãããã¾ãããã¢ã¯ã·ã§ã³ ã¡ãã¥ã¼ã«ã¢ã¯ã»ã¹ãã¦ã¤ã³ã¹ã¿ã³ã¹ãä½æãã¦ãã ããã
page.text.no.optional.messages=ãã®ã¾ã¼ã³ã«éä¿¡ããããªãã·ã§ã³ ã¡ãã»ã¼ã¸ãããã¾ããã
page.text.no.override=ããã©ã«ããä½¿ç¨
page.text.no.para.styles=æ®µè½ã¹ã¿ã¤ã«ãå®ç¾©ããã¦ãã¾ãã
page.text.no.paragraph.styles=ä½¿ç¨å¯è½ãªæ®µè½ã¹ã¿ã¤ã«ãªã
page.text.no.primary.data.source=ãã©ã¤ããª ãã¼ã¿ ã½ã¼ã¹ãªã
page.text.no.rate.sheets.for.insert.schedule=ãã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã«å¯¾ãã¦æéè¡¨ãé¸æããã¦ãã¾ããã
page.text.no.rate.sheets=æéè¡¨ãããã¾ãã
page.text.no.records.defined=ã¬ã³ã¼ããå®ç¾©ããã¦ãã¾ãã
page.text.no.reference.data.source=ä½¿ç¨å¯è½ãªåç§ãã¼ã¿ ã½ã¼ã¹ãããã¾ãã
page.text.no.restriction=å¶éãªã
page.text.no.restrictions=å¶éãªã
page.text.no.selector.definition.set.for.insert.schedule=ãã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã«é¢é£ä»ããããã¿ãããã¤ã³ãã«å¯¾ããã»ã¬ã¯ã¿ã¼å®ç¾©ãè¨­å®ããã¦ãã¾ããã
page.text.no.selector.values.defined.for.insert.schedule=ãã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã«å¯¾ããã»ã¬ã¯ã¿ã¼å¤ãå®ç¾©ããã¦ãã¾ããã
page.text.no.selector.values.defined.for.touchpoint.variant=ãã®ã¿ãããã¤ã³ã ããªã¢ã³ãã«å¯¾ããã»ã¬ã¯ã¿ã¼å¤ãå®ç¾©ããã¦ãã¾ããã
page.text.no.selectors.for.language.variant=ãã®è¨èªããªã¢ã³ãã«å¯¾ããã»ã¬ã¯ã¿ã¼å¤ãå®ç¾©ããã¦ãã¾ããã
page.text.no.selectors=ã»ã¬ã¯ã¿ã¼ãªã
page.text.no.tags.defined=ã¿ã°ãå®ç¾©ããã¦ãã¾ãã
page.text.no.tasks=ç¾å¨ã®é¸ææ¡ä»¶ã¨ä¸è´ããã¿ã¹ã¯ãããã¾ããã
page.text.no.template.zones.detected=ãã®é»å­ã¡ã¼ã« ãã³ãã¬ã¼ãã«å¯¾ããã¾ã¼ã³ãæ¤åºããã¾ããã
page.text.no.text.styles=ãã­ã¹ã ã¹ã¿ã¤ã«ãå®ç¾©ããã¦ãã¾ãã
page.text.no.timing.for.inserts.in.schedule=ãã®ã¹ã±ã¸ã¥ã¼ã«ã®æ¿å¥ã«å¯¾ãã¦ã¿ã¤ãã³ã°ãæå®ããã¦ãã¾ããã
page.text.no.timing=ã¿ã¤ãã³ã°ãªã
page.text.no.touchpoint.collections.list=ã¿ãããã¤ã³ã ã³ã¬ã¯ã·ã§ã³ãå®ç¾©ããã¦ãã¾ããã[ã³ã¬ã¯ã·ã§ã³ã®è¿½å ] ãã¯ãªãã¯ãã¦ãã ããã
page.text.no.touchpoints.available.to.you=ã¿ãããã¤ã³ããå©ç¨ã§ããããã«ãªã£ã¦ãã¾ãããç®¡çèã«åãåããã¦è¡¨ç¤ºè¨­å®ãèª¿æ´ããããæåã®ã¿ãããã¤ã³ããæ§æãã¦ãã ããã
page.text.no.touchpoints.matching.search=æ¤ç´¢æ¡ä»¶ã«ä¸è´ããã¿ãããã¤ã³ããããã¾ããã
page.text.no.variable.brackets=[å¤æ°ãªã]
page.text.no.variables=å¤æ°ãªã
page.text.no.visible.connected.zones=ãã®ã¿ãããã¤ã³ãã«å¯¾ãã¦è¡¨ç¤ºå¯è½ãªæ¥ç¶æ¸ã¿ã¾ã¼ã³ãæ§æããã¦ãã¾ããã
page.text.no.visible.inserts.for.touchpoint=é¸æããã¿ãããã¤ã³ãã«é¢é£ä»ããããè¡¨ç¤ºå¯è½ãªã¢ã¯ãã£ãæ¿å¥ãå­å¨ãã¾ããã
page.text.no.visible.mandatory.inserts.associated.with.touchpoint=é¸æããã¿ãããã¤ã³ãã«é¢é£ä»ããããè¡¨ç¤ºå¯è½ãªå¿é ã®æ¿å¥ãå­å¨ãã¾ããã
page.text.no.visible.messages.for.touchpoint=é¸æããã¿ãããã¤ã³ãã«é¢é£ä»ããããè¡¨ç¤ºå¯è½ãªéä¿¡æ¸ã¿ã¡ãã»ã¼ã¸ãå­å¨ãã¾ããã
page.text.no.visible.optional.insert.associated.with.touchpoint=é¸æããã¿ãããã¤ã³ãã«é¢é£ä»ããããè¡¨ç¤ºå¯è½ãªãªãã·ã§ã³ã®æ¿å¥ãå­å¨ãã¾ããã
page.text.no.workflow.data.to.display=ãã®ã¢ã»ããã¯ä½æ¥­ã³ãã¼ç¶æã§ãããããè¡¨ç¤ºå¯¾è±¡ã®ã¯ã¼ã¯ãã­ã¼ ãã¼ã¿ãã¾ã å­å¨ãã¾ããã
page.text.no.workgroups.defined=ã¯ã¼ã¯ã°ã«ã¼ããå®ç¾©ããã¦ãã¾ãã
page.text.no.working.copies.selected.for.test=ãã®ãã¹ãã«å¯¾ãã¦ä½æ¥­ã³ãã¼ãé¸æããã¦ãã¾ãã
page.text.no.zone.associations.defined.for.this.workgroup=ãã®ã¯ã¼ã¯ã°ã«ã¼ãã«å¯¾ãã¦å®ç¾©ãããã¾ã¼ã³ã®é¢é£ä»ããå­å¨ãã¾ããã
page.text.none.please.select=ãªããé¸æãã¦ãã ãã --->
page.text.none=ãªã...
page.text.note.required.brackets=[æ³¨ãå¿è¦]
page.text.note.required.to.reject.brackets=[æå¦ããã®ã«æ³¨ãå¿è¦]
page.text.of.these.target.groups.must.pass=ãããã®ã¿ã¼ã²ãã ã°ã«ã¼ãããã¹ããå¿è¦ãããã¾ã
page.text.of.these.users=ãããã®ã¦ã¼ã¶ã¼
page.text.on.hold.messages.not.used.in.production=ããªã¢ã³ããã¢ã¯ãã£ãåããã¦ããå ´åããä¿çä¸­ãã®ã¡ãã»ã¼ã¸ã¯ãã­ãã¯ã·ã§ã³ã§ä½¿ç¨ããã¾ããã
page.text.one.of=ãããã
page.text.only.one.language.associated.with.touchpoint=ãã®ã¿ãããã¤ã³ãã«ã¯è¨èªã 1 ã¤ã ãé¢é£ä»ãããã¦ãã¾ããã³ã³ãã³ãã¯ãã¹ã¦æ¬¡ã®ããã«å¦çããã¾ã
page.text.open.in.new.window=æ°ããã¦ã£ã³ãã¦ã§éã
page.text.or.drag.and.drop.images=ã¾ãã¯ç»åããã©ãã°ã¢ã³ããã­ãããã
page.text.order.entry.item.is.primary.driver=ã¨ã³ããªãã³ãã¥ãã±ã¼ã·ã§ã³ã®ãã©ã¤ããª ãã©ã¤ãã¼å¤ã¨ãã¦ä½¿ç¨ãã
page.text.other.messages=ä»ã®ã¡ãã»ã¼ã¸
page.text.otherwise=ããä»¥å¤ã®å ´å
page.text.override.value=æ°è¦ã³ã³ãã³ã
page.text.page.weights=ãã¼ã¸ã®éé
page.text.pagenotfound=åé¡ãçºçãã¾ããã
page.text.pagenotfounddescription=ã¢ã¯ã»ã¹ãããã¼ã¸ã¯æ¢ã«å­å¨ãã¾ããã
page.text.parameter.noparametergroups=ã·ã¹ãã åã«å®ç¾©ãããã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ ã°ã«ã¼ããããã¾ãã
page.text.parameter.noparameters=ã·ã¹ãã åã«å®ç¾©ãããã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ãããã¾ãã
page.text.password.reset.selected.user=é¸æããã¦ã¼ã¶ã¼ã®ãã¹ã¯ã¼ããå¤æ´ãã¾ãã?
page.text.passwordreset.confirmation=ãã¹ã¯ã¼ãããªã»ããããæé ãè¨è¼ããé»å­ã¡ã¼ã«ããéããã¾ããããµã¤ã³ã¤ã³ ãã¼ã¸ã«æ»ãã«ã¯ã<a href=\"./signin.jsp?tenant=Provider\">ãã</a>ãã¯ãªãã¯ãã¦ãã ããã
page.text.passwordreset=ãã¹ã¯ã¼ãããå¿ãã§ãã? ãã¹ã¯ã¼ãããªã»ããããå ´åã¯ããä½¿ãã®ã¢ã«ã¦ã³ãã§ä½¿ç¨ãã¦ããé»å­ã¡ã¼ã« ã¢ãã¬ã¹ãæå®ãã¦ãã ãããæå®ããé»å­ã¡ã¼ã« ã¢ãã¬ã¹ã«ãã¹ã¯ã¼ãããªã»ãããããªã³ã¯ãå«ãé»å­ã¡ã¼ã«ããéããã¾ãã
page.text.pending.approval.from=ä¿çä¸­æ¿èªå
page.text.permitted.characters=ãã®å¥åã«ã¯æ¬¡ã®æå­ãä½¿ç¨ã§ãã¾ã
page.text.playtoallcustomers=ã¿ã¼ã²ããè¨­å®ãªã - ãã¹ã¦ã®åä¿¡èãé©æ ¼ã«ãªãã¾ã
page.text.please.contact.messagepoint=Messagepoint ã«åãåããã¦ãã ãã
page.text.prepopulate.optional=äºåè¨­å® (ãªãã·ã§ã³)
page.text.preview=ãã¬ãã¥ã¼
page.text.previous.section=åã®ã»ã¯ã·ã§ã³
page.text.primary=ãã©ã¤ããª
page.text.privacy=ãã©ã¤ãã·ã¼
page.text.proof.selected.communication=ç¾å¨é¸æãã¦ããã³ãã¥ãã±ã¼ã·ã§ã³ãæ ¡æ­£ãã¾ãã?
page.text.proof=æ ¡æ­£
page.text.provide.description.of.action.required=å¿è¦ãªã¢ã¯ã·ã§ã³ã®èª¬æãå¥åãã¾ãã
page.text.provider.insert.schedules=ãã­ãã¤ãã¼æ¿å¥ã¹ã±ã¸ã¥ã¼ã«
page.text.qualify=é©æ ¼ã«ãã
page.text.quick.brown.fox=The quick brown fox jumps over the lazy dog.
page.text.range_to=ãã
page.text.rate.sheet.coverage=æéè¡¨ã®é©ç¨ç¯å²
page.text.rate.sheet.covers.period.of.insert.schedule=ãã®æéè¡¨ã¯ãã®æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ã«å¯¾ãã¦æå®ãããå¨æéãã«ãã¼ãã¦ãã¾ã
page.text.rate.sheet.transition=æéè¡¨ã®ç§»è¡: ãã®å°æ¸çºéã¹ã±ã¸ã¥ã¼ã«ã®æéä¸­ã« 2 ã¤ã®æéè¡¨ã®éã§ç§»è¡ãè¡ããã¾ãã
page.text.reassign.smart.text.to.user=ãã®ã¹ãã¼ã ãã­ã¹ããå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æããå¿è¦ãªã¢ã¯ã·ã§ã³ã®èª¬æãå¥åãã¾ãã
page.text.record.contains.no.elements=ã¬ã³ã¼ãã«è¦ç´ ãå«ã¾ãã¦ãã¾ãã
page.text.record_types=ã¬ã³ã¼ã ã¿ã¤ã
page.text.refresh.segmentation.analysis=ã»ã°ã¡ã³ãååæãæ´æ°ãã¾ãã?
page.text.release.for.tenant.usage.before.overrides=ãããå¤ã®ãªã¼ãã¼ã©ã¤ãã«ã¢ã¯ã»ã¹ããåã«ããã³ãç¨ã«ãªãªã¼ã¹ãã
page.text.release.for.tenant.usage.for.timing=ããã³ãæ¿å¥ã¿ã¤ãã³ã°ãè¡¨ç¤ºããåã«ããã³ãç¨ã«ãªãªã¼ã¹ãã
page.text.release.insert.schedule.for.tenant.usage=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ãããã³ãç¨ã«ãªãªã¼ã¹ãã¾ãã? ããã³ãã¯æä½å¯è½ãªæéåã«æ¿å¥ãç®¡çã§ãã¾ãã
page.text.release.insert.schedule.for.use=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«ããªãªã¼ã¹ãã¦ä½¿ç¨ãã¾ãã?
page.text.release.tenant.usage.before.bin.assignment=ããã³ã ãã³å²ãå½ã¦ãè¡¨ç¤ºããåã«ããã³ãç¨ã«ãªãªã¼ã¹ãã
page.text.remaining=æ®ã
page.text.remember.my.username=ã¦ã¼ã¶ã¼åãè¨æ¶ãã
page.text.remote.server.settings.configured=ãªã¢ã¼ã ãµã¼ãã¼è¨­å®ã¯æ§ææ¸ã¿ã§ãã?
page.text.remove.alternate.template=ä»£æ¿ãã³ãã¬ã¼ãã®åé¤
page.text.remove.insert.from.favorites.folder=ãæ°ã«å¥ããã©ã«ãã¼ããé¸æããæ¿å¥ãåé¤ãã¾ãã?
page.text.remove.inserts.from.favorites.folder=ãæ°ã«å¥ããã©ã«ãã¼ããé¸æããæ¿å¥ãåé¤ãã¾ãã?
page.text.remove.paragraph.style.customization=é¸æããæ®µè½ã¹ã¿ã¤ã«ã®ã«ã¹ã¿ãã¤ãºãåé¤ãã¾ãã?
page.text.remove.style.customization=é¸æãããã­ã¹ã ã¹ã¿ã¤ã«ã®ã«ã¹ã¿ãã¤ãºãåé¤ãã¾ãã?
page.text.remove.variant.data=ããªã¢ã³ã ãã¼ã¿ã®åé¤
page.text.removed.selected.variant=é¸æããããªã¢ã³ãã®å®å¨ãªåé¤ãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.rename.selected.branch=ç¾å¨é¸æãã¦ãããã¡ã¤ã³ã®ååãå¤æ´ãã¾ãã?
page.text.rename.selected.selection=ç¾å¨é¸æãã¦ããé¸æåå®¹ã®ååãå¤æ´ãã¾ãã?
page.text.rename.selected.variant=ç¾å¨é¸æãã¦ããããªã¢ã³ãã®ååãå¤æ´ãã¾ãã?
page.text.rename.this.asset=ãã®ã¢ã»ããã®ååãå¤æ´ãã¾ãã?
page.text.reopen.insert.selection.window=ä»¥ä¸ã®ããã³ãã¯æ¿å¥ã®é¸æãå®äºãã¦ãã¾ããããã³ããé¸æãã¦æ¿å¥ã®é¸æã¦ã£ã³ãã¦ãåãªã¼ãã³ãã¦ãã ããã
page.text.repeats.annually.brackets=(æ¯å¹´ç¹°ãè¿ã)
page.text.replacement=ç½®ãæã
page.text.report.being.prepared=ã¬ãã¼ãã®æºåä¸­...
page.text.report.loads.in.separate.window=ã¬ãã¼ãã¯å¥ã®ãããã¢ãã ã¦ã£ã³ãã¦ã«è¡¨ç¤ºããã¾ãã
page.text.reports.which.are=æ¬¡ã®ç¶æã®ã¬ãã¼ã
page.text.requested.HTML.file.of.type=è¦æ±ãã HTML ãã¡ã¤ã«ã®ã¿ã¤ã
page.text.requires.approval.from=æ¿èªåãå¿è¦
page.text.reservation=äºç´
page.text.reservations=äºç´
page.text.reset.default.header.logo=ããã©ã«ãã®ãããã¼ ã­ã´ã«ãªã»ãããã¦ãããããã§ãã?
page.text.reset.default.provider.logo=ããã©ã«ãã®ãã­ãã¤ãã¼ ã­ã´ã«ãªã»ãããã¦ãããããã§ãã?
page.text.restore.message=é¸æããã¡ãã»ã¼ã¸ãå¾©åãã¾ãã? ã¡ãã»ã¼ã¸ã¯ååº¦ãã­ãã¯ã·ã§ã³ã«è¿½å ããã¾ãã
page.text.restore.selected.messages=é¸æããã¡ãã»ã¼ã¸ãå¾©åãã¾ãã? ã¡ãã»ã¼ã¸ã¯ååº¦ãã­ãã¯ã·ã§ã³ã«è¿½å ããã¾ãã
page.text.restricted.access.note=ãä½¿ãã®ã¢ã«ã¦ã³ãã¯ Messagepoint ã¸ã®ã¢ã¯ã»ã¹ãå¶éããã¦ãã¾ããç®¡çèã«åãåããã¦ãã ããã
page.text.restricted.access=å¶éä»ãã¢ã¯ã»ã¹
page.text.review.touchpoint.data.collection=ãã®ã¿ãããã¤ã³ãã®ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³ãç¢ºèªãã¦ãã ããã
page.text.role.activate.consequence1=å½¹å²ãã¢ã¯ãã£ãã«ããã¨ãå½¹å²ãå²ãå½ã¦ãããä»»æã®ã¢ã¯ãã£ã ã¦ã¼ã¶ã¼ã Messagepoint ã«ãµã¤ã³ã¤ã³ã§ããããã«ãªãã¾ãã
page.text.role.activate.consequence2=ãã®ã¢ã¯ã·ã§ã³ãå¾ããåãæ¶ãã«ã¯ãå½¹å²ãéã¢ã¯ãã£ãã«ãã¾ãã
page.text.role.activate.instructions=[ç¶è¡] ãã¯ãªãã¯ãã¦å½¹å²ãã¢ã¯ãã£ãã«ãããã[ã­ã£ã³ã»ã«] ãã¯ãªãã¯ãã¦æä½ãã­ã£ã³ã»ã«ãã¦ãã ããã
page.text.role.deactivate.consequence1=å½¹å²ãéã¢ã¯ãã£ãã«ããã¨ãå½¹å²ãå²ãå½ã¦ãããä»»æã®ã¢ã¯ãã£ã ã¦ã¼ã¶ã¼ã Messagepoint ã«ãµã¤ã³ã¤ã³ã§ããªããªãã¾ãã
page.text.role.deactivate.consequence2=ãã®ã¢ã¯ã·ã§ã³ãå¾ããåãæ¶ãã«ã¯ãå½¹å²ãã¢ã¯ãã£ãã«ãã¾ãã
page.text.role.deactivate.instructions=[ç¶è¡] ãã¯ãªãã¯ãã¦éã¢ã¯ãã£ãã«ãããã[ã­ã£ã³ã»ã«] ãã¯ãªãã¯ãã¦æä½ãã­ã£ã³ã»ã«ãã¦ãã ããã
page.text.role.delete.consequence1=å½¹å²ãåé¤ããã¨ãMessagepoint ããå½¹å²ã®ã¬ã³ã¼ããåé¤ããã¾ãã
page.text.role.delete.consequence2=åé¤ã¯åãæ¶ããã¨ãã§ããªãã¢ã¯ã·ã§ã³ã§ãã
page.text.role.delete.instructions=ä»¥ä¸ã®åå®¹ãç¢ºèªãããã®ãã¼ã¸ã®ä¸é¨ã«ããè©²å½ãããã¿ã³ãä½¿ç¨ãã¦å½¹å²ã®åé¤ãç¶è¡ã¾ãã¯ã­ã£ã³ã»ã«ãã¦ãã ããã
page.text.role.noroles=ã·ã¹ãã åã«å®ç¾©ãããå½¹å²ãããã¾ãã
page.text.rule.is.parameterized=ãã®ãã©ã¡ã¼ã¿ã¼åãããã«ã¼ã«ã¯ä½¿ç¨ããã¦ãããããå¤æ´ã§ãã¾ããã
page.text.rules.must.pass.for.target.group.to.qualify=ãã®ã¿ã¼ã²ãã ã°ã«ã¼ããé©æ ¼ã¨ãªãã«ã¯ããããã®ã«ã¼ã«ããã¹ããå¿è¦ãããã¾ã
page.text.same.as.content=æ¬¡ã¨åãã³ã³ãã³ã
page.text.same.as=æ¬¡ã¨åã
page.text.scan.domains=æ¢å­ã®ãã¹ã¦ã®ãã¡ã¤ã³ãã¹ã­ã£ã³ãã¾ãã
page.text.scenarios.changed=ååã®ãã¹ãå®è¡å¾ã«ããã®ãã¹ã ã·ããªãªåã®é¸æãããä¸é¨ã®ä½æ¥­ã³ãã¼ ã¡ãã»ã¼ã¸ãã¢ã¯ãã£ãåã¾ãã¯ç ´æ£ããã¾ããããã¹ããå®è¡ããã«ã¯ãç®çã®ä½æ¥­ã³ãã¼ ã¡ãã»ã¼ã¸ã§ãã¹ã ã·ããªãªãæ´æ°ãã¦ãã ããã
page.text.schedule.not.released.for.tenant.usage.threshold.override.not.available=ãã®ã¹ã±ã¸ã¥ã¼ã«ã¯ã¾ã ããã³ãã§ä½¿ç¨ããããã«ãªãªã¼ã¹ããã¦ãã¾ããããªãªã¼ã¹ãããã¾ã§ãããå¤ã®ãªã¼ãã¼ã©ã¤ããæå®ã§ãã¾ããã
page.text.search.for.rules=ã«ã¼ã«ã®æ¤ç´¢
page.text.search.for.target.groups=ã¿ã¼ã²ãã ã°ã«ã¼ãã®æ¤ç´¢
page.text.select.a.user.to.assign.asset.to=ã¢ã»ãããå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æãã¾ãã
page.text.select.a=é¸æ
page.text.select.and.add.content.selector=ã³ã³ãã³ã ã»ã¬ã¯ã¿ã¼ã®é¸æã¨è¿½å 
page.text.select.composition.package=ã³ã³ãã¸ã·ã§ã³ ããã±ã¼ã¸ã®é¸æ
page.text.select.content.library=é©ç¨ããç»åã®é¸æ
page.text.select.content.type.for.message=æ°è¦ã¡ãã»ã¼ã¸ã®ã³ã³ãã³ã ã¿ã¤ããé¸æãã¾ãã
page.text.select.data.range.for.batch.window=ããã ã¦ã£ã³ãã¦ã®æ¥ä»ã®ç¯å²ãé¸æãã¾ãããã®ç¯å²åã®ãã¹ã¦ã®ã³ãã¥ãã±ã¼ã·ã§ã³ããããã«å«ã¾ãã¾ãã
page.text.select.data.to.remove.from.content.selection=ã³ã³ãã³ã ããªã¢ã³ã ãã¼ã¿å®ç¾©ããåé¤ãã 1 ã¤ã¾ãã¯è¤æ°ã®é¸æãã¼ã¿å¤ãé¸æãã¾ãã
page.text.select.data.to.remove.from.variant=ã³ã³ãã³ã ããªã¢ã³ã ãã¼ã¿å®ç¾©ããåé¤ãããã¼ã¿ãé¸æãã¾ãã
page.text.select.date=ã«ã¬ã³ãã¼ ã¢ã¤ã³ã³ãã¯ãªãã¯ãã¦æ¥ä»ãé¸æãã¾ããSpaceãDeleteãããã³ Backspace ãä½¿ç¨ããã¨é¸æããæ¥ä»ãã¯ãªã¢ããã¾ãã
page.text.select.default.language=é¸æããè¨èªãããã©ã«ãã«ãã¾ãã?
page.text.select.default.paragraph.style=ããã©ã«ãæ®µè½ã¹ã¿ã¤ã«ã®é¸æ
page.text.select.default.text.style=ããã©ã«ã ãã­ã¹ã ã¹ã¿ã¤ã«ã®é¸æ
page.text.select.from.existing.radio.groups=æ¢å­ã®ã©ã¸ãª ã°ã«ã¼ãããé¸æ
page.text.select.image.to.upload=[ç»åã®é¸æ] ãã¯ãªãã¯ãã¦ç»åã®ã¢ããã­ã¼ããéå§ãã¾ãã
page.text.select.instance=ã¤ã³ã¹ã¿ã³ã¹ã®é¸æ
page.text.select.message=ã¡ãã»ã¼ã¸ã®é¸æ
page.text.select.no.default.style=-- ããã©ã«ããªã --
page.text.select.resource=ãªã½ã¼ã¹ã®é¸æ
page.text.select.server=ãµã¼ãã¼ã®é¸æ
page.text.select.shared.data.definition=æ°è¦ããªã¢ã³ãã«é¢é£ä»ããå±æãã¼ã¿å®ç¾©ãé¸æãã¾ãã
page.text.select.shared.data.for.content.selection=æ°è¦ããªã¢ã³ãã«é¢é£ä»ããå±æããªã¢ã³ã ãã¼ã¿å®ç¾©ãé¸æãã¾ãã
page.text.select.target.message.for.linking=ãªã³ã¯åã®ã¡ãã»ã¼ã¸ãé¸æãã¾ããã¿ã¤ãã³ã°ã¨ã¿ã¼ã²ããè¨­å®ã¯ã¿ã¼ã²ãã ã¡ãã»ã¼ã¸ããåç§ããã¾ããç¾å¨ã®ã¡ãã»ã¼ã¸ã®æ¢å­ã®ã¿ã¤ãã³ã°ãã¿ã¼ã²ããè¨­å®ã¯åé¤ããã¾ãã
page.text.select.target.variant.for.message.clone=ã¡ãã»ã¼ã¸ã®è¤è£½åã®ããªã¢ã³ããé¸æãã¾ãã
page.text.select.template.package.for.touchpoint=ãã®ã¿ãããã¤ã³ããè¡¨ããã³ãã¬ã¼ã ããã±ã¼ã¸ãé¸æãã¾ã
page.text.select.tenant=ããã³ããé¸æãã¦ãã ãã
page.text.select.time.range.for.variant.version.history=ç¾å¨ã®ã¿ãããã¤ã³ã ããªã¢ã³ãã®ãã¼ã¸ã§ã³å±¥æ­´ã®æéãé¸æãã[ç¶è¡] ãã¯ãªãã¯ãã¾ãã
page.text.select.time.range.for.version.history=ç¾å¨ã®ã¢ã»ããã®ãã¼ã¸ã§ã³å±¥æ­´ã®æéãé¸æãã[ç¶è¡] ãã¯ãªãã¯ãã¾ãã
page.text.select.touchpoint.collection=ã³ã¬ã¯ã·ã§ã³ãé¸æãã¦ãã ãã
page.text.select.touchpoint=ã¿ãããã¤ã³ããé¸æãã¦ãã ãã
page.text.select.user.for.image.reassign=ãã®ç»åãå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æããå¿è¦ãªã¢ã¯ã·ã§ã³ã®èª¬æãå¥åãã¾ãã
page.text.select.user.to.assign.asset.to=ãã®ã¢ã»ãããå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æããå¿è¦ãªã¢ã¯ã·ã§ã³ã®èª¬æãå¥åãã¾ãã
page.text.select.user.to.assign.asset=ãã®ã¢ã»ãããå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æãã¾ãã
page.text.select.user.to.assign.deactivated.insert.to=éã¢ã¯ãã£ãåãããæ¿å¥ãå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æãã¾ãã
page.text.select.user.to.assign.insert.to=æ¿å¥ãå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æããå¿è¦ãªã¢ã¯ã·ã§ã³ã®èª¬æãå¥åãã¾ãã
page.text.select.user.to.assign.reinstated.insert.to=åéããæ¿å¥ãå²ãå½ã¦ãã¦ã¼ã¶ã¼ãé¸æããå¿è¦ãªã¢ã¯ã·ã§ã³ã®èª¬æãå¥åãã¾ãã
page.text.select.user=ã¦ã¼ã¶ã¼ã®é¸æ
page.text.select.variable=å¤æ°ã®é¸æ
page.text.select.variables.to.define.compound.key=1 ã¤ã¾ãã¯è¤æ°ã®å¤æ°ãé¸æãã¦è¤åã­ã¼ãå®ç¾©ãã¾ãã
page.text.select.variant.to.copy.content.to=ã³ã³ãã³ããã³ãã¼ããããªã¢ã³ããé¸æãã¾ãã
page.text.select.zone.to.add.message=ã¡ãã»ã¼ã¸ãè¿½å ããã¾ã¼ã³ãé¸æãã¾ã
page.text.select.zone=ã¾ã¼ã³ã®é¸æ
page.text.selected.messages.now.used.in.production=ããªã¢ã³ããã¢ã¯ãã£ãåããã¦ããå ´åããããã®ã¡ãã»ã¼ã¸ã¯ãã­ãã¯ã·ã§ã³ã§ä½¿ç¨ããã¾ãã
page.text.selected.variant.will.be.deleted=é¸æããããªã¢ã³ããåé¤ããã¾ãã
page.text.selected.variants.will.be.deleted=é¸æããããªã¢ã³ããåé¤ããã¾ãã
page.text.selector.definition.not.set.for.touchpoint.variant=ãã®ããªã¢ã³ãã«é¢é£ä»ããããã¿ãããã¤ã³ãã«å¯¾ããã»ã¬ã¯ã¿ã¼å®ç¾©ãè¨­å®ããã¦ãã¾ããã
page.text.selector.no.set.for.language.variant=ãã®ããªã¢ã³ãã«é¢é£ä»ããããè¨èªã«å¯¾ããã»ã¬ã¯ã¿ã¼å®ç¾©ãè¨­å®ããã¦ãã¾ããã
page.text.sessiontimeout=ã»ãã·ã§ã³ãã¿ã¤ã ã¢ã¦ããã¾ããã
page.text.sessiontimeoutdescription.signin=ä¸å®æéæä½ãè¡ãããªãã£ããããã»ã­ã¥ãªãã£ä¸ã®çç±ã«ãããµã¤ã³ã¢ã¦ããã¾ããã
page.text.sessiontimeoutdescription=ä¸å®æéæä½ãè¡ãããªãã£ããããã»ã­ã¥ãªãã£ä¸ã®çç±ã«ãããµã¤ã³ã¢ã¦ããã¾ããããµã¤ã³ã¤ã³ ãã¼ã¸ã«æ»ã£ã¦ãå¼ãç¶ã Messagepoint ãä½¿ç¨ã§ãã¾ãã
page.text.set.cell.border.all=ãã¹ã¦ã®ã»ã«æ ç·ã®è¨­å®
page.text.set.cell.border.bottom=ã»ã«ã®ä¸æ ç·ã®è¨­å®
page.text.set.cell.border.left=ã»ã«ã®å·¦æ ç·ã®è¨­å®
page.text.set.cell.border.right=ã»ã«ã®å³æ ç·ã®è¨­å®
page.text.set.cell.border.top=ã»ã«ã®ä¸æ ç·ã®è¨­å®
page.text.set.options.for.audit.report=è¦æ±ããç£æ»ã¬ãã¼ãã®å¿è¦ãªãªãã·ã§ã³ãè¨­å®ãã¾ãã
page.text.set.table.border.all=ãã¹ã¦ã®ãã¼ãã«æ ç·ã®è¨­å®
page.text.set.table.border.outer=ãã¼ãã«ã®å¤æ ç·ã®è¨­å®
page.text.share.image.to.touchpoints=ä»¥ä¸ã®ã¿ãããã¤ã³ãã§ãã®ç»åãå±æ
page.text.share.object.across.touchpoints=ä»¥ä¸ã®ã¿ãããã¤ã³ãã§ãã®ã¹ãã¼ã ãã­ã¹ã ãªãã¸ã§ã¯ããå±æ
page.text.sign.in.to.transition=ãã¬ãªãªã¼ã¹ ãã¼ã¸ã§ã³ã«ãµã¤ã³ã¤ã³
page.text.signoutsuccess=Messagepoint ããæ­£å¸¸ã«ãµã¤ã³ã¢ã¦ããã¾ããã
page.text.simulation.for=ã·ãã¥ã¬ã¼ã·ã§ã³å¯¾è±¡
page.text.simulations.which.are=æ¬¡ã®ç¶æã®ã·ãã¥ã¬ã¼ã·ã§ã³
page.text.single.selector.touchpoint.return.to.master=ãã®ã¿ãããã¤ã³ãã¯ã1 ã¤ã®ããªã¢ã³ã ã»ã¬ã¯ã¿ã¼ã«é©ç¨ããã¾ãããã®ããããã®ã¿ãããã¤ã³ãã®ããªã¢ã³ãã¯ãµãããªã¨ã¼ã·ã§ã³ã«é©ç¨ãããªãå ´åãããã¾ãããã¹ã¿ã¼ ãã¥ã¼ã«æ»ã£ã¦ããªã¢ã³ãç®¡çãç¶è¡ããããç¾å¨ã®ããªã¢ã³ãã®ã¡ãã»ã¼ã¸ ãã¥ã¼ã«åãæ¿ãã¾ãã
page.text.smart.text.which.are=æ¬¡ã®ç¶æã®ã¹ãã¼ã ãã­ã¹ã
page.text.specify.data.for.content.selection=ã³ã³ãã³ã ããªã¢ã³ã ãã¼ã¿å®ç¾©ã«è¿½å ããããªã¢ã³ã ãã¼ã¿ãæå®ãã¾ãã
page.text.specify.name.and.channel.for.touchpoint=ååãæå®ããã¿ãããã¤ã³ãã®ãã£ãã«ã¨ã³ãã¯ã¿ã¼ãé¸æãã¾ã
page.text.specify.name.and.data.for.content.variant=æ°è¦ããªã¢ã³ãã«é¢é£ä»ããããååã¨ããªã¢ã³ã ãã¼ã¿ãæå®ãã¾ãã
page.text.specify.name.for.external.event=ååãæå®ããå¤é¨ã¤ãã³ãã®ã¿ã¤ããé¸æãã¾ãã
page.text.specify.new.variant.name=ããªã¢ã³ãã®æ°ããååãæå®ãã¾ã
page.text.specify.variant.data=ã³ã³ãã³ã ããªã¢ã³ã ãã¼ã¿å®ç¾©ã«è¿½å ãããã¼ã¿ãæå®ãã¾ãã
page.text.specify.variant.name.and.data=æ°è¦ããªã¢ã³ãã«é¢é£ä»ããããååã¨ãã¼ã¿ãæå®ãã¾ãã
page.text.starting=éå§
page.text.support.dot=ãµãã¼ãã
page.text.suppress.message=é¸æããã¡ãã»ã¼ã¸ãéè¡¨ç¤ºã«ãã¾ãã? éè¡¨ç¤ºã«ããã¡ãã»ã¼ã¸ã¯ãã­ãã¯ã·ã§ã³ããé¤å¤ããã¾ãã
page.text.suppress.production=ãã­ãã¯ã·ã§ã³ãéè¡¨ç¤ºã«ãã¾ãã?
page.text.suppress.selected.messages=é¸æããã¡ãã»ã¼ã¸ãéè¡¨ç¤ºã«ãã¾ãã? éè¡¨ç¤ºã«ããã¡ãã»ã¼ã¸ã¯ãã­ãã¯ã·ã§ã³ããé¤å¤ããã¾ãã
page.text.suppressed=éè¡¨ç¤º
page.text.system.maintenance.default=Messagepoint Web ãµã¤ãã§ã¯ START_DATE ã® START_TIME ï½ END_TIME TIME_ZONE ã«ããã¦ã¡ã³ããã³ã¹ãäºå®ãã¦ãã¾ãããã®æéä¸­ã¯ããµã¤ãã®ä¸é¨ãã¼ã¸ãå©ç¨ã§ããªããªãã¾ãããä¸ä¾¿ãããããã¾ãããäºæ¿ã®ç¨ããããããé¡ãç³ãä¸ãã¾ãã
page.text.system.upgrade.did.not.finish=ã·ã¹ãã  ã¢ããã°ã¬ã¼ããå®äºãã¾ããã§ãã
page.text.target.group.is.parameterized=ãã®ãã©ã¡ã¼ã¿ã¼åãããã¿ã¼ã²ãã ã°ã«ã¼ãã¯ä½¿ç¨ããã¦ãããããå¤æ´ã§ãã¾ããã
page.text.targeting.search.and=ããã³
page.text.targeting.search.empty.or.null=ç©ºã¾ãã¯ null
page.text.targeting.search.equals=ç­ãã
page.text.targeting.search.greater.than.or.equal=æ¬¡ã®å¤ä»¥ä¸
page.text.targeting.search.greater.than=æ¬¡ã®å¤ããå¤§ãã
page.text.targeting.search.less.than.or.equal=æ¬¡ã®å¤ä»¥ä¸
page.text.targeting.search.less.than=æ¬¡ã®å¤ããå°ãã
page.text.targeting.search.not.empty.or.null=ç©ºã¾ãã¯ null ã¯ã§ãªã
page.text.targeting.search.not.equal=ç­ãããªã
page.text.targeting.search.user.specified=ã¦ã¼ã¶ã¼æå®
page.text.template.contains.zones.with.no.connector.name=ãã®ãã³ãã¬ã¼ãã«ã¯ãæå¹ãªã³ãã¯ã¿ã¼åãæå®ãã¦ããªãã¾ã¼ã³ãå«ã¾ãã¦ãã¾ãããã¹ã¦ã®ã¾ã¼ã³ã«æå¹ãªãaltãå±æ§ãå«ã¾ãã¦ãããã¨ãç¢ºèªãã¦ãã ããã
page.text.template.does.not.specify.all.zone.dimensions=ãã®ãã³ãã¬ã¼ãã«ã¯ãæå¹ãªå¯¸æ³å±æ§ãæå®ãã¦ããªãã¾ã¼ã³ãå«ã¾ãã¦ãã¾ãããã¹ã¦ã®ã¾ã¼ã³ã«æå¹ãªãé«ããããã³ãå¹ãå±æ§ãå«ã¾ãã¦ãããã¨ãç¢ºèªãã¦ãã ããã
page.text.template.file.DAT.for.upload=ã¢ããã­ã¼ãç¨ã®ãã³ãã¬ã¼ã ãã¡ã¤ã« (DAT)
page.text.template.package.uploaded=ãã³ãã¬ã¼ã ãã¹ã¯ã¼ãã¯ã¢ããã­ã¼ãæ¸ã¿ã§ãã?
page.text.template.package=ãã³ãã¬ã¼ã ããã±ã¼ã¸
page.text.template=ãã³ãã¬ã¼ã
page.text.tenant.activation.instruction=ãã®ããã³ããã¢ã¯ãã£ãã«ããã«ã¯ãæåã«æ¬¡ã®ãããããå®è¡ããå¿è¦ãããã¾ã:<br><br>1) ã¢ã¯ãã£ããªããã³ããéã¢ã¯ãã£ãã«ãã¾ãã<br>2) Messagepoint<sup>TM</sup> ãµã¼ãã¹æå½èã«åãåããã¦ã<br>Â Â Â Â è¿½å ããã³ãã®ã©ã¤ã»ã³ã¹ãåå¾ãã¾ãã<br><br>Messagepoint<sup>TM</sup> ãã¼ã  ãã¼ã¸ã«æ»ãã«ã¯ãä¸ã® [ç¶è¡] ãã¿ã³ãã¯ãªãã¯ãã¦ãã ããã
page.text.tenant.bin.assignments=ããã³ã ãã³å²ãå½ã¦
page.text.tenant.inserts.timeframe=(ããã³ããæ¿å¥ãæå®ããæé)
page.text.tenant.limit.reached.part1=ããã³ãã®ã¢ã¯ãã£ãåãå®è¡ããã¾ãããæ¢ã«ã¢ã¯ãã£ããªããã³ãã®ã©ã¤ã»ã³ã¹æ°ã®ä¸é
page.text.tenant.limit.reached.part2=ã«å°éãã¦ãã¾ãã
page.text.tenant.limit.reached.part3=ããã³ãã®ã¢ã¯ãã£ãåãå®è¡ããã¾ãããæ¢ã«ã¢ã¯ãã£ããªããã³ãæ°ã®ä¸éã«å°éãã¦ãã¾ãã
page.text.tenant.participation.will.also.be.removed.for.schedule=ããã³ãã®åå ãåé¤ããã¾ããé¢é£ããããã³ããããã®ã¹ã±ã¸ã¥ã¼ã«ã®è¡¨ç¤ºããã®ã¹ã±ã¸ã¥ã¼ã«ã¸ã®åå ã¯ã§ããªããªãã¾ãã
page.text.tenant.schedules=ããã³ã ã¹ã±ã¸ã¥ã¼ã«
page.text.tenant.thresholds.same.as.default=ããã³ãã®ãããå¤ã¯ããã©ã«ãå®ç¾©ã¨åãã§ã
page.text.tenantconfirm.action.activate=ä»¥ä¸ã«ç¤ºãããã³ãã®ã¢ã¯ãã£ãåãè¦æ±ããã¾ããã
page.text.tenantconfirm.action.deactivate=ä»¥ä¸ã«ç¤ºãããã³ãã®éã¢ã¯ãã£ãåãè¦æ±ããã¾ããã
page.text.tenantconfirm.consequence.activate1=ããã³ããã¢ã¯ãã£ãã«ããã¨ãä»»æã®ã¢ã¯ãã£ã ã¦ã¼ã¶ã¼ã Messagepoint ã«ãµã¤ã³ã¤ã³ã§ããããã«ãªãã¾ãã
page.text.tenantconfirm.consequence.activate2=ã¢ã¯ãã£ãåãå¾ããåãæ¶ãã«ã¯ããã®ããã³ããéã¢ã¯ãã£ãåã«ãã¾ãã
page.text.tenantconfirm.consequence.activate3=ãã®ããã³ããã¢ã¯ãã£ãã«ããã¨ãã¢ã¯ãã£ããªããã³ãã®æ°ã {0} ã«ãªãã¾ããç¾å¨ã©ã¤ã»ã³ã¹ãä»ä¸ããã¦ããã¢ã¯ãã£ããªããã³ãã®æ°ã¯ {1} ã§ãã
page.text.tenantconfirm.consequence.deactivate1=ããã³ããéã¢ã¯ãã£ãã«ããã¨ãä»»æã®ã¢ã¯ãã£ã ã¦ã¼ã¶ã¼ã Messagepoint ã«ãµã¤ã³ã¤ã³ã§ããªããªãã¾ãã
page.text.tenantconfirm.consequence.deactivate2=éã¢ã¯ãã£ãåãå¾ããåãæ¶ãã«ã¯ããã®ããã³ããã¢ã¯ãã£ãã«ãã¾ãã
page.text.tenantconfirm.instruction.activate=ä»¥ä¸ã®åå®¹ãç¢ºèªãããã®ãã¼ã¸ã®ä¸é¨ã«ããè©²å½ãããã¿ã³ãä½¿ç¨ãã¦ããã³ãã®ã¢ã¯ãã£ãåãç¢ºèªã¾ãã¯ã­ã£ã³ã»ã«ãã¦ãã ããã
page.text.tenantconfirm.instruction.deactivate=ä»¥ä¸ã®åå®¹ãç¢ºèªãããã®ãã¼ã¸ã®ä¸é¨ã«ããè©²å½ãããã¿ã³ãä½¿ç¨ãã¦ããã³ãã®éã¢ã¯ãã£ãåãç¢ºèªã¾ãã¯ã­ã£ã³ã»ã«ãã¦ãã ããã
page.text.test.of=ãã¹ã
page.text.tests.which.are=æ¬¡ã®ç¶æã®ãã¹ã
page.text.thank.you=Messagepoint ããå©ç¨ããã ããããã¨ããããã¾ãã
page.text.the.following=ä»¥ä¸
page.text.there.are.no.variables=å¤æ°ãããã¾ãã
page.text.threshold.overrides.for=ãããå¤ãªã¼ãã¼ã©ã¤ãã®å¯¾è±¡
page.text.thresholds=ãããå¤
page.text.timing.no.end.date=çµäºæ¥ãªã
page.text.timing.starts.now=ä»ããéå§
page.text.to.begin=éå§ãã
page.text.to.request.a.new.one=æ°ããé ç®ãè¦æ±ãã¾ãã
page.text.toggle.advanced.search.to.include.markup=ãã³ã: ã³ã³ãã³ã ãã¼ã¯ã¢ãã ã¿ã°ãæ¤ç´¢ã«å«ããå ´åã¯é«åº¦ãªæ¤ç´¢ã«åãæ¿ãã¾ãã
page.text.toggle.background.images.panel=èæ¯ç»åããã«ã®åãæ¿ã
page.text.toggle.delivery.panel=éä¿¡ããã«ã®åãæ¿ã
page.text.toggle.touchpoint.assignment.panel=ã¿ãããã¤ã³ãå²ãå½ã¦ããã«ã®åãæ¿ã
page.text.toreturntohomepage=ããã©ã«ãã® Messagepoint ãã¼ã ãã¼ã¸ã«æ»ãã¾ãã
page.text.toreturntosigninpage=ã¢ããªã±ã¼ã·ã§ã³ã«ååº¦ãµã¤ã³ã¤ã³ãã¾ãã
page.text.tosignout=ãµã¤ã³ã¢ã¦ããã¾ãã
page.text.touchpoint.context.not.apply.for.proofing.data=ç¾å¨ã®ã¿ãããã¤ã³ã ã³ã³ãã­ã¹ãã¯æ ¡æ­£ãã¼ã¿ã«é©ç¨ããã¾ããã
page.text.touchpoint.does.not.apply.for.communications.workflow=ç¾å¨ã®ã¿ãããã¤ã³ã ã³ã³ãã­ã¹ãã¯ã³ãã¥ãã±ã¼ã·ã§ã³ ã¯ã¼ã¯ãã­ã¼ã«é©ç¨ããã¾ããã
page.text.touchpoint.does.not.apply.for.order.entry=ç¾å¨ã®ã¿ãããã¤ã³ã ã³ã³ãã­ã¹ãã¯ãªã¼ãã¼ ã¨ã³ããªã«é©ç¨ããã¾ããã
page.text.touchpoint.does.not.apply.for.variant.workflow=ç¾å¨ã®ã¿ãããã¤ã³ã ã³ã³ãã­ã¹ãã¯ããªã¢ã³ã ã¯ã¼ã¯ãã­ã¼ã«é©ç¨ããã¾ããã
page.text.touchpoint.has.no.order.entry.items=ãã®ã¿ãããã¤ã³ãã«ã¯ç¾å¨ãªã¼ãã¼ ã¨ã³ããª ã¢ã¤ãã ãå­å¨ãã¾ããã
page.text.touchpoint.variants.have.proofing.data=ãã¹ã¦ã®ã¿ãããã¤ã³ã ããªã¢ã³ãã§æ ¡æ­£ãã¼ã¿ãæ§ææ¸ã¿ã§ãã?
page.text.transparent.background=èæ¯ãéæã«ãã¾ãã?
page.text.treat.qualified.messages=é©æ ¼ãªã¡ãã»ã¼ã¸ãéä¿¡æ¸ã¿ã¡ãã»ã¼ã¸ã¨ãã¦æ±ã
page.text.turn.node.off.line=é¸æããã¤ã³ã¹ã¿ã³ã¹ã¯ãªãã©ã¤ã³ã«ãªãã¾ã!
page.text.turn.node.online=ãã®ã¤ã³ã¹ã¿ã³ã¹ããªã³ã©ã¤ã³ã«ãã¾ãã?
page.text.type.content.you.are.searching.for=ä¸è¨ã®å¥åã«æ¤ç´¢ããã³ã³ãã³ããå¥åãã[æ¤ç´¢] ãã¯ãªãã¯ãã¾ãã
page.text.unlimited=ç¡å¶é
page.text.update.data.collection.of.touchpoint.in.packaged.collection=ãã®ã¿ãããã¤ã³ãã¯ããã±ã¼ã¸ãããã³ã¬ã¯ã·ã§ã³ã®ä¸é¨ã§ããé¢é£ãããã¼ã¿ ã³ã¬ã¯ã·ã§ã³ãå¤æ´ããã¨ãæ¬¡ã®ã¿ãããã¤ã³ããæ´æ°ããã¾ã:
page.text.upload.backgrounds=èæ¯ã®ã¢ããã­ã¼ã
page.text.uploading.image=ç»åã®ã¢ããã­ã¼ãä¸­...
page.text.user.specified.values=ã¦ã¼ã¶ã¼æå®å¤?
page.text.username.change.notification=ã¦ã¼ã¶ã¼åå¤æ´éç¥ã
page.text.username.has.been.changed=Messagepoint(tm) ã¢ããªã±ã¼ã·ã§ã³ã§ã¦ã¼ã¶ã¼åãå¤æ´ããã¾ããã\n\n\næ°è¦ã¦ã¼ã¶ã¼å: {0}\nå¤ãã¦ã¼ã¶ã¼å: {1}\n\n\nãã®ã¡ã¼ã«ã«ã¯è¿ä¿¡ããªãã§ãã ã ããã
page.text.users.may.select=ã¦ã¼ã¶ã¼ãé¸æã§ãã¾ã
page.text.validate.selected.node=é¸æããã¤ã³ã¹ã¿ã³ã¹ã® DB ã¹ã­ã¼ããæ¤è¨¼ãã¾ãã
page.text.validator.alphabetic=è±å­
page.text.validator.alphanum.at.dot.dash.underscore.apos=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥ãã¢ãããã¼ã¯ãããããã¢ãã¹ãã­ãã£
page.text.validator.alphanum.dash.space=è±æ°å­ãããã·ã¥ãã¹ãã¼ã¹
page.text.validator.alphanum.dash.underscore.apos=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥ãã¢ãã¹ãã­ãã£\t
page.text.validator.alphanum.dash.underscore=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥
page.text.validator.alphanum.dot.dash.backslash.slash.colon.space=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥ãããããããã¯ã¹ã©ãã·ã¥ãã¹ã©ãã·ã¥ãã³ã­ã³ãã¹ãã¼ã¹
page.text.validator.alphanum.dot.dash.backslash.slash.space=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥ãããããããã¯ã¹ã©ãã·ã¥ãã¹ã©ãã·ã¥ãã¹ãã¼ã¹
page.text.validator.alphanum.dot.dash.underscore=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥ãããã
page.text.validator.alphanum.dot.slash=è±æ°å­ãããããã¹ã©ãã·ã¥
page.text.validator.alphanum.parenth.dash.space=è±æ°å­ããã£ããããã·ã¥ãã¹ãã¼ã¹
page.text.validator.alphanum.space.dash.underscore.apos=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥ãã¢ãã¹ãã­ãã£ãã¹ãã¼ã¹
page.text.validator.alphanum.space.dash.underscore=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥ãã¹ãã¼ã¹
page.text.validator.alphanum.space.dot.dash.comma.semi.underscore=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥ãããããã³ã³ããã»ãã³ã­ã³ãã¹ãã¼ã¹
page.text.validator.alphanum.space.dot.dash.underscore.apos=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥ãããããã¢ãã¹ãã­ãã£ãã¹ãã¼ã¹
page.text.validator.alphanum.space.dot.dash.underscore=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥ãããããã¹ãã¼ã¹
page.text.validator.alphanum.symbols.space=è±æ°å­ãã»ã¨ãã©ã®è¨å·ãã¹ãã¼ã¹
page.text.validator.alphanum.symbols=è±æ°å­ãã»ã¨ãã©ã®è¨å·
page.text.validator.alphanum.underscore.dash.apos.dot.comma.colon.at.numbersign.square.space=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥ãã¢ãã¹ãã­ãã£ãããããã³ã³ããã³ã­ã³ãã¢ãããã¼ã¯ãçªå·è¨å·ãè§ãã£ããã¹ãã¼ã¹
page.text.validator.alphanum.underscore.dash.apos.parenth.space=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥ãã¢ãã¹ãã­ãã£ãå·¦ããã³å³ãã£ããã¹ãã¼ã¹
page.text.validator.alphanum.underscore.dash.numbersign.space=è±æ°å­ãã¢ã³ãã¼ã¹ã³ã¢ãããã·ã¥ãçªå·è¨å·ãã¹ãã¼ã¹
page.text.validator.alphanum=è±æ°å­
page.text.validator.any.character=ä»»æã®æå­
page.text.validator.numeric=æ°å¤
page.text.values.must.meet.criteria.for.condition=ãã®æ¡ä»¶ã«ãã¹ããã«ã¯ããããã®å¤ãåºæºãæºããå¿è¦ãããã¾ã
page.text.variant.has.no.working.copy=ãã®ããªã¢ã³ãã«ã¯ä½æ¥­ã³ãã¼ãããã¾ãããã³ã³ãã³ãã®ç·¨éãå§ããåã«ä½æ¥­ã³ãã¼ãä½æãã¦ãã ããã
page.text.variant.is.not.active=ãã®ããªã¢ã³ãã¯ã¢ã¯ãã£ãã§ã¯ããã¾ãããã³ã³ãã³ããã¢ã¯ãã£ãã«ãªãã«ã¯ãäºåã«ã¢ã¯ãã£ãå/æ¿èªãè¡ãå¿è¦ãããã¾ãã
page.text.variants.for.each.touchpoint.language=ã¿ãããã¤ã³ãã®åè¨èªã®ããªã¢ã³ããä½ææ¸ã¿ã§ãããªã¢ã³ãã«ã»ã¬ã¯ã¿ã¼ ãã¼ã¿ãããã¾ãã?
page.text.variants.which.are=æ¬¡ã®ç¶æã®ããªã¢ã³ã
page.text.view.dynamic.content=åçã³ã³ãã³ãã®è¡¨ç¤º
page.text.view.fullscreen=å¨ç»é¢è¡¨ç¤º
page.text.view.references.by=åç§ã®è¡¨ç¤º
page.text.view.variant.data=ããªã¢ã³ã ãã¼ã¿ã®è¡¨ç¤º
page.text.view=è¡¨ç¤º
page.text.warning.email.touchpoint.contains.template.package.match.with.export=è­¦å! ãã®é»å­ã¡ã¼ã« ã¿ãããã¤ã³ãã«ã¯ãã³ãã¬ã¼ã ããã±ã¼ã¸ãå«ã¾ãã¦ãã¾ãããã³ãã¬ã¼ã ããã±ã¼ã¸ããã¦ã³ã­ã¼ãããå°æ¥ã®ã¤ã³ãã¼ãã«åãã¦ã¨ã¯ã¹ãã¼ã ãã¡ã¤ã«ã¨ã¨ãã«ä¿ç®¡ããå¿è¦ãããã¾ãã
page.text.warning.no.data.collection.for.touchpoint=è­¦å! ãã®ã¿ãããã¤ã³ãã«ã¯ãã¼ã¿ ã³ã¬ã¯ã·ã§ã³ãããã¾ããã
page.text.warning.no.template.package.for.touchpoint=è­¦å! ãã®é»å­ã¡ã¼ã«/Web ã¿ãããã¤ã³ãã«ã¯ãã³ãã¬ã¼ã ããã±ã¼ã¸ãã¾ã å«ã¾ãã¦ãã¾ããã
page.text.warning.upmapped.reference.variables.for.touchpoint=è­¦å! ãã®ã¿ãããã¤ã³ãã«ã¯ãããã³ã°ããã¦ããªãåç§å¤æ°ããããã³ã°ã§ããªã 1 ã¤ä»¥ä¸ã®åç§å¤æ°ãå­å¨ãã¾ãã
page.text.warning.upmapped.variables.for.touchpoint=è­¦å! ãã®ã¿ãããã¤ã³ãã«ã¯ãããã³ã°ããã¦ããªããã¼ã¿è¦ç´ ãå«ãå¤æ°ãå­å¨ãã¾ãã
page.text.warning=è­¦å!
page.text.what.is.customer.identifier.for.communication=ãã®ã³ãã¥ãã±ã¼ã·ã§ã³ã®é¡§å®¢ ID ã¯ä½ã§ãã?
page.text.when.greater.than=æ¬¡ã®å¤ããå¤§ããå ´å
page.text.when.selected.comma=é¸æããã¦ããå ´åã
page.text.workflow.has.no.steps=ç¾å¨ãã®ã¯ã¼ã¯ãã­ã¼ã«ã¯ã¹ããããå­å¨ãã¾ããããã®ã¯ã¼ã¯ãã­ã¼ãä½¿ç¨ããã¢ã¤ãã ã¯ãå²ãå½ã¦ãããã¦ã¼ã¶ã¼ãç´æ¥ã¢ã¯ãã£ãåã§ãã¾ããæ¿èªã¯å¿è¦ããã¾ããã
page.text.workflow.owners.may.approve.step=ã¯ã¼ã¯ãã­ã¼ææèã¯ã¹ããããæ¿èªã§ãã¾ãããã®ã¢ã»ãããæ¿èªãã¾ãã?
page.text.workflow.owners.may.reject.step=ã¯ã¼ã¯ãã­ã¼ææèã¯ã¢ã»ããã®æ¿èªãæå¦ã§ãã¾ãããã®ã¢ã»ãããæå¦ãã¾ãã?
page.text.workflow.step.must.contain.at.least.one.approver=åã¯ã¼ã¯ãã­ã¼ ã¹ãããã«ã¯ãå°ãªãã¨ã 1 äººã®æ¿èªèãå«ããå¿è¦ãããã¾ãã1 äººã¾ãã¯è¤æ°ã®ã¦ã¼ã¶ã¼ãæ¿èªèãªã¹ããããã®ã¹ãããã«ãã©ãã°ãã¾ããæ³¨æ: ã¯ã¼ã¯ãã­ã¼ææèãæ¿èªèã«ãããã¨ã¯ã§ãã¾ããã
page.text.working.copies.associated.with.test.have.changed=ååã®ãã¹ãå®è¡å¾ã«ããã®ãã¹ã ã·ããªãªåã®é¸æãããä¸é¨ã®ä½æ¥­ã³ãã¼ ã¡ãã»ã¼ã¸ãã¢ã¯ãã£ãåã¾ãã¯ç ´æ£ããã¾ããã[ç¶è¡] ãã¯ãªãã¯ãã¦ç®çã®ä½æ¥­ã³ãã¼ ã¡ãã»ã¼ã¸ã§ã·ããªãªãæ´æ°ãããã[ã­ã£ã³ã»ã«] ãã¯ãªãã¯ãã¦ãã®ãã¹ãã®å®è¡ãä¸­æ­¢ãã¦ãã ããã
page.text.working.copies=ä½æ¥­ã³ãã¼
page.text.would.you.like.activate.branch=ãã®ãã¡ã¤ã³ãã¢ã¯ãã£ãã«ãã¾ãã?
page.text.would.you.like.reject=ãã®ã¢ã»ãããæå¦ãã¾ãã?
page.text.would.you.like.to.activate.communications=ã³ãã¥ãã±ã¼ã·ã§ã³ãã¢ã¯ãã£ãã«ãã¾ãã?
page.text.would.you.like.to.activate.image=ãã®ç»åãã¢ã¯ãã£ãã«ãã¾ãã?
page.text.would.you.like.to.activate.images=ç»åãã¢ã¯ãã£ãã«ãã¾ãã?
page.text.would.you.like.to.activate.shared.content=ãã®å±æã³ã³ãã³ããã¢ã¯ãã£ãã«ãã¾ãã?
page.text.would.you.like.to.activate=ãã®ã¢ã»ãããã¢ã¯ãã£ãã«ãã¾ãã?
page.text.would.you.like.to.approve.step=ãã®ã¹ããããæ¿èªãã¾ãã?
page.text.would.you.like.to.approve=ãã®ã¢ã»ãããæ¿èªãã¾ãã?
page.text.would.you.like.to.clone.image=ç¾å¨é¸æãã¦ããç»åãè¤è£½ãã¾ãã?
page.text.would.you.like.to.clone.message=ãã®ã¡ãã»ã¼ã¸ãè¤è£½ãã¾ãã?
page.text.would.you.like.to.clone.selected.asset=ç¾å¨é¸æãã¦ããã¢ã»ãããè¤è£½ãã¾ãã?
page.text.would.you.like.to.clone.this.image=ãã®ç»åãè¤è£½ãã¾ãã?
page.text.would.you.like.to.close=ãã®ã¢ã»ãããè¤è£½ãã¾ãã?
page.text.would.you.like.to.cms.sync=CMS ã¢ã»ãããåæãã¾ãã?
page.text.would.you.like.to.deactivate.branch=ãã®ãã¡ã¤ã³ãéã¢ã¯ãã£ãã«ãã¾ãã?
page.text.would.you.like.to.download.message.template=ã¡ãã»ã¼ã¸ ãã³ãã¬ã¼ãããã¦ã³ã­ã¼ããã¾ãã?
page.text.would.you.like.to.download.variants=ãã¹ã¦ã®ããªã¢ã³ãããã¦ã³ã­ã¼ããã¾ãã?
page.text.yes.default.locale=ã¯ã (ããã©ã«ã ã­ã±ã¼ã«)
page.text.you.are.about.to.discard.working.copy.of.variant=é¸æããããªã¢ã³ãã®ä½æ¥­ã³ãã¼ãç ´æ£ãããã¨ãã¦ãã¾ããããã¯åãæ¶ããã¨ãã§ãã¾ãããç¶è¡ãã¾ãã?
page.text.you.have.asked.to.create.working.copy.of.asset=ãã®ã¢ã»ããã®ä½æ¥­ã³ãã¼ã®ä½æãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã«ãã£ã¦é¢é£ããã¢ã¯ãã£ã ã¿ãããã¤ã³ã ã¢ã»ãããå¤æ´ããããã¨ã¯ããã¾ããã
page.text.you.have.asked.to.remove.asset=ãã®ã¢ã»ããã®å®å¨åé¤ãè¦æ±ããã¾ããããã®ã¢ã¯ã·ã§ã³ã¯åãæ¶ããã¨ãã§ãã¾ããã
page.text.yourpersonalsettings=Messagepoint ã®åäººè¨­å®ã®æ´æ°ã¾ãã¯æ°è¦ãã¹ã¯ã¼ãã®æå®ã¨ç¢ºèªãè¡ã£ã¦ãã ãã
page.text.zone.can.flow=ã¾ã¼ã³ã¯æ°ãããã¼ã¸ã«ãã­ã¼ã§ãã¾ã
page.text.zone.can.grow=ã¾ã¼ã³ã¯ãã¼ã¸åã§æ¡å¼µã§ãã¾ã
page.text.zone.minimum.size=ã¾ã¼ã³ã¯ãã®ãµã¤ãºä»¥ä¸ã§ãªããã°ãªãã¾ãã
page.text.zones.which.are=æ¬¡ã®ç¶æã®ã¾ã¼ã³
page.title.bulk.upload.confirm.heading=ä¸æ¬ã¢ããã­ã¼ãã®ç¢ºèª
page.title.bulk.upload.heading=ä¸æ¬ã¢ããã­ã¼ã
page.title.import.confirm.heading=ã¤ã³ãã¼ãã®ç¢ºèª
page.title.newpassword.confirmation=æ°è¦ãã¹ã¯ã¼ãã®ç¢ºèª
page.title.newuserid.confirmation=æ°è¦ã¦ã¼ã¶ã¼åã®ç¢ºèª
page.title.newuseridpassword.confirmation=æ°è¦ã¦ã¼ã¶ã¼åã¨ãã¹ã¯ã¼ãã®ç¢ºèª
page.title.role.overview=å½¹å²æ¦è¦
page.title.role.permissions=å½¹å²æ¨©é
page.title.tenantconfirm.activate.heading=ããã³ãã®ã¢ã¯ãã£ãåã®ç¢ºèª
page.title.tenantconfirm.activate=ããã³ãã®ã¢ã¯ãã£ãå
page.title.tenantconfirm.deactivate.heading=ããã³ãã®éã¢ã¯ãã£ãåã®ç¢ºèª
page.title.tenantconfirm.deactivate=ããã³ãã®éã¢ã¯ãã£ãå
page.title.tenantconfirm.overview=ããã³ãæ¦è¦
page.title.touchpoint.import.confirm.heading=ã¿ãããã¤ã³ã ã¤ã³ãã¼ãã®ç¢ºèª
page.work.flow.action.addcontent=ã³ã³ãã³ãã®è¿½å 
page.work.flow.action.approvalready=æ¿èªæºåå®äº
page.work.flow.action.markworkcomplete=ãªãªã¼ã¹ãã¦æ¿èªãåãã
password.validation.errors.maximumLength=ãã¹ã¯ã¼ãã¯ {0} æå­ä»¥åã§ãªããã°ãªãã¾ããã
password.validation.errors.minimumLength=ãã¹ã¯ã¼ãã¯ {0} æå­ä»¥ä¸ã§ãªããã°ãªãã¾ããã
password.validation.errors.mustContainAtleastOneLowerCaseChar=ãã¹ã¯ã¼ãã«ã¯å°æå­ã 1 ã¤ä»¥ä¸å«ã¾ãã¦ããå¿è¦ãããã¾ã
password.validation.errors.mustContainAtleastOneNumericChar=ãã¹ã¯ã¼ãã«ã¯æ°å­ã 1 ã¤ä»¥ä¸å«ã¾ãã¦ããå¿è¦ãããã¾ã
password.validation.errors.mustContainAtleastOneSpecialChar=ãã¹ã¯ã¼ãã«ã¯ç¹æ®æå­ã 1 ã¤ä»¥ä¸å«ã¾ãã¦ããå¿è¦ãããã¾ã
password.validation.errors.mustContainAtleastOneUpperCaseChar=ãã¹ã¯ã¼ãã«ã¯å¤§æå­ã 1 ã¤ä»¥ä¸å«ã¾ãã¦ããå¿è¦ãããã¾ã
password.validation.errors.mustNotBeRepeated.within.period=éå» {0} ã«æä»¥åã«ä½¿ç¨ããã¦ããªããã¹ã¯ã¼ããæå®ãã¦ãã ããã
password.validation.errors.mustNotBeRepeated=éå» {0} åã®å¤ã¨ç°ãªããã¹ã¯ã¼ããæå®ãã¦ãã ããã
password.validation.errors.mustNotContainFLName=ãã¹ã¯ã¼ãã«æ°åãå«ããªãã§ãã ããã
password.validation.errors.mustNotContainUsername=ãã¹ã¯ã¼ãã«ã¦ã¼ã¶ã¼åãå«ããªãã§ãã ããã
password.validation.errors.repeating=ãã¹ã¯ã¼ãã«åãæå­ãé£ç¶ãã¦ä½¿ç¨ãããã¨ã¯ã§ãã¾ãã
service.default.language.email.template.not.found=ããã©ã«ãè¨èªã®é»å­ã¡ã¼ã« ãã³ãã¬ã¼ããè¦ã¤ããã¾ãã
service.email.template.image.size.too.large=ãã³ãã¬ã¼ãç»åã¢ã»ãã {0} ããã®éä¿¡ã§è¨±å¯ãããæå¤§ãã¡ã¤ã« ãµã¤ãºãè¶ãã¦ãã¾ã ({1}kb)ã
service.email.template.language.more.than.one.template.file={1} ã«å¯¾ããè¤æ°ã®ãã³ãã¬ã¼ã ãã¡ã¤ã« {0} ãè¦ã¤ããã¾ãã
service.email.template.language.no.images.directory={0} ã«å¯¾ããç»åãã£ã¬ã¯ããªãè¦ã¤ããã¾ãã
service.email.template.language.no.template.file={0} ã«å¯¾ãããã³ãã¬ã¼ã ãã¡ã¤ã«ãè¦ã¤ããã¾ãã
service.email.template.language.not.found={0} ã«å¯¾ãããã£ã¬ã¯ããªãè¦ã¤ããã¾ããã§ãã
service.email.template.none.existing.zone.or.template.modifier.found=ãã¹ã¿ã¼ ãã³ãã¬ã¼ãã§ 1 ã¤ä»¥ä¸ã®ã¾ã¼ã³ã¾ãã¯ãã³ãã¬ã¼ã ã¢ãã£ãã¡ã¤ã¢ã¼ãè¦ã¤ããã¾ããã
service.email.template.none.language.found=ãã®ã¿ãããã¤ã³ãã§æ§æãããããã®è¨èªãã¢ããã­ã¼ãããããã±ã¼ã¸ã§è¡¨ç¤ºããã¾ãã
service.import.constants.too.long=255 æå­ãããé·ãã°ã­ã¼ãã«å®æ° \"{0}\" ãã¤ã³ãã¼ããããã¨ãã¦ãã¾ããã¤ã³ãã¼ããä¸­æ­¢ããã¾ããã
service.import.content.library.does.not.exist=ç»åã©ã¤ãã©ãª ã¤ã³ã¹ã¿ã³ã¹ãå­å¨ãã¾ããã
service.import.instance.does.not.exist=GUID \"{0}\" ã®ã¤ã³ã¹ã¿ã³ã¹ãå­å¨ãã¾ããã
service.import.instance.is.not.working.copy=ã¤ã³ã¹ã¿ã³ã¹ {0} ã¯ä½æ¥­ã³ãã¼ã§ã¯ããã¾ããã
service.import.touchpoint.does.not.exist=GUID \"{0}\" ã®ã¿ãããã¤ã³ããå­å¨ãã¾ããã
service.import.touchpoint.name.exists=åãååã®ã¿ãããã¤ã³ããæ¢ã«å­å¨ãã¾ã
service.import.variable.exists=ç°ãªããã¼ã/ããã³ã ã¬ãã«ã«æ¢ã«å­å¨ããå¤æ° \"{0}\" ãã¤ã³ãã¼ããããã¨ãã¦ãã¾ããã¤ã³ãã¼ããä¸­æ­¢ããã¾ããã
service.proxy.login.delete.mapped.pod=ãããã³ã°ãããããããåé¤ãããã¨ãã¦ãã¾ããåé¤ã¢ã¯ã·ã§ã³ãä¸­æ­¢ããã¾ããã
service.touchpoint.language.exists=è¨èª \"{0}\" ã¯ãã®ã¿ãããã¤ã³ãåã«æ¢ã«å­å¨ãã¾ãã
service.validation.admin.role.roleExists=ãã®ååã®å½¹å²ãæ¢ã«å­å¨ãã¾ããå¥ã®ååãå¥åãã¦ãã ããã
service.validation.admin.role.roleIsInUse=å½¹å²ã¯ç¾å¨ä½¿ç¨ããã¦ããããåé¤ã§ãã¾ããã
service.validation.admin.role.roleNameInValid=å½¹å²åãç¡å¹ã§ã
service.validation.admin.securitySettings.maxattempts=æå¤§è©¦è¡åæ°ã¯ 0 ããå¤§ãããªããã°ãªãã¾ãã
service.validation.admin.securitySettings.maximumUserIdLength.lessthanminimum=[ã¦ã¼ã¶ã¼åã®æå¤§æå­æ°] ã¯ [ã¦ã¼ã¶ã¼åã®æå°æå­æ°] ãããå¤§ãããã¦ãã ããã
service.validation.admin.securitySettings.maximumUserIdLength.outofrange=[ã¦ã¼ã¶ã¼åã®æå¤§æå­æ°] ã¯ 2 ï½ 80 ã®éã®æ°å¤ã«ãã¦ãã ããã
service.validation.admin.securitySettings.minimumPasswordLength.outofrange=[ãã¹ã¯ã¼ãã®æå°æå­æ°] ã¯ 6 ï½ 32 ã®éã®æ°å¤ã«ãã¦ãã ããã
service.validation.admin.securitySettings.minimumUserIdLength.outofrange=[ã¦ã¼ã¶ã¼åã®æå°æå­æ°] ã¯ 2 ï½ 80 ã®éã®æ°å¤ã«ãã¦ãã ããã
service.validation.admin.user.emaiAddressExists=é»å­ã¡ã¼ã« ã¢ãã¬ã¹ãæ¢ã«ä½¿ç¨ããã¦ãã¾ã
service.validation.admin.user.emaiAddressInvalid=é»å­ã¡ã¼ã« ã¢ãã¬ã¹ãç¡å¹ã§ã
service.validation.admin.user.firstNameInvalid=åãç¡å¹ã§ã
service.validation.admin.user.lastNameInvalid=å§ãç¡å¹ã§ã
service.validation.admin.user.passwordInvalid=ãã¹ã¯ã¼ããç¡å¹ã§ã
service.validation.admin.user.userIdAlreadyExists=ã¦ã¼ã¶ã¼åãæ¢ã«ä½¿ç¨ããã¦ãã¾ã
service.validation.admin.user.userIdCannotContainSpaces=ã¦ã¼ã¶ã¼åã«ã¹ãã¼ã¹ãå«ãããã¨ã¯ã§ãã¾ããã
service.validation.admin.user.userIdInvalid=ã¦ã¼ã¶ã¼åãç¡å¹ã§ã
service.validation.admin.user.userIdLength=ã¦ã¼ã¶ã¼åã¯ {0} ï½ {1} æå­ã§æå®ãã¦ãã ããã
service.validation.admin.user.usermusthaverole=ã¦ã¼ã¶ã¼ã®å½¹å²ãé¸æãã¦ãã ãã
service.validation.common.illegalName=ååãä¸æ­£ã§ãã{0}
service.validation.lookup.import.error.in.file=å¥åãã¡ã¤ã«ã®è¡ {0} ã«ã¨ã©ã¼ãå­å¨ãã¾ã
service.validation.lookup.import.general.error=ãã¡ã¤ã«åã«ä¸è¬ã¨ã©ã¼ãå­å¨ãã¾ã
service.validation.message.cloneContentLibrary.contentLibraryDoesNotExist=ç»åã©ã¤ãã©ãª ã¢ã¤ãã ãå­å¨ãã¾ããã
service.validation.message.cloneEmbeddedContent.embeddedContentDoesNotExist=åãè¾¼ã¿ã³ã³ãã³ããå­å¨ãã¾ããã
service.validation.message.cloneMessage.messageDoesNotExist=ã¡ãã»ã¼ã¸ãå­å¨ãã¾ããã
static.type.archive.type.deleted=åé¤æ¸ã¿
static.type.archive.type.none=ãªã
static.type.archive.type.temporary=ä¸æçãªãã­ãã¯ã·ã§ã³ä¼æ­¢
static.type.background.theme.blue=é
static.type.background.theme.dark_green=æ¿ãç·
static.type.background.theme.gray=ç°è²
static.type.background.theme.light_blue=æ°´è²
static.type.background.theme.orange=ãªã¬ã³ã¸
static.type.background.theme.red=èµ¤
static.type.background.theme.yellow=é»è²
static.type.channel.composition=ã³ã³ãã¸ã·ã§ã³
static.type.channel.email=é»å­ã¡ã¼ã«
static.type.channel.generic=ä¸è¬
static.type.channel.sms=SMS
static.type.channel.web=Web
static.type.condition.type.all_of=ãã¹ã¦ (ãã§ãã¯ããã¯ã¹)
static.type.condition.type.any_of=ãããã§ã (ãã§ãã¯ããã¯ã¹)
static.type.condition.type.one_of=ãããã (ã©ã¸ãªãã¿ã³)
static.type.connector.clickatell=Clickatell
static.type.connector.e_messaging=e-Messaging
static.type.connector.exacttarget=ExactTarget
static.type.connector.flat_files=ãã©ãã ãã¡ã¤ã«
static.type.connector.ftp=FTP
static.type.connector.gmc=GMC
static.type.connector.hp_exstream=HP Exstream
static.type.connector.native=ãã¤ãã£ã
static.type.connector.sendmail=Sendmail
static.type.connector.xml=XML
static.type.content.association.type.custom=ã«ã¹ã¿ã 
static.type.content.association.type.empty=ç©º
static.type.content.association.type.none=ãªã
static.type.content.association.type.reference=åç§
static.type.content.association.type.same.as.default=ããã©ã«ãã¨åã
static.type.content.association.type.suppress=éè¡¨ç¤º
static.type.content.selection.type.changed=å¤æ´æ¸ã¿
static.type.content.selection.type.content.library=åç
static.type.content.selection.type.embedded=åç
static.type.content.selection.type.global=ã°ã­ã¼ãã«
static.type.content.selection.type.message=åç
static.type.content.selection.type.new=æ°è¦
static.type.content.selection.type.regular=éç
static.type.content.selection.type.touchpoint.content=æ§é å
static.type.content.selection.type.unchanged=å¤æ´ãªã
static.type.content.type.graphic=ã°ã©ãã£ãã¯
static.type.content.type.multi_part=è¤æ°ãã¼ã
static.type.content.type.text=ãã­ã¹ã
static.type.content.type.video=ãããª
static.type.daily.frequency.type.everyday=æ¯æ¥ (æææ¥ - æ¥ææ¥)
static.type.daily.frequency.type.weekdays=å¹³æ¥ã®ã¿ (æææ¥ - éææ¥)
static.type.data.comparison.type.between=ãã
static.type.data.comparison.type.contains=å«ã
static.type.data.comparison.type.empty_or_null=ç©ºã¾ãã¯ null
static.type.data.comparison.type.ends_with=æ¬¡ã§çµãã
static.type.data.comparison.type.equals=ç­ãã
static.type.data.comparison.type.greater_than=æ¬¡ã®å¤ããå¤§ãã
static.type.data.comparison.type.greater_than_or_equal_to=æ¬¡ã®å¤ä»¥ä¸
static.type.data.comparison.type.is_not_one_of=ä¸é¨ã§ã¯ãªã
static.type.data.comparison.type.is_one_of=ä¸é¨ã§ãã
static.type.data.comparison.type.less_than=æ¬¡ã®å¤ããå°ãã
static.type.data.comparison.type.less_than_or_equal_to=æ¬¡ã®å¤ä»¥ä¸
static.type.data.comparison.type.not_empty_or_null=ç©ºã¾ãã¯ null ã¯ã§ãªã
static.type.data.comparison.type.not_equal=ç­ãããªã
static.type.data.comparison.type.occurs_after=æ¬¡ã®å¾ã«å®è¡
static.type.data.comparison.type.occurs_before=æ¬¡ã®åã«å®è¡
static.type.data.comparison.type.occurs_on_or_after=æ¬¡ã®æç¹ã¾ãã¯å¾ã«å®è¡
static.type.data.comparison.type.occurs_on_or_before=æ¬¡ã®æç¹ã¾ãã¯åã«å®è¡
static.type.data.comparison.type.starts_with=æ¬¡ã§å§ã¾ã
static.type.data.record.level.level_0=ã¬ãã« 0
static.type.data.record.level.level_10=ã¬ãã« 10
static.type.data.record.level.level_11=ã¬ãã« 11
static.type.data.record.level.level_12=ã¬ãã« 12
static.type.data.record.level.level_13=ã¬ãã« 13
static.type.data.record.level.level_14=ã¬ãã« 14
static.type.data.record.level.level_15=ã¬ãã« 15
static.type.data.record.level.level_16=ã¬ãã« 16
static.type.data.record.level.level_17=ã¬ãã« 17
static.type.data.record.level.level_18=ã¬ãã« 18
static.type.data.record.level.level_19=ã¬ãã« 19
static.type.data.record.level.level_1=ã¬ãã« 1
static.type.data.record.level.level_20=ã¬ãã« 20
static.type.data.record.level.level_2=ã¬ãã« 2
static.type.data.record.level.level_3=ã¬ãã« 3
static.type.data.record.level.level_4=ã¬ãã« 4
static.type.data.record.level.level_5=ã¬ãã« 5
static.type.data.record.level.level_6=ã¬ãã« 6
static.type.data.record.level.level_7=ã¬ãã« 7
static.type.data.record.level.level_8=ã¬ãã« 8
static.type.data.record.level.level_9=ã¬ãã« 9
static.type.data.sub.type.boolean=ãã¼ã«å
static.type.data.sub.type.currency=éè²¨
static.type.data.sub.type.date=æ¥ä»
static.type.data.sub.type.double=Double å
static.type.data.sub.type.float=Float å
static.type.data.sub.type.image=ç»å
static.type.data.sub.type.integer=æ´æ°
static.type.data.sub.type.numeric=æ°å¤
static.type.data.sub.type.short=Short å
static.type.data.sub.type.string=æå­åå
static.type.data.sub.type.uinteger=UInteger å
static.type.data.sub.type.unknown=ä¸æ
static.type.data.sub.type.ushort=UShort å
static.type.data.type.boolean=ãã¼ã«å
static.type.data.type.date=æ¥ä»
static.type.data.type.numeric=æ°å¤
static.type.data.type.string=æå­åå
static.type.favorites_folder=ãæ°ã«å¥ã
static.type.frequency.type.daily=æ¯æ¥
static.type.frequency.type.monthly=æ¯æ
static.type.frequency.type.quarterly=ååæãã¨
static.type.frequency.type.weekly=æ¯é±
static.type.frequency.type.yearly=æ¯å¹´
static.type.last_visited_folder=æçµè¡¨ç¤ºæ¥æ
static.type.layout.type.columnar=åç¥¨å½¢å¼
static.type.layout.type.delimited=åºåãè¨å·ä»ã
static.type.layout.type.xml=XML
static.type.new.item.folder.description=ããã©ã«ãã®æ°è¦ã¢ã¤ãã  ãã©ã«ãã¼
static.type.new.item.folder=æ°è¦ã¢ã¤ãã 
static.type.pod.invalid.schema=ããã ãã¹ã¿ã¼ ã¹ã­ã¼ããç¡å¹ã§ã
static.type.pod.scan.fail=ã¹ã­ã£ã³å¤±æ
static.type.pod.scan.success=ã¹ã­ã£ã³æå
static.type.pod.status.offline=ãªãã©ã¤ã³
static.type.pod.status.online=ãªã³ã©ã¤ã³
static.type.pod.type.generic=ä¸è¬
static.type.pod.type.production=ãã­ãã¯ã·ã§ã³
static.type.pod.type.transition=ãã¬ãªãªã¼ã¹
static.type.production.event.type.external.trigger=å¤é¨ããªã¬ã¼
static.type.production.event.type.immediate=å³æ
static.type.production.event.type.schedule=ã¹ã±ã¸ã¥ã¼ã«
static.type.proof.definition.status.invalid=ç¡å¹
static.type.proof.definition.status.unknown=ä¸æ
static.type.proof.definition.status.valid=æå¹
static.type.qualification.output.dxf=DXF
static.type.qualification.output.flat_files=ãã©ãã ãã¡ã¤ã«
static.type.qualification.output.xml_optimized=XML æé©åæ¸ã¿
static.type.qualification.output.xml_verbose=XML è©³ç´°
static.type.resource.location.type.local=ã­ã¼ã«ã«
static.type.resource.location.type.remote=ãªã¢ã¼ã
static.type.source.type.primary=ãã©ã¤ããª
static.type.source.type.reference.connected=åç§ (æ¥ç¶æ¸ã¿è¦æ±è)
static.type.source.type.reference=åç§
static.type.system.theme.ever_green=ã¨ãã¼ ã°ãªã¼ã³
static.type.system.theme.monochrome=ã¢ãã¯ã­
static.type.system.theme.ocean_blue=ãªã¼ã·ã£ã³ ãã«ã¼
static.type.system.theme.red_rose=ã¬ãã ã­ã¼ãº
static.type.task.type.follow_up=ãã©ã­ã¼ã¢ãã
static.type.task.type.none_complete=ãªã - å®äº
static.type.task.type.system=ã·ã¹ãã 
static.type.task.type.verification=æ¤è¨¼
static.type.text.formatting.cobol.signed=COBOL ç¬¦å·ä»ã (æ«å°¾)
static.type.text.formatting.general.number=æ°å¤
static.type.text.formatting.keep.blanks.default=ç©ºç½ãç¶­æ (ããã©ã«ã)
static.type.text.formatting.keep.blanks=ç©ºç½ãç¶­æ
static.type.text.formatting.lower.keep.blanks=ä¸ãç©ºç½ãç¶­æ
static.type.text.formatting.lower.trim.blanks=ä¸ãç©ºç½ãããªãã³ã°
static.type.text.formatting.lower.trim.leading=ä¸ãåé ­ãããªãã³ã°
static.type.text.formatting.lower.trim.trailing=ä¸ãæ«å°¾ãããªãã³ã°
static.type.text.formatting.packed=ããã¯ (å·¦ããå³)
static.type.text.formatting.trim.blanks=ç©ºç½ãããªãã³ã°
static.type.text.formatting.trim.leading.blanks=åé ­ã®ç©ºç½ãããªãã³ã°
static.type.text.formatting.trim.trailing.blanks=æ«å°¾ã®ç©ºç½ãããªãã³ã°
static.type.text.formatting.upper.keep.blanks=ä¸ãç©ºç½ãç¶­æ
static.type.text.formatting.upper.trim.blanks=ä¸ãç©ºç½ãããªãã³ã°
static.type.text.formatting.upper.trim.leading=ä¸ãåé ­ãããªãã³ã°
static.type.text.formatting.upper.trim.trailing=ä¸ãæ«å°¾ãããªãã³ã°
static.type.touchpoint.selection.type.content=æ§é å
static.type.touchpoint.selection.type.message=ãã¬ã­ã·ãã«
static.type.touchpoint.selection.type.regular=æ¨æº
static.type.work.group.all=ãã¹ã¦
static.type.zone.type.document.zone=ãã­ã¥ã¡ã³ã ã¾ã¼ã³
static.type.zone.type.email.subject.line.zone=é»å­ã¡ã¼ã«ä»¶åè¡ã¾ã¼ã³
static.type.zone.type.email.zone=é»å­ã¡ã¼ã« ã¾ã¼ã³
static.type.zone.type.freeform.page.zone=ããªã¼ãã©ã¼ã  ãã¼ã¸
static.type.zone.type.freeform.zone=ããªã¼ãã©ã¼ã  ã¾ã¼ã³
static.type.zone.type.interactive.zone=æ¥ç¶æ¸ã¿ã¾ã¼ã³
static.type.zone.type.sms.zone=SMS ã¾ã¼ã³
static.type.zone.type.web.title.line.zone=Web ã¿ã¤ãã«è¡ã¾ã¼ã³
static.type.zone.type.web.zone=Web ã¾ã¼ã³
user.deactivate.reason.1=éã¢ã¯ãã£ãåããã¦ãã¾ã
user.deactivate.reason.2=ã¾ã ã¢ã¯ãã£ãåããã¦ãã¾ãã
user.deactivate.reason.3=ã­ãã¯ã¢ã¦ãããã¦ãã¾ã
verbiage.version.reason.new.content.library=æ°è¦ç»å
verbiage.version.reason.new.embedded.content=æ°è¦ã¹ãã¼ã ãã­ã¹ã
verbiage.version.reason.newmessage=æ°è¦ã¡ãã»ã¼ã¸
xslt_messages.ACTIVE=ã¢ã¯ãã£ã
xslt_messages.ASSIGNMENT=å²ãå½ã¦
xslt_messages.BIN=ãã³
xslt_messages.DATE.RANGE=æ¥ä»ã®ç¯å²
xslt_messages.ID=ID
xslt_messages.NAME=åå
xslt_messages.TIMING=ã¿ã¤ãã³ã°
xslt_messages.WORKING.COPY=ä½æ¥­ã³ãã¼
xslt_messages.access.log.save.select.package=ã­ã°ã«ã¢ã¯ã»ã¹ããã«ã¯ã[ä¿å­] ãã¯ãªãã¯ãã¦è¨ºæ­ããã±ã¼ã¸ãé¸æãã¾ãã
xslt_messages.action=ã¢ã¯ã·ã§ã³
xslt_messages.active.as.of=ã¢ã¯ãã£ãæ¥ä»
xslt_messages.active=ã¢ã¯ãã£ã
xslt_messages.always=å¸¸æ
xslt_messages.ancestor.info=è¦ªè¦ç´ æå ±
xslt_messages.and.of.these.include=ãããã®åãä»¥ä¸ãå«ãã
xslt_messages.approvals=æ¿èª
xslt_messages.archived=ã¢ã¼ã«ã¤ãæ¸ã¿
xslt_messages.as.of=æ¥ä»
xslt_messages.assigned.to=å²ãå½ã¦å
xslt_messages.back.to.batch.report=ããã ã¬ãã¼ãã«æ»ã
xslt_messages.back.to.the.report=ã¬ãã¼ãã«æ»ã
xslt_messages.batch.level=ããã ã¬ãã«
xslt_messages.batch.num=ããã#
xslt_messages.batch.report=ããã ã¬ãã¼ã
xslt_messages.batches=ããã
xslt_messages.bin.assignments=ãã³å²ãå½ã¦
xslt_messages.condition.id=æ¡ä»¶ ID
xslt_messages.conditions=æ¡ä»¶
xslt_messages.constant.name=å®æ°å
xslt_messages.constant=å®æ°
xslt_messages.content.suppressed=ã³ã³ãã³ãéè¡¨ç¤º
xslt_messages.content.type=ã³ã³ãã³ã ã¿ã¤ã
xslt_messages.content=ã³ã³ãã³ã
xslt_messages.contents=ã³ã³ãã³ã
xslt_messages.created=ä½ææ¸ã¿
xslt_messages.current.state=ç¾å¨ã®ç¶æ
xslt_messages.custom=ã«ã¹ã¿ã 
xslt_messages.date.range=æ¥ä»ã®ç¯å²
xslt_messages.date.run=å®è¡æ¥ä»
xslt_messages.date.values=ãã¼ã¿å¤
xslt_messages.date=æ¥ä»
xslt_messages.delivered=éä¿¡æ¸ã¿
xslt_messages.delivery=éä¿¡
xslt_messages.description=èª¬æ
xslt_messages.diagnostics.report=è¨ºæ­ã¬ãã¼ã
xslt_messages.direct.references=ç´æ¥åç§
xslt_messages.elapsed.time=çµéæé
xslt_messages.email.address=é»å­ã¡ã¼ã« ã¢ãã¬ã¹
xslt_messages.email=é»å­ã¡ã¼ã«
xslt_messages.empty.content=ç©ºã®ã³ã³ãã³ã
xslt_messages.empty.part=ç©ºã®ãã¼ã
xslt_messages.envelope=å°ç­
xslt_messages.exclude=é¤å¤
xslt_messages.export.version=ãã¼ã¸ã§ã³ã®ã¨ã¯ã¹ãã¼ã
xslt_messages.external.id=å¤é¨ ID
xslt_messages.failed.deliveries=å¤±æããéä¿¡
xslt_messages.failed=å¤±æ
xslt_messages.for=å¯¾è±¡
xslt_messages.from.date=éå§æ¥
xslt_messages.graphic=ã°ã©ãã£ãã¯
xslt_messages.image.library.content.selections=ç»åã©ã¤ãã©ãª ã³ã³ãã³ãé¸æ
xslt_messages.image.library.reference.data=ç»åã©ã¤ãã©ãªåç§ãã¼ã¿
xslt_messages.image.library=ç»åã©ã¤ãã©ãª
xslt_messages.in.approval=æ¿èª
xslt_messages.inactive=éã¢ã¯ãã£ã
xslt_messages.include.active.objects.only=ã¢ã¯ãã£ã ãªãã¸ã§ã¯ãã®ã¿ãå«ãã
xslt_messages.include.content=ã³ã³ãã³ããå«ãã
xslt_messages.include.indirect.map=éæ¥ããããå«ãã
xslt_messages.include.insert.targeting=æ¿å¥ã¿ã¼ã²ããè¨­å®ãå«ãã
xslt_messages.include.targeting=ã¿ã¼ã²ããè¨­å®ãå«ãã
xslt_messages.include=å«ãã
xslt_messages.included.indirect.references=å«ã¾ããéæ¥åç§
xslt_messages.indefinite=ä¸å®
xslt_messages.inherited.from=ç¶æ¿å
xslt_messages.inherited=ç¶æ¿
xslt_messages.insert.schedules=æ¿å¥ã¹ã±ã¸ã¥ã¼ã«
xslt_messages.inserts.reference.data=æ¿å¥ã®åç§ãã¼ã¿
xslt_messages.job.count=ã¸ã§ãæ°
xslt_messages.job.id=ã¸ã§ã ID
xslt_messages.job.level=ã¸ã§ã ã¬ãã«
xslt_messages.job=ã¸ã§ã
xslt_messages.language=è¨èª
xslt_messages.less.than=æ¬¡ã®å¤ããå°ãã
xslt_messages.level=ã¬ãã«
xslt_messages.log=ã­ã°
xslt_messages.message.content.selections=ã¡ãã»ã¼ã¸ ã³ã³ãã³ãé¸æ
xslt_messages.message.priority=ã¡ãã»ã¼ã¸åªååº¦
xslt_messages.message.type=ã¡ãã»ã¼ã¸ ã¿ã¤ã
xslt_messages.message=ã¡ãã»ã¼ã¸
xslt_messages.messages=ã¡ãã»ã¼ã¸
xslt_messages.metadata=ã¡ã¿ãã¼ã¿
xslt_messages.name=åå
xslt_messages.no.data.for.time.period=ãã®æéã«ä½¿ç¨ã§ãããã¼ã¿ã¯ããã¾ããã
xslt_messages.no.direct.reference.for.variable=ãã®å¤æ°ã«ã¯ç´æ¥åç§ãããã¾ããã
xslt_messages.no.events.for.date.range=é¸æãããã¼ã¿ç¯å²ã«ã¤ãã³ããå­å¨ãã¾ããã
xslt_messages.no.indirect.references.for.variable=ãã®å¤æ°ã«ã¯éæ¥åç§ãããã¾ããã
xslt_messages.no.proofs.for.selection=ãã®é¸æã«å¯¾ããæ ¡æ­£ãããã¾ããã
xslt_messages.no.status=ã¹ãã¼ã¿ã¹ãªã
xslt_messages.no=ããã
xslt_messages.none=ãªã
xslt_messages.notes=æ³¨
xslt_messages.on.level=ã¬ãã«
xslt_messages.otherwise=ããä»¥å¤ã®å ´å
xslt_messages.part=ãã¼ã
xslt_messages.path=ãã¹
xslt_messages.proofs=æ ¡æ­£
xslt_messages.qualification=é©æ ¼
xslt_messages.ran.on=å®è¡æ¥
xslt_messages.rate.sheet.reference.data=æéè¡¨åç§ãã¼ã¿
xslt_messages.rate.sheets=æéè¡¨
xslt_messages.recipient.id=åä¿¡è ID
xslt_messages.recipient.level=åä¿¡èã¬ãã«
xslt_messages.recipients.minute=åä¿¡è/å
xslt_messages.recipients=åä¿¡è
xslt_messages.reference.map=åç§ããã
xslt_messages.reference.name=åç§å
xslt_messages.referencing.image.library=åç§åç»åã©ã¤ãã©ãª
xslt_messages.released=ãªãªã¼ã¹æ¸ã¿
xslt_messages.repeats.annually=æ¯å¹´ç¹°ãè¿ã
xslt_messages.report.request.summary=ã¬ãã¼ãè¦æ±ãµããªã¼
xslt_messages.report.type=ã¬ãã¼ã ã¿ã¤ã
xslt_messages.request.date=è¦æ±æ¥
xslt_messages.request.options=è¦æ±ãªãã·ã§ã³
xslt_messages.request.overview=è¦æ±æ¦è¦
xslt_messages.requested.by=è¦æ±è
xslt_messages.requested.on=è¦æ±æ¥
xslt_messages.requested=è¦æ±æ¸ã¿
xslt_messages.rules=ã«ã¼ã«
xslt_messages.run.date=å®è¡æ¥
xslt_messages.same.as.default.language=ããã©ã«ãè¨èªã¨åã
xslt_messages.same.as=æ¬¡ã¨åã
xslt_messages.section=ã»ã¯ã·ã§ã³
xslt_messages.selected.insert.schedule.index=é¸æããæ¿å¥ã¹ã±ã¸ã¥ã¼ã« ã¤ã³ããã¯ã¹
xslt_messages.selected.messages.index=é¸æããã¡ãã»ã¼ã¸ ã¤ã³ããã¯ã¹
xslt_messages.send=éä¿¡
xslt_messages.sent=éä¿¡æ¸ã¿
xslt_messages.sheets=ã·ã¼ã
xslt_messages.smart.text=ã¹ãã¼ã ãã­ã¹ã
xslt_messages.state=ç¶æ
xslt_messages.stating=éå§
xslt_messages.status=ã¹ãã¼ã¿ã¹
xslt_messages.stock.id=ã¹ããã¯ ID
xslt_messages.succeeded=æå
xslt_messages.suppressed=éè¡¨ç¤º
xslt_messages.targeting=ã¿ã¼ã²ããè¨­å®
xslt_messages.text=ãã­ã¹ã
xslt_messages.thresholds=ãããå¤
xslt_messages.through.conditions=æ¡ä»¶ãä½¿ç¨
xslt_messages.through.content=ã³ã³ãã³ããä½¿ç¨
xslt_messages.to.date=çµäºæ¥
xslt_messages.touchpoint.audit.report=ã¿ãããã¤ã³ãç£æ»ã¬ãã¼ã
xslt_messages.touchpoint.change.report=ã¿ãããã¤ã³ãå¤æ´ã¬ãã¼ã
xslt_messages.touchpoint.content=ã¿ãããã¤ã³ã ã³ã³ãã³ã
xslt_messages.touchpoint.delivery.report=ã¿ãããã¤ã³ãéä¿¡ã¬ãã¼ã
xslt_messages.touchpoint.index=ã¿ãããã¤ã³ã ã¤ã³ããã¯ã¹
xslt_messages.touchpoint.or.touchpoints=ã¿ãããã¤ã³ã
xslt_messages.touchpoint.reference.data=ã¿ãããã¤ã³ãåç§ãã¼ã¿
xslt_messages.touchpoint.selections=ã¿ãããã¤ã³ãã®é¸æåå®¹
xslt_messages.touchpoint=ã¿ãããã¤ã³ã
xslt_messages.touchpoints=ã¿ãããã¤ã³ã
xslt_messages.tp.delivery.report=TP éä¿¡ã¬ãã¼ã
xslt_messages.unexported=ã¢ã³ã¨ã¯ã¹ãã¼ãæ¸ã¿
xslt_messages.unknown.type=ä¸æãªã¿ã¤ã
xslt_messages.unknown=ä¸æ
xslt_messages.unspecified.assuming.requested.date=æå®ããã¦ãã¾ãããè¦æ±ãããæ¥ä»ã¨ãã¾ã
xslt_messages.upload.date=ã¢ããã­ã¼ãæ¥ä»
xslt_messages.use=ä½¿ç¨
xslt_messages.used.by=ä½¿ç¨è
xslt_messages.user=ã¦ã¼ã¶ã¼
xslt_messages.variable=å¤æ°
xslt_messages.versions=ãã¼ã¸ã§ã³
xslt_messages.wc.or.archived=ä½æ¥­ã³ãã¼ã¾ãã¯ã¢ã¼ã«ã¤ãæ¸ã¿
xslt_messages.web.page=WEB ãã¼ã¸
xslt_messages.weight=éé
xslt_messages.when.greater.than=æ¬¡ã®å¤ããå¤§ããå ´å
xslt_messages.where.used=ä½¿ç¨å ´æ
xslt_messages.working.copy=ä½æ¥­ã³ãã¼
xslt_messages.yes=ã¯ã
xslt_messages.zone=ã¾ã¼ã³
