<?xml version="1.0" encoding="ISO-8859-1"?>

<web-app xmlns="http://java.sun.com/xml/ns/javaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
            http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0">

	<display-name>messagepoint</display-name>
	<description>MessagePoint Web Application</description>

	<distributable />

	<!--
	  - Location of the XML file that defines the root application context
	  - Applied by ContextLoaderListener.
	  -->
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>
		    /WEB-INF/applicationContext-common-persistency.xml
		    /WEB-INF/applicationContext-common-services.xml
			/WEB-INF/applicationContext-security.xml
            /WEB-INF/applicationContext-session.xml
            /WEB-INF/applicationContext-common-business.xml
			/WEB-INF/messagepoint-schedule.xml
		</param-value>
	</context-param>
	
	<context-param>
		<param-name>log4jConfigLocation</param-name>
		<param-value>/WEB-INF/classes/log4j.properties</param-value>
	</context-param>

	<!-- Defines a unique web application root key for the messagepoint application. -->
	<context-param>
		<param-name>webAppRootKey</param-name>
		<param-value>com.prinova.messagepoint.root</param-value>
	</context-param>

    <!-- Disable resteasy-spring module/integration in jboss(wildfly-16.0.0.Final) -->
    <context-param>
        <param-name>org.jboss.as.jaxrs.enableSpringIntegration</param-name>
        <param-value>false</param-value>
    </context-param>

    <filter>
        <filter-name>springSessionRepositoryFilter</filter-name>
        <filter-class>com.prinova.messagepoint.controller.SessionRepositoryFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>springSessionRepositoryFilter</filter-name>
        <url-pattern>/*</url-pattern>
        <dispatcher>REQUEST</dispatcher>
        <dispatcher>ERROR</dispatcher>
    </filter-mapping>

    <filter>
        <filter-name>MessagepointStartFilter</filter-name>
        <filter-class>com.prinova.messagepoint.controller.MessagepointStartFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>MessagepointStartFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

	<filter>
	    <filter-name>characterEncodingFilter</filter-name>
	    <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
	    <init-param>
	        <param-name>encoding</param-name>
	        <param-value>UTF-8</param-value>
	    </init-param>
	    <init-param>
	        <param-name>forceEncoding</param-name>
	        <param-value>true</param-value>
	    </init-param>
	</filter>
	<filter-mapping>
	    <filter-name>characterEncodingFilter</filter-name>
	    <url-pattern>/*</url-pattern>
	</filter-mapping>

	<!-- Messagepoint OpenSessionInView for lazy loading -->
    <filter>
          <filter-name>MessagepointOpenSessionInViewFilter</filter-name>
          <filter-class>
              com.prinova.messagepoint.integrator.MessagepointOpenSessionInViewFilter
          </filter-class>
          <init-param>
                <param-name>sessionFactoryBeanName</param-name>
                <param-value>hibernateSessionFactory</param-value>
          </init-param>
          <init-param>
                <param-name>singleSession</param-name>
                <param-value>true</param-value>
          </init-param>          
    </filter>

    <filter-mapping>
          <filter-name>MessagepointOpenSessionInViewFilter</filter-name>
          <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>FileUploadAntiVirusFilter</filter-name>
        <filter-class>com.prinova.messagepoint.controller.FileUploadSecurityFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>FileUploadAntiVirusFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
	
	 
	<filter>
        <filter-name>MessagePointCheckSessionFilter</filter-name>
        <filter-class>com.prinova.messagepoint.controller.CheckSessionFilter</filter-class>
   	</filter>
   	
   	 
	<filter>
		<filter-name>MessagePointCSRFFilter</filter-name>
		<filter-class>com.prinova.messagepoint.controller.MessagePointCSRFFilter</filter-class>
		<init-param>
			<param-name>prevention</param-name>
			<param-value>true</param-value>
		</init-param>
		<init-param>
      		<param-name>entryPoints</param-name>
      		<param-value>/healthcheck.form,
                        /index.jsp,
                        /signin.jsp,
                        /idpsignin.jsp,
                        /j_spring_security_check,
                        /password_recovery.form,
                        /password_recovery_confirmation.jsp,
                        /password_reset.form,
                        /verify_email.form,
                        /invalid_parameter_error.jsp,
                        /unprocessable_entity_error.jsp,
                        /access_forbidden_error.jsp,
                        /pagenotfound.jsp,
                        /displaymessage.form,
                        /error.jsp,
                        /timeout.jsp,
                        /download/*,
                        /includes/javascript/tinymce*,
                        /includes/javascript/fonts*,
                        /saml/*,
                        /sso/*,
                        /logout.jsp,
                        /authentication_error.jsp,
                        /api/*,
                        /fileserve/*,
                        /communication_external_validation.form,
                        /oidc/*,
                        /idp/*,
                        /rest/*
      		</param-value>
   		</init-param>
	</filter>	    
   
	<filter>
	    <filter-name>springSecurityFilterChain</filter-name>
	    <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
	</filter>

   	<filter>
        <filter-name>MessagepointUpgradeFilter</filter-name>
        <filter-class>com.prinova.messagepoint.controller.MessagepointUpgradeFilter</filter-class>
        <init-param>
        	<param-name>error-page</param-name><param-value>/upgrade_inprogress.jsp</param-value>
        </init-param>
   	</filter>

   	<filter>
        <filter-name>CacheResponseFiter</filter-name>
        <filter-class>com.prinova.messagepoint.controller.CacheResponseFiter</filter-class>
        <init-param>
        	<param-name>cache-for</param-name><param-value>3600</param-value>
        </init-param>
   	</filter>

    <filter>
        <filter-name>SecurityHeaderFilter</filter-name>
        <filter-class>com.prinova.messagepoint.controller.SecurityHeaderFilter</filter-class>
    </filter>

    <filter>
        <filter-name>AnalyticsEventEndFilter</filter-name>
        <filter-class>com.prinova.messagepoint.controller.AnalyticsEventEndFilter</filter-class>
    </filter>
	 
   	<filter>
        <filter-name>MessagePointLogFilter</filter-name>
        <filter-class>com.prinova.messagepoint.controller.LogFilter</filter-class>
   	</filter>

   	<filter>
        <filter-name>MessagePointRequestParameterInputFilter</filter-name>
        <filter-class>com.prinova.messagepoint.controller.RequestParameterInputFilter</filter-class>
   	</filter>

   	<filter>
        <filter-name>LocaleConfigurerFilter</filter-name>
        <filter-class>com.prinova.messagepoint.controller.LocaleConfigurerFilter</filter-class>
   	</filter>
   	
   	<!--filter>
        <filter-name>ServerNamespacePropertyFilter</filter-name>
        <filter-class>com.prinova.messagepoint.controller.ServerNamespacePropertyInitializer</filter-class>
   	</filter-->
   	
   	<filter>
        <filter-name>DeviceResolverRequestFilter</filter-name>
        <filter-class>org.springframework.mobile.device.DeviceResolverRequestFilter</filter-class>
    </filter>

   	<filter-mapping>
      <filter-name>MessagePointCheckSessionFilter</filter-name>
      <url-pattern>/*</url-pattern>
    </filter-mapping>
    
    <!-- WEBDAV
    <filter-mapping>
        <filter-name>MiltonFilter</filter-name>
        <url-pattern>/file_serve/*</url-pattern>
    </filter-mapping>
    -->
   	<filter-mapping>
   		<filter-name>MessagePointCSRFFilter</filter-name>
   		<url-pattern>/*</url-pattern>
   	</filter-mapping>
   	
    <filter-mapping>
      <filter-name>springSecurityFilterChain</filter-name>
      <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- filter-mapping>
      <filter-name>ServerNamespacePropertyFilter</filter-name>
      <url-pattern>/signin.jsp</url-pattern>
    </filter-mapping>
    
    <filter-mapping>
      <filter-name>ServerNamespacePropertyFilter</filter-name>
      <url-pattern>/index.jsp</url-pattern>
    </filter-mapping -->
    
    <filter-mapping>
      <filter-name>DeviceResolverRequestFilter</filter-name>
      <url-pattern>/*</url-pattern>
    </filter-mapping>
    	
	<!-- 
	<filter-mapping>
		<filter-name>logoutFilter</filter-name>
		<url-pattern>/signout</url-pattern>
	</filter-mapping> 
	 -->
	 
    <filter-mapping>
      <filter-name>MessagePointLogFilter</filter-name>
      <url-pattern>/*</url-pattern>
    </filter-mapping>
    
    <filter-mapping>
      <filter-name>MessagePointRequestParameterInputFilter</filter-name>
      <url-pattern>/*</url-pattern>
    </filter-mapping>
    
    <filter-mapping>
      <filter-name>LocaleConfigurerFilter</filter-name>
      <url-pattern>/*</url-pattern>
    </filter-mapping>

	<!-- 
		===============================================================
		  The following mappings are for caching non dynamicresources.
		===============================================================
	 -->
    <filter-mapping>
      <filter-name>MessagepointUpgradeFilter</filter-name>
      <url-pattern>*.jsp</url-pattern>
    </filter-mapping>

    <filter-mapping>
      <filter-name>MessagepointUpgradeFilter</filter-name>
      <url-pattern>*.form</url-pattern>
    </filter-mapping>

    <filter-mapping>
      <filter-name>CacheResponseFiter</filter-name>
      <url-pattern>*.js</url-pattern>
    </filter-mapping>

    <filter-mapping>
        <filter-name>CacheResponseFiter</filter-name>
        <url-pattern>*.svg</url-pattern>
    </filter-mapping>

    <filter-mapping>
        <filter-name>CacheResponseFiter</filter-name>
        <url-pattern>*.map</url-pattern>
    </filter-mapping>

    <filter-mapping>
      <filter-name>CacheResponseFiter</filter-name>
      <url-pattern>*.gif</url-pattern>
    </filter-mapping>

    <filter-mapping>
      <filter-name>CacheResponseFiter</filter-name>
      <url-pattern>*.jpg</url-pattern>
    </filter-mapping>

    <filter-mapping>
      <filter-name>CacheResponseFiter</filter-name>
      <url-pattern>*.png</url-pattern>
    </filter-mapping>

    <filter-mapping>
      <filter-name>CacheResponseFiter</filter-name>
      <url-pattern>*.html</url-pattern>
    </filter-mapping>

    <filter-mapping>
        <filter-name>SecurityHeaderFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter-mapping>
        <!-- AnalyticsEventEndFilter should always be the last filter -->
        <filter-name>AnalyticsEventEndFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!--
      - Loads the root application context of this web app at startup.
      - The application context is then available via
      - WebApplicationContextUtils.getWebApplicationContext(servletContext).
    -->
	<listener>
        <listener-class>com.prinova.messagepoint.MessagepointContextLoaderListener</listener-class>
	</listener>

    <!--
	<listener>
		<listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>
	</listener>
    -->
    <!--
        The HttpSessionEventPublisher will publish
        HttpSessionCreatedEvent and HttpSessionDestroyedEvent
        to the WebApplicationContext
 -->
    <!--
  	<listener>
        <listener-class>org.springframework.security.web.session.HttpSessionEventPublisher</listener-class>
    </listener>
   

 	<listener>
        <listener-class>com.prinova.messagepoint.controller.HttpSessionTimeoutListener</listener-class>
    </listener>
    -->
    
  <!--
	- Provides core MVC application controller. See contacts-servlet.xml.
    -->
	<servlet>
		<servlet-name>messagepoint</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<load-on-startup>1</load-on-startup>
        <async-supported>true</async-supported>
	</servlet>

	<servlet-mapping>
		<servlet-name>messagepoint</servlet-name>
		<url-pattern>*.form</url-pattern>
	</servlet-mapping>

    <servlet>
        <servlet-name>spring-ws</servlet-name>
        <servlet-class>com.prinova.messagepoint.platform.ws.MpMessageDispatcherServlet</servlet-class>
        <init-param>
            <param-name>transformWsdlLocations</param-name>
            <param-value>true</param-value>
        </init-param>
    </servlet>

    <servlet-mapping>
        <servlet-name>spring-ws</servlet-name>
        <url-pattern>/api/*</url-pattern> 
    </servlet-mapping>
    
    <servlet>
        <servlet-name>messagepoint-oidc</servlet-name>
        <servlet-class>com.prinova.messagepoint.platform.auth.MpOpenIdConnectDispatcherServlet</servlet-class>
    </servlet>
    
    <servlet-mapping>
        <servlet-name>messagepoint-oidc</servlet-name>
        <url-pattern>/oidc/*</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>messagepoint-idp</servlet-name>
        <servlet-class>com.prinova.messagepoint.platform.auth.MpIdpDispatcherServlet</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>messagepoint-idp</servlet-name>
        <url-pattern>/idp/*</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>messagepoint-rest</servlet-name>
        <servlet-class>com.prinova.messagepoint.platform.auth.MpRestDispatcherServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
        <async-supported>true</async-supported>
    </servlet>

    <servlet-mapping>
        <servlet-name>messagepoint-rest</servlet-name>
        <url-pattern>/rest/*</url-pattern>
    </servlet-mapping>


    <session-config>
        <session-timeout>30</session-timeout>
        <cookie-config>
            <http-only>true</http-only>
            <max-age>36000</max-age>
        </cookie-config>
    </session-config>

	<servlet-mapping>
	    <servlet-name>spring-ws</servlet-name>
	    <url-pattern>/dews/*</url-pattern>
	</servlet-mapping>

	<servlet-mapping>
	    <servlet-name>spring-ws</servlet-name>
	    <url-pattern>/mpcustomer/*</url-pattern>
	</servlet-mapping>

	<mime-mapping>
     	<extension>xml</extension>
     	<mime-type>text/xml</mime-type>
     </mime-mapping>
	<jsp-config>
        <taglib>
            <taglib-uri>http://messagepoint.prinova.com/taglib</taglib-uri>
            <taglib-location>/WEB-INF/prinova.tld</taglib-location>
        </taglib>
	    <jsp-property-group>
        	<url-pattern>*.jsp</url-pattern>
        	<page-encoding>UTF-8</page-encoding>
    	</jsp-property-group>
        <jsp-property-group>
            <url-pattern>*.jsp</url-pattern>
            <trim-directive-whitespaces>true</trim-directive-whitespaces>
        </jsp-property-group>
        <jsp-property-group>
            <url-pattern>*.jsp</url-pattern>
            <trim-directive-whitespaces>true</trim-directive-whitespaces>
        </jsp-property-group>
	</jsp-config>
 	
 	<welcome-file-list>
		<welcome-file>index.jsp</welcome-file>
	</welcome-file-list>

    <error-page>
        <exception-type>java.lang.Throwable</exception-type>
        <location>/error.jsp</location>
    </error-page>
    <error-page>
        <error-code>400</error-code>
        <location>/invalid_parameter_error.jsp</location>
    </error-page>
    <error-page>
        <error-code>422</error-code>
        <location>/unprocessable_entity_error.jsp</location>
    </error-page>
    <error-page>
    	<!-- Unsupported servlet method -->
    	<error-code>401</error-code>
    	<location>/error.jsp</location>
	</error-page>
    <error-page>
        <error-code>403</error-code>
        <location>/access_forbidden_error.jsp</location>
    </error-page>
    <error-page>
        <error-code>404</error-code>
        <location>/pagenotfound.jsp</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/error.jsp</location>
    </error-page>
    <error-page>
    	<!-- Unsupported servlet method -->
    	<error-code>503</error-code>
    	<location>/error.jsp</location>
	</error-page>
    
</web-app>
