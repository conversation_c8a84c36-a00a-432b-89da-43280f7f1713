<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans-4.2.xsd"
       profile="enablequartz">

    <!-- Scheduling specific beans -->

    <bean id="scheduler" class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
        <property name="configLocation" value="classpath:quartz.properties"/>
        <property name="autoStartup" value="true"/>
        <property name="applicationContextSchedulerContextKey" value="applicationContext"/>
        <property name="waitForJobsToCompleteOnShutdown" value="true"/>
        <property name="overwriteExistingJobs" value="true"/>
        <property name="jobDetails">
            <list>
                <ref bean="DeliveryEventStatusCheckJobDetail" />
                <ref bean="DailySchedulerJobDetail" />
                <ref bean="EmailBounceFinderJobDetail" />
                <ref bean="EmailProofReceiverJobDetail" />
                <ref bean="WorkflowDueByCheckSchedulerJobDetail" />
                <ref bean="NotificationEmailCheckSchedulerJobDetail" />
                <ref bean="TaskDueByCheckSchedulerJobDetail" />
                <ref bean="SandboxFileCleanupSchedulerJobDetail" />
                <ref bean="UserHardDeactivationCheckSchedulerJobDetail" />
                <ref bean="CommunicationProductionStatusSchedulerJobDetail" />
                <ref bean="TemporaryObjectsStatusSchedulerJobDetail" />
                <ref bean="RationalizerIndexingSchedulerJobDetail" />
                <ref bean="PodStatisticReportingSchedulerJobDetail" />
                <ref bean="MessagepointIndexingSchedulerJobDetail" />
                <ref bean="BackgroundTaskRemovingSchedulerJobDetail" />
                <ref bean="MessagepointElasticIndexDeleteSchedulerJobDetail" />
                <ref bean="ConnectedUpdateSupportingDataSchedulerJobDetail" />
                <ref bean="CheckForeignKeysSchedulerJobDetail" />
            </list>
        </property>
        <property name="triggers">
            <list>
                <ref bean="DeliveryEventStatusCheckTrigger" />
                <ref bean="DailySchedulerTrigger" />
                <ref bean="EmailBounceFinderTrigger" />
                <ref bean="EmailProofReceiverTrigger" />
                <ref bean="WorkflowDueByCheckSchedulerTrigger" />
                <ref bean="NotificationEmailCheckSchedulerTrigger" />
                <ref bean="TaskDueByCheckSchedulerTrigger" />
                <ref bean="SandboxFileCleanupSchedulerTrigger" />
                <ref bean="UserHardDeactivationCheckSchedulerTrigger" />
                <ref bean="CommunicationProductionStatusSchedulerTrigger" />
                <ref bean="TemporaryObjectsStatusSchedulerTrigger" />
                <ref bean="RationalizerIndexingSchedulerTrigger" />
                <ref bean="PodStatisticReportingSchedulerTrigger" />
                <ref bean="MessagepointIndexingSchedulerTrigger" />
                <ref bean="BackgroundTaskRemovingSchedulerTrigger" />
                <ref bean="MessagepointElasticIndexDeleteSchedulerTrigger" />
                <ref bean="ConnectedUpdateSupportingDataSchedulerTrigger" />
                <ref bean="CheckForeignKeysSchedulerTrigger" />
            </list>
        </property>
    </bean>

    <!-- This is a helper bean that provides access to the configured scheduler factory -->
    <bean id="messagepointQuartzUtils" class="com.prinova.messagepoint.integrator.MessagepointQuartzUtils" scope="singleton" />

    <!-- Sync Data Expiration schedule -->
    <bean id="SyncDataExpirationSchedulerJobBean" class="com.prinova.messagepoint.platform.services.admin.SyncDataExpirationScheduler" />

    <bean id="SyncDataExpirationSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="SyncDataExpirationSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="SyncDataExpirationSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointCronTriggerFactoryBean">
        <property name="jobDetail" ref="SyncDataExpirationSchedulerJobDetail" />
        <property name="startDelay" value="30000" />
        <property name="cronExpression">
            <value>0 0 0 * * ?</value> <!-- Run everyday at midnight -->
        </property>
    </bean>

    <!-- com.prinova.messagepoint.job.DeliveryEventStatusCheck -->
    <bean id="DeliveryEventStatusCheckJobBean" class="com.prinova.messagepoint.job.DeliveryEventStatusCheck" />

    <bean id="DeliveryEventStatusCheckJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="DeliveryEventStatusCheckJobBean" />
            </map>
        </property>
    </bean>

    <bean id="DeliveryEventStatusCheckTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="DeliveryEventStatusCheckJobDetail" />
        <!-- run 10 seconds after startup -->
        <property name="startDelay" value="10000" />
        <!-- IMPORTANT: Do not run this process more frequently than every 10 seconds! -->
        <!-- repeat every 5 second -->
        <property name="repeatInterval" value="5000" />
    </bean>

    <!-- com.prinova.messagepoint.job.DailyScheduler -->

    <bean id="DailySchedulerJobBean" class="com.prinova.messagepoint.job.DailyScheduler" />

    <bean id="DailySchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="DailySchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="DailySchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="DailySchedulerJobDetail" />
        <property name="startDelay" value="30000" />
        <!-- IMPORTANT: Do not run this process more frequently than every 30 seconds! -->
        <!-- repeat every hour -->
        <property name="repeatInterval" value="3600000" />
    </bean>


    <!-- com.prinova.messagepoint.email.EmailBounceFinder -->

    <bean id="EmailBounceFinderJobBean" class="com.prinova.messagepoint.email.EmailBounceFinder" />

    <bean id="EmailBounceFinderJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="EmailBounceFinderJobBean" />
            </map>
        </property>
    </bean>

    <bean id="EmailBounceFinderTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="EmailBounceFinderJobDetail" />
        <property name="startDelay" value="15000" />
        <!-- IMPORTANT: Do not run this process more frequently than every 600 seconds (10 minutes)! -->
        <!-- repeat every 10 minutes -->
        <property name="repeatInterval" value="600000" />
    </bean>


    <!-- com.prinova.messagepoint.email.EmailProofReceiver -->
    <bean id="EmailProofReceiverJobBean" class="com.prinova.messagepoint.email.EmailProofReceiver" />

    <bean id="EmailProofReceiverJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="EmailProofReceiverJobBean" />
            </map>
        </property>
    </bean>

    <bean id="EmailProofReceiverTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="EmailProofReceiverJobDetail" />
        <property name="startDelay" value="15000" />
        <property name="repeatInterval" value="60000" />
    </bean>


    <!-- com.prinova.messagepoint.platform.services.workflow.WorkflowDueByCheckScheduler -->

    <bean id="WorkflowDueByCheckSchedulerJobBean" class="com.prinova.messagepoint.platform.services.workflow.WorkflowDueByCheckScheduler" />

    <bean id="WorkflowDueByCheckSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="WorkflowDueByCheckSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="WorkflowDueByCheckSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="WorkflowDueByCheckSchedulerJobDetail" />
        <property name="startDelay" value="20000" />
        <!-- repeat every 60 minutes -->
        <property name="repeatInterval" value="3600000" />
    </bean>


    <!-- com.prinova.messagepoint.platform.services.notification.NotificationEmailCheckScheduler -->

    <bean id="NotificationEmailCheckSchedulerJobBean" class="com.prinova.messagepoint.platform.services.notification.NotificationEmailCheckScheduler" />

    <bean id="NotificationEmailCheckSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="NotificationEmailCheckSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="NotificationEmailCheckSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="NotificationEmailCheckSchedulerJobDetail" />
        <property name="startDelay" value="25000" />
        <!-- repeat every 30 minutes -->
        <property name="repeatInterval" value="1800000" />
    </bean>


    <!-- com.prinova.messagepoint.platform.services.tasks.TaskDueByCheckScheduler -->

    <bean id="TaskDueByCheckSchedulerJobBean" class="com.prinova.messagepoint.platform.services.tasks.TaskDueByCheckScheduler" />

    <bean id="TaskDueByCheckSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="TaskDueByCheckSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="TaskDueByCheckSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="TaskDueByCheckSchedulerJobDetail" />
        <property name="startDelay" value="40000" />
        <!-- repeat every 15 minutes -->
        <property name="repeatInterval" value="900000" />
    </bean>


    <!-- com.prinova.messagepoint.platform.services.content.SandboxFileCleanupScheduler -->

    <bean id="SandboxFileCleanupSchedulerJobBean" class="com.prinova.messagepoint.platform.services.content.SandboxFileCleanupScheduler" />

    <bean id="SandboxFileCleanupSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="SandboxFileCleanupSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="SandboxFileCleanupSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="SandboxFileCleanupSchedulerJobDetail" />
        <property name="startDelay" value="45000" />
        <!-- repeat everyday -->
        <property name="repeatInterval" value="86400000" />
    </bean>


    <!-- com.prinova.messagepoint.platform.services.admin.UserHardDeactivationCheckScheduler -->

    <bean id="UserHardDeactivationCheckSchedulerJobBean" class="com.prinova.messagepoint.platform.services.admin.UserHardDeactivationCheckScheduler" />

    <bean id="UserHardDeactivationCheckSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="UserHardDeactivationCheckSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="UserHardDeactivationCheckSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="UserHardDeactivationCheckSchedulerJobDetail" />
        <property name="startDelay" value="50000" />
        <!-- repeat everyday -->
        <property name="repeatInterval" value="86400000" />
    </bean>


    <!-- com.prinova.messagepoint.platform.services.communication.CommunicationProductionStatusScheduler -->

    <bean id="CommunicationProductionStatusSchedulerJobBean" class="com.prinova.messagepoint.platform.services.communication.CommunicationProductionStatusScheduler" />

    <bean id="CommunicationProductionStatusSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="CommunicationProductionStatusSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="CommunicationProductionStatusSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="CommunicationProductionStatusSchedulerJobDetail" />
        <property name="startDelay" value="18000" />
        <!-- repeat everyday -->
        <property name="repeatInterval" value="5000" />
    </bean>


    <!-- com.prinova.messagepoint.platform.services.TemporaryObjectsStatusScheduler -->

    <bean id="TemporaryObjectsStatusSchedulerJobBean" class="com.prinova.messagepoint.platform.services.TemporaryObjectsStatusScheduler" />

    <bean id="TemporaryObjectsStatusSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="TemporaryObjectsStatusSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="TemporaryObjectsStatusSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="TemporaryObjectsStatusSchedulerJobDetail" />
        <property name="startDelay" value="55000" />
        <!-- repeat 5 seconds -->
        <property name="repeatInterval" value="5000" />
    </bean>

    <!-- com.prinova.messagepoint.platform.services.rationalizer.RationalizerIndexingScheduler -->

    <bean id="RationalizerIndexingSchedulerJobBean" class="com.prinova.messagepoint.platform.services.rationalizer.RationalizerIndexingScheduler" />

    <bean id="RationalizerIndexingSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="RationalizerIndexingSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="RationalizerIndexingSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="RationalizerIndexingSchedulerJobDetail" />
        <property name="startDelay" value="10000" />
        <property name="repeatInterval" value="2000" />
    </bean>

    <bean id="MessagepointIndexingSchedulerJobBean" class="com.prinova.messagepoint.platform.services.elasticsearch.scheduler.MessagepointIndexingScheduler" />

    <bean id="MessagepointIndexingSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="MessagepointIndexingSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="MessagepointIndexingSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="MessagepointIndexingSchedulerJobDetail" />
        <property name="startDelay" value="10000" />
        <property name="repeatInterval" value="2000" />
    </bean>

    <!-- Production job purging scheduler-->
    <bean id="ProdJobPurgingSchedulerJobBean" class="com.prinova.messagepoint.platform.services.job.ProdJobPurgingScheduler" />

    <bean id="ProdJobPurgingSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="ProdJobPurgingSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="ProdJobPurgingSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointCronTriggerFactoryBean">
        <property name="jobDetail" ref="ProdJobPurgingSchedulerJobDetail" />
        <property name="cronExpression">
            <value>0 0 0 * * ?</value> <!-- Run everyday at midnight -->
        </property>
    </bean>

    <!-- Test job purging scheduler-->
    <bean id="TestJobPurgingSchedulerJobBean" class="com.prinova.messagepoint.platform.services.job.TestJobPurgingScheduler" />

    <bean id="TestJobPurgingSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="TestJobPurgingSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="TestJobPurgingSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointCronTriggerFactoryBean">
        <property name="jobDetail" ref="TestJobPurgingSchedulerJobDetail" />
        <property name="cronExpression">
            <value>0 0 0 * * ?</value> <!-- Run everyday at midnight -->
        </property>
    </bean>

    <!-- Proof job purging scheduler-->
    <bean id="ProofJobPurgingSchedulerJobBean" class="com.prinova.messagepoint.platform.services.job.ProofJobPurgingScheduler" />

    <bean id="ProofJobPurgingSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="ProofJobPurgingSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="ProofJobPurgingSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointCronTriggerFactoryBean">
        <property name="jobDetail" ref="ProofJobPurgingSchedulerJobDetail" />
        <property name="cronExpression">
            <value>0 0 0 * * ?</value> <!-- Run everyday at midnight -->
        </property>
    </bean>

    <!-- Connected job purging scheduler-->
    <bean id="ConnectedJobPurgingSchedulerJobBean" class="com.prinova.messagepoint.platform.services.job.ConnectedJobPurgingScheduler" />

    <bean id="ConnectedJobPurgingSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="ConnectedJobPurgingSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="ConnectedJobPurgingSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointCronTriggerFactoryBean">
        <property name="jobDetail" ref="ConnectedJobPurgingSchedulerJobDetail" />
        <property name="cronExpression">
            <value>0 0 0 * * ?</value> <!-- Run everyday at midnight -->
        </property>
    </bean>

    <!-- Orphaned job purging scheduler-->
    <bean id="OrphanedJobPurgingSchedulerJobBean" class="com.prinova.messagepoint.platform.services.job.OrphanedJobPurgingScheduler" />

    <bean id="OrphanedJobPurgingSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="OrphanedJobPurgingSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="OrphanedJobPurgingSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointCronTriggerFactoryBean">
        <property name="jobDetail" ref="OrphanedJobPurgingSchedulerJobDetail" />
        <property name="cronExpression">
            <value>0 0 0 * * ?</value> <!-- Run everyday at midnight -->
        </property>
    </bean>

    <bean id="PodStatisticReportingSchedulerJobBean" class="com.prinova.messagepoint.platform.services.stats.PodStatisticReportingScheduler" />

    <bean id="PodStatisticReportingSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="PodStatisticReportingSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="PodStatisticReportingSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointCronTriggerFactoryBean">
        <property name="jobDetail" ref="PodStatisticReportingSchedulerJobDetail" />
        <property name="cronExpression">
            <!-- <value>0 0 3 1 * ?</value> --> <!-- Run the first day every month at 3am -->
            <!-- <value>0 0 3 ? * MON#1</value> --> <!-- Run the first Monday every month at 3am -->
            <value>0 0 3 ? * MON</value> <!-- Run every Monday at 3am -->
            <!-- <value>0 0/15 9-17 * * ?</value> -->
        </property>
    </bean>

    <bean id="BackgroundTaskRemovingSchedulerJobBean" class="com.prinova.messagepoint.platform.services.backgroundtask.BackgroundTaskRemovingScheduler" />

    <bean id="BackgroundTaskRemovingSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="BackgroundTaskRemovingSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="BackgroundTaskRemovingSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointCronTriggerFactoryBean">
        <property name="jobDetail" ref="BackgroundTaskRemovingSchedulerJobDetail" />
        <property name="cronExpression">
<!--            <value>0 0 3 * * ?</value>  --> <!-- Run every day morning at 3am -->
            <value>0 0 0/4 * * ?</value> <!-- every 4 hours -->
        </property>
    </bean>


    <!-- Messagepoint Elastic indexes purging scheduler-->
    <bean id="MessagepointElasticIndexDeleteSchedulerJobBean" class="com.prinova.messagepoint.platform.services.job.MessagepointElasticIndexDeleteScheduler" />

    <bean id="MessagepointElasticIndexDeleteSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="MessagepointElasticIndexDeleteSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="MessagepointElasticIndexDeleteSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointCronTriggerFactoryBean">
        <property name="jobDetail" ref="MessagepointElasticIndexDeleteSchedulerJobDetail" />
        <property name="cronExpression">
            <value>0 0 1 * * ?</value> <!-- Run everyday at 1 am -->
        </property>
    </bean>


    <!-- Connected: Update Communication SupportingData  scheduler-->
    <bean id="ConnectedUpdateSupportingDataSchedulerJobBean" class="com.prinova.messagepoint.platform.services.job.ConnectedUpdateSupportingDataScheduler" />

    <bean id="ConnectedUpdateSupportingDataSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="ConnectedUpdateSupportingDataSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="ConnectedUpdateSupportingDataSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="ConnectedUpdateSupportingDataSchedulerJobDetail" />
        <property name="startDelay" value="60000" />
        <property name="repeatInterval" value="300000" />
    </bean>

    <!-- Check and rebuild Foreign Keys if necessary scheduler-->
    <bean id="CheckForeignKeysSchedulerJobBean" class="com.prinova.messagepoint.platform.services.admin.CheckForeignKeysScheduler" />

    <bean id="CheckForeignKeysSchedulerJobDetail" class="org.springframework.scheduling.quartz.JobDetailFactoryBean">
        <property name="jobClass" value="com.prinova.messagepoint.integrator.MessagepointQuartzJobBean" />
        <property name="durability" value="true"/>
        <property name="jobDataAsMap">
            <map>
                <entry key="batchProcessorName" value="CheckForeignKeysSchedulerJobBean" />
            </map>
        </property>
    </bean>

    <bean id="CheckForeignKeysSchedulerTrigger" class="com.prinova.messagepoint.integrator.MessagepointSimpleTriggerFactoryBean">
        <property name="jobDetail" ref="CheckForeignKeysSchedulerJobDetail" />
        <property name="startDelay" value="100000" /> <!-- 100 seconds -->
        <!-- <property name="repeatInterval" value="300000" /> -->
        <property name="repeatCount" value="0" /> <!-- Ensures the job runs only once -->
    </bean>
</beans>
