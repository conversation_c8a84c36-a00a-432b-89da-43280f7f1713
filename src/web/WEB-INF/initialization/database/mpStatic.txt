aggregation_operator
all_reference_query
app_version
background_themes
condition_operator
condition_type
connector_type
content_type
daily_frequency_type
data_comparison
data_record_level
data_subtype
data_subtype_comparison
data_type
data_type_subtype
date_data_value
deactivate_types
dialogue_data_mapping
encoding_type
event_type
frequency_type
item_type
layout_type
mp_channel
mp_connector
mp_qualification_output
navigation_drop_down_menu
navigation_drop_down_menu_item
navigation_menu_item_perm
navigation_tab_default_map
navigation_tab_permission
navigation_tabs
navigation_tree
old_workflow
permission
permission_category
category_group
delivery_event_type
record_type
reference_query
source_type
sub_content_type
system_state
system_theme
tag_cloud_type
translation_provider
version_activity_reason
version_status
workflow_position
workflow_position_permission
workflow_property
workflow_state
workflow_tab
workflow_tab_permission
pod_status
pod_type
barcode_type
deserver_type
deserver_communicationtype