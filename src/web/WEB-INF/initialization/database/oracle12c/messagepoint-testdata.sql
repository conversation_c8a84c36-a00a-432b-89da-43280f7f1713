delete from users;
INSERT INTO users (id, guid, email, password, user_id, first_name, last_name, isactive, updated_by_id, created, default_tab_id,email_notify_daily, email_notify_realtime, passwordexpired, invalid_signins, default_list_filter_id, insert_list_filter_id,insert_schedule_list_filter_id, tp_selection_list_filter_id, tp_cont_seln_list_filter_id, tag_list_filter_id, testing_list_filter_id, variable_list_filter_id, report_list_filter_id, workgroup_id, supervisor_id, hidden_supervisor, app_locale_id, master_admin_status, idp_type, soft_deactivated, licensed_type) VALUES (1, '8114a5c1a8b064074d690f32453e6941', '<EMAIL>', '06988e3d344d2c0bf169a2002cb266a81a25a397', '<EMAIL>', 'Super', 'User', 1 ,1, to_date('01-01-2006','DD-MM-YYYY'), 13, 1, 1, 0, 0,1,1,1,1,1,1,1,1,1,1,0,0,1,0,0,0,0);
INSERT INTO password_history(id, user_id, password, created) VALUES (1, 1, '06988e3d344d2c0bf169a2002cb266a81a25a397', CURRENT_DATE);
