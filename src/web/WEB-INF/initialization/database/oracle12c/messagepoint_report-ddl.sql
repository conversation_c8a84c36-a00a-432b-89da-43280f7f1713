drop table batch_operation_statistics cascade constraints;
drop table batch_statistics_summary cascade constraints;
drop table complete_insert_report cascade constraints;
drop table complete_report cascade constraints;
drop table condition_coverage_report cascade constraints;
drop table customer cascade constraints;
drop table customer_field cascade constraints;
drop table executed_variable cascade constraints;
drop table job_insert cascade constraints;
drop table job_insert_recipient cascade constraints;
drop table job_insert_recipient_report cascade constraints;
drop table job_insert_schedule cascade constraints;
drop table job_insert_schedule_insert_id cascade constraints;
drop table job_insert_schedule_rates cascade constraints;
drop table job_insert_schedule_statistic cascade constraints;
drop table job_insert_statistic cascade constraints;
drop table job_message cascade constraints;
drop table job_rate_schedule cascade constraints;
drop table job_rate_schedule_statistic cascade constraints;
drop table job_statistics_variant cascade constraints;
drop table job_weight_limit cascade constraints;
drop table log_entry cascade constraints;
drop table message_coverage_report cascade constraints;
drop table msg_target_coverage_report cascade constraints;
drop table report cascade constraints;
drop table report_graphic cascade constraints;
drop table report_job_metadata cascade constraints;
drop table report_job_metadata_detail cascade constraints;
drop table report_job_metadata_messages cascade constraints;
drop table report_job_metadata_part cascade constraints;
drop table report_job_metadata_xml cascade constraints;
drop table rpt_job_metadata_trgt_grps cascade constraints;
drop table rule_coverage_report cascade constraints;
drop table simulation_coverage_report cascade constraints;
drop table target_group_coverage_report cascade constraints;
drop table unhandled_delivery_updates cascade constraints;
drop table zone_coverage_report cascade constraints;
create table batch_operation_statistics (id number(19,0) not null, job_id number(19,0), batch_id number(19,0), qual_eng_start timestamp, qual_eng_finish timestamp, delivery_start timestamp, delivery_finish timestamp, recipient_count number(19,0), primary key (id));
create table batch_statistics_summary (id number(19,0) not null, job_id number(19,0), batch_id number(19,0), job_type number(19,0), document_id number(19,0), year varchar2(2 char), month varchar2(2 char), rundate timestamp, recipients number(19,0), primary key (id));
create table complete_insert_report (id number(19,0) not null, insert_id number(19,0), scenario_id number(19,0), scenario_type_id number(10,0), qualified number(19,0), delivered number(19,0), total number(19,0), primary key (id));
create table complete_report (id number(19,0) not null, content_object_id number(19,0), scenario_id number(19,0), scenario_type_id number(10,0), qualified number(19,0), delivered number(19,0), total number(19,0), primary key (id));
create table condition_coverage_report (id number(19,0) not null, guid varchar2(255 char), condition_instance_id number(19,0), passes number(19,0), attempts number(19,0), data_group_id number(19,0), rule_coverage_report_id number(19,0), primary key (id));
create table customer (job_id number(19,0) not null, part_id number(10,0) not null, transaction_id varchar2(255 char) not null, customer_id varchar2(255 char) not null, language_code varchar2(255 char) not null, email_guid varchar2(255 char), email_status number(10,0), email_address varchar2(255 char), document_delivered number(1,0), updated timestamp, primary key (job_id, part_id, transaction_id, customer_id));
create table customer_field (job_id number(19,0) not null, part_id number(10,0) not null, transaction_id varchar2(255 char) not null, customer_id_value varchar2(255 char) not null, field varchar2(255 char) not null, f_value varchar2(255 char), primary key (job_id, part_id, transaction_id, customer_id_value, field));
create table executed_variable (job_id number(19,0) not null, part_id number(10,0) not null, transaction_id varchar2(255 char) not null, sq number(19,0) not null, name varchar2(96 char), f_value varchar2(4000 char), report_sequence number(19,0), primary key (job_id, part_id, transaction_id, sq));
create table job_insert (id number(19,0) not null, job_id number(19,0), insert_id number(19,0), name varchar2(96 char) not null, insert_external_id varchar2(96 char), weight number(19,0), delivery_type varchar2(20 char), primary key (id));
create table job_insert_recipient (id number(19,0) not null, batch_statistics_id number(19,0), recipient_id varchar2(255 char), insert_schedule_id number(19,0), rate_schedule_id number(19,0), sheet_count number(19,0), delivery_weight number(19,0), delivery_cost number(19,0), primary key (id));
create table job_insert_recipient_report (id number(19,0) not null, job_insert_recipient_id number(19,0), insert_id number(19,0), delivered number(1,0), primary key (id));
create table job_insert_schedule (id number(19,0) not null, job_id number(19,0), insert_schedule_id number(19,0), name varchar2(96 char) not null, schedule_id varchar2(96 char), primary key (id));
create table job_insert_schedule_insert_id (job_insert_schedule_id number(19,0) not null, insert_id number(19,0) not null, primary key (job_insert_schedule_id, insert_id));
create table job_insert_schedule_rates (job_insert_schedule_id number(19,0) not null, number_of_sheets number(19,0) not null, rate_schedule_id number(19,0), primary key (job_insert_schedule_id, number_of_sheets));
create table job_insert_schedule_statistic (id number(19,0) not null, batch_statistics_id number(19,0), insert_schedule_id number(19,0), qualified number(19,0), primary key (id));
create table job_insert_statistic (id number(19,0) not null, batch_statistics_id number(19,0), insert_id number(19,0), qualified number(19,0), delivered number(19,0), primary key (id));
create table job_message (id number(19,0) not null, job_id number(19,0), content_object_id number(19,0), message_external_id varchar2(255 char), name varchar2(96 char) not null, job_metadata_model_id number(19,0), primary key (id));
create table job_rate_schedule (id number(19,0) not null, job_id number(19,0), rate_schedule_id number(19,0), name varchar2(96 char) not null, envelope_name varchar2(255 char), envelope_weight number(19,0), weight_limits_units number(19,0), primary key (id));
create table job_rate_schedule_statistic (id number(19,0) not null, batch_statistics_id number(19,0), rate_schedule_id number(19,0), qualified number(19,0), primary key (id));
create table job_statistics_variant (job_id number(19,0) not null, part_id number(10,0) not null, transaction_id varchar2(255 char) not null, content_object_id number(19,0) not null, variant_id number(19,0) not null, qualified number(19,0), delivered number(19,0), total number(19,0), primary key (job_id, part_id, transaction_id, content_object_id, variant_id));
create table job_weight_limit (id number(19,0) not null, job_rate_schedule_id number(19,0), weight_limit number(19,0), cost number(19,0), primary key (id));
create table log_entry (id number(19,0) not null, guid varchar2(255 char) not null, detail_id number(19,0), type_f number(10,0), text varchar2(255 char), log_time timestamp, updated timestamp, primary key (id));
create table message_coverage_report (id number(19,0) not null, guid varchar2(255 char), zone_coverage_report_id number(19,0), content_object_id number(19,0), qualified number(19,0), disqualified number(19,0), attempts number(19,0), primary key (id));
create table msg_target_coverage_report (message_coverage_report_id number(19,0) not null, targetgrp_coverage_report_id number(19,0) not null, primary key (message_coverage_report_id, targetgrp_coverage_report_id));
create table report (job_id number(19,0) not null, part_id number(10,0) not null, transaction_id varchar2(255 char) not null, sq number(19,0) not null, customer_id varchar2(255 char), zone_id number(19,0), content_object_id number(19,0), variant_id number(19,0), tp_selectable_id number(19,0), played number(1,0), primary key (job_id, part_id, transaction_id, sq));
create table report_graphic (job_id number(19,0) not null, batch_id number(10,0) not null, transaction_id varchar2(255 char) not null, seq number(19,0) not null, report_sequence number(19,0), part_number number(19,0), content_library_id number(19,0), content_library_selection_id number(19,0), primary key (job_id, batch_id, transaction_id, seq));
create table report_job_metadata (id number(19,0) not null, delivery_event_id number(19,0), delivery_event_type_id number(19,0), delivery_event_request_date timestamp, job_id number(19,0), job_creation_date timestamp, primary key (id));
create table report_job_metadata_detail (id number(19,0) not null, job_id number(19,0), return_code number(10,0), log clob, completion_date varchar2(255 char), part_id number(19,0), primary key (id));
create table report_job_metadata_messages (parent_id number(19,0) not null, content_object_id number(19,0));
create table report_job_metadata_part (id number(19,0) not null, name varchar2(96 char), sequence number(10,0), primary key (id));
create table report_job_metadata_xml (xml_id number(10,0) not null, parent_id number(19,0) not null, xml_data clob, xml_compressed number(1,0), primary key (xml_id, parent_id));
create table rpt_job_metadata_trgt_grps (parent_id number(19,0) not null, target_group_id number(19,0));
create table rule_coverage_report (id number(19,0) not null, guid varchar2(255 char), rule_instance_id number(19,0), passes number(19,0), attempts number(19,0), or_conditions number(1,0), target_coverage_report_id number(19,0), primary key (id));
create table simulation_coverage_report (id number(19,0) not null, job_id number(19,0), customer_count number(19,0), primary key (id));
create table target_group_coverage_report (id number(19,0) not null, guid varchar2(255 char), model_id number(19,0), target_group_id number(19,0), passes number(19,0), attempts number(19,0), exclude number(1,0), or_rules number(1,0), primary key (id));
create table unhandled_delivery_updates (id number(19,0) not null, email_guid varchar2(255 char), email_status number(10,0), created timestamp, primary key (id));
create table zone_coverage_report (id number(19,0) not null, zone_id number(19,0), data_group_count number(19,0), messages_qualified number(19,0), qualification_attempts number(19,0), simulation_coverage_report_id number(19,0), primary key (id));
alter table log_entry add constraint uc_guid_log_entry unique (guid);

drop table schema_info_report;
create table schema_info_report (version number(11,0));
drop table data_migration_info_report;
create table data_migration_info_report (version number(11,0));
drop table schema_migrations_report;
