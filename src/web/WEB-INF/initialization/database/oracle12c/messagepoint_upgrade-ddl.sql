drop table aggregation_operator cascade constraints;
drop table all_reference_query cascade constraints;
drop table app_version cascade constraints;
drop table background_themes cascade constraints;
drop table barcode_type cascade constraints;
drop table category_group cascade constraints;
drop table condition_operator cascade constraints;
drop table condition_type cascade constraints;
drop table content_type cascade constraints;
drop table daily_frequency_type cascade constraints;
drop table data_comparison cascade constraints;
drop table data_record_level cascade constraints;
drop table data_subtype cascade constraints;
drop table data_subtype_comparison cascade constraints;
drop table data_type cascade constraints;
drop table data_type_subtype cascade constraints;
drop table date_data_value cascade constraints;
drop table deactivate_types cascade constraints;
drop table delivery_event_type cascade constraints;
drop table deserver_communicationtype cascade constraints;
drop table dialogue_data_mapping cascade constraints;
drop table encoding_type cascade constraints;
drop table event_type cascade constraints;
drop table frequency_type cascade constraints;
drop table item_type cascade constraints;
drop table layout_type cascade constraints;
drop table mp_channel cascade constraints;
drop table mp_connector cascade constraints;
drop table mp_qualification_output cascade constraints;
drop table navigation_drop_down_menu cascade constraints;
drop table navigation_drop_down_menu_item cascade constraints;
drop table navigation_menu_item_perm cascade constraints;
drop table navigation_tab_default_map cascade constraints;
drop table navigation_tab_permission cascade constraints;
drop table navigation_tabs cascade constraints;
drop table navigation_tree cascade constraints;
drop table old_workflow cascade constraints;
drop table permission cascade constraints;
drop table permission_category cascade constraints;
drop table pod_status cascade constraints;
drop table pod_type cascade constraints;
drop table record_type cascade constraints;
drop table reference_query cascade constraints;
drop table source_type cascade constraints;
drop table sub_content_type cascade constraints;
drop table system_state cascade constraints;
drop table system_task_type cascade constraints;
drop table system_theme cascade constraints;
drop table version_activity_reason cascade constraints;
drop table version_status cascade constraints;
drop table workflow_position cascade constraints;
drop table workflow_position_permission cascade constraints;
drop table workflow_property cascade constraints;
drop table workflow_state cascade constraints;
drop table workflow_tab cascade constraints;
drop table workflow_tab_permission cascade constraints;
create table aggregation_operator (id number(10,0) not null, operator varchar2(255 char) not null, primary key (id));
create table all_reference_query (id number(19,0) not null, object_class_name varchar2(255 char) not null, direct_ref_class_name varchar2(255 char), f_query varchar2(4000 char), f_type varchar2(8 char), primary key (id));
create table app_version (id number(19,0) not null, version_key varchar2(255 char), version_value varchar2(255 char), updated timestamp, updated_by_id number(19,0), created timestamp, created_by_id number(19,0), primary key (id));
create table background_themes (id number(19,0) not null, name varchar2(96 char) not null, filename varchar2(255 char) not null, primary key (id));
create table barcode_type (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), sample_image varchar2(255 char), enabled number(1,0), type number(10,0), primary key (id));
create table category_group (category_group_id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), priority number(10,0) not null, primary key (category_group_id));
create table condition_operator (id number(10,0) not null, operator varchar2(255 char) not null, primary key (id));
create table condition_type (id number(10,0) not null, name varchar2(96 char), parameterized number(1,0) default 1, description varchar2(255 char), primary key (id));
create table content_type (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table daily_frequency_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table data_comparison (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), scheme varchar2(255 char), primary key (id));
create table data_record_level (id number(10,0) not null, name varchar2(96 char), f_value number(10,0), primary key (id));
create table data_subtype (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), selectable number(1,0), primary key (id));
create table data_subtype_comparison (data_subtype_id number(10,0) not null, data_comparison_id number(19,0) not null, primary key (data_subtype_id, data_comparison_id));
create table data_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), selectable number(1,0), primary key (id));
create table data_type_subtype (data_type_id number(10,0) not null, data_subtype_id number(10,0) not null, primary key (data_type_id, data_subtype_id));
create table date_data_value (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table deactivate_types (id number(19,0) not null, name varchar2(96 char), primary key (id));
create table delivery_event_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table deserver_communicationtype (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table dialogue_data_mapping (id number(19,0) not null, dialogue_data_type varchar2(255 char), data_type_id number(10,0), primary key (id));
create table encoding_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table event_type (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table frequency_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table item_type (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table layout_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table mp_channel (id number(19,0) not null, name varchar2(96 char), presentation_name varchar2(96 char), created timestamp, updated timestamp, primary key (id));
create table mp_connector (id number(19,0) not null, name varchar2(96 char), presentation_name varchar2(96 char), created timestamp, updated timestamp, channel_id number(19,0), primary key (id));
create table mp_qualification_output (id number(19,0) not null, name varchar2(96 char), created timestamp, updated timestamp, primary key (id));
create table navigation_drop_down_menu (id number(19,0) not null, name varchar2(255 char) not null, ordering number(10,0) not null, tab_id number(19,0), string_id varchar2(255 char), requires varchar2(255 char), primary key (id));
create table navigation_drop_down_menu_item (id number(19,0) not null, name varchar2(255 char), type varchar2(2 char), icon varchar2(255 char), ordering number(10,0) not null, menu_id number(19,0) not null, tree_id number(19,0), string_id varchar2(255 char), url varchar2(255 char), requires varchar2(255 char), store_in_session number(1,0), authorization_type varchar2(2 char) not null, primary key (id));
create table navigation_menu_item_perm (navigation_menu_item_id number(19,0) not null, permission_id number(19,0) not null, primary key (navigation_menu_item_id, permission_id));
create table navigation_tab_default_map (tab_id number(19,0) not null, type varchar2(255 char) not null, item_id number(19,0) not null, primary key (tab_id, type));
create table navigation_tab_permission (navigation_tab_id number(19,0) not null, permission_id number(19,0) not null, primary key (navigation_tab_id, permission_id));
create table navigation_tabs (id number(19,0) not null, ordering number(10,0) not null, name varchar2(255 char), string_id varchar2(255 char), url varchar2(255 char), default_menu_item_id number(19,0), authorization_type varchar2(2 char) not null, primary key (id));
create table navigation_tree (id number(19,0) not null, f_query varchar2(510 char), parent_id number(19,0), parameter varchar2(255 char), url varchar2(255 char), icon varchar2(255 char), type number(10,0), sequence number(10,0), primary key (id));
create table old_workflow (id number(19,0) not null, name varchar2(255 char) not null, class_name varchar2(255 char) not null, primary key (id));
create table permission (id number(19,0) not null, name varchar2(255 char), description varchar2(255 char), type varchar2(2 char) not null, updated timestamp, updated_by_id number(19,0), category_id number(19,0), created timestamp, primary key (id));
create table permission_category (id number(19,0) not null, name varchar2(255 char), description varchar2(255 char), priority number(10,0) not null, category_group_id number(19,0), primary key (id));
create table pod_status (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table pod_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table record_type (id number(10,0) not null, name varchar2(96 char), string_id varchar2(255 char), primary key (id));
create table reference_query (id number(19,0) not null, object_class_name varchar2(255 char) not null, direct_ref_class_name varchar2(255 char), f_query varchar2(4000 char), f_type varchar2(8 char), primary key (id));
create table source_type (id number(10,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table sub_content_type (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), parent_content_id number(19,0), primary key (id));
create table system_state (id number(19,0) not null, name varchar2(96 char), string_code varchar2(255 char), description varchar2(255 char), bit_flag number(19,0), primary key (id));
create table system_task_type (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), primary key (id));
create table system_theme (id number(19,0) not null, name varchar2(96 char), folder varchar2(255 char), uploaded number(1,0), primary key (id));
create table version_activity_reason (id number(19,0) not null, name varchar2(96 char), description varchar2(255 char), string_code varchar2(255 char), primary key (id));
create table version_status (id number(19,0) not null, name varchar2(96 char), string_code varchar2(255 char), description varchar2(255 char), primary key (id));
create table workflow_position (id number(19,0) not null, workflow_id number(19,0) not null, workflow_state_id number(19,0) not null, required number(1,0) default 1, requires_permission number(1,0) default 0, enabled number(1,0), name varchar2(255 char) not null, authorization_type varchar2(2 char) not null, previous number(19,0), next number(19,0), primary key (id));
create table workflow_position_permission (workflow_position_id number(19,0) not null, permission_id number(19,0) not null, primary key (workflow_position_id, permission_id));
create table workflow_property (id number(19,0) not null, workflow_position_id number(19,0) not null, required number(1,0) default 1, name varchar2(255 char) not null, primary key (id));
create table workflow_state (id number(19,0) not null, name varchar2(96 char), string_id varchar2(255 char), primary key (id));
create table workflow_tab (id number(19,0) not null, workflow_position_id number(19,0) not null, name varchar2(255 char) not null, parameter varchar2(255 char) not null, string_id varchar2(255 char), authorization_type varchar2(2 char) not null, edit_url varchar2(255 char), view_url varchar2(255 char), list_url varchar2(255 char), visibility_toggle_attr varchar2(255 char), required_id number(19,0), primary key (id));
create table workflow_tab_permission (workflow_tab_id number(19,0) not null, permission_id number(19,0) not null, primary key (workflow_tab_id, permission_id));
