CREATE OR <PERSON><PERSON><PERSON>CE PROCEDURE CreateSyncHistory(PodMaster VARCHAR2, SourceInstanceSchema VARCHAR2, TargetInstanceSchema VARCHAR2, CopiedTime TIMESTAMP DEFAULT NULL) AUTHID CURRENT_USER AS
    TYPE RefCurType                 IS REF CURSOR;

    SourceInstanceId        NUMBER;
    SourceInstanceGuid      VARCHAR2(255);
    SourceInstanceCreated   TIMESTAMP;

    TargetInstanceId        NUMBER;
    TargetInstanceGuid      VARCHAR2(255);

    TargetInstanceCreatedById   NUMBER;

    InstanceCopiedTime      TIMESTAMP;
    
    DocumentID      NUMBER;
    DocumentName    VARCHAR2(255);
    DocumentDna     VARCHAR2(255);
    DocumentGuid    VARCHAR2(255);

    LogOutputFile                   UTL_FILE.FILE_TYPE;

    SyncTime                        TIMESTAMP;

    HasHideUntilNextChange          BOOLEAN := FALSE;
    HasLanguageHash                 BOOLEAN := FALSE;
    IsNewContentObjectModel         BOOLEAN := FALSE;
    
    CONTENTTYPE_TEXT               NUMBER := 1;
    CONTENTTYPE_GRAPHIC            NUMBER := 2;

	OBJECT_TYPE_MESSAGE				NUMBER :=  0;
	OBJECT_TYPE_LOCAL_SMART_TEXT	NUMBER :=  1;
	OBJECT_TYPE_LOCAL_IMAGE			NUMBER :=  2;
	OBJECT_TYPE_GLOBAL_SMART_TEXT	NUMBER :=  4;
	OBJECT_TYPE_GLOBAL_IMAGE		NUMBER :=  8;

	OBJECT_TYPE_ANY_LOCAL			NUMBER :=  3;
	OBJECT_TYPE_ANY_GLOBAL			NUMBER := 12;

	SYNCTYPE_ALL				   NUMBER := 0;
	SYNCTYPE_MESSAGE			   NUMBER := 1;
	SYNCTYPE_ZONE 			       NUMBER := 2;
	SYNCTYPE_SECTION			   NUMBER := 3;
	SYNCTYPE_VARIANT			   NUMBER := 4;
	SYNCTYPE_LOCAL_SMART_TEXT	   NUMBER := 5;
	SYNCTYPE_LOCAL_IMAGE		   NUMBER := 6;
    SYNCTYPE_LOCAL_SMART_CANVAS    NUMBER := 7;
    SYNCTYPE_DOCUMENT              NUMBER := 8;
    SYNCTYPE_SMART_CANVAS          NUMBER := 9;
    SYNCTYPE_SMART_TEXT            NUMBER := 10;
    SYNCTYPE_CONTENT_LIBRARY       NUMBER := 11;

    SYNCTYPE_TARGET_GROUP          NUMBER := 12;
    SYNCTYPE_TARGET_RULE           NUMBER := 13;
    SYNCTYPE_PARAMETER_GROUP       NUMBER := 14;
    SYNCTYPE_VARIABLE              NUMBER := 15;

    SYNCTYPE_DOCUMENT_SETTING      NUMBER := 16;

    SYNCTYPE_DATASOURCE            NUMBER := 17;
    SYNCTYPE_DATACOLLECTION        NUMBER := 18;

    SYNCTYPE_LOOKUPTABLE           NUMBER := 19;

    SYNCTYPE_TEXTSTYLE             NUMBER := 20;
    SYNCTYPE_PARAGRAPHSTYLE        NUMBER := 21;
    SYNCTYPE_LISTSTYLE             NUMBER := 22;

    RefCur          RefCurType;

    ModelID         NUMBER;
    ModelGUID       VARCHAR2(255);
    SourceCreated   TIMESTAMP;
    TargetCreated   TIMESTAMP;

    SourceUpdated   TIMESTAMP;
    TargetUpdated   TIMESTAMP;

    SourceModelHash VARCHAR2(255);
    TargetModelHash VARCHAR2(255);

    SourceArchivedCopyHash    VARCHAR2(255);
    SourceActiveCopyHash      VARCHAR2(255);
    SourceWorkingCopyHash     VARCHAR2(255);

    TargetArchivedCopyHash    VARCHAR2(255);
    TargetActiveCopyHash      VARCHAR2(255);
    TargetWorkingCopyHash     VARCHAR2(255);

    SourceArchivedCopyAttrHash VARCHAR2(255);
    SourceActiveCopyAttrHash   VARCHAR2(255);
    SourceWorkingCopyAttrHash  VARCHAR2(255);

    TargetArchivedCopyAttrHash VARCHAR2(255);
    TargetActiveCopyAttrHash   VARCHAR2(255);
    TargetWorkingCopyAttrHash  VARCHAR2(255);

    SourceArchivedCopyLanguageHash NUMBER;
    SourceActiveCopyLanguageHash   NUMBER;
    SourceWorkingCopyLanguageHash  NUMBER;

    TargetArchivedCopyLanguageHash NUMBER;
    TargetActiveCopyLanguageHash   NUMBER;
    TargetWorkingCopyLanguageHash  NUMBER;

    SyncHistoryID                  NUMBER;


    PROCEDURE LogInfo(Msg VARCHAR2)
    IS
    BEGIN
        DBMS_OUTPUT.PUT_LINE(Msg);
        UTL_FILE.PUT_LINE(LogOutputFile, TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SSxFF3') || ' ' || Msg, TRUE);
    END LogInfo;

    FUNCTION TableExists(SchemaName VARCHAR2, TableName VARCHAR2) RETURN BOOLEAN IS
        TableCount  NUMBER := 0;
    BEGIN
        EXECUTE IMMEDIATE
        '
            SELECT COUNT(*)
            FROM ALL_TABLES
            WHERE TABLE_NAME = :TableName AND OWNER = :SchemaName
        '
        INTO TableCount 
        USING TableName, SchemaName;

        RETURN TableCount > 0;
    END TableExists;

    FUNCTION ColumnExists(SchemaName VARCHAR2, TableName VARCHAR2, ColumnName VARCHAR2) RETURN BOOLEAN IS
        ColumnCount  NUMBER := 0;
    BEGIN
        EXECUTE IMMEDIATE '
            SELECT COUNT(*) 
            FROM ALL_TAB_COLUMNS
            WHERE COLUMN_NAME = :COLUMN_NAME AND TABLE_NAME = :TableName AND OWNER = :SchemaName' 
        INTO ColumnCount 
        USING ColumnName, TableName, SchemaName;

        RETURN ColumnCount > 0;
    END ColumnExists;

    FUNCTION SyncHistoryExists(ObjectID NUMBER) RETURN BOOLEAN IS
        SyncHistoryCount    NUMBER := 0;
    BEGIN
        EXECUTE IMMEDIATE '
            SELECT COUNT(*)
            FROM ' || TargetInstanceSchema || '.SYNC_HISTORY
            WHERE OBJECT_ID = :ObjectID 
                AND DOCUMENT_ID = :DocumentID
                AND SOURCE_DOCUMENT_ID = :SourceDocumentID 
                AND INSTANCE_GUID = :SourceInstanceGuid' 
        INTO SyncHistoryCount 
        USING ObjectID, DocumentID, DocumentID, SourceInstanceGuid;
        
        IF SyncHistoryCount > 0 THEN 
            LogInfo('    SyncHistory exists.');
        END IF;
        
        RETURN SyncHistoryCount > 0;
    END SyncHistoryExists;

    FUNCTION FetchModel(ModelName VARCHAR2) RETURN BOOLEAN IS
    BEGIN
        ModelID         := NULL;
        ModelGUID       := NULL;
        SourceCreated   := NULL;
        TargetCreated   := NULL;

        SourceUpdated   := NULL;
        TargetUpdated   := NULL;

        SourceModelHash := NULL;
        TargetModelHash := NULL;

        SourceArchivedCopyHash    := NULL;
        SourceActiveCopyHash      := NULL;
        SourceWorkingCopyHash     := NULL;

        TargetArchivedCopyHash    := NULL;
        TargetActiveCopyHash      := NULL;
        TargetWorkingCopyHash     := NULL;

        SourceArchivedCopyAttrHash := NULL;
        SourceActiveCopyAttrHash   := NULL;
        SourceWorkingCopyAttrHash  := NULL;

        TargetArchivedCopyAttrHash := NULL;
        TargetActiveCopyAttrHash   := NULL;
        TargetWorkingCopyAttrHash  := NULL;

        SourceArchivedCopyLanguageHash := NULL;
        SourceActiveCopyLanguageHash   := NULL;
        SourceWorkingCopyLanguageHash  := NULL;

        TargetArchivedCopyLanguageHash := NULL;
        TargetActiveCopyLanguageHash   := NULL;
        TargetWorkingCopyLanguageHash  := NULL;

        SyncHistoryID                  := NULL;

        BEGIN
            FETCH RefCur INTO ModelID, ModelGUID, SourceCreated, TargetCreated, SourceUpdated, TargetUpdated, SourceModelHash, TargetModelHash;
            IF RefCur%NOTFOUND THEN
                RETURN FALSE;
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                LogInfo('    FetchModel: Error');
                RAISE;
        END;
        
        IF SourceUpdated IS NULL THEN
            SourceUpdated := SourceCreated;
        END IF;

        IF TargetUpdated IS NULL THEN
            TargetUpdated := TargetCreated;
        END IF;

        LogInfo('  ' || ModelName || ' ID = ' || ModelID);

        IF SourceModelHash IS NULL THEN
            SourceModelHash := 'SOURCE MODEL HASH WAS NULL';
        END IF;

        IF TargetModelHash IS NULL THEN
            TargetModelHash := 'TARGET MODEL HASH WAS NULL';
        END IF;

        RETURN TRUE;
    END FetchModel;

    FUNCTION CompareString(String1 VARCHAR2, String2 VARCHAR2) RETURN NUMBER IS
    BEGIN
        IF String1 IS NULL AND String2 IS NULL THEN
            RETURN 0;
        END IF;

        IF String1 IS NULL AND String2 IS NOT NULL THEN
            RETURN -1;
        END IF;

        IF String1 IS NOT NULL AND String2 IS NULL THEN
            RETURN 1;
        END IF;

        IF String1 < String2 THEN
            RETURN -1;
        END IF;

        IF String1 > String2 THEN 
            RETURN 1;
        END IF;

        RETURN 0;
    END CompareString;

    FUNCTION CompareLanguageHash(LanguageHash1 NUMBER, LanguageHash2 NUMBER) RETURN NUMBER IS
        DifferenceCount NUMBER;
    BEGIN
        IF LanguageHash1 IS NULL AND LanguageHash2 IS NULL THEN
            RETURN 0;
        END IF;
        
        IF LanguageHash1 IS NOT NULL AND LanguageHash2 IS NULL THEN
            RETURN 1;
        END IF;
        
        IF LanguageHash1 IS NULL AND LanguageHash2 IS NOT NULL THEN
            RETURN -1;
        END IF;
        
        EXECUTE IMMEDIATE '
            SELECT COUNT(*)
            FROM (
                SELECT HM.MESSAGEPOINT_LOCALE_ID, HM.SHA256_HASH
                FROM ' || TargetInstanceSchema || '.LANGUAGE_CONTENT_HASH H
                LEFT OUTER JOIN ' || TargetInstanceSchema || '.LANGUAGE_CONTENT_HASH_MAP HM ON HM.LANGUAGE_CONTENT_HASH_ID = H.ID
                WHERE H.ID = :LanguageHash1
            ) S
            FULL OUTER JOIN
            (
                SELECT HM.MESSAGEPOINT_LOCALE_ID, HM.SHA256_HASH
                FROM ' || TargetInstanceSchema || '.LANGUAGE_CONTENT_HASH H
                LEFT OUTER JOIN ' || TargetInstanceSchema || '.LANGUAGE_CONTENT_HASH_MAP HM ON HM.LANGUAGE_CONTENT_HASH_ID = H.ID
                WHERE H.ID = :LanguageHash2
            ) T ON T.MESSAGEPOINT_LOCALE_ID = S.MESSAGEPOINT_LOCALE_ID
            WHERE  (S.SHA256_HASH IS NULL AND T.SHA256_HASH IS NOT NULL) 
                OR (S.SHA256_HASH IS NOT NULL AND T.SHA256_HASH IS NULL)
                OR (S.SHA256_HASH IS NOT NULL AND T.SHA256_HASH IS NOT NULL AND S.SHA256_HASH <> T.SHA256_HASH)
        ' 
        INTO DifferenceCount
        USING LanguageHash1, LanguageHash2;

        RETURN DifferenceCount;
    END CompareLanguageHash;

    FUNCTION GetNewID RETURN NUMBER IS
        NewID   NUMBER;
    BEGIN
        EXECUTE IMMEDIATE 'SELECT ' || TargetInstanceSchema || '.HIBERNATE_SEQUENCE.NEXTVAL FROM DUAL' INTO NewID;
        RETURN NewID;
    END GetNewID;

    FUNCTION CopyLanguagContentHash(InstanceSchema IN VARCHAR2, OriginLanguageHashID NUMBER) RETURN NUMBER IS
        LanguageContentHashID NUMBER;
    BEGIN        
        IF OriginLanguageHashID IS NULL THEN
            LanguageContentHashID := NULL;
        ELSE
            LanguageContentHashID := GetNewID();
    
            EXECUTE IMMEDIATE '
                INSERT INTO ' || TargetInstanceSchema || '.LANGUAGE_CONTENT_HASH(ID) VALUES(:LanguageContentHashID)
            ' USING LanguageContentHashID;
    
            EXECUTE IMMEDIATE '
                INSERT INTO ' || TargetInstanceSchema || '.LANGUAGE_CONTENT_HASH_MAP(LANGUAGE_CONTENT_HASH_ID, MESSAGEPOINT_LOCALE_ID, SHA256_HASH) 
                SELECT ' || LanguageContentHashID || ', MESSAGEPOINT_LOCALE_ID, SHA256_HASH
                FROM ' || InstanceSchema || '.LANGUAGE_CONTENT_HASH_MAP 
                WHERE LANGUAGE_CONTENT_HASH_ID = :OriginLanguageHashID
            ' USING OriginLanguageHashID;
        END IF;
        
        RETURN LanguageContentHashID;
    END CopyLanguagContentHash;

    PROCEDURE GetVersionHashes
    (
        InstanceSchema              IN VARCHAR2, 
        SqlQuery                    IN VARCHAR2,
        LastUpdated                 IN OUT NOCOPY TIMESTAMP,
        ArchivedCopyHash            OUT NOCOPY VARCHAR2,         
        ActiveCopyHash              OUT NOCOPY VARCHAR2,           
        WorkingCopyHash             OUT NOCOPY VARCHAR2, 
        ArchivedCopyAttrHash        OUT NOCOPY VARCHAR2,
        ActiveCopyAttrHash          OUT NOCOPY VARCHAR2,
        WorkingCopyAttrHash         OUT NOCOPY VARCHAR2, 
        CopiedArchivedCopyLanguageHash    OUT NOCOPY NUMBER, 
        CopiedActiveCopyLanguageHash      OUT NOCOPY NUMBER,
        CopiedWorkingCopyLanguageHash     OUT NOCOPY NUMBER
    ) IS 
        ArchivedCopyID              NUMBER;
        ActiveCopyID                NUMBER;
        WorkingCopyID               NUMBER;

        ArchivedCopyCreated         TIMESTAMP;
        ActiveCopyCreated           TIMESTAMP;
        WorkingCopyCreated          TIMESTAMP;

        ArchivedCopyUpdated         TIMESTAMP;
        ActiveCopyUpdated           TIMESTAMP;
        WorkingCopyUpdated          TIMESTAMP;

        ArchivedCopyLanguageHash    NUMBER;
        ActiveCopyLanguageHash      NUMBER;
        WorkingCopyLanguageHash     NUMBER;
    BEGIN
        EXECUTE IMMEDIATE SqlQuery
        INTO 
            ArchivedCopyID, ActiveCopyID, WorkingCopyID, 
            ArchivedCopyHash,   ArchivedCopyCreated,    ArchivedCopyUpdated,    ArchivedCopyAttrHash,   ArchivedCopyLanguageHash,
            ActiveCopyHash,     ActiveCopyCreated,      ActiveCopyUpdated,      ActiveCopyAttrHash,     ActiveCopyLanguageHash,
            WorkingCopyHash,    WorkingCopyCreated,     WorkingCopyUpdated,     WorkingCopyAttrHash,    WorkingCopyLanguageHash
        ;

        IF ActiveCopyID IS NOT NULL THEN
            IF ActiveCopyCreated IS NOT NULL AND ActiveCopyCreated > LastUpdated THEN
                LastUpdated := ActiveCopyCreated;
            END IF;
            IF ActiveCopyUpdated IS NOT NULL AND ActiveCopyUpdated > LastUpdated THEN
                LastUpdated := ActiveCopyUpdated;
            END IF;
        ELSIF ArchivedCopyID IS NOT NULL THEN
            IF ArchivedCopyCreated IS NOT NULL AND ArchivedCopyCreated > LastUpdated THEN
                LastUpdated := ArchivedCopyCreated;
            END IF;
            IF ArchivedCopyUpdated IS NOT NULL AND ArchivedCopyUpdated > LastUpdated THEN
                LastUpdated := ArchivedCopyUpdated;
            END IF;
        END IF;

        IF WorkingCopyID IS NOT NULL THEN
            IF WorkingCopyCreated IS NOT NULL AND WorkingCopyCreated > LastUpdated THEN
                LastUpdated := WorkingCopyCreated;
            END IF;
            IF WorkingCopyUpdated IS NOT NULL AND WorkingCopyUpdated > LastUpdated THEN
                LastUpdated := WorkingCopyUpdated;
            END IF;
        END IF;

        IF HasLanguageHash THEN
            CopiedArchivedCopyLanguageHash := CopyLanguagContentHash(InstanceSchema, ArchivedCopyLanguageHash);
            CopiedActiveCopyLanguageHash   := CopyLanguagContentHash(InstanceSchema, ActiveCopyLanguageHash);
            CopiedWorkingCopyLanguageHash  := CopyLanguagContentHash(InstanceSchema, WorkingCopyLanguageHash);
        ELSE
            CopiedArchivedCopyLanguageHash := NULL;
            CopiedActiveCopyLanguageHash   := NULL;
            CopiedWorkingCopyLanguageHash  := NULL;
        END IF;
    END GetVersionHashes;

    PROCEDURE GetVersionedInstanceHashes
    (
        InstanceSchema              IN VARCHAR2, 
        ModelTable                  IN VARCHAR2, 
        InstanceTable               IN VARCHAR2, 
        VersionMapTable             IN VARCHAR2, 
        ModelID                     IN NUMBER, 
        LastUpdated                 IN OUT NOCOPY TIMESTAMP,
        ArchivedCopyHash            OUT NOCOPY VARCHAR2,         
        ActiveCopyHash              OUT NOCOPY VARCHAR2,           
        WorkingCopyHash             OUT NOCOPY VARCHAR2, 
        ArchivedCopyAttrHash        OUT NOCOPY VARCHAR2,
        ActiveCopyAttrHash          OUT NOCOPY VARCHAR2,
        WorkingCopyAttrHash         OUT NOCOPY VARCHAR2, 
        ArchivedCopyLanguageHash    OUT NOCOPY NUMBER, 
        ActiveCopyLanguageHash      OUT NOCOPY NUMBER,
        WorkingCopyLanguageHash     OUT NOCOPY NUMBER
    ) IS
        SelectFields                VARCHAR2(4096);
    BEGIN
        IF HasLanguageHash THEN 
            SelectFields := '                    
                    AR.ID, AC.ID, WC.ID,
                    AR.SHA256_HASH, AR.CREATED, AR.UPDATED, AR.ATTRIBUTES_HASH, AR.LANGUAGE_CONTENT_HASH_ID,
                    AC.SHA256_HASH, AC.CREATED, AC.UPDATED, AC.ATTRIBUTES_HASH, AC.LANGUAGE_CONTENT_HASH_ID,
                    WC.SHA256_HASH, WC.CREATED, WC.UPDATED, WC.ATTRIBUTES_HASH, WC.LANGUAGE_CONTENT_HASH_ID
            ';
        ELSE
            SelectFields := '                    
                    AR.ID, AC.ID, WC.ID,
                    AR.SHA256_HASH, AR.CREATED, AR.UPDATED, NULL, NULL,
                    AC.SHA256_HASH, AC.CREATED, AC.UPDATED, NULL, NULL,
                    WC.SHA256_HASH, WC.CREATED, WC.UPDATED, NULL, NULL
            ';
        END IF;

        GetVersionHashes(InstanceSchema,
            '
            SELECT ' || SelectFields || '
            FROM ' || InstanceSchema || '.' || ModelTable || ' M
            LEFT OUTER JOIN ' || InstanceSchema || '.' || VersionMapTable || ' ARVM ON ARVM.' || ModelTable || '_ID = M.ID AND ARVM.STATUS_ID = 2 AND ARVM.END_DATE IS NOT NULL AND ARVM.END_DATE < CURRENT_TIMESTAMP AND ARVM.LATEST_ARCHIVED <> 0
            LEFT OUTER JOIN ' || InstanceSchema || '.' || InstanceTable || ' AR ON AR.ID = ARVM.' || InstanceTable || '_ID
            LEFT OUTER JOIN ' || InstanceSchema || '.' || VersionMapTable || ' ACVM ON ACVM.' || ModelTable || '_ID = M.ID AND ACVM.STATUS_ID = 2 AND (ACVM.END_DATE IS NULL OR ACVM.END_DATE > CURRENT_TIMESTAMP)
            LEFT OUTER JOIN ' || InstanceSchema || '.' || InstanceTable || ' AC ON AC.ID = ACVM.' || InstanceTable || '_ID
            LEFT OUTER JOIN ' || InstanceSchema || '.' || VersionMapTable || ' WCVM ON WCVM.' || ModelTable || '_ID = M.ID AND WCVM.STATUS_ID = 5 
            LEFT OUTER JOIN ' || InstanceSchema || '.' || InstanceTable || ' WC ON WC.ID = WCVM.' || InstanceTable || '_ID
            WHERE M.ID = ' || ModelID,
            LastUpdated,
            ArchivedCopyHash,         
            ActiveCopyHash,           
            WorkingCopyHash, 
            ArchivedCopyAttrHash,
            ActiveCopyAttrHash,
            WorkingCopyAttrHash, 
            ArchivedCopyLanguageHash, 
            ActiveCopyLanguageHash,
            WorkingCopyLanguageHash
        );
    END GetVersionedInstanceHashes;

    PROCEDURE GetContentObjectDataHashes
    (
        InstanceSchema              IN VARCHAR2, 
        ModelID                     IN NUMBER, 
        LastUpdated                 IN OUT NOCOPY TIMESTAMP,
        ArchivedCopyHash            OUT NOCOPY VARCHAR2,         
        ActiveCopyHash              OUT NOCOPY VARCHAR2,           
        WorkingCopyHash             OUT NOCOPY VARCHAR2, 
        ArchivedCopyAttrHash        OUT NOCOPY VARCHAR2,
        ActiveCopyAttrHash          OUT NOCOPY VARCHAR2,
        WorkingCopyAttrHash         OUT NOCOPY VARCHAR2, 
        ArchivedCopyLanguageHash    OUT NOCOPY NUMBER, 
        ActiveCopyLanguageHash      OUT NOCOPY NUMBER,
        WorkingCopyLanguageHash     OUT NOCOPY NUMBER
    ) IS
    BEGIN
        HasLanguageHash := TRUE;
        
        GetVersionHashes(InstanceSchema,
            '
            SELECT
                AR.CONTENT_OBJECT_ID, AC.CONTENT_OBJECT_ID, WC.CONTENT_OBJECT_ID,
                AR.SHA256_HASH, AR.CREATED, AR.UPDATED, AR.ATTRIBUTES_HASH, AR.LANGUAGE_CONTENT_HASH_ID,
                AC.SHA256_HASH, AC.CREATED, AC.UPDATED, AC.ATTRIBUTES_HASH, AC.LANGUAGE_CONTENT_HASH_ID,
                WC.SHA256_HASH, WC.CREATED, WC.UPDATED, WC.ATTRIBUTES_HASH, WC.LANGUAGE_CONTENT_HASH_ID
            FROM ' || InstanceSchema || '.CONTENT_OBJECT M
            LEFT OUTER JOIN ' || InstanceSchema || '.CONTENT_OBJECT_DATA AR ON AR.CONTENT_OBJECT_ID = M.ID AND AR.DATA_TYPE = 4
            LEFT OUTER JOIN ' || InstanceSchema || '.CONTENT_OBJECT_DATA AC ON AC.CONTENT_OBJECT_ID = M.ID AND AC.DATA_TYPE = 2
            LEFT OUTER JOIN ' || InstanceSchema || '.CONTENT_OBJECT_DATA WC ON WC.CONTENT_OBJECT_ID = M.ID AND WC.DATA_TYPE = 1
            WHERE M.ID = ' || ModelID,
            LastUpdated,
            ArchivedCopyHash,         
            ActiveCopyHash,           
            WorkingCopyHash, 
            ArchivedCopyAttrHash,
            ActiveCopyAttrHash,
            WorkingCopyAttrHash, 
            ArchivedCopyLanguageHash, 
            ActiveCopyLanguageHash,
            WorkingCopyLanguageHash
        );
    END GetContentObjectDataHashes;

    PROCEDURE AddSyncHistory(SyncObjectType NUMBER) IS
        HideUntilNextChangeFieldName VARCHAR2(255);
        HideUntilNextChangeValue     VARCHAR2(255);
    BEGIN
        IF HasHideUntilNextChange THEN 
            HideUntilNextChangeFieldName := 'HIDE_UNTIL_NEXT_CHANGE,';
            HideUntilNextChangeValue := '0,';
        ELSE
            HideUntilNextChangeFieldName := '';
            HideUntilNextChangeValue := '';
        END IF;
        
        BEGIN
            IF CompareString(SourceModelHash,       TargetModelHash) <> 0 OR
               CompareString(SourceArchivedCopyHash,    TargetArchivedCopyHash) <> 0 OR 
               CompareString(SourceActiveCopyHash,      TargetActiveCopyHash) <> 0 OR 
               CompareString(SourceWorkingCopyHash,     TargetWorkingCopyHash) <> 0 OR 
               CompareString(SourceArchivedCopyAttrHash,   TargetArchivedCopyAttrHash) <> 0 OR 
               CompareString(SourceActiveCopyAttrHash,     TargetActiveCopyAttrHash) <> 0 OR 
               CompareString(SourceWorkingCopyAttrHash,    TargetWorkingCopyAttrHash) <> 0 OR 
               CompareLanguageHash(SourceArchivedCopyLanguageHash, TargetArchivedCopyLanguageHash) <> 0 OR
               CompareLanguageHash(SourceActiveCopyLanguageHash,   TargetActiveCopyLanguageHash) <> 0 OR
               CompareLanguageHash(SourceWorkingCopyLanguageHash,  TargetWorkingCopyLanguageHash) <> 0
            THEN
                LogInfo('   AddSyncHistory: ! Source and target model hash was different');
                IF SourceUpdated > InstanceCopiedTime THEN
                    SourceModelHash := 'Source Changed After ' || TO_CHAR(InstanceCopiedTime, 'YYYY-MM-DD HH24:MI:SS.FF6');
                    LogInfo('   AddSyncHistory: ! Source model was changed ' || SourceUpdated || ' > ' || InstanceCopiedTime);
                END IF;
    
                IF TargetUpdated > InstanceCopiedTime THEN
                    TargetModelHash := 'Target Changed After ' || TO_CHAR(InstanceCopiedTime, 'YYYY-MM-DD HH24:MI:SS.FF6');
                    LogInfo('   AddSyncHistory: ! Target model was changed ' || TargetUpdated || ' > ' || InstanceCopiedTime);
                END IF;
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                LogInfo('   AddSyncHistory: Compare error !');
                RAISE;
        END;
        
        SyncHistoryID := GetNewID();

        LogInfo('   Adding new sync history ID = ' || SyncHistoryID);
        
        IF HasLanguageHash THEN
            BEGIN
                EXECUTE IMMEDIATE '
                    INSERT INTO ' || TargetInstanceSchema || '.SYNC_HISTORY
                    (
                        ID,
                        DOCUMENT_ID,
                        OBJECT_ID,
                        OBJECT_TYPE_ID,
                        ' || HideUntilNextChangeFieldName || '
                        INSTANCE_GUID,
                        SYNC_TYPE_ID,
                        SYNC_TIME,
                        SYNC_BY_ID,
                        SOURCE_DOCUMENT_ID,
                        SOURCE_OBJECT_ID,
                        SOURCE_MODEL_HASH,
                        TARGET_MODEL_HASH,
    
                        SOURCE_ARCHIVED_COPY_HASH,
                        SOURCE_ACTIVE_COPY_HASH,
                        SOURCE_WORKING_COPY_HASH,
    
                        TARGET_ARCHIVED_COPY_HASH,
                        TARGET_ACTIVE_COPY_HASH,
                        TARGET_WORKING_COPY_HASH,
    
                        SOURCE_ARCHIVED_COPY_ATTR_HASH,
                        SOURCE_ACTIVE_COPY_ATTR_HASH,
                        SOURCE_WORKING_COPY_ATTR_HASH,
    
                        TARGET_ARCHIVED_COPY_ATTR_HASH,
                        TARGET_ACTIVE_COPY_ATTR_HASH,
                        TARGET_WORKING_COPY_ATTR_HASH,
    
                        SOURCE_ARCHIVED_COPY_LANG_HASH,
                        SOURCE_ACTIVE_COPY_LANG_HASH,
                        SOURCE_WORKING_COPY_LANG_HASH,
    
                        TARGET_ARCHIVED_COPY_LANG_HASH,
                        TARGET_ACTIVE_COPY_LANG_HASH,
                        TARGET_WORKING_COPY_LANG_HASH
                    ) 
                    VALUES 
                    (
                        :ID,
                        :DOCUMENT_ID,
                        :OBJECT_ID,
                        :OBJECT_TYPE_ID,
                        ' || HideUntilNextChangeValue || '
                        :INSTANCE_GUID,
                        :SYNC_TYPE_ID,
                        :SYNC_TIME,
                        :SYNC_BY_ID,
                        :SOURCE_DOCUMENT_ID,
                        :SOURCE_OBJECT_ID,
                        
                        :SOURCE_MODEL_HASH,
                        :TARGET_MODEL_HASH,
    
                        :SOURCE_ARCHIVED_COPY_HASH,
                        :SOURCE_ACTIVE_COPY_HASH,
                        :SOURCE_WORKING_COPY_HASH,
    
                        :TARGET_ARCHIVED_COPY_HASH,
                        :TARGET_ACTIVE_COPY_HASH,
                        :TARGET_WORKING_COPY_HASH,
    
                        :SOURCE_ARCHIVED_COPY_ATTR_HASH,
                        :SOURCE_ACTIVE_COPY_ATTR_HASH,
                        :SOURCE_WORKING_COPY_ATTR_HASH,
    
                        :TARGET_ARCHIVED_COPY_ATTR_HASH,
                        :TARGET_ACTIVE_COPY_ATTR_HASH,
                        :TARGET_WORKING_COPY_ATTR_HASH,
    
                        :SOURCE_ARCHIVED_COPY_LANG_HASH,
                        :SOURCE_ACTIVE_COPY_LANG_HASH,
                        :SOURCE_WORKING_COPY_LANG_HASH,
    
                        :TARGET_ARCHIVED_COPY_LANG_HASH,
                        :TARGET_ACTIVE_COPY_LANG_HASH,
                        :TARGET_WORKING_COPY_LANG_HASH
                    )' 
                USING 
                    SyncHistoryId, DocumentId, ModelID, SyncObjectType, SourceInstanceGuid, 0, SyncTime, 
                    TargetInstanceCreatedById, DocumentId, ModelID, 
                    
                    SourceModelHash, 
                    TargetModelHash,
                    
                    SourceArchivedCopyHash, SourceActiveCopyHash, SourceWorkingCopyHash,
                    TargetArchivedCopyHash, TargetActiveCopyHash, TargetWorkingCopyHash,
                    
                    SourceArchivedCopyAttrHash, SourceActiveCopyAttrHash, SourceWorkingCopyAttrHash,
                    TargetArchivedCopyAttrHash, TargetActiveCopyAttrHash, TargetWorkingCopyAttrHash,
                    
                    SourceArchivedCopyLanguageHash, SourceActiveCopyLanguageHash,   SourceWorkingCopyLanguageHash,
                    TargetArchivedCopyLanguageHash, TargetActiveCopyLanguageHash,   TargetWorkingCopyLanguageHash
                    ;
            EXCEPTION
                WHEN OTHERS THEN
                    LogInfo('   AddSyncHistory: Insert sync history with language content hash error ! DocumentID:' || DocumentId || ' ModelID:' || ModelID);
                    RAISE;
            END;
        ELSE
            BEGIN
                EXECUTE IMMEDIATE '
                    INSERT INTO ' || TargetInstanceSchema || '.SYNC_HISTORY
                    (
                        ID,
                        DOCUMENT_ID,
                        OBJECT_ID,
                        OBJECT_TYPE_ID,
                        ' || HideUntilNextChangeFieldName || '
                        INSTANCE_GUID,
                        SYNC_TYPE_ID,
                        SYNC_TIME,
                        SYNC_BY_ID,
                        SOURCE_DOCUMENT_ID,
                        SOURCE_OBJECT_ID,
                        SOURCE_MODEL_HASH,
                        TARGET_MODEL_HASH,
    
                        SOURCE_ARCHIVED_COPY_HASH,
                        SOURCE_ACTIVE_COPY_HASH,
                        SOURCE_WORKING_COPY_HASH,
    
                        TARGET_ARCHIVED_COPY_HASH,
                        TARGET_ACTIVE_COPY_HASH,
                        TARGET_WORKING_COPY_HASH
                    ) 
                    VALUES 
                    (
                        :ID,
                        :DOCUMENT_ID,
                        :OBJECT_ID,
                        :OBJECT_TYPE_ID,
                        ' || HideUntilNextChangeValue || '
                        :INSTANCE_GUID,
                        :SYNC_TYPE_ID,
                        :SYNC_TIME,
                        :SYNC_BY_ID,
                        :SOURCE_DOCUMENT_ID,
                        :SOURCE_OBJECT_ID,
                        :SOURCE_MODEL_HASH,
                        :TARGET_MODEL_HASH,
    
                        :SOURCE_ARCHIVED_COPY_HASH,
                        :SOURCE_ACTIVE_COPY_HASH,
                        :SOURCE_WORKING_COPY_HASH,
    
                        :TARGET_ARCHIVED_COPY_HASH,
                        :TARGET_ACTIVE_COPY_HASH,
                        :TARGET_WORKING_COPY_HASH
                    )' 
                USING 
                    SyncHistoryId, DocumentId, ModelID, SyncObjectType, SourceInstanceGuid, 0, SyncTime, 
                    TargetInstanceCreatedById, DocumentId, ModelID, 
                    
                    SourceModelHash, 
                    TargetModelHash,
                    
                    SourceArchivedCopyHash, SourceActiveCopyHash, SourceWorkingCopyHash,
                    TargetArchivedCopyHash, TargetActiveCopyHash, TargetWorkingCopyHash
                    ;
            EXCEPTION
                WHEN OTHERS THEN
                    LogInfo('   AddSyncHistory: Insert sync history without language content hash error ! DocumentID:' || DocumentId || ' ModelID:' || ModelID);
                    RAISE;
            END;
        END IF;
        
        IF HasLanguageHash THEN
            BEGIN
                EXECUTE IMMEDIATE '
                    INSERT INTO ' || TargetInstanceSchema || '.SYNC_HISTORY_LOCALES(SYNC_HISTORY_ID, MESSAGEPOINT_LOCALE_ID)
                    SELECT DISTINCT :SyncHistoryId, L.ID
                    FROM ' || TargetInstanceSchema || '.DOCUMENT D
                    JOIN ' || TargetInstanceSchema || '.TOUCHPOINT_LANGUAGE TPL ON TPL.DOCUMENT_ID = D.ID
                    JOIN ' || TargetInstanceSchema || '.MESSAGEPOINT_LOCALE L ON L.ID = TPL.MESSAGEPOINT_LOCALE_ID
                    WHERE D.ID = :DocumentID
                '
                USING SyncHistoryId, DocumentID 
                ;
            EXCEPTION
                WHEN OTHERS THEN
                    LogInfo('   AddSyncHistory: Append sync history messagepoint locales error ! DocumentID:' || DocumentId || ' ModelID:' || ModelID);
                    RAISE;
            END;
        END IF;
    END AddSyncHistory;

    PROCEDURE ForModel(SyncObjectType NUMBER, TableName VARCHAR2, CreatedField VARCHAR2, UpdatedField VARCHAR2) IS
    BEGIN
        IF NOT ColumnExists(TargetInstanceSchema, TableName, 'SHA256_HASH') THEN
                LogInfo('    ForModel: ' || TableName || ' is not included in sync');
            RETURN;
        END IF;
        
        BEGIN
            OPEN RefCur FOR '
                SELECT 
                    SDS.ID, SDS.GUID,
                    SDS.' || CreatedField || ', TDS.' || CreatedField || ', 
                    SDS.' || UpdatedField || ', TDS.' || UpdatedField || ', 
                    SDS.SHA256_HASH, TDS.SHA256_HASH 
                FROM ' || SourceInstanceSchema || '.' || TableName || ' SDS 
                JOIN ' || TargetInstanceSchema || '.' || TableName || ' TDS ON TDS.ID = SDS.ID AND TDS.GUID = SDS.GUID';
        EXCEPTION
            WHEN OTHERS THEN
                LogInfo('    ForModel: Open cursor failed');
                RAISE;
        END;

        BEGIN
            LOOP
                EXIT WHEN NOT FetchModel(TableName);

                CONTINUE WHEN SyncHistoryExists(ModelID);
                
                IF SourceModelHash <> TargetModelHash THEN
                    LogInfo('   ! Source and target model hash was different');
                    IF SourceUpdated > InstanceCopiedTime THEN
                        SourceModelHash := 'Source Changed After ' || TO_CHAR(InstanceCopiedTime, 'YYYY-MM-DD HH24:MI:SS.FF6'); -- SUBSTR(SourceModelHash, 1, 60);
                        LogInfo('   ! Source model was changed');
                    END IF;

                    IF TargetUpdated > InstanceCopiedTime THEN
                        TargetModelHash := 'Target Changed After ' || TO_CHAR(InstanceCopiedTime, 'YYYY-MM-DD HH24:MI:SS.FF6'); -- SUBSTR(TargetModelHash, 1, 60);
                        LogInfo('   ! Target model was changed');
                    END IF;
                END IF;

                AddSyncHistory(SyncObjectType);
            END LOOP;
        EXCEPTION
            WHEN OTHERS THEN BEGIN
                LogInfo('    ForModel: ! Data Error');
                CLOSE RefCur;
                RAISE;
            END;
        END;
        
        CLOSE RefCur;
    END ForModel;

    PROCEDURE ForVersionedModel(
        SyncObjectType NUMBER, 
        ModelName VARCHAR2, 
        ModelTable VARCHAR2, 
        InstanceTable VARCHAR2, 
        VersionMapTable VARCHAR2, 
        CurQuery VARCHAR2
    ) IS 

    BEGIN
        HasLanguageHash := ColumnExists(SourceInstanceSchema, InstanceTable, 'LANGUAGE_CONTENT_HASH_ID');
    
        BEGIN
            OPEN RefCur FOR CurQuery;
        EXCEPTION
            WHEN OTHERS THEN
                LogInfo('    ForVersionedModel: Open cursor failed');
                RAISE;
        END;

        BEGIN
            LOOP
                BEGIN
                    EXIT WHEN NOT FetchModel(ModelName);
                EXCEPTION
                    WHEN OTHERS THEN
                        LogInfo('    ForVersionedModel: Fetch model error');
                        RAISE;
                END;
                
                BEGIN
                    CONTINUE WHEN SyncHistoryExists(ModelID);
                EXCEPTION
                    WHEN OTHERS THEN
                        LogInfo('    ForVersionedModel: check sync history exists error');
                        RAISE;
                END;
                
                BEGIN
                    GetVersionedInstanceHashes(SourceInstanceSchema, 
                        ModelTable, 
                        InstanceTable, 
                        VersionMapTable, 
                        ModelID, SourceUpdated,
                        SourceArchivedCopyHash,         SourceActiveCopyHash,           SourceWorkingCopyHash, 
                        SourceArchivedCopyAttrHash,     SourceActiveCopyAttrHash,       SourceWorkingCopyAttrHash, 
                        SourceArchivedCopyLanguageHash, SourceActiveCopyLanguageHash,   SourceWorkingCopyLanguageHash);
                EXCEPTION
                    WHEN OTHERS THEN
                        LogInfo('    ForVersionedModel: get source instance hashes error');
                        RAISE;
                END;
    
                BEGIN
                    GetVersionedInstanceHashes(TargetInstanceSchema, 
                        ModelTable, 
                        InstanceTable, 
                        VersionMapTable, 
                        ModelID, TargetUpdated,
                        TargetArchivedCopyHash,         TargetActiveCopyHash,           TargetWorkingCopyHash, 
                        TargetArchivedCopyAttrHash,     TargetActiveCopyAttrHash,       TargetWorkingCopyAttrHash, 
                        TargetArchivedCopyLanguageHash, TargetActiveCopyLanguageHash,   TargetWorkingCopyLanguageHash);
                EXCEPTION
                    WHEN OTHERS THEN
                        LogInfo('    ForVersionedModel: get target instance hashes error');
                        RAISE;
                END;
    
                BEGIN
                    AddSyncHistory(SyncObjectType);            
                EXCEPTION
                    WHEN OTHERS THEN
                        LogInfo('    ForVersionedModel: add sync history error');
                        RAISE;
                END;
            END LOOP;
        EXCEPTION
            WHEN OTHERS THEN BEGIN
                LogInfo('    ForVersionedModel: ! Data Error');
                CLOSE RefCur;
                RAISE;
            END;
        END;
        CLOSE RefCur;
    END ForVersionedModel;

    PROCEDURE ForSharedVersionedModel(SyncObjectType NUMBER, ModelName VARCHAR2) IS 
        ModelTable      VARCHAR2(255);
        InstanceTable   VARCHAR2(255);
        VersionMapTable VARCHAR2(255);
        CurQuery        VARCHAR2(4096);
    BEGIN
        ModelTable := ModelName;
        InstanceTable := ModelName || '_INSTANCE';
        VersionMapTable := ModelName || '_VERSION_MAP';

        CurQuery := '
            SELECT 
                SM.ID, SM.GUID, 
                SM.CREATE_TIME, TM.CREATE_TIME, 
                SM.UPDATE_TIME, TM.UPDATE_TIME, 
                SM.SHA256_HASH, TM.SHA256_HASH 
            FROM ' || SourceInstanceSchema || '.' || ModelTable || ' SM 
            JOIN ' || TargetInstanceSchema || '.' || ModelTable || ' TM ON TM.ID = SM.ID AND TM.GUID = SM.GUID AND TM.DNA = SM.DNA 
            ';

        ForVersionedModel(SyncObjectType, ModelName, ModelTable, InstanceTable, VersionMapTable, CurQuery);
    END ForSharedVersionedModel;

    PROCEDURE ForMessageModel(SyncObjectType NUMBER, IsLocal BOOLEAN, IsLocalImage BOOLEAN) IS 
        ModelName       VARCHAR2(255);
        ModelTable      VARCHAR2(255);
        InstanceTable   VARCHAR2(255);
        VersionMapTable VARCHAR2(255);

        ContentTypeCondition     VARCHAR2(32);
        CurQuery        VARCHAR2(4096);
    BEGIN
        IF IsLocal THEN
            IF IsLocalImage THEN
                ModelName := 'LOCAL IMAGE';
                ContentTypeCondition := ' = ' || CONTENTTYPE_GRAPHIC;
            ELSE
                ModelName := 'LOCAL SMART TEXT';
                ContentTypeCondition := ' <> ' || CONTENTTYPE_GRAPHIC;
            END IF;
        ELSE
            ModelName := 'MESSAGE';
        END IF;

        ModelTable := 'MESSAGE';
        InstanceTable := 'MESSAGE_INSTANCE';
        VersionMapTable := 'MESSAGE_VERSION_MAP';

        CurQuery := '
            SELECT 
                SM.ID, SM.GUID,  
                SM.CREATED, TM.CREATED,
                SM.UPDATED, TM.UPDATED, 
                SM.SHA256_HASH, TM.SHA256_HASH 
            FROM ' || SourceInstanceSchema || '.' || ModelTable || ' SM 
            JOIN ' || TargetInstanceSchema || '.' || ModelTable || ' TM ON TM.ID = SM.ID AND TM.GUID = SM.GUID AND TM.DNA = SM.DNA 
            ';

        IF IsLocal THEN
            CurQuery := CurQuery || '
                WHERE SM.IS_TOUCHPOINT_LOCAL <> 0 AND TM.IS_TOUCHPOINT_LOCAL <> 0 
                    AND SM.DOCUMENT_ID IS NOT NULL AND TM.DOCUMENT_ID IS NOT NULL 
                    AND SM.DOCUMENT_ID = TM.DOCUMENT_ID AND SM.DOCUMENT_ID = ' || DocumentID || '
                    AND EXISTS
                    (
                        SELECT *
                        FROM ' || SourceInstanceSchema || '.MESSAGE_VERSION_MAP VM
                        JOIN ' || SourceInstanceSchema || '.MESSAGE_INSTANCE INST ON INST.ID = VM.MESSAGE_INSTANCE_ID AND INST.CONTENT_TYPE_ID ' || ContentTypeCondition || '
                        WHERE VM.MESSAGE_ID = SM.ID
                    )
                    AND EXISTS
                    (
                        SELECT *
                        FROM ' || TargetInstanceSchema || '.MESSAGE_VERSION_MAP VM
                        JOIN ' || TargetInstanceSchema || '.MESSAGE_INSTANCE INST ON INST.ID = VM.MESSAGE_INSTANCE_ID AND INST.CONTENT_TYPE_ID ' || ContentTypeCondition || '
                        WHERE VM.MESSAGE_ID = TM.ID 
                    )
                ';
        ELSE
            CurQuery := CurQuery || '
                WHERE SM.IS_TOUCHPOINT_LOCAL = 0 AND TM.IS_TOUCHPOINT_LOCAL = 0 
                AND EXISTS
                (
                        SELECT *
                        FROM ' || SourceInstanceSchema || '.MESSAGE_VERSION_MAP VM
                        JOIN ' || SourceInstanceSchema || '.MESSAGE_INSTANCE INST ON INST.ID = VM.MESSAGE_INSTANCE_ID 
                        JOIN ' || SourceInstanceSchema || '.MESSAGE_ZONE MZ ON MZ.MESSAGE_INSTANCE_ID = INST.ID
                        JOIN ' || SourceInstanceSchema || '.ZONE Z ON Z.ID = MZ.ZONE_ID AND Z.DOCUMENT_ID = ' || DocumentID || '
                        WHERE VM.MESSAGE_ID = SM.ID 
                )
                AND EXISTS
                (
                        SELECT *
                        FROM ' || TargetInstanceSchema || '.MESSAGE_VERSION_MAP VM
                        JOIN ' || TargetInstanceSchema || '.MESSAGE_INSTANCE INST ON INST.ID = VM.MESSAGE_INSTANCE_ID
                        JOIN ' || TargetInstanceSchema || '.MESSAGE_ZONE MZ ON MZ.MESSAGE_INSTANCE_ID = INST.ID
                        JOIN ' || TargetInstanceSchema || '.ZONE Z ON Z.ID = MZ.ZONE_ID AND Z.DOCUMENT_ID = ' || DocumentID || '
                        WHERE VM.MESSAGE_ID = TM.ID 
                )
                ';
        END IF;

        ForVersionedModel(SyncObjectType, ModelName, ModelTable, InstanceTable, VersionMapTable, CurQuery);
    END ForMessageModel;

    PROCEDURE ForContentObjectModel(SyncObjectType NUMBER, ModelName VARCHAR2, ContentObjectType NUMBER, IsGlobalObject BOOLEAN) IS
        DocumentCondition   VARCHAR2(255);
   BEGIN
        IF IsGlobalObject THEN
            DocumentCondition := 'SM.DOCUMENT_ID IS NULL';
        ELSE
            DocumentCondition := 'SM.DOCUMENT_ID = ' || DocumentID;
        END IF;

        BEGIN
            OPEN RefCur FOR '
                SELECT 
                    SM.ID, SM.GUID, 
                    SM.CREATED, TM.CREATED,
                    SM.UPDATED, TM.UPDATED, 
                    SM.SHA256_HASH, TM.SHA256_HASH
                FROM ' || SourceInstanceSchema || '.CONTENT_OBJECT SM
                JOIN ' || TargetInstanceSchema || '.CONTENT_OBJECT TM ON TM.ID = SM.ID AND TM.GUID = SM.GUID
                WHERE SM.OBJECT_TYPE = ' || ContentObjectType || ' AND ' || DocumentCondition;
        EXCEPTION
            WHEN OTHERS THEN
                LogInfo('    ForContentObjectModel: Open cursor failed');
                RAISE;
        END;

        BEGIN
            LOOP
                EXIT WHEN NOT FetchModel(ModelName);
    
                CONTINUE WHEN SyncHistoryExists(ModelID);
                
                GetContentObjectDataHashes(SourceInstanceSchema, 
                    ModelID, SourceUpdated,
                    SourceArchivedCopyHash,         SourceActiveCopyHash,           SourceWorkingCopyHash, 
                    SourceArchivedCopyAttrHash,     SourceActiveCopyAttrHash,       SourceWorkingCopyAttrHash, 
                    SourceArchivedCopyLanguageHash, SourceActiveCopyLanguageHash,   SourceWorkingCopyLanguageHash);
    
                GetContentObjectDataHashes(TargetInstanceSchema, 
                    ModelID, TargetUpdated,
                    TargetArchivedCopyHash,         TargetActiveCopyHash,           TargetWorkingCopyHash, 
                    TargetArchivedCopyAttrHash,     TargetActiveCopyAttrHash,       TargetWorkingCopyAttrHash, 
                    TargetArchivedCopyLanguageHash, TargetActiveCopyLanguageHash,   TargetWorkingCopyLanguageHash);
    
                AddSyncHistory(SyncObjectType);
            END LOOP;
        EXCEPTION
            WHEN OTHERS THEN
                LogInfo('    ForContentObjectModel: ! Data Error');
                CLOSE RefCur;
                RAISE;
        END;

        CLOSE RefCur;        
    END ForContentObjectModel;

    PROCEDURE ForDataSources IS
    BEGIN
        LogInfo(' DataSources');

        ForModel
        (
            SyncObjectType => SYNCTYPE_DATASOURCE,
            TableName => 'DATA_SOURCE',
            CreatedField => 'UPDATED',
            UpdatedField => 'UPDATED'
        );
    END ForDataSources;
    
    PROCEDURE ForLookupTables IS
        ModelName       VARCHAR2(255);
        ModelTable      VARCHAR2(255);
        InstanceTable   VARCHAR2(255);
        VersionMapTable VARCHAR2(255);
        CurQuery        VARCHAR2(4096);
    BEGIN
        LogInfo(' LookupTables');

        IF TableExists(SourceInstanceSchema, 'LOOKUP_TABLE') 
            AND TableExists(SourceInstanceSchema, 'LOOKUP_TABLE_INSTANCE')
            AND TableExists(SourceInstanceSchema, 'LOOKUP_TABLE_VERSION_MAP')
        THEN
            IF ColumnExists(SourceInstanceSchema, 'LOOKUP_TABLE', 'SHA256_HASH') 
                AND ColumnExists(SourceInstanceSchema, 'LOOKUP_TABLE_INSTANCE', 'SHA256_HASH')
            THEN
                ModelName := 'LOOKUP_TABLE';
                ModelTable := 'LOOKUP_TABLE';
                InstanceTable := 'LOOKUP_TABLE_INSTANCE';
                VersionMapTable := 'LOOKUP_TABLE_VERSION_MAP';
    
                CurQuery := '
                    SELECT 
                        SM.ID, SM.GUID,  
                        SM.CREATED, TM.CREATED,
                        SM.UPDATED, TM.UPDATED, 
                        SM.SHA256_HASH, TM.SHA256_HASH 
                    FROM ' || SourceInstanceSchema || '.' || ModelTable || ' SM 
                    JOIN ' || TargetInstanceSchema || '.' || ModelTable || ' TM ON TM.ID = SM.ID AND TM.GUID = SM.GUID AND TM.DNA = SM.DNA 
                    ';
    
                ForVersionedModel(SYNCTYPE_LOOKUPTABLE, ModelName, ModelTable, InstanceTable, VersionMapTable, CurQuery);
            ELSE
                LogInfo('    ForLookupTables: ' || ModelName || ' is not included in sync');
            END IF;
        END IF;
    END ForLookupTables;

    PROCEDURE ForDataCollections IS
    BEGIN
        LogInfo(' DataCollections');

        ForModel
        (
            SyncObjectType => SYNCTYPE_DATACOLLECTION,
            TableName => 'DATA_SOURCE_ASSOCIATION',
            CreatedField => 'CREATED',
            UpdatedField => 'UPDATED'
        );
    END ForDataCollections;

    PROCEDURE ForVariables IS
    BEGIN
        LogInfo(' Variables');

        ForModel
        (
            SyncObjectType => SYNCTYPE_DATACOLLECTION,
            TableName => 'DATA_ELEMENT_VARIABLE',
            CreatedField => 'CREATED',
            UpdatedField => 'UPDATED'
        );
    END ForVariables;

    PROCEDURE ForParameterGroups IS
    BEGIN
        LogInfo(' SelectorGroups');

        ForModel
        (
            SyncObjectType => SYNCTYPE_PARAMETER_GROUP,
            TableName => 'PARAMETER_GROUP',
            CreatedField => 'CREATED',
            UpdatedField => 'UPDATED'
        );
    END ForParameterGroups;

    PROCEDURE ForTargetRules IS
    BEGIN
        LogInfo(' TargetRules');
        ForModel
        (
            SyncObjectType => SYNCTYPE_TARGET_RULE,
            TableName => 'CONDITION_ELEMENT',
            CreatedField => 'CREATED',
            UpdatedField => 'UPDATED'
        );
    END ForTargetRules;

    PROCEDURE ForTargetGroups IS
    BEGIN
        LogInfo(' TargetGroups');
        ForModel
        (
            SyncObjectType => SYNCTYPE_TARGET_GROUP,
            TableName => 'TARGET_GROUP',
            CreatedField => 'CREATED',
            UpdatedField => 'UPDATED'
        );
    END ForTargetGroups;

    PROCEDURE ForContentLibrarys IS
    BEGIN
        IF IsNewContentObjectModel THEN
            LogInfo(' Global images from CONTENT_OBJECT table');
            ForContentObjectModel
            (
                SyncObjectType => SYNCTYPE_CONTENT_LIBRARY,
                ModelName => 'Global Image',
                ContentObjectType => OBJECT_TYPE_GLOBAL_IMAGE,
                IsGlobalObject => TRUE
            );
        ELSIF TableExists(SourceInstanceSchema, 'CONTENT_LIBRARY') 
            AND TableExists(SourceInstanceSchema, 'CONTENT_LIBRARY_INSTANCE') 
            AND TableExists(SourceInstanceSchema, 'CONTENT_LIBRARY_VERSION_MAP') 
        THEN
            LogInfo(' Global images from CONTENT_LIBRARY table');
            ForSharedVersionedModel
            (
                SyncObjectType => SYNCTYPE_CONTENT_LIBRARY,
                ModelName => 'CONTENT_LIBRARY'
            );
        END IF;
    END ForContentLibrarys;

    PROCEDURE ForEmbeddedContents IS
    BEGIN
        IF IsNewContentObjectModel THEN
            LogInfo(' Global smartt texts from CONTENT_OBJECT table');
            ForContentObjectModel
            (
                SyncObjectType => SYNCTYPE_SMART_TEXT,
                ModelName => 'Global Smart Text',
                ContentObjectType => OBJECT_TYPE_GLOBAL_SMART_TEXT,
                IsGlobalObject => TRUE
            );
        ELSIF TableExists(SourceInstanceSchema, 'EMBEDDED_CONTENT') 
            AND TableExists(SourceInstanceSchema, 'EMBEDDED_CONTENT_INSTANCE')
            AND TableExists(SourceInstanceSchema, 'EMBEDDED_CONTENT_VERSION_MAP')
        THEN
            LogInfo(' Global smartt texts from EMBEDDED_CONTENT table');
            ForSharedVersionedModel
            (
                SyncObjectType => SYNCTYPE_SMART_TEXT,
                ModelName => 'EMBEDDED_CONTENT'
            );
        END IF;
    END ForEmbeddedContents;

    PROCEDURE ForLocalContentLibrarys IS
    BEGIN
        IF IsNewContentObjectModel THEN
            LogInfo(' Local images from CONTENT_OBJECT table');
            ForContentObjectModel
            (
                SyncObjectType => SYNCTYPE_LOCAL_IMAGE,
                ModelName => 'Local Image',
                ContentObjectType => OBJECT_TYPE_LOCAL_IMAGE,
                IsGlobalObject => FALSE
            );
        ELSIF TableExists(SourceInstanceSchema, 'MESSAGE') 
            AND TableExists(SourceInstanceSchema, 'MESSAGE_INSTANCE') 
            AND TableExists(SourceInstanceSchema, 'MESSAGE_VERSION_MAP') 
        THEN
            LogInfo(' Local images from MESSAGE table');
            ForMessageModel
            (
                SyncObjectType => SYNCTYPE_LOCAL_IMAGE,
                IsLocal => TRUE,
                IsLocalImage => TRUE
            );
        END IF;
    END ForLocalContentLibrarys;

    PROCEDURE ForLocalEmbeddedContents IS
    BEGIN
        IF IsNewContentObjectModel THEN
            LogInfo(' Local smart texts from CONTENT_OBJECT table');
            ForContentObjectModel
            (
                SyncObjectType => SYNCTYPE_LOCAL_SMART_TEXT,
                ModelName => 'Local Smart Text',
                ContentObjectType => OBJECT_TYPE_LOCAL_SMART_TEXT,
                IsGlobalObject => FALSE
            );
        ELSIF TableExists(SourceInstanceSchema, 'MESSAGE') 
            AND TableExists(SourceInstanceSchema, 'MESSAGE_INSTANCE') 
            AND TableExists(SourceInstanceSchema, 'MESSAGE_VERSION_MAP') 
        THEN
            LogInfo(' Local smart texts from MESSAGE table');
            ForMessageModel
            (
                SyncObjectType => SYNCTYPE_LOCAL_SMART_TEXT,
                IsLocal => TRUE,
                IsLocalImage => FALSE
            );
        END IF;
    END ForLocalEmbeddedContents;

    PROCEDURE ForMessages IS
    BEGIN
        IF IsNewContentObjectModel THEN
            LogInfo(' Messages from CONTENT_OBJECT table');
            ForContentObjectModel
            (
                SyncObjectType => SYNCTYPE_MESSAGE,
                ModelName => 'Message',
                ContentObjectType => OBJECT_TYPE_MESSAGE,
                IsGlobalObject => FALSE
            );
        ELSIF TableExists(SourceInstanceSchema, 'MESSAGE') 
            AND TableExists(SourceInstanceSchema, 'MESSAGE_INSTANCE') 
            AND TableExists(SourceInstanceSchema, 'MESSAGE_VERSION_MAP') 
        THEN
            LogInfo(' Messages from MESSAGE table');
            ForMessageModel
            (
                SyncObjectType => SYNCTYPE_MESSAGE,
                IsLocal => FALSE,
                IsLocalImage => FALSE
            );
        END IF;
    END ForMessages;

    PROCEDURE ForDocument IS
    BEGIN
        LogInfo('');
        LogInfo('Create sync history for document ID: ' || DocumentID || ' Name: ' || DocumentName || ' Dna: ' || DocumentDna || ' BEGIN');    
        ForDataSources;
        ForLookupTables;
        ForDataCollections;
        ForVariables;
        ForParameterGroups;
        ForTargetRules;
        ForTargetGroups;
        ForContentLibrarys;
        ForEmbeddedContents;
        ForLocalContentLibrarys;
        ForLocalEmbeddedContents;
        ForMessages;
        LogInfo('Create sync history for document ID: ' || DocumentID || ' Name: ' || DocumentName || ' END');    
    END ForDocument;

    PROCEDURE ForAllDocuments IS
        DocumentCur     RefCurType;
    BEGIN
        OPEN DocumentCur FOR 'SELECT SD.ID, SD.NAME, SD.GUID, SD.DNA FROM ' || SourceInstanceSchema || '.DOCUMENT SD JOIN ' || TargetInstanceSchema || '.DOCUMENT TD ON TD.ID = SD.ID AND TD.DNA = SD.DNA AND TD.GUID = SD.GUID';
        LOOP
            FETCH DocumentCur INTO DocumentId, DocumentName, DocumentGuid, DocumentDna;
            EXIT WHEN DocumentCur%NOTFOUND;
            ForDocument;
        END LOOP;
        CLOSE DocumentCur;
    END ForAllDocuments;

    PROCEDURE ForInstance IS
    BEGIN
        HasHideUntilNextChange := ColumnExists(TargetInstanceSchema, 'SYNC_HISTORY', 'HIDE_UNTIL_NEXT_CHANGE');

        IsNewContentObjectModel := 
            TableExists(TargetInstanceSchema, 'CONTENT_OBJECT')
            AND TableExists(TargetInstanceSchema, 'CONTENT_OBJECT_DATA');

        InstanceCopiedTime := CopiedTIme;
        
        IF InstanceCopiedTime IS NULL THEN
            InstanceCopiedTime := CURRENT_TIMESTAMP;
        END IF;
        
        SyncTime := InstanceCopiedTime;
        
        LogInfo('Find Instances information from PodMaster schema: ' || PodMaster);

        BEGIN 
            EXECUTE IMMEDIATE 'SELECT ID, GUID, CREATED FROM ' || PodMaster || '.NODE WHERE SCHEMA_NAME = :InstanceSchema' INTO SourceInstanceId, SourceInstanceGuid, SourceInstanceCreated USING SourceInstanceSchema;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                LogInfo('Source node not found');
                RAISE;
            WHEN OTHERS THEN
                LogInfo('Unknown error');
                RAISE;
        END;

        LogInfo('Source Instance ID: ' || SourceInstanceId || ' Source Instance Guid: ' || SourceInstanceGuid || ' Created: ' || SourceInstanceCreated);

        BEGIN
            EXECUTE IMMEDIATE 'SELECT ID, GUID, CREATED_BY_ID FROM ' || PodMaster || '.NODE WHERE SCHEMA_NAME = :InstanceSchema' INTO TargetInstanceId, TargetInstanceGuid, TargetInstanceCreatedById USING TargetInstanceSchema;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                LogInfo('Target node not found');
                RAISE;
            WHEN OTHERS THEN
                LogInfo('Unknown error');
                RAISE;
        END;

        LogInfo('Target Instance ID: ' || TargetInstanceId || ' Target Instance Guid: ' || TargetInstanceGuid || ' Created: ' || InstanceCopiedTime);

        ForAllDocuments;

        LogInfo('Committing changes ...');
        COMMIT;
        LogInfo('Changes committed');
        
    EXCEPTION
        WHEN OTHERS THEN BEGIN
            LogInfo('CreateSyncHistory Failed');
            RAISE;
        END;
    END ForInstance;
BEGIN
    DBMS_OUTPUT.ENABLE(NULL);

    BEGIN
        LogOutputFile := UTL_FILE.FOPEN(location => 'MSGPOINT', filename => 'CreateSyncHistory.log', open_mode => 'a');
    EXCEPTION
        WHEN UTL_FILE.INVALID_PATH OR UTL_FILE.INVALID_OPERATION THEN BEGIN
            LogOutputFile := UTL_FILE.FOPEN(location => 'DATA_PUMP_DIR', filename => 'CreateSyncHistory.log', open_mode => 'a');
        END;
    END;

    LogInfo('CreateSyncHistory From ' || SourceInstanceSchema || ' To ' || TargetInstanceSchema || ' Begin');

    ForInstance;

    LogInfo('CreateSyncHistory From ' || SourceInstanceSchema || ' To ' || TargetInstanceSchema || ' End');

    UTL_FILE.FCLOSE(LogOutputFile);
END CreateSyncHistory;


