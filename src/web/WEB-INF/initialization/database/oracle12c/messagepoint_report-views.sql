drop view v_job_statistics;
drop view v_job_summary;
drop view v_message_tp_tracking_monthly;
drop view v_message_tp_tracking_by_job;

create view v_job_statistics as select job_id, part_id, transaction_id, content_object_id, sum(qualified) as qualified, sum(delivered) as delivered, sum(total) as total from job_statistics_variant group by job_id, part_id, transaction_id, content_object_id;
create view v_job_summary as select job_id, job_type, document_id, year, month, SUM(recipients) as recipients from batch_statistics_summary group by job_id, job_type, document_id, year, month;
create view v_message_tp_tracking_monthly as select document_id as touchpoint_id, year, month, customer_id, content_object_id, count(content_object_id) as qualified, played as delivered from report inner join v_job_summary on report.job_id = v_job_summary.job_id group by document_id, month, year, customer_id, content_object_id, played, job_type having content_object_id > 0 and job_type = 4;
create view v_message_tp_tracking_by_job as select report.job_id, document_id as touchpoint_id, year, month, customer_id, content_object_id, count(content_object_id) as qualified, played as delivered from report inner join v_job_summary on report.job_id = v_job_summary.job_id group by report.job_id, document_id, year, month, customer_id, content_object_id, job_type, played having content_object_id > 0 and job_type = 4;
