drop table if exists aggregation_operator cascade;
drop table if exists all_reference_query cascade;
drop table if exists app_version cascade;
drop table if exists background_themes cascade;
drop table if exists barcode_type cascade;
drop table if exists category_group cascade;
drop table if exists condition_operator cascade;
drop table if exists condition_type cascade;
drop table if exists content_type cascade;
drop table if exists daily_frequency_type cascade;
drop table if exists data_comparison cascade;
drop table if exists data_record_level cascade;
drop table if exists data_subtype cascade;
drop table if exists data_subtype_comparison cascade;
drop table if exists data_type cascade;
drop table if exists data_type_subtype cascade;
drop table if exists date_data_value cascade;
drop table if exists deactivate_types cascade;
drop table if exists delivery_event_type cascade;
drop table if exists deserver_communicationtype cascade;
drop table if exists dialogue_data_mapping cascade;
drop table if exists encoding_type cascade;
drop table if exists event_type cascade;
drop table if exists frequency_type cascade;
drop table if exists item_type cascade;
drop table if exists layout_type cascade;
drop table if exists mp_channel cascade;
drop table if exists mp_connector cascade;
drop table if exists mp_qualification_output cascade;
drop table if exists navigation_drop_down_menu cascade;
drop table if exists navigation_drop_down_menu_item cascade;
drop table if exists navigation_menu_item_perm cascade;
drop table if exists navigation_tab_default_map cascade;
drop table if exists navigation_tab_permission cascade;
drop table if exists navigation_tabs cascade;
drop table if exists navigation_tree cascade;
drop table if exists old_workflow cascade;
drop table if exists permission cascade;
drop table if exists permission_category cascade;
drop table if exists pod_status cascade;
drop table if exists pod_type cascade;
drop table if exists record_type cascade;
drop table if exists reference_query cascade;
drop table if exists source_type cascade;
drop table if exists sub_content_type cascade;
drop table if exists system_state cascade;
drop table if exists system_theme cascade;
drop table if exists tag_cloud_type cascade;
drop table if exists translation_provider cascade;
drop table if exists version_activity_reason cascade;
drop table if exists version_status cascade;
drop table if exists workflow_position cascade;
drop table if exists workflow_position_permission cascade;
drop table if exists workflow_property cascade;
drop table if exists workflow_state cascade;
drop table if exists workflow_tab cascade;
drop table if exists workflow_tab_permission cascade;
create table aggregation_operator (id int4 not null, operator varchar(255) not null, primary key (id));
create table all_reference_query (id int8 not null, object_class_name varchar(255) not null, direct_ref_class_name varchar(255), f_query varchar(4000), f_type varchar(8), primary key (id));
create table app_version (id int8 not null, version_key varchar(255), version_value varchar(255), updated timestamp, updated_by_id int8, created timestamp, created_by_id int8, primary key (id));
create table background_themes (id int8 not null, name varchar(96) not null, filename varchar(255) not null, primary key (id));
create table barcode_type (id int8 not null, name varchar(96), description varchar(255), sample_image varchar(255), enabled boolean, type int4, primary key (id));
create table category_group (category_group_id int8 not null, name varchar(96), description varchar(255), priority int4 not null, primary key (category_group_id));
create table condition_operator (id int4 not null, operator varchar(255) not null, primary key (id));
create table condition_type (id int4 not null, name varchar(96), parameterized boolean default true, description varchar(255), primary key (id));
create table content_type (id int8 not null, name varchar(96), description varchar(255), primary key (id));
create table daily_frequency_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table data_comparison (id int8 not null, name varchar(96), description varchar(255), scheme varchar(255), primary key (id));
create table data_record_level (id int4 not null, name varchar(96), f_value int4, primary key (id));
create table data_subtype (id int4 not null, name varchar(96), description varchar(255), selectable boolean, primary key (id));
create table data_subtype_comparison (data_subtype_id int4 not null, data_comparison_id int8 not null, primary key (data_subtype_id, data_comparison_id));
create table data_type (id int4 not null, name varchar(96), description varchar(255), selectable boolean, primary key (id));
create table data_type_subtype (data_type_id int4 not null, data_subtype_id int4 not null, primary key (data_type_id, data_subtype_id));
create table date_data_value (id int8 not null, name varchar(96), description varchar(255), primary key (id));
create table deactivate_types (id int8 not null, name varchar(96), primary key (id));
create table delivery_event_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table deserver_communicationtype (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table dialogue_data_mapping (id int8 not null, dialogue_data_type varchar(255), data_type_id int4, primary key (id));
create table encoding_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table event_type (id int8 not null, name varchar(96), description varchar(255), primary key (id));
create table frequency_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table item_type (id int8 not null, name varchar(96), description varchar(255), primary key (id));
create table layout_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table mp_channel (id int8 not null, name varchar(96), presentation_name varchar(96), created timestamp, updated timestamp, primary key (id));
create table mp_connector (id int8 not null, name varchar(96), presentation_name varchar(96), created timestamp, updated timestamp, channel_id int8, primary key (id));
create table mp_qualification_output (id int8 not null, name varchar(96), created timestamp, updated timestamp, primary key (id));
create table navigation_drop_down_menu (id int8 not null, name varchar(255) not null, ordering int4 not null, tab_id int8, string_id varchar(255), requires varchar(255), primary key (id));
create table navigation_drop_down_menu_item (id int8 not null, name varchar(255), type varchar(2), icon varchar(255), ordering int4 not null, menu_id int8 not null, tree_id int8, string_id varchar(255), url varchar(255), requires varchar(255), store_in_session boolean, authorization_type varchar(2) not null, primary key (id));
create table navigation_menu_item_perm (navigation_menu_item_id int8 not null, permission_id int8 not null, primary key (navigation_menu_item_id, permission_id));
create table navigation_tab_default_map (tab_id int8 not null, type varchar(255) not null, item_id int8 not null, primary key (tab_id, type));
create table navigation_tab_permission (navigation_tab_id int8 not null, permission_id int8 not null, primary key (navigation_tab_id, permission_id));
create table navigation_tabs (id int8 not null, ordering int4 not null, name varchar(255), string_id varchar(255), url varchar(255), default_menu_item_id int8, authorization_type varchar(2) not null, primary key (id));
create table navigation_tree (id int8 not null, f_query varchar(510), parent_id int8, parameter varchar(255), url varchar(255), icon varchar(255), type int4, sequence int4, primary key (id));
create table old_workflow (id int8 not null, name varchar(255) not null, class_name varchar(255) not null, primary key (id));
create table permission (id int8 not null, name varchar(255), description varchar(255), type varchar(2) not null, updated timestamp, updated_by_id int8, category_id int8, created timestamp, primary key (id));
create table permission_category (id int8 not null, name varchar(255), description varchar(255), priority int4 not null, category_group_id int8, primary key (id));
create table pod_status (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table pod_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table record_type (id int4 not null, name varchar(96), string_id varchar(255), primary key (id));
create table reference_query (id int8 not null, object_class_name varchar(255) not null, direct_ref_class_name varchar(255), f_query varchar(4000), f_type varchar(8), primary key (id));
create table source_type (id int4 not null, name varchar(96), description varchar(255), primary key (id));
create table sub_content_type (id int8 not null, name varchar(96), description varchar(255), parent_content_id int8, primary key (id));
create table system_state (id int8 not null, name varchar(96), string_code varchar(255), description varchar(255), bit_flag int8, primary key (id));
create table system_theme (id int8 not null, name varchar(96), folder varchar(255), uploaded boolean, primary key (id));
create table tag_cloud_type (id int4 not null, name varchar(150), primary key (id));
create table translation_provider (id int8 not null, name varchar(255), code varchar(255), name_code varchar(255), primary key (id));
create table version_activity_reason (id int8 not null, name varchar(96), description varchar(255), string_code varchar(255), primary key (id));
create table version_status (id int8 not null, name varchar(96), string_code varchar(255), description varchar(255), primary key (id));
create table workflow_position (id int8 not null, workflow_id int8 not null, workflow_state_id int8 not null, required boolean default true, requires_permission boolean default false, enabled boolean, name varchar(255) not null, authorization_type varchar(2) not null, previous int8, next int8, primary key (id));
create table workflow_position_permission (workflow_position_id int8 not null, permission_id int8 not null, primary key (workflow_position_id, permission_id));
create table workflow_property (id int8 not null, workflow_position_id int8 not null, required boolean default true, name varchar(255) not null, primary key (id));
create table workflow_state (id int8 not null, name varchar(96), string_id varchar(255), primary key (id));
create table workflow_tab (id int8 not null, workflow_position_id int8 not null, name varchar(255) not null, parameter varchar(255) not null, string_id varchar(255), authorization_type varchar(2) not null, edit_url varchar(255), view_url varchar(255), list_url varchar(255), visibility_toggle_attr varchar(255), required_id int8, primary key (id));
create table workflow_tab_permission (workflow_tab_id int8 not null, permission_id int8 not null, primary key (workflow_tab_id, permission_id));
alter table data_subtype_comparison add constraint fk_data_subtype_comparison_data_comparison_id foreign key (data_comparison_id) references data_comparison on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_subtype_comparison add constraint fk_data_subtype_comparison_data_subtype_id foreign key (data_subtype_id) references data_subtype on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_type_subtype add constraint fk_data_type_subtype_data_subtype_id foreign key (data_subtype_id) references data_subtype on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_type_subtype add constraint fk_data_type_subtype_data_type_id foreign key (data_type_id) references data_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dialogue_data_mapping add constraint fk_dialogue_data_mapping_data_type_id foreign key (data_type_id) references data_subtype on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table mp_connector add constraint fk_mp_connector_channel_id foreign key (channel_id) references mp_channel on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_drop_down_menu add constraint fk_navigation_drop_down_menu_tab_id foreign key (tab_id) references navigation_tabs on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_drop_down_menu_item add constraint fk_navigation_drop_down_menu_item_menu_id foreign key (menu_id) references navigation_drop_down_menu on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_drop_down_menu_item add constraint fk_navigation_drop_down_menu_item_tree_id foreign key (tree_id) references navigation_tree on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_menu_item_perm add constraint fk_navigation_menu_item_perm_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_menu_item_perm add constraint fk_navigation_menu_item_perm_navigation_menu_item_id foreign key (navigation_menu_item_id) references navigation_drop_down_menu_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tab_default_map add constraint fk_navigation_tab_default_map_item_id foreign key (item_id) references navigation_drop_down_menu_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tab_default_map add constraint fk_navigation_tab_default_map_tab_id foreign key (tab_id) references navigation_tabs on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tab_permission add constraint fk_navigation_tab_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tab_permission add constraint fk_navigation_tab_permission_navigation_tab_id foreign key (navigation_tab_id) references navigation_tabs on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tabs add constraint fk_navigation_tabs_default_menu_item_id foreign key (default_menu_item_id) references navigation_drop_down_menu_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tree add constraint fk_navigation_tree_parent_id foreign key (parent_id) references navigation_tree on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table permission add constraint fk_permission_category_id foreign key (category_id) references permission_category on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table permission_category add constraint fk_permission_category_category_group_id foreign key (category_group_id) references category_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sub_content_type add constraint fk_sub_content_type_parent_content_id foreign key (parent_content_id) references content_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position add constraint fk_workflow_position_workflow_id foreign key (workflow_id) references old_workflow on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position add constraint fk_workflow_position_workflow_state_id foreign key (workflow_state_id) references workflow_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position add constraint fk_workflow_position_previous foreign key (previous) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position add constraint fk_workflow_position_next foreign key (next) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position_permission add constraint fk_workflow_position_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position_permission add constraint fk_workflow_position_permission_workflow_position_id foreign key (workflow_position_id) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_property add constraint fk_workflow_property_workflow_position_id foreign key (workflow_position_id) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_tab add constraint fk_workflow_tab_workflow_position_id foreign key (workflow_position_id) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_tab add constraint fk_workflow_tab_required_id foreign key (required_id) references workflow_tab on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_tab_permission add constraint fk_workflow_tab_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_tab_permission add constraint fk_workflow_tab_permission_workflow_tab_id foreign key (workflow_tab_id) references workflow_tab on delete NO ACTION on update NO ACTION deferrable initially deferred;
