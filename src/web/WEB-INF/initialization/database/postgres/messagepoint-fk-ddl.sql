alter table action_entry add constraint fk_action_entry_text_style_from_id foreign key (text_style_from_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table action_entry add constraint fk_action_entry_text_style_to_id foreign key (text_style_to_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table action_entry add constraint fk_action_entry_tstp_id foreign key (tstp_id) references text_style_transformation_profile on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table application_locale add constraint fk_application_locale_mp_locale_id foreign key (mp_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table approval add constraint fk_approval_state_id foreign key (state_id) references workflow_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table approval_detail add constraint fk_approval_detail_wf_action_id foreign key (wf_action_id) references workflow_action on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table approval_user add constraint fk_approval_user_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table approval_user add constraint fk_approval_user_wf_step_id foreign key (wf_step_id) references workflow_step on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attachment add constraint fk_attachment_recipient_name_value_id foreign key (recipient_name_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attachment add constraint fk_attachment_recipient_location_value_id foreign key (recipient_location_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attachment_document add constraint fk_attachment_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attachment_document add constraint fk_attachment_document_attachment_id foreign key (attachment_id) references attachment on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attachment_tg_instance_map add constraint fk_attachment_tg_instance_map_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attachment_tg_instance_map add constraint fk_attachment_tg_instance_map_tg_id foreign key (tg_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attchmnt_target_group_excluded add constraint fk_attchmnt_target_group_excluded_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attchmnt_target_group_excluded add constraint fk_attchmnt_target_group_excluded_attachment_id foreign key (attachment_id) references attachment on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attchmnt_target_group_extended add constraint fk_attchmnt_target_group_extended_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attchmnt_target_group_extended add constraint fk_attchmnt_target_group_extended_attachment_id foreign key (attachment_id) references attachment on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attchmnt_target_group_included add constraint fk_attchmnt_target_group_included_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table attchmnt_target_group_included add constraint fk_attchmnt_target_group_included_attachment_id foreign key (attachment_id) references attachment on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table audit_event_doc add constraint fk_audit_event_doc_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table audit_event_doc add constraint fk_audit_event_doc_audit_event_id foreign key (audit_event_id) references audit_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table branch add constraint fk_branch_parent_id foreign key (parent_id) references branch on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clickatell_configuration add constraint fk_clickatell_configuration_customer_phone_number_var_id foreign key (customer_phone_number_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clickatell_configuration add constraint fk_clickatell_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clickatell_configuration add constraint fk_clickatell_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clickatell_configuration add constraint fk_clickatell_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clickatell_configuration add constraint fk_clickatell_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clipboard_content add constraint fk_clipboard_content_workgroup_id foreign key (workgroup_id) references workgroup on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table clipboard_content add constraint fk_clipboard_content_content_id foreign key (content_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table columnar_indicators add constraint fk_columnar_indicators_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_ext_proof_validation_map add constraint fk_comm_ext_proof_validation_map_ext_proof_validation_id foreign key (ext_proof_validation_id) references external_proof_validation on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_ext_proof_validation_map add constraint fk_comm_ext_proof_validation_map_communication_id foreign key (communication_id) references communication on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_ext_proof_validation_map add constraint fk_comm_ext_proof_validation_map_communication_proof_id foreign key (communication_proof_id) references communication_proof on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_mini_prod_event_communications add constraint fk_comm_mini_prod_event_communications_communication_id foreign key (communication_id) references communication on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_mini_prod_event_communications add constraint fk_comm_mini_prod_event_communications_comm_prod_event_id foreign key (comm_prod_event_id) references communication_mini_prod_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_prod_event_communications add constraint fk_comm_prod_event_communications_communication_id foreign key (communication_id) references communication on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_prod_event_communications add constraint fk_comm_prod_event_communications_comm_prod_event_id foreign key (comm_prod_event_id) references communication_production_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_zone_content_association add constraint fk_comm_zone_content_association_communication_id foreign key (communication_id) references communication on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table comm_zone_content_association add constraint fk_comm_zone_content_association_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table communication add constraint fk_communication_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_content_object add constraint fk_complex_value_content_object_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_content_object add constraint fk_complex_value_content_object_complex_value_id foreign key (complex_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_data_elements add constraint fk_complex_value_data_elements_data_element_id foreign key (data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_data_elements add constraint fk_complex_value_data_elements_complex_value_id foreign key (complex_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_lookup_tables add constraint fk_complex_value_lookup_tables_lookup_table_id foreign key (lookup_table_id) references lookup_table on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_lookup_tables add constraint fk_complex_value_lookup_tables_complex_value_id foreign key (complex_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_variable add constraint fk_complex_value_variable_variable_id foreign key (variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table complex_value_variable add constraint fk_complex_value_variable_complex_value_id foreign key (complex_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table composition_additional_files add constraint fk_composition_additional_files_database_file_id foreign key (database_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table composition_additional_files add constraint fk_composition_additional_files_composition_file_set_id foreign key (composition_file_set_id) references composition_file_set on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table composition_file_set add constraint fk_composition_file_set_tp_collection_id foreign key (tp_collection_id) references touchpoint_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table compound_key_item add constraint fk_compound_key_item_compound_key_id foreign key (compound_key_id) references compound_key on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table compound_key_item add constraint fk_compound_key_item_variable_id foreign key (variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_item add constraint fk_condition_item_target_group_id foreign key (target_group_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_item_attrib add constraint fk_condition_item_attrib_civ_id foreign key (civ_id) references condition_item_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_item_value add constraint fk_condition_item_value_condition_item_id foreign key (condition_item_id) references condition_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_item_value add constraint fk_condition_item_value_condition_subelement_id foreign key (condition_subelement_id) references condition_subelement on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_param_map add constraint fk_condition_param_map_tgi_id foreign key (tgi_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_param_map add constraint fk_condition_param_map_condition_item_id foreign key (condition_item_id) references condition_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_sub_attrib add constraint fk_condition_sub_attrib_cs_id foreign key (cs_id) references condition_subelement on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_subelement add constraint fk_condition_subelement_condition_element_id foreign key (condition_element_id) references condition_element on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_subelement add constraint fk_condition_subelement_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table condition_subelement add constraint fk_condition_subelement_condition_operator_id foreign key (condition_operator_id) references condition_operator on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table connected_tp_sel_visible_user add constraint fk_connected_tp_sel_visible_user_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table connected_tp_sel_visible_user add constraint fk_connected_tp_sel_visible_user_tp_selection_id foreign key (tp_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table connection_resource add constraint fk_connection_resource_data_resource_id foreign key (data_resource_id) references data_resource on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table connection_resource add constraint fk_connection_resource_reference_data_file_id foreign key (reference_data_file_id) references data_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table connection_resource add constraint fk_connection_resource_reference_connection_id foreign key (reference_connection_id) references reference_connection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content add constraint fk_content_image_link foreign key (image_link) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content add constraint fk_content_image_alt_text foreign key (image_alt_text) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content add constraint fk_content_image_ext_link foreign key (image_ext_link) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content add constraint fk_content_image_ext_path foreign key (image_ext_path) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_assistant_target_locales add constraint fk_content_assistant_target_locales_locale_id foreign key (locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_assistant_target_locales add constraint fk_content_assistant_target_locales_content_assistant_id foreign key (content_assistant_id) references content_assistant on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_assistant_target_tps add constraint fk_content_assistant_target_tps_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_assistant_target_tps add constraint fk_content_assistant_target_tps_content_assistant_id foreign key (content_assistant_id) references content_assistant on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_content_object_type add constraint fk_content_content_object_type_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_content_object_type add constraint fk_content_content_object_type_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_content_targeting add constraint fk_content_content_targeting_content_targeting_id foreign key (content_targeting_id) references content_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_content_targeting add constraint fk_content_content_targeting_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_images add constraint fk_content_images_database_file_id foreign key (database_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_images add constraint fk_content_images_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_list_style add constraint fk_content_list_style_list_style_id foreign key (list_style_id) references list_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_list_style add constraint fk_content_list_style_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object add constraint fk_content_object_content_type_id foreign key (content_type_id) references content_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object add constraint fk_content_object_tp_selection_id foreign key (tp_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object add constraint fk_content_object_global_parent_object_id foreign key (global_parent_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object add constraint fk_content_object_state_id foreign key (state_id) references workflow_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object add constraint fk_content_object_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_co_pg_tn_id foreign key (co_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_ref_co_pg_tn_id foreign key (ref_co_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_tp_pg_tn_id foreign key (tp_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_ref_tp_pg_tn_id foreign key (ref_tp_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_zone_part_id foreign key (zone_part_id) references zone_part on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_ref_messagepoint_locale_id foreign key (ref_messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_ref_image_library_id foreign key (ref_image_library_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_association add constraint fk_content_object_association_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_comment add constraint fk_content_object_comment_content_object_id_data_type foreign key (content_object_id, data_type) references content_object_data on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_data add constraint fk_content_object_data_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_data add constraint fk_content_object_data_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_data add constraint fk_content_object_data_metadata_form_id foreign key (metadata_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_document_map add constraint fk_content_object_document_map_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_document_map add constraint fk_content_object_document_map_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_excluded_map add constraint fk_content_object_tg_excluded_map_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_excluded_map add constraint fk_content_object_tg_excluded_map_content_object_id_data_type foreign key (content_object_id, data_type) references content_object_data on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_extended_map add constraint fk_content_object_tg_extended_map_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_extended_map add constraint fk_content_object_tg_extended_map_content_object_id_data_type foreign key (content_object_id, data_type) references content_object_data on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_included_map add constraint fk_content_object_tg_included_map_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_included_map add constraint fk_content_object_tg_included_map_content_object_id_data_type foreign key (content_object_id, data_type) references content_object_data on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_instance_map add constraint fk_content_object_tg_instance_map_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tg_instance_map add constraint fk_content_object_tg_instance_map_tg_id foreign key (tg_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tpc_map add constraint fk_content_object_tpc_map_tp_collection_id foreign key (tp_collection_id) references touchpoint_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_object_tpc_map add constraint fk_content_object_tpc_map_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_paragraph_style add constraint fk_content_paragraph_style_paragraph_style_id foreign key (paragraph_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_paragraph_style add constraint fk_content_paragraph_style_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_placeholders add constraint fk_content_placeholders_placeholder_id foreign key (placeholder_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_placeholders add constraint fk_content_placeholders_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_target_group_excluded add constraint fk_content_target_group_excluded_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_target_group_excluded add constraint fk_content_target_group_excluded_content_targeting_id foreign key (content_targeting_id) references content_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_target_group_extended add constraint fk_content_target_group_extended_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_target_group_extended add constraint fk_content_target_group_extended_content_targeting_id foreign key (content_targeting_id) references content_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_target_group_included add constraint fk_content_target_group_included_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_target_group_included add constraint fk_content_target_group_included_content_targeting_id foreign key (content_targeting_id) references content_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_text_style add constraint fk_content_text_style_style_id foreign key (style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_text_style add constraint fk_content_text_style_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_tg_instance_map add constraint fk_content_tg_instance_map_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_tg_instance_map add constraint fk_content_tg_instance_map_tg_id foreign key (tg_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_variable add constraint fk_content_variable_variable_id foreign key (variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table content_variable add constraint fk_content_variable_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_core add constraint fk_data_element_core_xml_data_tag_definition_id foreign key (xml_data_tag_definition_id) references xml_data_tag_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_core add constraint fk_data_element_core_data_record_id foreign key (data_record_id) references data_record on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_flat add constraint fk_data_element_flat_data_element_id foreign key (data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_json add constraint fk_data_element_json_data_element_id foreign key (data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_variable add constraint fk_data_element_variable_parameter_id foreign key (parameter_id) references parameter on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_variable add constraint fk_data_element_variable_expression foreign key (expression) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_variable add constraint fk_data_element_variable_script foreign key (script) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_variable add constraint fk_data_element_variable_non_driver_agg_op_id foreign key (non_driver_agg_op_id) references aggregation_operator on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_variable add constraint fk_data_element_variable_non_driver_data_group_id foreign key (non_driver_data_group_id) references data_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_element_xml add constraint fk_data_element_xml_data_element_id foreign key (data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_file add constraint fk_data_file_source_type_id foreign key (source_type_id) references source_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_file_document add constraint fk_data_file_document_data_file_id foreign key (data_file_id) references data_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_file_document add constraint fk_data_file_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_file_preview_languages add constraint fk_data_file_preview_languages_data_file_id foreign key (data_file_id) references data_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_file_tpc add constraint fk_data_file_tpc_tp_collection_id foreign key (tp_collection_id) references touchpoint_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_file_tpc add constraint fk_data_file_tpc_data_file_id foreign key (data_file_id) references data_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_group add constraint fk_data_group_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_record add constraint fk_data_record_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_record add constraint fk_data_record_parent_data_record_id foreign key (parent_data_record_id) references data_record on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_record add constraint fk_data_record_data_group_id foreign key (data_group_id) references data_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_resource add constraint fk_data_resource_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_resource add constraint fk_data_resource_primary_data_file_id foreign key (primary_data_file_id) references data_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_source add constraint fk_data_source_encoding_type_id foreign key (encoding_type_id) references encoding_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_source add constraint fk_data_source_layout_type_id foreign key (layout_type_id) references layout_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_source add constraint fk_data_source_source_type_id foreign key (source_type_id) references source_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_source add constraint fk_data_source_record_type_id foreign key (record_type_id) references record_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_source_association add constraint fk_data_source_association_primary_data_source_id foreign key (primary_data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_source_association add constraint fk_data_source_association_customer_data_element_id foreign key (customer_data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_subtype_comparison add constraint fk_data_subtype_comparison_data_comparison_id foreign key (data_comparison_id) references data_comparison on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_subtype_comparison add constraint fk_data_subtype_comparison_data_subtype_id foreign key (data_subtype_id) references data_subtype on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_type_subtype add constraint fk_data_type_subtype_data_subtype_id foreign key (data_subtype_id) references data_subtype on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table data_type_subtype add constraint fk_data_type_subtype_data_type_id foreign key (data_type_id) references data_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table de_state_transition add constraint fk_de_state_transition_delivery_event_id foreign key (delivery_event_id) references delivery_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table de_state_transition add constraint fk_de_state_transition_system_state_id foreign key (system_state_id) references system_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table delimited_indicators add constraint fk_delimited_indicators_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table delivery_event add constraint fk_delivery_event_job_id foreign key (job_id) references job on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table delivery_event_metadata add constraint fk_delivery_event_metadata_delivery_event_id foreign key (delivery_event_id) references delivery_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table delivery_event_schedule add constraint fk_delivery_event_schedule_frequency_type_id foreign key (frequency_type_id) references frequency_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table delivery_event_schedule add constraint fk_delivery_event_schedule_daily_frequency_type_id foreign key (daily_frequency_type_id) references daily_frequency_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table deserver add constraint fk_deserver_communication_type foreign key (communication_type) references deserver_communicationtype on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table deserver add constraint fk_deserver_private_sshkey_file_id foreign key (private_sshkey_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table deserver add constraint fk_deserver_public_sshkey_file_id foreign key (public_sshkey_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table deserver add constraint fk_deserver_bundle_state_id foreign key (bundle_state_id) references deserver_bundle_type_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dev_de_map add constraint fk_dev_de_map_de_id foreign key (de_id) references variable_data_element_map on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dev_de_map add constraint fk_dev_de_map_dev_id foreign key (dev_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dev_ds_hash_map add constraint fk_dev_ds_hash_map_dev_id foreign key (dev_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dialogue_configuration add constraint fk_dialogue_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dialogue_configuration add constraint fk_dialogue_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dialogue_configuration add constraint fk_dialogue_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dialogue_configuration add constraint fk_dialogue_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dialogue_data_mapping add constraint fk_dialogue_data_mapping_data_type_id foreign key (data_type_id) references data_subtype on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table dialogue_metadata_tags add constraint fk_dialogue_metadata_tags_config_id foreign key (config_id) references dialogue_configuration on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_communications_dataresource_id foreign key (communications_dataresource_id) references data_resource on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_communication_marker_style_id foreign key (communication_marker_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_comm_comp_result_webserv_id foreign key (comm_comp_result_webserv_id) references web_service_config on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_comm_prod_status_webserv_id foreign key (comm_prod_status_webserv_id) references web_service_config on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_comm_notification_webserv_id foreign key (comm_notification_webserv_id) references web_service_config on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_comm_data_feed_webserv_id foreign key (comm_data_feed_webserv_id) references web_service_config on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_comm_multi_recipient_id foreign key (comm_multi_recipient_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_touchpoint_metadata_def_id foreign key (touchpoint_metadata_def_id) references metadata_form_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_variant_metadata_def_id foreign key (variant_metadata_def_id) references metadata_form_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_brand_profile_id foreign key (brand_profile_id) references brand_profile on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document add constraint fk_document_data_source_association_id foreign key (data_source_association_id) references data_source_association on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_history add constraint fk_document_history_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_preview add constraint fk_document_preview_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_preview add constraint fk_document_preview_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_preview add constraint fk_document_preview_workgroup_id foreign key (workgroup_id) references workgroup on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_preview add constraint fk_document_preview_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_section add constraint fk_document_section_parent_document_section_id foreign key (parent_document_section_id) references document_section on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_section add constraint fk_document_section_original_image_file_id foreign key (original_image_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_section add constraint fk_document_section_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table document_tenant_email_address add constraint fk_document_tenant_email_address_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table domain_pod add constraint fk_domain_pod_domain foreign key (domain) references domain on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table domain_pod add constraint fk_domain_pod_pod foreign key (pod) references pod on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table emessaging_configuration add constraint fk_emessaging_configuration_customer_email_address_var_id foreign key (customer_email_address_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table emessaging_configuration add constraint fk_emessaging_configuration_customer_phone_number_var_id foreign key (customer_phone_number_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table emessaging_configuration add constraint fk_emessaging_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table emessaging_configuration add constraint fk_emessaging_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table emessaging_configuration add constraint fk_emessaging_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table emessaging_configuration add constraint fk_emessaging_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table exacttarget_configuration add constraint fk_exacttarget_configuration_customer_email_address_var_id foreign key (customer_email_address_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table exacttarget_configuration add constraint fk_exacttarget_configuration_customer_key_var_id foreign key (customer_key_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table exacttarget_configuration add constraint fk_exacttarget_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table exacttarget_configuration add constraint fk_exacttarget_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table exacttarget_configuration add constraint fk_exacttarget_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table exacttarget_configuration add constraint fk_exacttarget_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ext_reporting_vars_doc add constraint fk_ext_reporting_vars_doc_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ext_reporting_vars_doc add constraint fk_ext_reporting_vars_doc_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table filter_condition add constraint fk_filter_condition_id foreign key (id) references condition_subelement on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table filter_condition add constraint fk_filter_condition_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table filter_condition_value_map add constraint fk_filter_condition_value_map_filter_condition_id foreign key (filter_condition_id) references filter_condition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table folder_insert add constraint fk_folder_insert_folder_id foreign key (folder_id) references folders on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table folder_insert add constraint fk_folder_insert_insert_id foreign key (insert_id) references insert_obj on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table folders add constraint fk_folders_parent_id foreign key (parent_id) references folders on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table formatting_selection add constraint fk_formatting_selection_transform_profile_id foreign key (transform_profile_id) references text_style_transformation_profile on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table formatting_selection add constraint fk_formatting_selection_alternate_document_id foreign key (alternate_document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table formatting_selection add constraint fk_formatting_selection_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ftp_configuration add constraint fk_ftp_configuration_recipfile_cplex_val_id foreign key (recipfile_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ftp_configuration add constraint fk_ftp_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ftp_configuration add constraint fk_ftp_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ftp_configuration add constraint fk_ftp_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table ftp_configuration add constraint fk_ftp_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table generic_configuration add constraint fk_generic_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table generic_configuration add constraint fk_generic_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table generic_configuration add constraint fk_generic_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table generic_configuration add constraint fk_generic_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table gmc_configuration add constraint fk_gmc_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table gmc_configuration add constraint fk_gmc_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table gmc_configuration add constraint fk_gmc_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table gmc_configuration add constraint fk_gmc_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content add constraint fk_hist_content_image_link foreign key (image_link) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content add constraint fk_hist_content_image_alt_text foreign key (image_alt_text) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content add constraint fk_hist_content_image_ext_link foreign key (image_ext_link) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content add constraint fk_hist_content_image_ext_path foreign key (image_ext_path) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_content_object_t add constraint fk_hist_content_content_object_t_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_content_object_t add constraint fk_hist_content_content_object_t_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_content_targeting add constraint fk_hist_content_content_targeting_content_targeting_id foreign key (content_targeting_id) references content_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_content_targeting add constraint fk_hist_content_content_targeting_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_images add constraint fk_hist_content_images_database_file_id foreign key (database_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_images add constraint fk_hist_content_images_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_list_style add constraint fk_hist_content_list_style_list_style_id foreign key (list_style_id) references list_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_list_style add constraint fk_hist_content_list_style_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_co_pg_tn_id foreign key (co_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_ref_co_pg_tn_id foreign key (ref_co_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_tp_pg_tn_id foreign key (tp_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_ref_tp_pg_tn_id foreign key (ref_tp_pg_tn_id) references pg_tree_node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_zone_part_id foreign key (zone_part_id) references zone_part on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_ref_messagepoint_locale_id foreign key (ref_messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_obj_association add constraint fk_hist_content_obj_association_ref_image_library_id foreign key (ref_image_library_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_paragraph_style add constraint fk_hist_content_paragraph_style_paragraph_style_id foreign key (paragraph_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_paragraph_style add constraint fk_hist_content_paragraph_style_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_placeholders add constraint fk_hist_content_placeholders_placeholder_id foreign key (placeholder_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_placeholders add constraint fk_hist_content_placeholders_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_text_style add constraint fk_hist_content_text_style_style_id foreign key (style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_text_style add constraint fk_hist_content_text_style_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_variable add constraint fk_hist_content_variable_content_id foreign key (content_id) references hist_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_content_variable add constraint fk_hist_content_variable_variable_id foreign key (variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_meta_form_item_value_set add constraint fk_hist_meta_form_item_value_set_metadata_form_item_id foreign key (metadata_form_item_id) references hist_metadata_form_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_metadata_form add constraint fk_hist_metadata_form_form_definition_id foreign key (form_definition_id) references metadata_form_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_metadata_form_item add constraint fk_hist_metadata_form_item_upload_file_id foreign key (upload_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_metadata_form_item add constraint fk_hist_metadata_form_item_form_item_definition_id foreign key (form_item_definition_id) references metadata_form_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_metadata_form_item add constraint fk_hist_metadata_form_item_metadata_form_id foreign key (metadata_form_id) references hist_metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_rat_shared_content add constraint fk_hist_rat_shared_content_rat_shared_content_id foreign key (rat_shared_content_id) references rationalizer_shared_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table hist_rationalizer_content add constraint fk_hist_rationalizer_content_rationalizer_content_id foreign key (rationalizer_content_id) references rationalizer_document_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_document add constraint fk_insert_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_document add constraint fk_insert_document_insert_id foreign key (insert_id) references insert_obj on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_obj add constraint fk_insert_obj_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_rate_schedule add constraint fk_insert_rate_schedule_rate_schedule_collection_id foreign key (rate_schedule_collection_id) references rate_schedule_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_rate_schedule add constraint fk_insert_rate_schedule_insert_schedule_id foreign key (insert_schedule_id) references insert_schedule on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_schedule add constraint fk_insert_schedule_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_schedule add constraint fk_insert_schedule_schedule_collection_id foreign key (schedule_collection_id) references insert_schedule_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_schedule_bin_assignment add constraint fk_insert_schedule_bin_assignment_insert_schedule_id foreign key (insert_schedule_id) references insert_schedule on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_target_group_excluded add constraint fk_insert_target_group_excluded_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_target_group_excluded add constraint fk_insert_target_group_excluded_insert_id foreign key (insert_id) references insert_obj on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_target_group_extended add constraint fk_insert_target_group_extended_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_target_group_extended add constraint fk_insert_target_group_extended_insert_id foreign key (insert_id) references insert_obj on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_target_group_included add constraint fk_insert_target_group_included_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_target_group_included add constraint fk_insert_target_group_included_insert_id foreign key (insert_id) references insert_obj on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_tg_instance_map add constraint fk_insert_tg_instance_map_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table insert_tg_instance_map add constraint fk_insert_tg_instance_map_tg_id foreign key (tg_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table job add constraint fk_job_system_state_id foreign key (system_state_id) references system_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table json_data_definition add constraint fk_json_data_definition_parent_def_id foreign key (parent_def_id) references json_data_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table json_data_definition add constraint fk_json_data_definition_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table json_data_definition add constraint fk_json_data_definition_data_group_id foreign key (data_group_id) references data_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table language_content_hash_map add constraint fk_language_content_hash_map_language_content_hash_id foreign key (language_content_hash_id) references language_content_hash on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table language_selection add constraint fk_language_selection_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table language_selection add constraint fk_language_selection_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence add constraint fk_licence_applicationId foreign key (applicationId) references licence_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence add constraint fk_licence_customerId foreign key (customerId) references licence_customer on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_attributes add constraint fk_licence_attributes_licence_id foreign key (licence_id) references licence on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_audit add constraint fk_licence_audit_licenceId foreign key (licenceId) references licence on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_audit add constraint fk_licence_audit_auditActionId foreign key (auditActionId) references licence_audit_action on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_customer add constraint fk_licence_customer_keypairId foreign key (keypairId) references licence_customer_keypair on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_customer add constraint fk_licence_customer_representativeId foreign key (representativeId) references licence_representative on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_history_reason add constraint fk_licence_history_reason_reason_id foreign key (reason_id) references licence_change_reasons on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_history_reason add constraint fk_licence_history_reason_history_id foreign key (history_id) references licence_history on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_module add constraint fk_licence_module_applicationId foreign key (applicationId) references licence_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_resource add constraint fk_licence_resource_licenceId foreign key (licenceId) references licence on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_server add constraint fk_licence_server_licenceId foreign key (licenceId) references licence on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table licence_server_ip_address add constraint fk_licence_server_ip_address_serverId foreign key (serverId) references licence_server on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table list_style add constraint fk_list_style_text_style_id foreign key (text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table list_style_cust add constraint fk_list_style_cust_text_style_id foreign key (text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table list_style_cust add constraint fk_list_style_cust_master_list_style_id foreign key (master_list_style_id) references list_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table list_style_cust_map add constraint fk_list_style_cust_map_list_style_cust_id foreign key (list_style_cust_id) references list_style_cust on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table list_style_cust_map add constraint fk_list_style_cust_map_list_style_id foreign key (list_style_id) references list_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table list_style_cust_map add constraint fk_list_style_cust_map_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table add constraint fk_lookup_table_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_document add constraint fk_lookup_table_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_document add constraint fk_lookup_table_document_lookup_table_instance_id foreign key (lookup_table_instance_id) references lookup_table_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_instance add constraint fk_lookup_table_instance_lookup_file_id foreign key (lookup_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_instance add constraint fk_lookup_table_instance_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_instance add constraint fk_lookup_table_instance_state_id foreign key (state_id) references workflow_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_tpc add constraint fk_lookup_table_tpc_tp_collection_id foreign key (tp_collection_id) references touchpoint_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_tpc add constraint fk_lookup_table_tpc_lookup_table_instance_id foreign key (lookup_table_instance_id) references lookup_table_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_version_map add constraint fk_lookup_table_version_map_lookup_table_id foreign key (lookup_table_id) references lookup_table on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_version_map add constraint fk_lookup_table_version_map_lookup_table_instance_id foreign key (lookup_table_instance_id) references lookup_table_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_table_version_map add constraint fk_lookup_table_version_map_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table lookup_value add constraint fk_lookup_value_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table meta_form_item_def_con_ele_map add constraint fk_meta_form_item_def_con_ele_map_data_element_id foreign key (data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table meta_form_item_def_con_ele_map add constraint fk_meta_form_item_def_con_ele_map_metadata_form_item_def_id foreign key (metadata_form_item_def_id) references metadata_form_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form add constraint fk_metadata_form_form_definition_id foreign key (form_definition_id) references metadata_form_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_definition add constraint fk_metadata_form_definition_web_service_config_id foreign key (web_service_config_id) references web_service_config on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_item add constraint fk_metadata_form_item_upload_file_id foreign key (upload_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_item add constraint fk_metadata_form_item_form_item_definition_id foreign key (form_item_definition_id) references metadata_form_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_item add constraint fk_metadata_form_item_metadata_form_id foreign key (metadata_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_item_definition add constraint fk_metadata_form_item_definition_data_element_id foreign key (data_element_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_item_definition add constraint fk_metadata_form_item_definition_metadata_form_definition_id foreign key (metadata_form_definition_id) references metadata_form_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_form_item_value_set add constraint fk_metadata_form_item_value_set_metadata_form_item_id foreign key (metadata_form_item_id) references metadata_form_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_points_of_interest add constraint fk_metadata_points_of_interest_form_item_definition_id foreign key (form_item_definition_id) references metadata_form_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table metadata_points_of_interest add constraint fk_metadata_points_of_interest_rationalizer_application_id foreign key (rationalizer_application_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table mp_connector add constraint fk_mp_connector_channel_id foreign key (channel_id) references mp_channel on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table mphcs_configuration add constraint fk_mphcs_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table mphcs_configuration add constraint fk_mphcs_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table mphcs_configuration add constraint fk_mphcs_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table mphcs_configuration add constraint fk_mphcs_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_delivery_report_doc add constraint fk_msg_delivery_report_doc_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_delivery_report_doc add constraint fk_msg_delivery_report_doc_message_delivery_report_id foreign key (message_delivery_report_id) references message_delivery_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_delivery_report_msg add constraint fk_msg_delivery_report_msg_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_delivery_report_msg add constraint fk_msg_delivery_report_msg_message_delivery_report_id foreign key (message_delivery_report_id) references message_delivery_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_simulation_report_doc add constraint fk_msg_simulation_report_doc_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_simulation_report_doc add constraint fk_msg_simulation_report_doc_message_simulation_report_id foreign key (message_simulation_report_id) references message_simulation_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_simulation_report_msg add constraint fk_msg_simulation_report_msg_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_simulation_report_msg add constraint fk_msg_simulation_report_msg_message_simulation_report_id foreign key (message_simulation_report_id) references message_simulation_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table native_comp_configuration add constraint fk_native_comp_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table native_comp_configuration add constraint fk_native_comp_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table native_comp_configuration add constraint fk_native_comp_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table native_comp_configuration add constraint fk_native_comp_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_drop_down_menu add constraint fk_navigation_drop_down_menu_tab_id foreign key (tab_id) references navigation_tabs on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_drop_down_menu_item add constraint fk_navigation_drop_down_menu_item_menu_id foreign key (menu_id) references navigation_drop_down_menu on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_drop_down_menu_item add constraint fk_navigation_drop_down_menu_item_tree_id foreign key (tree_id) references navigation_tree on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_menu_item_perm add constraint fk_navigation_menu_item_perm_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_menu_item_perm add constraint fk_navigation_menu_item_perm_navigation_menu_item_id foreign key (navigation_menu_item_id) references navigation_drop_down_menu_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tab_default_map add constraint fk_navigation_tab_default_map_item_id foreign key (item_id) references navigation_drop_down_menu_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tab_default_map add constraint fk_navigation_tab_default_map_tab_id foreign key (tab_id) references navigation_tabs on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tab_permission add constraint fk_navigation_tab_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tab_permission add constraint fk_navigation_tab_permission_navigation_tab_id foreign key (navigation_tab_id) references navigation_tabs on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tabs add constraint fk_navigation_tabs_default_menu_item_id foreign key (default_menu_item_id) references navigation_drop_down_menu_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table navigation_tree add constraint fk_navigation_tree_parent_id foreign key (parent_id) references navigation_tree on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table node add constraint fk_node_parent_id foreign key (parent_id) references node on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table node add constraint fk_node_branch_id foreign key (branch_id) references branch on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table notification_settings_tp add constraint fk_notification_settings_tp_touchpoint_id foreign key (touchpoint_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table notification_settings_tp add constraint fk_notification_settings_tp_notification_id foreign key (notification_id) references notification_settings on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table object_wf_action_map add constraint fk_object_wf_action_map_wf_action_id foreign key (wf_action_id) references workflow_action on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table operations_report_doc add constraint fk_operations_report_doc_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table operations_report_doc add constraint fk_operations_report_doc_operations_report_id foreign key (operations_report_id) references operations_report_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table operations_report_event add constraint fk_operations_report_event_delivery_event_id foreign key (delivery_event_id) references delivery_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table operations_report_event add constraint fk_operations_report_event_report_id foreign key (report_id) references operations_report_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_def_con_ele_map add constraint fk_order_entry_def_con_ele_map_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_def_con_ele_map add constraint fk_order_entry_def_con_ele_map_order_entry_item_def_id foreign key (order_entry_item_def_id) references order_entry_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item add constraint fk_order_entry_item_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item add constraint fk_order_entry_item_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item add constraint fk_order_entry_item_upload_file_id foreign key (upload_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item add constraint fk_order_entry_item_communication_id foreign key (communication_id) references communication on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item_con_ele_map add constraint fk_order_entry_item_con_ele_map_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item_con_ele_map add constraint fk_order_entry_item_con_ele_map_order_entry_item_id foreign key (order_entry_item_id) references order_entry_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item_definition add constraint fk_order_entry_item_definition_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_entry_item_definition add constraint fk_order_entry_item_definition_data_element_variable_id foreign key (data_element_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_item_def_selections add constraint fk_order_item_def_selections_touchpoint_selection_id foreign key (touchpoint_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_item_def_selections add constraint fk_order_item_def_selections_order_entry_item_definition_id foreign key (order_entry_item_definition_id) references order_entry_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_item_selections add constraint fk_order_item_selections_touchpoint_selection_id foreign key (touchpoint_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table order_item_selections add constraint fk_order_item_selections_order_entry_item_id foreign key (order_entry_item_id) references order_entry_item on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table para_style_cust add constraint fk_para_style_cust_text_style_id foreign key (text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table para_style_cust add constraint fk_para_style_cust_master_para_style_id foreign key (master_para_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table para_style_cust_line_spacings add constraint fk_para_style_cust_line_spacings_para_style_cust_id foreign key (para_style_cust_id) references para_style_cust on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table para_style_cust_map add constraint fk_para_style_cust_map_para_style_cust_id foreign key (para_style_cust_id) references para_style_cust on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table para_style_cust_map add constraint fk_para_style_cust_map_para_style_id foreign key (para_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table para_style_cust_map add constraint fk_para_style_cust_map_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table paragraph_style add constraint fk_paragraph_style_style_id foreign key (style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table paragraph_style_line_spacings add constraint fk_paragraph_style_line_spacings_paragraph_style_id foreign key (paragraph_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table parameter_group_instance add constraint fk_parameter_group_instance_pg_id foreign key (pg_id) references parameter_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table parameter_group_instance add constraint fk_parameter_group_instance_pg_instance_collection_id foreign key (pg_instance_collection_id) references pg_instance_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table parameter_group_item add constraint fk_parameter_group_item_pg_id foreign key (pg_id) references parameter_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table parameter_group_item add constraint fk_parameter_group_item_parameter_id foreign key (parameter_id) references parameter on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table password_history add constraint fk_password_history_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table permission add constraint fk_permission_category_id foreign key (category_id) references permission_category on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table permission_category add constraint fk_permission_category_category_group_id foreign key (category_group_id) references category_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table pg_tree_node add constraint fk_pg_tree_node_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table pg_tree_node add constraint fk_pg_tree_node_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table pg_tree_node add constraint fk_pg_tree_node_pgi_collection_id foreign key (pgi_collection_id) references pg_instance_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table pod add constraint fk_pod_type foreign key (type) references pod_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table pod add constraint fk_pod_status foreign key (status) references pod_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project add constraint fk_project_metadata_form_id foreign key (metadata_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_applied_wf_steps add constraint fk_project_applied_wf_steps_wf_step_id foreign key (wf_step_id) references workflow_step on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_applied_wf_steps add constraint fk_project_applied_wf_steps_project_task_id foreign key (project_task_id) references project_task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_step_req_note_map add constraint fk_project_step_req_note_map_project_task_id foreign key (project_task_id) references project_task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_step_req_note_map add constraint fk_project_step_req_note_map_wf_step_id foreign key (wf_step_id) references workflow_step on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_task add constraint fk_project_task_task_id foreign key (task_id) references task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_task add constraint fk_project_task_project_id foreign key (project_id) references project on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_task add constraint fk_project_task_id foreign key (id) references project_task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_wf_step_user_map add constraint fk_project_wf_step_user_map_project_task_id foreign key (project_task_id) references project_task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table project_wf_step_user_map add constraint fk_project_wf_step_user_map_wf_step_id foreign key (wf_step_id) references workflow_step on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table proof add constraint fk_proof_proof_definition_id foreign key (proof_definition_id) references proof_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table proof_definition add constraint fk_proof_definition_tp_selection_id foreign key (tp_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rat_hist_shared_content add constraint fk_rat_hist_shared_content_hist_rat_shared_content_id foreign key (hist_rat_shared_content_id) references hist_rat_shared_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rat_hist_shared_content add constraint fk_rat_hist_shared_content_rat_shared_content_id foreign key (rat_shared_content_id) references rationalizer_shared_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rat_hist_shared_content_form add constraint fk_rat_hist_shared_content_form_hist_metadata_form_id foreign key (hist_metadata_form_id) references hist_metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rat_hist_shared_content_form add constraint fk_rat_hist_shared_content_form_rat_shared_content_id foreign key (rat_shared_content_id) references rationalizer_shared_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rate_schedule add constraint fk_rate_schedule_rate_schedule_collection_id foreign key (rate_schedule_collection_id) references rate_schedule_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rate_schedule_detail add constraint fk_rate_schedule_detail_rate_schedule_id foreign key (rate_schedule_id) references rate_schedule on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_app_nav_tree add constraint fk_rationalizer_app_nav_tree_form_item_def_id foreign key (form_item_def_id) references metadata_form_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_app_nav_tree add constraint fk_rationalizer_app_nav_tree_rationalizer_app_id foreign key (rationalizer_app_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_app_tp_reference add constraint fk_rationalizer_app_tp_reference_rationalizer_app_id foreign key (rationalizer_app_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_app_visible_user add constraint fk_rationalizer_app_visible_user_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_app_visible_user add constraint fk_rationalizer_app_visible_user_rationalizer_app_id foreign key (rationalizer_app_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_application add constraint fk_rationalizer_application_brand_profile_id foreign key (brand_profile_id) references brand_profile on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_document add constraint fk_rationalizer_document_parsed_document_form_id foreign key (parsed_document_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_document add constraint fk_rationalizer_document_rationalizer_application_id foreign key (rationalizer_application_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_document_content add constraint fk_rationalizer_document_content_parsed_content_form_id foreign key (parsed_content_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_document_content add constraint fk_rationalizer_document_content_rationalizer_document_id foreign key (rationalizer_document_id) references rationalizer_document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_document_content add constraint fk_rationalizer_document_content_rationalizer_shared_content_id foreign key (rationalizer_shared_content_id) references rationalizer_shared_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_filter_item_defs add constraint fk_rationalizer_filter_item_defs_metadata_form_definition_id foreign key (metadata_form_definition_id) references metadata_form_item_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_filter_item_defs add constraint fk_rationalizer_filter_item_defs_rationalizer_application foreign key (rationalizer_application) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_hist_content add constraint fk_rationalizer_hist_content_hist_rationalizer_content_id foreign key (hist_rationalizer_content_id) references hist_rationalizer_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_hist_content add constraint fk_rationalizer_hist_content_rationalizer_content_id foreign key (rationalizer_content_id) references rationalizer_document_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_hist_form add constraint fk_rationalizer_hist_form_hist_metadata_form_id foreign key (hist_metadata_form_id) references hist_metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_hist_form add constraint fk_rationalizer_hist_form_rationalizer_content_id foreign key (rationalizer_content_id) references rationalizer_document_content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_query add constraint fk_rationalizer_query_rationalizer_application_id foreign key (rationalizer_application_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_query_component add constraint fk_rationalizer_query_component_rationalizer_query_id foreign key (rationalizer_query_id) references rationalizer_query on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_shared_content add constraint fk_rationalizer_shared_content_parsed_content_form_id foreign key (parsed_content_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rationalizer_shared_content add constraint fk_rationalizer_shared_content_rationalizer_application_id foreign key (rationalizer_application_id) references rationalizer_application on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table redirection_info add constraint fk_redirection_info_domain foreign key (domain) references domain on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table redirection_info add constraint fk_redirection_info_production foreign key (production) references pod on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table redirection_info add constraint fk_redirection_info_transition foreign key (transition) references pod on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table reference_connection add constraint fk_reference_connection_data_source_association_id foreign key (data_source_association_id) references data_source_association on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table reference_connection add constraint fk_reference_connection_reference_data_source_id foreign key (reference_data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table reference_connection add constraint fk_reference_connection_primary_key_variable_id foreign key (primary_key_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table reference_connection add constraint fk_reference_connection_reference_key_variable_id foreign key (reference_key_variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table reference_connection add constraint fk_reference_connection_primary_compound_key_id foreign key (primary_compound_key_id) references compound_key on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table reference_connection add constraint fk_reference_connection_reference_compound_key_id foreign key (reference_compound_key_id) references compound_key on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table report_scenario_delivery_event add constraint fk_report_scenario_delivery_event_delivery_event_id foreign key (delivery_event_id) references delivery_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table report_scenario_delivery_event add constraint fk_report_scenario_delivery_event_report_id foreign key (report_id) references report_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table report_scenario_message add constraint fk_report_scenario_message_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table report_scenario_message add constraint fk_report_scenario_message_report_id foreign key (report_id) references report_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table role_permission add constraint fk_role_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table role_permission add constraint fk_role_permission_role_id foreign key (role_id) references role on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table search_result_ids add constraint fk_search_result_ids_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sefas_configuration add constraint fk_sefas_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sefas_configuration add constraint fk_sefas_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sefas_configuration add constraint fk_sefas_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sefas_configuration add constraint fk_sefas_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table seg_analysis_analyzables add constraint fk_seg_analysis_analyzables_seg_analysis_id foreign key (seg_analysis_id) references segmentation_analysis on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sendmail_configuration add constraint fk_sendmail_configuration_customer_email_address_var_id foreign key (customer_email_address_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sendmail_configuration add constraint fk_sendmail_configuration_id foreign key (id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sendmail_configuration add constraint fk_sendmail_configuration_connector_id foreign key (connector_id) references mp_connector on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sendmail_configuration add constraint fk_sendmail_configuration_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sendmail_configuration add constraint fk_sendmail_configuration_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table simulation add constraint fk_simulation_workgroup_id foreign key (workgroup_id) references workgroup on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table simulation_content_object add constraint fk_simulation_content_object_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table simulation_content_object add constraint fk_simulation_content_object_simulation_id foreign key (simulation_id) references simulation on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sub_content_type add constraint fk_sub_content_type_parent_content_id foreign key (parent_content_id) references content_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sync_history_locales add constraint fk_sync_history_locales_sync_history_id foreign key (sync_history_id) references sync_history on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sync_object add constraint fk_sync_object_sync_process_id foreign key (sync_process_id) references sync_process on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table sync_worker add constraint fk_sync_worker_sync_process_id foreign key (sync_process_id) references sync_process on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag add constraint fk_tag_tag_content foreign key (tag_content) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag add constraint fk_tag_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag add constraint fk_tag_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_content_object add constraint fk_tag_content_object_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_content_object add constraint fk_tag_content_object_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_document add constraint fk_tag_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_document add constraint fk_tag_document_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_insert add constraint fk_tag_insert_insert_id foreign key (insert_id) references insert_obj on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_insert add constraint fk_tag_insert_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_target_group_excluded add constraint fk_tag_target_group_excluded_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_target_group_excluded add constraint fk_tag_target_group_excluded_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_target_group_extended add constraint fk_tag_target_group_extended_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_target_group_extended add constraint fk_tag_target_group_extended_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_target_group_included add constraint fk_tag_target_group_included_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_target_group_included add constraint fk_tag_target_group_included_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_tg_instance_map add constraint fk_tag_tg_instance_map_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_tg_instance_map add constraint fk_tag_tg_instance_map_tg_id foreign key (tg_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_touchpoint_collection add constraint fk_tag_touchpoint_collection_touchpoint_collection_id foreign key (touchpoint_collection_id) references touchpoint_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tag_touchpoint_collection add constraint fk_tag_touchpoint_collection_tag_id foreign key (tag_id) references tag on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table target_group add constraint fk_target_group_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table target_group_document add constraint fk_target_group_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table target_group_document add constraint fk_target_group_document_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task add constraint fk_task_reporter_id foreign key (reporter_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task add constraint fk_task_metadata_form_id foreign key (metadata_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task add constraint fk_task_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task add constraint fk_task_content_id foreign key (content_id) references content on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task_document add constraint fk_task_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task_document add constraint fk_task_document_task_id foreign key (task_id) references task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task_item add constraint fk_task_item_task_id foreign key (task_id) references task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task_user add constraint fk_task_user_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table task_user add constraint fk_task_user_task_id foreign key (task_id) references task on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table template_modifier add constraint fk_template_modifier_complex_value_id foreign key (complex_value_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table template_modifier add constraint fk_template_modifier_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table template_modifier add constraint fk_template_modifier_tp_selection_id foreign key (tp_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table template_variant add constraint fk_template_variant_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tenant_permission add constraint fk_tenant_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tenant_permission add constraint fk_tenant_permission_tenant_id foreign key (tenant_id) references tenants on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tenant_theme_info add constraint fk_tenant_theme_info_system_theme foreign key (system_theme) references system_theme on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tenant_theme_info add constraint fk_tenant_theme_info_background_theme foreign key (background_theme) references background_themes on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tenants add constraint fk_tenants_parent_id foreign key (parent_id) references tenants on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tenants add constraint fk_tenants_id foreign key (id) references tenant_theme_info on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table test_scenario add constraint fk_test_scenario_workgroup_id foreign key (workgroup_id) references workgroup on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table test_scenario add constraint fk_test_scenario_test_suite_id foreign key (test_suite_id) references test_suite on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table test_scenario_content_object add constraint fk_test_scenario_content_object_content_object_id foreign key (content_object_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table test_scenario_content_object add constraint fk_test_scenario_content_object_test_id foreign key (test_id) references test_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_colors add constraint fk_text_style_colors_text_style_id foreign key (text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_cust add constraint fk_text_style_cust_master_text_style_id foreign key (master_text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_cust_colors add constraint fk_text_style_cust_colors_text_style_cust_id foreign key (text_style_cust_id) references text_style_cust on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_cust_map add constraint fk_text_style_cust_map_text_style_cust_id foreign key (text_style_cust_id) references text_style_cust on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_cust_map add constraint fk_text_style_cust_map_text_style_id foreign key (text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_cust_map add constraint fk_text_style_cust_map_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_cust_point_sizes add constraint fk_text_style_cust_point_sizes_text_style_cust_id foreign key (text_style_cust_id) references text_style_cust on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_font add constraint fk_text_style_font_ttf_file_id foreign key (ttf_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_font add constraint fk_text_style_font_eot_file_id foreign key (eot_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_font add constraint fk_text_style_font_ttf_bold_file_id foreign key (ttf_bold_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_font add constraint fk_text_style_font_ttf_italic_file_id foreign key (ttf_italic_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_font add constraint fk_text_style_font_ttf_bold_italic_file_id foreign key (ttf_bold_italic_file_id) references database_file on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table text_style_point_sizes add constraint fk_text_style_point_sizes_text_style_id foreign key (text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_collection add constraint fk_touchpoint_collection_metadata_form_id foreign key (metadata_form_id) references metadata_form on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_collection add constraint fk_touchpoint_collection_output_filename_cplex_val_id foreign key (output_filename_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_collection add constraint fk_touchpoint_collection_output_doc_title_cplex_val_id foreign key (output_doc_title_cplex_val_id) references complex_value on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_language add constraint fk_touchpoint_language_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_selection add constraint fk_touchpoint_selection_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_selection add constraint fk_touchpoint_selection_template_variant_id foreign key (template_variant_id) references template_variant on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_selection_workgroup add constraint fk_touchpoint_selection_workgroup_workgroup_id foreign key (workgroup_id) references workgroup on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_selection_workgroup add constraint fk_touchpoint_selection_workgroup_tp_selection_id foreign key (tp_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_targeting add constraint fk_touchpoint_targeting_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_tg_instance_map add constraint fk_touchpoint_tg_instance_map_instance_id foreign key (instance_id) references target_group_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table touchpoint_tg_instance_map add constraint fk_touchpoint_tg_instance_map_tg_id foreign key (tg_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_collection_touchpoint add constraint fk_tp_collection_touchpoint_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_collection_touchpoint add constraint fk_tp_collection_touchpoint_collection_id foreign key (collection_id) references touchpoint_collection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_delivery_report_doc add constraint fk_tp_delivery_report_doc_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_delivery_report_doc add constraint fk_tp_delivery_report_doc_tp_delivery_report_id foreign key (tp_delivery_report_id) references tp_delivery_report_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_delivery_report_event add constraint fk_tp_delivery_report_event_delivery_event_id foreign key (delivery_event_id) references delivery_event on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_delivery_report_event add constraint fk_tp_delivery_report_event_report_id foreign key (report_id) references tp_delivery_report_scenario on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_instance_locales add constraint fk_tp_instance_locales_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_instance_locales add constraint fk_tp_instance_locales_tpivm_id foreign key (tpivm_id) references tp_instance_visibility_map on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_sel_visible_user add constraint fk_tp_sel_visible_user_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_sel_visible_user add constraint fk_tp_sel_visible_user_tp_selection_id foreign key (tp_selection_id) references touchpoint_selection on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_target_group_excluded add constraint fk_tp_target_group_excluded_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_target_group_excluded add constraint fk_tp_target_group_excluded_touchpoint_targeting_id foreign key (touchpoint_targeting_id) references touchpoint_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_target_group_extended add constraint fk_tp_target_group_extended_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_target_group_extended add constraint fk_tp_target_group_extended_touchpoint_targeting_id foreign key (touchpoint_targeting_id) references touchpoint_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_target_group_included add constraint fk_tp_target_group_included_target_group_id foreign key (target_group_id) references target_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tp_target_group_included add constraint fk_tp_target_group_included_touchpoint_targeting_id foreign key (touchpoint_targeting_id) references touchpoint_targeting on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tstp_language add constraint fk_tstp_language_messagepoint_locale_id foreign key (messagepoint_locale_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table tstp_language add constraint fk_tstp_language_tstp_id foreign key (tstp_id) references text_style_transformation_profile on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table user_permission add constraint fk_user_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table user_permission add constraint fk_user_permission_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table user_role add constraint fk_user_role_role_id foreign key (role_id) references role on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table user_role add constraint fk_user_role_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table variable_data_element_map add constraint fk_variable_data_element_map_de_id foreign key (de_id) references data_element_core on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table variable_data_element_map add constraint fk_variable_data_element_map_op_id foreign key (op_id) references aggregation_operator on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table variable_data_element_map add constraint fk_variable_data_element_map_data_record_level_id foreign key (data_record_level_id) references data_record_level on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table variable_document add constraint fk_variable_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table variable_document add constraint fk_variable_document_variable_id foreign key (variable_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow add constraint fk_workflow_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_instance add constraint fk_workflow_instance_status_id foreign key (status_id) references version_status on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_instance add constraint fk_workflow_instance_workflow_id foreign key (workflow_id) references workflow on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_library_document add constraint fk_workflow_library_document_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_library_document add constraint fk_workflow_library_document_workflow_library_id foreign key (workflow_library_id) references workflow on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position add constraint fk_workflow_position_workflow_id foreign key (workflow_id) references old_workflow on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position add constraint fk_workflow_position_workflow_state_id foreign key (workflow_state_id) references workflow_state on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position add constraint fk_workflow_position_previous foreign key (previous) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position add constraint fk_workflow_position_next foreign key (next) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position_permission add constraint fk_workflow_position_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position_permission add constraint fk_workflow_position_permission_workflow_position_id foreign key (workflow_position_id) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position_user add constraint fk_workflow_position_user_user_id foreign key (user_id) references users on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_position_user add constraint fk_workflow_position_user_workflow_position_id foreign key (workflow_position_id) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_property add constraint fk_workflow_property_workflow_position_id foreign key (workflow_position_id) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step add constraint fk_workflow_step_ts_trans_profile_id foreign key (ts_trans_profile_id) references text_style_transformation_profile on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step add constraint fk_workflow_step_frequency_type_id foreign key (frequency_type_id) references frequency_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step add constraint fk_workflow_step_wf_instance_id foreign key (wf_instance_id) references workflow_instance on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step_langs add constraint fk_workflow_step_langs_lang_id foreign key (lang_id) references messagepoint_locale on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step_langs add constraint fk_workflow_step_langs_wf_step_id foreign key (wf_step_id) references workflow_step on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step_subwfs add constraint fk_workflow_step_subwfs_wf_id foreign key (wf_id) references workflow on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_step_subwfs add constraint fk_workflow_step_subwfs_wf_step_id foreign key (wf_step_id) references workflow_step on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_tab add constraint fk_workflow_tab_workflow_position_id foreign key (workflow_position_id) references workflow_position on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_tab add constraint fk_workflow_tab_required_id foreign key (required_id) references workflow_tab on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_tab_permission add constraint fk_workflow_tab_permission_permission_id foreign key (permission_id) references permission on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workflow_tab_permission add constraint fk_workflow_tab_permission_workflow_tab_id foreign key (workflow_tab_id) references workflow_tab on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workgroup_zone add constraint fk_workgroup_zone_workgroup_id foreign key (workgroup_id) references workgroup on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table workgroup_zone add constraint fk_workgroup_zone_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table xml_data_tag_definition add constraint fk_xml_data_tag_definition_data_source_id foreign key (data_source_id) references data_source on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table xml_data_tag_definition add constraint fk_xml_data_tag_definition_parent_tag_id foreign key (parent_tag_id) references xml_data_tag_definition on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table xml_data_tag_definition add constraint fk_xml_data_tag_definition_data_group_id foreign key (data_group_id) references data_group on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_parent_zone_id foreign key (parent_zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_default_text_style_id foreign key (default_text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_starter_text_style_id foreign key (starter_text_style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_default_paragraph_style_id foreign key (default_paragraph_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_default_list_style_id foreign key (default_list_style_id) references list_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_template_image_id foreign key (template_image_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_template_smart_text_id foreign key (template_smart_text_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_comm_data_image_var_id foreign key (comm_data_image_var_id) references data_element_variable on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_document_id foreign key (document_id) references document on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone add constraint fk_zone_document_section_id foreign key (document_section_id) references document_section on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_attributes add constraint fk_zone_attributes_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_image_assets add constraint fk_zone_image_assets_image_id foreign key (image_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_image_assets add constraint fk_zone_image_assets_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_list_style add constraint fk_zone_list_style_list_style_id foreign key (list_style_id) references list_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_list_style add constraint fk_zone_list_style_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_paragraph_style add constraint fk_zone_paragraph_style_paragraph_style_id foreign key (paragraph_style_id) references paragraph_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_paragraph_style add constraint fk_zone_paragraph_style_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_part add constraint fk_zone_part_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_part add constraint fk_zone_part_content_type foreign key (content_type) references content_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_part add constraint fk_zone_part_sub_content_type_id foreign key (sub_content_type_id) references sub_content_type on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_smart_text_assets add constraint fk_zone_smart_text_assets_smart_text_id foreign key (smart_text_id) references content_object on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_smart_text_assets add constraint fk_zone_smart_text_assets_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_style add constraint fk_zone_style_style_id foreign key (style_id) references text_style on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_style add constraint fk_zone_style_zone_id foreign key (zone_id) references zone on delete NO ACTION on update NO ACTION deferrable initially deferred;
