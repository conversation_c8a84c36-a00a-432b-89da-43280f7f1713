alter table condition_coverage_report add constraint fk_condition_coverage_report_rule_coverage_report_id foreign key (rule_coverage_report_id) references rule_coverage_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table job_insert_recipient_report add constraint fk_job_insert_recipient_report_job_insert_recipient_id foreign key (job_insert_recipient_id) references job_insert_recipient on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table job_insert_schedule_insert_id add constraint fk_job_insert_schedule_insert_id_job_insert_schedule_id foreign key (job_insert_schedule_id) references job_insert_schedule on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table job_insert_schedule_rates add constraint fk_job_insert_schedule_rates_job_insert_schedule_id foreign key (job_insert_schedule_id) references job_insert_schedule on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table job_weight_limit add constraint fk_job_weight_limit_job_rate_schedule_id foreign key (job_rate_schedule_id) references job_rate_schedule on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table message_coverage_report add constraint fk_message_coverage_report_zone_coverage_report_id foreign key (zone_coverage_report_id) references zone_coverage_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_target_coverage_report add constraint fk_msg_target_coverage_report_targetgrp_coverage_report_id foreign key (targetgrp_coverage_report_id) references target_group_coverage_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table msg_target_coverage_report add constraint fk_msg_target_coverage_report_message_coverage_report_id foreign key (message_coverage_report_id) references message_coverage_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table report_job_metadata_detail add constraint fk_report_job_metadata_detail_part_id foreign key (part_id) references report_job_metadata_part on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table report_job_metadata_xml add constraint fk_report_job_metadata_xml_parent_id foreign key (parent_id) references report_job_metadata on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table rule_coverage_report add constraint fk_rule_coverage_report_target_coverage_report_id foreign key (target_coverage_report_id) references target_group_coverage_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
alter table zone_coverage_report add constraint fk_zone_coverage_report_simulation_coverage_report_id foreign key (simulation_coverage_report_id) references simulation_coverage_report on delete NO ACTION on update NO ACTION deferrable initially deferred;
