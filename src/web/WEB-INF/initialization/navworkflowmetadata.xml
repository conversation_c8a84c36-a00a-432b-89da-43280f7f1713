<?xml version="1.0" encoding="UTF-8"?>
<NavWorkflowMetadata>

	<NavigationTrees>

	<!--
	*******************************
	Admin Trees 
	*******************************
	-->

		<!-- Locale Settings Tree -->
		<NavigationTree id="450">		
			<Query><![CDATA[hql:select languageId, languageName, languageFavourite from MessagepointLocale m where m.defaultLocale = '1' order by languageName]]></Query>
			<Parameter>languageSettingsId</Parameter>
			<Url>admin/language_settings_view.jsp</Url>
			<Icon></Icon>
			<Type>com.prinova.messagepoint.model.navigation.Tree.TREE_TYPE_ID_NAME_CSS_PAIR</Type>
			<Nodes>
				<NavigationTree>
					<Query><![CDATA[hql:select id, name, favourite, defaultLocale from MessagepointLocale m where m.languageId = {parent} order by name ASC]]></Query>
					<Parameter>localeSettingsId</Parameter>
					<Url>admin/locale_settings_view.jsp</Url>
					<Icon></Icon>
					<Type>com.prinova.messagepoint.model.navigation.Tree.TREE_TYPE_ID_NAME_CSS_PAIR</Type>
				</NavigationTree>
			</Nodes>
		</NavigationTree>	

		<!-- Workgroups Associations Tree -->
		<NavigationTree id="600">
			<Query>page.label.all.workgroups</Query>
			<Parameter />
			<Url>admin/workgroup_associations_view.form</Url>
			<Icon>tree_folder</Icon>
			<Type>com.prinova.messagepoint.model.navigation.Tree.TREE_TYPE_FOLDER</Type>
			<Nodes>
				<NavigationTree>
					<Query><![CDATA[hql:from Workgroup w order by w.name asc]]></Query>
					<Parameter>wgid</Parameter>
					<Url>admin/workgroup_view.form</Url>
					<Icon>tree_user</Icon>
					<Type>com.prinova.messagepoint.model.navigation.Tree.TREE_TYPE_MODEL</Type>
				</NavigationTree>
			</Nodes>
		</NavigationTree>
			
	</NavigationTrees>
	
	<!-- MENU DEFINITIONS -->
	<NavigationDropDownMenus>
		<!-- Tasks Menus -->
		<NavigationDropDownMenu id="200">
			<Order>1</Order>
			<NavigationTab refid="1" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.view</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="201">
			<Order>2</Order>
			<NavigationTab refid="1" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.new</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="202">
			<Order>3</Order>
			<NavigationTab refid="1" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.edit</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<!-- Contents Menus -->
		<NavigationDropDownMenu id="103">
			<Order>1</Order>
			<NavigationTab refid="2" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.setup</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="101">
			<Order>2</Order>
			<NavigationTab refid="2" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.view</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="102">
			<Order>3</Order>
			<NavigationTab refid="2" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.new</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="104">
			<Order>4</Order>
			<NavigationTab refid="2" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.styles</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="105">
			<Order>5</Order>
			<NavigationTab refid="2" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.content</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="106">
			<Order>6</Order>
			<NavigationTab refid="2" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.edit</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="107">
			<Order>7</Order>
			<NavigationTab refid="2" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.setup</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<!-- Inserts Menus -->
		<NavigationDropDownMenu id="600">
			<Order>1</Order>
			<NavigationTab refid="12" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.view</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="601">
			<Order>2</Order>
			<NavigationTab refid="12" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.new</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<!-- Reports Menus -->
		<NavigationDropDownMenu id="10">
			<Order>1</Order>
			<NavigationTab refid="6" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.view</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<!-- Testing Menus -->
		<NavigationDropDownMenu id="300">
			<Order>1</Order>
			<NavigationTab refid="9" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.view</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<!-- Touchpoints Menus -->
		<NavigationDropDownMenu id="700">
			<Order>1</Order>
			<NavigationTab refid="13" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.view</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="701">
			<Order>2</Order>
			<NavigationTab refid="13" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.setup</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="702">
			<Order>3</Order>
			<NavigationTab refid="13" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.new</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<!-- Data Menus -->
		<NavigationDropDownMenu id="400">
			<Order>1</Order>
			<NavigationTab refid="10" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.view</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="402">
			<Order>2</Order>
			<NavigationTab refid="10" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.settings</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="401">
			<Order>3</Order>
			<NavigationTab refid="10" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.new</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<!-- Admin Menus -->
		<NavigationDropDownMenu id="7">
			<Order>2</Order>
			<NavigationTab refid="8" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.setup</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="8">
			<Order>3</Order>
			<NavigationTab refid="8" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.new</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="9">
			<Order>4</Order>
			<NavigationTab refid="8" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.edit</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="11">
			<Order>5</Order>
			<NavigationTab refid="8" /> <!-- This refers to a NavigationTab object which hasn't been parsed yet, it comes later in this file, so this reference has to be resolved at the very end of the import job! -->
			<Name>page.label.information</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<!-- Target Menus -->
		<NavigationDropDownMenu id="5">
			<Order>1</Order>
			<NavigationTab refid="3" />
			<Name>page.label.view</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<NavigationDropDownMenu id="6">
			<Order>2</Order>
			<NavigationTab refid="3" />
			<Name>page.label.new</Name>
			<Requires></Requires>
		</NavigationDropDownMenu>
		<!-- PINC Menus -->
<!--		<NavigationDropDownMenu id="500">-->
<!--			<Order>1</Order>-->
<!--			<NavigationTab refid="4" />-->
<!--			<Name>page.label.view</Name>-->
<!--			<Requires></Requires>-->
<!--		</NavigationDropDownMenu>-->
<!--		<NavigationDropDownMenu id="501">-->
<!--			<Order>2</Order>-->
<!--			<NavigationTab refid="4" />-->
<!--			<Name>page.label.setup</Name>-->
<!--			<Requires></Requires>-->
<!--		</NavigationDropDownMenu>-->
	</NavigationDropDownMenus>

	<!-- MENU ITEM DEFINITIONS -->
	<NavigationDropDownMenuItems>
		<!-- Touchpoints - List Menu -->
		<NavigationDropDownMenuItem id="467">
			<Order>1</Order>
			<NavigationDropDownMenu refid="700" />
			<Icon><![CDATA[tree_document]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.touchpoints</Name>
			<Url>touchpoints/touchpoint_content_object_list.form</Url>
			<Permissions>
				<Permission refid="154" />  <!-- ROLE_LICENCED_TOUCHPOINT_SELECTIONS_VIEW -->
				<Permission refid="159" />  <!-- ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_VIEW -->
				<Permission refid="36" />	<!-- ID_ROLE_MESSAGE_VIEW_MY -->
				<Permission refid="37" />	<!-- ID_ROLE_MESSAGE_VIEW_ALL -->
				<Permission refid="178" />	<!-- ROLE_TOUCHPOINT_CONTENT_VIEW -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="465">
			<Order>2</Order>
			<NavigationDropDownMenu refid="700" />
			<Icon><![CDATA[tree_document]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.touchpoint.collections</Name>
			<Url>touchpoints/touchpoint_collection_list.form</Url>
			<Permissions>
				<Permission refid="151" />   	<!-- ROLE_COLLECTION_SETUP -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>		
		<NavigationDropDownMenuItem id="462">
			<Order>3</Order>
			<NavigationDropDownMenu refid="700" />
			<Icon />
			<NavigationTree refid="0" />
			<Name>page.label.output.tags</Name>
			<Url>tpadmin/tags_list.form</Url>
			<Permissions>
				<Permission refid="117" />
			</Permissions>
			<Requires/>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="26">
			<Order>4</Order>
			<NavigationDropDownMenu refid="700" />
			<Icon><![CDATA[tree_event]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.deliveryevents</Name>
			<Url>tpadmin/delivery_event_list.form</Url>
			<Permissions>
				<Permission refid="5" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		
		<!-- Touchpoints - New Menu -->
		<NavigationDropDownMenuItem id="404">
			<Order>1</Order>
			<NavigationDropDownMenu refid="702" />
			<Icon><![CDATA[tree_document]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.touchpoint.dot</Name>
			<Url>javascript:createTouchpointAction();</Url>
			<Permissions>
				<Permission refid="5" /> <!-- ID_ROLE_TOUCHPOINT_ADMIN -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="403">
			<Order>2</Order>
			<NavigationDropDownMenu refid="702" />
			<Icon><![CDATA[tree_document]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.import.dot</Name>
			<Url>tpadmin/touchpoint_import.form</Url>
			<Permissions>
				<Permission refid="5" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>

		<!-- Target - Setup Menu -->
		<NavigationDropDownMenuItem id="44">
			<Order>1</Order>
			<NavigationDropDownMenu refid="5" />
			<Icon>tree_target_rule</Icon>
			<NavigationTree refid="0" />
			<Name>page.label.targetgroups</Name>
			<Url>dataadmin/targetgroups.form</Url>
			<Permissions>
				<Permission refid="28" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="34">
			<Order>2</Order>
			<NavigationDropDownMenu refid="5" />
			<Icon>tree_target_rule</Icon>
			<NavigationTree refid="0" />
			<Name>page.label.targetgroup.details</Name>
			<Url>dataadmin/conditionelements.form</Url>
			<Permissions>
				<Permission refid="58" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>

		<!-- Content - Styles -->
		<NavigationDropDownMenuItem id="419">
			<Order>1</Order>
			<NavigationDropDownMenu refid="104" />
			<Icon><![CDATA[tree_message]]></Icon>
			<Name>page.label.text</Name>
			<Url>content/text_style_list.form</Url>
			<Permissions>
				<Permission refid="141" /> <!-- STYLES_UPDATE -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="420">
			<Order>2</Order>
			<NavigationDropDownMenu refid="104" />
			<Icon><![CDATA[tree_message]]></Icon>
			<Name>page.label.paragraph</Name>
			<Url>content/paragraph_style_list.form</Url>
			<Permissions>
				<Permission refid="141" /> <!-- STYLES_UPDATE -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="421">
			<Order>3</Order>
			<NavigationDropDownMenu refid="104" />
			<Icon><![CDATA[tree_message]]></Icon>
			<Name>page.label.list</Name>
			<Url>content/list_style_list.form</Url>
			<Permissions>
				<Permission refid="141" /> <!-- STYLES_UPDATE -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
				
		<!-- Content - Content -->
		<NavigationDropDownMenuItem id="418">
			<Order>1</Order>
			<NavigationDropDownMenu refid="105" />
			<Icon><![CDATA[tree_message]]></Icon>
			<Name>page.label.embedded.content</Name>
			<Url>content/global_content_list.form</Url>
			<Permissions>
				<Permission refid="132" /> <!-- EMBBEDED_CONTENT_VIEW -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="430">
			<Order>2</Order>
			<NavigationDropDownMenu refid="105" />
			<Icon><![CDATA[tree_message]]></Icon>
			<Name>page.label.smart.canvas</Name>
			<Url>content/global_content_list.form?isFreeform=true</Url>
			<Permissions>
				<Permission refid="132" /> <!-- EMBBEDED_CONTENT_VIEW -->
		</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="424">
			<Order>3</Order>
			<NavigationDropDownMenu refid="105" />
			<Icon><![CDATA[tree_message]]></Icon>
			<Name>page.label.content.library</Name>
			<Url>content/global_content_list.form?localContentType=2</Url>
			<Permissions>
				<Permission refid="136" /> <!-- CONTENT_LIBRARY_VIEW -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>	 -->
		<!-- Content - Edit -->
		<NavigationDropDownMenuItem id="434">
			<Order>1</Order>
			<NavigationDropDownMenu refid="106" />
			<Icon><![CDATA[tree_message]]></Icon>
			<Name>page.label.power.edit</Name>
			<Url>content/content_power_edit.form</Url>
			<Permissions>
				<Permission refid="198" /> <!-- POWER_EDIT -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="435">
			<Order>2</Order>
			<NavigationDropDownMenu refid="107" />
			<Icon><![CDATA[tree_message]]></Icon>
			<Name>page.label.content.assistants</Name>
			<Url>contentintelligence/content_assistant_list.form</Url>
			<Permissions>
				<Permission refid="198" /> <!-- POWER_EDIT -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>

		<!-- Inserts - List -->
		<NavigationDropDownMenuItem id="427">
			<Order>1</Order>
			<NavigationDropDownMenu refid="600" />
			<Icon></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.inserts</Name>
			<Url>insert/insert_list.form</Url>
			<Permissions>
				<Permission refid="87" /> <!-- ROLE_LICENCED_INSERT_VIEW -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="429">
			<Order>2</Order>
			<NavigationDropDownMenu refid="600" />
			<Icon></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.insert.schedules</Name>
			<Url>insert/insert_schedule_list.form</Url>
			<Permissions>
				<Permission refid="98" /> <!-- ROLE_LICENCED_INSERT_SCHEDULE_VIEW -->
				<Permission refid="100" /> <!-- ROLE_LICENCED_INSERT_SCHEDULE_APPROVE -->
				<Permission refid="101" /> <!-- ROLE_LICENCED_INSERT_SCHEDULE_SETUP -->
				<Permission refid="103" /> <!-- ROLE_LICENCED_INSERT_SCHEDULE_REASSIGN -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="428">
			<Order>3</Order>
			<NavigationDropDownMenu refid="600" />
			<Icon></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.insert.schedules.setup</Name>
			<Url>insert/insert_schedule_list.form?isSetup=true</Url>
			<Permissions>
				<Permission refid="101" /> <!-- ROLE_LICENCED_INSERT_SCHEDULE_SETUP -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="433">
			<Order>4</Order>
			<NavigationDropDownMenu refid="600" />
			<Icon></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.rate.sheets</Name>
			<Url>insert/rate_schedule_list.form</Url>
			<Permissions>
				<Permission refid="106" /> <!-- ROLE_LICENCED_RATE_SCHEDULE_VIEW -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>

		<!-- Testing - List -->
		<NavigationDropDownMenuItem id="302">
			<Order>1</Order>
			<NavigationDropDownMenu refid="300" />
			<Icon><![CDATA[tree_message]]></Icon>
			<Name>page.label.tests</Name>
			<Url>testing/testing_list.form</Url>
			<Permissions>
				<Permission refid="26" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<!-- Simulations - List Menu -->
		<NavigationDropDownMenuItem id="551">
			<Order>2</Order>
			<NavigationDropDownMenu refid="300" />
			<Icon><![CDATA[tree_document]]></Icon>
			<Name>page.label.simulations</Name>
			<Url>simulations/simulations_list.form</Url>
			<Permissions>
				<Permission refid="78" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<!-- Connected Test Center - List Menu -->
		<NavigationDropDownMenuItem id="552">
			<Order>3</Order>
			<NavigationDropDownMenu refid="300" />
			<Icon><![CDATA[tree_document]]></Icon>
			<Name>page.label.connected</Name>
			<Url>testing/test_center_communications_list.form</Url>
			<Permissions>
				<Permission refid="152" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>

		<!-- Reports - List -->
		<NavigationDropDownMenuItem id="52">
			<Order>1</Order>
			<NavigationDropDownMenu refid="10" />
			<Icon><![CDATA[tree_report]]></Icon>
			<Name>page.label.dashboards</Name>
			<Url>dashboards/global_dashboard.form</Url>
			<Permissions>
				<Permission refid="16" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>

		<!-- Tasks - List -->
		<NavigationDropDownMenuItem id="200">
			<Order>1</Order>
			<NavigationDropDownMenu refid="200" />
			<Icon></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.tasks</Name>
			<Url>tasks/task_list.form</Url>
			<Permissions>
				<Permission refid="42" />
				<Permission refid="43" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>

		<!-- Changes - List -->
		<NavigationDropDownMenuItem id="208">
			<Order>3</Order>
			<NavigationDropDownMenu refid="200" />
			<Icon></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.changes</Name>
			<Url>tasks/change_list.form</Url>
			<Permissions>
				<Permission refid="256" />
				<Permission refid="257" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>

		<!--
		<NavigationDropDownMenuItem id="202">
			<Order>1</Order>
			<NavigationDropDownMenu refid="200" />
			<Icon></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.projects</Name>
			<Url>projects/project_list.form</Url>
			<Permissions>
				<Permission refid="185" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>		
		-->
		<!-- Data - Setup Menu -->
		<!-- Data - Setup Menu (Data Definitions) -->
		<NavigationDropDownMenuItem id="48">
			<Order>1</Order>
			<NavigationDropDownMenu refid="400" />
			<Icon>tree_database</Icon>
			<NavigationTree refid="0" />
			<Name>page.label.datasources</Name>
			<Url>dataadmin/datasources.form</Url>
			<Permissions>
				<Permission refid="51" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
<!--		<NavigationDropDownMenuItem id="47">-->
<!--			<Order>2</Order>-->
<!--			<NavigationDropDownMenu refid="400" />-->
<!--			<Icon>tree_target_rule</Icon>-->
<!--			<NavigationTree refid="0" />-->
<!--			<Name>page.label.data.groups</Name>-->
<!--			<Url>dataadmin/datagroups.jsp</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="51" />-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->
		<NavigationDropDownMenuItem id="49">
			<Order>3</Order>
			<NavigationDropDownMenu refid="400" />
			<Icon>tree_database</Icon>
			<NavigationTree refid="0" />
			<Name>page.label.data.source.associations</Name>
			<Url>dataadmin/data_source_associations.jsp</Url>
			<Permissions>
				<Permission refid="51" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<!-- Data - Setup Menu (Test Data) -->
		<NavigationDropDownMenuItem id="29">
			<Order>4</Order>
			<NavigationDropDownMenu refid="400" />
			<Icon><![CDATA[tree_target_rule]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.data.files</Name>
			<Url>dataadmin/data_files.form</Url>
			<Permissions>
				<Permission refid="51" /> <!-- ROLE_TOUCHPOINT_DATA_LIST -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="32">
			<Order>5</Order>
			<NavigationDropDownMenu refid="400" />
			<Icon><![CDATA[tree_target_rule]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.data.resources</Name>
			<Url>dataadmin/data_resources.form</Url>
			<Permissions>
				<Permission refid="51" /> <!-- ROLE_TOUCHPOINT_DATA_LIST -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<!-- Data - Setup Menu (Insertable Content) -->
		<NavigationDropDownMenuItem id="40">
			<Order>10</Order>
			<NavigationDropDownMenu refid="400" />
			<Icon>tree_target_rule</Icon>
			<NavigationTree refid="0" />
			<Name>page.label.variables</Name>
			<Url>dataadmin/variable_list.form</Url>
			<Permissions>
				<Permission refid="51" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<!-- Data - Setup Menu (Content Selection) -->
		<NavigationDropDownMenuItem id="425">
			<Order>12</Order>
			<NavigationDropDownMenu refid="400" />
			<Icon><![CDATA[tree_users]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.parameters</Name>
			<Url>dataadmin/parameter_list.form</Url>
			<Permissions>
				<Permission refid="51" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="426">
			<Order>13</Order>
			<NavigationDropDownMenu refid="400" />
			<Icon><![CDATA[tree_users]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.parameter.groups</Name>
			<Url>dataadmin/parameter_group_list.form</Url>
			<Permissions>
				<Permission refid="51" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="422">
			<Order>14</Order>
			<NavigationDropDownMenu refid="400" />
			<Icon></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.external.event</Name>
			<Url>dataadmin/external_event_list.form</Url>
			<Permissions>
				<Permission refid="120" />	<!-- ROLE_EXTERNAL_EVENT_VIEW -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>		
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="470">
			<Order>17</Order>
			<NavigationDropDownMenu refid="400" />
			<Icon></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.lookup.tables</Name>
			<Url>dataadmin/lookup_table_list.form</Url>
			<Permissions>
				<Permission refid="174" /> 	<!-- ROLE_LICENCED_LOOKUP_TABLE_VIEW -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="471">
			<Order>18</Order>
			<NavigationDropDownMenu refid="400" />
			<Icon></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.metadata.forms</Name>
			<Url>metadata/metadata_form_definition_list.form</Url>
			<Permissions>
				<Permission refid="182" /> 	<!-- ROLE_METADATA_FORM_ADMIN -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="472">
			<Order>19</Order>
			<NavigationDropDownMenu refid="400" />
			<Icon></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.tag.cloud</Name>
			<Url>dataadmin/tag_cloud_list.form</Url>
			<Permissions>
				<Permission refid="255" /> 	<!-- ROLE_METATAGS_ADMIN -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>

 		<NavigationDropDownMenuItem id="25">
			<Order>1</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.system.settings</Name>
			<Url>admin/systemproperty_view.form</Url>
			<Permissions>
				<Permission refid="56" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="711">
			<Order>20</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.bundle.delivery</Name>
			<Url>admin/deserver_list.form</Url>
			<Permissions>
				<Permission refid="203" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
	 	<NavigationDropDownMenuItem id="41">
			<Order>6</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.feature.activation</Name>
			<Url>admin/licence_management.form</Url>
			<Permissions>
				<Permission refid="900" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>		
	 	<NavigationDropDownMenuItem id="42">
			<Order>5</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.licence.information</Name>
			<Url>admin/licenceInfo.jsp</Url>
			<Permissions>
				<Permission refid="56" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="60">
			<Order>3</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="450" />
			<Name>page.label.locale.settings</Name>
			<Url>admin/locale_settings_view.jsp</Url>
			<Permissions>
				<Permission refid="56" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="61">
			<Order>2</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.security.settings</Name>
			<Url>admin/password_security_view.form</Url>
			<Permissions>
				<Permission refid="56" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>

		<NavigationDropDownMenuItem id="502">
			<Order>13</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_users]]></Icon>
			<Name>page.label.users</Name>
			<Url>admin/user_list.form</Url>
			<Permissions>
				<!-- AG -->
				<Permission refid="44" />
				<Permission refid="900" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="602">
			<Order>12</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<Name>page.label.roles</Name>
			<Url>admin/role_list.form</Url>
			<Permissions>
				<!-- AG -->
				<Permission refid="46" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="612">
			<Order>13</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_users]]></Icon>
			<NavigationTree refid="600" />
			<Name>page.label.workgroups</Name>
			<Url>admin/workgroup_associations_view.form</Url>
			<Permissions>
				<Permission refid="66" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="615">
			<Order>15</Order>
			<NavigationDropDownMenu refid="7" />
			<Name>page.label.workflow.library</Name>
			<Url>workflow/workflow_library.form</Url>
			<Permissions>
				<Permission refid="142" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="614">
			<Order>14</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_users]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.auditing</Name>
			<Url>admin/auditing_list.form</Url>
			<Permissions>
				<Permission refid="165" />
				<Permission refid="999" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="618">
			<Order>15</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.fileroot.management</Name>
			<Url>admin/fileroot_management_edit.form</Url>
			<Permissions>
				<Permission refid="999" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="620">
			<Order>16</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.data.expiration</Name>
			<Url>admin/data_expiration_edit.form</Url>
			<Permissions>
				<Permission refid="999" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="608">
			<Order>7</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[separator]]></Icon>
			<Type>2</Type>
			<NavigationTree refid="0" />
			<Name></Name>
			<Url></Url>
			<Permissions>
				<Permission refid="48" /> <!-- ROLE_TENANT_LEVEL_PROVIDER -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="606">
			<Order>8</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.user.interface.settings</Name>
			<Url>admin/user_interface_view.jsp</Url>
			<Permissions>
				<Permission refid="4" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="613">
			<Order>4</Order>
			<NavigationDropDownMenu refid="8" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.workgroup</Name>
			<Url>admin/workgroup_edit.form</Url>
			<Permissions>
				<Permission refid="67" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="616">
			<Order>18</Order>
			<NavigationDropDownMenu refid="11" />
			<Name>page.label.statistics</Name>
			<Url>javascript:runStatisticsReportAction();</Url>
			<Permissions>
				<Permission refid="900" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="619">
			<Order>19</Order>
			<NavigationDropDownMenu refid="7" />
			<Name>page.label.brand</Name>
			<Url>brand/brand_profile_list.form</Url>
			<Permissions>
				<Permission refid="210" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="812">
			<Order>20</Order>
			<NavigationDropDownMenu refid="7" />
			<Name>page.label.api.tokens</Name>
			<Url>admin/rest_api_token_list.form</Url>
			<Permissions>
				<Permission refid="900" />
				<Permission refid="259" /> <!-- ROLE_API_TOKEN_ADMIN -->
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>

		<!-- Admin - List -->
		<NavigationDropDownMenuItem id="707">
			<Order>27</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.domain.admin.branch</Name>
			<Url>admin/branch_list.form</Url>
			<Permissions>
				<Permission refid="900" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>0</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="705">
			<Order>28</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.domain.admin.maintenance</Name>
			<Url>admin/maintenance_view.form</Url>
			<Permissions>
				<Permission refid="900" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="706">
			<Order>29</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.domain.admin.system.settings</Name>
			<Url>admin/systemproperty_prinova_edit.form</Url>
			<Permissions>
				<Permission refid="999" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="708">
			<Order>30</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.domain.admin.users</Name>
			<Url>admin/master_users_list.form</Url>
			<Permissions>
				<Permission refid="900" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>0</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="709">
			<Order>31</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.domain.admin.licences</Name>
			<Url>admin/licence_list.form</Url>
			<Permissions>
				<Permission refid="900" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="710">
			<Order>32</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.master.unified.login</Name>
			<Url>admin/pod_list.form</Url>
			<Permissions>
				<Permission refid="900" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="712">
			<Order>33</Order>
			<NavigationDropDownMenu refid="7" />
			<Icon><![CDATA[tree_user]]></Icon>
			<NavigationTree refid="0" />
			<Name>page.label.master.services</Name>
			<Url>admin/services_list.form</Url>
			<Permissions>
				<Permission refid="900" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="621">
			<Order>21</Order>
			<NavigationDropDownMenu refid="7" />
			<Name>page.label.configurable.links</Name>
			<Url>link/configurable_links_edit.form</Url>
			<Permissions>
				<Permission refid="56" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="816">
			<Order>22</Order>
			<NavigationDropDownMenu refid="7" />
			<Name>page.label.touchpoint.dictionaries</Name>
			<Url>admin/touchpoint_dictionaries_edit.form</Url>
			<Permissions>
				<Permission refid="5" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>2</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="622">
			<Order>23</Order>
			<NavigationDropDownMenu refid="7" />
			<Name>page.label.stats.pr</Name>
			<Url>stats/stats_tool.form?pr=true</Url>
			<Permissions>
				<Permission refid="900" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="623">
			<Order>24</Order>
			<NavigationDropDownMenu refid="7" />
			<Name>page.label.stats.no_pr</Name>
			<Url>stats/stats_tool.form?pr=false</Url>
			<Permissions>
				<Permission refid="900" />
			</Permissions>
			<Requires></Requires>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>
		<NavigationDropDownMenuItem id="813">
			<Order>25</Order>
			<NavigationDropDownMenu refid="7" />
			<Name>client_messages.signing_key_pairs</Name>
			<Url>admin/signing_key_pair_list.form</Url>
			<Permissions>
				<Permission refid="900" />
				<Permission refid="259" /> <!-- ROLE_API_TOKEN_ADMIN -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
		</NavigationDropDownMenuItem>

		<!-- PINC Menu Items -->
<!--		<NavigationDropDownMenuItem id="801">-->
<!--			<Order>1</Order>-->
<!--			<NavigationDropDownMenu refid="500" />-->
<!--			<Name>page.label.pinc.applications</Name>-->
<!--			<Url>pinc/application_list.form</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->
<!--		<NavigationDropDownMenuItem id="802">-->
<!--			<Order>2</Order>-->
<!--			<NavigationDropDownMenu refid="500" />-->
<!--			<Name>page.label.pinc.jobs</Name>-->
<!--			<Url>pinc/job_list.form</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->
<!--		<NavigationDropDownMenuItem id="803">-->
<!--			<Order>1</Order>-->
<!--			<NavigationDropDownMenu refid="501" />-->
<!--			<Name>page.label.pinc.api.tokens</Name>-->
<!--			<Url>pinc/api_token_list.form</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->
<!--		<NavigationDropDownMenuItem id="804">-->
<!--			<Order>2</Order>-->
<!--			<NavigationDropDownMenu refid="501" />-->
<!--			<Name>page.label.pinc.encryption.keys</Name>-->
<!--			<Url>pinc/encryption_key_list.form</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->
<!--		<NavigationDropDownMenuItem id="805">-->
<!--			<Order>3</Order>-->
<!--			<NavigationDropDownMenu refid="501" />-->
<!--			<Name>page.label.pinc.executables</Name>-->
<!--			<Url>pinc/executable_list.form</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->
<!--		<NavigationDropDownMenuItem id="806">-->
<!--			<Order>4</Order>-->
<!--			<NavigationDropDownMenu refid="501" />-->
<!--			<Name>page.label.pinc.files</Name>-->
<!--			<Url>pinc/file_list.form</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->
<!--		<NavigationDropDownMenuItem id="807">-->
<!--			<Order>5</Order>-->
<!--			<NavigationDropDownMenu refid="501" />-->
<!--			<Name>page.label.pinc.authentication.keys</Name>-->
<!--			<Url>pinc/authentication_key_list.form</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->
<!--		<NavigationDropDownMenuItem id="808">-->
<!--			<Order>6</Order>-->
<!--			<NavigationDropDownMenu refid="501" />-->
<!--			<Name>page.label.pinc.notifications</Name>-->
<!--			<Url>pinc/notification_list.form</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->
<!--		<NavigationDropDownMenuItem id="809">-->
<!--			<Order>7</Order>-->
<!--			<NavigationDropDownMenu refid="501" />-->
<!--			<Name>client_messages.pinc.mp_instances</Name>-->
<!--			<Url>pinc/mp_instance_list.form</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->
<!--		<NavigationDropDownMenuItem id="810">-->
<!--			<Order>8</Order>-->
<!--			<NavigationDropDownMenu refid="501" />-->
<!--			<Name>client_messages.pinc.engines</Name>-->
<!--			<Url>pinc/engine_list.form</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->
<!--		<NavigationDropDownMenuItem id="811">-->
<!--			<Order>3</Order>-->
<!--			<NavigationDropDownMenu refid="500" />-->
<!--			<Name>client_messages.pinc.destinations</Name>-->
<!--			<Url>pinc/destination_list.form</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->
<!--		<NavigationDropDownMenuItem id="814">-->
<!--			<Order>9</Order>-->
<!--			<NavigationDropDownMenu refid="501" />-->
<!--			<Name>client_messages.pinc.job_managers</Name>-->
<!--			<Url>pinc/job_manager_list.form</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->
<!--		<NavigationDropDownMenuItem id="815">-->
<!--			<Order>9</Order>-->
<!--			<NavigationDropDownMenu refid="501" />-->
<!--			<Name>client_messages.pinc.webhooks</Name>-->
<!--			<Url>pinc/webhook_list.form</Url>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Requires></Requires>-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationDropDownMenuItem>-->

	</NavigationDropDownMenuItems>

	<!-- TAB DEFINITIONS -->
	<NavigationTabs>
		<!-- Touchpoint Tab -->
		<NavigationTab id="13">
			<Order>1</Order>
			<Name>Touchpoint</Name>
			<StringId>page.label.touchpoint</StringId>
			<Permissions>
				<Permission refid="5" />   	<!-- ROLE_TOUCHPOINT_ADMIN -->
				<Permission refid="154" /> 	<!-- ROLE_LICENCED_TOUCHPOINT_SELECTIONS_VIEW -->
				<Permission refid="155" /> 	<!-- ROLE_LICENCED_TOUCHPOINT_SELECTIONS_EDIT -->
				<Permission refid="159" /> 	<!-- ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_VIEW -->
				<Permission refid="160" /> 	<!-- ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_EDIT -->
				<Permission refid="200" /> 	<!-- ROLE_LICENCED_TOUCHPOINT_SELECTIONS_ADMIN_SETUP -->
				<Permission refid="117" /> 	<!-- ROLE_TAG_VIEW -->
				<Permission refid="118" /> 	<!-- ROLE_TAG_EDIT -->
				<Permission refid="36" /> 	<!-- ID_ROLE_MESSAGE_VIEW_MY -->
				<Permission refid="37" /> 	<!-- ID_ROLE_MESSAGE_VIEW_ALL -->
				<Permission refid="146" /> 	<!-- ROLE_LICENCED_COMMUNICATIONS_VIEW -->
				<Permission refid="178" /> 	<!-- ROLE_TOUCHPOINT_CONTENT_VIEW -->
			</Permissions>
			<Url>tpadmin/index.jsp</Url>
			<DefaultMenuItem refid="467" /> <!-- Touchpoint Selections -->
			<AuthorizationType>1</AuthorizationType>
			<DefaultMenuItemMap>
				<Item type="tagsList">462</Item>
			</DefaultMenuItemMap>
		</NavigationTab>
		<!-- Task Tab -->
		<NavigationTab id="1">
			<Order>2</Order>
			<Name>Activity</Name>
			<StringId>page.label.activity</StringId>
			<Permissions>
				<Permission refid="42" />
				<Permission refid="43" />
			</Permissions>
			<Url>tasks/index.jsp</Url>
			<DefaultMenuItem refid="200" />
			<AuthorizationType>1</AuthorizationType>
		</NavigationTab>
		<!-- Target Tab -->
		<NavigationTab id="3">
			<Order>3</Order>
			<Name>Target</Name>
			<StringId>page.label.target</StringId>
			<Permissions>
				<Permission refid="28" /> <!-- ROLE_TARGETING_EDIT -->
				<Permission refid="58" /> <!-- ROLE_RULE_VIEW -->
				<Permission refid="59" /> <!-- ROLE_RULE_EDIT -->
			</Permissions>
			<Url>dataadmin/targetgroups.form</Url>
			<DefaultMenuItem>44</DefaultMenuItem> <!-- Target Groups -->
			<AuthorizationType>1</AuthorizationType>
		</NavigationTab>
		<!-- Content Tab -->
		<NavigationTab id="2">
			<Order>4</Order>
			<Name>Content</Name>
			<StringId>page.label.content</StringId>
			<Permissions>
				<Permission refid="5" />
				<Permission refid="7" />			
				<Permission refid="36" />
				<Permission refid="37" />
				<Permission refid="132" /> <!-- ROLE_EMBEDDED_CONTENT_VIEW -->
				<Permission refid="136" /> <!-- ROLE_CONTENT_LIBRARY_VIEW -->
			</Permissions>
			<Url>content/index.jsp</Url>
			<DefaultMenuItem refid="418" /> <!-- List: Smart Text -->
			<DefaultMenuItemMap>
			</DefaultMenuItemMap>
			<AuthorizationType>1</AuthorizationType>
		</NavigationTab>
		<!-- Insert Tab -->
		<NavigationTab id="12">
			<Order>5</Order>
			<Name>Insert</Name>
			<StringId>page.label.insert</StringId>
			<Permissions>
				<Permission refid="87" />   <!-- ROLE_LICENCED_INSERT_VIEW -->
				<Permission refid="88" />   <!-- ROLE_LICENCED_INSERT_EDIT -->
				<Permission refid="89" />   <!-- ROLE_LICENCED_INSERT_APPROVE -->
				<Permission refid="90" />   <!-- ROLE_LICENCED_INSERT_ARCHIVE -->
				<Permission refid="91" />   <!-- ROLE_LICENCED_INSERT_REASSIGN -->
				<Permission refid="98" />   <!-- ROLE_LICENCED_INSERT_SCHEDULE_VIEW -->
				<Permission refid="99" />   <!-- ROLE_LICENCED_INSERT_SCHEDULE_EDIT -->
				<Permission refid="100" />  <!-- ROLE_LICENCED_INSERT_SCHEDULE_APPROVE -->
				<Permission refid="101" />  <!-- ROLE_LICENCED_INSERT_SCHEDULE_SETUP -->
				<Permission refid="103" />  <!-- ROLE_LICENCED_INSERT_SCHEDULE_REASSIGN -->
				<Permission refid="106" />  <!-- ROLE_LICENCED_RATE_SCHEDULE_VIEW -->
				<Permission refid="107" />  <!-- ROLE_LICENCED_RATE_SCHEDULE_EDIT -->
			</Permissions>
			<Url>insert/index.jsp</Url>
			<DefaultMenuItem refid="427" />
			<DefaultMenuItemMap>
				<Item type="insertSchedules">429</Item>
			</DefaultMenuItemMap>
			<AuthorizationType>1</AuthorizationType>
		</NavigationTab>
		<!-- Test Tab -->
		<NavigationTab id="9">
			<Order>6</Order>
			<Name>Test</Name>
			<StringId>page.label.test</StringId>
			<Permissions>
				<Permission refid="26" />
				<Permission refid="27" />
			</Permissions>
			<Url>testing/index.jsp</Url>
			<DefaultMenuItem refid="302" />
			<DefaultMenuItemMap>
				<Item type="simulations">551</Item>
			</DefaultMenuItemMap>	
			<AuthorizationType>1</AuthorizationType>
		</NavigationTab>	
		<!-- Report Tab -->
		<NavigationTab id="6">
			<Order>7</Order>
			<Name>Report</Name>
			<StringId>page.label.report</StringId>
			<Permissions>
				<Permission refid="16" />
				<Permission refid="17" />				
			</Permissions>
			<Url>reports/index.jsp</Url>
			<DefaultMenuItem refid="52" />
			<AuthorizationType>1</AuthorizationType>
		</NavigationTab>
		<!-- Data Tab -->
		<NavigationTab id="10">
			<Order>8</Order>
			<Name>Data</Name>
			<StringId>page.label.data</StringId>
			<Permissions>
				<Permission refid="51" /> 	<!-- ROLE_TOUCHPOINT_DATA_LIST -->
				<Permission refid="54" /> 	<!-- ROLE_TOUCHPOINT_DATA_EDIT -->
				<Permission refid="120" />	<!-- ROLE_EXTERNAL_EVENT_VIEW -->
				<Permission refid="174" />	<!-- ROLE_LICENCED_LOOKUP_TABLE_VIEW -->
			</Permissions>
			<Url>dataadmin/index.jsp</Url>
			<DefaultMenuItem refid="48" /> <!-- Data Model -->
			<AuthorizationType>1</AuthorizationType>
			<DefaultMenuItemMap>
				<Item type="dataModel">48</Item>
			</DefaultMenuItemMap>		
		</NavigationTab>
		<!-- Setup Tab -->
		<NavigationTab id="8">
			<Order>9</Order>
			<Name>Setup</Name>
			<StringId>page.label.setup</StringId>
			<Permissions>
				<Permission refid="4" />
				<Permission refid="44" />
				<Permission refid="45" />
				<Permission refid="46" />
				<Permission refid="47" />
				<Permission refid="52" />
				<Permission refid="53" />
				<Permission refid="66" />
				<Permission refid="67" />
				<Permission refid="142" />
				<Permission refid="204" />
				<Permission refid="900" />
			</Permissions>
			<Url>admin/index.jsp</Url>
			<DefaultMenuItem refid="502" />
			<DefaultMenuItemMap>
				<Item type="roles">602</Item>
				<Item type="localeSettings">60</Item>
				<Item type="workgroup_associations">612</Item>
				<Item type="theme">606</Item>
			</DefaultMenuItemMap>
			<AuthorizationType>1</AuthorizationType>
		</NavigationTab>
		<!-- PINC tab -->
<!--		<NavigationTab id="4">-->
<!--			<Order>10</Order>-->
<!--			<Name>PINC</Name>-->
<!--			<StringId>page.label.pinc</StringId>-->
<!--			<Permissions>-->
<!--				<Permission refid="250" /> &lt;!&ndash; ROLE_PINC_COMPANY_ADMIN &ndash;&gt;-->
<!--				<Permission refid="251" /> &lt;!&ndash; ROLE_PINC_COMPANY_READ &ndash;&gt;-->
<!--				<Permission refid="252" /> &lt;!&ndash; ROLE_PINC_COMPANY_TEST &ndash;&gt;-->
<!--				<Permission refid="253" /> &lt;!&ndash; ROLE_PINC_COMPANY_PRODUCTION &ndash;&gt;-->
<!--				<Permission refid="254" /> &lt;!&ndash; ROLE_PINC_COMPANY_AUTHOR &ndash;&gt;-->
<!--				<Permission refid="258" /> &lt;!&ndash; ROLE_PINC_ADMIN_OPERATOR &ndash;&gt;-->
<!--			</Permissions>-->
<!--			<Url>pinc/index.jsp</Url>-->
<!--			<DefaultMenuItem refid="801" />-->
<!--			<AuthorizationType>1</AuthorizationType>-->
<!--		</NavigationTab>-->
	</NavigationTabs>

	<Workflows>
		<Workflow id="2">
			<Name>Message Flow</Name>
			<ClassName>com.prinova.messagepoint.model.content.ContentObject</ClassName>
		</Workflow>
		<Workflow id="3">
			<Name>Shared Content Flow</Name>
			<ClassName>com.prinova.messagepoint.model.content.ContentObject</ClassName>
		</Workflow>
		<Workflow id="4">
			<Name>Insert Flow</Name>
			<ClassName>com.prinova.messagepoint.model.insert.Insert</ClassName>
		</Workflow>
		<Workflow id="5">
			<Name>Insert Schedule Flow</Name>
			<ClassName>com.prinova.messagepoint.model.insert.InsertSchedule</ClassName>
		</Workflow>
		<Workflow id="6">
			<Name>Tag Flow</Name>
			<ClassName>com.prinova.messagepoint.model.tag.Tag</ClassName>
		</Workflow>
		<Workflow id="7">
			<Name>Attachment Flow</Name>
			<ClassName>com.prinova.messagepoint.model.attachment.Attachment</ClassName>
		</Workflow>
		<Workflow id="8">
			<Name>Touchpoint Targeting Flow</Name>
			<ClassName>com.prinova.messagepoint.model.TouchpointTargeting</ClassName>
		</Workflow>
		<Workflow id="11">
			<Name>Smart Text Flow</Name>
			<ClassName>com.prinova.messagepoint.model.content.ContentObject</ClassName>
		</Workflow>
		<Workflow id="12">
			<Name>Image Library Flow</Name>
			<ClassName>com.prinova.messagepoint.model.content.ContentObject</ClassName>
		</Workflow>
		<Workflow id="13">
			<Name>Content Targeting Flow</Name>
			<ClassName>com.prinova.messagepoint.model.ContentTargeting</ClassName>
		</Workflow>
	</Workflows>

	<WorkflowStates>
		<WorkflowState id="1">
			<Name>Overview</Name>
			<StringId>page.label.overview</StringId>
		</WorkflowState>
		<WorkflowState id="10">
			<Name>Timing</Name>
			<StringId>page.label.timing</StringId>
		</WorkflowState>
		<WorkflowState id="15">
			<Name>Content</Name>
			<StringId>page.label.content</StringId>
		</WorkflowState>
		<WorkflowState id="20">
			<Name>Targeting</Name>
			<StringId>page.label.targeting</StringId>
		</WorkflowState>
		<WorkflowState id="28">
			<Name>Content Approval</Name>
			<StringId>page.label.contentapproval</StringId>
		</WorkflowState>
		<WorkflowState id="29">
			<Name>Released for approval</Name>
			<StringId>page.work.flow.action.approvalready</StringId>
		</WorkflowState>		
		<WorkflowState id="30">
			<Name>Localization</Name>
			<StringId>page.label.localization</StringId>
		</WorkflowState>
		<WorkflowState id="31">
			<Name>Localization Testing</Name>
			<StringId>page.label.localization</StringId>
		</WorkflowState>
		<WorkflowState id="35">
			<Name>Testing</Name>
			<StringId>page.label.functionaltesting</StringId>
		</WorkflowState>		
		<WorkflowState id="39">
			<Name>Production Testing</Name>
			<StringId>page.work.flow.action.final.approval</StringId>
		</WorkflowState>		
		<WorkflowState id="40">
			<Name>Production</Name>
			<StringId>page.label.na</StringId>
		</WorkflowState>		
		<WorkflowState id="41">
			<Name>Rate Sheets</Name>
			<StringId>page.label.rate.sheets</StringId>
		</WorkflowState>
		<WorkflowState id="42">
			<Name>Selectors</Name>
			<StringId>page.label.selectors</StringId>
		</WorkflowState>
		<WorkflowState id="43">
			<Name>Reservations</Name>
			<StringId>page.label.reservations</StringId>
		</WorkflowState>
		<WorkflowState id="44">
			<Name>Insert Timing</Name>
			<StringId>page.label.insert.timing</StringId>
		</WorkflowState>
		<WorkflowState id="45">
			<Name>Associations</Name>
			<StringId>page.label.associations</StringId>
		</WorkflowState>
	</WorkflowStates>

	<WorkflowPositions>
		<WorkflowPosition id="10">
			<Workflow refid="2" />
			<WorkflowState refid="1" />
			<Required>TRUE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Next refid="11" />
			<Name>Overview</Name>
			<Permissions>
				<Permission refid="7" />  <!-- ROLE_MESSAGE_EDIT -->
				<Permission refid="179" />  <!-- ROLE_TOUCHPOINT_CONTENT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		<WorkflowPosition id="11">
			<Workflow refid="2" />
			<WorkflowState refid="15" />
			<Required>TRUE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="10" />
			<Next refid="13" />
			<Name>Content</Name>
			<Permissions>
				<Permission refid="7" />  <!-- ROLE_MESSAGE_EDIT -->
				<Permission refid="179" />  <!-- ROLE_TOUCHPOINT_CONTENT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		<WorkflowPosition id="13">
			<Workflow refid="2" />
			<WorkflowState refid="20" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="11" />
			<Name>Targeting</Name>
			<Permissions>
				<Permission refid="7" />  <!-- ROLE_MESSAGE_EDIT -->
				<Permission refid="179" />  <!-- ROLE_TOUCHPOINT_CONTENT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>

		<WorkflowPosition id="21">
			<Workflow refid="3" />
			<WorkflowState refid="1" />
			<Required>TRUE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>false</RequiresApproval>
			<Name>Overview</Name>
			<Permissions>
				<Permission refid="7" />  <!-- ROLE_MESSAGE_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
		</WorkflowPosition>
		
		<!-- INSERT WorkflowPositions -->
		<WorkflowPosition id="24">
			<Workflow refid="4" />
			<WorkflowState refid="1" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Next refid="25" />
			<Name>Overview</Name>
			<Permissions>
				<Permission refid="88" />  <!-- ROLE_LICENCED_INSERT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>	
		<WorkflowPosition id="25">
			<Workflow refid="4" />
			<WorkflowState refid="20" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="24" />
			<Next refid="26" />
			<Name>Targeting</Name>
			<Permissions>
				<Permission refid="88" />  <!-- ROLE_LICENCED_INSERT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		<WorkflowPosition id="26">
			<Workflow refid="4" />
			<WorkflowState refid="15" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="25" />
			<Name>Content</Name>
			<Permissions>
				<Permission refid="88" />  <!-- ROLE_LICENCED_INSERT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		
		<!-- INSERT SCHEDULE WorkflowPositions -->
		<WorkflowPosition id="27">
			<Workflow refid="5" />
			<WorkflowState refid="1" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Next refid="28" />
			<Name>Overview</Name>
			<Permissions>
				<Permission refid="99" />   <!-- ROLE_LICENCED_INSERT_SCHEDULE_EDIT  -->
				<Permission refid="101" />  <!-- ROLE_LICENCED_INSERT_SCHEDULE_SETUP -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>	
		<WorkflowPosition id="28">
			<Workflow refid="5" />
			<WorkflowState refid="43" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="27" />
			<Next refid="29" />
			<Name>Reservations</Name>
			<Permissions>
				<Permission refid="99" />   <!-- ROLE_LICENCED_INSERT_SCHEDULE_EDIT  -->
				<Permission refid="101" />  <!-- ROLE_LICENCED_INSERT_SCHEDULE_SETUP -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		<WorkflowPosition id="29">
			<Workflow refid="5" />
			<WorkflowState refid="44" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="28" />
			<Next refid="30" />
			<Name>Insert Timing</Name>
			<Permissions>
				<Permission refid="99" />   <!-- ROLE_LICENCED_INSERT_SCHEDULE_EDIT  -->
				<Permission refid="101" />  <!-- ROLE_LICENCED_INSERT_SCHEDULE_SETUP -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		<WorkflowPosition id="30">
			<Workflow refid="5" />
			<WorkflowState refid="41" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="29" />
			<Next refid="31" />
			<Name>Rate Sheets</Name>
			<Permissions>
				<Permission refid="101" />  <!-- ROLE_LICENCED_INSERT_SCHEDULE_SETUP -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		<WorkflowPosition id="31">
			<Workflow refid="5" />
			<WorkflowState refid="42" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="30" />
			<Name>Selectors</Name>
			<Permissions>
				<Permission refid="101" />  <!-- ROLE_LICENCED_INSERT_SCHEDULE_SETUP -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		
		<!-- TAG WorkflowPositions -->
		<WorkflowPosition id="32">
			<Workflow refid="6" />
			<WorkflowState refid="1" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Next refid="33" />
			<Name>Overview</Name>
			<Permissions>
				<Permission refid="118" />  <!-- ROLE_TAG_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>	
		<WorkflowPosition id="33">
			<Workflow refid="6" />
			<WorkflowState refid="45" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="32" />
			<Next refid="34" />
			<Name>Associations</Name>
			<Permissions>
				<Permission refid="118" />  <!-- ROLE_TAG_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		<WorkflowPosition id="34">
			<Workflow refid="6" />
			<WorkflowState refid="20" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="33" />
			<Name>Targeting</Name>
			<Permissions>
				<Permission refid="118" />  <!-- ROLE_TAG_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		
		<!-- ATTACHMENT WorkflowPositions -->
		<WorkflowPosition id="35">
			<Workflow refid="7" />
			<WorkflowState refid="1" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Next refid="36" />
			<Name>Overview</Name>
			<Permissions>
				<Permission refid="5" />  <!-- ID_ROLE_TOUCHPOINT_ADMIN -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		<WorkflowPosition id="36">
			<Workflow refid="7" />
			<WorkflowState refid="20" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="35" />
			<Name>Targeting</Name>
			<Permissions>
				<Permission refid="5" />  <!-- ID_ROLE_TOUCHPOINT_ADMIN -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		
		<!-- TOUCHPOINT TARGETING WorkflowPositions -->
		<WorkflowPosition id="37">
			<Workflow refid="8" />
			<WorkflowState refid="20" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Name>Targeting</Name>
			<Permissions>
				<Permission refid="5" />  <!-- ID_ROLE_TOUCHPOINT_ADMIN -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		
		<!-- WORKFLOW POSITIONS - EMBEDDED TEXT -->
		<WorkflowPosition id="43">
			<Workflow refid="11" />
			<WorkflowState refid="1" />
			<Required>TRUE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Next refid="44" />
			<Name>Details</Name>
			<Permissions>
				<Permission refid="133" />  <!-- ROLE_EMBEDDED_CONTENT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		<WorkflowPosition id="44">
			<Workflow refid="11" />
			<WorkflowState refid="15" />
			<Required>TRUE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="43" />
			<Next refid="45" />
			<Name>Content</Name>
			<Permissions>
				<Permission refid="133" />  <!-- ROLE_EMBEDDED_CONTENT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		<WorkflowPosition id="45">
			<Workflow refid="11" />
			<WorkflowState refid="20" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="44" />
			<Name>Targeting</Name>
			<Permissions>
				<Permission refid="133" />  <!-- ROLE_EMBEDDED_CONTENT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		
		<!-- WORKFLOW POSITIONS - CONTENT LIBRARY -->
		<WorkflowPosition id="46">
			<Workflow refid="12" />
			<WorkflowState refid="1" />
			<Required>TRUE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Next refid="47" />
			<Name>Details</Name>
			<Permissions>
				<Permission refid="137" />  <!-- ROLE_CONTENT_LIBRARY_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
		<WorkflowPosition id="47">
			<Workflow refid="12" />
			<WorkflowState refid="15" />
			<Required>TRUE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Previous refid="46" />
			<Name>Content</Name>
			<Permissions>
				<Permission refid="137" />  <!-- ROLE_CONTENT_LIBRARY_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>	
		<!-- CONTENT TARGETING WorkflowPositions -->
		<WorkflowPosition id="48">
			<Workflow refid="13" />
			<WorkflowState refid="20" />
			<Required>FALSE</Required>
			<Enabled>TRUE</Enabled>
			<RequiresApproval>FALSE</RequiresApproval>
			<Name>Targeting</Name>
			<Permissions>
				<Permission refid="7" />  	<!-- ID_ROLE_MESSAGE_EDIT -->
				<Permission refid="133" />  <!-- ROLE_EMBEDDED_CONTENT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<AssignedUserId refid="1" />
		</WorkflowPosition>
	</WorkflowPositions>
	
	<WorkflowProperties>
		<!--  Message workflow properties -->
		<WorkflowProperty id="1">
			<WorkflowPosition refid="10" />
			<Required>TRUE</Required>
			<Name>name</Name>
		</WorkflowProperty>
		<WorkflowProperty id="2">
			<WorkflowPosition refid="10" />
			<Required>false</Required>
			<Name>description</Name>
		</WorkflowProperty>
		<WorkflowProperty id="3">
			<WorkflowPosition refid="10" />
			<Required>true</Required>
			<Name>contentType</Name>
		</WorkflowProperty>
		<WorkflowProperty id="5">
			<WorkflowPosition refid="10" />
			<Required>true</Required>
			<Name>zones</Name>
		</WorkflowProperty>
		<WorkflowProperty id="9">
			<WorkflowPosition refid="11" />
			<Required>true</Required>
			<Name>contentAssociations[en]</Name>
		</WorkflowProperty>
		<WorkflowProperty id="14">
			<WorkflowPosition refid="11" />
			<Required>false</Required>
			<Name>contentAssociations[fr]</Name>
		</WorkflowProperty>
		<WorkflowProperty id="15">
			<WorkflowPosition refid="11" />
			<Required>false</Required>
			<Name>contentAssociations[es]</Name>
		</WorkflowProperty>
		<WorkflowProperty id="10">
			<WorkflowPosition refid="13" />
			<Required>false</Required>
			<Name>includedTargetGroups</Name>
		</WorkflowProperty>
		<WorkflowProperty id="11">
			<WorkflowPosition refid="13" />
			<Required>false</Required>
			<Name>excludedTargetGroups</Name>
		</WorkflowProperty>
		
		<!-- SHARED CONTENT PROPERTIES -->
		<WorkflowProperty id="28">
			<WorkflowPosition refid="21" />
			<Required>TRUE</Required>
			<Name>name</Name>
		</WorkflowProperty>
		<WorkflowProperty id="29">
			<WorkflowPosition refid="21" />
			<Required>TRUE</Required>
			<Name>contentAssociations[en]</Name>
		</WorkflowProperty>
		<WorkflowProperty id="30">
			<WorkflowPosition refid="21" />
			<Required>FALSE</Required>
			<Name>contentAssociations[fr]</Name>
		</WorkflowProperty>
		<WorkflowProperty id="31">
			<WorkflowPosition refid="21" />
			<Required>FALSE</Required>
			<Name>contentAssociations[es]</Name>
		</WorkflowProperty>
		
	</WorkflowProperties>

	<WorkflowTabs>
		<!-- MESSAGE WorkflowTabs -->
		<WorkflowTab id="10">
			<WorkflowPosition refid="10" />
			<Name>Message Overview</Name>
			<StringId>page.label.details</StringId>
			<Permissions>
				<Permission refid="7" />
				<Permission refid="179" />  
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>content/content_object_edit.form</EditUrl>
			<ViewUrl>content/content_object_view.form</ViewUrl>
			<ListUrl>touchpoints/touchpoint_content_object_list.form</ListUrl>
			<Parameter>contentObjectId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="11">
			<WorkflowPosition refid="11" />
			<Name>Message Content</Name>
			<StringId>page.label.content</StringId>
			<Permissions>
				<Permission refid="7" />
				<Permission refid="179" />
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>content/content_object_edit_content.form</EditUrl>
			<ViewUrl>content/content_object_view.form</ViewUrl>
			<ListUrl>touchpoints/touchpoint_content_object_list.form</ListUrl>
			<RequiredId refid="10" />
			<Parameter>contentObjectId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="13">
			<WorkflowPosition refid="13" />
			<Name>Message Targeting</Name>
			<StringId>page.label.targeting</StringId>
			<Permissions>
				<Permission refid="7" />
				<Permission refid="179" />
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>content/content_object_edit_targeting.form</EditUrl>
			<ViewUrl>content/content_object_view.form</ViewUrl>
			<ListUrl>touchpoints/touchpoint_content_object_list.form</ListUrl>
			<RequiredId refid="10" />
			<Parameter>contentObjectId</Parameter>
		</WorkflowTab>

		<!-- INSERT WorkflowTabs -->
		<WorkflowTab id="19">
			<WorkflowPosition refid="24" />
			<Name>Insert Overview</Name>
			<StringId>page.label.overview</StringId>
			<Permissions>
				<Permission refid="88" />  
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>insert/insert_overview_edit.form</EditUrl>
			<ViewUrl>insert/insert_view.form</ViewUrl>
			<Parameter>insertId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="20">
			<WorkflowPosition refid="25" />
			<Name>Insert Targeting</Name>
			<StringId>page.label.targeting</StringId>
			<Permissions>
				<Permission refid="88" />  
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>insert/insert_targeting_edit.form</EditUrl>
			<ViewUrl>insert/insert_view.form</ViewUrl>
			<Parameter>insertId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="21">
			<WorkflowPosition refid="26" />
			<Name>Insert Content</Name>
			<StringId>page.label.content</StringId>
			<Permissions>
				<Permission refid="88" />  
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>insert/insert_content_edit.form</EditUrl>
			<ViewUrl>insert/insert_view.form</ViewUrl>
			<Parameter>insertId</Parameter>
		</WorkflowTab>
		
		<!-- INSERT SCHEDULE WorkflowTabs -->
		<WorkflowTab id="22">
			<WorkflowPosition refid="27" />
			<Name>Insert Schedule Overview</Name>
			<StringId>page.label.overview</StringId>
			<Permissions>
				<Permission refid="99" />  
				<Permission refid="101" />  
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>insert/insert_schedule_overview_edit.form</EditUrl>
			<ViewUrl>insert/insert_schedule_view.form</ViewUrl>
			<Parameter>insertSchedId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="23">
			<WorkflowPosition refid="28" />
			<Name>Insert Schedule Reservations</Name>
			<StringId>page.label.reservations</StringId>
			<Permissions>
				<Permission refid="99" />  
				<Permission refid="101" />  
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>insert/insert_schedule_bin_assignment_edit.form</EditUrl>
			<ViewUrl>insert/insert_schedule_view.form</ViewUrl>
			<Parameter>insertSchedId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="24">
			<WorkflowPosition refid="29" />
			<Name>Insert Schedule Insert Timing</Name>
			<StringId>page.label.insert.timing</StringId>
			<Permissions>
				<Permission refid="99" />  
				<Permission refid="101" />
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>insert/insert_schedule_insert_timing_edit.form</EditUrl>
			<ViewUrl>insert/insert_schedule_view.form</ViewUrl>
			<Parameter>insertSchedId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="25">
			<WorkflowPosition refid="30" />
			<Name>Insert Schedule Rate Sheets</Name>
			<StringId>page.label.rate.sheets</StringId>
			<Permissions>
				<Permission refid="101" />  
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>insert/insert_schedule_rate_schedule_edit.form</EditUrl>
			<ViewUrl>insert/insert_schedule_view.form</ViewUrl>
			<Parameter>insertSchedId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="26">
			<WorkflowPosition refid="31" />
			<Name>Insert Schedule Selectors</Name>
			<StringId>page.label.selectors</StringId>
			<Permissions>
				<Permission refid="101" />  
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>insert/insert_schedule_selector_edit.form</EditUrl>
			<ViewUrl>insert/insert_schedule_view.form</ViewUrl>
			<Parameter>insertSchedId</Parameter>
		</WorkflowTab>
		
		<!-- TAG WorkflowTabs -->
		<WorkflowTab id="27">
			<WorkflowPosition refid="32" />
			<Name>Tag Overview</Name>
			<StringId>page.label.overview</StringId>
			<Permissions>
				<Permission refid="118" />  
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>tpadmin/tag_overview_edit.form</EditUrl>
			<ViewUrl>tpadmin/tag_overview_view.form</ViewUrl>
			<Parameter>tagId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="28">
			<WorkflowPosition refid="33" />
			<Name>Tag Associations</Name>
			<StringId>page.label.associations</StringId>
			<Permissions>
				<Permission refid="118" />  
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>tpadmin/tag_associations_edit.form</EditUrl>
			<ViewUrl>tpadmin/tag_overview_view.form</ViewUrl>
			<Parameter>tagId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="29">
			<WorkflowPosition refid="34" />
			<Name>Tag Targeting</Name>
			<StringId>page.label.targeting</StringId>
			<Permissions>
				<Permission refid="118" />  
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>tpadmin/tag_targeting_edit.form</EditUrl>
			<ViewUrl>tpadmin/tag_overview_view.form</ViewUrl>
			<Parameter>tagId</Parameter>
		</WorkflowTab>
		
		<!-- ATTACHMENT WorkflowTabs -->
		<WorkflowTab id="30">
			<WorkflowPosition refid="35" />
			<Name>Attachment Overview</Name>
			<StringId>page.label.overview</StringId>
			<Permissions>
				<Permission refid="5" />  <!-- ID_ROLE_TOUCHPOINT_ADMIN -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>attachment/attachment_overview_edit.form</EditUrl>
			<ViewUrl>tpadmin/document_view.form</ViewUrl>
			<Parameter>attachmentId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="31">
			<WorkflowPosition refid="36" />
			<Name>Attachment Targeting</Name>
			<StringId>page.label.targeting</StringId>
			<Permissions>
				<Permission refid="5" />  <!-- ID_ROLE_TOUCHPOINT_ADMIN -->  
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>attachment/attachment_targeting_edit.form</EditUrl>
			<ViewUrl>tpadmin/document_view.form</ViewUrl>
			<Parameter>attachmentId</Parameter>
		</WorkflowTab>
		
		<!-- TOUCHPOINT TARGETING WorkflowTabs -->
		<WorkflowTab id="32">
			<WorkflowPosition refid="37" />
			<Name>Touchpoint Targeting</Name>
			<StringId>page.label.targeting</StringId>
			<Permissions>
				<Permission refid="5" />  <!-- ID_ROLE_TOUCHPOINT_ADMIN -->  
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>tpadmin/touchpoint_targeting_edit.form</EditUrl>
			<ViewUrl>tpadmin/document_view.form</ViewUrl>
			<Parameter>touchpointTargetingId</Parameter>
		</WorkflowTab>

		<!-- EMBEDDED TEXT WorkflowTabs -->
		<!-- Don't change 'Name': Keyword used for tab IDs -->
		<WorkflowTab id="38">
			<WorkflowPosition refid="43" />
			<Name>Smart Text Details</Name>
			<StringId>page.label.details</StringId>
			<Permissions>
				<Permission refid="133" /> <!-- ROLE_EMBEDDED_CONTENT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>content/content_object_edit.form</EditUrl>
			<ViewUrl>content/content_object_view.form</ViewUrl>
			<ListUrl>content/global_content_list.form</ListUrl>
			<Parameter>contentObjectId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="39">
			<WorkflowPosition refid="44" />
			<Name>Smart Text</Name>
			<StringId>page.label.content</StringId>
			<Permissions>
				<Permission refid="133" /> <!-- ROLE_EMBEDDED_CONTENT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>content/content_object_edit_content.form</EditUrl>
			<ViewUrl>content/content_object_view.form</ViewUrl>
			<ListUrl>content/global_content_list.form</ListUrl>
			<RequiredId refid="10" />
			<Parameter>contentObjectId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="40">
			<WorkflowPosition refid="45" />
			<Name>Smart Text Advanced</Name>
			<StringId>page.label.advanced</StringId>
			<Permissions>
				<Permission refid="133" /> <!-- ROLE_EMBEDDED_CONTENT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>content/content_object_edit_targeting.form</EditUrl>
			<ViewUrl>content/content_object_view.form</ViewUrl>
			<ListUrl>content/global_content_list.form</ListUrl>
			<RequiredId refid="10" />
			<Parameter>contentObjectId</Parameter>
			<VisibilityToggleAttr>advanced=false</VisibilityToggleAttr> <!-- If advanced = false, hide tab -->
		</WorkflowTab>

		<!-- CONTENT LIBRARY WorkflowTabs -->
		<WorkflowTab id="41">
			<WorkflowPosition refid="46" />
			<Name>Image Library Details</Name>
			<StringId>page.label.details</StringId>
			<Permissions>
				<Permission refid="137" /> <!-- ROLE_CONTENT_LIBRARY_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>content/content_object_edit_content.form</EditUrl>
			<ViewUrl>content/content_object_view.form</ViewUrl>
			<ListUrl>content/content_library_list.form</ListUrl>
			<Parameter>contentObjectId</Parameter>
		</WorkflowTab>
		<WorkflowTab id="42">
			<WorkflowPosition refid="47" />
			<Name>Image Library</Name>
			<StringId>page.label.content</StringId>
			<Permissions>
				<Permission refid="137" /> <!-- ROLE_CONTENT_LIBRARY_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>content/content_object_edit_content.form</EditUrl>
			<ViewUrl>content/content_object_view.form</ViewUrl>
			<ListUrl>content/content_library_list.form</ListUrl>
			<RequiredId refid="10" />
			<Parameter>contentObjectId</Parameter>
		</WorkflowTab>
		
		<!-- CONTENT TARGETING WorkflowTabs -->
		<WorkflowTab id="43">
			<WorkflowPosition refid="48" />
			<Name>Content Targeting</Name>
			<StringId>page.label.targeting</StringId>
			<Permissions>
				<Permission refid="7" />  	<!-- ID_ROLE_MESSAGE_EDIT -->
				<Permission refid="133" />  <!-- ROLE_EMBEDDED_CONTENT_EDIT -->
			</Permissions>
			<AuthorizationType>1</AuthorizationType>
			<EditUrl>content/content_targeting_edit.form</EditUrl>
			<ViewUrl>frameClose.jsp?nprSaveSuccess=true</ViewUrl>
			<Parameter>contentTargetingId</Parameter>
		</WorkflowTab>
	</WorkflowTabs>
</NavWorkflowMetadata>