<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
    xmlns:tx="http://www.springframework.org/schema/tx"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
                     http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
                     http://www.springframework.org/schema/context
                     http://www.springframework.org/schema/context/spring-context-4.2.xsd
                     http://www.springframework.org/schema/tx
                     http://www.springframework.org/schema/tx/spring-tx-4.2.xsd ">
                     
    <bean class="com.prinova.messagepoint.MessagepointPropertyPlaceholderConfigurer">
	    <property name="locations">
	      <list>
	        	<value>classpath:applicationContext-common.properties</value>
	        	<value>classpath:licenceConfig.properties</value>
	        </list>
	    </property>
	</bean>
	
	<bean id="applicationUtil" class="com.prinova.messagepoint.util.ApplicationUtil" scope="singleton"/>
		
	<bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
		<property name="defaultEncoding" value="UTF-8"/>
		<property name="basename"><value>/WEB-INF/i18n/messages</value></property>
		<property name="cacheSeconds" value="-1" />
	</bean>
	
	<bean id="localeResolver" class="com.prinova.messagepoint.integrator.MessagepointLocaleResolver">
	</bean>	
	
	<bean id="hibernateUtil" class="com.prinova.messagepoint.util.HibernateUtil" scope="singleton"/>
	
	<bean id="mpDaoImpl" class="com.prinova.messagepoint.security.MessagepointDaoImpl"/>
	<bean id="mpPreAuthUserDetailImpl" class="com.prinova.messagepoint.security.PreAuthUserDetailImpl" />
    
	<bean id="initializeClassTableNames" class="com.prinova.messagepoint.initialization.InitializeClassTableNames" scope="singleton">
		<!-- <property name="hibernateDialect" value="${db.messagepoint.hibernate.dialect}" /> -->
   		<property name="hibernateDialect" value="com.prinova.messagepoint.util.MessagepointPostgresDialect" />
	    <property name="classTableMappings" value="javaclass2tablenames.properties" />
	</bean>

	<!-- Interceptor for SQL Server -->
    <bean id="initializationHibernateInterceptor" class="com.prinova.messagepoint.initialization.InitializationHibernateInterceptor" depends-on="initializeClassTableNames">
		<property name="initializeClassTableNames">
			<ref bean="initializeClassTableNames"/>
		</property>
    </bean>

    <bean id="dataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName">
            <value>${db.messagepoint.driver}</value>
        </property>
        <property name="url">
            <value>${db.messagepoint.url}</value>
        </property>
        <property name="username">
            <value>${db.messagepoint.user}</value>
        </property>
        <property name="password">
            <value>${db.messagepoint.password}</value>
        </property>
    </bean> 

    <bean id="transactionManager" class="org.springframework.orm.hibernate5.HibernateTransactionManager">
		<property name="sessionFactory" ref="initializationSessionFactory"/>
		<property name="autodetectDataSource" value="false" />
	</bean>

	<bean id="initializationSessionFactory" class="com.prinova.messagepoint.initialization.InitializationLocalSessionFactoryBean" depends-on="dataSource">
		<property name="dataSource" ref="dataSource" />
		<property name="mappingLocations">
		  <list>
			<value>classpath*:com/prinova/messagepoint/initialization/**/*.hbm.xml</value>
			<value>classpath*:com/prinova/messagepoint/model/**/*.hbm.xml</value>
			<value>classpath*:com/prinova/messagepoint/reports/model/**/*.hbm.xml</value>
			<value>classpath*:com/prinova/licence/webapp/models/**/*.hbm.xml</value>
			  <value>classpath*:com/prinova/messagepoint/platform/**/*.hbm.xml</value>
		  </list>
		</property>
		<property name="hibernateProperties">
			<ref bean="hibernateProperties"/>
		</property>
		<property name="entityInterceptor">
			<ref bean="initializationHibernateInterceptor"/>
		</property>
	</bean>

	<bean id="hibernateProperties"
		class="org.springframework.beans.factory.config.PropertiesFactoryBean">
		<property name="properties">
			<props>
				<!-- <prop key="hibernate.dialect">${db.messagepoint.hibernate.dialect}</prop> -->
				<prop key="hibernate.dialect">com.prinova.messagepoint.util.MessagepointPostgresDialect</prop>
				<prop key="hibernate.cache.provider_class">org.hibernate.cache.internal.NoCachingRegionFactory</prop>
				<prop key="hibernate.cache.use_second_level_cache">false</prop>
				<prop key="hibernate.show_sql">false</prop>

				<prop key="hibernate.allow_update_outside_transaction">true</prop>

				<prop key="hibernate.multiTenancy">SCHEMA</prop>
				<prop key="hibernate.tenant_identifier_resolver">com.prinova.messagepoint.integrator.MessagepointCurrentTenantIdentifierResolver</prop>
				<prop key="hibernate.multi_tenant_connection_provider">com.prinova.messagepoint.integrator.MessagepointMultiTenantConnectionProvider</prop>
			</props>
		</property>
	</bean>	

    <bean id="initializationHibernateObjectManager" class="com.prinova.messagepoint.model.manager.HibernateObjectManager" >
    	<property name="sessionFactory" ref="initializationSessionFactory" />
    </bean>
    
    <bean id="Comment" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="1" />
		<property name="maxLength" value="1000" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.symbols.space" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9\p{L}\p{Sc}\s_\-'\*.$!+=\?,:;@#\[\]\\/]*+" />
    </bean>
	
	<bean id="Description" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="1" />
		<property name="maxLength" value="255" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.symbols.space" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9\p{L}\p{Sc}\s_\-'\*.$!+=\?,:;@#\[\]\\/]*+" />
    </bean>
	
	<bean id="ObjectName" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="true" />
		<property name="minLength" value="3" />
		<property name="maxLength" value="96" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.space.dash.underscore.apos" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9\p{L}\s_\-']*+" />
    </bean>
	
	<bean id="FileName" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="1" />
		<property name="maxLength" value="255" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.dot.dash.backslash.slash.colon.space" />
		<property name="restrictedCharsRegex" value="^(?!~)[A-Za-z0-9!(){}\s_\-./\\:~]*+" />
    </bean>
	
	<bean id="Password" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="6" />
		<property name="maxLength" value="32" />
		<property name="restrictedCharsList" value="" />
		<property name="restrictedCharsRegex" value="" />
    </bean>
	
	<bean id="PersonName" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="true" />
		<property name="minLength" value="2" />
		<property name="maxLength" value="64" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.space.dot.dash.underscore.apos" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9\p{L}_'\-.\s]*+"/>
    </bean>
	
	<bean id="Email" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="5" />
		<property name="maxLength" value="96" />
		<property name="restrictedCharsList" value="" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9!#$\*\+=\?_\-/\(\)`~']([A-Za-z0-9!#$\*\+=\?\._\-/\(\)`~']?[A-Za-z0-9!#$\*\+=\?_\-/\(\)`~']+)*@[A-Za-z0-9\-]([A-Za-z0-9.\-]?[A-Za-z0-9\-]+)*\.[A-Za-z]{2,}$"/>
		<property name="restrictedCharsErrorCode" value="error.input.email.charrestriction" />
    </bean>
	
	<bean id="Phone" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="3" />
		<property name="maxLength" value="32" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.parenth.dash.space" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9()\-\s]*+" />
    </bean>
	
	<bean id="PostalCode" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="false" />
		<property name="minLength" value="5" />
		<property name="maxLength" value="10" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.dash.space" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9\-\s]*+" />
    </bean>
	
	<bean id="Date" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="true" />
		<property name="minLength" value="6" />
		<property name="maxLength" value="32" />
		<property name="restrictedCharsList" value="" />
		<property name="restrictedCharsRegex" value="" />
    </bean>
	
	<bean id="Username" class="com.prinova.messagepoint.validator.MessagepointInputValidationType">
		<property name="mandatory" value="true" />
		<property name="minLength" value="2" />
		<property name="maxLength" value="32" />
		<property name="restrictedCharsList" value="page.text.validator.alphanum.at.dot.dash.underscore.apos" />
		<property name="restrictedCharsRegex" value="^[A-Za-z0-9_\-@.']*+" />
    </bean>
       
</beans>
