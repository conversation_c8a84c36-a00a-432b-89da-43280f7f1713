
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
	<msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.EDIT %>">
	

		<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />
		
		<msgpt:Stylesheet href="includes/javascript/${tinymceDir}/skins/lightgray/skin.min.css" />

		<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/jquery.colorpicker.js"/>
		<link href="../includes/javascript/jQueryPlugins/colorpicker_new/jquery.colorpicker.css" rel="stylesheet" type="text/css"/>
		<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/swatches/jquery.ui.colorpicker-pantone.js"/>
		<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/parts/jquery.ui.colorpicker-memory.js"/>
		<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/parsers/jquery.ui.colorpicker-cmyk-parser.js"/>
		<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/parsers/jquery.ui.colorpicker-cmyk-percentage-parser.js"/>
		
		<msgpt:Stylesheet href="includes/themes/commoncss/tinyMCEEditorContent.css" />
		<msgpt:Script src="includes/javascript/${tinymceDir}/jquery.tinymce.min.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/tinyMCEinit/jquery.tinyMCEinit.js" />

		<msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js" />
		<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css" />

		<msgpt:Script src="includes/javascript/handlebars/handlebars-v4.7.8.js" />

		<msgpt:Script src="includes/javascript/jQueryPlugins/hoverIntent/jquery.hoverIntent.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.editorActions.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.varTagAttrManager.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.smartTextTagAttrManager.js" />
		<msgpt:Script src="includes/javascript/jQueryPlugins/disableSelection/jquery.disable.text.select.pack.js" />
		
		<style>
			.ui-dialog .ui-dialog-buttonpane {
			    font-size: 13px;
			}
			div.mce-panel .header-label {
				font-weight: bold;
				padding-bottom: 10px;
				font-size: 16px;
			}
			div.mce-panel .property-label {
				line-height: 30px;
				vertical-align: middle;
				width: 125px;
			}

			div.mce-panel .property-value {
				vertical-align: middle;
				text-align: left;
				white-space: nowrap;
			}
			div.mce-panel .ptUnits, .canvasUnits {
				font-size: 12px;
				padding-left: 3px;
			}
			
			.ui-dialog .ui-dialog-buttonpane {
			    font-size: 13px;
			}
			#tab_alttext.mce-active, #altTextInterfaceContainer, #tab_ID.mce-active, #IDInterfaceContainer {
				background-color: #f0f0f0;
			}
			#layoutInterfaceContainer.mce-panel, #conditionalEnableInterfaceContainer {
				background-color: #fff;
			}

			.conditionalEnableItemsContainer {
				padding: 12px;
				height: 390px;
				overflow: auto;
			}
			.conditionalEnableItemTargetTextContainer, .conditionalEnableItemValueTextContainer {
				white-space: normal;
			}
			.conditionalEnableItem {
				border: 1px solid #ccc;
				background-color: #f5f5f5;
				padding: 10px;
				margin-bottom: 3px;
			}
			.itemRemoveButton, .itemEditButton {
				cursor: pointer;
				color: #999;
			}
			.itemRemoveButton:hover, .itemEditButton:hover {
				color: #555;
			}
		</style>

        <msgpt:Script>
            <script>
	            var PX_PER_CM = 39.37007874015748;

                var ed 			= window.parent.tinymce.activeEditor;
                var settings 	= ed.settings;
                var unitScale 	= ed.settings.mp_fn.get_units() == 'cm' ? PX_PER_CM : 100;

				function initSpellCheckLang() {
					if (window.parent && window.parent.WEBSPELLCHECKER != undefined) {
						var wscInstances = window.parent.WEBSPELLCHECKER.getInstances();
						if (wscInstances != undefined && wscInstances != null && wscInstances.length > 0)
							window.spellcheckLang = wscInstances[0].getLang();
					} else {
						setTimeout(function () {
							setSpellCheckLang();
						}, 1000);
					}
				}
				initSpellCheckLang();

                var isMessageContext = ed.settings.is_global_context === false;
	            var editorData = {
	            	content_css						: ed.settings.content_css,
                    async_variables 				: { type : isMessageContext ? 'message' : 'embedded_content' },
					async_embedded_content 			: { type : isMessageContext ? 'message' : 'embedded_content', snippets_only: true },
					async_local_embedded_content 	: { type : 'message', enable : isMessageContext },
					markers_list					: ed.settings.insertvariable_markerlist,
	                applies_text_styles				: false,
	                applies_paragraph_styles		: false,
	                applies_templates				: false,
                    is_global_context				: ed.settings.is_global_context,
                    content_object_id 				: ed.settings.content_object_id,
                    content_menu					: {
									                        applied: ed.settings.content_menu.applied,
									                        mode: ed.settings.content_menu.mode
									                    },
	                can_edit_source					: ${sourceEditPerm}
	            };

				var valueEditorData = {
					content_css						: ed.settings.content_css,
					async_variables 				: { type : isMessageContext ? 'message' : 'embedded_content' },
					async_embedded_content 			: { type : isMessageContext ? 'message' : 'embedded_content' },
					async_local_embedded_content 	: { type : 'message', enable : isMessageContext },
					markers_list					: ed.settings.insertvariable_markerlist,
					applies_text_styles				: false,
					applies_paragraph_styles		: false,
					applies_templates				: false,
					canvas_dimensions				: '5.5:1.5',
					unrestricted_frame_width		: true,
					is_global_context				: ed.settings.is_global_context,
					content_object_id 				: ed.settings.content_object_id,
					content_menu					: {
						applied: ed.settings.content_menu.applied,
						mode: ed.settings.content_menu.mode
					},
					can_edit_source					: ${sourceEditPerm},
					snippet_mode					: true
				};

				// CONDITIONAL ENABLE FUNCTIOLNS
				function initConditionalEnableItem(item) {
					$(item).find('.itemEditButton').click( function() {
						$('#conditionalEnableInterfaceContainer,#addConditionButton,#applyPropertiesButton,#cancelButton').hide();
						$('#conditionalEnableEditInterfaceContainer,#setConditionButton,#conditionItemBackButton').show();

						// INIT: TinyMCE
						if ( !( $('#conditionalEnableInterfaceContainer').find('.mce-tinymce').length > 0
								&& $('#conditionalEnableInterfaceContainer').find('.mce-tinymce').is(':visible') ) ) {
							tinyMCEvalueEditorInit("conditionalEnableTargetEditor", "658", "100", valueEditorData);
						}
						$('#conditionalEnableOperatorSelect').val( $(item).find('.conditionalEnableOperatorItemInput').val() );
						$('#conditionalEnableValueSelect').val( $(item).find('.conditionalEnableValueItemInput').val() );

						function pollForEditorLoaded(conditionItem) {
							if ( $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').length > 0
									&& $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').is(':visible') ) {

								$('#targetConditionalEnableItemIndex').val( parseId(item) );
								tinymce.get('editor_conditionalEnableTargetEditor').setContent( $(conditionItem).find('.conditionalEnableTargetItemInput').val() );

							} else {
								window.setTimeout( function(){ pollForEditorLoaded(conditionItem); }, 100);
							}
						}
						pollForEditorLoaded( $(this).closest('.conditionalEnableItem') );

					});
					$(item).find('.itemRemoveButton').click( function() {
						$(this).closest('.conditionalEnableItem').remove();
						toggleInfoDisplay();
					});
				}

				function generateConditionalEnableItem(valueContent, targetContent, op, itemIndex) {
					var zConditionalEnableItemTemplate = _zConditionalEnableItemTemplate || (_zConditionalEnableItemTemplate = Handlebars.compile($('#conditionalEnableItemTemplate').html()));
					var indData = {
						index			: itemIndex,
						value_content	: $('<div>').append(valueContent).html(),
						target_content	: $('<div>').append(targetContent).html(),
						operator		: op
					};

					var newItem = $(zConditionalEnableItemTemplate(indData))[0];

					$('.conditionalEnableItemsListContainer').append( newItem );

					initConditionalEnableItem( $('.conditionalEnableItemsListContainer').find("[id^='conditionalEnableItem_"+itemIndex+"']") );
				}

				function toggleInfoDisplay() {
					if ( $('.conditionalEnableItemsListContainer .conditionalEnableItem').length != 0 )
						$('.conditionalEnableItemsContainer .addItemInfoContainer').hide();
					else
						$('.conditionalEnableItemsContainer .addItemInfoContainer').showEle('normal');
				}
				// END CONDITIONAL ENABLE INTERFACE INIT


				function initTabs() {
                    $("#elementPropertyTabs [id^='tab_']").each( function() {

                        $(this).click( function() {
                            if ( !$(this).is('.mce-active') ) {
                                $('#elementPropertyTabs .mce-tab').removeClass('mce-active');
                                $(this).addClass('mce-active');

                                $('#layoutInterfaceContainer, #valueInterfaceContainer, #altTextInterfaceContainer, #IDInterfaceContainer,#customGroupInterfaceContainer, #conditionalEnableInterfaceContainer, #addConditionButton, #setConditionButton, #conditionItemBackButton').hide();
                                if ( $(this).attr('id').indexOf('layout') != -1 ) {
                                    $('#layoutInterfaceContainer').show();
                                } else if ( $(this).attr('id').indexOf('alttext') != -1 ) {
                                    $('#altTextInterfaceContainer').show();

                                    // INIT: TinyMCE
                                    if ( !( $('#altTextInterfaceContainer').find('.mce-tinymce').length > 0
                                        && $('#altTextInterfaceContainer').find('.mce-tinymce').is(':visible') ) ) {
                                        tinyMCEvalueEditorInit("inputAltTextEditor", "658", "352", editorData);
                                    }
                                } else if ( $(this).attr('id').indexOf('ID') != -1 ) {
                                    $('#IDInterfaceContainer').show();

                                    // INIT: TinyMCE
                                    if ( !( $('#IDInterfaceContainer').find('.mce-tinymce').length > 0
                                        && $('#IDInterfaceContainer').find('.mce-tinymce').is(':visible') ) ) {
                                        tinyMCEvalueEditorInit("inputIDEditor", "658", "352", editorData);
                                    }
                                } else if ( $(this).attr('id').indexOf('customgroup') != -1 ) {
                                    $('#customGroupInterfaceContainer').show();

                                    // INIT: TinyMCE
                                    if ( !( $('#customGroupInterfaceContainer').find('.mce-tinymce').length > 0
                                        && $('#customGroupInterfaceContainer').find('.mce-tinymce').is(':visible') ) ) {
                                        tinyMCEvalueEditorInit("inputCustomGroupEditor", "658", "352", editorData);
                                    }
								} else if ( $(this).attr('id').indexOf('conditionalEnable') != -1 ) {
									$('#conditionalEnableInterfaceContainer,#addConditionButton').show();
								}
                            }
                        });

                    });

                }

                function initBtn(ele, type) {

                    $(ele)
                        .mouseover( function() {
                            if ( !$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight .actionBtn_toggleSelect]') ) {
                                $(this).removeClass('actionBtn');
                                $(this).addClass('actionBtn_hov');
                            }
                        })
                        .mouseout( function() {
                            if ( !$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight .actionBtn_toggleSelect') ) {
                                $(this).removeClass('actionBtn_hov');
                                $(this).addClass('actionBtn');
                            }
                        });

                    if ( type == "toggle" ) {
                        $(ele)
                            .click( function() {
                                if ( !$(this).hasClass('actionBtn_disabled') ) {
                                    if ( $(this).hasClass('actionBtn_toggleSelectHighlight') )
                                        $(this).removeClass('actionBtn_toggleSelectHighlight').addClass('actionBtn');
                                    else
                                        $(this).removeClass('actionBtn').addClass('actionBtn_toggleSelectHighlight');
                                }

                            });
                    } else if ( type == "button" ) {
                        $(ele)
                            .mousedown( function() {
                                if ( !$(this).hasClass('actionBtn_disabled') )
                                    $(this).removeClass('actionBtn actionBtn_hov').addClass('actionBtn_selected');
                            })
                            .mouseup( function() {
                                if ( !$(this).hasClass('actionBtn_disabled') )
                                    $(this).removeClass('actionBtn_selected').addClass('actionBtn');
                            });
                    }

                }


            	function openTargetingFrame(data, isNew, ele) {
            		var iFramePopup_fullFrameAttr_cust = iFramePopup_fullFrameAttr;
            		iFramePopup_fullFrameAttr_cust.closeBtnId = 'link_Cancel';
            		
            		$(ele).iFramePopup($.extend({
            			src				: ed.settings.content_targeting_url,
            			id				: "contentTargeting",
            			title			: client_messages.title.default_selection_targeting,
            			displayOnInit	: true,
            			appliedParams	: {
            								contentType			: (ed.settings.content_targeting_type != undefined && ed.settings.content_targeting_type != null ? ed.settings.content_targeting_type : "null"),
            								contentTargetingId	: data.content_targeting_id,
            								isNew				: isNew
            							  },
            			screenMask		: false,
            			draggable		: false,
            			onSave			: function() {
            				$(ele).attr('content_targeting_id', data.content_targeting_id);
            				
            				// Remove targeting if no 'play to all'
            				$.ajax({
            					type: "GET",
            					url: context+"/getContentTargeting.form?action=has_targeting&contentTargetingId=" + data.content_targeting_id,
            					dataType: "json",
            					success: function(data) {
            						if ( !data.has_targeting )
            							$(ele).removeAttr('content_targeting_id');
            					}
            				});
            				
            				return true;
            			},
            			beforePopupClose: function(o) { 
            				if ( isNew ) {
            					// Remove targeting icon
            				}
            			}
            		},iFramePopup_fullFrameAttr_cust));		
            	}

                function toggleBorderColorContainer() {
                    if ( $('#borderWidthInput').val().length > 0 && $('#borderWidthInput').val() != 0 )
                        $('#borderColorContainer').showEle('normal')
                    else
                        $('#borderColorContainer').hide();
                }

                function toggleDefaultValueType() {
                    var type = $('#defaultValueTypeSelect').val();
					$('#defaultSelectionTargetingBtn').hide();
					if ( type == '2' ) {
						$('#defaultSelectionTargetingBtn').show();
					}
                }

                function toggleLabelWidthContainers() {
					if ( $('#minimizeWidthToggle').is(':checked') )
						$('#fixedLabelWidthContainer').hide();
					else
						$('#fixedLabelWidthContainer').show();
                }

                function toggleLabelType() {
                    if ( $('#labelTypeSelect').val() == "none" )
                        $('.labelPropertyContainer').hide();
                    else
                        $('.labelPropertyContainer').showEle('normal');
                }

                function toggleGroupSelect() {
                	if ( $('#radioGroupSelect').val() == "C" )
                		$('#tab_customgroup').show();
                	else
                		$('#tab_customgroup').hide();
                }

                function validateTabOrder() {
                    if ( $('#tabOrderInput').val().length == 0 )
                        return;

                    var tabOrderArray = new Array();
                    $(window.parent.tinymce.activeEditor.getBody()).find('.mceFormInput').each( function() {
                        if ( $(this).attr('tab_order') != undefined )
                            tabOrderArray[tabOrderArray.length] = $(this).attr('tab_order');
                    });

                    if ( tabOrderArray.indexOf( $('#tabOrderInput').val() ) != -1 )
                        $('#tabOrderWarningContainer').showEle('normal');
                    else
                        $('#tabOrderWarningContainer').hide();
                }

                // INIT
				var _zConditionalEnableItemTemplate = null;
                $( function() {

                    if ( getTopFrame().MAIN_DEV_MODE() )
                        $(document).find('body')
                            .append("<div style=\"padding: 5px; position: absolute; top: "+($(document).find('body').height()-45)+"px; left: 0px;\">" +
                                "<input id=\"devModeBtn\" type=\"button\" value=\"DEV MODE: RELOAD FRAME\" style=\"width: 150px;\" title=\"Toggle 'devMode' in iFramePopup init\" onclick=\"javascript:window.location.reload()\" />" +
                                "</div>");

                    ed.settings.mp_fn.position_popup();

                    popupFactoryRemove('tinymceCheckboxInit');

                    // INIT TEXT STYLES
                    if ( settings.style_formats != null && settings.style_formats != undefined && settings.style_formats.length != 0 ) {
                        var textStyleOptions = "<option value=\"none\">" + client_messages.content_editor.none + "</option>";
                        for (var i=0; i < settings.style_formats.length; i++)
                            textStyleOptions += "<option value=\"" + settings.style_formats[i].title + "\">" + settings.style_formats[i].title + "</option>";
                        $('#textStyleSelect').html( textStyleOptions );
                    } else {
                        $('#textStylesContainer').remove();
                    }

                    // INIT PARAGRAPH STYLES
                    if ( settings.paragraph_style_data != null && settings.paragraph_style_data != undefined && settings.paragraph_style_data.length != 0 ) {
                        var paragraphStyleOptions = "<option value=\"none\">" + client_messages.content_editor.none + "</option>";
                        for (var i=0; i < settings.paragraph_style_data.length; i++) {
                            var currentStyle = mpParseJSON(settings.paragraph_style_data[i]);
                            paragraphStyleOptions += "<option value=\"" + currentStyle.name + "\">" + currentStyle.name + "</option>";
                        }
                        $('#paragraphStyleSelect').html( paragraphStyleOptions );
                    } else {
                        $('#paragraphStylesContainer').remove();
                    }

                    var targetEle = $(ed.getBody()).find(getParam('type') == 'checkbox' ? '.mceContentSelected.mceCheckboxElement' : '.mceContentSelected.mceRadioElement');
					if ( $(ed.getBody()).find('.mceInlineSelected.mceCheckboxElement').length != 0 || $(ed.getBody()).find('.mceInlineSelected.mceRadioElement').length != 0 )
						targetEle = $(ed.getBody()).find('.mceInlineSelected');
                    if ( targetEle.length == 0 )
                        targetEle = $(ed.selection.getStart()).closest(getParam('type') == 'checkbox' ? '.mceCheckboxElement' : '.mceRadioElement');

                    if ( targetEle.length != 0 ) {

                        // INIT LABEL ATTRIBUTES
                        var labelEle = $(targetEle).find('.mceFormInputLabel');
                        $('#labelTypeSelect').val( labelEle.length != 0 ? $(labelEle).attr('type') : 'none' );

                        if ( labelEle.length != 0 ) {
                            var firstParagraph = $(labelEle).find('p:first');
                            if ( $(firstParagraph).length != 0 && $(firstParagraph).attr('paragraphclass') != undefined )
                                $('#paragraphStyleSelect').val( $(firstParagraph).attr('paragraphclass').replace(settings.paragraph_class_prefix, '') );

                            var firstSpan = $(labelEle).find('p span:first-child');
                            if ( $(firstSpan).length != 0 && $(firstSpan).attr('class') != undefined )
                                $('#textStyleSelect').val( $(firstSpan).attr('class') );

                            if ( $(labelEle).attr('minimize_width') != undefined && $(labelEle).attr('minimize_width') == "true" )
                                $('#minimizeWidthToggle').attr('checked','checked');
                            else
                            	$('#minimizeWidthToggle').removeAttr('checked');

                            var width = Math.round( ($(labelEle).outerWidth() / unitScale) * 100 ) / 100;
                            $('#labelWidthInput').val( width );
                        }
                        
						// INIT ALT TEXT 
                        if ( $(targetEle).find('.mceFormEleAltText').length != 0 ) {
                            var value = $(targetEle).find('.mceFormEleAltText').html();
                            $('#editor_inputAltTextEditor').val(value);
                        }
						// INIT ID 
                        if ( $(targetEle).find('.mceFormEleID').length != 0 ) {
                            var value = $(targetEle).find('.mceFormEleID').html();
                            $('#editor_inputIDEditor').val(value);
                        }
						// INIT CUSTOM GROUP NAME 
                        if ( $(targetEle).find('.mceFormEleGroupName').length != 0 ) {
                            var value = $(targetEle).find('.mceFormEleGroupName').html();
                            $('#editor_inputCustomGroupEditor').val(value);
                        }

						// INIT CONDITIONAL ENABLE ITEMS
						var conditionalEnableItemList = $(targetEle).find('.mceConditionalEnableItems');
						$(conditionalEnableItemList).find('.mceConditionalEnableItem').each( function() {
							var itemIndex = $('.conditionalEnableItemsContainer .conditionalEnableItem').length;
							generateConditionalEnableItem( $(this).find('.mceConditionalEnableValue').html(), $(this).find('.mceConditionalEnableTarget').html(), $(this).attr('operator'), itemIndex );
						});

                        // INIT INPUT ATTRIBUTES
                        var inputEle = $(targetEle).find(getParam('type') == 'checkbox' ? '.mceCheckboxInput' : '.mceRadioInput');

                        $('#checkTypeSelect').val( $(inputEle).attr('type') );

                        var width = Math.round( ($(inputEle).outerWidth() / unitScale) * 100 ) / 100;
                        $('#fieldWidthInput').val( width );

                        var height = Math.round( ($(inputEle).outerHeight() / unitScale) * 100 ) / 100;
                        $('#fieldHeightInput').val( height );

                        if ( $(inputEle).attr('font_size') != undefined )
                            $('#fontSizeInput').val( $(inputEle).attr('font_size') );

                        if ( $(inputEle).attr('border_width') != undefined )
                            $('#borderWidthInput').val( $(inputEle).attr('border_width') );

                        if ( $(inputEle).attr('border_color') != undefined )
                            $('#borderColorInput').val( $(inputEle).attr('border_color') );

                        if ( $(inputEle).attr('radio_group') != undefined )
                            $('#radioGroupSelect').val( $(inputEle).attr('radio_group') );
                        else
                        	$('#radioGroupSelect').val( '1' );

                        if ( $(inputEle).attr('tab_order') != undefined )
                            $('#tabOrderInput').val( $(inputEle).attr('tab_order') );

                        if ( $(inputEle).is('[selected_targeting_id]') ) {
                            $('#defaultSelectionTargetingBtn').attr('content_targeting_id', $(inputEle).attr('selected_targeting_id'));
                            $('#defaultValueTypeSelect').val('2');
                       	} else if ( $(inputEle).is('[is_checked]') ) {
                       		$('#defaultValueTypeSelect').val('1');
                        } else {
                        	$('#defaultValueTypeSelect').val('0');
                        }

                        if ( $(inputEle).attr('deselectable') != undefined && $(inputEle).attr('deselectable') == 'true' )
                        	$('#deselectableToggle').attr('checked','checked');

                        if ( $(inputEle).is('[vertical_align]') )
                            $('#verticalAlignSelect').val( $(inputEle).attr('vertical_align') ); 

                    }

                    if ( $('#tabOrderInput').val() == "" ) {
                        var tabOrder = 0;
                        $(ed.getBody()).find('.mceFormInput').each( function() {
                            if ( $(this).attr('tab_order') != undefined && parseInt($(this).attr('tab_order')) > tabOrder )
                                tabOrder = parseInt($(this).attr('tab_order'));
                        });
                        $('#tabOrderInput').val( tabOrder + 1 );
                    }

                    if ( $('#fieldWidthInput').val() == "" )
                        $('#fieldWidthInput').val(settings.mp_fn.get_units() == 'cm' ? 0.5 : 0.2);
                    if ( $('#fieldHeightInput').val() == "" )
                        $('#fieldHeightInput').val(settings.mp_fn.get_units() == 'cm' ? 0.5 : 0.2);
                    if ( $('#labelWidthInput').val() == "" )
                        $('#labelWidthInput').val(settings.mp_fn.get_units() == 'cm' ? 2 : 1);

                    $('.style_select').each( function() {
                        $(this).styleActionElement({
                            maxItemDisplay: $(this).is('.cap_5') ? 5 : 10
                        });
                    });

                    initBtn($('#groupSelectBtn'), 'button');

                    // UPDATE CHECKBOX: Copy checkbox properties to target checkbox or create new
                    $('#applyPropertiesButton').click( function() {

                        var params = {};

                        params.input_type		= getParam('type');

                        params.label_type 		= $('#labelTypeSelect').val();
                        params.text_style 		= $('#textStyleSelect').length != 0 && $('#textStyleSelect').val() != 0 && $('#textStyleSelect').val() != 'none' ? $('#textStyleSelect').val() : null;
                        params.paragraph_style 	= $('#paragraphStyleSelect').length != 0 && $('#paragraphStyleSelect').val() != 0 && $('#paragraphStyleSelect').val() != 'none' ? $('#paragraphStyleSelect').val() : null;
                        params.label_width 		= $('#labelWidthInput').val().length != 0 ? $('#labelWidthInput').val() * unitScale : null;

                        params.check_type			= $('#checkTypeSelect').val();
                        params.field_width			= $('#fieldWidthInput').length != 0 && $('#fieldWidthInput').val().length != 0 ? $('#fieldWidthInput').val() * unitScale : null;
                        params.field_height			= $('#fieldHeightInput').length != 0 && $('#fieldHeightInput').val().length != 0 ? $('#fieldHeightInput').val() * unitScale : null;
                        params.vertical_align		= $('#labelTypeSelect').val() != 'none' ? $('#verticalAlignSelect').val() : null;
                        params.font_size			= $('#fontSizeInput').val().length != 0 ? $('#fontSizeInput').val() : 12;
                        params.border_width			= $('#borderWidthInput').val().length != 0 ? $('#borderWidthInput').val() : null;
                        params.border_color			= $('#borderColorInput').val();
                        params.tab_order			= $('#tabOrderInput').val().length != 0 ? $('#tabOrderInput').val() : null;
                        params.alt_text				= $('#editor_inputAltTextEditor').val();
                        params.input_id				= $('#editor_inputIDEditor').val();
                        params.selected_targeting_id= ( $('#defaultValueTypeSelect').val() == '2' && $('#defaultSelectionTargetingBtn').is('[content_targeting_id]') ) ? $('#defaultSelectionTargetingBtn').attr('content_targeting_id') : null;
						params.is_checked			= $('#defaultValueTypeSelect').val() == '1';
                        params.minimize_label		= $('#minimizeWidthToggle').length != 0 && $('#minimizeWidthToggle').is(':checked');
                        params.deselectable			= $('#deselectableToggle').length != 0 && $('#deselectableToggle').is(':checked');
						
                        if ( getParam('type') == 'radio' ) {
                            params.radio_group		= $('#radioGroupSelect').val();
                            params.custom_group		= $('#radioGroupSelect').val() == 'C' ? $('#editor_inputCustomGroupEditor').val() : null;
                        }

						var conditionsArray = new Array();
						$('.conditionalEnableItemsContainer .conditionalEnableItem').each( function() {
							conditionsArray[conditionsArray.length] =
									{
										'target' 	: $(this).find('.conditionalEnableTargetItemInput').val(),
										'value'		: $(this).find('.conditionalEnableValueItemInput').val(),
										'operator'	: $(this).find('.conditionalEnableOperatorItemInput').val()
									}
						});
						params.conditional_enable_items = conditionsArray;

                        window.parent.tinymce.activeEditor.execCommand("mceUpdateCheckboxRadioProperties", false, params);

                        window.parent.tinymce.activeEditor.windowManager.close();

                    });

                    toggleLabelType();
                    toggleBorderColorContainer();
                    toggleDefaultValueType();
                    toggleGroupSelect();
                    validateTabOrder();
                    toggleLabelWidthContainers();
					toggleInfoDisplay();

                    $('#cancelButton').click( function() {
                        ed.windowManager.close();
                    });


                    $('.canvasUnits').html(settings.mp_fn.get_units());

                    $('#defaultSelectionTargetingBtn').click( function() {
                        var btn = this;
                		if ( $(btn).attr('content_targeting_id') != undefined && $(btn).attr('content_targeting_id').length != 0 ) {
                			var data = {};
                			data.content_targeting_id = $(this).attr('content_targeting_id');
                			openTargetingFrame(data, false, btn);
                		} else {
                			// Init targeting: Generate new targeting id
                			$.ajax({
                				type: "GET",
                				url: context+"/getContentTargeting.form?action=create_new",
                				dataType: "json",
                				success: function(data) {
                					if ( data.content_targeting_id != undefined )
                						openTargetingFrame(data, true, btn);
                				}
                			});
                		}
                    });

    				var stampDate 	= new Date();
    				var contentTargetingId = $('#defaultSelectionTargetingBtn').attr('content_targeting_id') != undefined && $('#defaultSelectionTargetingBtn').attr('content_targeting_id').length != 0 ?
    						$('#defaultSelectionTargetingBtn').attr('content_targeting_id') : 0;
    				$('#defaultSelectionTargetingBtn').popupFactory({
    					title					: client_messages.title.targeting,
    					popupLocation			: "left",
    					width					: 250,
    					asyncDataType			: "xml",
    					asyncSetContentURL		: context + "/getTargeting.form?type=summary&contentTargetingId=" + contentTargetingId + "&cacheStamp=" + (stampDate.getTime()),
    					asyncSetContentHandler	: function(o, data) {
    												if ($(data).find("content").length > 0)
    													return "<div align=\"left\" style=\"padding: 6px 12px; font-size: 10px;\">" +
    																$(data).find("content").text() +
    															"</div>";
    												else 
    													return "<div align=\"left\" style=\"padding: 6px 12px; font-size: 10px;\">" +
    																client_messages.text.no_targeting_unchecked_for_all_customers + 
    															"</div>";
    											  }
    				});

                    $('#borderColorInput').colorpicker({
                        showOn			: 'both',
                        buttonImageOnly	: true,
                        buttonImage		: '../includes/javascript/jQueryPlugins/colorpicker_new/images/ui-colorpicker.png',
                        modal			: true,
                        parts			: ['map', 'bar', 'hex', 'hsv', 'lab', 'rgb', 'cmyk', 'preview', 'swatches', 'memory', 'footer'],
                        buttonColorize	: true,
                        colorFormat		: 'RGB',
                        showNoneButton	: true,
                        color			: '#000000',
                        open			: function(formatted, colorPicker) {
                            $(colorPicker.colorPicker.dialog).css({'z-index': 3});
                            $('.ui-widget-overlay').css({'z-index': 2});
                        }
                    });

                    // CONDITIONAL ENABLE INIT
					$('#addConditionButton').click( function() {

						$('#conditionalEnableInterfaceContainer,#addConditionButton,#applyPropertiesButton,#cancelButton').hide();
						$('#conditionalEnableEditInterfaceContainer,#setConditionButton,#conditionItemBackButton').show();

						// INIT: TinyMCE
						if ( !( $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').length > 0
								&& $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').is(':visible') ) ) {
							tinyMCEvalueEditorInit("conditionalEnableTargetEditor", "658", "100", valueEditorData);
						}
						$('#conditionalEnableOperatorSelect').val('disable');
						$('#conditionalEnableValueSelect').val('off');

						function pollForEditorLoaded() {
							if ( $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').length > 0
									&& $('#conditionalEnableEditInterfaceContainer').find('.mce-tinymce').is(':visible') &&
									tinymce.get('editor_conditionalEnableTargetEditor') && tinymce.get('editor_conditionalEnableTargetEditor').settings &&
									tinymce.get('editor_conditionalEnableTargetEditor').settings.utils ) {

								$('#targetConditionalEnableItemIndex').val('-1');
								var ciEd = tinymce.get('editor_conditionalEnableTargetEditor');
								ciEd.setContent("<p></p>");
								ciEd.settings.utils.focus();
								ciEd.selection.setCursorLocation($(ciEd.getBody()).find('p:first').get(0), 0);
							} else {
								window.setTimeout( function(){ pollForEditorLoaded(); }, 100);
							}
						}
						pollForEditorLoaded();

					});

					$('#setConditionButton').click( function() {

						function fmtCondition(conditionStr) {
							var returnCondition = $("<div>" + conditionStr + "</div>");
							$(returnCondition).find('*').each( function() {
								if ( $(this).closest('.staticContentItem').length == 0 )
									$(this).contents().unwrap();
							});
							return $(returnCondition).html();
						}

						var targetContent = fmtCondition( $('#editor_conditionalEnableTargetEditor').val() );
						var valueContent = $('#conditionalEnableValueSelect').val();
						var operator = $('#conditionalEnableOperatorSelect').val();

						var conditionItemIndex = $('#targetConditionalEnableItemIndex').val() == -1 ? $('.conditionalEnableItemsListContainer .conditionEnableItem').length : $('#targetConditionalEnableItemIndex').val();

						if ( $('#targetConditionalEnableItemIndex').val() == -1 ) {

							generateConditionalEnableItem(valueContent, targetContent, operator, conditionItemIndex);

						} else {

							$('#conditionalEnableItem_'+conditionItemIndex).find('.conditionalEnableTargetItemInput').val(targetContent);
							$('#conditionalEnableItem_'+conditionItemIndex).find('.conditionalEnableItemTargetTextContainer').html(targetContent);

							$('#conditionalEnableItem_'+conditionItemIndex).find('.conditionalEnableOperatorItemInput').val(operator);
							$('#conditionalEnableItem_'+conditionItemIndex).find('.conditionalEnableItemOperatorTextContainer').html(operator);

							$('#conditionalEnableItem_'+conditionItemIndex).find('.conditionalEnableValueItemInput').val(valueContent);
							$('#conditionalEnableItem_'+conditionItemIndex).find('.conditionalEnableItemValueTextContainer').html(valueContent);

						}

						$('#conditionalEnableEditInterfaceContainer,#setConditionButton,#conditionItemBackButton').hide();
						$('#conditionalEnableInterfaceContainer,#addConditionButton,#applyPropertiesButton,#cancelButton').show();
						toggleInfoDisplay();
					});

					$('#conditionItemBackButton').click( function() {
						$('#conditionalEnableEditInterfaceContainer,#setConditionButton,#conditionItemBackButton').hide();
						$('#conditionalEnableInterfaceContainer,#addConditionButton,#applyPropertiesButton,#cancelButton').show();
					});

					// Sort: Initialize container layer sorting
					$('.sortableList').sortable({
						cancel		: ".disableSorting",
						placeholder	: "sortPlaceholder"
					});
					// END CONDITIONAL ENABLE INIT

                    initTabs();

                });

            </script>
        </msgpt:Script>

	</msgpt:HeaderNew>

	<msgpt:BodyNew type="minimal" cssStyle="border-radius: 0 0 6px 6px; overflow-y: hidden; font-size: 10px;">

		<script id="conditionalEnableItemTemplate" type="text/x-handlebars-template">

			<div class="conditionalEnableItem" id="conditionalEnableItem_{{index}}">
				<table width="100%" cellspacing="0" cellpadding="0" border="0">
					<tr>
						<td style="padding: 4px 12px; vertical-align: middle;" align="left">
							<div>
								<span style="font-size: 12px;"><fmtSpring:message code="page.text.when.input.value.is"/></span>
								<span id="conditionalEnableItemValueTextContainer_{{index}}" style="font-size: 12px; font-style: italic;;" class="conditionalEnableItemValueTextContainer">{{{value_content}}}</span>
								<span id="conditionalEnableItemOperatorTextContainer_{{index}}" style="font-size: 12px; font-style: italic;" class="conditionalEnableItemOperatorTextContainer">{{{operator}}}</span>
								<span style="font-size: 12px;"><fmtSpring:message code="page.text.the.following.named.inputs"/></span>
							</div>

							<input type="hidden" id="conditionalEnableValueItemInput_{{index}}" class="conditionalEnableValueItemInput" value="{{value_content}}" />
							<input type="hidden" id="conditionalEnableOperatorItemInput_{{index}}" class="conditionalEnableOperatorItemInput" value="{{operator}}" />

							<div id="conditionalEnableItemTargetTextContainer_{{index}}" style="font-size: 14px;" class="conditionalEnableItemTargetTextContainer">{{{target_content}}}</div>
							<input type="hidden" id="conditionalEnableTargetItemInput_{{index}}" class="conditionalEnableTargetItemInput" value="{{target_content}}" />

						</td>
						<td width="1%" style="padding: 0px 10px; vertical-align: middle; white-space: nowrap; border-left: 1px solid #bbb;">
							<i class="fa fa-times itemRemoveButton" style="font-size: 16px; margin-right: 15px;"></i>
							<i class="fa fa-pencil itemEditButton" style="font-size: 16px;"></i>
						</td>
					</tr>
				</table>
			</div>

		</script>
	
		<div style="height: 446px;">
		
			<!-- TABS -->
			<div id="elementPropertyTabs" class="mce-container">
				<div class="mce-tabs" >
					<div id="tab_layout" class="mce-tab mce-active" unselectable="on"><fmtSpring:message code="page.label.layout"/></div><div id="tab_alttext" class="mce-tab" unselectable="on"><fmtSpring:message code="page.label.alt.text"/></div><div id="tab_ID" class="mce-tab" unselectable="on"><fmtSpring:message code="${param.type == 'radio' ? 'page.label.value' : 'page.label.name'}"/></div><div id="tab_customgroup" class="mce-tab" unselectable="on" style="display: none;"><fmtSpring:message code="page.label.group.name"/></div><div id="tab_conditionalEnable" class="mce-tab" unselectable="on"><fmtSpring:message code="page.label.conditional.enable"/></div>
				</div>
			</div>
			
			<!-- TAB: LAYOUT -->
			<div class="mce-panel" id="layoutInterfaceContainer" style="height: 100%; padding: 20px 20px;">
			
				<div class="mce-widget header-label">
					<fmtSpring:message code="${param.type == 'radio' ? 'page.label.radio' : 'page.label.checkbox'}" />
				</div>
			
				<div style="white-space: nowrap; vertical-align: top;">
					<!-- CHECKBOX: Left column -->
					<div style="display: inline-block; width: 50%; vertical-align: top;">
						<table cellspacing="0" cellpadding="0" border="0">
							<c:if test="${param.type == 'radio'}">
								<tr>
									<td class="mce-widget property-label" align="left">
										<fmtSpring:message code="page.label.group.name" />
									</td>
									<td class="property-value">
										<div>
											<select id="radioGroupSelect" onchange="toggleGroupSelect()" class="style_select inputS cap_5">
												<option value="C"><fmtSpring:message code="page.label.custom" /></option>
												<option value="1"><fmtSpring:message code="page.label.count.one" /></option>
												<option value="2"><fmtSpring:message code="page.label.count.two" /></option>
												<option value="3"><fmtSpring:message code="page.label.count.three" /></option>
												<option value="4"><fmtSpring:message code="page.label.count.four" /></option>
												<option value="5"><fmtSpring:message code="page.label.count.five" /></option>
												<option value="6"><fmtSpring:message code="page.label.count.six" /></option>
												<option value="7"><fmtSpring:message code="page.label.count.seven" /></option>
												<option value="8"><fmtSpring:message code="page.label.count.eight" /></option>
												<option value="9"><fmtSpring:message code="page.label.count.nine" /></option>
												<option value="10"><fmtSpring:message code="page.label.count.ten" /></option>
											</select>
										</div>
									</td>
								</tr>
							</c:if>
							<tr>
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.type" />
								</td>
								<td class="property-value">
									<div>
										<select id="checkTypeSelect" class="style_select inputS cap_5">
											<option value="check"><fmtSpring:message code="page.label.check.type.check" /></option>
											<option value="circle" ${param.type == 'radio' ? 'selected="selected"' : ''}>
												<fmtSpring:message code="page.label.check.type.circle" />
											</option>
											<option value="cross"><fmtSpring:message code="page.label.check.type.cross" /></option>
											<option value="diamond"><fmtSpring:message code="page.label.check.type.diamond" /></option>
											<option value="square"><fmtSpring:message code="page.label.check.type.square" /></option>
											<option value="star"><fmtSpring:message code="page.label.check.type.star" /></option>
										</select>
									</div>
								</td>
							</tr>
							<tr>
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.width" />
								</td>
								<td class="property-value">
									<div style="display: inline-block;">
										<msgpt:InputFilter type="decimal">
											<input type="text" id="fieldWidthInput" value="" class="inputS" />
										</msgpt:InputFilter>
									</div>
									<div class="canvasUnits" style="display: inline-block;">
									</div>
								</td>
							</tr>
							<c:if test="${param.type == 'checkbox'}">
								<tr>
									<td class="mce-widget property-label" align="left">
										<fmtSpring:message code="page.label.height" />
									</td>
									<td class="property-value">
										<div style="display: inline-block;">
											<msgpt:InputFilter type="decimal">
												<input type="text" id="fieldHeightInput" value="" class="inputS" />
											</msgpt:InputFilter>
										</div>
										<div class="canvasUnits" style="display: inline-block;">
										</div>
									</td>
								</tr>
							</c:if>
							<tr class="labelPropertyContainer">
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.vertical.alignment" />
								</td>
								<td class="property-value">
									<div>
										<select id="verticalAlignSelect" class="style_select inputS cap_5">
											<option value="top"><fmtSpring:message code="page.label.vertical.align.type.top" /></option>
											<option value="middle" selected="selected"><fmtSpring:message code="page.label.vertical.align.type.middle" /></option>
											<option value="bottom"><fmtSpring:message code="page.label.vertical.align.type.bottom" /></option>
										</select>
									</div>
								</td>
							</tr>
							<c:if test="${param.type == 'radio'}">
								<tr>
									<td class="mce-widget property-label" align="left">
										<fmtSpring:message code="page.label.deselectable" />
									</td>
									<td class="property-value">
										<input id="deselectableToggle" type="checkbox" class="checkbox" />
									</td>
								</tr>
							</c:if>
						</table>
					</div>
					<!-- CHECKBOX: Right column -->
					<div style="display: inline-block; width: 50%; vertical-align: top;">
						<table cellspacing="0" cellpadding="0" border="0">
							<tr>
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.font.size" />
								</td>
								<td class="property-value">
									<div style="display: inline-block;">
										<msgpt:InputFilter type="decimal">
											<input type="text" id="fontSizeInput" value="12" class="inputS" />
										</msgpt:InputFilter>							
									</div>
									<div class="ptUnits" style="display: inline-block;">
										pt
									</div>
								</td>
							</tr>
							<tr>
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.tab.order" />
								</td>
								<td class="property-value">
									<div style="display: inline-block;">
										<msgpt:InputFilter type="numeric">
											<input type="text" id="tabOrderInput" onchange="validateTabOrder()" onkeyup="validateTabOrder()" class="inputS" />
										</msgpt:InputFilter>
									</div>
									<div id="tabOrderWarningContainer" style="display: inline-block;">
										<i class="fa fa-info-circle detailTip" style="display: none; padding-left: 6px; color: #de2f19; font-size: 16px;" title="|<div class='detailTipText'>${msgpt:getMessage("page.text.duplicate.tab.order")}</div>"></i>
									</div>
								</td>
							</tr>
							<tr>
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.border.width" />
								</td>
								<td class="property-value">
									<div style="display: inline-block;">
										<msgpt:InputFilter type="decimal">
											<input type="text" id="borderWidthInput" onchange="toggleBorderColorContainer" onkeyup="toggleBorderColorContainer()" value="1" class="inputS" />
										</msgpt:InputFilter>
									</div>
									<div class="ptUnits" style="display: inline-block;">
										pt
									</div>
								</td>
							</tr>
							<tr id="borderColorContainer" style="display: none;">
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.border.color" />
								</td>
								<td class="property-value">
									<input type="text" id="borderColorInput" value="rgb(0,0,0)" style="display: none;" />
								</td>
							</tr>
							<tr>
								<td class="mce-widget property-label">
									<fmtSpring:message code="page.label.default.value.selection" />
								</td>
								<td class="property-value" width="1%" style="white-space: nowrap;">
									<div style="display: inline-block; vertical-align: middle;">
										<select id="defaultValueTypeSelect" onchange="toggleDefaultValueType();" class="style_select inputM">
											<option value="0"><fmtSpring:message code="page.label.unchecked" /></option>
											<option value="1"><fmtSpring:message code="page.label.checked" /></option>
											<option value="2"><fmtSpring:message code="page.label.targeting" /></option>
										</select>
									</div>
									<div style="display: inline-block; vertical-align: middle; min-width: 30px;">
										<div id="defaultSelectionTargetingBtn" class="fa-mp-container" style="display: none;">
											<i class="fa fa-bullseye fa-mp-btn" style="margin-left: 8px; font-size: 24px; cursor: pointer;"></i>
										</div>
									</div>
								</td>
							</tr>
						</table>
					</div>
				</div>
				
				<div class="mce-widget header-label" style="margin-top: 10px;">
					<fmtSpring:message code="page.label.label" />
				</div>
			
				<div style="white-space: nowrap; vertical-align: top;">
					<!-- LABEL: Left column -->
					<div style="display: inline-block; width: 50%; vertical-align: top;">
						<table cellspacing="0" cellpadding="0" border="0">
							<tr>
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.type" />
								</td>
								<td class="property-value">
									<div>
										<select id="labelTypeSelect" onchange="toggleLabelType()" class="style_select inputS">
											<option value="none"><fmtSpring:message code="page.label.no.label" /></option>
											<option value="above" selected="selected"><fmtSpring:message code="page.label.above" /></option>
											<option value="left"><fmtSpring:message code="page.label.left" /></option>
											<option value="right"><fmtSpring:message code="page.label.right" /></option>
										</select>
									</div>
								</td>
							</tr>
							<tr class="labelPropertyContainer">
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.width" />
								</td>
								<td class="property-value">
									<div id="minimizeWidthContainer"> 
										<div style="display: inline-block;">
											<input id="minimizeWidthToggle" checked="checked" type="checkbox" class="checkbox" onclick="toggleLabelWidthContainers()" />
										</div>
										<div style="display: inline-block; font-size:12px; padding-left: 3px;">
											<fmtSpring:message code="page.label.minimize" />
										</div>
									</div>
									<div id="fixedLabelWidthContainer" style="display: none;"> 
										<div style="display: inline-block;">
											<msgpt:InputFilter type="decimal">
												<input type="text" id="labelWidthInput" value="" class="inputS" />
											</msgpt:InputFilter>
										</div>
										<div class="canvasUnits" style="display: inline-block;">
										</div>
									</div>
								</td>
							</tr>
						</table>
					</div>
					<!-- LABEL: Right column -->
					<div style="display: inline-block; width: 50%; vertical-align: top;">
						<table cellspacing="0" cellpadding="0" border="0">
							<tr id="textStylesContainer" class="labelPropertyContainer">
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.text" />
								</td>
								<td class="property-value">
									<div>
										<select id="textStyleSelect" class="style_select inputS">
											<option value="0"><fmtSpring:message code="page.text.loading" /></option>
										</select>
									</div>
								</td>
							</tr>
							<tr id="paragraphStylesContainer" class="labelPropertyContainer">
								<td class="mce-widget property-label" align="left">
									<fmtSpring:message code="page.label.paragraph" />
								</td>
								<td class="property-value">
									<div>
										<select id="paragraphStyleSelect" class="style_select inputS">
											<option value="0"><fmtSpring:message code="page.text.loading" /></option>
										</select>
									</div>
								</td>
							</tr>
						</table>
					</div>
				</div>
				
			</div>

			<!-- TAB: ALT TEXT -->
			<div id="altTextInterfaceContainer" style="display: none;">
			
				<textarea id="editor_inputAltTextEditor" class="mceEditor_inputAltTextEditor" >
				</textarea>
			
			</div> <!-- END altTextInterfaceContainer -->
			
			<!-- TAB: ID -->
			<div id="IDInterfaceContainer" style="display: none;">
			
				<textarea id="editor_inputIDEditor" class="mceEditor_inputIDEditor" >
				</textarea>
			
			</div> <!-- END IDInterfaceContainer -->
			
			<!-- TAB: GROUP -->
			<div id="customGroupInterfaceContainer" style="display: none;">
			
				<textarea id="editor_inputCustomGroupEditor" class="mceEditor_inputCustomGroupEditor" >
				</textarea>
			
			</div> <!-- END customGroupInterfaceContainer -->

			<!-- TAB: CONDITIONAL ENABLE -->
			<div id="conditionalEnableInterfaceContainer" style="display: none;">
				<div style="white-space: nowrap; vertical-align: top;">
					<div class="conditionalEnableItemsContainer">
						<div class="addItemInfoContainer" style="margin: 8px; display: none;">
							<div class="InfoSysContainer_info" style="font-szie: 12px;">
								<fmtSpring:message code="page.text.click.to.add.conditional.enable.criteria" />
							</div>
						</div>

						<div id="conditionalEnableItemsListContainer" class="conditionalEnableItemsListContainer sortableList">
						</div>
					</div>
				</div>
			</div> <!-- END conditionalEnableInterfaceContainer -->

			<div id="conditionalEnableEditInterfaceContainer" style="display: none; position: relative; top: -30px;">

				<div class="mce-container">
					<div class="mce-tabs" >
						<div class="mce-tab" unselectable="on"><fmtSpring:message code="page.label.edit.condition"/></div>
					</div>
				</div>

				<input type="hidden" id="targetConditionalEnableItemIndex" value="" />

				<div style="background-color: #fff; padding: 7px 8px;">
					<div style="display: inline-block; vertical-align: middle; padding-left: 8px;">
						<span style="font-size: 14px;"><fmtSpring:message code="page.text.when.input.value.is"/></span>
					</div>
					<div style="display: inline-block; vertical-align: middle;">
						<select id="conditionalEnableValueSelect" style="font-size: 14px;">
							<option value="on"><fmtSpring:message code="page.label.on"/></option>
							<option value="off"><fmtSpring:message code="page.label.off"/></option>
						</select>
					</div>
					<div style="display: inline-block; vertical-align: middle;">
						<select id="conditionalEnableOperatorSelect" style="font-size: 14px;">
							<option value="enable"><fmtSpring:message code="page.label.enable"/></option>
							<option value="disable"><fmtSpring:message code="page.label.disable"/></option>
						</select>
					</div>
					<div style="display: inline-block; vertical-align: middle; padding-left: 8px;">
						<span style="font-size: 14px;"><fmtSpring:message code="page.text.the.following.named.inputs"/></span>
					</div>
				</div>

				<textarea id="editor_conditionalEnableTargetEditor" class="mceEditor_conditionalEnableTargetEditor" >
				</textarea>

			</div> <!-- END conditionalEnableEditInterfaceContainer -->
			
		</div>
		
		<div class="mce-container mce-panel mce-foot" role="group" tabindex="-1" hidefocus="1" style="border-width: 1px 0px 0px; left: 0px; top: 0px; width: 100%; height: 50px;">
			<div class="mce-container-body mce-abs-layout" style="width: 100%; height: 50px;">
				<div class="mce-abs-end"></div>
				<div id="addConditionButton" class="mce-widget mce-btn mce-abs-layout-item" tabindex="-1" role="button" style="right: 145px; top: 10px; width: 105px; height: 28px; display: none;">
					<button tabindex="-1" type="button" role="presentation" style="height: 100%; width: 100%;"><fmtSpring:message code="page.label.add.condition" /></button>
				</div>
				<div id="applyPropertiesButton" class="mce-widget mce-btn mce-primary mce-first mce-abs-layout-item" tabindex="-1" role="button" style="right: 85px; top: 10px; width: 50px; height: 28px;">
					<button tabindex="-1" type="button" role="presentation" style="height: 100%; width: 100%;"><fmtSpring:message code="page.label.apply" /></button>
				</div>
				<div id="cancelButton" class="mce-widget mce-btn mce-last mce-abs-layout-item" tabindex="-1" role="button" style="right: 10px; top: 10px; width: 64px; height: 28px;">
					<button tabindex="-1" type="button" role="presentation" style="height: 100%; width: 100%;"><fmtSpring:message code="page.label.cancel" /></button>
				</div>
				<div id="setConditionButton" class="mce-widget mce-primary mce-btn mce-abs-layout-item" tabindex="-1" role="button" style="right: 10px; top: 10px; width: 100px; height: 28px; display: none;">
					<button tabindex="-1" type="button" role="presentation" style="height: 100%; width: 100%;"><fmtSpring:message code="page.label.set.condition" /></button>
				</div>
				<div id="conditionItemBackButton" class="mce-widget mce-btn mce-abs-layout-item" tabindex="-1" role="button" style="right: 120px; top: 10px; width: 64px; height: 28px; display: none;">
					<button tabindex="-1" type="button" role="presentation" style="height: 100%; width: 100%;"><fmtSpring:message code="page.label.back" /></button>
				</div>
			</div>
		</div>
	</msgpt:BodyNew>
</msgpt:Html5>