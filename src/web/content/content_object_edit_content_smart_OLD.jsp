<%@page import="com.prinova.messagepoint.model.admin.ContentType" %>
<%@page import="com.prinova.messagepoint.model.content.ContentAssociationType" %>

<%@ include file="../includes/includes.jsp" %>

<c:set var="contentObject" value="${command.contentObject}"/>

<msgpt:Section>

    <msgpt:Stylesheet href="${contentObject.defaultViewCSSFilePath}?cacheStamp=${timeStamp}" standalone="true"/>
    <c:if test="${not empty contentObject.styles}">
        <msgpt:Stylesheet href="${contentObject.CSSFilename}?cacheStamp=${timeStamp}" standalone="true"/>
    </c:if>
    <c:if test="${not empty contentObject.paragraphStyles}">
        <msgpt:Stylesheet href="${contentObject.paragraphCSSFilename}?cacheStamp=${timeStamp}" standalone="true"/>
    </c:if>
    <c:if test="${not empty contentObject.listStyles}">
        <msgpt:Stylesheet href="${contentObject.listCSSFilename}?cacheStamp=${timeStamp}" standalone="true"/>
    </c:if>

    <msgpt:Script src="includes/javascript/jQueryPlugins/iFramePopup/jquery.iFramePopup.js"/>
    <msgpt:Script src="includes/javascript/${tinymceDir}/jquery.tinymce.min.js"/>
    <msgpt:Script src="includes/javascript/jQueryPlugins/tinyMCEinit/jquery.tinyMCEinit.js"/>

    <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
    <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>

    <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
    <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

    <msgpt:Script>
        <script>
            global_data.spellcheck_languages = ${contentObject.spellcheckLanguages};
            $(function () {
                var editorData = {
                    content_css: "${contentObject.defaultEditorCSSFilePath}?cacheStamp=${timeStamp}" +
                        "<c:if test="${!empty contentObject.styles}">,${contentObject.CSSFilename}?cacheStamp=${timeStamp}</c:if>",
                    text_data: ${!empty contentObject.styles ? contentObject.textStyleData : "null"},
                    async_variables: {type: 'embedded_content'},
                    async_embedded_content: {type: 'embedded_content'},
                    markers_list: ${systemVariablesData},
                    smart_canvas_list: ${smartCanvasContentData},
                    applies_text_styles: ${!empty contentObject.styles},
                    applies_paragraph_styles: ${!empty contentObject.paragraphStyles},
                    paragraph_style_css: "<c:if test="${!empty contentObject.paragraphStyles}">${contentObject.paragraphCSSFilename}?cacheStamp=${timeStamp}</c:if>",
                    paragraph_style_data: ${!empty contentObject.paragraphStyles ? contentObject.paragraphStyleData : "null"},
                    applies_list_styles: ${!empty contentObject.listStyles},
                    list_style_css: "<c:if test="${!empty contentObject.listStyles}">${contentObject.listCSSFilename}?cacheStamp=${timeStamp}</c:if>",
                    list_style_data: ${!empty contentObject.listStyles ? contentObject.listStyleData : "null"},
                    applies_templates: ${isCommunicationContent},
                    apply_super_sub: true,
                    channel: ${contentObject.appliedChannels},
                    connector: ${contentObject.appliedConnectors},
                    is_exstream_html: ${contentObject.isExstreamHtml},
                    is_exstream_dxf: ${contentObject.isExstreamDxf},
                    is_exstream_runtime_dxf: ${contentObject.isExstreamRunTimeDxf},
                    is_smart_text: true,
                    can_edit_source: ${sourceEditPerm},
                    marcie_flags: ${marcieFlags},
                    applies_freeform: ${isFreeformCanvas},
                    applies_forms: ${contentObject.appliesForms},
                    applies_barcodes: ${contentObject.appliesBarcodes},
                    applies_images: ${contentObject.appliesImages},
                    applies_tables: ${contentObject.appliesTables},
                    content_menu: {
                        applied: ${isCommunicationOnlyContent || contentObject.appliesContentMenus},
                        mode: 'edit'
                    },
                    is_dynamic_canvas: ${isFreeformCanvas || contentObject.appliesTables},
                    is_translation_compare: ${contentObject.inTranslationStep},
                    canvas_dimensions: "${contentObject.canvasDimensions}",
                    dynamic_canvas_bindings: {
                        max_width: '#canvasMaxWidth',
                        max_height: '#canvasMaxHeight',
                        trim_width: '#canvasTrimWidth',
                        trim_height: '#canvasTrimHeight'
                    },
                    applies_connected_authoring: ${contentObject.appliesConnectedAuthoring},
                    is_global_context: ${contentObject.globalContentObject},
                    content_object_id: ${contentObject.id},
                    isVariableContentEnabled: ${contentObject.isVariableContentEnabled},
                    variant_id: ${contentObject.dynamicVariantEnabled ? -1 : "null" },
                    is_render_as_tagged_text: ${contentObject.renderAsTaggedText},
                    starter_style : "${contentObject.zoneStarterTextStyle}",
                    supports_custom_paragraph: ${contentObject.zone != null ? contentObject.zone.supportsCustomParagraphs : false},
                    table_of_contents_enabled: ${tableOfContentsEnabled}
                };

                <c:choose>
                <c:when test="${contentObject.isMarkup}">
                tinyMCEsimpleTextEditorInit('887', '450', 'MULTILINE', editorData);
                </c:when>
                <c:otherwise>
                tinyMCEinit('887', '450', editorData);
                </c:otherwise>
                </c:choose>
            });

            function setContentAreaWidth(o) {
                $('.editorContentPanel').width(o.toggled ? '700px' : '956px');
                $("#contentData").resize();
            }

            function variantSelected(event, data) {
                goToVariant(data.node.id);
            }

            function contextMenuOptionSelected(optionSelected, selectedItemId) {
                if (optionSelected == 0) {
                    goToVariant(selectedItemId);
                } else if (optionSelected == 1) {
                    doSubmitWithParameter(1, 'continueInstId', selectedItemId);
                } else if (optionSelected >= 2) {
                    $('#parentNode_id').val(selectedItemId);
                    loadVariantPopupIframe(selectedItemId, optionSelected);
                    displayPopup();
                }
            }
        </script>
    </msgpt:Script>

    <msgpt:Script src="includes/javascript/jQueryPlugins/hoverIntent/jquery.hoverIntent.js"/>
    <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>
    <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.editorActions.js"/>
    <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.varTagAttrManager.js"/>
    <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.smartTextTagAttrManager.js"/>

    <msgpt:Script src="includes/javascript/jQueryPlugins/contentEditor/jquery.contentEditor.js"/>
    <msgpt:Script src="includes/javascript/jQueryPlugins/smartTextContentViewer/jquery.smartTextContentViewer.js"/>
    <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/contentEditor/contentEditor.css"/>
    <msgpt:Script src="includes/javascript/pdfObject/pdfobject.min.js"/>

    <msgpt:Script>
        <script>
            function variableMappingResolveAction() {

                var variableIdList = "";
                var documentParm = "${ not empty refDocument ? refDocument.id : -1}";
                var dsIds = "";

                if ($('#variableMapLinkBtnVariableIds').length != 0) {
                    variableIdList = $('#variableMapLinkBtnVariableIds').val();
                    documentParm = $('#variableMapLinkBtnVariableIds').attr('documentid');
                    dsIds = $('#variableMapLinkBtnVariableIds').attr('dsids');
                }

                var mappingPageTargetURL = context +
                    "/dataadmin/bridge_variables.form?tk=" + getParam('tk') +
                    "&variableIds=" + variableIdList +
                    "&documentId=" + documentParm +
                    "&dataSourceIds=" + dsIds;

                if (getParam('contentObjectId') != "") {
                    mappingPageTargetURL = mappingPageTargetURL +
                        "&returnPage=" +
                        escape(context + "/content/content_object_edit.form" + (getParam('contentObjectId') != "" ? "?contentObjectId=" + getParam('contentObjectId') : ""));
                } else {
                    mappingPageTargetURL = mappingPageTargetURL +
                        "&returnPage=" +
                        escape(context + "/content/content_object_edit.form" + (getParam('documentId') != "" ? "?documentId=" + getParam('documentId') : ""));
                }

                window.location.href = mappingPageTargetURL;

            }

            function getMatchIndexes(str, toMatch) {
                var re = new RegExp(toMatch, "g"),
                    indexMatches = [], match;

                while (match = re.exec(str)) {
                    indexMatches.push(match.index);
                }

                return indexMatches;
            }

            function pollContent() {
                var hasEditorLoaded = $('.editorContentPanel').find('.mce-tinymce.mce-container').length != 0;
                if (!hasEditorLoaded)
                    return;

                var startsWithBlockContent = false;
                $("[id^='contentInput_']").each(function () {
                    var currentLang = parseId(this);
                    if ($('<div>' + $(this).val().replaceAll('src=', 'src_block=') + '</div>').children().first().is('table') && !$('#sameAsDefaultCheckbox_' + currentLang).is(':checked'))
                        startsWithBlockContent = true;
                });

                if (startsWithBlockContent) {
                    $("#insertAsParagraphToggleContainer").each(function () {
                        $(this).hide();
                    });
                    $("#insertAsParagraphToggle").prop("checked", true);
                } else {
                    $("#insertAsParagraphToggleContainer").each(function () {
                        $(this).showEle('normal');
                    });
                }
            }

            // Popup Init
            $(function () {
                if ($('#variableMapLinkBtnContainer').length != 0)
                    $('#variableMapLinkBtnContainer').append("<div style=\"padding-top: 8px;\"><input title=\"" + client_messages.button.resolve.variables + "\" type=\"button\" id=\"editVariableMappingBtn\" onclick=\"variableMappingResolveAction();\" style=\"display: none;\" /></div>");

                pollContent();
                setInterval(function () {
                    pollContent();
                }, 100);

                $('#variantSelectTreePanel').snapInPanel({
                    panelId: 'variantSelectTreePanel',
                    panelTitle: client_messages.text.variants,
                    controlTrigger: '#variantSelectTreeSnapInBtn',
                    autoSnap: ${param.fastEdit ? false : true},
                    beforeSnapIn: function () {
                        common.disableElement($('#variantSelectTreeBtn'));
                    },
                    afterSnapOff: function () {
                        common.enableElement($('#variantSelectTreeBtn'));
                    }
                });

                $("input:button").styleActionElement();

                $(".style_menu,.style_select").styleActionElement({'labelAlign': true});

                $("#contentData").contentEditor({
                    text_data:    ${!empty contentObject.styles ? contentObject.textStyleData : "null"},
                    paragraph_data: ${!empty contentObject.paragraphStyles ? contentObject.paragraphStyleData : "null"},
                    contextPath: '${contextPath}',
                    defaultLocaleId: '${defaultLocaleId}',
                    defaultLanguage: '${defaultLanguage}',
                    focusLocaleId: '${focusLocaleId}',
                    globalContentObject: true,
                    editorInitData:    ${contentObject.isMarkup ? 'tinymceEditorDef_simpleText' : 'tinymceEditorDef_standard'},
                    languages:    ${languagesJSON},
                    topPadding: 10,
                    usage: 'edit',
                    size: 'large',
                    isTranslating: ${isTranslating}
                });

            });

        </script>
    </msgpt:Script>
    <div class="row no-gutters fullscreenBoundary">
        <div class="col-3 d-flex flex-column">
            <c:if test="${contentObject.dynamicVariantEnabled}">
                <div class="d-flex justify-content-start align-items-center mb-3">
                    <div class="btn-group mr-2">
                        <button id="variantSelectTreeBtn" type="button"
                                class="btn btn-dark btn-sm dropdown-toggle"
                                data-toggle="dropdown" aria-haspopup="true"
                                aria-expanded="false">
                            <fmtSpring:message
                                    code="page.label.variants"/>
                        </button>
                        <div class="dropdown-menu px-3"
                             aria-labelledby="variantTreeBtn">
                            <div class="dropdown-content dropdown-active">
                                <div id="variantSelectTreePanel">
                                    <div class="clearfix py-2">
                                        <msgpt:VariantTree id="variantSelect"
                                                           selectedNodeId="-9"
                                                           style="min-width: 18rem;"
                                                           cssClass="variantSelectionContainer w-100"
                                                           expanded="true"
                                                           dataBinding="${contentObject}"
                                                           onChange="variantSelected"
                                                           showContentStatus="true"
                                                           canUpdate="true"
                                                           onContextMenuSelect="contextMenuOptionSelected"/>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-2 pt-2 pb-1 border-top text-right">
                                <a id="variantSelectTreeSnapInBtn"
                                   class="d-inline-block text-uppercase text-muted fs-xs"
                                   href="#"
                                   role="button">
                                    <fmtSpring:message
                                            code="client_messages.text.snap_out"/><i
                                        class="far fa-external-link-square-alt fa-lg ml-2"
                                        aria-hidden="true"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>
            <div class="flex-grow-1 p-3 bg-lightest rounded">
                <!-- Language Selection -->
                <div id="languageSelectionSection" class="d-none">
                    <div class="mb-3 pb-2 border-bottom font-weight-bold">
                        <fmtSpring:message
                                code="client_messages.text.language"/>
                    </div>
                    <div class="input-group input-group-sm mb-4">
                        <select class="custom-select custom-select-sm" id="contentLanguageSelect"
                                aria-label="${msgpt:getMessage('client_messages.content_editor.content_language')}"></select>
                    </div>
                </div>
                <!-- Same as default language -->
                <div id="same-as-default-language" class="mt-4"></div>
                <!-- Compound Variable Format Type -->
                <c:if test="${not contentObject.isFreeform && (not contentObject.isGlobalContentObject || not contentObject.isMarkup) && not contentObject.inTranslationStep}">
                    <div class="mb-3 pb-2 border-bottom font-weight-bold">
                        <fmtSpring:message code="page.label.settings.for.all.languages"/>
                    </div>
                    <div class="form-group mb-3">
                        <label for="compoundVariableFormatSelect">
                            <fmtSpring:message code="page.label.compound.variable.format"/>
                        </label>
                        <form:select id="compoundVariableFormatSelect" cssClass="custom-select custom-select-sm"
                                     path="contentObject.compVarFormatType">
                            <c:forEach items="${compVarFormatTypes}" var="compVarFormatType">
                                <form:option value="${compVarFormatType.id}"
                                             label="${compVarFormatType.name}"/>
                            </c:forEach>
                        </form:select>
                    </div>
                    <c:if test="${not contentObject.isMarkup}">
                        <div id="insertAsParagraphToggleContainer"
                             class="mb-2 custom-control custom-switch">
                            <form:checkbox id="insertAsParagraphToggle"
                                           path="contentObject.insertAsBlockContent"
                                           cssClass="custom-control-input"/>
                            <label class="custom-control-label" for="insertAsParagraphToggle">
                                <fmtSpring:message code="page.label.insert.as.paragraph"/>
                            </label>
                        </div>
                        <div id="contentTrimToggleContainer" class="form-group mb-3">
                            <label for="contentTrimTypeSelect">
                                <fmtSpring:message code="page.label.trim.empty.paragraphs"/>
                            </label>
                            <form:select id="contentTrimTypeSelect" cssClass="custom-select custom-select-sm"
                                         path="contentObject.contentTrimType">
                                <c:forEach items="${contentTrimTypes}" var="contentTrimType">
                                    <form:option value="${contentTrimType.id}"
                                                 label="${contentTrimType.name}"/>
                                </c:forEach>
                            </form:select>
                        </div>
                    </c:if>
                </c:if>
            </div>
        </div>
        <div class="col-9 pl-4">
            <div id="editorPanel" class="editorContentPanel contentPanel">
                <div class="dynamicCanvasBindings" style="display: none;">
                    <form:input path="canvasMaxWidth" id="canvasMaxWidth"/>
                    <form:input path="canvasMaxHeight" id="canvasMaxHeight"/>
                    <form:input path="canvasTrimWidth" id="canvasTrimWidth"/>
                    <form:input path="canvasTrimHeight" id="canvasTrimHeight"/>
                </div>
                <div id="contentData" globalState="edit"
                     globalRefObjLabel="UNUSED"
                     style="display: none;">
                    <div class="contentGroup"
                         groupId="embeddedContent${contentObject.id}"
                         groupState="inherit"
                         canvasDimensions="${contentObject.canvasDimensions}"
                         content="text">
                        <c:forEach var="locale" items="${locales}"
                                   varStatus="langStat">
                            <c:set var="contentState" value="inherit"/>
                            <c:if test="${command.contents[locale.id].sameAsDefault}">
                                <c:set var="contentState" value="sameAsDefault"/>
                            </c:if>

                            <div class="contentEntry"
                                 contentState="${contentState}"
                                 localeId="${locale.id}">
                                <c:if test="${locale.id != defaultLocaleId}">
                                    <form:checkbox
                                            id="sameAsDefaultCheckbox_${locale.id}"
                                            path="contents[${locale.id}].sameAsDefault"
                                            cssClass="sameAsDefaultInput custom-control-input"/>
                                </c:if>
                                <div class="textContentInput">
                                    <form:textarea class="${locale.id == defaultLocaleId ? 'defaultLocaleContent' : ''}"
                                                   id="contentInput_${locale.id}"
                                                   path="contents[${locale.id}].content"/>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </div>
            </div>
        </div>
    </div>
</msgpt:Section>