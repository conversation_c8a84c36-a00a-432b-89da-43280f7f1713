<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.reports" extendedScripts="true">

        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:CalendarIncludes/>


        <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>

        <msgpt:Script>
            <script>

                var $updateBtn, $deleteBtn, $reRunBtn;

                $(function () {

                    $updateBtn = $('#updateBtn');
                    $deleteBtn = $('#deleteBtn');
                    $reRunBtn = $('#reRunBtn');

                    validateActionReq();

                    $('#addMsgDelReportBtn').iFramePopup($.extend({
                        src: context + "/reports/scenario_edit.form",
                        appliedParams: {tk: "${param.tk}"},
                        beforePopup: function (inst) {
                            if ($('#addMsgDelReportBtn').hasClass('highlightedBtnDisabled'))
                                return false;
                        },
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr));

                    $('#addTpDelReportBtn').iFramePopup($.extend({
                        src: context + "/reports/scenario_tp_delivery_edit.form",
                        appliedParams: {tk: "${param.tk}"},
                        beforePopup: function (inst) {
                            if ($('#addTpDelReportBtn').hasClass('highlightedBtnDisabled'))
                                return false;
                        },
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr));

                    $('#addBatchReportBtn').iFramePopup($.extend({
                        src: context + "/reports/scenario_operations_edit.form",
                        appliedParams: {tk: "${param.tk}"},
                        beforePopup: function (inst) {
                            if ($('#addBatchReportBtn').hasClass('highlightedBtnDisabled'))
                                return false;
                        },
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr));
                });

                // Report list validation
                function validateActionReq(reportId) {
                    var singleSelect = true;
                    var canDelete = true;
                    var canUpdate = true;
                    var inProcess = false;

                    // Resolve selection flags
                    if ($("input[id^='listItemCheck_']:checked").length != 1) {
                        singleSelect = false;
                    }
                    $("input[id^='listItemCheck_']:checked").each(
                        function () {
                            var reportId = this.id.replace('listItemCheck_', '');
                            if (!exists('canDelete_' + reportId)) {
                                canDelete = false;
                            }
                            if (exists('canUpdate_' + reportId)) {
                                canUpdate = true;
                            }
                            if (exists('inProcess_' + reportId)) {
                                inProcess = true;
                            }
                        }
                    );

                    //Disable all context menu entries
                    $('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');
                    $('ul.contextMenu').find("a[id^='actioniFrame']").addClass('disabled');
                    common.disableElement($updateBtn);
                    common.disableElement($deleteBtn);
                    common.disableElement($reRunBtn);

                    if ($("input[id^='listItemCheck_']:checked").length >= 1) {
                        if (singleSelect) {
                            if (canUpdate) {
                                $('a#actioniFrame_1').removeClass('disabled');
                                common.enableElement($updateBtn);
                            }
                        }
                        if (canUpdate) {	// Re-run report
                            $('a#actionOption_3').removeClass('disabled');
                            common.enableElement($reRunBtn);
                        }
                        if (canDelete) { // Delete report
                            $('a#actionOption_2').removeClass('disabled');
                            common.enableElement($deleteBtn);
                        }
                    }
                }

                // List actions: Edit

                function iFrameAction(actionId) {
                    var scenarioid, typeid;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        scenarioid = this.id.replace('listItemCheck_', '');
                    });

                    typeid = $('#actionLabel_' + scenarioid).attr('itemtype');
                    var scenarioEditForm;
                    switch (typeid) {
                        case '1':
                            scenarioEditForm = 'scenario_edit.form';
                            break;
                        case '2':
                            scenarioEditForm = 'scenario_tp_delivery_edit.form';
                            break;
                        case '3':
                            scenarioEditForm = 'scenario_operations_edit.form';
                            break;
                    }
                    if (actionId == '1') {
                        $('#actioniFrame_' + actionId).iFramePopup($.extend({
                            src: context + "/reports/" + scenarioEditForm,
                            displayOnInit: true,
                            id: "testFrame",
                            appliedParams: {'tk': "${param.tk}", 'scenarioid': scenarioid},
                            beforePopupClose: function () {
                                rebuildListTable();
                            }
                        }, iFramePopup_fullFrameAttr));
                    }
                }

                function iFrameView(path, eleId, e) {
                    if (e != undefined) {
                        if (!e.shiftKey && !e.ctrlKey && !e.metaKey) {
                            $("#" + eleId).iFramePopup($.extend({
                                src: path,
                                displayOnInit: true,
                                id: "reportFrame",
                                appliedParams: {tk: "${param.tk}"},
                                beforePopupClose: function () {
                                    rebuildListTable();
                                }
                            }, iFramePopup_fullFrameAttr));
                        }
                        e.preventDefault ? e.stopPropagation() : e.returnValue = false;
                    }
                }

                function toggleFilter(select) {
                    rebuildListTable();
                }

                function rebuildListTable() {
                    $('#reportList').DataTable().ajax.reload(null, true);
                }

                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    obj.columns = [
                        {
                            columnMap: 'name',
                            columnName: client_messages.text.name,
                            sort: true,
                            width: '30%',
                            colVisToggle: false
                        },
                        {
                            columnMap: 'type',
                            columnName: client_messages.text.type,
                            sort: true,
                            width: '20%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'status',
                            columnName: client_messages.text.status,
                            sort: true,
                            width: '15%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'date',
                            columnName: client_messages.text.date,
                            sort: true,
                            width: '15%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'user',
                            columnName: client_messages.text.user,
                            sort: true,
                            width: '20%',
                            colVisToggle: true
                        }
                    ];
                    return obj;
                }

                function getAsyncExtParams() {
                    var stampDate = new Date();
                    var obj = [
                        {"name": "listTableType", "value": "13"},
                        {"name": "displayMode", "value": "full"},
                        {"name": "documentId", "value": getParam('documentId') != "" ? getParam('documentId') : -1},
                        {"name": "reportsAssignmentFilterId", "value": $('#reportsListAssignmentFilter').val()},
                        {"name": "reportsTypeFilterId", "value": $('#reportsListTypeFilter').val()},
                        {"name": "reportsStatusFilterId", "value": $('#reportsListStatusFilter').val()},
                        {"name": "globalContext", "value": $("#globalContextContainer").is('.globalContextEnabled')},
                        {"name": "cacheStamp", "value": stampDate.getTime()}
                    ];
                    return obj;
                }

                function postListRenderFlagInjection(oObj) {
                    var binding = oObj.aData.binding;
                    var text = oObj.aData.name;

                    //Selection check select
                    text += binding;

                    //Selection permission flags
                    if (oObj.aData.flags.canUpdate)
                        text += "<input type='hidden' id='canUpdate_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canDelete)
                        text += "<input type='hidden' id='canDelete_" + oObj.aData.dt_RowId + "' />";

                    return text;
                }

                // *********  LIST TABLE FUNCTIONS: END  *********

                // Retrieve selected ids seperated by ','
                function getSelectedIds() {
                    var selectedIds = '';
                    var selectedCount = $("input[id^='listItemCheck']:checked").length;
                    var count = 0;
                    $("input[id^='listItemCheck']:checked").each(
                        function () {
                            selectedIds += this.id.replace('listItemCheck_', '');
                            count++;
                            if (count != selectedCount)
                                selectedIds += '_';
                        }
                    );
                    return selectedIds;
                }

            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>

    <msgpt:BodyNew>
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_REPORTS %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_REPORTS %>"/>
        <msgpt:ContextBarNew globalContextApplied="true" languageContextApplied="false"/>
        <msgpt:LowerContainer fullPanel="true">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel>
                <c:if test="${not trashTpContext }">
                    <form:form method="post" modelAttribute="command">
                        <form:errors path="*">
                            <msgpt:Information errorMsgs="${messages}" type="error"/>
                        </form:errors>
                        <h1 class="h4 pb-2 mb-4"><fmtSpring:message code="page.label.reports"/></h1>
                        <div class="box-shadow-4 rounded bg-white p-4">
                            <div class="px-2 py-1">
                                <div class="mb-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <form:hidden path="actionValue" id="actionElement"/>
                                        <msgpt:IfAuthGranted authority="ROLE_REPORT_EDIT">
                                            <div class="dropdown mr-3">
                                                <button class="dropdown-toggle btn btn-primary" type="button"
                                                        id="addReportMenu" data-toggle="dropdown"
                                                        aria-haspopup="true"
                                                        aria-expanded="false">
                                                    <i class="far fa-plus-circle mr-2"
                                                       aria-hidden="true"></i>
                                                    <fmtSpring:message code="page.label.add"/>
                                                </button>
                                                <div class="dropdown-menu" aria-labelledby="addReportMenu">
                                                    <button id="addMsgDelReportBtn" class="dropdown-item"
                                                            type="button">
                                                        <i class="far fa-file-alt mr-2 fs-md"
                                                           aria-hidden="true"></i>
                                                        <c:out value='${msgpt:getMessage("page.label.add.message.delivery.report")}'/>
                                                    </button>
                                                    <button id="addTpDelReportBtn" class="dropdown-item"
                                                            type="button">
                                                        <i class="far fa-file-alt mr-2 fs-md"
                                                           aria-hidden="true"></i>
                                                        <c:out value='${msgpt:getMessage("page.label.add.touchpoint.delivery.report")}'/>
                                                    </button>
                                                    <button id="addBatchReportBtn" class="dropdown-item"
                                                            type="button">
                                                        <i class="far fa-file-alt mr-2 fs-md"
                                                           aria-hidden="true"></i>
                                                        <c:out value='${msgpt:getMessage("page.label.add.batch.report")}'/>
                                                    </button>
                                                </div>
                                            </div>
                                        </msgpt:IfAuthGranted>
                                        <div class="btn-group border-separate mr-auto" role="group"
                                             aria-label="${msgpt:getMessage("page.label.actions")}">
                                            <button id="updateBtn" type="button" class="btn btn-dark"
                                                    onclick="iFrameAction(1);" disabled>
                                                <i class="far fa-edit mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="action.button.label.update"/>
                                            </button>
                                            <button id="deleteBtn" type="button" class="btn btn-dark"
                                                    onclick="actionSelected(2);" disabled>
                                                <i class="far fa-trash-alt mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="page.label.delete"/>
                                            </button>
                                            <button id="reRunBtn" type="button"
                                                    class="btn btn-dark rounded-right"
                                                    onclick="actionSelected(3);" disabled>
                                                <i class="far fa-repeat mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="page.label.rerun"/>
                                            </button>
                                        </div>
                                        <div class="mr-3 colVisToggleContainer" data-toggle="tooltip"
                                             title="${msgpt:getMessage('client_messages.label.toggle_columns')}">
                                        </div>
                                        <div class="form-group position-relative d-inline-block m-0">
                                            <label for="listSearchInput" class="sr-only"><fmtSpring:message
                                                    code="page.label.search"/></label>
                                            <i class="far fa-search ml-3 position-absolute text-dark top-30"
                                               style="z-index: 1;" aria-hidden="true"></i>
                                            <msgpt:InputFilter type="description">
                                                <input id="listSearchInput" type="text" size="25"
                                                       class="form-control bg-lightest has-control-l border-0"
                                                       placeholder="${msgpt:getMessage('page.label.search')}"/>
                                            </msgpt:InputFilter>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center pt-1">
                                        <span class="text-dark mr-1" id="filter">
                                            <fmtSpring:message code="page.label.filter"/>:
                                        </span>
                                        <div class="mx-2">
                                            <select id="reportsListAssignmentFilter" aria-labelledby="filter"
                                                    data-toggle="complex-dropdown"
                                                    data-menu-class="dropdown-custom mt-2"
                                                    class="complex-dropdown-select persistedValue"
                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                    onchange="toggleFilter(this)">
                                                <c:forEach items="${reportAssignmentFilterTypes}"
                                                           var="currentFilter">
                                                    <option id="reportsListAssignmentFilterOption_${currentFilter.id}"
                                                            value="${currentFilter.id}"><fmtSpring:message
                                                            code="${currentFilter.displayMessageCode}"/></option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                        <div class="mx-2">
                                            <select id="reportsListTypeFilter" aria-labelledby="filter"
                                                    data-toggle="complex-dropdown"
                                                    data-menu-class="dropdown-custom mt-2"
                                                    class="complex-dropdown-select persistedValue"
                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                    onchange="toggleFilter(this)">
                                                <c:forEach items="${reportTypeFilterTypes}"
                                                           var="currentFilter">
                                                    <option id="reportsListTypeFilterOption_${currentFilter.id}"
                                                            value="${currentFilter.id}"><fmtSpring:message
                                                            code="${currentFilter.displayMessageCode}"/></option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                        <span class="text-dark mr-1" id="report-status">
                                            <fmtSpring:message code="page.text.reports.which.are"/>
                                        </span>
                                        <div class="ml-2">
                                            <select id="reportsListStatusFilter"
                                                    aria-labelledby="report-status"
                                                    data-toggle="complex-dropdown"
                                                    data-menu-class="dropdown-custom mt-2"
                                                    class="complex-dropdown-select persistedValue"
                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                    onchange="toggleFilter(this)">
                                                <option id="reportsListStatusFilter_0" value="0">
                                                    <fmtSpring:message
                                                            code="page.label.any.status"/></option>
                                                <c:forEach items="${reportStatusFilterTypes}"
                                                           var="currentFilter">
                                                    <option id="reportsListStatusFilter_${currentFilter.id}"
                                                            value="${currentFilter.id}"><fmtSpring:message
                                                            code="${currentFilter.displayMessageCode}"/></option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white">
                                    <msgpt:DataTable id="reportList" listHeader="page.label.reports" async="true"
                                                     columnReorder="true" numUnreorderableCols="2"
                                                     columnVisibility="true"
                                                     drillDown="false" multiSelect="true" searchFilter="true">
                                    </msgpt:DataTable>
                                </div>
                            </div>
                        </div>

                        <!-- POPUP DATA -->
                        <div id="actionSpecs" style="display: none;">
                            <!-- ACTIONS POPUP DATA -->
                            <div id="actionSpec_2" type="simpleConfirm" submitId="2"> <!-- Delete report scenario -->
                                <div id="actionTitle_2"><fmtSpring:message
                                        code="page.label.confirm.delete.report"/></div>
                                <div id="actionInfo_2"><p><fmtSpring:message code="page.text.delete.reports"/></p></div>
                            </div>
                        </div>

                        <!-- POPUP INTERFACE -->
                        <msgpt:Popup id="actionPopup" theme="minimal">
                            <div id="actionPopupInfoFrame">
                                <div id="actionPopupInfo">&nbsp;</div>
                            </div>
                            <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                                <span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                  label="page.label.cancel"
                                                                                                  disabled="true"/></span>
                                <span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                                          label="page.label.cancel"/></span>
                                <span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                    label="page.label.continue"
                                                                                                    disabled="true"/></span>
                                <span id="continueBtnEnabled"><msgpt:Button URL="#" label="page.label.continue"
                                                                            primary="true"/></span>
                            </div>
                        </msgpt:Popup>
                    </form:form>
                </c:if>

                <c:if test="${trashTpContext}">
                    <div class="InfoSysContainer_info">
                        <i class="fa icon fa-info-circle" aria-hidden="true"></i>
                        <p><fmtSpring:message code="page.text.trash.touchpoint.does.not.apply"/></p>
                    </div>
                </c:if>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
        <msgpt:ContextMenu name="reportList">
            <msgpt:ContextMenuEntry name="actioniFrame_1" link="#iFrameAction:1"><fmtSpring:message
                    code="page.label.edit"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_2" link="#actionSelected:2"><fmtSpring:message
                    code="page.label.delete"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_3" link="#actionSelected:3"><fmtSpring:message
                    code="page.label.rerun"/></msgpt:ContextMenuEntry>
        </msgpt:ContextMenu>
    </msgpt:BodyNew>
</msgpt:Html5>