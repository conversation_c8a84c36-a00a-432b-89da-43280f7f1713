
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.reports" extendedScripts="true" viewType="<%= MessagepointHeader.ViewType.EDIT %>">


    <msgpt:Script>
        <script>
            $( function() {
                $("input:button").styleActionElement();
            });
        </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:BodyNew>
	<msgpt:BannerNew  edit="true" type="full" />

	<!-- Report Control Bar -->
	<div style="border: 1px solid #bbb; border-left: none; border-right: none; background-color: #eee; padding: 5px;">
		<table width="100%" cellspacing="0" cellpadding="0" border="0" class="innerContentTable">
			<tr>
				<td width="98%" style="padding: 0px; vertical-align: middle;"/>
				<td width="1%" align="right" style="padding: 0px; vertical-align: middle;">
					<input title="${msgpt:getMessage('page.label.PRINT')}" type="button" onclick="window.print()" style="display: none;" />
				</td>
				<td width="1%" align="right" style="padding: 0px; vertical-align: middle;">
					<input title="${msgpt:getMessage('page.label.CLOSE')}" type="button" onclick="window.close();" style="display: none;" />
				</td>
			</tr>
		</table>
	</div>

	<msgpt:LowerContainer>
		<msgpt:ContentPanel>
			<msgpt:ContentData titleString='${msgpt:getMessage("page.label.customer")} ${msgpt:getMessage("page.label.delivery")} ${msgpt:getMessage("page.label.report")}'>
				<form:form cssClass="formNoMargin" name="editForm">
					<form:errors path="*">
						<msgpt:Information errorMsgs="${messages}" type="error" />
					</form:errors>
					<msgpt:CalculatePageNavigation itemsName="reports" page="${param.page}" pageSize="${param.pagesize}" maxPages="${param.maxpages}" outputName="reports"/>
					<msgpt:DataTable multiColumn="true" labelWidths="20%" contentWidth="20%">
						<msgpt:TableHeader label="page.label.customer.report.summary" />
						<msgpt:TableItem label="page.label.generated.by">
							<c:out value="${scenario.updatedByName}" />
						</msgpt:TableItem>
						<msgpt:TableItem label="page.label.actual.deliveries">
							<c:out value="${requestScope.totalItems}" />
						</msgpt:TableItem>
						<msgpt:TableItem label="page.label.date">
							<fmtJSTL:formatDate value="${scenario.updated}" pattern="${dateTimeFormat}" />
						</msgpt:TableItem>
						<msgpt:TableItem>
							<c:out value=" "/>
						</msgpt:TableItem>
						<msgpt:TableItem label="page.label.time">
							<fmtJSTL:formatDate value="${scenario.updated}" pattern="${timeFormatInput}" />
						</msgpt:TableItem>
					</msgpt:DataTable>
					
					<!-- Filter -->
					<table style="width: 55%; margin-left: 22px;" summary="Page Form" cellspacing="0" cellpadding="0" border="0" class="filterContent">
					    <tbody class="contentTableContentTREven" style="padding: 6px;">
						    <tr>
								<td style="padding-top: 5px;" width="35%" align="right">${msgpt:getMessage("page.label.customerId")}</td>
								<td style="padding-top: 5px;" align="left">
									<msgpt:InputFilter type="simpleTextNoApos">
										<form:input path="customerId" cssClass="inputL"/> 
									</msgpt:InputFilter>
								</td>
							</tr>
							<tr>
								<td align="right">${msgpt:getMessage("page.label.customerfield.first") }</td>
								<td align="left">
									<msgpt:InputFilter type="filterField">
										<form:input path="field1" cssClass="inputL"/> 
									</msgpt:InputFilter>
								</td>
							</tr>
							<tr>
								<td align="right">${msgpt:getMessage("page.label.customerfield.second") }</td>
								<td align="left">
									<msgpt:InputFilter type="filterField">
										<form:input path="field2" cssClass="inputL"/> 
									</msgpt:InputFilter>
								</td>	
							</tr>	 	
							 <tr>
							 	<td>&nbsp;</td>
							 	<td style="padding: 7px;" align="left">			
					 				<msgpt:Button label="page.label.filter.customers" URL="javascript:doSubmit()" flowControl="true"/>
								</td>
					 		</tr>
						</tbody>
					</table>
					<!-- Pagination -->
					<msgpt:OutputPageNavigation page="${param.page}" pageSize="${param.pagesize}" maxPages="${param.maxpages}">
						<c:set var="params" value="scenarioid=${param.scenarioid}&customerId=${param.customerId}&field1=${param.field1}&field2=${param.field2}" />
						<msgpt:URLBuilder page="/reports/scenario_customer_report_view.form?${msgpt:cleanEmptyParams(params)}" maintainQueryStringParms="true" queryStringParms="${allTreeParameters}" />
					</msgpt:OutputPageNavigation>
						
					<msgpt:DataTable listHeader="page.label.customer.report.details">
						<c:choose>
							<c:when test="${empty reports }">
								<msgpt:TableItem>
									<c:out value='${msgpt:getMessage("page.label.no.customers.report")}' />
								</msgpt:TableItem>
							</c:when>
							<c:otherwise>
							    <c:forEach var="customerTouchpointValuePair" items="${reports}" varStatus="loopStatus">
								   <msgpt:QueryHibernateObjects hqlQuery="from Customer c where c.customerId = '${customerTouchpointValuePair[0]}' AND c.jobId = ${customerTouchpointValuePair[1]}" outputName="customers" />
					   			   <msgpt:TableListGroup>
										<msgpt:TableElement label="page.label.customerId">
											<a href="javascript:popItUp('reports_customer_sub.jsp?scenarioid=${param.scenarioid}&customerid=${msgpt:forHrefPercent(customerTouchpointValuePair[0], true)}&jobid=${customerTouchpointValuePair[1]}')">
												<div class="reportIcon" style="float: left; padding-top: 0px;  height: 14px;">
												<c:out value="${customers[0].customerId}"/>
												</div>
									      	</a>&nbsp;
			   	      					</msgpt:TableElement>

										<c:set var="varA" value=""/>
										<c:set var="varB" value=""/>
							         	<c:forEach var="customerField" items="${customers[0].customerFieldsFromDB}" varStatus="customerFieldCount">
											<c:if test="${customerField.field == '21' }" > <!-- FROM Variable A -->
												<c:set var="varA" value="${customerField.value}"/>
											</c:if>
											<c:if test="${customerField.field == '22' }" > <!-- FROM Variable B -->
												<c:set var="varB" value="${customerField.value}"/>
											</c:if>
					         		   	</c:forEach>
					         		   	<c:if test="${customerDisplayNameOfReportingVariableA != 'NULL'}">
											<msgpt:TableElement label="${customerDisplayNameOfReportingVariableA}">
												<c:out value="${ varA }" />
											</msgpt:TableElement>			         		   	
					         		   	</c:if>
					         		   	<c:if test="${customerDisplayNameOfReportingVariableB != 'NULL'}">
											<msgpt:TableElement label="${customerDisplayNameOfReportingVariableB}">
												<c:out value="${ varB }" />
											</msgpt:TableElement>
										</c:if>	
											
										<msgpt:TableElement label="page.label.touchpoint">
											<msgpt:GetHibernateObject hibernateObjectName="com.prinova.messagepoint.model.deliveryevent.Job" id="${customerTouchpointValuePair[1]}" outputName="job"/>
											<msgpt:GetHibernateObject hibernateObjectName="com.prinova.messagepoint.model.deliveryevent.DeliveryEvent" id="${job.eventId}" outputName="event"/>
											<msgpt:GetHibernateObject hibernateObjectName="com.prinova.messagepoint.model.Document" id="${event.item.documentId}" outputName="document"/>											<c:out value="${document.name}" />
										</msgpt:TableElement>
											
										<msgpt:TableElement label="page.label.delivery.date">
											<msgpt:GetHibernateObject hibernateObjectName="com.prinova.messagepoint.model.deliveryevent.Job" id="${customerTouchpointValuePair[1]}" outputName="job" />
											<fmtJSTL:formatDate value="${job.updated}" pattern="${dateTimeFormat}" />&nbsp;										
										</msgpt:TableElement>
									</msgpt:TableListGroup>
								</c:forEach>
							</c:otherwise>
						</c:choose>
					</msgpt:DataTable>
				</form:form>		
			</msgpt:ContentData>
		</msgpt:ContentPanel>
	</msgpt:LowerContainer> 
</msgpt:BodyNew>
</msgpt:Html5>