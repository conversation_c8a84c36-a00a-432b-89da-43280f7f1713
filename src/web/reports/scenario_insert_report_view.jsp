<%@page import="com.prinova.messagepoint.model.scenario.ReportScenario"%>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.reports">


    <msgpt:Script>
        <script>
            $( function() {
                $("input:button").styleActionElement();
            });
        </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:BodyNew>
	<msgpt:BannerNew  edit="true" type="full" />

	<!-- Report Control Bar -->
	<div style="border: 1px solid #bbb; border-left: none; border-right: none; background-color: #eee; padding: 5px;">
		<table width="100%" cellspacing="0" cellpadding="0" border="0" class="innerContentTable">
			<tr>
				<td width="98%" style="padding: 0px; vertical-align: middle;"/>
				<td width="1%" align="right" style="padding: 0px; vertical-align: middle;">
					<input title="${msgpt:getMessage('page.label.PRINT')}" type="button" onclick="window.print()" style="display: none;" />
				</td>
				<td width="1%" align="right" style="padding: 0px; vertical-align: middle;">
					<input title="${msgpt:getMessage('page.label.CLOSE')}" type="button" onclick="window.close();" style="display: none;" />
				</td>
			</tr>
		</table>
	</div>

	<msgpt:LowerContainer>
		<msgpt:ContentPanel>
			<msgpt:ContentData title='page.label.insert.delivery.summary'>

				<msgpt:DataTable multiColumn="true" contentWidth="20%">
					<!-- Message Report Summary -->
					<msgpt:TableHeader label="page.label.insert.report.summary" />
					<!-- Generated By -->
					<msgpt:TableItem label="page.label.generated.by">
						<c:out value="${command.updatedByName }"/>
					</msgpt:TableItem>
					<!-- Actual Deliveries -->
					<msgpt:TableItem label="page.label.actual.deliveries">
						<c:out value="${command.insertsTotal}"/>
					</msgpt:TableItem>
					<!-- Date -->
					<msgpt:TableItem label="page.label.date">
						<fmtJSTL:formatDate value="${command.updated}" pattern="${dateTimeFormat}" />
					</msgpt:TableItem>
					<msgpt:TableItem label="">
						<c:out value=""/>
					</msgpt:TableItem>
					<!-- Time -->
					<msgpt:TableItem label="page.label.time">
						<fmtJSTL:formatDate value="${command.updated}" pattern="${timeFormatInput}" />
					</msgpt:TableItem>
					
					<c:choose>
						<c:when test="${noInsertSchedules}">
							<msgpt:TableItem label="page.label.note">
								<c:out value='${msgpt:getMessage("page.label.no.insert.schedules")}' />
							</msgpt:TableItem>
						</c:when>
					</c:choose>
				</msgpt:DataTable>

					<c:choose>
						<c:when test="${empty command.messageReports }">
							<msgpt:DataTable listHeader="page.label.insert.report.details">
								<msgpt:TableItem>
									<fmtSpring:message code="page.text.no.inserts.report" />
								</msgpt:TableItem>
							</msgpt:DataTable>
						</c:when>
						<c:otherwise>
							<msgpt:DataTable listHeader="page.label.insert.report.details">
								<msgpt:TableHeader label="" type="LISTHEADER">
									<tr class="contentTableHeaderTRsecond">
										<td>&nbsp;</td>
										<td colspan="2"><fmtSpring:message code="page.label.qualified" /></td>
										<td colspan="2"><fmtSpring:message code="page.label.played" /></td>
									</tr>
								</msgpt:TableHeader>
								<c:forEach var="completeReport" items="${command.insertReports}" varStatus="loopStatus">
									<msgpt:TableListGroup>
										<msgpt:TableElement label="page.label.insert">
											<c:choose>
												<c:when test="${completeReport.item == null }" >
													<c:out value='${msgpt:getMessage("page.label.insert.deleted")}' />
												</c:when>
												<c:otherwise>
													<c:out value="${completeReport.item.name}"/>
												</c:otherwise>
											</c:choose>
										</msgpt:TableElement>
										<msgpt:TableElement label="#">
									    	<c:choose>
									        	<c:when test="${completeReport.qualified > 0}">
											    	<a href="javascript:popItUp('reports_insert_sub.form?scenarioid=${param.scenarioid}&insid=${completeReport.insertId}&qualified=true')">
											        	<div class="reportIcon" style="float: left; padding-top: 0px; height: 14px;">
											        	<c:out value="${completeReport.qualified }"/>
											        	</div>
										        	</a>
									         	</c:when>
									       		<c:otherwise>
										        	<div class="reportIcon" style="float: left; padding-top: 0px; height: 14px;">
										        	<c:out value="${completeReport.qualified }"/>
										        	</div>
									        	</c:otherwise>
									   		</c:choose>
										</msgpt:TableElement>
										<msgpt:TableElement label="%">
							                <fmtJSTL:formatNumber maxFractionDigits="2" value="${ ( completeReport.total > 0 ) ? ( completeReport.qualified/completeReport.total * 100 ) : 0 }" />%
										</msgpt:TableElement>
										<msgpt:TableElement label="#">
									    	<c:choose>
									        	<c:when test="${completeReport.delivered > 0}">
									            	<a href="javascript:popItUp('reports_insert_sub.form?scenarioid=${param.scenarioid}&insid=${completeReport.insertId}&delivered=true')">
									                	<div class="reportIcon" style="float: left; padding-top: 0px; height: 14px;">
									                	<c:out value="${ completeReport.delivered }"/>
									                	</div>
										        	</a>
									        	</c:when>
									    		<c:otherwise>
								                	<div class="reportIcon" style="float: left; padding-top: 0px;  height: 14px;">
								                	<c:out value="${ completeReport.delivered }"/>
								                	</div>
									        	</c:otherwise>
									    	</c:choose>
										</msgpt:TableElement>
										<msgpt:TableElement label="%">
							                	<fmtJSTL:formatNumber maxFractionDigits="2" value="${ ( completeReport.total > 0 ) ? ( completeReport.delivered/completeReport.total * 100 ) : 0 }" />%
										</msgpt:TableElement>
									</msgpt:TableListGroup>
								</c:forEach>
							</msgpt:DataTable>
						</c:otherwise>
					</c:choose>

			</msgpt:ContentData>
		</msgpt:ContentPanel>
	</msgpt:LowerContainer>
</msgpt:BodyNew>
</msgpt:Html5>