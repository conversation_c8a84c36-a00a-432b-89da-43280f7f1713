<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.model.admin.ContentType" %>

<%@ include file="../includes/includes.jsp" %>


<c:set var="contentTypeMultipart" value="<%= ContentType.MULTIPART %>"/>
<c:set var="contentTypeVideo" value="<%= ContentType.VIDEO %>"/>
<c:set var="multipartID" value="<%= ContentType.MULTIPART %>"/>
<c:set var="zone" value="${command.zone}"/>
<c:set var="zoneId" value="${zone.id}"/>
<c:set var="document" value="${not empty zone.document ? zone.document : rootDocument}"/>

<msgpt:Html5>

    <msgpt:HeaderNew title="page.label.zone" extendedScripts="true">


        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:CalendarIncludes/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/colorPicker/js/colorpicker.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/colorPicker/js/eye.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/colorPicker/js/utils.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/colorPicker/js/layout.js"/>

        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/colorPicker/css/colorpicker.css"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/colorPicker/css/layout.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/scrollTo/jquery.scrollTo-2.1.2.min.js"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css"/>

        <msgpt:Script src="includes/javascript/handlebars/handlebars-v4.7.8.js"/>

        <msgpt:Script>
            <script>
                /* Create an array with the values of all the input boxes in a column */
                $.fn.dataTableExt.afnSortData['dom-text'] = function (setting, col) {
                    return this.api().column(col, {order: 'index'}).nodes().map(function (td, i) {
                        var rtv = $(td).find('input.zonePartNameInput,input.numeric').val();
                        return rtv ? rtv : 0;
                    });
                };

                /* Create an array with the values of all the select boxes in a column */
                $.fn.dataTableExt.afnSortData['dom-select'] = function (setting, col) {
                    return this.api().column(col, {order: 'index'}).nodes().map(function (td, i) {
                        var rtv = $(td).find('option:selected').text();
                        return rtv ? rtv : '';
                    });
                };

                function initBtn(ele, type) {

                    $(ele)
                        .mouseover(function () {
                            if (!$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight .actionBtn_toggleSelect]')) {
                                $(this).removeClass('actionBtn');
                                $(this).addClass('actionBtn_hov');
                            }
                        })
                        .mouseout(function () {
                            if (!$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight .actionBtn_toggleSelect')) {
                                $(this).removeClass('actionBtn_hov');
                                $(this).addClass('actionBtn');
                            }
                        });


                    if (type == "toggle") {
                        $(ele)
                            .click(function () {
                                if (!$(this).hasClass('actionBtn_disabled')) {
                                    if ($(this).hasClass('actionBtn_toggleSelectHighlight'))
                                        $(this).removeClass('actionBtn_toggleSelectHighlight').addClass('actionBtn');
                                    else
                                        $(this).removeClass('actionBtn').addClass('actionBtn_toggleSelectHighlight');
                                }

                            });
                    } else if (type == "button") {
                        $(ele)
                            .mousedown(function () {
                                if (!$(this).hasClass('actionBtn_disabled'))
                                    $(this).removeClass('actionBtn actionBtn_hov').addClass('actionBtn_selected');
                            })
                            .mouseup(function () {
                                if (!$(this).hasClass('actionBtn_disabled'))
                                    $(this).removeClass('actionBtn_selected').addClass('actionBtn');
                            });
                    }

                }

                function initFlipToggles() {

                    // CONNECTED LIBRARY ITEMS ONLY
                    if ($("#isConnectedLibraryItemsOnly:visible").length != 0 && $("#isConnectedLibraryItemsOnly:visible").closest('.ibutton-container').length == 0)
                        $("#isConnectedLibraryItemsOnly").iButton({
                            labelOn: $("#isConnectedLibraryItemsOnly").attr('title').split(';')[0],
                            labelOff: $("#isConnectedLibraryItemsOnly").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {

                            }
                        });

                    // MESSAGE LIBRARY ITEMS ONLY
                    if ($("#isMessageLibraryItemsOnly:visible").length != 0 && $("#isMessageLibraryItemsOnly:visible").closest('.ibutton-container').length == 0)
                        $("#isMessageLibraryItemsOnly").iButton({
                            labelOn: $("#isMessageLibraryItemsOnly").attr('title').split(';')[0],
                            labelOff: $("#isMessageLibraryItemsOnly").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {

                            }
                        });

                    // RESTRICT LIBRARY ASSETS
                    if ($("#libraryAssetsToggle:visible").length != 0 && $("#libraryAssetsToggle:visible").closest('.ibutton-container').length == 0)
                        $("#libraryAssetsToggle").iButton({
                            labelOn: $("#libraryAssetsToggle").attr('title').split(';')[0],
                            labelOff: $("#libraryAssetsToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {
                                toggleContentType();
                                toggleOverride('sharedAssets');
                            }
                        });

                    // MARKUP
                    if ($("#markupToggle:visible").length != 0 && $("#markupToggle:visible").closest('.ibutton-container').length == 0)
                        $("#markupToggle").iButton({
                            labelOn: $("#markupToggle").attr('title').split(';')[0],
                            labelOff: $("#markupToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {
                                toggleState($("#markupToggle"));
                            }
                        });

                    // BARCODE
                    if ($("#barcodeToggle:visible").length != 0 && $("#barcodeToggle:visible").closest('.ibutton-container').length == 0)
                        $("#barcodeToggle").iButton({
                            labelOn: $("#barcodeToggle").attr('title').split(';')[0],
                            labelOff: $("#barcodeToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {
                                toggleState($("#barcodeToggle"));
                            }
                        });

                    // CONNECTED ZONE
                    if ($("#isInteractiveZone:visible").length != 0 && $("#isInteractiveZone:visible").closest('.ibutton-container').length == 0)
                        $("#isInteractiveZone").iButton({
                            labelOn: $("#isInteractiveZone").attr('title').split(';')[0],
                            labelOff: $("#isInteractiveZone").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {
                                toggleInteractiveZone();
                            }
                        });

                    // TABLES
                    if ($("#tablesToggle:visible").length != 0 && $("#tablesToggle:visible").closest('.ibutton-container').length == 0)
                        $("#tablesToggle").iButton({
                            labelOn: $("#tablesToggle").attr('title').split(';')[0],
                            labelOff: $("#tablesToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {
                                toggleState($("#tablesToggle"));
                            }
                        });

                    // FORMS
                    if ($("#formsToggle:visible").length != 0 && $("#formsToggle:visible").closest('.ibutton-container').length == 0)
                        $("#formsToggle").iButton({
                            labelOn: $("#formsToggle").attr('title').split(';')[0],
                            labelOff: $("#formsToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {

                            }
                        });

                    // ALTERNATIVE TEXT
                    if ($("#altTextToggle:visible").length != 0 && $("#altTextToggle:visible").closest('.ibutton-container').length == 0)
                        $("#altTextToggle").iButton({
                            labelOn: $("#altTextToggle").attr('title').split(';')[0],
                            labelOff: $("#altTextToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {

                            }
                        });

                    // IMAGE LINK
                    if ($("#imageLinkToggle:visible").length != 0 && $("#imageLinkToggle:visible").closest('.ibutton-container').length == 0)
                        $("#imageLinkToggle").iButton({
                            labelOn: $("#imageLinkToggle").attr('title').split(';')[0],
                            labelOff: $("#imageLinkToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {

                            }
                        });

                    // EXTERNAL LINK
                    if ($("#extLinkToggle:visible").length != 0 && $("#extLinkToggle:visible").closest('.ibutton-container').length == 0)
                        $("#extLinkToggle").iButton({
                            labelOn: $("#extLinkToggle").attr('title').split(';')[0],
                            labelOff: $("#extLinkToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {

                            }
                        });

                    // EXTERNAL PATH
                    if ($("#extPathToggle:visible").length != 0 && $("#extPathToggle:visible").closest('.ibutton-container').length == 0)
                        $("#extPathToggle").iButton({
                            labelOn: $("#extPathToggle").attr('title').split(';')[0],
                            labelOff: $("#extPathToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {

                            }
                        });

                    // TRANSPARENT BACKGROUND
                    if ($("#isBackgroundTransparent:visible").length != 0 && $("#isBackgroundTransparent:visible").closest('.ibutton-container').length == 0)
                        $("#isBackgroundTransparent").iButton({
                            labelOn: $("#isBackgroundTransparent").attr('title').split(';')[0],
                            labelOff: $("#isBackgroundTransparent").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {
                                $('.backgroundColorSelectContainer').hide();
                                if (!$("#isBackgroundTransparent").is(':checked'))
                                    $('.backgroundColorSelectContainer').showEle('normal');
                                toggleOverride('backgroundColor');
                            }
                        });

                    // ENABLE ZONE
                    if ($("#zoneEnableToggle:visible").length != 0 && $("#zoneEnableToggle:visible").closest('.ibutton-container').length == 0)
                        $("#zoneEnableToggle").iButton({
                            labelOn: $("#zoneEnableToggle").attr('title').split(';')[0],
                            labelOff: $("#zoneEnableToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {
                                toggleOverride('enabled');
                            }
                        });

                    // FLOW ZONE
                    if ($("#flowZoneToggle:visible").length != 0 && $("#flowZoneToggle:visible").closest('.ibutton-container').length == 0)
                        $("#flowZoneToggle").iButton({
                            labelOn: $("#flowZoneToggle").attr('title').split(';')[0],
                            labelOff: $("#flowZoneToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {
                                toggleFlowZone();
                            }
                        });

                    if ($("#backerToggle:visible").length != 0 && $("#backerToggle:visible").closest('.ibutton-container').length == 0)
                        $("#backerToggle").iButton({
                            labelOn: $("#backerToggle").attr('title').split(';')[0],
                            labelOff: $("#backerToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {
                                toggleBackerZone();
                            }
                        });

                    // FREEFORM ZONE
                    if ($("#freeformToggle:visible").length != 0 && $("#freeformToggle:visible").closest('.ibutton-container').length == 0)
                        $("#freeformToggle").iButton({
                            labelOn: $("#freeformToggle").attr('title').split(';')[0],
                            labelOff: $("#freeformToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {
                                toggleContentType();
                                toggleState($("#freeformToggle"));
                            }
                        });

                    // ENFORCE MINIMUM HEIGHT
                    if ($("#enforceMinimumHeightToggle:visible").length != 0 && $("#enforceMinimumHeightToggle:visible").closest('.ibutton-container').length == 0)
                        $("#enforceMinimumHeightToggle").iButton({
                            labelOn: $("#enforceMinimumHeightToggle").attr('title').split(';')[0],
                            labelOff: $("#enforceMinimumHeightToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {

                            }
                        });

                    // SPLIT TABLES
                    if ($("#splitTablesToggle:visible").length != 0 && $("#splitTablesToggle:visible").closest('.ibutton-container').length == 0)
                        $("#splitTablesToggle").iButton({
                            labelOn: $("#splitTablesToggle").attr('title').split(';')[0],
                            labelOff: $("#splitTablesToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {

                            }
                        });

                    // CUSTOM PARAGRAPH STYLES
                    if ($("#customParagraphStylesToggle:visible").length != 0 && $("#customParagraphStylesToggle:visible").closest('.ibutton-container').length == 0)
                        $("#customParagraphStylesToggle").iButton({
                            labelOn: $("#customParagraphStylesToggle").attr('title').split(';')[0],
                            labelOff: $("#customParagraphStylesToggle").attr('title').split(';')[1],
                            resizeHandle: false,
                            change: function () {

                            }
                        });
 
                }

                function initOverrideInputs() {
                    $("[class*='overrideInput_']:not(.init)").each(function () {
                        var id = null;
                        var classes = $(this).attr('class').split(' ');
                        for (var i = 0; i < classes.length; i++)
                            if (classes[i].indexOf('overrideInput_') != -1)
                                id = parseId(classes[i]);

                        if ($(this).is(':checkbox') || $(this).is('div')) {
                            $(this).click(function () {
                                toggleOverride(id);
                            });
                        } else if ($(this).is(':input')) {
                            $(this)
                                .keyup(function () {
                                    toggleOverride(id);
                                })
                                .change(function () {
                                    toggleOverride(id);
                                });
                        }
                        $(this).addClass('init')
                    });
                }

                function onSelectColor() {
                    toggleOverride('backgroundColor');
                }

                function toggleFlowZone() {
                    if ($("#flowZoneToggle").length != 0) {
                        if ($('#flowZoneToggle').is(':checked')) {
                            $('#primaryAttributes').find('tr').each(function () {
                                if (!$(this).is('.flowZoneProperty') && !$(this).is('.contentTableHeaderTR_minimal'))
                                    $(this).hide();
                            });
                            $('#interactiveZoneInterface,#connectedPropertiesTable,#stylesTable,#compositionAttributes').hide();
                        } else {
                            $('#primaryAttributes').find('tr').each(function () {
                                if (!$(this).is('.flowZoneProperty'))
                                    $(this).show();
                            });
                            $('#interactiveZoneInterface,#connectedPropertiesTable,#stylesTable,#compositionAttributes').show();
                        }
                        $('.verticallyRelativeToToggleContainer, .verticallyRelativeToToggleContainer tr').show();
                        toggleAboveInputs();
                    }
                }

                function toggleBackerZone() {
                    if ($("#backerToggle").length != 0) {
                        if ($('#backerToggle').is(':checked')) {
                            //    Backer - #zoneContentType = 205; #verticallyRelativeToSelect == 0 (.verticallyRelativeToToggleContainer); #flowIntoZoneSelect == -1 (.flowIntoZoneContainer);
                            //    #flowZoneToggle:not(:checked) (.flowZoneContainer); rotationSelect == 0 (.rotationContainer)-->
                            $('.verticallyRelativeToToggleContainer,.flowIntoZoneContainer,.flowZoneContainer,.rotationContainer').hide();
                        } else {
                            $('.verticallyRelativeToToggleContainer,.flowIntoZoneContainer,.flowZoneContainer,.rotationContainer').show();
                        }
                    }
                }

                function toggleState(ele) {

                    var zoneContentType = $('#zoneContentType').val();

                    // FREEFORM
                    if ($('#freeformToggle').is(':checked')) {
                        $('.contentTypeToggleContainer,.tablesToggleContainer,#markupToggle').hide();
                    } else {
                        $('.contentTypeToggleContainer,#markupToggle').show();
                    }

                    // TABLES
                    if ( ($('#dxfToggle').length != 0 && !$('#dxfToggle').is(':checked') && $('#htmlToggle').length != 0 && !$('#htmlToggle').is(':checked')) || (zoneContentType != 1 && zoneContentType != 4)) {
                        $('.tablesToggleContainer').hide();
                        if(($('#dxfToggle').length != 0 && !$('#dxfToggle').is(':checked') && $('#htmlToggle').length != 0 && !$('#htmlToggle').is(':checked'))) {
                            setFlipToggleState($("#tablesToggle"), true);
                        } else {
                            setFlipToggleState($("#tablesToggle"), false);
                        }

                    } else {
                        $('.tablesToggleContainer').show();

                    }

                    // FORMS
                    if ( ($('#dxfToggle').length != 0 && !$('#dxfToggle').is(':checked') && $('#htmlToggle').length != 0 && !$('#htmlToggle').is(':checked')) || zoneContentType != 1) {
                        $('.formsToggleContainer').hide();
                        setFlipToggleState($('#formsToggle'), false);
                    } else if ($('#freeformToggle').length != 0 && !$('#freeformToggle').is(':checked') &&
                        $('#tablesToggle').length != 0 && !$('#tablesToggle').is(':checked')) {
                        $('.formsToggleContainer').hide();
                        setFlipToggleState($('#formsToggle'), false);
                    } else {
                        $('.formsToggleContainer').show();
                    }

                    // MESSAGE CONTENT EDIT
                    $('.messageContentEditingContainer').hide();
                    if (zoneContentType != 1) {
                        if (!$('#isInteractiveZone').is(':checked'))
                            $('.messageContentEditingContainer').show();
                    }

                    // BARCODE
                    $('.barcodeToggleContainer').hide();
                    if (zoneContentType == 1) {
                        if ($('#isFreeformPage').val() == "true" || ($('#freeformToggle').length != 0 && $('#freeformToggle').is(':checked')) ||
                            ($('#tablesToggle').length != 0 && $('#tablesToggle').is(':checked')))
                            $('.barcodeToggleContainer').show();
                    }


                    // MARKUP
                    if (zoneContentType == 1) {
                        if ($('#markupToggle').is(':checked')) {
                            $('#outputFormatSelect').selectOption('outputFormatOption_tagged');
                            $('.contentTypeToggleContainer,.tablesToggleContainer,.formsToggleContainer,.freeformToggleContainer,#barcodeToggle').hide();
                            $('#stylesTable').hide();
                        } else if (($('#freeformToggle').length != 0 && $('#freeformToggle').is(':checked')) ||
                            ($('#dxfToggle').length != 0 && $('#dxfToggle').is(':checked')) ||
                            ($('#htmlToggle').length != 0 && $('#htmlToggle').is(':checked')) ||
                            ($('#formsToggle').length != 0 && $('#formsToggle').is(':checked')) ||
                            ($('#barcodeToggle').length != 0 && $('#barcodeToggle').is(':checked'))) {
                            $('.markupToggleContainer').hide();
                            setFlipToggleState($("#markupToggle"), false);
                        } else {
                            $('.markupToggleContainer').show();
                        }
                    }

                    if (($(ele).is('#barcodeToggle') && !$('#barcodeToggle').is(':checked')) ||
                        ($(ele).is('#markupToggle') && !$('#markupToggle').is(':checked'))) {
                        toggleContentType();
                        $('.freeformToggleContainer').show();
                    }

                    toggleNavItems();
                    initFlipToggles();
                }

                function setFlipToggleState(ele, state) {
                    if (!state) {
                        if ($(ele).closest('.ibutton-container').length != 0 && $(ele).is(':checked'))
                            $(ele).iButton("toggle", false);
                    } else {
                        if ($(ele).closest('.ibutton-container').length != 0 && !$(ele).is(':checked'))
                            $(ele).iButton("toggle", true);
                    }
                }

                function toggleInteractiveZone() {
                    $('#smartTextSelectContainer,#connectedSmartTextSelectContainer,#imageSelectContainer,#connectedImageSelectContainer,.connectedLibraryItemsOnlyContainer').hide();
                    $('#smartTextSelectContainer,#connectedSmartTextSelectContainer,#imageSelectContainer,#connectedImageSelectContainer')
                        .find('input').attr('disabled', 'disabled');
                    if ($('#isInteractiveZone').is(':checked')) {
                        $('#connectedSmartTextSelectContainer,#connectedImageSelectContainer,.connectedLibraryItemsOnlyContainer').show();
                        $('#connectedSmartTextSelectContainer,#connectedImageSelectContainer').find('input').removeAttr('disabled');
                    } else {
                        $('#smartTextSelectContainer,#imageSelectContainer').show();
                        $('#smartTextSelectContainer,#imageSelectContainer').find('input').removeAttr('disabled');
                    }
                    toggleInteractiveTemplateSelect($('#zoneContentType'));
                }

                function toggleInteractiveTemplateSelect(selectEle) {
                    $("[class*='communicationTemplatesContainer']").hide();
                    $("#interactiveZoneInterface").show();
                    if ($(selectEle).val() == 1) {
                        if ( $('#isInteractiveZone').is(':checked') )
                        	$(".communicationTemplatesContainer_text").showEle('normal');
                    } else if ($.inArray($(selectEle).val(), ["201", "202", "205", "206", "210", "211", "213", "214", "215", "216"]) != -1) {
                    	if ( $('#isInteractiveZone').is(':checked') )
                    		$(".communicationTemplatesContainer_image").showEle('normal');
                    } else if ($(selectEle).val() == 218) {
                        if ( $('#isInteractiveZone').is(':checked') ) {
                            $('#smartTextSelectContainer,#connectedSmartTextSelectContainer,#imageSelectContainer,#connectedImageSelectContainer,.connectedLibraryItemsOnlyContainer').hide();
                            $(".communicationTemplatesContainer_variable").showEle('normal');
                        }
                    }else {
                        $("#interactiveZoneInterface").hide();
                    }

                    initFlipToggles();

                }

                function toggleContentType() {
                    toggleInteractiveTemplateSelect($('#zoneContentType'));

                    var multipartIndicator = '${contentTypeMultipart}';
                    var videoIndicator = '${contentTypeVideo}';

                    // MULTI-PART: Toggle interface
                    if ($('#zoneContentType').find('option:selected').val() == multipartIndicator)
                        $('#multipartContainer').showEle('normal');
                    else
                        $('#multipartContainer').hide();

                    var appliesText = false;
                    var hasGrahpicZoneOrPart = false;
                    var isGraphicData = false;
                    var isGraphicPDF = false;
                    $('select.zoneContentTypeSelect,select.partContentTypeSelect').each(function () {
                        var contentType = parseInt($(this).val());
                        if (!(contentType >= 200 && contentType < 300) && contentType != multipartIndicator && contentType != videoIndicator)
                            appliesText = true;
                        if (contentType >= 200)
                            hasGrahpicZoneOrPart = true;
                        if (contentType == 218)
                            isGraphicData = true;
                        if (contentType == 205)
                            isGraphicPDF = true;
                    });

                    // ALTERNATIVE TEXT, IMAGE DIMENSIONS
                    if (hasGrahpicZoneOrPart) {
                        $('.applyAltTextToggleContainer,.imageDimensionsTypeContainer').show();
                        $('.applyImageExtLinkToggleContainer,.imageDimensionsTypeContainer').show();
                        $('.applyImageExtPathToggleContainer,.imageDimensionsTypeContainer').show();
                    } else {
                        $('.applyAltTextToggleContainer,.imageDimensionsTypeContainer').hide();
                        $('.applyImageExtLinkToggleContainer,.imageDimensionsTypeContainer').hide();
                        $('.applyImageExtPathToggleContainer,.imageDimensionsTypeContainer').hide();
                    }

                    // SHARED ASSETS
                    toggleInteractiveZone();
                    if ($('#libraryAssetsToggle').is(':checked')) {
                        $('.assetsContainer_smartText,.assetsContainer_imageLibrary').hide();
                        $('#sharedAssetsContainer_smartText,#sharedAssetsContainer_imageLibrary').hide();
                        if (appliesText) {
                            $('#sharedAssetsContainer_smartText').showEle('normal');
                            $('.assetsContainer_smartText').showEle('normal');
                        }
                        if (hasGrahpicZoneOrPart || $('#freeformToggle').is(':checked') || $('#isFreeformPage').val() == "true") {
                            $('#sharedAssetsContainer_imageLibrary').showEle('normal');
                            $('.assetsContainer_imageLibrary').showEle('normal');
                        }
                    } else {
                        $('#sharedAssetsContainer_smartText,#sharedAssetsContainer_imageLibrary,.assetsContainer_smartText,.assetsContainer_imageLibrary').hide();
                    }

                    var hasSelectedStyles = $('#starterTextStyleSelect option:selected').val() > 0 ||
                                          $('#defaultTextStyleSelect option:selected').val() > 0 ||
                                          $('#textStyleSelect option:selected').length > 0 ||
                                          $('#defaultParagraphStyleSelect option:selected').val() > 0 ||
                                          $('#paragraphStyleSelect option:selected').length > 0 ||
                                          $('#defaultListStyleSelect option:selected').val() > 0 ||
                                          $('#listStyleSelect option:selected').length > 0 ||
                                          $('#customParagraphStylesToggle').is(':checked');

                    if (appliesText || hasSelectedStyles) {
                        $('#stylesTable,.tablesToggleContainer,.formsToggleContainer,.freeformToggleContainer,.barcodeToggleContainer,.markupToggleContainer').showEle('normal');
                        $('.applyImageLinkToggleContainer').hide();
                        toggleState();
                    } else {
                        $('#stylesTable,.tablesToggleContainer,.formsToggleContainer,.freeformToggleContainer,.barcodeToggleContainer,.markupToggleContainer').hide();
                        $('.applyImageLinkToggleContainer').showEle('normal');
                        toggleNavItems();
                    }

                    if (isGraphicPDF) {
                        setTimeout( function() { $('.backerToggleContainer').show() }, 50);
                    } else {
                        setTimeout( function() { $('.backerToggleContainer').hide() }, 50);
                    }

                }

                function toggleOutputFormat() {
                    var outputFormatId = $('#outputFormatSelect').val();
                    $('#dxfToggle,#htmlToggle').removeAttr('checked');
                    if ( outputFormatId == '1' ) {
                        $('#dxfToggle').attr('checked', 'checked');
                        toggleState($("#dxfToggle"));
                    } else if ( outputFormatId == '2' ) {
                        $('#htmlToggle').attr('checked', 'checked');
                        toggleState($("#htmlToggle"));
                    } else {
                        toggleState();
                    }
                }

                function toggleOverride(id) {
                    var btn = $('#overrideBtn_' + id);
                    if (!$(btn).hasClass('actionBtn_toggleSelectHighlight')) {
                        $(btn).addClass('actionBtn_toggleSelectHighlight');
                        $('#overrideCheckbox_' + id).attr('checked', 'checked');
                    }
                }

                function toggleAboveInputs(){
                    // if vertical relation is set to above
                    if ($('#verticallyRelativeToSelect').val() == '1') {
                        $('.aboveInput').show();
                    } else {
                        $('.aboveInput').hide();
                        $('#zone\\.relativeDistanceInInches').val('0.25');
                        $('#zone\\.minimumKeepTogetherInInches').val('');
                    }
                }

                $(function () {
                    if (getParam('nprSaveSuccess') == "true")
                        getTopFrame().location.reload();

                    $('#zoneContentType,#dataGroupSelect,.partContentTypeSelect,#rotationSelect,#renderedContainerTypeSelect,' +
                        '#imageDimensionsTypeSelect,#outputFormatSelect,#flowIntoZoneSelect,#verticalAlignmentSelect').styleActionElement();

                    $('#verticallyRelativeToSelect').styleActionElement();

                    toggleInteractiveZone();
                    toggleInteractiveTemplateSelect($('#zoneContentType'));
                    toggleContentType();
                    toggleState();
                    toggleFlowZone();
                    toggleBackerZone();

                    initNavContainer();

                    $('#communicationTemplateSelect_text,#communicationTemplateSelect_image').each(function () {
                        var currentSelect = $(this);
                        var type = "null";
                        if ($(currentSelect).attr('id').indexOf('_text') != -1)
                            type = "communication_template_text";
                        else if ($(currentSelect).attr('id').indexOf('_image') != -1)
                            type = "communication_template_image";

                        $(currentSelect).styleActionElement({
                            isAsync: true,
                            getItemsAsync: {
                                getItemsURL: context + "/getDataForSelectMenu.form",
                                asyncSearch: false,
                                fnExtServerParams: function (o) {
                                    return [
                                        {"name": "type", "value": type},
                                        {"name": "selectedItemId", "value": o.inputEle.val()},
                                        {"name": "isFixedDefinition", "value": true},
                                        {"name": "documentId", "value": "${rootDocument.id}"}
                                    ];
                                }
                            },
                            alphaSort: (type == "communication_template_text" || type == "communication_template_image"),
                            afterFilter: function (o, t) {
                                initOverrideInputs();
                            }
                        });
                    });

                    $('#defaultTextStyleSelect,#defaultParagraphStyleSelect,#defaultListStyleSelect').each(function () {
                        var currentSelect = $(this);
                        var type = "null";
                        if ($(currentSelect).attr('id').indexOf('ParagraphStyle') != -1)
                            type = "paragraph_styles";
                        else if ($(currentSelect).attr('id').indexOf('ListStyle') != -1)
                            type = "list_styles";
                        else if ($(currentSelect).attr('id').indexOf('TextStyle') != -1)
                            type = "text_styles";

                        $(currentSelect).complexDropdown({
                            isAsync: true,
                            getItemsAsync: {
                                getItemsURL: context + "/getDataForSelectMenu.form",
                                asyncSearch: false,
                                fnExtServerParams: function (o) {
                                    return [
                                        {"name": "type", "value": type},
                                        {"name": "selectedItemId", "value": o.val()},
                                        {"name": "isFixedDefinition", "value": true},
                                        {"name": "documentId", "value": "${rootDocument.id}"}
                                    ];
                                }
                            },
                            afterFilter: function (o, t) {
                                initOverrideInputs();
                            }
                        });
                    });

                    $('#starterTextStyleSelect').complexDropdown({
                        isAsync: true,
                        getItemsAsync: {
                            getItemsURL: context + "/getDataForSelectMenu.form",
                            asyncSearch: false,
                            fnExtServerParams: function (o) {
                                return [
                                    {"name": "type", "value": "text_styles"},
                                    {"name": "selectedItemId", "value": o.val()},
                                    {"name": "isFixedOrDefaultDefinition", "value": true},
                                    {"name": "documentId", "value": "${rootDocument.id}"}
                                ];
                            }
                        },
                        afterFilter: function (o, t) {
                            initOverrideInputs();
                        }
                    });

                    $("input:button").styleActionElement();
                    $("#workgroupSelect").styleActionElement({alphaSort: true});
                    $("#smartTextSelect,#connectedSmartTextSelect,#imageSelect,#connectedImageSelect").each(function () {
                        var currentSelect = $(this);
                        $(currentSelect).styleActionElement({
                            maxItemsInList: 20,
                            isAsync: true,
                            tagCloudFilter: $(currentSelect).attr('tagCloudType') ? true : false,
                            tagCloudType: $(currentSelect).attr('tagCloudType'),
                            getItemsAsync: {
                                getItemsURL: context + "/getDataForMultiselectMenu.form",
                                fnExtServerParams: function (o) {
                                    return [
                                        {"name": "type", "value": o.inputEle.attr('typeLabel')},
                                        {"name": "documentId", "value": "${rootDocument.id}"},
                                        {"name": "includeArchived", "value": false},
                                        {"name": "activeOnly", "value": true},
                                        {
                                            "name": "isConnectedContext",
                                            "value": o.inputEle.attr('id').indexOf('connected') != -1
                                        },
                                    ];
                                }
                            },
                            afterFilter: function (o, t) {
                                toggleInteractiveZone();
                                initOverrideInputs();
                            }
                        });
                    });
                    $("#textStyleSelect,#paragraphStyleSelect,#listStyleSelect").each(function () {
                        var currentSelect = $(this);
                        var type = "null";
                        if ($(currentSelect).attr('id').indexOf('paragraphStyle') != -1)
                            type = "paragraph_style";
                        else if ($(currentSelect).attr('id').indexOf('listStyle') != -1)
                            type = "list_style";
                        else if ($(currentSelect).attr('id').indexOf('textStyle') != -1)
                            type = "text_style";

                        $(currentSelect).complexDropdown({
                            maxItemsInList: 20,
                            isAsync: true,
                            getItemsAsync: {
                                getItemsURL: context + "/getDataForMultiselectMenu.form",
                                fnExtServerParams: function (o) {
                                    return [
                                        {"name": "type", "value": type},
                                        {"name": "documentId", "value": "${rootDocument.id}"},
                                        {"name": "includeArchived", "value": false},
                                        {"name": "activeOnly", "value": true},
                                    ];
                                }
                            },
                            afterFilter: function (o, t) {
                                toggleInteractiveZone();
                                initOverrideInputs();
                            }
                        });
                    });
                    // Disable the drill-down for the zone part list
                    $('#zonePartsTable tr').each(function () {
                        $(this).find('td.control').unbind();
                        $(this).find('#drill-down-icon').removeClass();
                    });

                    // Init: Override btns
                    $("[id^='overrideCheckbox_']").each(function () {
                        var id = parseId(this);
                        var checkbox = this;
                        var btn = $('#overrideBtn_' + id);
                        initBtn(btn, 'toggle');
                        if ($(this).is(':checked'))
                            $(btn).addClass('actionBtn_toggleSelectHighlight');
                        $(btn).click(function () {
                            if ($(this).is('.actionBtn_toggleSelectHighlight'))
                                $(checkbox).attr('checked', 'checked')
                            else
                                $(checkbox).removeAttr('checked');
                        });
                    });

                    $('#zoneMetatags').tagCloud({
                        tagCloudType: 19
                    });

                    // Init: Override toggling
                    initOverrideInputs();

                    $("[id^='zonePart_']")
                        .mouseover(function () {
                            $('.partThumbnailContainer .multipartZonePart').removeClass('zonePartSelectedClass zonePartNotSelectedClass').addClass('zonePartNotSelectedClass');
                            $('.partThumbnailContainer #zone' + $('#zoneId').val() + '_part' + parseId(this)).addClass('zonePartSelectedClass');
                        })
                        .mouseout(function () {
                            $('.partThumbnailContainer .multipartZonePart').removeClass('zonePartSelectedClass zonePartNotSelectedClass').addClass('zonePartNotSelectedClass');
                        });
                    console.log('starting1ss');
                    var $thumbnailContainer = $('#thumbnailContainer');
                    if ($thumbnailContainer.length > 0) {
                        $thumbnailContainer.css({
                            'position': 'sticky',
                            'top': '0px',
                            'z-index': '1000'
                        });

                        // Fallback for older browsers or iframe issues
                        var $parentTd = $thumbnailContainer.closest('.partThumbnailContainer');
                        if ($parentTd.length > 0) {
                            $parentTd.css({
                                'position': 'sticky',
                                'top': '0px'
                            });
                        }
                    }

                    $('#zone\\.relativeDistanceInInches, #zone\\.minimumKeepTogetherInInches')
                        .on('input', function() {
                            var validNumber = /^\d*\.?\d{0,2}$/;
                            if (!validNumber.test($(this).val())) {
                                $(this).val($(this).val().slice(0, -1));
                            }
                        });

                    $('#zone\\.relativeDistanceInInches, #zone\\.minimumKeepTogetherInInches')
                        .on('blur', function() {
                            var value = parseFloat($(this).val());
                            if (!isNaN(value)) {
                                $(this).val(value.toFixed(2));
                            } else {
                                $(this).val('');
                            }
                        });

                });
            </script>
        </msgpt:Script>

        <style type="text/css">
            .zoneEditContainer div.zoneNotSelectedClass, .zoneEditContainer div.zoneSelectedClass {
                border-width: 1px;
            }

            .overrideCheckbox {
                display: none;
            }

            .overrideBtn {
                display: inline-block;
                position: relative;
                margin-left: 3px;
                vertical-align: top;
            }

            #colorpickerHolder2 {
                left: -30px;
            }

            .dropdown-menu {
                min-width: 450px;
            }
        </style>

    </msgpt:HeaderNew>

    <msgpt:BodyNew theme="minimal" type="iframe">

        <%--@elvariable id="command" type=""--%>
        <form:form modelAttribute="command" cssStyle="height: 100%">
        <c:if test="${!canUpdate}">
            <msgpt:Information type="error">
                <fmtSpring:message code="error.message.action.not.permitted"/>
            </msgpt:Information>
        </c:if>
        <c:if test="${canUpdate}">
            <input type="hidden" id="documentId" value="${document.id}"/>
            <input type="hidden" id="zoneId" value="${zoneId}"/>
            <input type="hidden" id="contentTypeId" value="${command.contentTypeId}"/>
            <input type="hidden" id="subcontentTypeId" value="${command.subContentTypeId}"/>


            <input type="hidden" id="multipartID" value="${multipartID}"/>

            <input type="hidden" id="isAlternateZone" value="${isAlternateZone}"/>
            <input type="hidden" id="zoneId" value="${command.zone.id}"/>

            <div id="popupHeaderTitle" style="display: none;">
			<span class="titleText">
				<c:choose>
					<c:when test="${empty param.zoneId}">
						<fmtSpring:message code="page.label.new"/>&nbsp;
					</c:when>
					<c:otherwise>
						<fmtSpring:message code="page.label.edit"/>&nbsp;
					</c:otherwise>
				</c:choose>
				<c:choose>
					<c:when test="${isPlaceholder}">
						<fmtSpring:message code="page.label.placeholder"/>
					</c:when>
					<c:otherwise>
						<fmtSpring:message code="page.label.zone"/>
					</c:otherwise>
				</c:choose>
			</span>
            </div>

            <div class="contentTableIframeExtended" style="height: 100%">

                <table width="100%" cellspacing="0" cellpadding="0" border="0" style="height: 100%;">
                    <tr>
                        <td width="1%" class="partThumbnailContainer"
                            style="padding: 0px; background-color: #efefef; min-width: 375px; vertical-align: top;">
                            <c:if test="${not isPlaceholder}">
                                <div id="thumbnailContainer">
                                    <!-- Document Thumbnail -->
                                    <div class="backgroundTile_50p"
                                         style="padding: 32px 40px; border-right: 1px solid #f5f5f5; border-bottom: 1px solid #ddd; max-height: 350px; overflow: auto;">
                                        <msgpt:DocumentTag document="${document}" maxWidth="250" type="admin"
                                                           showDisabled="true" selectedZoneId="${zoneId}"
                                                           showSectionNames="false" readOnly="true"/>
                                    </div>
                                </div>
                            </c:if>
                            <div id="navContainer"></div>
                        </td>
                        <td style="padding: 0px; vertical-align: top;">
                            <div class="backgroundTile_10p propertiesContainer"
                                 style="height: 609px; max-width: 525px; border-left: 1px solid #eee;">

                                <form:errors path="*">
                                    <msgpt:Information errorMsgs="${messages}" type="error"/>
                                </form:errors>

                                <msgpt:DataTable labelPosition="top" id="primaryAttributes">
                                    <msgpt:TableHeader label="page.label.details"/>
                                <!-- Zone Name -->
                                <msgpt:TableItem label="page.label.name" cssClass="flowZoneProperty">
                                <div style="white-space: nowrap;">
                                    <msgpt:InputFilter type="simpleName">
                                        <form:input cssClass="inputXXL overrideInput_friendlyName"
                                                    path="zone.friendlyName" id="zoneFriendlyName"/>
                                    </msgpt:InputFilter>
                                    <!-- OVERRIDE: Name -->
                                    <c:if test="${isAlternateZone}">
                                        <form:checkbox path="zone.overrideFriendlyName"
                                                       id="overrideCheckbox_friendlyName" cssClass="overrideCheckbox"/>
                                        <c:if test="${not isOmniAlternate}">
                                            <div id="overrideBtn_friendlyName"
                                                 class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
                                                 title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.zone.name.toggle')}</div>">
                                                <i class="fa fa-unlink fa-mp-btn"
                                                   style="font-size: 16px;"></i>
                                            </div>
                                        </c:if>
                                    </c:if>
                                </div>
                                </msgpt:TableItem>
                                <!-- Connector Name -->
                                <c:if test="${not isPlaceholder && not command.zone.isEmailSubjectLine && not document.isSmsTouchpoint && not command.zone.isWebTitleLine}">
                                <msgpt:TableItem label="page.label.touchpoint.container.zoneconnector" cssClass="flowZoneProperty">
                                <div style="white-space: nowrap;">
                                    <c:choose>
                                        <c:when test="${document.isEmailTouchpoint || document.isWebTouchpoint}">
                                            <c:out value="${command.zone.name}"/>
                                        </c:when>
                                        <c:otherwise>
                                            <msgpt:InputFilter type="simpleName">
                                                <form:input cssClass="inputXXL overrideInput_name" path="zone.name"
                                                            id="zoneConnectorName"/>
                                            </msgpt:InputFilter>
                                            <!-- OVERRIDE: Connector -->
                                            <c:if test="${isAlternateZone}">
                                                <form:checkbox path="zone.overrideName" id="overrideCheckbox_name"
                                                               cssClass="overrideCheckbox"/>
                                                <div id="overrideBtn_name"
                                                     class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
                                                     title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.zone.connector.toggle')}</div>">
                                                    <i class="fa fa-unlink fa-mp-btn"
                                                       style="font-size: 16px;"></i>
                                                </div>
                                            </c:if>
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                                </msgpt:TableItem>
                                </c:if>
                                <!-- Metatags -->
                                <msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
                                    <msgpt:TableItem label="page.label.metatags" cssClass="flowZoneProperty">
                                        <div style="white-space: nowrap;">
                                            <msgpt:InputFilter type="simpleName">
                                                <form:input id="zoneMetatags"
                                                            path="metatags"
                                                            placeholder="${msgpt:getMessage('page.label.optional')}"
                                                            cssClass="inputXXL overrideInput_name"
                                                            maxlength="255"/>
                                            </msgpt:InputFilter>
                                        </div>
                                    </msgpt:TableItem>
                                </msgpt:IfAuthGranted>

                                <!-- Enabled -->
                                <msgpt:TableItem label="page.label.enabled" cssClass="flowZoneProperty">
                                <div style="white-space: nowrap;">
	                                <div style="display: inline-block; position: relative;">
	                                    <form:checkbox id="zoneEnableToggle"
	                                                   cssClass="radioBtn overrideInput_enabled"
	                                                   path="zone.enabled"
	                                                   title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
	                                </div>
	                                <!-- OVERRIDE: Enabled -->
	                                <c:if test="${isAlternateZone}">
	                                    <form:checkbox path="zone.overrideEnabled" id="overrideCheckbox_enabled"
	                                                   cssClass="overrideCheckbox"/>
	                                    <div id="overrideBtn_enabled"
	                                         class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
	                                         title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.zone.enable.toggle')}</div>">
	                                        <i class="fa fa-unlink fa-mp-btn"
	                                           style="font-size: 16px;"></i>
	                                    </div>
	                                </c:if>
                                </div>
                                </msgpt:TableItem>

                                <!-- Visibility -->
                                <c:choose>
                                <c:when test="${empty workgroups}">
                                <!-- Info: No workgroups -->
                                <msgpt:TableItem label="page.label.visibility">
                                <div style="margin: 5px 0; padding: 5px 10px; background-color: #eee; border: 1px solid #bbb; color: #333;">
                                    <fmtSpring:message code="page.text.no.workgroups.defined"/>
                                </div>
                                </msgpt:TableItem>
                                </c:when>
                                <c:otherwise>
                                <!-- WORKGROUPS -->
                                <msgpt:TableItem label="page.label.visibility" style="white-space: nowrap">
                                <div>
                                    <div id="workgroupSelect" class="style_multiselect input325">
                                        <c:forEach var="currentWorkgroup" items="${workgroups}">
                                            <div id="workgroupOption_${currentWorkgroup.id}">
                                                <fmtSpring:bind path="zone.workgroups">
                                                    <input name="zone.workgroups" value="${currentWorkgroup.id}"
                                                           type="checkbox"
                                                        ${msgpt:contains( zone.workgroups, currentWorkgroup) ? 'checked="checked"' : '' }
                                                           class="style_multiselect_binding overrideInput_workgroups"/>
                                                    <input type="hidden" name="_zone.workgroups"/>
                                                </fmtSpring:bind>
                                                <span><c:out value="${currentWorkgroup.name}"/></span>
                                            </div>
                                        </c:forEach>
                                    </div>
                                    <!-- OVERRIDE: Workgroups -->
                                    <c:if test="${isAlternateZone}">
                                        <form:checkbox path="zone.overrideWorkgroups" id="overrideCheckbox_workgroups"
                                                       cssClass="overrideCheckbox"/>
                                            <div id="overrideBtn_workgroups"
                                                 class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
                                                 style="margin-left: 0px; border-width: 1px; padding: 0px;"
                                                 title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.zone.workgroups.toggle')}</div>">
                                                <i class="fa fa-unlink fa-mp-btn"
                                                   style="font-size: 16px;"></i>
                                            </div>
                                    </c:if>
                                </div>
                                </msgpt:TableItem>
                                <msgpt:TableItem><div style="margin: -24px">&nbsp;</div></msgpt:TableItem>
                                </c:otherwise>
                                </c:choose>

                                <c:if test="${(command.zone.document.isSefasCompositionTouchpoint || command.zone.document.isMPHCSCompositionTouchpoint)
                                                && not command.zone.multipart && not command.zone.isPDFPageZone && not command.zone.freeformPageZone}">
                                    <!-- Flow Zone -->
                                    <c:if test="${not isAlternateZone}">
                                        <msgpt:TableItem label="page.label.flow.zone" cssClass="flowZoneProperty flowZoneContainer">
                                            <div style="white-space: nowrap;">
                                                <form:checkbox id="flowZoneToggle"
                                                               cssClass="radioBtn"
                                                               path="zone.flowZone"
                                                               title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                            </div>
                                        </msgpt:TableItem>
                                    </c:if>
                                </c:if>

                                <!-- Backer - #zoneContentType = 205; #verticallyRelativeToSelect == 0 (.verticallyRelativeToToggleContainer); #flowIntoZoneSelect == -1 (.flowIntoZoneContainer);
                                    #flowZoneToggle:not(:checked) (.flowZoneContainer); rotationSelect == 0 (.rotationContainer)-->
                                <c:if test="${enabledBackerZonesFeatureFlag && !sectionHasPDFBacker &&
                                    !isHeaderOrFooterZone && !isFullPageZone && !isAlternateZone && !isPlaceholder &&
                                    !command.zone.multipart && !command.zone.isPDFPageZone && !command.zone.freeformPageZone &&
                                    (command.zone.document.isSefasCompositionTouchpoint || command.zone.document.isMPHCSCompositionTouchpoint) &&
                                    command.zone.document.isTemplateControlled }">
                                    <msgpt:TableItem label="page.label.backer.zone" cssClass="backerToggleContainer">
                                        <div style="white-space: nowrap;">
                                            <form:checkbox id="backerToggle"
                                                           cssClass="radioBtn"
                                                           path="zone.backer"
                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                        </div>
                                        <label for="backerToggle" style="position: absolute; top: 10px; left: 100px;">
                                        <span class="d-inline-block ml-2" data-toggle="tooltip"
                                              title="${msgpt:getMessage('page.text.backer.zone')}">
                                            <i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
                                        </span>
                                        </label>
                                    </msgpt:TableItem>
                                </c:if>

                                <!-- Content Type -->
                                <msgpt:TableItem label="page.label.content.type" cssClass="contentTypeToggleContainer">
	                                <c:choose>
		                                <c:when test="${command.zone.isEmailSubjectLine || document.isSmsTouchpoint || command.zone.isWebTitleLine}">
		                                    <fmtSpring:message code="${command.zone.contentType.name}"/>
		                                </c:when>
		                                <c:otherwise>
			                                <c:if test="${command.zone.combinedContentTypeId == multipartID || isAlternateZone || isPlaceholder || command.zone.isPDFPageZone}">
			                                    <fmtSpring:message code="${command.zone.contentType.name}"/>
			                                </c:if>
			                                <c:if test="${command.zone.freeformPageZone}">
			                                    <fmtSpring:message code="page.label.freeform"/>
			                                </c:if>
                                            <c:if test="${command.zone.isPDFPageZone}">
                                                (<fmtSpring:message code="page.text.pdf.page"/>)
                                            </c:if>
			                                <div style="${isAlternateZone || command.zone.combinedContentTypeId == multipartID || command.zone.freeformPageZone || isPlaceholder || command.zone.isPDFPageZone ? 'display: none;' : ''}">
			                                    <form:select id="zoneContentType"
			                                                 cssClass="style_select inputL zoneContentTypeSelect"
			                                                 path="zone.combinedContentTypeId" items="${contentTypes}"
			                                                 itemValue="id" itemLabel="name" onchange="toggleContentType();"/>
			                                </div>
		                                </c:otherwise>
	                                </c:choose>
	                                <input id="isFreeformPage" type="hidden" value="${command.zone.freeformPageZone}"/>
                                </msgpt:TableItem>
                                <c:if test="${not isAlternateZone}">
	                                <!-- Freeform -->
	                                <c:if test="${not command.zone.freeformPageZone && not document.isSmsTouchpoint && not zone.multipart && not isPlaceholder}">
		                                <msgpt:TableItem label="page.label.freeform" cssClass="freeformToggleContainer">
		                                    <form:checkbox id="freeformToggle" path="isFreeformZone"
		                                                   title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
		                                </msgpt:TableItem>
	                                </c:if>
	                                <!-- Markup -->
	                                <c:if test="${isGMCTouchpoint}">
		                                <msgpt:TableItem label="page.label.markup" cssClass="markupToggleContainer">
		                                    <form:checkbox id="markupToggle" path="isMarkupZone"
		                                                   title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
		                                </msgpt:TableItem>
	                                </c:if>
                                </c:if>
                                <c:if test="${not command.zone.isEmailSubjectLine && not document.isSmsTouchpoint && not command.zone.isWebTitleLine && not hasCommunications && not isAlternateZone}">
                                    <!-- Data Group -->
                                    <msgpt:TableItem label="page.label.touchpoint.container.zone.datagroup" cssClass="dataGroupSelectContainer">
                                        <div>
                                            <form:select id="dataGroupSelect" cssClass="style_select inputL" path="dataGroupId">
                                                <form:option value="0"><i><fmtSpring:message code="page.label.none" /></i></form:option>
                                                <form:option value="-99"><i><fmtSpring:message code="page.label.mixed" /></i></form:option>
                                                <c:forEach items="${dataGroups}" var="currentDataGroup">
                                                    <form:option value="${currentDataGroup.id}"><c:out value="${currentDataGroup.name}"/> (<c:out value="${currentDataGroup.level}"/>)</form:option>
                                                </c:forEach>
                                            </form:select>
                                        </div>
                                    </msgpt:TableItem>
                                    <msgpt:TableItem><div style="margin: -24px;">&nbsp;</div></msgpt:TableItem>
                                </c:if>
                                <c:if test="${not isAlternateZone}">
                                    <c:if test="${not document.isSmsTouchpoint}">
                                        <!-- Tables -->
                                        <msgpt:TableItem label="page.label.tables" cssClass="tablesToggleContainer">
                                            <form:checkbox id="tablesToggle" path="zone.supportsTables"
                                                           title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
                                        </msgpt:TableItem>
                                        <c:if test="${isNativeCompositionTouchpoint}">
                                            <!-- Forms -->
                                            <msgpt:TableItem label="page.label.forms" cssClass="formsToggleContainer">
                                                <form:checkbox id="formsToggle" path="zone.supportsForms"
                                                               title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
                                            </msgpt:TableItem>
                                            <!-- Barcode -->
                                            <msgpt:TableItem label="page.label.barcode" cssClass="barcodeToggleContainer">
                                                <form:checkbox id="barcodeToggle" path="zone.supportsBarcodes"
                                                               title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
                                            </msgpt:TableItem>
                                        </c:if>
                                    </c:if>

                                    <!-- Alternative Text -->
                                    <msgpt:TableItem label="page.label.alternative_text"
                                                     cssClass="applyAltTextToggleContainer">
                                        <form:checkbox id="altTextToggle" path="zone.applyAltText"
                                                       title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
                                    </msgpt:TableItem>

                                    <!-- Accessibility zone read order (Sequence) -->
                                    <c:if test="${(command.zone.document.isSefasCompositionTouchpoint || command.zone.document.isMPHCSCompositionTouchpoint) && isBodyZone && command.zone.document.isTemplateControlled && isPrimaryLayoutSelected}">
                                        <msgpt:TableItem label="page.label.accessibility.sequence" cssClass="flowZoneProperty">
                                            <div style="white-space: nowrap;">
                                                <msgpt:InputFilter type="numeric">
                                                    <form:input cssClass="input2digit overrideInput_name" path="zone.sequence"
                                                                id="zoneSequence" maxlength="6"/>
                                                </msgpt:InputFilter>
                                                <div style="display: inline-block">
                                                    <span id="sequenceIndicator" style="padding-left: 8px; ${empty command.zone.sequence ? '' : 'display: none;'}">
                                                        (<fmtSpring:message code="page.label.inferred.sequence"/>: ${inferredSequence})
                                                    </span>
                                                </div>
                                            </div>
                                        </msgpt:TableItem>
                                    </c:if>

                                    <c:if test="${command.zone.hasAtLeastOneGraphicPart || not command.zone.multipart}">
                                        <c:if test="${command.zone.document.isWebTouchpoint || command.zone.document.isEmailTouchpoint || command.zone.document.isOmniChannel}">
                                            <!-- Image Link -->
                                            <msgpt:TableItem label="page.label.image.link" cssClass="applyImageLinkToggleContainer">
                                                <form:checkbox id="imageLinkToggle" path="zone.applyImageLink"
                                                               title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
                                            </msgpt:TableItem>
                                        </c:if>

                                        <c:if test="${command.zone.document.isSefasCompositionTouchpoint || command.zone.document.isMPHCSCompositionTouchpoint}">
                                            <!-- External Path -->
                                            <msgpt:TableItem label="page.label.external.path" cssClass="applyImageExtPathToggleContainer">
                                                <form:checkbox id="extPathToggle" path="zone.applyImageExtPath"
                                                               title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
                                            </msgpt:TableItem>
                                        </c:if>
                                    </c:if>
                                </c:if>

                                <c:if test="${command.zone.hasAtLeastOneGraphicPart || not command.zone.multipart}">
                                    <c:if test="${command.zone.document.isWebTouchpoint || command.zone.document.isEmailTouchpoint}">
                                        <!-- External Link -->
                                        <msgpt:TableItem label="page.label.external.link" cssClass="applyImageExtLinkToggleContainer">
                                            <form:checkbox id="extLinkToggle" path="zone.applyImageExtLink"
                                                           title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
                                        </msgpt:TableItem>
                                    </c:if>
                                </c:if>

                                <c:if test="${not isPlaceholder && (command.zone.document.isNativeCompositionTouchpoint || command.zone.document.isSefasCompositionTouchpoint || command.zone.document.isMPHCSCompositionTouchpoint) && not isAlternateZone}">
                                <msgpt:TableItem><div style="margin: -24px">&nbsp;</div></msgpt:TableItem>
                                <msgpt:TableItem label="page.label.rotation" cssClass="rotationContainer">
                                <div>
                                    <form:select id="rotationSelect" path="zone.rotationAngle"
                                                 cssClass="style_select input3digit">
                                        <form:option value="0">None</form:option>
                                        <form:option value="90">90&deg;</form:option>
                                        <form:option value="180">180&deg;</form:option>
                                        <form:option value="270">270&deg;</form:option>
                                    </form:select>
                                    <label for="rotationSelect" style="position: absolute; top: 10px; left: 100px;">
                                        <fmtSpring:message code="page.text.beta.version"/>
                                        <span class="d-inline-block ml-2" data-toggle="tooltip"
                                              title="${msgpt:getMessage('page.text.mark.beta.version')}">
                                            <i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
                                        </span>
                                    </label>
                                </div>
                                </msgpt:TableItem>
                                </c:if>

                                <!-- Vertical Alignment -->
                                <c:if test="${not command.zone.multipart && not isPlaceholder && (command.zone.document.isSefasCompositionTouchpoint || command.zone.document.isMPHCSCompositionTouchpoint) && not isAlternateZone}">
                                    <msgpt:TableItem label="page.label.zone.vertical.alignment">
                                        <form:select id="verticalAlignmentSelect" path="zone.verticalAlignment"
                                                     cssClass="style_select input5digit">
                                            <c:forEach var="currentAlignment" items="${verticalAlignments}">
                                                <form:option value="${currentAlignment.id}">
                                                    <fmtSpring:message code="${currentAlignment.displayMessageCode}"/>
                                                </form:option>
                                            </c:forEach>
                                        </form:select>
                                        <label for="verticalAlignmentSelect" style="position: absolute; top: 10px; left: 110px;">
                                            <fmtSpring:message code="page.text.beta.version"/>
                                            <span class="d-inline-block ml-2" data-toggle="tooltip"
                                                  title="${msgpt:getMessage('page.text.mark.beta.version')}">
                                                    <i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
                                                </span>
                                        </label>
                                    </msgpt:TableItem>
                                </c:if>



                                <c:if test="${(command.zone.document.isSefasCompositionTouchpoint || command.zone.document.isMPHCSCompositionTouchpoint) &&
                                                not command.zone.multipart && not command.zone.isPDFPageZone && not command.zone.freeformPageZone}">
                                    <!-- Flow Into Zone: Select -->
                                    <msgpt:TableItem label="page.label.target.flow.zone" cssClass="flowIntoZoneContainer">
                                        <form:select id="flowIntoZoneSelect" path="zone.flowIntoZone" cssClass="style_select input325">
                                            <form:option value="-1"><fmtSpring:message code="page.label.none"/></form:option>
                                            <c:forEach var="currentZone" items="${zones}">
                                                <form:option value="${currentZone.id}">
                                                    <c:out value="${currentZone.friendlyName}"/> (<c:out value="${currentZone.section.name}"/>)
                                                </form:option>
                                            </c:forEach>
                                        </form:select>
                                    </msgpt:TableItem>
                                </c:if>

                                <!-- Vertically Relative to: Select -->
                                <c:if test="${(!isHeaderOrFooterZone && !isFullPageZone) && (command.zone.document.isSefasCompositionTouchpoint || command.zone.document.isMPHCSCompositionTouchpoint) && command.zone.document.isTemplateControlled}">
                                    <msgpt:TableItem label="page.label.vertically.relative.to" cssClass="verticallyRelativeToToggleContainer">
                                        <form:select id="verticallyRelativeToSelect" path="zone.verticallyRelativeTo"
                                                     cssClass="style_select input325" onchange="toggleAboveInputs()">
                                            <c:forEach var="verticallyRelativeTo" items="${verticallyRelativeToOptions}">
                                                <form:option value="${verticallyRelativeTo.id}">
                                                    <fmtSpring:message code="${verticallyRelativeTo.displayMessageCode}"/>
                                                </form:option>
                                            </c:forEach>
                                        </form:select>
                                        <label for="rotationSelect" style="position: absolute; top: 10px; left: 100px;">
                                            <fmtSpring:message code="page.text.beta.version"/>
                                            <span class="d-inline-block ml-2" data-toggle="tooltip"
                                                  title="${msgpt:getMessage('page.text.mark.beta.version')}">
                                                <i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
                                            </span>
                                        </label>
                                    </msgpt:TableItem>
                                    <msgpt:TableItem label="page.label.relative.distance.in" cssClass="aboveInput">
                                        <msgpt:InputFilter type="decimal">
                                            <form:input path="zone.relativeDistanceInInches" cssClass="inputS" maxlength="9" />
                                            <span class="d-inline-block ml-2" data-toggle="tooltip"
                                                  title="${msgpt:getMessage('page.text.relative.distance')}">
                                                <i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
                                            </span>
                                        </msgpt:InputFilter>
                                    </msgpt:TableItem>
                                    <msgpt:TableItem label="page.label.minimum.keep.together.in" cssClass="aboveInput">
                                        <msgpt:InputFilter type="decimal">
                                            <form:input path="zone.minimumKeepTogetherInInches" cssClass="inputS" maxlength="9"/>
                                            <span class="d-inline-block ml-2" data-toggle="tooltip"
                                                  title="${msgpt:getMessage('page.text.minimum.keep.together')}">
                                                <i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
                                            </span>
                                        </msgpt:InputFilter>
                                    </msgpt:TableItem>
                                </c:if>

                                <!-- Placeholder: Default canvas width -->
                                <c:if test="${isPlaceholder}">
                                	<msgpt:TableItem label="page.label.default.canvas.width">
                                		<msgpt:InputFilter type="decimal">
                                			<form:input path="defaultCanvasWidth" cssClass="inputS" />&nbsp;<fmtSpring:message code="page.label.inches.short"/>.
                                		</msgpt:InputFilter>
                                	</msgpt:TableItem>
                                </c:if>


                                </msgpt:DataTable>

                                <!-- BACKGROUND -->
                                <c:if test="${not isPlaceholder && not document.isSmsTouchpoint && not command.zone.freeformPageZone}">
                                <msgpt:DataTable labelPosition="top">
                                    <msgpt:TableHeader label="page.label.background"/>
                                <msgpt:TableItem label="page.text.transparent.background">
                                <table width="100%" class="" cellspacing="0" cellpadding="0"
                                       border="0">
                                    <tr>
                                        <td align="left" style="padding: 0px; vertical-align: middle;">
                                            <form:checkbox id="isBackgroundTransparent" path="isBackgroundTransparent"
                                                           cssClass="overrideInput_backgroundColor"
                                                           title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                        </td>
                                        <td align="right" style="padding: 0px; vertical-align: middle;">
                                            <!-- OVERRIDE: Background Color -->
                                            <c:if test="${isAlternateZone && not command.zone.isEmailSubjectLine && not command.zone.isWebTitleLine}">
                                                <form:checkbox path="zone.overrideBackgroundColor"
                                                               id="overrideCheckbox_backgroundColor"
                                                               cssClass="overrideCheckbox"/>
                                                <div id="overrideBtn_backgroundColor"
                                                     class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
                                                     style="margin-right: 4px;"
                                                     title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.zone.background.toggle')}</div>">
                                                    <i class="fa fa-unlink fa-mp-btn"
                                                       style="font-size: 16px;"></i>
                                                </div>
                                            </c:if>
                                        </td>
                                    </tr>
                                </table>
                                </msgpt:TableItem>
                                <msgpt:TableItem label="page.label.background.color"
                                                 cssClass="backgroundColorSelectContainer"
                                                 style="${command.isBackgroundTransparent ? 'display: none;' : ''}">
                                    <form:input id="colorBinding" path="zone.backgroundColor"
                                                cssClass="colorPickerRawBinding2 overrideInput_backgroundColor"
                                                cssStyle="display:none;"/>
                                <div id="customWidget">
                                    <div id="colorSelector2">
                                        <div style="background-color: #${command.zone.backgroundColor}"></div>
                                    </div>
                                    <div id="colorpickerHolder2">
                                    </div>
                                </div>
                                </msgpt:TableItem>
                                </msgpt:DataTable>
                                </c:if> <!-- END BACKGROUND -->

                                <!-- LIBRARY -->
                                <c:if test="${not isAlternateZone || command.isInteractiveZone}">
                                <msgpt:DataTable labelPosition="top" id="connectedPropertiesTable">
                                    <msgpt:TableHeader label="page.label.library"/>
                                <msgpt:TableItem label="page.label.access">
                                <table width="100%" class="" cellspacing="0" cellpadding="0"
                                       border="0">
                                    <tr>
                                        <td align="left" style="padding: 0px; vertical-align: middle;">
                                            <form:checkbox id="libraryAssetsToggle"
                                                           cssClass="radioBtn overrideInput_sharedAssets"
                                                           path="zone.restrictSharedAssets"
                                                           title="${msgpt:getMessage('page.label.limited')};${msgpt:getMessage('page.label.all')}"/>
                                        </td>
                                        <td align="right" style="padding: 0px; vertical-align: middle;">
                                            <!-- OVERRIDE: Shared Assets -->
                                            <c:if test="${isAlternateZone}">
                                                <form:checkbox path="zone.overrideSharedAssets"
                                                               id="overrideCheckbox_sharedAssets"
                                                               cssClass="overrideCheckbox"/>
                                                <div id="overrideBtn_sharedAssets"
                                                     class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
                                                     style="margin-left: 0px;"
                                                     title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.zone.shared.assets.toggle')}</div>">
                                                    <i class="fa fa-unlink fa-mp-btn"
                                                       style="font-size: 16px;"></i>
                                                </div>
                                            </c:if>
                                        </td>
                                    </tr>
                                </table>
                                </msgpt:TableItem>
                                <!-- Smart Text Assets -->
                                <msgpt:TableItem cssClass="assetsContainer_smartText" label="page.label.smart.text">
                                <div id="sharedAssetsContainer_smartText"
                                     style="display: inline-block; margin-bottom: 3px;">
                                    <div id="smartTextSelectContainer" style="display: none;">
                                        <div id="smartTextSelect" class="style_multiselect inputXL" tagCloudType="2"
                                             typeLabel="smart_assets" currentDocumentId="${document.id}"
                                             style="display: none;">
                                            <!-- CURRENT ITEM SELECTIONS -->
                                            <c:forEach var="currentSmartText" items="${zone.smartTextAssets}">
                                                <div id="smartTextOption_${currentSmartText.id}"
                                                     tags="${currentSmartText.metatags}">
                                                    <!--Input implements redundant check onclick to offset onclick check implemented by parent div-->
                                                    <input id="checkbox_smartText_${currentSmartText.id}"
                                                           name="zone.smartTextAssets"
                                                           value="${currentSmartText.id}"
                                                           type="checkbox" checked="checked"
                                                           class="style_multiselect_binding overrideInput_sharedAssets"/>
                                                    <input type="hidden" name="_zone.smartTextAssets"/>
                                                    <span><c:out value="${currentSmartText.name}"/></span>
                                                </div>
                                            </c:forEach>
                                            <!-- BINDING TEMPLATE -->
                                            <script class="optionTemplate" type="text/x-handlebars-template">
                                                <div id="smartTextOption_{{optionId}}" {{metatags}}>
                                                    <input id="checkbox_smartText_{{optionId}}"
                                                           name="zone.smartTextAssets" value="{{optionId}}"
                                                           type="checkbox"
                                                           class="style_multiselect_binding overrideInput_sharedAssets"
                                                           {{selected}}/>
                                                    <input type="hidden" name="_zone.smartTextAssets"/>
                                                    <span>{{optionName}}</span>
                                                </div>
                                            </script>
                                        </div>
                                    </div>
                                    <div id="connectedSmartTextSelectContainer" style="display: none;">
                                        <div id="connectedSmartTextSelect" class="style_multiselect inputXL"
                                             tagCloudType="2" typeLabel="smart_assets"
                                             currentDocumentId="${document.id}" style="display: none;">
                                            <!-- CURRENT ITEM SELECTIONS -->
                                            <c:forEach var="currentSmartText" items="${zone.smartTextAssets}">
                                                <div id="connectedSmartTextOption_${currentSmartText.id}"
                                                     tags="${currentSmartText.metatags}">
                                                    <!--Input implements redundant check onclick to offset onclick check implemented by parent div-->
                                                    <input id="checkbox_connectedSmartText_${currentSmartText.id}"
                                                           name="zone.smartTextAssets"
                                                           value="${currentSmartText.id}"
                                                           type="checkbox" checked="checked"
                                                           class="style_multiselect_binding overrideInput_sharedAssets"/>
                                                    <input type="hidden" name="_zone.smartTextAssets"/>
                                                    <span><c:out value="${currentSmartText.name}"/></span>
                                                </div>
                                            </c:forEach>
                                            <!-- BINDING TEMPLATE -->
                                            <script class="optionTemplate" type="text/x-handlebars-template">
                                                <div id="connectedSmartTextOption_{{optionId}}" {{metatags}}>
                                                    <input id="checkbox_connectedSmartText_{{optionId}}"
                                                           name="zone.smartTextAssets" value="{{optionId}}"
                                                           type="checkbox"
                                                           class="style_multiselect_binding overrideInput_sharedAssets"
                                                           {{selected}}/>
                                                    <input type="hidden" name="_zone.smartTextAssets"/>
                                                    <span>{{optionName}}</span>
                                                </div>
                                            </script>
                                        </div>
                                    </div>
                                </div>
                                </msgpt:TableItem>
                                <!-- Image Library Assets -->
                                <msgpt:TableItem cssClass="assetsContainer_imageLibrary" label="page.label.image.library">
                                <div id="sharedAssetsContainer_imageLibrary"
                                     style="display: inline-block;">
                                    <div id="imageSelectContainer" style="display: none;">
                                        <div id="imageSelect" class="style_multiselect inputXL" tagCloudType="3"
                                             typeLabel="image_library" currentDocumentId="${document.id}"
                                             style="display: none;">
                                            <!-- CURRENT ITEM SELECTIONS -->
                                            <c:forEach var="currentImage" items="${zone.imageAssets}">
                                                <div id="imageOption_${currentImage.id}"
                                                     tags="${currentImage.metatags}">
                                                    <!--Input implements redundant check onclick to offset onclick check implemented by parent div-->
                                                    <input id="checkbox_image_${currentImage.id}"
                                                           name="zone.imageAssets" value="${currentImage.id}"
                                                           type="checkbox" checked="checked"
                                                           class="style_multiselect_binding overrideInput_sharedAssets"/>
                                                    <input type="hidden" name="_zone.imageAssets"/>
                                                    <span><c:out value="${currentImage.name}"/></span>
                                                </div>
                                            </c:forEach>
                                            <!-- BINDING TEMPLATE -->
                                            <script class="optionTemplate" type="text/x-handlebars-template">
                                                <div id="imageOption_{{optionId}}" {{metatags}}>
                                                    <input id="checkbox_image_{{optionId}}"
                                                           name="zone.imageAssets" value="{{optionId}}"
                                                           type="checkbox"
                                                           class="style_multiselect_binding overrideInput_sharedAssets"
                                                           {{selected}}/>
                                                    <input type="hidden" name="_zone.imageAssets"/>
                                                    <span>{{optionName}}</span>
                                                </div>
                                            </script>
                                        </div>
                                    </div>
                                    <div id="connectedImageSelectContainer" style="display: none;">
                                        <div id="connectedImageSelect" class="style_multiselect inputXL"
                                             tagCloudType="3" typeLabel="image_library"
                                             currentDocumentId="${document.id}" style="display: none;">
                                            <!-- CURRENT ITEM SELECTIONS -->
                                            <c:forEach var="currentImage" items="${zone.imageAssets}">
                                                <div id="connectedImageOption_${currentImage.id}"
                                                     tags="${currentImage.metatags}">
                                                    <!--Input implements redundant check onclick to offset onclick check implemented by parent div-->
                                                    <input id="checkbox_connectedImage_${currentImage.id}"
                                                           name="zone.imageAssets" value="${currentImage.id}"
                                                           type="checkbox" checked="checked"
                                                           class="style_multiselect_binding overrideInput_sharedAssets"/>
                                                    <input type="hidden" name="_zone.imageAssets"/>
                                                    <span><c:out value="${currentImage.name}"/></span>
                                                </div>
                                            </c:forEach>
                                            <!-- BINDING TEMPLATE -->
                                            <script class="optionTemplate" type="text/x-handlebars-template">
                                                <div id="connectedImageOption_{{optionId}}" {{metatags}}>
                                                    <input id="checkbox_connectedImage_{{optionId}}"
                                                           name="zone.imageAssets" value="{{optionId}}"
                                                           type="checkbox"
                                                           class="style_multiselect_binding overrideInput_sharedAssets"
                                                           {{selected}}/>
                                                    <input type="hidden" name="_zone.imageAssets"/>
                                                    <span>{{optionName}}</span>
                                                </div>
                                            </script>
                                        </div>
                                    </div>
                                </div>
                                </msgpt:TableItem>
                                <c:if test="${not command.zone.multipart}">
                                <msgpt:TableItem label="page.text.library.items.only"
                                                 cssClass="messageContentEditingContainer">
                                    <form:checkbox id="isMessageLibraryItemsOnly" path="zone.messageContentEditing"
                                                   title="${msgpt:getMessage('page.label.no')};${msgpt:getMessage('page.label.yes')}"/>
                                </msgpt:TableItem>
                                </c:if>
                                </msgpt:DataTable>
                                </c:if>
                                <!-- END LIBRARY -->

                                <!-- CONNECTED -->
                                <c:if test="${isBodyZone && licencedForMessagepointInteractive && !document.isSmsTouchpoint && (document.connectedEnabled || command.isInteractiveZone)}">
                                <msgpt:DataTable labelPosition="top" id="interactiveZoneInterface"
                                                 style="display: none;">
                                    <msgpt:TableHeader label="page.label.interactive"/>
                                <msgpt:TableItem label="page.text.enabled">
                                <table width="100%" class="" cellspacing="0" cellpadding="0"
                                       border="0">
                                    <tr>
                                        <td align="left" style="padding: 0px; vertical-align: middle;">
                                            <c:if test="${hasCommunications || hasMessageDeliveries || isAlternateZone}">
                                                <c:choose>
                                                    <c:when test="${command.isInteractiveZone}">
                                                        <fmtSpring:message code="page.label.yes"/>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <fmtSpring:message code="page.label.no"/>
                                                    </c:otherwise>
                                                </c:choose>
                                            </c:if>
                                            <div style="${hasCommunications || hasMessageDeliveries || isAlternateZone ? 'display: none;' : ''}">
                                                <form:checkbox id="isInteractiveZone" path="isInteractiveZone"
                                                               title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"/>
                                            </div>
                                        </td>
                                        <td align="right" style="padding: 0px; vertical-align: middle;">
                                            <!-- OVERRIDE: Connected Template -->
                                            <c:if test="${isAlternateZone}">
                                                <form:checkbox path="zone.overrideCommunicationTemplate"
                                                               id="overrideCheckbox_communicationTemplate"
                                                               cssClass="overrideCheckbox"/>
                                                <div id="overrideBtn_communicationTemplate"
                                                     class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
                                                     style="margin-left: 0px;"
                                                     title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.zone.connected.template.toggle')}</div>">
                                                    <i class="fa fa-unlink fa-mp-btn"
                                                       style="font-size: 16px;"></i>
                                                </div>
                                            </c:if>
                                        </td>
                                    </tr>
                                </table>
                                </msgpt:TableItem>

                                <c:if test="${!hasMessageDeliveries && !isGraphicData}">

                                <msgpt:TableItem label="page.label.image.variable"
                                                 cssClass="communicationTemplatesContainer_variable">
                                    <div>
                                        <form:select path="zone.communicationDataImageVariable" items="${dataElementVariables}" itemLabel="name" itemValue="id" />
                                    </div>
                                </msgpt:TableItem>

                                <msgpt:TableItem label="page.label.embedded.content"
                                                 cssClass="communicationTemplatesContainer_text">
                                <div>
                                    <c:set var="selectedText"
                                           value="${command.zone.defaultCommunicationTemplateSmartText}"/>
                                    <form:select id="communicationTemplateSelect_text"
                                                 path="zone.defaultCommunicationTemplateSmartText"
                                                 cssClass="style_select input325 overrideInput_communicationTemplate">
                                        <form:option id="smartTextOption_0" value="0"><fmtSpring:message
                                                code="page.label.none"/></form:option>
                                        <c:if test="${not empty selectedText}">
                                            <form:option id="smartTextOption_${selectedText.id}"
                                                         value="${selectedText.id}"><c:out
                                                    value="${selectedText.name}"/></form:option>
                                        </c:if>
                                    </form:select>
                                </div>
                                </msgpt:TableItem>
                                <msgpt:TableItem label="page.label.template"
                                                 cssClass="communicationTemplatesContainer_image">
                                <div>
                                    <c:set var="selectedImage"
                                           value="${command.zone.defaultCommunicationTemplateImage}"/>
                                    <form:select id="communicationTemplateSelect_image"
                                                 path="zone.defaultCommunicationTemplateImage"
                                                 cssClass="style_select input325 overrideInput_communicationTemplate">
                                        <form:option id="imageOption_0" value="0"><fmtSpring:message
                                                code="page.label.none"/></form:option>
                                        <c:if test="${not empty selectedImage}">
                                            <form:option id="imageOption_${selectedImage.id}"
                                                         value="${selectedImage.id}"><c:out
                                                    value="${selectedImage.name}"/></form:option>
                                        </c:if>
                                    </form:select>
                                </div>
                                </msgpt:TableItem>
                                <c:if test="${not isAlternateZone}">
                                <msgpt:TableItem label="page.text.library.items.only" cssClass="connectedLibraryItemsOnlyContainer">
                                    <form:checkbox id="isConnectedLibraryItemsOnly"
                                                   path="zone.communicationContentEditing"
                                                   title="${msgpt:getMessage('page.label.no')};${msgpt:getMessage('page.label.yes')}"/>
                                </msgpt:TableItem>
                                </c:if>

                                </c:if>
                                </msgpt:DataTable>
                                </c:if> <!-- END CONNECTED -->

                                <!-- STYLES -->
                                <c:if test="${not command.zone.isEmailSubjectLine && not document.isSmsTouchpoint && not command.zone.isWebTitleLine}">
                                <msgpt:DataTable labelPosition="top" id="stylesTable" style="display: none;">
                                    <msgpt:TableHeader label="page.label.styles"/>
                                <!-- Text Styles -->
                                <c:choose>
                                <c:when test="${not hasTextStyle}">
                                <!-- Info: No text styles -->
                                <msgpt:TableItem label="page.label.text.styles" cssClass="textStylesSelectContainer">
                                <div style="margin: 5px 0; padding: 5px 10px; background-color: #eee; border: 1px solid #bbb; color: #333;">
                                    <fmtSpring:message code="page.text.no.text.styles"/>
                                </div>
                                </msgpt:TableItem>
                                </c:when>
                                <c:otherwise>
                                <msgpt:TableItem label="page.label.starter.text.style" cssClass="starterTextStyleSelectContainer">
                                    <div style="white-space: nowrap;">
                                        <div style="display: inline-block;">
                                            <form:select
                                                    id="starterTextStyleSelect"
                                                    path="zone.starterTextStyle"
                                                    class="complex-dropdown-select"
                                                    title="${msgpt:getMessage('page.label.starter.text.style')}"
                                                    aria-label="${msgpt:getMessage('page.label.starter.text.style')}"
                                                    data-enablefilter="true"
                                                    data-menu-class="dropdown-menu-left w-100"
                                                    data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                                    data-show-titles="true"
                                                    data-titles-max-length="55"
                                            >
                                                <c:if test="${not empty zone.starterTextStyle }">
                                                    <option value="${zone.starterTextStyle.id}" selected="selected"><c:out
                                                            value="${zone.starterTextStyle.name}"/></option>
                                                </c:if>
                                            </form:select>
                                        </div>
                                        <label>
                                            <span class="d-inline-block ml-2" data-toggle="tooltip"
                                                  title="${msgpt:getMessage('page.text.mark.starter.text.style')}">
                                                <i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
                                            </span>
                                        </label>
                                    </div>
                                </msgpt:TableItem>
                                <!-- Select: Default text style -->
                                <msgpt:TableItem label="page.label.default.text.style"
                                                 cssClass="defaultTextStyleSelectContainer">
                                <div style="white-space: nowrap;">
                                    <div style="display: inline-block;">
                                        <form:select
                                            id="defaultTextStyleSelect"
                                            path="zone.defaultTextStyle"
                                            class="complex-dropdown-select"
                                            title="${msgpt:getMessage('page.label.default.text.style')}"
                                            aria-label="${msgpt:getMessage('page.label.default.text.style')}"
                                            data-enablefilter="true"
                                            data-menu-class="dropdown-menu-left w-100"
                                            data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                            data-show-titles="true"
                                            data-titles-max-length="55"
                                        >
                                            <c:if test="${not empty zone.defaultTextStyle }">
                                                <option value="${zone.defaultTextStyle.id}" selected="selected"><c:out
                                                        value="${zone.defaultTextStyle.name}"/></option>
                                            </c:if>
                                        </form:select>
                                    </div>
                                    <!-- OVERRIDE: Default Text Style -->
                                    <c:if test="${isAlternateZone}">
                                        <form:checkbox path="zone.overrideDefaultTextStyle"
                                                       id="overrideCheckbox_defaultTextStyle"
                                                       cssClass="overrideCheckbox"/>
                                        <div id="overrideBtn_defaultTextStyle"
                                             class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
                                             style="margin-left: 0px;"
                                             title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.zone.default.text.style.toggle')}</div>">
                                            <i class="fa fa-unlink fa-mp-btn"
                                               style="font-size: 16px;"></i>
                                        </div>
                                    </c:if>
                                    <label for="rotationSelect">
                                            <span class="d-inline-block ml-2" data-toggle="tooltip"
                                                  title="${msgpt:getMessage('page.text.mark.default.text.style')}">
                                                <i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
                                            </span>
                                    </label>
                                </div>
                                </msgpt:TableItem>
                                <!-- Check select: Text styles -->
                                <msgpt:TableItem label="page.label.text.styles" cssClass="textStylesSelectContainer"
                                                 style="white-space: nowrap;">
                                <div>
                                    <div style="display: inline-block;">
                                        <form:select
                                            id="textStyleSelect"
                                            path="zone.styles"
                                            class="complex-dropdown-select"
                                            title="${msgpt:getMessage('page.label.text.styles')}"
                                            aria-label="${msgpt:getMessage('page.label.text.styles')}"
                                            data-enablefilter="true"
                                            data-enable-selectall="true"
                                            data-enable-view-selected="true"
                                            data-menu-class="dropdown-menu-left w-100"
                                            data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                            data-show-titles="true"
                                            data-titles-max-length="70"
                                            multiple="true"
                                        >
                                            <!-- CURRENT ITEM SELECTIONS -->
                                            <c:forEach var="currentTextStyle" items="${command.zone.styles}">
                                                <option id="textStyleOption_${currentTextStyle.id}"
                                                        class="ml-0" value="${currentTextStyle.id}"
                                                        selected="selected">
                                                        ${currentTextStyle.name}
                                                </option>
                                            </c:forEach>
                                        </form:select>
                                    </div>
                                    <!-- OVERRIDE: Text Styles -->
                                    <c:if test="${isAlternateZone}">
                                        <form:checkbox path="zone.overrideTextStyles" id="overrideCheckbox_textStyles"
                                                       cssClass="overrideCheckbox"/>
                                        <div id="overrideBtn_textStyles"
                                             class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
                                             style="margin-left: 0px; border-width: 1px; padding: 0px;"
                                             title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.zone.text.styles.toggle')}</div>">
                                            <i class="fa fa-unlink fa-mp-btn"
                                               style="font-size: 16px;"></i>
                                        </div>
                                    </c:if>
                                </div>
                                </msgpt:TableItem>
                                </c:otherwise>
                                </c:choose>

                                <!-- Paragraph Styles -->
                                <c:choose>
                                <c:when test="${not hasParagraphStyle}">
                                <!-- Info: No paragraph styles -->
                                <msgpt:TableItem label="page.label.paragraph.styles"
                                                 cssClass="paragraphStylesSelectContainer">
                                <div style="margin: 5px 0; padding: 5px 10px; background-color: #eee; border: 1px solid #bbb; color: #333;">
                                    <fmtSpring:message code="page.text.no.para.styles"/>
                                </div>
                                </msgpt:TableItem>
                                </c:when>
                                <c:otherwise>
                                <!-- Select: Default paragraph style -->
                                <msgpt:TableItem label="page.label.default.paragraph.style"
                                                 cssClass="defaultParagraphStyleSelectContainer">
                                <div style="white-space: nowrap;">
                                    <div style="display: inline-block;">
                                        <form:select
                                            id="defaultParagraphStyleSelect"
                                            path="zone.defaultParagraphStyle"
                                            class="complex-dropdown-select"
                                            title="${msgpt:getMessage('page.label.default.paragraph.style')}"
                                            aria-label="${msgpt:getMessage('page.label.default.paragraph.style')}"
                                            data-enablefilter="true"
                                            data-menu-class="dropdown-menu-left w-100"
                                            data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                            data-show-titles="true"
                                            data-titles-max-length="70"
                                        >
                                            <c:if test="${not empty zone.defaultParagraphStyle }">
                                                <option value="${zone.defaultParagraphStyle.id}" selected="selected">
                                                    <c:out value="${zone.defaultParagraphStyle.name}"/></option>
                                            </c:if>
                                        </form:select>
                                    </div>
                                    <!-- OVERRIDE: Default Paragraph Style -->
                                    <c:if test="${isAlternateZone}">
                                        <form:checkbox path="zone.overrideDefaultParagraphStyle"
                                                       id="overrideCheckbox_defaultParagraphStyle"
                                                       cssClass="overrideCheckbox"/>
                                        <div id="overrideBtn_defaultParagraphStyle"
                                             class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
                                             style="margin-left: 0px;"
                                             title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.zone.default.paragraph.style.toggle')}</div>">
                                            <i class="fa fa-unlink fa-mp-btn"
                                               style="font-size: 16px;"></i>
                                        </div>
                                    </c:if>
                                </div>
                                </msgpt:TableItem>
                                <!-- Custom Paragraph Styles -->
                                <%--<c:if test="${((command.zone.document.isSefasCompositionTouchpoint || command.zone.document.isMPHCSCompositionTouchpoint) && command.zone.document.isTemplateControlled ) ||
                                (command.zone.dxfOutput || command.zone.document.enabledForDXFOutput) ||
                                (isGMCTouchpoint && controlStyles)}">--%>
                                    <msgpt:TableItem label="page.label.custom.paragraph.styles">
                                        <form:checkbox id="customParagraphStylesToggle" path="zone.supportsCustomParagraphs"
                                                       title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
                                    </msgpt:TableItem>
                              <%--  </c:if>--%>
                                <!-- PARAGRAPH STYLES -->
                                <msgpt:TableItem label="page.label.paragraph.styles"
                                                 cssClass="paragraphStylesSelectContainer" style="white-space: nowrap;">
                                <div>
                                    <div style="display: inline-block;">
                                        <form:select
                                            id="paragraphStyleSelect"
                                            path="zone.paragraphStyles"
                                            class="complex-dropdown-select"
                                            title="${msgpt:getMessage('page.label.paragraph.styles')}"
                                            aria-label="${msgpt:getMessage('page.label.paragraph.styles')}"
                                            data-enablefilter="true"
                                            data-enable-selectall="true"
                                            data-enable-view-selected="true"
                                            data-menu-class="dropdown-menu-left w-100"
                                            data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                            data-show-titles="true"
                                            data-titles-max-length="70"
                                            multiple="true"
                                        >
                                            <!-- CURRENT ITEM SELECTIONS -->
                                            <c:forEach var="currentParagraphStyle" items="${command.zone.paragraphStyles}">
                                                <option id="paragraphStyleOption_${currentParagraphStyle.id}"
                                                        class="ml-0" value="${currentParagraphStyle.id}"
                                                        selected="selected">
                                                        ${currentParagraphStyle.name}
                                                </option>
                                            </c:forEach>
                                        </form:select>
                                    </div>
                                    <!-- OVERRIDE: Paragraph Styles -->
                                    <c:if test="${isAlternateZone}">
                                        <form:checkbox path="zone.overrideParagraphStyles"
                                                       id="overrideCheckbox_paragraphStyles"
                                                       cssClass="overrideCheckbox"/>
                                        <div id="overrideBtn_paragraphStyles"
                                             class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
                                             style="margin-left: 0px; border-width: 1px; padding: 0px;"
                                             title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.zone.paragraph.styles.toggle')}</div>">
                                            <i class="fa fa-unlink fa-mp-btn"
                                               style="font-size: 16px;"></i>
                                        </div>
                                    </c:if>
                                </div>
                                </msgpt:TableItem>
                                </c:otherwise>
                                </c:choose>

                                <!-- List Styles -->
                                <c:choose>
                                <c:when test="${not hasListStyle}">
                                <!-- Info: No list styles -->
                                <msgpt:TableItem label="page.label.list.styles" cssClass="listStylesSelectContainer">
                                <div style="margin: 5px 0; padding: 5px 10px; background-color: #eee; border: 1px solid #bbb; color: #333;">
                                    <fmtSpring:message code="page.text.no.list.styles"/>
                                </div>
                                </msgpt:TableItem>
                                </c:when>
                                <c:otherwise>
                                <!-- Select: Default list style -->
                                <msgpt:TableItem label="page.label.default.list.style"
                                                 cssClass="defaultListStyleSelectContainer">
                                <div style="white-space: nowrap;">
                                    <div style="display: inline-block;">
                                        <form:select
                                            id="defaultListStyleSelect"
                                            path="zone.defaultListStyle"
                                            class="complex-dropdown-select"
                                            title="${msgpt:getMessage('page.label.default.list.style')}"
                                            aria-label="${msgpt:getMessage('page.label.default.list.style')}"
                                            data-enablefilter="true"
                                            data-menu-class="dropdown-menu-left w-100"
                                            data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                            data-show-titles="true"
                                            data-titles-max-length="70"
                                        >
                                            <c:if test="${not empty zone.defaultListStyle }">
                                                <option value="${zone.defaultListStyle.id}" selected="selected"><c:out
                                                        value="${zone.defaultListStyle.name}"/></option>
                                            </c:if>
                                        </form:select>
                                    </div>
                                    <!-- OVERRIDE: Default List Style -->
                                    <c:if test="${isAlternateZone}">
                                        <form:checkbox path="zone.overrideDefaultListStyle"
                                                       id="overrideCheckbox_defaultListStyle"
                                                       cssClass="overrideCheckbox"/>
                                        <div id="overrideBtn_defaultListStyle"
                                             class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
                                             style="margin-left: 0px;"
                                             title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.zone.default.list.style.toggle')}</div>">
                                            <i class="fa fa-unlink fa-mp-btn"
                                               style="font-size: 16px;"></i>
                                        </div>
                                    </c:if>
                                </div>
                                </msgpt:TableItem>
                                <!-- LIST STYLES -->
                                <msgpt:TableItem label="page.label.list.styles" cssClass="listStylesSelectContainer"
                                                 style="white-space: nowrap;">
                                <div>
                                    <div style="display: inline-block;">
                                        <form:select
                                            id="listStyleSelect"
                                            path="zone.listStyles"
                                            class="complex-dropdown-select"
                                            title="${msgpt:getMessage('page.label.list.styles')}"
                                            aria-label="${msgpt:getMessage('page.label.list.styles')}"
                                            data-enablefilter="true"
                                            data-enable-selectall="true"
                                            data-enable-view-selected="true"
                                            data-menu-class="dropdown-menu-left w-100"
                                            data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
                                            data-show-titles="true"
                                            data-titles-max-length="70"
                                            multiple="true"
                                        >
                                            <!-- CURRENT ITEM SELECTIONS -->
                                            <c:forEach var="currentListStyle" items="${command.zone.listStyles}">
                                                <option id="listStyleOption_${currentListStyle.id}"
                                                        class="ml-0" value="${currentListStyle.id}"
                                                        selected="selected">
                                                        ${currentListStyle.name}
                                                </option>
                                            </c:forEach>
                                        </form:select>
                                    </div>
                                    <!-- OVERRIDE: List Styles -->
                                    <c:if test="${isAlternateZone}">
                                        <form:checkbox path="zone.overrideListStyles" id="overrideCheckbox_listStyles"
                                                       cssClass="overrideCheckbox"/>
                                        <div id="overrideBtn_listStyles"
                                             class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
                                             style="margin-left: 0px; border-width: 1px; padding: 0px;"
                                             title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.zone.list.styles.toggle')}</div>">
                                            <i class="fa fa-unlink fa-mp-btn"
                                               style="font-size: 16px;"></i>
                                        </div>
                                    </c:if>
                                </div>
                                </msgpt:TableItem>
                                </c:otherwise>
                                </c:choose>
                                </msgpt:DataTable>
                                </c:if> <!-- END STYLES -->

                                <!-- PARTS -->
                                <c:if test="${not document.isSmsTouchpoint}">
                                <!-- If: not SMS Touchpoint (display multipart components) -->
                                <msgpt:DataTable labelPosition="top" id="multipartContainer"
                                                 style="${zone.multipart ? '' : 'display:none;'}">
                                    <msgpt:TableHeader label="page.label.parts"/>
                                <msgpt:TableItem>

                                <msgpt:DataTable id="zonePartsTable" drillDown="false" columnVisibility="false"
                                                 columnReorder="false" style="margin-top: 6px;">
                                <c:choose>
                                <c:when test="${empty zone.partsInOrder}">
                    <tr id="headerLabelTR" class="listTableTR" style="display: none;">
                        <!-- For retrieving the table header -->
                        <td align="left" label="${msgpt:getMessage('page.label.name')}" sortable="true" width="10%"/>
                        <c:if test="${not isAlternateZone}">
                            <td align="left" label="${msgpt:getMessage('page.label.content.type')}" sortable="true"
                                width="10%"/>
                            <td align="left" label="${msgpt:getMessage('page.label.sequence')}" sortable="true"
                                width="10%"/>
                        </c:if>
                    </tr>
                    </c:when>
                    <c:otherwise>
                    <c:forEach var="zonePart" items="${zone.partsInOrder}" varStatus="forStatus">
                    <msgpt:TableListGroup id="zonePart_${zonePart.id}" iFrameId="" iFrameSrc="">
                    <msgpt:TableElement align="left" label="page.label.name" sortable="true" width="10%"
                                        ssortdatatype="dom-text">
                    <div style="white-space: nowrap;">
                        <msgpt:InputFilter type="simpleName">
                            <form:input cssClass="zonePartNameInput inputL overrideInput_partName${forStatus.index}"
                                        path="zone.partsInOrder[${forStatus.index}].name" htmlEscape="true"/>
                        </msgpt:InputFilter>
                        <!-- OVERRIDE: Part Name -->
                        <c:if test="${isAlternateZone}">
                            <form:checkbox path="zone.partsInOrder[${forStatus.index}].overrideName"
                                           id="overrideCheckbox_partName${forStatus.index}"
                                           cssClass="overrideCheckbox"/>
                            <div id="overrideBtn_partName${forStatus.index}"
                                 class="actionBtn_roundAll actionBtn txtFmtTip fa-mp-container overrideBtn"
                                 title="|<div class='detailTipText'>${msgpt:getMessage('page.text.override.part.name.toggle')}</div>">
                                <i class="fa fa-unlink fa-mp-btn"
                                   style="font-size: 16px;"></i>
                            </div>
                        </c:if>
                    </div>
                    </msgpt:TableElement>
                    <msgpt:TableElement align="left" label="page.label.content.type" sortable="true" width="10%"
                                        ssortdatatype="dom-select">
                    <div style="${isAlternateZone ? 'display: none;' : ''}">
                        <form:select id="partContentTypeSelect_${forStatus.index}"
                                     path="zone.partsInOrder[${forStatus.index}].combinedContentTypeId"
                                     items="${partContentTypes}" itemLabel="name" itemValue="id"
                                     cssClass="input100 style_select partContentTypeSelect"
                                     onchange="toggleContentType()"/>
                    </div>
                    <c:if test="${isAlternateZone}">
                        <fmtSpring:message code="${zone.partsInOrder[forStatus.index].contentType.name}"/>
                    </c:if>
                    </msgpt:TableElement>
                    <c:if test="${not isAlternateZone}">
                    <msgpt:TableElement align="left" label="page.label.sequence" sortable="true" width="5%"
                                        ssortdatatype="dom-text" stype="numeric">
                    <div style="width: 70px; text-align: center;">
                        <msgpt:InputFilter type="numeric">
                            <form:input cssClass="input3digit" maxlength="2"
                                        path="zone.partsInOrder[${forStatus.index}].sequence" htmlEscape="true"/>
                        </msgpt:InputFilter>
                    </div>
                    </msgpt:TableElement>
                    </c:if>
                    </msgpt:TableListGroup>
                    </c:forEach>
                    </c:otherwise>
                    </c:choose>
                    </msgpt:DataTable>

                    </msgpt:TableItem>
                    </msgpt:DataTable>
                    </c:if>    <!-- END PARTS -->

                    <!-- ATTRIBUTES -->
                    <c:if test="${(not isAlternateZone || isOmniAlternate) && not isPlaceholder}">
	                    <msgpt:DataTable labelPosition="top" id="compositionAttributes">
	                        <msgpt:TableHeader label="page.label.composition"/>
		                    <c:if test="${document.isEmailTouchpoint || document.isWebTouchpoint}">
			                    <msgpt:TableItem label="page.label.content.container">
			                        <form:select id="renderedContainerTypeSelect" path="zone.renderedContainerTypeId"
			                                     cssClass="style_select inputM" items="${renderedContainerTypes}" itemLabel="name"
			                                     itemValue="id"/>
			                    </msgpt:TableItem>
			             	</c:if>
			             	<c:if test="${document.isEmailTouchpoint || document.isWebTouchpoint || isNativeCompositionTouchpoint}">
			                    <msgpt:TableItem label="page.label.image.dimensions" cssClass="imageDimensionsTypeContainer">
			                        <form:select id="imageDimensionsTypeSelect" path="zone.imageDimensionsTypeId"
			                                     cssClass="style_select inputM" items="${imageDimensionTypes}" itemLabel="name"
			                                     itemValue="id"/>
			                    </msgpt:TableItem>
		                    </c:if>
                            <c:if test="${not isAlternateZone && isDialogueTouchpoint}">
                                <msgpt:TableItem label="page.label.split.tables">
                                    <form:checkbox id="splitTablesToggle" path="zone.splitTables" title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
                                </msgpt:TableItem>
                            </c:if>
		                    <c:if test="${not isAlternateZone}">
			                    <!-- NOT FOR: Subject zones, title zones, SMS zones -->
			                    <c:if test="${not command.zone.isEmailSubjectLine && not document.isSmsTouchpoint && not command.zone.isWebTitleLine}">
				                    <!-- DXF Output -->
				                    <c:if test="${not isAlternateZone && (isDialogueTouchpoint || isNativeCompositionTouchpoint)}">
					                    <c:if test="${document.connectorConfiguration.qualificationOutput.id != 4 && document.connectorConfiguration.qualificationOutput.id != 6 && isDialogueTouchpoint}">
						                    <msgpt:TableItem label="page.label.output" >
                                                <select id="outputFormatSelect" onchange="toggleOutputFormat();" class="style_select">
                                                    <option id="outputFormatOption_tagged" value="0"><fmtSpring:message code="page.label.tagged.text" /></option>
                                                    <option id="outputFormatOption_dxf" ${command.zone.dxfOutput ? 'selected="selected"' : ''} value="1"><fmtSpring:message code="page.label.dxf" /></option>
                                                    <option id="outputFormatOption_html" ${command.zone.htmlOutput ? 'selected="selected"' : ''} value="2"><fmtSpring:message code="page.label.html" /></option>
                                                </select>
						                        <form:checkbox id="dxfToggle" path="zone.dxfOutput" cssStyle="display: none;"/>
                                                <form:checkbox id="htmlToggle" path="zone.htmlOutput" cssStyle="display: none;" />
						                    </msgpt:TableItem>
					                    </c:if>
				                    </c:if>
				                    <msgpt:TableItem label="page.label.Delivery">
				                        <form:checkbox path="zone.zoneAttributes['qualifiedEqualsDelivered']" value="true"
				                                       cssClass="radioBtn"/>
				                        <fmtSpring:message code="page.text.treat.qualified.messages"/>
				                    </msgpt:TableItem>
				                    <c:if test="false">
					                    <c:if test="${command.zone.document.nativeCompositionTouchpoint && not command.zone.multipart}">
						                    <msgpt:TableItem label="page.label.rendering">
						                        <form:checkbox path="zone.absolutePositioning" value="true" cssClass="radioBtn"/>
						                        <fmtSpring:message code="page.text.zone.absolute.position"/>
						                    </msgpt:TableItem>
						                    <msgpt:TableItem>
						                        <form:checkbox path="zone.canFlow" value="true" cssClass="radioBtn"/>
						                        <fmtSpring:message code="page.text.zone.can.flow"/>
						                    </msgpt:TableItem>
						                    <msgpt:TableItem>
						                        <form:checkbox path="zone.canGrow" value="true" cssClass="radioBtn"/>
						                        <fmtSpring:message code="page.text.zone.can.grow"/>
						                    </msgpt:TableItem>
						                    <msgpt:TableItem>
						                        <form:checkbox path="zone.minimumSize" value="true" cssClass="radioBtn"/>
						                        <fmtSpring:message code="page.text.zone.minimum.size"/>
						                    </msgpt:TableItem>
					                    </c:if>
					                    <c:if test="${command.zone.document.nativeCompositionTouchpoint && command.zone.multipart}">
						                    <msgpt:TableItem label="page.label.rendering">
						                        <form:checkbox path="zone.repeats" value="true" cssClass="radioBtn"/>
						                        <fmtSpring:message code="page.text.multipart.zone.repeats"/>
						                    </msgpt:TableItem>
					                    </c:if>
			                    	</c:if>
		                    	</c:if>
		                    	<c:if test="${command.zone.document.nativeCompositionTouchpoint}">
				                    <msgpt:TableItem label="page.label.enforce.minimum.height">
				                        <form:checkbox id="enforceMinimumHeightToggle" path="zone.enforceMinimumHeight" title="${msgpt:getMessage('page.label.enabled')};${msgpt:getMessage('page.label.disabled')}"/>
				                    </msgpt:TableItem>
			                    </c:if>
	                    	</c:if>
	                    </msgpt:DataTable>
                    </c:if>
                    <!-- END ATTRIBUTES -->

            </div>
            <!-- SCROLL CONTAINER -->
            </td>
            </tr></table>

            </div>

            <div class="workflowButtonsContainer pddng-lv2 pddng-horizontal-lv3 align-content-right">
                <div class="inline-block-item mrgn-right-lv2">
                    <msgpt:Button label="page.label.cancel" URL="javascript:closeIframe();" flowControl="true"/>
                </div>
                <div class="inline-block-item">
                    <msgpt:Button URL="javascript:document.getElementById('command').submit();" label="page.label.save"
                                  flowControl="true" primary="true" icon="fa-save"/>
                </div>
            </div>
        </c:if>
        </form:form>

    </msgpt:BodyNew>
</msgpt:Html5>