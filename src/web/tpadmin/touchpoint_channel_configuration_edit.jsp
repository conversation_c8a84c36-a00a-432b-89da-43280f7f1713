<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.model.Document"%>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>
<%@ page import="com.prinova.messagepoint.model.admin.EventType" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.EDIT %>">

	<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />

	<msgpt:Script src="includes/javascript/jQueryPlugins/selectInputTools/jquery.selectboxes.pack.js" />
	
	<!--  start of the piece for Recipient Filename Editor --> 
	<msgpt:Stylesheet href="includes/themes/commoncss/tinyMCEEditorContent.css" />
	<msgpt:Script src="includes/javascript/${tinymceDir}/jquery.tinymce.min.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/tinyMCEinit/jquery.tinyMCEinit.js" />
	
	<msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js" />
	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css" />
	
	<msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js" />
	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css" />

    <msgpt:Script>
        <script>
            $( function() {
                var editorData = {
                    content_css				: "../includes/themes/commoncss/tinyMCEEditorContent.css",
                    async_variables 		: { type : 'document' },
    				async_embedded_content 	: { type : 'document' },
                    markers_list			: ${systemVariablesData},
                    applies_text_styles		: false,
                    applies_paragraph_styles: false,
                    applies_templates		: false,
                    context_lang			: null,
                    document_ids			: [${document.id}]
                };

                tinyMCEvalueEditorInit("fileNameEditor", "658", "30", editorData);
                tinyMCEvalueEditorInit("outputFilenameEditor", "658", "30", editorData);
                tinyMCEvalueEditorInit("outputDocumentTitleEditor", "658", "30", editorData);
            });
        </script>
    </msgpt:Script>
	<msgpt:Script src="includes/javascript/jQueryPlugins/hoverIntent/jquery.hoverIntent.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.editorActions.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.varTagAttrManager.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.smartTextTagAttrManager.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/disableSelection/jquery.disable.text.select.pack.js" />
	
	<msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js" />
	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css" />
	
	<style type="text/css">
		tr.contentTableContentTR table.actionBtnTable {
			margin: 0px 0px;
		}
	</style>

<!--  end of the piece for Recipient Filename Editor -->
    <msgpt:Script>
        <script>
			var connectorSelectShowMsgWarning = false;
            function toggleOverrideRemote() {
                if ( $("#flipToggle_overrideRemote").is(':checked') ){
                    $('#remote_server_IP').show();
                    $('#remote_server_port').show();
                    $('#remote_server_user').show();
                    $('#remote_server_password').show();
                    $('#deServerRow').show();
                }else{
                    $('#remote_server_IP').hide();
                    $('#remote_server_port').hide();
                    $('#remote_server_user').hide();
                    $('#remote_server_password').hide();
                    $('#deServerRow').hide();
                }
            }

            function toggleFilenameSeparator() {
            	if ( $('.flipToggle_bundleImageFilenames').is(':checked') )
            		$('#filenameSeparatorContainer').show();
            	else
					$('#filenameSeparatorContainer').hide();
			}

			function toggleTemplateControl() {
				if ( $('.flipToggle_sefasTemplateControl').is(':checked') ) {
					$("#connector_triple_dialogue_gmc_sefas_package").hide();
					$("#templateControlnWarning").hide();
					$("#connector_sefas_accessibility").show();
				}
				else {
					$("#connector_triple_dialogue_gmc_sefas_package").show();
					$("#templateControlnWarning").show();
					$("#connector_sefas_accessibility").hide();
					if ( $('.flipToggle_sefasAccessibility').is(':checked') ) {
						$(".flipToggle_sefasAccessibility").data('iButton').toggle();
					}
				}
			}
			function toggleRunTimeDxf() {
				if ( $('.flipToggle_runTimeDxf').is(':checked') ) {
					$('#mixedDXFTaggedTextSelect').hide();
					$('#legacyDXFModeSelect').hide();
				}
				else {
					$('#mixedDXFTaggedTextSelect').show();
					$('#legacyDXFModeSelect').show();
				}
			}
            function toggleOverrideSMTP() {
                if ( $("#flipToggle_connectorSMTPOverride").is(':checked') ){
                     $("[id^='connector_override_smtp_settings']").show();
				}else{
                    $("[id^='connector_override_smtp_settings']").hide();
                }
            }

            $( function() {
                connectorSelect($('#connectorSelection'));
				toggleFilenameSeparator();

				// Stacking classes for easier backport of flags
                $(".flipToggle_bundleCombinedContent," +
					".flipToggle_executeInCloud," +
                    ".flipToggle_playMessageOnEmptyVars," +
					".flipToggle_startsOnOddPage," +
					".flipToggle_duplexOutput," +
                    ".flipToggle_validateProductionBundle," +
					".flipToggle_escapeTagsInDriverData," +
					".flipToggle_convertTableBorderPxToPts," +
					".flipToggle_evalNotEqualOnMissingTag, " +
					".flipToggle_playEmptyAggFirstLastVar," +
					".flipToggle_removeZeroFromStyleConnector," +
					".flipToggle_sefasAccessibility," +
					".flipToggle_compTimeParentTagging," +
					".flipToggle_dataGroupExpressionVarProc," +
					".flipToggle_scriptVarAppliesUndefined," +
					".flipToggle_correctParagraphTextStyles," +
					".flipToggle_fixInlineTargetingStyles," +
					".flipToggle_preserveDataWhitespace," +
					".flipToggle_nbspComposedAsSpace," +
					".flipToggle_gmcSpanToTTag," +
					".flipToggle_blueUnderlineLinks," +
					".flipToggle_normalizeEmbeddedContent," +
					".flipToggle_normalizeImageLibrary").each( function() {
                    var currentBinding = $(this);
                    $(this).iButton({
                        labelOn: $(currentBinding).attr('title').split(';')[0],
                        labelOff: $(currentBinding).attr('title').split(';')[1],
                        resizeHandle: false,
                        resizeContainer: "auto"
                    });
                });
				if ( $('.flipToggle_sefasTemplateControl').is(':checked') ) {
					$("#connector_sefas_accessibility").show();
				} else {
					$("#connector_sefas_accessibility").hide();
				}

				$(".flipToggle_bundleImageFilenames").each( function() {
					var currentBinding = $(this);
					$(this).iButton({
						labelOn: $(currentBinding).attr('title').split(';')[0],
						labelOff: $(currentBinding).attr('title').split(';')[1],
						resizeHandle: false,
						resizeContainer: "auto",
						change: function() {
							toggleFilenameSeparator();
						}
					});
				});

				$(".flipToggle_sefasTemplateControl").each( function() {
					var currentBinding = $(this);
					$(this).iButton({
						labelOn: $(currentBinding).attr('title').split(';')[0],
						labelOff: $(currentBinding).attr('title').split(';')[1],
						resizeHandle: false,
						resizeContainer: "auto",
						change: function() {
							toggleTemplateControl();
						}
					});
				});

				$(".flipToggle_runTimeDxf").each( function() {
					var currentBinding = $(this);
					$(this).iButton({
						labelOn: $(currentBinding).attr('title').split(';')[0],
						labelOff: $(currentBinding).attr('title').split(';')[1],
						resizeHandle: false,
						resizeContainer: "auto",
						change: function() {
							toggleRunTimeDxf();
						}
					});
				});

				$(".flipToggle_tablePaddingInPts").each( function() {
					var currentBinding = $(this);
					$(this).iButton({
						labelOn: $(currentBinding).attr('title').split(';')[0],
						labelOff: $(currentBinding).attr('title').split(';')[1],
						resizeHandle: false,
						resizeContainer: "auto",
						change: function() {
						}
					});
				});

                if ($("#flipToggle_connectorSMTPOverride:visible").length != 0 && $("#flipToggle_connectorSMTPOverride:visible").closest('.ibutton-container').length == 0) {
	                $("#flipToggle_connectorSMTPOverride").iButton({
	                        labelOn: $("#flipToggle_connectorSMTPOverride").attr('title').split(';')[0],
	                        labelOff: $("#flipToggle_connectorSMTPOverride").attr('title').split(';')[1],
	                        resizeHandle: false,
	                        resizeContainer: "auto",
	                        change: function() {
	                            toggleOverrideSMTP();
	                        }
	                    });
            		}
                toggleOverrideSMTP();
            
                $("#flipToggle_overrideRemote").each( function() {
                    var currentBinding = $(this);
                    $(this).iButton({
                        labelOn: $(currentBinding).attr('title').split(';')[0],
                        labelOff: $(currentBinding).attr('title').split(';')[1],
                        resizeHandle: false,
                        resizeContainer: "auto",
                        change: function() {
                            toggleOverrideRemote();
                        }
                    });
                });

                toggleOverrideRemote();

				<c:if test="${!connectedEnabled}">
					$('.productionDataResourceSelect').styleActionElement();
				</c:if>
				$('.compositionFileSet,.compositionInputType,#textStyleCompositionTypeSelect,#paragraphStyleCompositionTypeSelect,' +
						'#listStyleCompositionTypeSelect, #listStyleControlTypeSelect, #colorOutputFormatTypeSelect').styleActionElement();

                $("[id^='menuOption_compositionFileSet_']").each( function() {

                    var packageId = $(this).attr('id').replace('menuOption_compositionFileSet_','');
                    var currentPackage = $('#compositionFileSetDef_'+packageId);
                    var configFileName = $(currentPackage).attr('configurationFileName');
                    var templateFileName = $(currentPackage).attr('templateFileName');

                    if ( packageId != 0 ) {
                        $(this).popupFactory({
                            title				: $(currentPackage).attr('packageName'),
                            popupLocation		: "right",
                            trigger				: "hover",
                            fnSetContent		: function(o) {
                                return 	"<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" +
                                    "<div style=\"word-wrap: break-word;\">" + client_messages.text.package_file + ":" + configFileName + "</div>" +
                                    "<div style=\"word-wrap: break-word;\">" + client_messages.text.template_file + ":" + templateFileName + "</div>" +
                                    "</div>";
                            }
                        });
                    }

                });
                var previousCompositionVersions = "${command.compositionVersion}";
                var isCustomVersion = $('#compositionVersionSelect').find('option[value="'+previousCompositionVersions+'"]').length == 0;
                if(isCustomVersion){
                    $('#compositionVersionSelect').find('option[value="0"]').attr('selected', 'selected');
                    $('#compositionVersionWarning').show();
                }else{
                    $('#compositionVersionSelect').find('option[value="'+previousCompositionVersions+'"]').attr('selected', 'selected');
                    $('#compositionVersionWarning').hide();
                }

                $('#deServer').styleActionElement();
            });

            function channelSelect(selectChannel) {
                $("[id^='connector_']").hide();
                var channelId = $(selectChannel).find(':selected').val();
                $('#connectorSelection').removeOption(/./);
                $('#connectorOptions_'+channelId).copyOptions( $('#connectorSelection'), "all" );
                $('#connectorSelection').selectOptions('0',true);
                connectorSelect($('#connectorSelection'));
            }

            function connectorSelect(connectorSelect) {
                $("[id^='connector_']").hide();
                var connectorId = $(connectorSelect).find(':selected').val();
                $("[id*='connector_all']").show();
                if (connectorId == 2)
                    $("[id*='connector_xml']").show();
                else if (connectorId == 3) {
                    $("[id*='connector_dialogue']").show();
                    $("[id*='connector_dual_dialogue_gmc']").show();
					$("[id*='connector_triple_dialogue_gmc_sefas']").show();
					$("[id*='connector_dialogue_sefas']").show();
                } else if (connectorId == 9) {
                    $("[id*='connector_gmc']").show();
                    $("[id*='connector_dual_dialogue_gmc']").show();
					$("[id*='connector_triple_dialogue_gmc_sefas']").show();
                } else if (connectorId == 10 || connectorId == 12) {
                    $("[id*='connector_email']").show();
                    $("[id*='connector_smtp_override']").show();

                    if ($("#flipToggle_connectorSMTPOverride:visible").length != 0 && $("#flipToggle_connectorSMTPOverride:visible").closest('.ibutton-container').length == 0) {
    	                $("#flipToggle_connectorSMTPOverride").iButton({
    	                        labelOn: $("#flipToggle_connectorSMTPOverride").attr('title').split(';')[0],
    	                        labelOff: $("#flipToggle_connectorSMTPOverride").attr('title').split(';')[1],
    	                        resizeHandle: false,
    	                        resizeContainer: "auto",
    	                        change: function() {
    	                            toggleOverrideSMTP();
    	                        }
    	                    });
                		}
            		}
                else if (connectorId == 13) {
                    $("[id*='connector_email']").show();
                    $("[id*='connector_et_email']").show();
                }
                else if (connectorId == 11 || connectorId == 14) {
                    $("[id*='connector_emessaging_sms']").show();
                } else if (connectorId == 15) {
                    $("[id*='connector_ftp']").show();
                } else if (connectorId == 16) {
                    $("[id*='connector_native']").show();
					$("[id*='connector_native_sefas']").show();
                } else if (connectorId == 17) {
                    $("[id*='connector_email']").show();
                } else if (connectorId == 18 || connectorId == 19) {
					$(".flipToggle_sefasTemplateControl").show();
					$(".flipToggle_tablePaddingInPts").show();
					$("[id*='connector_triple_dialogue_gmc_sefas']").show();
					toggleTemplateControl();
					$("#templateControlnWarning").hide();
					$("[id*='connector_native_sefas']").show();
					$("[id*='connector_sefas']").show();
					$("[id*='connector_gmc_sefas']").show();
					$("[id*='connector_native_duplex_output']").show();
					$("[id*='connector_sefas_connected']").show();
					$("[id*='connector_dialogue_sefas']").show();
				}

				if ( $('#connectedEnabled').length != 0 )
					$("[id*='connector_sefas_connected']").show();

				if (connectorId != 18 && connectorId != 19) {
					$("[id*='connector_qualification_output']").show();
					$("[id*='connector_recipient_output_files_encoding']").show();
					$("[id*='connector_bundle_combined_content']").show();
				}

                // Reset the composition version to Default
                $('#compositionVersionSelect').find('option[value="0"]').attr('selected', 'selected');

                $('#qualificationOutputSelection').removeOption(/./);
                $('#qualificationOutputOptions').copyOptions( $('#qualificationOutputSelection'), "all" );
                if (connectorId != 3)
                    $('#qualificationOutputSelection').removeOption(["4"]);
                if (connectorId == 9)
					$('#qualificationOutputSelection').removeOption(["5","6"]);
                $('#qualificationOutputSelection').selectOptions('${command.qualificationOutputId}',true);

				if(connectorSelectShowMsgWarning) {
					$('#connectorSelectionWarning').show();
				}
				connectorSelectShowMsgWarning = true;
				qualificationOutputSelect($('#qualificationOutputSelection'));
            }

			function qualificationOutputSelect(qualificationOutputSelect) {
				$('#runTimeDxfSelect').hide();
				$('#mixedDXFTaggedTextSelect').hide();
				$('#legacyDXFModeSelect').hide();
				var outputType = $(qualificationOutputSelect).find(':selected').val();
				if ( (outputType == 4) || (outputType == 1) )
				{
					$('#runTimeDxfSelect').show();
					$('#mixedDXFTaggedTextSelect').show();
					$('#legacyDXFModeSelect').show();
					toggleRunTimeDxf();
				}
			}

			function compVersionSelect(compVersionSelect){
                var version = $(compVersionSelect).find(':selected').val();
                $('#compositionVersionWarning').hide();
            }

        </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:BodyNew theme="minimal" >
	<msgpt:BannerNew  edit="true" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>" />

	<msgpt:NewNavigationTabs edit="true" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>" />
	<msgpt:LowerContainer>
		<msgpt:ContentPanel extendedWidth="true">

			<form:form modelAttribute="command" enctype="multipart/form-data">

				<form:errors path="*">
					<msgpt:Information errorMsgs="${messages}" type="error" />
				</form:errors>

				<c:if test="${connectedEnabled}">
					<input type="hidden" id="connectedEnabled" value="true" />
				</c:if>

				<div class="box-shadow-4 rounded bg-white">
					<div class="p-4">
						<div class="pt-2 px-2">

							<div class="row">
								<div class="col-lg-9 mx-lg-auto col-xl">
									<div class="mt-4 pt-1">

										<div class="border-bottom pb-4 mb-4 bg-white">
											<div style="float: left;" class="d-flex flex-column mt-2">
												<!-- Touchpoint Channel Configuration -->
												<hgroup id="touchpointPropertiesLabel">
													<h1 class="h4 anchor-title mb-1">
                                                        <span class="anchor-title-inner" style="text-transform: capitalize;">
                                                            <fmtSpring:message code="page.label.touchpoint.channel.configuration"/> - <fmtSpring:message code="${command.document.connectorConfiguration.channel.presentationName}"/>
                                                                <a href="#basic-info">
                                                                    <i class="far fa-hashtag" aria-hidden="true"></i>
                                                                    <span class="sr-only">
                                                                        <fmtSpring:message code="page.label.anchor"/>
                                                                    </span>
                                                                </a>
                                                        </span>
													</h1>
												</hgroup>
											</div>
											<!-- BUTTONS -->
											<div class="d-flex flex-column" style="text-align: right;">

												<c:set var="cancelURL" value="${contextPath}/tpadmin/document_view.form?docid=${param.documentid}"/>

												<div class="py-1 p-xl-0 mt-xl-0 ml-xl-auto order-xl-1">
													<div class="btn-group ml-3">
														<button id="channelConfigCancelBtn" onclick="javascriptHref('${cancelURL}')" type="button"
																class="btn btn-outline-light text-body post-trigger">
															<i class="far fa-times-circle mr-2" aria-hidden="true"></i>
															<fmtSpring:message code="page.label.cancel"/>
														</button>
													</div>
													<div class="btn-group border-separate ml-3">
														<button id="channelConfigSaveBtn" type="button"
																onclick="javascript:document.getElementById('command').submit();"
																class="btn btn-primary post-trigger">
															<i class="far fa-save mr-2" aria-hidden="true"></i>
															<fmtSpring:message code="page.label.save"/>
														</button>
													</div>
												</div>
											</div>
											<!-- END BUTTONS -->

										</div>



										<!-- Channel Configuration -->
										<section id="basic-info" class="border-bottom pb-4 mb-4">

											<!-- HIDDEN - LEGACY TOGGLE -->
											<div class="form-row" style="display: none;">
												<!-- Channel -->
												<div class="form-group col-md-6">
													<label for="channelSelection">
														<fmtSpring:message code="page.label.channel"/>
													</label>
													<div class="d-flex flex-column">
														<fmtSpring:message code="${command.document.connectorConfiguration.channel.presentationName}"/>
														<form:select id="channelSelection" path="channelId" onchange="javascript:channelSelect(this)" cssClass="inputL" cssStyle="display: none;">
															<c:forEach var="channel" items="${channels}">
																<option value="${channel.id}" ${ command.channelId == channel.id ? 'selected="selected"' : '' }>
																	<fmtSpring:message code="${channel.presentationName}"/>
																</option>
															</c:forEach>
														</form:select>
													</div>
												</div>
											</div>

											<div class="form-row">
												<!-- Connector -->
												<div class="form-group col-md-6">
													<label for="connectorSelection">
														<fmtSpring:message code="page.label.connector"/>
													</label>
													<div class="d-flex flex-column">

														<form:select id="connectorSelection" path="connectorId" onchange="connectorSelect(this)" cssClass="inputL" cssStyle="width: 240px;">
															<!-- <form:option value="0" label="${msgpt:getMessage('page.label.select.connector')}" /> -->
															<c:forEach var="connector" items="${connectors}">
																<c:if test="${command.channelId == connector.channel.id}">
																	<option value="${connector.id}" ${command.connectorId==connector.id?'selected="selected"':''}>
																		<fmtSpring:message code="${connector.presentationName}"/>
																	</option>
																</c:if>
															</c:forEach>
														</form:select>
														<span id="connectorSelectionWarning" style="padding-left: 10px; font-size: small; display: none; color: #7d5e15;"><fmtSpring:message code="warning.connector.selection"/>&nbsp;</span>

														<select id="connectorOptions_0" disabled="disabled" style="display:none;">
															<option value="0"><c:out value='${msgpt:getMessage("page.text.channel.selection.required")}' /></option>
														</select>
														<c:forEach var="currentChannel" items="${channels}">
															<select id="connectorOptions_${currentChannel.id}" disabled="disabled" style="display:none;">
																<c:if test="${currentChannel.id != 2}">
																	<option value="0"><fmtSpring:message code="page.label.select.connector"/></option>
																</c:if>
																<c:forEach var="currentConnector" items="${connectors}">
																	<c:if test="${currentChannel.id == currentConnector.channel.id}">
																		<option value="${currentConnector.id}" >
																			<fmtSpring:message code="${currentConnector.presentationName}"/>
																		</option>
																	</c:if>
																</c:forEach>
															</select>
														</c:forEach>

													</div>
												</div>
												<!-- Sefas/HCS Template Control -->
												<div class="form-group col-md-6" id="connector_sefas_template_control" style="display: none;">
													<label for="channelSelection">
														<fmtSpring:message code="page.label.template.controlled"/>
													</label>
													<div class="d-flex flex-column">
														<table>
															<tr>
																<td><form:checkbox class="flipToggle_sefasTemplateControl" path="templateControl" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
																<td><div id="templateControlnWarning" style="font-size: small; color: #7d5e15; margin-left: 10px;"><fmtSpring:message code="warning.connector.selection"/>&nbsp;</div></td>
															</tr>
														</table>
													</div>
												</div>

											</div>

											<div class="form-row">
												<!-- Composition Package Select -->
												<div class="form-group col-md-6" id="connector_triple_dialogue_gmc_sefas_package" style="display: none;">
													<label for="compositionPackage">
														<fmtSpring:message code="page.label.composition.package"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="compositionPackage" cssClass="inputXXL compositionFileSet style_select" path="compositionFileSet">
															<form:option value="0">-- <fmtSpring:message code="page.text.select.composition.package"/> --</form:option>
															<form:options items="${compositionFileSets}" itemLabel="name" itemValue="id" />
														</form:select>
														<div style="display: none;">
															<c:forEach var="currentPackage" items="${compositionFileSets}">
																<div id="compositionFileSetDef_${currentPackage.id}"
																	 packageName="${currentPackage.name}"
																	 templateFileName="${currentPackage.templateFileName}"
																	 configurationFileName="${currentPackage.compositionConfigurationFileName}"></div>
															</c:forEach>
														</div>
													</div>
												</div>

												<!-- Composition Version -->
												<div class="form-group col-md-6" id="connector_triple_dialogue_gmc_sefas_version" style="display: none;">
													<label for="compositionVersionSelect">
														<fmtSpring:message code="page.label.composition.version"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="compositionVersionSelect" cssClass="w-100" onchange="compVersionSelect(this);" path="compositionVersion">
															<form:option value="0"><fmtSpring:message code="page.label.default"/></form:option>
															<c:forEach var="exstreamVersion" items="${exstreamVersions}">
																<form:option value="${exstreamVersion}" id="connector_dialogue"><c:out value="${exstreamVersion}" /></form:option>
															</c:forEach>
															<c:forEach var="gmcVersion" items="${gmcVersions}">
																<form:option value="${gmcVersion}" id="connector_gmc"><c:out value="${gmcVersion}" /></form:option>
															</c:forEach>
															<c:forEach var="sefasVersion" items="${sefasVersions}">
																<form:option value="${sefasVersion}" id="connector_sefas"><c:out value="${sefasVersion}" /></form:option>
															</c:forEach>
														</form:select>
														<span id="compositionVersionWarning" style="color: #944;"><fmtSpring:message code="error.previous.selected.version"/>&nbsp;<c:out value="${command.compositionVersion}" />&nbsp;<fmtSpring:message code="error.document.composition.version.is.not.available"/></span>
													</div>
												</div>
											</div>

											<!-- ALL -->
											<div class="form-row">
												<!-- Connector Name -->
												<div class="form-group col-md-6">
													<label for="name">
														<fmtSpring:message code="page.label.connector.name"/>
													</label>
													<div class="d-flex flex-column">
														<input class="d-none" name="fakeAutoFillConnectorName" type="password"> <!-- Disable autofill -->
														<msgpt:InputFilter type="simpleName">
															<form:input id="name" cssClass="inputXL" path="document.connectorName" maxlength="96" autocomplete="false"/>
														</msgpt:InputFilter>
													</div>
												</div>
												<!-- Bundle Delivery -->
												<div class="form-group col-md-6">
													<label for="deServerSelect">
														<fmtSpring:message code="page.label.bundle.delivery"/>
													</label>
													<div class="d-flex flex-column">
														<msgpt:BundleDeliverySelect id="deServerSelect" deServerGuidPath="command.DEServerGuid" filenameOverridePath="command.bundleNameOverride" eventTypeFilter="<%= EventType.TYPE_TEST %>" />
													</div>
												</div>
											</div>

											<!-- Sendmail override address -->
											<div class="form-row">
												<div class="form-group col-md-6" id="connector_smtp_override" style="display: none;">
													<label for="flipToggle_connectorSMTPOverride">
														<fmtSpring:message code="page.label.customer.smtp.override"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="flipToggle_connectorSMTPOverride" path="overrideSMTP" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
													</div>
												</div>
											</div>

											<!-- SMTP Settings -->
											<div class="form-row">
												<div class="form-group col-md-6" id="connector_override_smtp_settings" style="display: none;">
													<label for="id">
														<fmtSpring:message code="page.label.customer.smtp.settings"/>
													</label>
													<div class="d-flex flex-column">
														<msgpt:DataTable labelWidths="20%">
															<msgpt:TableItem label="page.label.host" id="connector_override_smtp_settings" style="display: none;">
																<msgpt:InputFilter type="filename">
																	<form:input cssClass="inputXL" path="smtpHost"/>
																</msgpt:InputFilter>
															</msgpt:TableItem>
															<msgpt:TableItem label="page.label.port" id="connector_override_smtp_settings" style="display: none;">
																<msgpt:InputFilter type="numeric">
																	<form:input cssClass="input5digit" maxlength="5" path="smtpPort"/>
																</msgpt:InputFilter>
															</msgpt:TableItem>
															<msgpt:TableItem label="page.label.security" id="connector_override_smtp_settings" style="display: none;">
																<form:select path="smtpSecurity" cssClass="inputL">
																	<form:option value="none"><fmtSpring:message code="page.label.none"/></form:option>
																	<form:option value="starttls">starttls</form:option>
																</form:select>
															</msgpt:TableItem>
															<msgpt:TableItem label="page.label.account" id="connector_override_smtp_settings" style="display: none;">
																<form:input cssClass="inputXL" path="smtpAccount"/>
															</msgpt:TableItem>
															<msgpt:TableItem label="page.label.password" id="connector_override_smtp_settings" style="display: none;">
																<msgpt:InputFilter type="password">
																	<form:password cssClass="inputXL" path="smtpPassword" showPassword="true"/>
																</msgpt:InputFilter>
															</msgpt:TableItem>
															<msgpt:TableItem label="page.label.custom.header" id="connector_override_smtp_settings" style="display: none;">
																<form:textarea maxlength="512" cssClass="inputXXL" path="smtpCustomHeader" />
															</msgpt:TableItem>
														</msgpt:DataTable>
													</div>
												</div>
											</div>

											<!-- Qualification Output -->
											<div class="form-row">
												<div class="form-group col-md-6" id="connector_qualification_output">
													<label for="qualificationOutputSelection">
														<fmtSpring:message code="page.label.qualification.output"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="qualificationOutputSelection" path="qualificationOutputId" onchange="javascript:qualificationOutputSelect(this)" cssClass="inputL">
															<c:forEach var="output" items="${qualificationOutputs}">
																<c:if test="${command.qualificationOutputId == output.id}">
																	<option value="${output.id}" ${command.qualificationOutputId == output.id ? 'selected="selected"' : ''}>
																		<fmtSpring:message code="${output.name}"/>
																	</option>
																</c:if>
															</c:forEach>
														</form:select>

														<select id="qualificationOutputOptions" style="display:none;">
															<c:forEach var="output" items="${qualificationOutputs}">
																<option value="${output.id}">
																	<fmtSpring:message code="${output.name}"/>
																</option>
															</c:forEach>
														</select>
													</div>
												</div>
											</div>

											<!-- XML -->
											<div class="form-row" id="connector_xml" style="display: none;">
												<!-- XML File Name -->
												<div class="form-group col-md-6">
													<label for="xmlFileName">
														<fmtSpring:message code="page.label.xml.file.name"/>
													</label>
													<div class="d-flex flex-column">
														<form:input id="xmlFileName" path="xmlFileName" cssClass="inputXL"/>
													</div>
												</div>
												<!-- XML Format Type -->
												<div class="form-group col-md-6">
													<label>
														<fmtSpring:message code="page.label.XML.format.type"/>
													</label>
													<div class="d-flex flex-column">
														<form:radiobutton path="formatType" value="1" cssClass="radioBtn" /><fmtSpring:message code="page.label.optimized"/><br>
														<form:radiobutton path="formatType" value="2" cssClass="radioBtn" /><fmtSpring:message code="page.label.verbose"/>
													</div>
												</div>
											</div>

											<!-- PreProcess XSLT File -->
											<div class="form-row">
												<div class="form-group col-md-6" id="connector_xml" style="display: none;">
													<label for="id">
														<fmtSpring:message code="page.label.PreProcess.XSLT.File"/>
													</label>
													<div class="d-flex flex-column">
														<c:choose>
															<c:when test="${not empty command.document.connectorConfigWrapper.preProcessXSLTFileName}">
																<c:out value="${command.document.connectorConfigWrapper.preProcessXSLTFileName}"/>
															</c:when>
															<c:otherwise><fmtSpring:message code="page.label.NA"/></c:otherwise>
														</c:choose>
													</div>
												</div>
											</div>

											<!-- XML -->
											<div class="form-row" id="connector_xml" style="display: none;">
												<!-- Disable PreProcess XSLT -->
												<c:if test="${not empty command.document.connectorConfigWrapper.preProcessXSLTFileName}">
													<div class="form-group col-md-6">
														<label for="disablePreXSLTFlag">
															Disable PreProcess XSLT File?
														</label>
														<div class="d-flex flex-column">
															<form:checkbox id="disablePreXSLTFlag" path="command.document.connectorConfigWrapper.disablePreXSLTFlag" cssClass="radioBtn" />
														</div>
													</div>
												</c:if>
												<!-- Disable PostProcess XSLT -->
												<c:if test="${not empty command.document.connectorConfigWrapper.postProcessXSLTFileName}">
													<div class="form-group col-md-6">
														<label for="disablePostXSLT">
															Disable PostProcess XSLT File?
														</label>
														<div class="d-flex flex-column">
															<form:checkbox id="disablePostXSLT" path="command.document.connectorConfigWrapper.disablePostXSLTFlag" cssClass="radioBtn" />
														</div>
													</div>
												</c:if>
											</div>

											<!-- XML -->
											<div class="form-row" id="connector_xml" style="display: none;">
												<!-- Upload PreProcess XSLT -->
												<div class="form-group col-md-6">
													<label for="preXSLTFile">
														Upload PreProcess XSLT File?
													</label>
													<div class="d-flex flex-column">
														<fmtSpring:bind path="command.document.connectorConfigWrapper.preXSLTFile">
															<input id="preXSLTFile" type="file" class="inputFile" name="${status.expression}"/>
														</fmtSpring:bind>
													</div>
												</div>
												<!-- PostProcess XSLT -->
												<div class="form-group col-md-6">
													<label>
														<fmtSpring:message code="page.label.PostProcess.XSLT.File"/>
													</label>
													<div class="d-flex flex-column">
														<c:choose>
															<c:when test="${not empty command.document.connectorConfigWrapper.postProcessXSLTFileName}">
																<c:out value="${command.document.connectorConfigWrapper.postProcessXSLTFileName}"/>
															</c:when>
															<c:otherwise><fmtSpring:message code="page.label.NA"/></c:otherwise>
														</c:choose>
													</div>
												</div>
											</div>

											<!-- PostProcess XSLT File -->
											<div class="form-row">
												<div class="form-group col-md-6" id="connector_xml" style="display: none;">
													<label for="postXSLTFile">
														Upload PostProcess XSLT File?
													</label>
													<div class="d-flex flex-column">
														<fmtSpring:bind path="command.document.connectorConfigWrapper.postXSLTFile">
															<input id="postXSLTFile" type="file" class="inputFile" name="${status.expression}"/>
														</fmtSpring:bind>
													</div>
												</div>
											</div>

											<div class="form-row" id="connector_dialogue_sefas">
												<!-- List Style Composition -->
												<div class="form-group col-md-6">
													<label for="listStyleComposition">
														<fmtSpring:message code="page.label.list.styles"/>
													</label>
													<div class="d-flex flex-column">
														<form:select cssClass="inputM  style_select" id="listStyleControlTypeSelect" path="listStyleControlType">
															<form:options id="listStyleControl" items="${styleControlTypes}" itemValue="id" itemLabel="name" />
														</form:select>
													</div>
												</div>
											</div>

											<!-- Supports Styles -->
											<div class="form-row" id="connector_dialogue">
												<div class="form-group col-md-6" style="display: none;">
													<label for="supportStyles">
														<fmtSpring:message code="page.label.supports.styles"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="supportStyles" cssClass="radioBtn" path="supportsStyles" />
													</div>
												</div>
                                                <!-- Run Time DXF -->
                                                <div class="form-group col-md-6" id="runTimeDxfSelect">
                                                    <label for="runTimeDxfSelection">
                                                        <fmtSpring:message code="page.label.run.time.dxf"/>
                                                    </label>
													<label style="position: absolute; top: 37px; left: 102px;">
															<fmtSpring:message code="page.text.beta.version"/>
															<span class="d-inline-block ml-2" data-toggle="tooltip"
																  title="${msgpt:getMessage('page.text.mark.beta.version')}">
																<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
															</span>
													</label>
                                                    <div class="d-flex flex-column" id="runTimeDxfSelection">
                                                        <td><form:checkbox class="flipToggle_runTimeDxf" path="runTimeDxf" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
                                                    </div>
                                                </div>
											</div>

											<!-- DIALOGUE -->
											<div class="form-row" id="connector_dialogue" style="display: none;">
												<!-- Legacy DXF Mode -->
												<div class="form-group col-md-6" id="legacyDXFModeSelect">
													<label for="legacyDXFMode">
														<fmtSpring:message code="page.label.legacy.dxf.mode"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="legacyDXFMode" cssClass="radioBtn" path="legacyDxfMode" />
													</div>
												</div>
												<!-- Mixed DXF/Tagged Text Mode -->
												<div class="form-group col-md-6" id="mixedDXFTaggedTextSelect">
													<label for="mixedDXFTaggedText">
														<fmtSpring:message code="page.label.mixed.dxf.tagged.text.mode"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="mixedDXFTaggedText" cssClass="radioBtn" path="mixedDxfTaggedText" />
													</div>
												</div>
											</div>

											<!-- GMC -->
											<div class="form-row" id="connector_gmc" style="display: none;">
												<!-- Style Composition Settings -->
												<div class="form-group col-md-4">
													<label for="textStyleCompositionTypeSelect">
														<fmtSpring:message code="page.label.text.style.composition"/>
													</label>
													<div class="d-flex flex-column">
														<form:select cssClass="inputM  style_select" id="textStyleCompositionTypeSelect" path="textStyleCompositionType">
															<form:options items="${styleCompositionTypes}" itemValue="id" itemLabel="name" />
														</form:select>
													</div>
												</div>
												<!-- Paragraph Style Composition -->
												<div class="form-group col-md-4">
													<label for="paragraphStyleCompositionTypeSelect">
														<fmtSpring:message code="page.label.paragraph.style.composition"/>
													</label>
													<div class="d-flex flex-column">
														<form:select cssClass="inputM  style_select" id="paragraphStyleCompositionTypeSelect" path="paragraphStyleCompositionType">
															<form:options items="${styleCompositionTypes}" itemValue="id" itemLabel="name" />
														</form:select>
													</div>
												</div>
												<!-- List Style Composition -->
												<div class="form-group col-md-4">
													<label for="listStyleComposition">
														<fmtSpring:message code="page.label.list.style.composition"/>
													</label>
													<div class="d-flex flex-column">
														<form:select cssClass="inputM  style_select" id="listStyleCompositionTypeSelect" path="listStyleCompositionType">
															<form:options id="listStyleComposition" items="${styleCompositionTypes}" itemValue="id" itemLabel="name" />
														</form:select>
													</div>
												</div>
											</div>

											<div class="form-row" id="connector_gmc" style="display: none;">
												<!-- Workflow File Path -->
												<div class="form-group col-md-6">
													<label for="workflowFileName">
														<fmtSpring:message code="page.label.touchpoint.container.workflow.file.path"/>
													</label>
													<div class="d-flex flex-column">
														<msgpt:InputFilter type="filename">
															<form:input id="workflowFileName" cssClass="inputXXL" maxlength="255" path="workflowFileName" />
														</msgpt:InputFilter>
													</div>
												</div>
												<!-- Escape Tags in Driver Data -->
												<div class="form-group col-md-6">
													<label for="escapeTagsInDriverData">
														<fmtSpring:message code="page.label.escape.tags.in.driver.data"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="escapeTagsInDriverData" class="flipToggle_escapeTagsInDriverData" path="escapeTagsInDriverData" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
													</div>
												</div>
											</div>

											<!-- GMC -->
											<div class="form-row" id="connector_gmc" style="display: none;">
												<!-- Style Control -->
												<div class="form-group col-md-6">
													<label for="controlStyles">
														<fmtSpring:message code="page.label.control.style.attributes"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="controlStyles" cssClass="radioBtn" path="controlStyles" />
													</div>
												</div>
												<!-- Restricted Quotes -->
												<div class="form-group col-md-6">
													<label for="restrictedQuotes">
														<fmtSpring:message code="page.label.restricted.quotes"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="restrictedQuotes" cssClass="radioBtn" path="restrictedQuotes" />
													</div>
												</div>
											</div>

											<!-- Encoding -->
											<div class="form-row">
												<div class="form-group col-md-6">
													<label for="inputCharacterEncodingSelect">
														<fmtSpring:message code="page.label.customer.input.files.encoding"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="inputCharacterEncodingSelect" cssClass="inputS" path="inputCharacterEncoding">
															<form:option value="0"><fmtSpring:message code="page.label.auto.detect"/></form:option>
															<form:option value="1"><fmtSpring:message code="page.label.windows1252"/></form:option>
															<form:option value="2"><fmtSpring:message code="page.label.windows1250"/></form:option>
															<form:option value="3"><fmtSpring:message code="page.label.utf8"/></form:option>
														</form:select>
													</div>
												</div>
											</div>

											<div class="form-row" id="connector_sefas_accessibility" style="display: none;">
												<!-- Style Control -->
												<div class="form-group col-md-6" id="connector_sefas_accessibility_control" style="display: none;">
													<label for="channelSelection">
														<fmtSpring:message code="page.label.configuration.accessibility"/>
													</label>
													<div class="d-flex flex-column">
														<table>
															<tr>
																<td><form:checkbox class="flipToggle_sefasAccessibility" path="accessibility" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
															</tr>
														</table>
													</div>
												</div>
												<div class="form-group col-md-6" id="connector_sefas">
													<label for="linespacePositionSelect">
														<fmtSpring:message code="page.label.linespace.position"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="linespacePositionSelect" cssStyle="width: 175px;" path="linespacePosition">
															<form:option value="1"><fmtSpring:message code="page.label.linespace.position.top"/></form:option>
															<form:option value="2"><fmtSpring:message code="page.label.linespace.position.bottom"/></form:option>
															<form:option value="3"><fmtSpring:message code="page.label.linespace.position.center"/></form:option>
														</form:select>
													</div>
												</div>
											</div>

											<!-- Customer Output File Encoding -->
											<div class="form-row">
												<div class="form-group col-md-6" id="connector_recipient_output_files_encoding">
													<label for="outputCharacterEncodingSelect">
														<fmtSpring:message code="page.label.customer.output.files.encoding"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="outputCharacterEncodingSelect" class="inputS" path="outputCharacterEncoding">
															<form:option value="0"><fmtSpring:message code="page.label.auto.detect"/></form:option>
															<form:option value="1"><fmtSpring:message code="page.label.windows1252"/></form:option>
															<form:option value="2"><fmtSpring:message code="page.label.windows1250"/></form:option>
															<form:option value="3"><fmtSpring:message code="page.label.utf8"/></form:option>
														</form:select>
													</div>
												</div>
												<c:if test="${showInternalLegacyToggles}">
												<div class="form-group col-md-6" id="connector_sefas">
													<label for="tablePaddingInPtsCheckbox">
														<fmtSpring:message code="page.label.table.padding.in.pts"/>
													</label>
													<div class="d-flex flex-column">
														<table>
															<tr>
																<td><form:checkbox id="tablePaddingInPtsCheckbox" class="flipToggle_tablePaddingInPts" path="tablePaddingInPts" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
															</tr>
														</table>
													</div>
												</div>
												</c:if>
											</div>

											<!-- ALL -->
											<!-- Publication File Path -->
											<div class="form-row">
												<div class="form-group col-md-6" id="connector_dialogue" style="display: none;">
													<label for="pubFileName">
														<fmtSpring:message code="page.label.touchpoint.container.publication.file.path"/>
													</label>
													<div class="d-flex flex-column">
														<msgpt:InputFilter type="filename">
															<form:input id="pubFileName" cssClass="inputXXL" maxlength="255" path="pubFileName" />
														</msgpt:InputFilter>
													</div>
												</div>
											</div>

											<!-- Customer Phone Number Variable -->
											<div class="form-row">
												<div class="form-group col-md-6" id="connector_emessaging_sms" style="display: none;">
													<label for="customerPhoneNumber">
														<fmtSpring:message code="page.label.customer.phone.number.variable"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="customerPhoneNumber" path="customerPhoneNumberVariable" cssClass="inputL">
															<form:option value="-1">-- <fmtSpring:message code="page.text.select.variable"/> --</form:option>
															<form:options items="${stringVariables}" itemLabel="displayName" itemValue="id"/>
														</form:select>
													</div>
												</div>
											</div>

											<!-- Customer Email Address Variable -->
											<div class="form-row">
												<div class="form-group col-md-6" id="connector_email" style="display: none;">
													<label for="customerEmailAddress">
														<fmtSpring:message code="page.label.customer.email.address.variable"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="customerEmailAddress" path="customerEmailAddressVariable" cssClass="inputL">
															<form:option value="-1">-- <fmtSpring:message code="page.text.select.variable"/> --</form:option>
															<form:options items="${stringVariables}" itemLabel="displayName" itemValue="id"/>
														</form:select>
													</div>
												</div>
											</div>

											<!-- Customer ET Key (GUID) Variable -->
											<div class="form-row">
												<div class="form-group col-md-6" id="connector_et_email" style="display: none;">
													<label for="customerKey">
														<fmtSpring:message code="page.label.customer.key.variable"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="customerKey" path="customerKeyVariable" cssClass="inputL">
															<form:option value="-1">-- <fmtSpring:message code="page.text.select.variable"/> --</form:option>
															<form:options items="${stringVariables}" itemLabel="displayName" itemValue="id"/>
														</form:select>
													</div>
												</div>
											</div>

											<!-- FTP -->
											<div class="form-row" id="connector_ftp" style="display: none;">
												<!-- FTP Server -->
												<div class="form-group col-md-6">
													<label for="ftpServer">
														<fmtSpring:message code="page.label.ftp.server"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="ftpServer" path="serverId" cssClass="inputL">
															<form:option value="0">-- <fmtSpring:message code="page.text.select.server"/> --</form:option>
															<form:option value="1">1.(${ftp1host})</form:option>
															<form:option value="2">2.(${ftp2host})</form:option>
															<form:option value="3">3.(${ftp3host})</form:option>
														</form:select>
													</div>
												</div>
												<!-- FTP Upload Location/Path -->
												<div class="form-group col-md-6">
													<label for="recipientFileLocation">
														<fmtSpring:message code="page.label.ftp.location"/>
													</label>
													<div class="d-flex flex-column">
														<msgpt:InputFilter type="filename">
															<form:input id="recipientFileLocation" cssClass="inputXXL" maxlength="255" path="recipientFileLocation" />
														</msgpt:InputFilter>
													</div>
												</div>
											</div>

											<!-- FTP -->
											<div class="form-row" id="connector_ftp" style="display: none;">
												<!-- Web URL -->
												<div class="form-group col-md-6">
													<label for="webURL">
														<fmtSpring:message code="page.label.web.url"/>
													</label>
													<div class="d-flex flex-column">
														<msgpt:InputFilter type="filename">
															<form:input id="webURL" cssClass="inputXXL" maxlength="255" path="webURL" />
														</msgpt:InputFilter>
													</div>
												</div>
												<!-- Recipient Filename -->
												<div class="form-group col-md-6">
													<label for="editor_fileNameEditor">
														<fmtSpring:message code="page.label.customer.filename"/>
													</label>
													<div class="d-flex flex-column">
														<form:textarea id="editor_fileNameEditor" cssClass="mceEditor_fileNameEditor" path="recipientFileName" />
													</div>
												</div>
											</div>

											<!-- FTP -->
											<div class="form-row" id="connector_ftp" style="display: none;">
												<!-- As Landing Page -->
												<div class="form-group col-md-6">
													<label for="asLandingPage">
														<fmtSpring:message code="page.label.as.landing.page"/>
													</label>
													<div class="d-flex flex-column">
														<form:radiobutton id="asLandingPage" path="isEmbedded" value="false" cssClass="radioBtn"/>
													</div>
												</div>
												<!-- As Embedded -->
												<div class="form-group col-md-6">
													<label for="asEmbedded">
														<fmtSpring:message code="page.label.as.embedded"/>
													</label>
													<div class="d-flex flex-column">
														<form:radiobutton id="asEmbedded" path="isEmbedded" value="true" cssClass="radioBtn"/>
													</div>
												</div>
											</div>

											<!-- MP COMP -->
											<div class="form-row" id="connector_native" style="display: none;">
												<!-- Fillable Forms -->
												<div class="form-group col-md-6">
													<label for="applyFillableForms">
														<fmtSpring:message code="page.label.fillable.forms"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="applyFillableForms" path="applyFillableForms" value="true" cssClass="checkbox"/>
													</div>
												</div>
												<!-- Output File Type -->
												<div class="form-group col-md-6">
													<label for="outputFileTypeId">
														<fmtSpring:message code="page.label.output.file.type"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="outputFileTypeId" path="outputFileTypeId" items="${outputFileTypes}" itemLabel="name" itemValue="id" />
													</div>
												</div>
											</div>

											<!-- MP COMP -->
											<div class="form-row" id="connector_native_mp" style="display: none;">
												<!-- Start Document On Odd Page -->
												<div class="form-group col-md-6">
													<label for="startsOnOddPage">
														<fmtSpring:message code="page.label.start.on.odd.page"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="startsOnOddPage" class="flipToggle_startsOnOddPage" path="startsOnOddPage" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
													</div>
												</div>
											</div>
											<!-- Duplex Output -->
											<div class="form-row" id="connector_native_duplex_output">
												<div class="form-group col-md">
													<label for="duplexOutput">
														<fmtSpring:message code="page.label.duplex.output"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="duplexOutput" class="flipToggle_duplexOutput" path="duplexOutput" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
													</div>
												</div>
											</div>

											<!-- Output Filename -->
											<div class="form-row">
												<div class="form-group col-md" id="connector_native_sefas" style="display: none;">
													<label for="editor_outputFilenameEditor">
														<fmtSpring:message code="page.label.output.filename"/>
													</label>
													<div class="d-flex flex-column">
														<form:textarea id="editor_outputFilenameEditor" cssClass="mceEditor_outputFilenameEditor" path="outputFilename" />
													</div>
												</div>
											</div>

											<!-- Output Document Title -->
											<div class="form-row">
												<div class="form-group col-md" id="connector_native" style="display: none;">
													<label for="editor_outputDocumentTitleEditor">
														<fmtSpring:message code="page.label.output.document.title"/>
													</label>
													<div class="d-flex flex-column">
														<form:textarea id="editor_outputDocumentTitleEditor" cssClass="mceEditor_outputDocumentTitleEditor" path="outputDocumentTitle" />
													</div>
												</div>
											</div>

											<!-- ALL -->
											<div class="form-row">
												<!-- Bundle Images Applying Filename -->
												<div class="form-group col-md-6">
													<label for="imageFileNames">
														<fmtSpring:message code="page.label.bundle.images.applying.filename"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="imageFileNames" class="flipToggle_bundleImageFilenames" path="applyFilenamesToBundledImages" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
													</div>
												</div>
												<!-- Filename SeparatorForBundledImages -->
												<div class="form-group col-md-6" id="filenameSeparatorContainer" style="display: none;">
													<label for="filenameSeparatorForBundledImages">
														<fmtSpring:message code="page.label.separator.for.bundled.image.filenames"/>
													</label>
													<div class="d-flex flex-column">
														<form:input id="filenameSeparatorForBundledImages" path="filenameSeparatorForBundledImages" cssClass="inputS" />
													</div>
												</div>
											</div>

											<!-- Bundle Combined Content -->
											<div class="form-row">
												<div class="form-group col-md-6" id="connector_bundle_combined_content">
													<label for="processUsingCombinedContent">
														<fmtSpring:message code="page.label.bundle.combined.content"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="processUsingCombinedContent" class="flipToggle_bundleCombinedContent" path="document.processUsingCombinedContent" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
													</div>
												</div>
											</div>

											<!-- ALL -->
											<div class="form-row">
												<!-- Play Message on Empty Vars -->
												<div class="form-group col-md-6">
													<label for="playMessageOnEmptyVar">
														<fmtSpring:message code="page.label.play.message.on.empty.variables"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="playMessageOnEmptyVar" class="flipToggle_playMessageOnEmptyVars" path="playMessageOnEmptyVar" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
													</div>
												</div>
												<c:if test="${showInternalLegacyToggles}">
												<!-- Play empty agg first last var -->
												<div class="form-group col-md-6">
													<label for="playEmptyAggFirstLastVar">
														<fmtSpring:message code="page.label.play.empty.agg.first.last.var"/>
														<span class="d-inline-block ml-2" data-toggle="tooltip"
															  title="${msgpt:getMessage('page.text.play.empty.agg.first.last.var')}">
															<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
														</span>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="playEmptyAggFirstLastVar" class="flipToggle_playEmptyAggFirstLastVar" path="playEmptyAggFirstLastVar" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
													</div>
												</div>
												</c:if>
											</div>

											<!-- ALL -->
											<div class="form-row">
												<!-- Validate Production Bundle -->
												<div class="form-group col-md-6">
													<label for="validateProductionBundle">
														<fmtSpring:message code="page.label.validate.production.bundle"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="validateProductionBundle" class="flipToggle_validateProductionBundle" path="validateProductionBundle" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
													</div>
												</div>
											</div>

											<div class="form-row">
												<!-- Render Table Border in pts -->
												<div class="form-group col-md-6" id="connector_dialogue" style="display: none;">
													<label for="convertTableBorderPxToPts">
														<fmtSpring:message code="page.label.render.table.border.in.pt"/>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="convertTableBorderPxToPts" class="flipToggle_convertTableBorderPxToPts" path="convertTableBorderPxToPts" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
													</div>
												</div>
											</div>

											<div class="form-row" id="connector_all" style="display: none;">
												<!-- Style Control -->
												<div class="form-group col-md-6" id="connector_sefas_connected" style="display: none;">
													<label for="channelSelection">
														<fmtSpring:message code="page.label.comp.time.parent.tagging"/>
													</label>
													<div class="d-flex flex-column">
														<table>
															<tr>
																<td><form:checkbox class="flipToggle_compTimeParentTagging" path="compTimeParentTagging" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
															</tr>
														</table>
													</div>
												</div>
												<c:if test="${showInternalLegacyToggles}">
												<div class="form-group col-md-6" id="connector_all">
													<label for="channelSelection">
														<fmtSpring:message code="page.label.data.group.expression.var.proc"/>
														<span class="d-inline-block ml-2" data-toggle="tooltip"
															  title="${msgpt:getMessage('page.text.data.group.expression.var.proc')}">
															<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
														</span>
													</label>
													<div class="d-flex flex-column">
														<table>
															<tr>
																<td><form:checkbox class="flipToggle_dataGroupExpressionVarProc" path="dataGroupExpressionVarProc" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
															</tr>
														</table>
													</div>
												</div>
												</c:if>
											</div>

											<c:if test="${showInternalLegacyToggles}">
												<div class="form-row">
													<div class="form-group col-md-6" id="connector_all">
														<label for="scriptVarAppliesUndefined">
															<fmtSpring:message code="page.label.script.var.applies.undefined"/>
															<span class="d-inline-block ml-2" data-toggle="tooltip"
																  title="${msgpt:getMessage('page.text.script.var.applies.undefined')}">
																<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
															</span>
														</label>
														<div class="d-flex flex-column">
															<table>
																<tr>
																	<td><form:checkbox id="scriptVarAppliesUndefined" class="flipToggle_scriptVarAppliesUndefined" path="scriptVarAppliesUndefined" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
																</tr>
															</table>
														</div>
													</div>
												</div>
											</c:if>

											<div class="form-row">
												<div class="form-group col-md-6" id="connector_all">
													<label for="correctParagraphTextStyles">
														<fmtSpring:message code="page.label.correct.paragraph.text.styles"/>
														<span class="d-inline-block ml-2" data-toggle="tooltip"
															  title="${msgpt:getMessage('page.text.correct.paragraph.text.styles')}">
																<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
															</span>
													</label>
													<div class="d-flex flex-column">
														<table>
															<tr>
																<td><form:checkbox id="correctParagraphTextStyles" class="flipToggle_correctParagraphTextStyles" path="correctParagraphTextStyles" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
															</tr>
														</table>
													</div>
												</div>
												<div class="form-group col-md-6" id="connector_all">
													<label for="preserveDataWhitespace">
														<fmtSpring:message code="page.label.preserve.data.whitespace"/>
														<span class="d-inline-block ml-2" data-toggle="tooltip"
															  title="${msgpt:getMessage('page.text.preserve.data.whitespace')}">
																<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
															</span>
													</label>
													<div class="d-flex flex-column">
														<table>
															<tr>
																<td><form:checkbox id="preserveDataWhitespace" class="flipToggle_preserveDataWhitespace" path="preserveDataWhitespace" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
															</tr>
														</table>
													</div>
												</div>
											</div>

											<div class="form-row">
												<div class="form-group col-md-6" id="connector_all">
													<label for="nbspComposedAsSpace">
														<fmtSpring:message code="page.label.nbsp.composed.as.space"/>
														<span class="d-inline-block ml-2" data-toggle="tooltip"
															  title="${msgpt:getMessage('page.text.nbsp.composed.as.space')}">
																<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
															</span>
													</label>
													<div class="d-flex flex-column">
														<table>
															<tr>
																<td><form:checkbox id="nbspComposedAsSpace" class="flipToggle_nbspComposedAsSpace" path="nbspComposedAsSpace" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
															</tr>
														</table>
													</div>
												</div>
												<div class="form-group col-md-6" id="connector_all">
													<label for="fixInlineTargetingStyles">
														<fmtSpring:message code="page.label.fix.inline.targeting.styles"/>
														<span class="d-inline-block ml-2" data-toggle="tooltip"
															  title="${msgpt:getMessage('page.text.fix.inline.targeting.styles')}">
																<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
															</span>
													</label>
													<div class="d-flex flex-column">
														<table>
															<tr>
																<td><form:checkbox id="fixInlineTargetingStyles" class="flipToggle_fixInlineTargetingStyles" path="fixInlineTargetingStyles" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
															</tr>
														</table>
													</div>
												</div>
											</div>

											<div class="form-row">
												<div class="form-group col-md-6" id="connector_all">
													<label for="normalizeEmbeddedContent">
														<fmtSpring:message code="page.label.normalize.smart.text"/>
														<span class="d-inline-block ml-2" data-toggle="tooltip"
															  title="${msgpt:getMessage('page.text.normalize.smart.text')}">
																<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
															</span>
													</label>
													<div class="d-flex flex-column">
														<table>
															<tr>
																<td><form:checkbox id="normalizeEmbeddedContent" class="flipToggle_normalizeEmbeddedContent" path="normalizeEmbeddedContent" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
															</tr>
														</table>
													</div>
												</div>
												<div class="form-group col-md-6" id="connector_all">
													<label for="normalizeImageLibrary">
														<fmtSpring:message code="page.label.normalize.image.library"/>
														<span class="d-inline-block ml-2" data-toggle="tooltip"
															  title="${msgpt:getMessage('page.text.normalize.image.library')}">
																<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
															</span>
													</label>
													<div class="d-flex flex-column">
														<table>
															<tr>
																<td><form:checkbox id="normalizeImageLibrary" class="flipToggle_normalizeImageLibrary" path="normalizeImageLibrary" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
															</tr>
														</table>
													</div>
												</div>
											</div>

											<div class="form-row">
												<div class="form-group col-md-6" id="connector_gmc">
													<label for="gmcSpanToTTag">
														<fmtSpring:message code="page.label.gmc.span.to.t.tag"/>
														<span class="d-inline-block ml-2" data-toggle="tooltip"
															  title="${msgpt:getMessage('page.text.gmc.span.to.t.tag')}">
																<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
															</span>
													</label>
													<div class="d-flex flex-column">
														<table>
															<tr>
																<td><form:checkbox id="gmcSpanToTTag" class="flipToggle_gmcSpanToTTag" path="gmcSpanToTTag" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
															</tr>
														</table>
													</div>
												</div>
											</div>

											<div class="form-row">
												<c:if test="${showInternalLegacyToggles}">
												<!-- Eval Not Equal On Missing Tag -->
												<div class="form-group col-md-6" id="connector_all" style="display: none;">
													<label for="evalNotEqualOnMissingTag">
														<fmtSpring:message code="page.label.eval.not.equal.on.missing.tag"/>
														<span class="d-inline-block ml-2" data-toggle="tooltip"
															  title="${msgpt:getMessage('page.text.eval.not.equal.on.missing.tag')}">
															<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
														</span>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="evalNotEqualOnMissingTag" class="flipToggle_evalNotEqualOnMissingTag" path="evalNotEqualOnMissingTag" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
													</div>
												</div>
												</c:if>
												<!-- Remove Zero From Style Connector -->
												<div class="form-group col-md-6" id="connector_dual_dialogue_gmc" style="display: none;">
													<label for="playEmptyAggFirstLastVar">
														<fmtSpring:message code="page.label.remove.underscore.zero.from.style.connector"/>
														<span class="d-inline-block ml-2" data-toggle="tooltip"
															  title="${msgpt:getMessage('page.text.remove.underscore.zero.from.style.connector')}">
															<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
														</span>
													</label>
													<div class="d-flex flex-column">
														<form:checkbox id="removeZeroFromStyleConnector" class="flipToggle_removeZeroFromStyleConnector" path="removeZeroFromStyleConnector" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}" />
													</div>
												</div>

											</div>

											<!-- MP Composer/Sefas Blue Underline Links -->
											<div class="form-row">
												<div class="form-group col-md-6" id="connector_gmc_sefas">
													<label for="blueUnderlineLinks">
														<fmtSpring:message code="page.label.blue.underline.links"/>
														<span class="d-inline-block ml-2" data-toggle="tooltip"
															  title="${msgpt:getMessage('page.text.blue.underline.links')}">
																<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
															</span>
													</label>
													<div class="d-flex flex-column">
														<table>
															<tr>
																<td><form:checkbox id="blueUnderlineLinks" class="flipToggle_blueUnderlineLinks" path="blueUnderlineLinks" title="${msgpt:getMessage('page.label.yes')};${msgpt:getMessage('page.label.no')}"  /></td>
															</tr>
														</table>
													</div>
												</div>
											</div>

											<!-- ALL -->
											<div class="form-row">
												<!-- Customer Driver Input Filename -->
												<div class="form-group col-md-6" id="connector_all" style="display: none;">
													<label for="customerDriverInputFilename">
														<fmtSpring:message code="page.label.customer.production.files.folder"/>
													</label>
													<msgpt:InputFilter type="filename">
														<form:input id="customerDriverInputFilename" cssClass="inputXXL" maxlength="255" path="customerDriverInputFilename" />
													</msgpt:InputFilter>
												</div>
												<c:if test="${!connectedEnabled}">
													<!-- Production Data Resource -->
													<div class="form-group col-md-6">
														<label for="productionDataResourceSelect">
															<fmtSpring:message code="page.label.customer.production.files.dataresource"/>
														</label>
														<div class="d-flex flex-column">
															<form:select cssClass="inputXXL productionDataResourceSelect style_select" id="productionDataResourceSelect" path="document.communicationsDataResource">
																<form:option value="0">-- <fmtSpring:message code="page.text.select.resource"/> --</form:option>
																<form:options items="${dataResources}" itemValue="id" itemLabel="name" />
															</form:select>
														</div>
													</div>
												</c:if>
											</div>

											<div class="form-row">
												<!-- PreQualEngineScript Path -->
												<div class="form-group col-md-6">
													<label for="preQualEngineScriptSelect">
														<fmtSpring:message code="page.label.preprocessor.script"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="preQualEngineScriptSelect" class="inputS" path="preQualEngineScript">
															<form:option value=""><fmtSpring:message code="page.label.none"/></form:option>
															<c:forEach var="dewsScript" items="${dewsScripts}">
																<form:option value="${dewsScript}"><c:out value="${dewsScript}" /></form:option>
															</c:forEach>
														</form:select>
													</div>
												</div>
											</div>

											<!-- ALL -->
											<div class="form-row">
												<!-- PostQualEngineScript Path-->
												<div class="form-group col-md-6">
													<label for="postQualEngineScriptSelect">
														<fmtSpring:message code="page.label.postprocessor.script1"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="postQualEngineScriptSelect" class="inputS" path="postQualEngineScript">
															<form:option value=""><fmtSpring:message code="page.label.none"/></form:option>
															<c:forEach var="dewsScript" items="${dewsScripts}">
																<form:option value="${dewsScript}"><c:out value="${dewsScript}" /></form:option>
															</c:forEach>
														</form:select>
													</div>
												</div>
												<!-- PostConnectorScript Path-->
												<div class="form-group col-md-6">
													<label for="postConnectorScriptSelect">
														<fmtSpring:message code="page.label.postprocessor.script2"/>
													</label>
													<div class="d-flex flex-column">
														<form:select id="postConnectorScriptSelect" class="inputS" path="postConnectorScript">
															<form:option value=""><fmtSpring:message code="page.label.none"/></form:option>
															<c:forEach var="dewsScript" items="${dewsScripts}">
																<form:option value="${dewsScript}"><c:out value="${dewsScript}" /></form:option>
															</c:forEach>
														</form:select>
													</div>
												</div>
											</div>
											<div class="form-row">
												<div class="form-group col-md-6">
													<label>
														<fmtSpring:message code="page.label.external.path.use.image.as.alternate" />
														<span class="d-inline-block ml-2" data-toggle="tooltip"
															  title="${msgpt:getMessage('page.label.uploaded.image.use.when.external.path.not.found')}">
															<i class="far fa-info-circle text-info mr-1" aria-hidden="true"></i>
														</span>
													</label>
													<div class="d-flex flex-column">
														<form:select id="useDefaultImage" cssClass="w-100" path="useDefaultImage">
															<form:option value="0"><fmtSpring:message code="page.label.use.uploaded.image.test.only"/></form:option>
															<form:option value="1"><fmtSpring:message code="page.label.use.uploaded.image.test.production.only"/></form:option>
															<form:option value="2"><fmtSpring:message code="page.label.never.use.uploaded.image"/></form:option>
														</form:select>
													</div>
												</div>
											</div>

										</section>
										<!-- END Channel Configuration -->

										<!-- BUTTONS -->
										<div class="d-flex flex-column flex-xl-row pb-4 bg-white" style="text-align: right;">

											<div class="py-1 p-xl-0 mt-xl-0 ml-xl-auto order-xl-1">
												<div class="btn-group ml-3">
													<button id="channelConfigCancelBtn2" onclick="javascriptHref('${cancelURL}')" type="button"
															class="btn btn-outline-light text-body post-trigger">
														<i class="far fa-times-circle mr-2" aria-hidden="true"></i>
														<fmtSpring:message code="page.label.cancel"/>
													</button>
												</div>
												<div class="btn-group border-separate ml-3">
													<button id="channelConfigSaveBtn2" type="button"
															onclick="javascript:document.getElementById('command').submit();"
															class="btn btn-primary post-trigger">
														<i class="far fa-save mr-2" aria-hidden="true"></i>
														<fmtSpring:message code="page.label.save"/>
													</button>
												</div>
											</div>
										</div>
										<!-- END BUTTONS -->

									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

			</form:form>

		</msgpt:ContentPanel>
	</msgpt:LowerContainer>
</msgpt:BodyNew>
</msgpt:Html5>