<%@ include file="../includes/includes.jsp" %>
<%@page import="com.prinova.messagepoint.model.admin.ContentType" %>
<%@page import="com.prinova.messagepoint.model.admin.ParameterGroupTreeNode" %>
<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<msgpt:Html5>
    <c:set var="contentSelection" value="${command.contentSelection}"/>
    <c:set var="contentObject" value="${contentSelection.contentObject}"/>
    <c:set var="showDocumentWidget"
           value="${not contentObject.isTouchpointLocal && not contentObject.isGlobalContentObject}"/>
    <c:set var="showVariants" value="${contentObject.dynamicVariantEnabled || contentObject.structuredContentEnabled}"/>
    <msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.VIEW %>"
                     extendedScripts="true">


        <link rel="stylesheet" type="text/css" href="${contentObject.defaultViewCSSFilePath}?cacheStamp=${timeStamp}"/>

        <c:if test="${not empty contentObject.styles}">
            <link rel="stylesheet" type="text/css" href="${contentObject.CSSFilename}?cacheStamp=${timeStamp}"/>
        </c:if>
        <c:if test="${not empty contentObject.paragraphStyles}">
            <link rel="stylesheet" type="text/css"
                  href="${contentObject.paragraphCSSFilename}?cacheStamp=${timeStamp}"/>
        </c:if>
        <c:if test="${not empty contentObject.listStyles}">
            <link rel="stylesheet" type="text/css" href="${contentObject.listCSSFilename}?cacheStamp=${timeStamp}"/>
        </c:if>

        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/jstree/themes/proton/style.css"/>

        <msgpt:Script src="includes/javascript/${tinymceDir}/jquery.tinymce.min.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/tinyMCEinit/jquery.tinyMCEinit.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/viewTagPanel/jquery.viewTagPanel.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/viewTagPanel/jquery.viewTagPanel.css"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/contentEditor/jquery.contentEditor.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/fastNavigation/jquery.fastNavigation.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/sharedTextFastEdit/jquery.sharedTextFastEdit.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/smartTextContentViewer/jquery.smartTextContentViewer.js"/>
        <msgpt:Script src="includes/javascript/pdfObject/pdfobject.min.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.varTagAttrManager.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.smartTextTagAttrManager.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.placeholderTagAttrManager.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/contentEditor/contentEditor.css"/>
        <msgpt:Stylesheet href="includes/javascript/${tinymceDir}/skins/lightgray/skin.min.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/jstree/jstree.js"/>
        <c:if test="${showVariants}">
            <msgpt:Script src="includes/javascript/variantSelection.js"/>
        </c:if>

        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>
        <msgpt:Script src="_ux/js/mp.stickyBar.js"/>
        <msgpt:Script src="_ux/js/mp.postTrigger.js"/>
        <msgpt:Script src="_ux/js/mp.iFramePlaceholder.js"/>

        <msgpt:Script>
            <script>
                var pageDataModule =  {
                    contentObjectId: '${contentObject.id}',
                    contentObjectName: "${msgpt:removeSpecialCharacters(contentObject.name)}",
                    contentTreeNodeId: '${touchpointSelection.parameterGroupTreeNode.id}',
                    contentTreeNodeName: "${msgpt:removeSpecialCharacters(touchpointSelection.parameterGroupTreeNode.name)}",
                };
            </script>
        </msgpt:Script>

        <msgpt:Script src="_ux/js/mp.historyQueue.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/taskManager/jquery.taskManager.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/taskManager/taskManager.css"/>

        <style type="text/css">
            .levelIndentation {
                width: 15px;
                height: 12px;
                float: left;
                background: url('../includes/themes/commonimages/icons/indentation_dot.gif') no-repeat left bottom;
            }

            li a.disabledTreeNode {
                color: #777;
            }
        </style>

        <msgpt:Script insertBeforeExternals="true">
            <script>
                var jsp_touchpointSelection_parameterGroupTreeNode_id = ${touchpointSelection.parameterGroupTreeNode.id};
                var jsp_document_id = ${document.id};

                var jsp_text_data = ${!empty contentObject.styles ? contentObject.textStyleData : "null"};
                var jsp_paragraph_data = ${!empty contentObject.paragraphStyles ? contentObject.paragraphStyleData : "null"};
                var jsp_charCountEnabled = ${contentObject.isSmsDelivery || contentObject.isEmailSubjectDelivery};
                var jsp_contextPath = '${contextPath}';
                var jsp_defaultLocaleId = '${defaultLocaleId}';
                var jsp_focusLocaleId = '${focusLocaleId}';
                var jsp_languages = ${languagesJSON};
                var jsp_contentObjectId = '${contentObject.id}';
                var jsp_applies_freeform = ${contentObject.isFreeform};
                var jsp_applies_canvas_dimension = ${!contentObject.isMarkup};
                var jsp_zone_rotation = ${contentObject.zoneRotation};
                var jsp_size = '${contentObject.isEmailSubjectDelivery || contentObject.isSmsDelivery ? "100px" : (contentObject.isVideoDelivery ? "large" : "400px") }';
                var jsp_statusViewId = ${contentObject.focusOnActiveData ? 2 : 1};
                var jsp_is_touchpoint_local = ${contentObject.isTouchpointLocal};
                var jsp_touchpoint_selection_id = ${touchpointSelection.id};

                var variantSelectionArgs = {
                    contentObjectId: ${contentObject.id},
                    documentId: ${document.id},
                    statusViewId: ${contentObject.focusOnArchivedData ? 4 : ( !isStatusViewActive && touchpointSelection.hasWorkingCopy ? 1 : 2 )},
                    getMasterNodeId: function () {
                        return $('#selectionNavTree').variantTree('getSettings').masterVariantId;
                    }
                };

                function openSelectedStatus(status) {
                    var searchParams = window.location.search.replace(/statusViewId=[^&$]*/, 'statusViewId=' + status).replace(/dataType=[^&$]*/, 'dataType=' + status);
                    if(!window.location.search.includes('statusViewId')) {
                        searchParams += '&statusViewId=' + status;
                    }
                    window.location.search = searchParams;
                }
            </script>
        </msgpt:Script>

        <msgpt:Script src="tpadmin/javascript/touchpoint_content_selection_view.js"/>
    </msgpt:HeaderNew>

    <msgpt:BodyNew type="${param.fastView ? 'iframe' : ''}">
        <c:if test="${not param.fastView}">
            <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>
            <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>"/>
        </c:if>
        <c:set var="contentObject" value="${contentSelection.contentObject}"/>
        <c:set var="isMySelection" value="${touchpointSelection.isMine}"/>
        <c:set var="isActiveSelection" value="${touchpointSelection.isActive}"/>
        <c:set var="textContentTypeID" value="<%= ContentType.TEXT %>"/>
        <c:set var="graphicContentTypeID" value="<%= ContentType.GRAPHIC %>"/>
        <c:set var="videoContentTypeID" value="<%= ContentType.VIDEO %>"/>
        <c:set var="isDefaultSelection" value="${touchpointSelection.master}"/>
        <c:set var="editPerm" value="false" scope="request"/>
        <msgpt:IfAuthGranted authority="ROLE_LICENCED_TOUCHPOINT_SELECTIONS_EDIT">
            <c:set var="editPerm" value="true" scope="request"/>
        </msgpt:IfAuthGranted>
        <c:if test="${contentObject.multipartType}">
            <input type="hidden" id="multipartMessage"/>
        </c:if>
        <input type="hidden" id="touchpointSelectionContextId" value="${touchpointSelection.id}"/>
        <div id="content" class="d-table-row h-100">
            <form:form modelAttribute="command" method="post" enctype="multipart/form-data">
                <div class="${param.fastView ? "" : "container mt-5 shiftContainer"}">
                    <form:errors path="*">
                        <msgpt:Information errorMsgs="${messages}" type="error"/>
                    </form:errors>
                    <c:if test="${not empty param.saveSuccess && param.saveSuccess == 'true'}">
                        <msgpt:Information type="success">
                            <fmtSpring:message code="page.label.save.complete"/>
                        </msgpt:Information>
                    </c:if>
                    <h1 class="iframe-modal-title h4 pb-2 mb-4">
                        <span id="popupHeaderTitle">
                            <span class="font-weight-normal fs-md text-muted"><c:out
                                    value='${contentObject.document.name}'/></span><br/>
                            <span class="titleText">
                                <msgpt:Breadcrumb titleText="${msgpt:getMessage('page.text.content.for').concat(': ')}"
                                                  contentObjectName="${contentObject.name}" />
                            </span>
                            <span class="font-weight-normal fs-sm text-muted ml-2">
                                <c:choose>
                                    <c:when test="${isEnabledForVariantWorkflow && !isMaster}">
                                        <c:choose>
                                            <c:when test="${((contentObject.hasActiveData && contentObject.hasWorkingData) || (touchpointSelection.hasActiveCopy && touchpointSelection.hasWorkingCopy)) && !contentObject.focusOnArchivedData}">
                                                <div class="dropdown d-inline-flex ml-1">
                                                    <button
                                                            type="button"
                                                            class="d-flex align-items-center btn rounded-pill dropdown-toggle ${isStatusViewActive ? 'btn-outline-success bg-success-lightest text-success-dark' : 'btn-outline-light bg-lightest text-dark'}"
                                                            data-toggle="dropdown"
                                                            aria-expanded="false">
                                                        <span class="text-uppercase font-weight-bold mr-1">
                                                            <c:if test="${isStatusViewActive}">
                                                                <i class="fas fa-check fs-sm mr-2"
                                                                   aria-hidden="true"></i><fmtSpring:message
                                                                    code="page.label.ACTIVE"/>
                                                            </c:if>
                                                            <c:if test="${not isStatusViewActive}">
                                                                <i class="fas fa-pencil fs-sm mr-2"
                                                                   aria-hidden="true"></i><fmtSpring:message
                                                                    code="page.label.WORKING.COPY"/>
                                                            </c:if>
                                                        </span>
                                                    </button>
                                                    <div class="dropdown-menu dropdown-menu-right">
                                                        <c:if test="${not isStatusViewActive}">
                                                            <button class="dropdown-item text-success"
                                                                    type="button"
                                                                    onclick="openSelectedStatus(2)">
                                                                <i class="fas fa-check-circle fa-lg mr-2"
                                                                   aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.active"/>
                                                            </button>
                                                        </c:if>
                                                        <c:if test="${isStatusViewActive}">
                                                            <button class="dropdown-item" type="button"
                                                                    onclick="openSelectedStatus(1)">
                                                                <i class="fas fa-pen-circle fa-lg mr-2"
                                                                   aria-hidden="true"></i>
                                                                <fmtSpring:message code="page.label.working.copy"/>
                                                            </button>
                                                        </c:if>
                                                    </div>
                                                </div>
                                            </c:when>
                                            <c:when test="${!isStatusViewActive && touchpointSelection.hasWorkingCopy && !contentObject.focusOnArchivedData}">
                                                <div class="d-inline-flex ml-1">
                                                    <span class="d-flex align-items-center btn rounded-pill ${contentObject.onHold ? 'btn-outline-warning bg-warning-lightest text-warning-dark' : 'btn-outline-light bg-lightest text-dark'}">
                                                        <span class="text-uppercase font-weight-bold mr-1">
                                                            <c:if test="${not contentObject.onHold}">
                                                                <i class="workingCopyIconDiv fa fa-pencil fs-sm mr-2"></i>
                                                            </c:if>
                                                            <c:if test="${contentObject.onHold}">
                                                                <i class="fa fa-pause fs-sm mr-2"></i>
                                                            </c:if>
                                                            <span class="labelText"><fmtSpring:message
                                                                    code="page.label.WORKING.COPY"/></span>
                                                            <c:if test="${contentObject.onHold}">
                                                                <span class="labelText"><fmtSpring:message
                                                                        code="page.text.ON.HOLD"/></span>
                                                            </c:if>
                                                        </span>
                                                    </span>
                                                </div>
                                            </c:when>
                                            <c:when test="${contentObject.focusOnArchivedData}">
                                                <div class="d-inline-flex ml-1">
                                                    <span class="d-flex align-items-center btn rounded-pill btn-outline-light bg-lightest text-dark">
                                                        <span class="text-uppercase font-weight-bold mr-1">
                                                            <i class="archiveIconDiv fa fa-archive fs-sm mr-2"></i>
                                                            <span class="labelText"><fmtSpring:message
                                                                    code="page.label.ARCHIVE"/></span>
                                                        </span>
                                                    </span>
                                                </div>
                                            </c:when>
                                            <c:otherwise>
                                                <div class="d-inline-flex ml-1">
                                                    <span class="d-flex align-items-center btn rounded-pill ${contentObject.suppressed ? 'btn-outline-danger bg-danger-lightest text-danger-dark' : 'btn-outline-success bg-success-lightest text-success-dark'}">
                                                        <span class="text-uppercase font-weight-bold mr-1">
                                                            <c:if test="${not contentObject.suppressed}">
                                                                <i class="activeIconDiv fas fa-check fs-sm mr-2"></i>
                                                            </c:if>
                                                            <c:if test="${contentObject.suppressed}">
                                                                <i class="fa fa-times fs-sm mr-2"></i>
                                                            </c:if>
                                                            <span class="labelText"><fmtSpring:message
                                                                    code="page.label.ACTIVE"/></span>
                                                            <c:if test="${contentObject.suppressed}">
                                                                <span class="labelText"><fmtSpring:message
                                                                        code="page.text.SUPPRESSED"/></span>
                                                            </c:if>
                                                        </span>
                                                    </span>
                                                </div>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:when>
                                    <c:otherwise>
                                        <c:if test="${contentObject.focusOnActiveData || contentObject.focusOnWorkingData || contentObject.focusOnArchivedData}">
                                            <c:choose>
                                                <c:when test="${contentObject.hasWorkingData && contentObject.hasActiveData && !contentObject.focusOnArchivedData}">
                                                    <div class="btn-group">
                                                        <button
                                                                type="button"
                                                                class="d-flex align-items-center btn rounded-pill dropdown-toggle ${contentObject.focusOnActiveData ? 'btn-outline-success bg-success-lightest text-success-dark' : 'btn-outline-light bg-lightest text-dark'}"
                                                                data-toggle="dropdown"
                                                                aria-expanded="false">
                                                            <span class=" text-uppercase font-weight-bold mr-1">
                                                                <c:if test="${contentObject.focusOnActiveData}">
                                                                    <i class="fas fa-check fs-sm mr-2"
                                                                       aria-hidden="true"></i><fmtSpring:message
                                                                        code="page.label.ACTIVE"/>
                                                                </c:if>
                                                                <c:if test="${contentObject.focusOnWorkingData}">
                                                                    <i class="fas fa-pencil fs-sm mr-2"
                                                                       aria-hidden="true"></i><fmtSpring:message
                                                                        code="page.label.WORKING.COPY"/>
                                                                </c:if>
                                                            </span>
                                                        </button>
                                                        <div class="dropdown-menu dropdown-menu-right">
                                                            <c:if test="${not contentObject.focusOnActiveData}">
                                                                <button class="dropdown-item text-success"
                                                                        type="button"
                                                                        onclick="openSelectedStatus(2)">
                                                                    <i class="fas fa-check-circle fa-lg mr-2"
                                                                       aria-hidden="true"></i>
                                                                    <fmtSpring:message code="page.label.active"/>
                                                                </button>
                                                            </c:if>
                                                            <c:if test="${not contentObject.focusOnWorkingData}">
                                                                <button class="dropdown-item" type="button"
                                                                        onclick="openSelectedStatus(1)">
                                                                    <i class="fas fa-pen-circle fa-lg mr-2"
                                                                       aria-hidden="true"></i>
                                                                    <fmtSpring:message code="page.label.working.copy"/>
                                                                </button>
                                                            </c:if>
                                                        </div>
                                                    </div>
                                                </c:when>
                                                <c:when test="${contentObject.focusOnActiveData}">
                                                    <c:set var="editPerm" value="false" scope="request"/>
                                                    <div class="d-inline-flex ml-1">
                                                        <span class="d-flex align-items-center btn rounded-pill ${contentObject.suppressed ? 'btn-outline-danger bg-danger-lightest text-danger-dark' : 'btn-outline-success bg-success-lightest text-success-dark'}">
                                                            <span class="text-uppercase font-weight-bold mr-1">
                                                                <c:if test="${not contentObject.suppressed}">
                                                                    <i class="activeIconDiv fas fa-check fs-sm mr-2"></i>
                                                                </c:if>
                                                                <c:if test="${contentObject.suppressed}">
                                                                    <i class="fa fa-times fs-sm mr-2"></i>
                                                                </c:if>
                                                                <span class="labelText"><fmtSpring:message
                                                                        code="page.label.ACTIVE"/></span>
                                                                <c:if test="${contentObject.suppressed}">
                                                                    <span class="labelText"> <fmtSpring:message
                                                                            code="page.text.SUPPRESSED"/></span>
                                                                </c:if>
                                                            </span>
                                                        </span>
                                                    </div>
                                                </c:when>
                                                <c:when test="${contentObject.focusOnWorkingData}">
                                                    <div class="d-inline-flex ml-1">
                                                        <span class="d-flex align-items-center btn rounded-pill ${contentObject.onHold ? 'btn-outline-warning bg-warning-lightest text-warning-dark' : 'btn-outline-light bg-lightest text-dark'}">
                                                            <span class="text-uppercase font-weight-bold mr-1">
                                                                <c:if test="${not contentObject.onHold}">
                                                                    <i class="workingCopyIconDiv fa fa-pencil fs-sm mr-2"></i>
                                                                </c:if>
                                                                <c:if test="${contentObject.onHold}">
                                                                    <i class="fa fa-pause fs-sm mr-2"></i>
                                                                </c:if>
                                                                <span class="labelText"><fmtSpring:message
                                                                        code="page.label.WORKING.COPY"/></span>
                                                                <c:if test="${contentObject.onHold}">
                                                                    <span class="labelText"> <fmtSpring:message
                                                                            code="page.text.ON.HOLD"/></span>
                                                                </c:if>
                                                            </span>
                                                        </span>
                                                    </div>
                                                </c:when>
                                                <c:when test="${contentObject.focusOnArchivedData}">
                                                    <div class="d-inline-flex ml-1">
                                                        <span class="d-flex align-items-center btn rounded-pill btn-outline-light bg-lightest text-dark">
                                                            <span class="text-uppercase font-weight-bold mr-1">
                                                                <i class="archiveIconDiv fa fa-archive fs-sm mr-2"></i>
                                                                <span class="labelText"><fmtSpring:message
                                                                        code="page.label.ARCHIVE"/></span>
                                                            </span>
                                                        </span>
                                                    </div>
                                                </c:when>
                                            </c:choose>
                                        </c:if>
                                    </c:otherwise>
                                </c:choose>
                            </span>
                        </span>
                    </h1>
                    <div class="${param.fastView ? "" : "box-shadow-4 rounded bg-white"}">
                        <c:if test="${not param.fastView}">
                            <div class="position-relative">
                                <div class="border-bottom" data-sticky="top"
                                     data-fixedclass="container px-0 box-shadow-4 bg-white border-white rounded-top">
                                    <div class="d-flex align-items-center mx-4 py-3 bg-white rounded-top">
                                        <div class="d-flex align-items-center">
                                            <div class="btn-group border-separate mr-3">
                                                <c:if test="${!isStatusViewActive}">
                                                    <button type="button" class="btn btn-dark post-trigger"
                                                            id="updateBtn" ${canEditInTranslation?'':(  (!editPerm ||
                                                            (!isEnabledForVariantWorkflow && !contentObject.mine) ||
                                                            (isEnabledForVariantWorkflow && (isActiveSelection ||
                                                                    (!isMySelection && !isActiveSelection) ||
                                                                    touchpointSelection.releasedForApproval ))) ? 'disabled' : '')}
                                                            onclick="btnJavascriptHref('touchpoint_content_selection_edit.form?contentObjectId=${contentObject.id}&contentSelectionId=${contentSelection.id}&touchpointSelectionId=${touchpointSelection.id}', this);">
                                                        <i class="far fa-edit mr-2" aria-hidden="true"></i>
                                                        <fmtSpring:message code="action.button.label.update"/>
                                                    </button>
                                                </c:if>
                                                <c:if test="${not isDefaultSelection}">
                                                    <button title="${msgpt:getMessage('page.label.VIEW.DATA')}"
                                                            type="button"
                                                            id="viewDataBtn" class="btn btn-dark"
                                                            onclick="${touchpointSelection.hasSelectorValues ? 'actionSelected(7)' : ''}" ${touchpointSelection.hasSelectorValues ? '' : 'disabled'}>
                                                        <fmtSpring:message code="page.label.view.data"/>
                                                    </button>
                                                </c:if>
                                            </div>
                                            <c:if test="${hasTaskViewPermission}">
                                                <div id="taskIndicator"
                                                     class="position-relative ml-1 fs-md actionBarTaskIndicator"></div>
                                            </c:if>
                                        </div>
                                        <div class="d-flex ml-auto">
                                            <button type="button" class="btn btn-outline-light text-body post-trigger"
                                                    onclick="submitAction(99, this);">
                                                <i class="far fa-times-circle mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="page.label.cancel"/>
                                            </button>
                                            <div class="btn-group border-separate ml-3">
                                                <!-- WORKFLOW HISTORY -->
                                                <button class="btn btn-dark" type="button"
                                                        id="contentHistoryBtn" onclick="contentHistoryPopup()">
                                                    <fmtSpring:message code="page.label.history"/>
                                                </button>
                                                <!-- VARIANT CONTENT COMPARE -->
                                                <c:if test="${contentObject.dynamicVariantEnabled || contentObject.structuredContentEnabled}">
                                                    <button type="button" id="variantContentCompareBtn"
                                                            class="btn btn-dark" data-toggle="tooltip"
                                                            title="${msgpt:getMessage('page.label.content.compare')}"
                                                            aria-label="${msgpt:getMessage('page.label.content.compare')}">
                                                        <i class="far fa-exchange fs-md" aria-hidden="true"></i>
                                                    </button>
                                                </c:if>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </c:if>
                        <!-- Proof bindings -->
                        <form:hidden id="targetProofZoneInput" path="targetProofZone"/>
                        <form:hidden id="targetProofTypeInput" path="proofType"/>
                        <div class="p-4 fullscreenBoundary">
                            <div class="${param.fastView ? "p-2" : "pt-2 px-2"}">
                                <div class="row">
                                    <c:if test="${showDocumentWidget || showVariants}">
                                        <div id="side-section" class="col-auto mx-auto pr-5">
                                            <div class="bg-lightest p-4 rounded">
                                                <div  id="side-section-content" class="px-2 opacity-0">
                                                    <c:if test="${showDocumentWidget && showVariants}">
                                                        <div class="w-75 ml-auto mb-3">
                                                            <div class="nav nav-justified btn-group btn-group-sm"
                                                                 id="nav-tab"
                                                                 role="tablist">
                                                                <a class="nav-item btn btn-outline-primary active persistedClass"
                                                                   id="nav-thumbnail-tab"
                                                                   data-toggle="tab" href="#nav-thumbnail" role="tab"
                                                                   aria-controls="nav-thumbnail"
                                                                   aria-selected="true"><fmtSpring:message
                                                                        code="page.label.thumbnail"/></a>
                                                                <a class="nav-item btn btn-outline-primary persistedClass"
                                                                   id="nav-variants-tab"
                                                                   data-toggle="tab"
                                                                   href="#nav-variants" role="tab"
                                                                   aria-controls="nav-variants"
                                                                   aria-selected="false"><fmtSpring:message
                                                                        code="page.label.variants"/></a>
                                                            </div>
                                                        </div>
                                                    </c:if>
                                                    <div class="tab-content" id="nav-tabContent">
                                                        <c:if test="${showDocumentWidget}">
                                                            <div id="nav-thumbnail"
                                                                 class="${(showDocumentWidget && showVariants) ? 'tab-pane fade show active' : ''} pt-2"
                                                                ${(showDocumentWidget && showVariants) ? 'role="tabpanel" aria-labelledby="nav-thumbnail-tab"' : ''}>
                                                                <div class="pt-2">
                                                                    <div id="docDisplay_${touchpointContext.id}">
                                                                        <msgpt:DocumentTag document="${document}"
                                                                                           contentObject="${contentObject}"
                                                                                           type="standard"
                                                                                           readOnly="true"
                                                                                           hasPartAction="${contentObject.multipartType}"
                                                                                           maxWidth="296"/>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </c:if>
                                                        <c:if test="${showVariants}">
                                                            <div id="nav-variants"
                                                                 class="${(showDocumentWidget && showVariants) ? 'tab-pane fade' : ''} pt-2"
                                                                ${(showDocumentWidget && showVariants) ? 'role="tabpanel" aria-labelledby="nav-variants-tab"' : ''}>
                                                                <div class="navPanelContainer">
                                                                    <div style="padding: 16px 20px 3px 20px; border-bottom: 1px solid #ccc; height: 30px;"
                                                                         class="navTreeFixedItems roundTopLeft_6px">
                                                                        <table cellspacing="0" cellpadding="0"
                                                                               border="0">
                                                                            <tr onclick="toogleSearchDisplay();">
                                                                                <td><fmtSpring:message
                                                                                        code="page.label.search"/></td>
                                                                                <td align="left" width="99%"
                                                                                    style="vertical-align: middle;">
                                                                                    <div id="searchExpansionArrow"
                                                                                         class="expansionArrow_closed"
                                                                                         style="width: 15px; height: 10px; font-size: 1px;">
                                                                                        &nbsp;
                                                                                    </div>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                    <div id="searchInputs"
                                                                         style="padding: 12px 8px; border-bottom: 1px solid #d0d0d0; background-color: #eee; display: none;">
                                                                        <msgpt:DataTable labelPosition="top"
                                                                                         style="margin-bottom: 0px;">
                                                                            <c:forEach var="currentSearchParam"
                                                                                       items="${searchParameters}"
                                                                                       varStatus="paramStat">
                                                                                <msgpt:TableItem
                                                                                        label="${currentSearchParam.parameter.name}">
                                                                                    <msgpt:InputFilter
                                                                                            type="dataElementVariable">
                                                                                        <form:input
                                                                                                path="searchValues[${paramStat.index}]"
                                                                                                cssClass="input250"/>
                                                                                    </msgpt:InputFilter>
                                                                                </msgpt:TableItem>
                                                                            </c:forEach>
                                                                            <msgpt:TableItem>
                                                                                <div style="float: right;">
                                                                                    <input id="variantFindBtn"
                                                                                           title="${msgpt:getMessage('page.label.find')}"
                                                                                           type="button"
                                                                                           onclick="submitAction('10',this);"/>
                                                                                </div>
                                                                            </msgpt:TableItem>
                                                                        </msgpt:DataTable>
                                                                    </div>
                                                                    <msgpt:VariantTree id="selectionNavTree"
                                                                                       expanded="false"
                                                                                       showContentStatus="true"
                                                                                       onChange="variantSelected"
                                                                                       selectedNodeId="${touchpointSelection.id}"
                                                                                       dataBinding="${masterTouchpointSelectionWrapper}"
                                                                                       style="overflow-y: auto; width: 18.5rem; height: 40rem; background-color: #fff"/>
                                                                </div>
                                                            </div>
                                                        </c:if>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </c:if>
                                    <div class="${(showDocumentWidget || showVariants) ? 'col-xl pt-3' : 'col-lg-9 col-xl-8 mx-lg-auto pt-3'}">
                                        <div class="mt-2">
                                            <dl class="row no-gutters mb-0">
                                                <div class="col-lg-6 pb-1">
                                                    <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                        <fmtSpring:message code="page.label.assigned.to"/>
                                                    </dt>
                                                    <dd class="pr-4 py-2">
                                                        <c:choose>
                                                            <c:when test="${not empty assignedTo}">
                                                                <i class="far fa-user mr-1" aria-hidden="true"></i>
                                                                <c:out value="${assignedTo}"/>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <fmtSpring:message code="page.label.na"/>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </dd>
                                                </div>
                                                <div class="col-lg-6 pb-1">
                                                    <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                        <fmtSpring:message code="page.label.updated"/>
                                                    </dt>
                                                    <dd class="pr-4 py-2">
                                                        <c:choose>
                                                            <c:when test="${not empty contentSelection.updated}">
                                                                <fmtJSTL:formatDate value="${contentSelection.updated}"
                                                                                    pattern="${dateTimeFormat}"/>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <fmtSpring:message code="page.label.new.content"/>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </dd>
                                                </div>
                                            </dl>
                                        </div>
                                        <section class="mt-5">
                                            <h2 class="h4 d-flex align-items-baseline pb-3 mb-4 border-bottom border-thick font-weight-normal">
                                                <span class="mr-auto">
                                                    <c:out value="${msgpt:getMessage('page.label.structured')} ${msgpt:getMessage('page.label.content')}"/>
                                                </span>
                                                <span class="fs-sm text-uppercase text-muted font-weight-bold">
                                                    (<fmtSpring:message code="page.text.switch.to.master.to.edit"/>)
                                                </span>
                                            </h2>
                                            <div>
                                                <c:set var="editorState" value="none"/>
                                                <c:choose>
                                                    <c:when test="${contentType.id == contentType_CUSTOM}">
                                                        <c:set var="editorState" value="edit"/>
                                                    </c:when>
                                                    <c:when test="${contentType.id == contentType_SUPPRESSES}">
                                                        <c:set var="editorState" value="suppress"/>
                                                    </c:when>
                                                    <c:when test="${contentType.id == contentType_SAME_AS}">
                                                        <c:set var="editorState" value="sameAs"/>
                                                    </c:when>
                                                </c:choose>
                                                <!-- DEFAULT Content: Tab by language -->
                                                <c:if test="${ not contentObject.multipartType }">
                                                    <c:set var="contentMap" value="${command.contentVO.langContentMap}"/>
                                                    <div id="contentData" globalState="${editorState}"
                                                         globalRefObjLabel="${contentSelection.contentObjectAssociation.referencingTouchpointPGTreeNode.name}"
                                                         style="display: none;">
                                                        <div class="contentGroup"
                                                             groupId="zone${zoneId}"
                                                             groupState="inherit"
                                                             groupRefObjLabel="UNUSED"
                                                             backgroundColor="${contentObject.zoneBackgroundColor}"
                                                             canvasDimensions="${contentObject.canvasDimensions}"
                                                             canvasRotation="${contentObject.canvasRotation}"
                                                             content="${contentObject.isText || contentObject.isMarkup || contentObject.isVideoDelivery ? 'text' : 'graphic'}">
                                                            <c:forEach var="locale" items="${locales}"
                                                                       varStatus="langStat">
                                                                <div class="contentEntry"
                                                                     contentState="inherit"
                                                                     localeId="${locale.id}">
                                                                    <c:if test="${isContentObjectTimeStampEnable}">
                                                                        <div class="contentInfo">
                                                                            <c:set var="isFromSource" value="${contentMap[locale.id].sameAsDefault
                                                                                || contentSelection.contentObjectAssociation.content.id == contentMap[locale.id].contentId}" />

                                                                            <p class="mb-0">
                                                                                <strong>
                                                                                    <fmtSpring:message code="page.label.content.id"/>
                                                                                    <c:if test="${isFromSource}">&nbsp;<fmtSpring:message code="page.label.from.source.content"/></c:if>:
                                                                                </strong>
                                                                                ${contentMap[locale.id].contentId}
                                                                            </p>
                                                                            <p>
                                                                                <strong>
                                                                                    <fmtSpring:message code="page.label.hash"/>
                                                                                    <c:if test="${isFromSource}">&nbsp;<fmtSpring:message code="page.label.from.source.content"/></c:if>:
                                                                                </strong>
                                                                                ${contentMap[locale.id].sha256Hash}
                                                                            </p>
                                                                        </div>
                                                                    </c:if>

                                                                    <c:if test="${contentObject.isText || contentObject.isMarkup}">
                                                                        <div class="textContentInput">
                                                                            <textarea>
                                                                                <c:out value="${command.contentVO.langContentMap[locale.id].content}"/>
                                                                            </textarea>
                                                                        </div>
                                                                        <div class="encodedContentInput">
                                                                            <textarea>
                                                                                <c:out value="${command.contentVO.langContentMap[locale.id].encodedContent}"/>
                                                                            </textarea>
                                                                        </div>
                                                                    </c:if>
                                                                    <c:if test="${contentObject.isVideoDelivery}">
                                                                        <div class="textContentInput">
                                                                            <textarea>
                                                                                <c:out value="${command.contentVO.langContentMap[locale.id].content}"/>
                                                                            </textarea>
                                                                            <c:if test="${not command.contentVO.langContentMap[locale.id].containsVariables}">
                                                                                <div class="embeddedVideo"><c:out
                                                                                    value="${command.contentVO.langContentMap[locale.id].unformattedText}"
                                                                                    escapeXml="true"/></div>
                                                                            </c:if>
                                                                        </div>
                                                                    </c:if>
                                                                    <c:if test="${contentObject.isGraphic}">
                                                                        <div class="useImageLibrary">
                                                                            <div class="useContentLibraryToggle">
                                                                                <input type="checkbox"
                                                                                       itemName="${contentMap[locale.id].imageLibraryName}" ${contentMap[locale.id].isGlobalImageLibrary ? 'checked="checked"' : ''} />
                                                                            </div>
                                                                            <div class="useLocalContentLibraryToggle">
                                                                                <input type="checkbox"
                                                                                       itemName="${contentMap[locale.id].imageLibraryName}" ${contentMap[locale.id].isLocalImageLibrary ? 'checked="checked"' : ''} />
                                                                            </div>
                                                                        </div>
                                                                        <div class="graphicFileInput"
                                                                             imgPath="${contentMap[locale.id].imageLocation}"
                                                                             imgName="${contentMap[locale.id].imageName}"
                                                                             contentId="${contentMap[locale.id].contentId}"
                                                                             uploadDate="<fmtJSTL:formatDate value="${contentMap[locale.id].imageUploadedDate}" pattern="${dateTimeFormat}"/>">
                                                                        </div>
                                                                        <div class="graphicAppliedImageName">
                                                                            <input value="${contentMap[locale.id].appliedImageFilename}"/>
                                                                        </div>
                                                                        <c:if test="${(contentObject.isMessage && contentObject.zone.applyImageLink) || (!contentObject.isMessage && contentObject.deliveredToWebOrEmailTouchpoint)}">
                                                                            <div class="graphicImageLink">
                                                                                <input class="imageLinkInput"
                                                                                       value="${contentMap[locale.id].imageLinkforView}"/>
                                                                            </div>
                                                                        </c:if>
                                                                        <c:if test="${contentObject.isMessage && contentObject.zone.applyAltText || !contentObject.isMessage}">
                                                                            <div class="graphicImageAltText">
                                                                                <input class="imageAltTextInput"
                                                                                       value="${contentMap[locale.id].imageAltTextforView}"/>
                                                                            </div>
                                                                        </c:if>
                                                                        <c:if test="${(contentObject.isMessage && (contentObject.zone.applyImageExtLink || channelZoneApplyImageExtLink) ) || (!contentObject.isMessage && (contentObject.deliveredToWebOrEmailTouchpoint || contentObject.isOmniChannel))}">
                                                                            <div class="graphicImageExtLink">
                                                                                <input class="imageExtLinkInput"
                                                                                       value="${contentMap[locale.id].imageExtLinkforView}"/>
                                                                            </div>
                                                                        </c:if>
                                                                        <c:if test="${(contentObject.isMessage && contentObject.zone.applyImageExtPath) || (!contentObject.isMessage && (contentObject.deliveredToSefasTouchpoint || contentObject.GMCTouchpoint))}">
                                                                            <div class="graphicImageExtPath">
                                                                                <input class="imageExtPathInput"
                                                                                       value="${contentMap[locale.id].imageExtPathforView}"/>
                                                                            </div>
                                                                        </c:if>
                                                                    </c:if>
                                                                </div>
                                                            </c:forEach>
                                                        </div>
                                                        <c:forEach var="locale" items="${locales}">
                                                            <div class="contentActions"
                                                                 localeId="${locale.id}">
                                                                <select title="${msgpt:getMessage('page.label.PROOF')}"
                                                                        documentId="${document.id}"
                                                                        isTouchpointLocal="${not contentObject.isMessage}"
                                                                        id="proofMenu_${locale.id}"
                                                                        class="inputM style_menu"
                                                                        onchange="infoItemAction(this,2)"
                                                                        style="display: none;">
                                                                    <option id="selectionProofingData_${locale.id}">
                                                                        <fmtSpring:message code="page.label.data"/>
                                                                    </option>
                                                                    <option id="itemRequest_${locale.id}" ${canProofForLanguage[locale.id] ? '' : 'disabled="disabled"'}>
                                                                        <fmtSpring:message code="page.label.request"/>
                                                                        <c:out value=" "/>
                                                                        <c:out value="${fn:length(languages) > 1 ? locale.languageDisplayName : ''}"/>
                                                                        <c:out value="${fn:length(languages) > 1 ? ' ' : ''}"/>
                                                                        <fmtSpring:message code="page.label.proof"/>
                                                                        <!-- c:out value="${ isStatusViewActive && !isMasterSelection ? '(active)' : ' '}"/-->
                                                                    </option>
                                                                    <c:if test="${((!isEnabledForVariantWorkflow && contentObject.focusOnActiveData) || isStatusViewActive) && not empty activeProofs[locale.id] }">
                                                                        <c:forEach var="activeProof"
                                                                                   items="${activeProofs[locale.id]}"
                                                                                   varStatus="proofStat">
                                                                            <c:choose>
                                                                                <c:when test="${not activeProof.complete && not activeProof.error}"> <!-- Proof in process -->
                                                                                    <option id="itemInProcess_${activeProof.id}"
                                                                                            disabled="disabled"
                                                                                            type="inProcess">
                                                                                        (<c:out
                                                                                            value="${activeProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.processing"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${activeProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${activeProof.hasDataError}"> <!-- Invalid proofing data -->
                                                                                    <option id="itemInvalidData_${activeProof.id}"
                                                                                            deliveryId="${currentProof.deliveryEvent.id}"
                                                                                            itemClass="${currentProof.class.name}">
                                                                                        (<c:out
                                                                                            value="${activeProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.invalid.proofing.data"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${activeProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${activeProof.error}"> <!-- Proof error -->
                                                                                    <option id="itemError_${activeProof.id}"
                                                                                            deliveryId="${activeProof.deliveryEvent.id}"
                                                                                            itemClass="${activeProof.class.name}"
                                                                                            style="color: red;">
                                                                                        (<c:out
                                                                                            value="${activeProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.view.log"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${activeProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${not activeProof.error && empty activeProof.outputPath}"> <!-- Proof log -->
                                                                                    <option id="itemLog_${activeProof.id}"
                                                                                            deliveryId="${activeProof.deliveryEvent.id}"
                                                                                            itemClass="${activeProof.class.name}">
                                                                                        (<c:out
                                                                                            value="${activeProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.view.log"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${activeProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:otherwise> <!-- Proof PDF -->
                                                                                    <c:if test="${ not empty activeProof.outputPath }">
                                                                                        <option id="itemPDF_${activeProof.id}"
                                                                                                value="${activeProof.resourceToken}">
                                                                                            (<c:out
                                                                                                value="${activeProof.type==1?'V':'C'}"/>)
                                                                                            <fmtSpring:message
                                                                                                    code="page.label.view"/>
                                                                                            <c:out value=" "/>
                                                                                            <c:out value="${fn:length(languages) > 1 ? locale.languageDisplayName : ''}"/>
                                                                                            <c:out value="${fn:length(languages) > 1 ? ' ' : ''}"/>
                                                                                            <fmtSpring:message
                                                                                                    code="page.label.proof.active"/>
                                                                                            - <fmtJSTL:formatDate
                                                                                                value="${activeProof.requestDate}"
                                                                                                pattern="${dateTimeFormat}"/>
                                                                                        </option>
                                                                                    </c:if>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                        </c:forEach>
                                                                    </c:if>
                                                                    <c:if test="${(!isEnabledForVariantWorkflow && !contentObject.focusOnActiveData) || !isStatusViewActive}">
                                                                        <c:forEach var="currentProof"
                                                                                   items="${proofs[locale.id]}"
                                                                                   varStatus="proofStat">
                                                                            <c:choose>
                                                                                <c:when test="${not currentProof.complete && not currentProof.error}"> <!-- Proof in process -->
                                                                                    <option id="itemInProcess_${currentProof.id}"
                                                                                            disabled="disabled"
                                                                                            type="inProcess">
                                                                                        (<c:out
                                                                                            value="${currentProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.processing"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${currentProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${currentProof.hasDataError}"> <!-- Invalid proofing data -->
                                                                                    <option id="itemInvalidData_${currentProof.id}"
                                                                                            deliveryId="${currentProof.deliveryEvent.id}"
                                                                                            itemClass="${currentProof.class.name}">
                                                                                        (<c:out
                                                                                            value="${currentProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.invalid.proofing.data"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${currentProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${currentProof.error}"> <!-- Proof error -->
                                                                                    <option id="itemError_${currentProof.id}"
                                                                                            deliveryId="${currentProof.deliveryEvent.id}"
                                                                                            itemClass="${currentProof.class.name}"
                                                                                            style="color: red;">
                                                                                        (<c:out
                                                                                            value="${currentProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.view.log"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${currentProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${not currentProof.error && empty currentProof.outputPath}"> <!-- Proof log -->
                                                                                    <option id="itemLog_${currentProof.id}"
                                                                                            deliveryId="${currentProof.deliveryEvent.id}"
                                                                                            itemClass="${currentProof.class.name}">
                                                                                        (<c:out
                                                                                            value="${currentProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.view.log"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${currentProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:otherwise> <!-- Proof PDF -->
                                                                                    <option id="itemPDF_${currentProof.id}"
                                                                                            value="${currentProof.resourceToken}">
                                                                                        (<c:out
                                                                                            value="${currentProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.view.proof"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${currentProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                        <c:if test="${currentProof.staled}">
                                                                                            &nbsp;<fmtSpring:message
                                                                                                code="page.label.stale.brackets"/>
                                                                                        </c:if>
                                                                                    </option>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                        </c:forEach>
                                                                    </c:if>
                                                                </select>
                                                            </div>
                                                        </c:forEach>
                                                    </div>
                                                </c:if> <!-- if:not multipart -->
                                                <!-- MULTIPART Content: Tab by language -->
                                                <c:if test="${ contentObject.multipartType }">
                                                    <msgpt:DataTable labelPosition="top">
                                                        <msgpt:TableItem label="page.label.part">
                                                            <!-- Part Select -->
                                                            <c:set var="zoneId"
                                                                   value="${command.mpContentVO.zoneVO.zoneId}"/>
                                                            <c:set var="zoneParts"
                                                                   value="${command.mpContentVO.zoneVO.parts}"/>
                                                            <div id="partSelectDiv_zone${zoneId}"
                                                                 style="">
                                                                <select id="partSelect_zone${zoneId}"
                                                                        class="style_select"
                                                                        onchange="changePartSelect(this);"
                                                                        style="display: none;">
                                                                    <c:forEach var="currentPart"
                                                                               items="${zoneParts}"
                                                                               varStatus="partsStat">
                                                                        <option id="partOption_zone${zoneId}_part${currentPart.zonePart.id}"
                                                                                value="zone${zoneId}_part${currentPart.zonePart.id}">
                                                                            <c:out value="${currentPart.zonePart.nameInContext}"/></option>
                                                                    </c:forEach>
                                                                </select>
                                                            </div>
                                                        </msgpt:TableItem>
                                                    </msgpt:DataTable>
                                                    <div id="contentData" globalState="${editorState}"
                                                         globalRefObjLabel="${contentSelection.contentObjectAssociation.referencingTouchpointPGTreeNode.name}"
                                                         style="display: none;">
                                                        <c:set var="zoneId"
                                                               value="${command.mpContentVO.zoneVO.zoneId}"/>
                                                        <c:set var="zoneParts"
                                                               value="${command.mpContentVO.zoneVO.parts}"/>

                                                        <c:forEach items="${zoneParts}" var="part"
                                                                   varStatus="partsStat">
                                                            <c:set var="mpVO" value="${zoneParts[partsStat.index]}"/>
                                                            <c:set var="currentPart" value="${part.zonePart}"/>

                                                            <c:set var="groupState" value="edit"/>
                                                            <c:choose>
                                                                <c:when test="${mpVO.emptyZonePart == true}">
                                                                    <c:set var="groupState" value="leaveEmpty"/>
                                                                </c:when>
                                                                <c:when test="${mpVO.emptyZonePart == false}">
                                                                    <c:set var="groupState" value="edit"/>
                                                                </c:when>
                                                            </c:choose>

                                                            <div class="contentGroup"
                                                                 groupId="part${currentPart.id}"
                                                                 groupState="${groupState}"
                                                                 groupRefObjLabel="UNUSED"
                                                                 backgroundColor="${currentPart.backgroundColor}"
                                                                 canvasDimensions="${currentPart.canvasDimensions}"
                                                                 canvasRotation="${currentPart.canvasRotation}"
                                                                 content="${currentPart.contentType.id == textContentTypeID ? 'text' : 'graphic'}">
                                                                <c:forEach var="locale" items="${locales}"
                                                                           varStatus="langStat">
                                                                    <div class="contentEntry"
                                                                         contentState="inherit"
                                                                         localeId="${locale.id}">
                                                                        <c:if test="${isContentObjectTimeStampEnable}">
                                                                            <div class="contentInfo">
                                                                                <c:set var="isFromSource" value="${mpVO.languageContentVOs[locale.id].sameAsDefault || mpVO.languageContentVOs[locale.id].reference}" />

                                                                                <p class="mb-0">
                                                                                    <strong>
                                                                                        <fmtSpring:message code="page.label.content.id"/>
                                                                                        <c:if test="${isFromSource}">&nbsp;<fmtSpring:message code="page.label.from.source.content"/></c:if>:
                                                                                    </strong>
                                                                                        ${mpVO.languageContentVOs[locale.id].contentId}
                                                                                </p>
                                                                                <p>
                                                                                    <strong>
                                                                                        <fmtSpring:message code="page.label.hash"/>
                                                                                        <c:if test="${isFromSource}">&nbsp;<fmtSpring:message code="page.label.from.source.content"/></c:if>:
                                                                                    </strong>
                                                                                        ${mpVO.languageContentVOs[locale.id].sha256Hash}
                                                                                </p>
                                                                            </div>
                                                                        </c:if>
                                                                        <c:if test="${currentPart.contentType.id == textContentTypeID}">
                                                                            <div class="textContentInput">
                                                                            <textarea>
                                                                                <c:out value="${command.mpContentVO.zoneVO.parts[partsStat.index].languageContentVOs[locale.id].content}"/>
                                                                            </textarea>
                                                                            </div>
                                                                            <div class="encodedContentInput">
                                                                            <textarea>
                                                                                <c:out value="${command.mpContentVO.zoneVO.parts[partsStat.index].languageContentVOs[locale.id].encodedContent}"/>
                                                                            </textarea>
                                                                            </div>
                                                                        </c:if>
                                                                        <c:if test="${currentPart.contentType.id == graphicContentTypeID}">
                                                                            <div class="useImageLibrary">
                                                                                <div class="useContentLibraryToggle">
                                                                                    <input type="checkbox"
                                                                                           itemName="${mpVO.languageContentVOs[locale.id].imageLibraryName}" ${mpVO.languageContentVOs[locale.id].isGlobalImageLibrary ? 'checked="checked"' : ''} />
                                                                                </div>
                                                                                <div class="useLocalContentLibraryToggle">
                                                                                    <input type="checkbox"
                                                                                           itemName="${mpVO.languageContentVOs[locale.id].imageLibraryName}" ${mpVO.languageContentVOs[locale.id].isLocalImageLibrary ? 'checked="checked"' : ''} />
                                                                                </div>
                                                                            </div>
                                                                            <div class="graphicFileInput"
                                                                                 imgPath="${mpVO.languageContentVOs[locale.id].imageLocation}"
                                                                                 imgName="${mpVO.languageContentVOs[locale.id].imageName}"
                                                                                 contentId="${mpVO.languageContentVOs[locale.id].contentId}"
                                                                                 uploadDate="<fmtJSTL:formatDate value="${mpVO.languageContentVOs[locale.id].imageUploadedDate}" pattern="${dateTimeFormat}"/>">
                                                                            </div>
                                                                            <div class="graphicAppliedImageName">
                                                                                <input value="${command.mpContentVO.zoneVO.parts[partsStat.index].languageContentVOs[locale.id].appliedImageFilename}"/>
                                                                            </div>
                                                                            <c:if test="${currentPart.zone.applyImageLink}">
                                                                                <div class="graphicImageLink">
                                                                                    <input class="imageLinkInput"
                                                                                           value="${command.mpContentVO.zoneVO.parts[partsStat.index].languageContentVOs[locale.id].imageLinkforView}"/>
                                                                                </div>
                                                                            </c:if>
                                                                            <c:if test="${currentPart.zone.applyAltText}">
                                                                                <div class="graphicImageAltText">
                                                                                    <input class="imageAltTextInput"
                                                                                           value="${command.mpContentVO.zoneVO.parts[partsStat.index].languageContentVOs[locale.id].imageAltTextforView}"/>
                                                                                </div>
                                                                            </c:if>
                                                                            <c:if test="${currentPart.zone.applyImageExtLink || channelZoneApplyImageExtLink}">
                                                                                <div class="graphicImageExtLink">
                                                                                    <input class="imageExtLinkInput"
                                                                                           value="${command.mpContentVO.zoneVO.parts[partsStat.index].languageContentVOs[locale.id].imageExtLinkforView}"/>
                                                                                </div>
                                                                            </c:if>
                                                                            <c:if test="${currentPart.zone.applyImageExtPath}">
                                                                                <div class="graphicImageExtPath">
                                                                                    <input class="imageExtPathInput"
                                                                                           value="${command.mpContentVO.zoneVO.parts[partsStat.index].languageContentVOs[locale.id].imageExtPathforView}"/>
                                                                                </div>
                                                                            </c:if>
                                                                        </c:if>
                                                                    </div>
                                                                </c:forEach>
                                                            </div>
                                                        </c:forEach>
                                                        <c:forEach var="locale" items="${locales}">
                                                            <div class="contentActions"
                                                                 localeId="${locale.id}">
                                                                <select title="${msgpt:getMessage('page.label.PROOF')}"
                                                                        documentId="${document.id}"
                                                                        isTouchpointLocal="${not contentObject.isMessage}"
                                                                        id="proofMenu_${locale.id}"
                                                                        class="inputM style_menu"
                                                                        onchange="infoItemAction(this,2)"
                                                                        style="display: none;">
                                                                    <option id="selectionProofingData_${locale.id}">
                                                                        <fmtSpring:message code="page.label.data"/>
                                                                    </option>
                                                                    <option id="itemRequest_${locale.id}" ${canProofForLanguage[locale.id] ? '' : 'disabled="disabled"'}>
                                                                        <fmtSpring:message code="page.label.request"/>
                                                                        <c:out value=" "/>
                                                                        <c:out value="${fn:length(languages) > 1 ? locale.languageDisplayName : ''}"/>
                                                                        <c:out value="${fn:length(languages) > 1 ? ' ' : ''}"/>
                                                                        <fmtSpring:message code="page.label.proof"/>
                                                                        <!-- c:out value="${ isStatusViewActive && !isMasterSelection ? '(active)' : ' '}"/-->
                                                                    </option>
                                                                    <c:if test="${((!isEnabledForVariantWorkflow && contentObject.focusOnActiveData) || isStatusViewActive) && not empty activeProofs[locale.id] }">
                                                                        <c:forEach var="activeProof"
                                                                                   items="${activeProofs[locale.id]}"
                                                                                   varStatus="proofStat">
                                                                            <c:choose>
                                                                                <c:when test="${not activeProof.complete && not activeProof.error}"> <!-- Proof in process -->
                                                                                    <option id="itemInProcess_${activeProof.id}"
                                                                                            disabled="disabled"
                                                                                            type="inProcess">
                                                                                        (<c:out
                                                                                            value="${activeProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.processing"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${activeProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${activeProof.hasDataError}"> <!-- Invalid proofing data -->
                                                                                    <option id="itemInvalidData_${activeProof.id}"
                                                                                            deliveryId="${currentProof.deliveryEvent.id}"
                                                                                            itemClass="${currentProof.class.name}">
                                                                                        (<c:out
                                                                                            value="${activeProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.invalid.proofing.data"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${activeProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${activeProof.error}"> <!-- Proof error -->
                                                                                    <option id="itemError_${activeProof.id}"
                                                                                            deliveryId="${activeProof.deliveryEvent.id}"
                                                                                            itemClass="${activeProof.class.name}"
                                                                                            style="color: red;">
                                                                                        (<c:out
                                                                                            value="${activeProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.view.log"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${activeProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${not activeProof.error && empty activeProof.outputPath}"> <!-- Proof log -->
                                                                                    <option id="itemLog_${activeProof.id}"
                                                                                            deliveryId="${activeProof.deliveryEvent.id}"
                                                                                            itemClass="${activeProof.class.name}">
                                                                                        (<c:out
                                                                                            value="${activeProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.view.log"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${activeProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:otherwise> <!-- Proof PDF -->
                                                                                    <c:if test="${ not empty activeProof.outputPath }">
                                                                                        <option id="itemPDF_${activeProof.id}"
                                                                                                value="${activeProof.resourceToken}">
                                                                                            (<c:out
                                                                                                value="${activeProof.type==1?'V':'C'}"/>)
                                                                                            <fmtSpring:message
                                                                                                    code="page.label.view"/>
                                                                                            <c:out value=" "/>
                                                                                            <c:out value="${fn:length(languages) > 1 ? locale.languageDisplayName : ''}"/>
                                                                                            <c:out value="${fn:length(languages) > 1 ? ' ' : ''}"/>
                                                                                            <fmtSpring:message
                                                                                                    code="page.label.proof.active"/>
                                                                                            - <fmtJSTL:formatDate
                                                                                                value="${activeProof.requestDate}"
                                                                                                pattern="${dateTimeFormat}"/>
                                                                                        </option>
                                                                                    </c:if>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                        </c:forEach>
                                                                    </c:if>
                                                                    <c:if test="${(!isEnabledForVariantWorkflow && !contentObject.focusOnActiveData) || !isStatusViewActive}">
                                                                        <c:forEach var="currentProof"
                                                                                   items="${proofs[locale.id]}"
                                                                                   varStatus="proofStat">
                                                                            <c:choose>
                                                                                <c:when test="${not currentProof.complete && not currentProof.error}"> <!-- Proof in process -->
                                                                                    <option id="itemInProcess_${currentProof.id}"
                                                                                            disabled="disabled"
                                                                                            type="inProcess">
                                                                                        (<c:out
                                                                                            value="${currentProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.processing"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${currentProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${currentProof.hasDataError}"> <!-- Invalid proofing data -->
                                                                                    <option id="itemInvalidData_${currentProof.id}"
                                                                                            deliveryId="${currentProof.deliveryEvent.id}"
                                                                                            itemClass="${currentProof.class.name}">
                                                                                        (<c:out
                                                                                            value="${currentProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.invalid.proofing.data"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${currentProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${currentProof.error}"> <!-- Proof error -->
                                                                                    <option id="itemError_${currentProof.id}"
                                                                                            deliveryId="${currentProof.deliveryEvent.id}"
                                                                                            itemClass="${currentProof.class.name}"
                                                                                            style="color: red;">
                                                                                        (<c:out
                                                                                            value="${currentProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.view.log"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${currentProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:when test="${not currentProof.error && empty currentProof.outputPath}"> <!-- Proof log -->
                                                                                    <option id="itemLog_${currentProof.id}"
                                                                                            deliveryId="${currentProof.deliveryEvent.id}"
                                                                                            itemClass="${currentProof.class.name}">
                                                                                        (<c:out
                                                                                            value="${currentProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.view.log"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${currentProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                    </option>
                                                                                </c:when>
                                                                                <c:otherwise> <!-- Proof PDF -->
                                                                                    <option id="itemPDF_${currentProof.id}"
                                                                                            value="${currentProof.resourceToken}">
                                                                                        (<c:out
                                                                                            value="${currentProof.type==1?'V':'C'}"/>)
                                                                                        <fmtSpring:message
                                                                                                code="page.label.view"/>
                                                                                        <c:out value=" "/>
                                                                                        <c:out value="${fn:length(languages) > 1 ? locale.languageDisplayName : ''}"/>
                                                                                        <c:out value="${fn:length(languages) > 1 ? ' ' : ''}"/>
                                                                                        <fmtSpring:message
                                                                                                code="page.label.proof"/>
                                                                                        - <fmtJSTL:formatDate
                                                                                            value="${currentProof.requestDate}"
                                                                                            pattern="${dateTimeFormat}"/>
                                                                                        <c:if test="${currentProof.staled}">
                                                                                            &nbsp;<fmtSpring:message
                                                                                                code="page.label.stale.brackets"/>
                                                                                        </c:if>
                                                                                    </option>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                        </c:forEach>
                                                                    </c:if>
                                                                </select>
                                                            </div>
                                                        </c:forEach>
                                                    </div>
                                                </c:if> <!-- if:multipart -->
                                            </div>
                                        </section>
                                        <c:if test="${contentObject.isMessage || !contentObject.contentType.graphic}">
                                            <section class="mt-5">
                                                <h2 class="h4 d-flex align-items-baseline pb-3 mb-4 border-bottom border-thick font-weight-normal">
                                                    <span class="mr-auto">
                                                        <fmtSpring:message code="page.label.targeting"/>
                                                    </span>
                                                    <span class="fs-sm text-uppercase text-muted font-weight-bold">
                                                        (<fmtSpring:message code="page.text.switch.to.master.to.edit"/>)
                                                    </span>
                                                </h2>
                                                <jsp:include page="../content/content_object_view_targeting.jsp" />
                                            </section>
                                        </c:if>
                                        <c:if test="${not empty command.formWrapper}">
                                            <section class="mt-5">
                                                <h2 class="h4 d-flex align-items-baseline pb-3 mb-4 border-bottom border-thick font-weight-normal">
                                                    <span class="mr-auto">
                                                        <fmtSpring:message code="page.label.metadata"/>
                                                    </span>
                                                    <span class="fs-sm text-uppercase text-muted font-weight-bold">
                                                        (<fmtSpring:message code="page.text.switch.to.master.to.edit"/>)
                                                    </span>
                                                </h2>
                                                <jsp:include page="../metadata/metadata_form_view.jsp"/>
                                            </section>
                                        </c:if>
                                        <section class="mt-5">
                                            <h2 class="h4 d-flex align-items-baseline pb-3 mb-4 border-bottom border-thick font-weight-normal">
                                                <span class="mr-auto">
                                                    <fmtSpring:message code="page.label.details"/>
                                                </span>
                                                <span class="fs-sm text-uppercase text-muted font-weight-bold">
                                                    (<fmtSpring:message code="page.text.switch.to.master.to.edit"/>)
                                                </span>
                                            </h2>
                                            <dl class="row no-gutters mb-0">
                                                <c:if test="${contentObject.dynamicVariantEnabled && (not empty isOwningTouchpointSelection && !isOwningTouchpointSelection)}">
                                                    <div class="col-lg-4 pb-1">
                                                        <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                            <fmtSpring:message code="page.label.shared.from"/>
                                                        </dt>
                                                        <dd class="pr-4 py-2">
                                                            <c:out value="${contentObject.owningTouchpointSelection.name}"/>
                                                            <c:if test="${contentObject.accessRestricted}">
                                                                [<fmtSpring:message code="page.text.restricted.access"/>]
                                                            </c:if>
                                                        </dd>
                                                    </div>
                                                </c:if>
                                                <c:if test="${contentObject.isTouchpointLocal && contentObject.zone != null}">
                                                    <div class="col-lg-4 pb-1">
                                                        <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                            <fmtSpring:message code="page.label.placeholder"/>
                                                        </dt>
                                                        <dd class="pr-4 py-2">
                                                            <c:out value="${contentObject.zone.friendlyName}"/>
                                                        </dd>
                                                    </div>
                                                </c:if>
                                                <c:if test="${contentObject.isMessage}">
                                                    <div class="col-lg-auto flex-lg-grow-1 pb-1">
                                                        <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                            <fmtSpring:message code="page.label.delivered.to"/>
                                                        </dt>
                                                        <dd class="pr-4 py-2">
                                                            <div id="deliveryNavTree" class="d-flex flex-wrap m-n1">
                                                                <c:out value="${contentObject.deliveryNavTreeHTML}"
                                                                       escapeXml="false"/>
                                                            </div>
                                                        </dd>
                                                    </div>
                                                    <div class="col-lg-4 pb-1">
                                                        <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                            <fmtSpring:message code="page.label.Delivery"/>
                                                        </dt>
                                                        <dd class="pr-4 py-2">
                                                            <c:out value="${contentObject.deliveryType == 1 ? (msgpt:getMessage('page.label.mandatory')) : (msgpt:getMessage('page.label.optional'))}"/>
                                                        </dd>
                                                    </div>
                                                </c:if>
                                                <c:if test="${contentObject.isOmniChannel && not empty contentObject.channelContextId}">
                                                    <div class="col-lg-4 pb-1">
                                                        <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                            <fmtSpring:message code="page.label.channel.restriction"/>
                                                        </dt>
                                                        <dd class="pr-4 py-2">
                                                            <c:out value="${contentObject.channelContextDisplay}"/>
                                                        </dd>
                                                    </div>
                                                </c:if>
                                                <c:if test="${not contentObject.isGlobalImage}">
                                                    <div class="col-lg-4 pb-1">
                                                        <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                            <fmtSpring:message code="page.label.timing"/>
                                                        </dt>
                                                        <dd class="pr-4 py-2">
                                                            <c:if test="${contentObject.hasTiming}">
                                                                <c:choose>
                                                                    <c:when test="${not empty contentObject.startDate && not empty contentObject.endDate}">
                                                                        <fmtJSTL:formatDate
                                                                                value="${contentObject.startDate}"
                                                                                pattern="${dateTimeFormat}"/>&nbsp;~&nbsp;
                                                                        <fmtJSTL:formatDate
                                                                                value="${contentObject.endDate}"
                                                                                pattern="${dateTimeFormat}"/>
                                                                    </c:when>
                                                                    <c:when test="${not empty contentObject.endDate}">
                                                                        <fmtSpring:message code="page.text.ending"/>
                                                                        <fmtJSTL:formatDate
                                                                                value="${contentObject.endDate}"
                                                                                pattern="${dateTimeFormat}"/>
                                                                    </c:when>
                                                                    <c:when test="${not empty contentObject.startDate}">
                                                                        <fmtSpring:message code="page.text.starting"/>
                                                                        <fmtJSTL:formatDate
                                                                                value="${contentObject.startDate}"
                                                                                pattern="${dateTimeFormat}"/>
                                                                    </c:when>
                                                                </c:choose>
                                                                <c:if test="${contentObject.startDate != null && contentObject.repeatDatesAnnually}">
                                                                    <fmtSpring:message
                                                                            code="page.text.repeats.annually.brackets"/>
                                                                </c:if>
                                                            </c:if>
                                                            <c:if test="${not contentObject.hasTiming}">
                                                                <fmtSpring:message code="page.text.no.restrictions"/>
                                                            </c:if>
                                                        </dd>
                                                    </div>
                                                </c:if>
                                                <msgpt:IfAuthGranted authority="ROLE_METATAGS_VIEW">
                                                    <div class="col-lg-4 pb-1">
                                                        <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                            <fmtSpring:message code="page.label.metatags"/>
                                                        </dt>
                                                        <dd class="pr-4 py-2">
                                                            <c:choose>
                                                                <c:when test="${not empty contentObject.metatags}">
                                                                    <c:out value='${contentObject.metatags}'/>&nbsp;
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <fmtSpring:message code="page.label.none"/>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </dd>
                                                    </div>
                                                </msgpt:IfAuthGranted>
                                                <div class="col-lg-4 pb-1">
                                                    <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                        <fmtSpring:message code="page.label.next.action"/>
                                                    </dt>
                                                    <dd class="pr-4 py-2">
                                                        <c:out value="${contentObject.actionRequired}"/>
                                                    </dd>
                                                </div>
                                                <div class="col-lg-4 pb-1">
                                                    <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                        <fmtSpring:message code="page.label.description"/>
                                                    </dt>
                                                    <dd class="pr-4 py-2">
                                                        <c:choose>
                                                            <c:when test="${not empty contentObject.description}">
                                                                <c:out value='${contentObject.description}'/>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <fmtSpring:message code="page.label.none"/>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </dd>
                                                </div>
                                                <div class="col-lg-4 pb-1">
                                                    <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                        <fmtSpring:message code="page.label.assigned.to"/>
                                                    </dt>
                                                    <dd class="pr-4 py-2">
                                                        <c:choose>
                                                            <c:when test="${contentObject.focusOnActiveData}">
                                                                <fmtSpring:message code="page.label.na"/>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <i class="far fa-user mr-1" aria-hidden="true"></i>
                                                                <c:out value="${contentObject.assignedToUserName}"/>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </dd>
                                                </div>
                                                <c:if test="${not contentObject.isMarkup && not contentObject.isMessage}">
                                                    <div class="col-lg-4 pb-1">
                                                        <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                            <fmtSpring:message code="page.label.insert.as.paragraph"/>
                                                        </dt>
                                                        <dd class="pr-4 py-2">
                                                            <c:choose>
                                                                <c:when test="${contentObject.insertAsBlockContent}">
                                                                    <fmtSpring:message code="page.label.yes"/>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <fmtSpring:message code="page.label.no"/>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </dd>
                                                    </div>
                                                </c:if>
                                                <c:if test="${isOmniChannel}">
                                                    <div class="col-lg-4 pb-1">
                                                        <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                            <fmtSpring:message code="page.label.channel.restriction"/>
                                                        </dt>
                                                        <dd class="pr-4 py-2">
                                                            <c:out value="${contentObject.channelContextDisplay}"/>
                                                        </dd>
                                                    </div>
                                                </c:if>
                                                <c:if test="${contentObject.isMessage && (contentObject.zone.isFooter || contentObject.zone.isHeader)}">
                                                    <div class="col-lg-4 pb-1">
                                                        <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                            <c:out value="${contentObject.zone.isFooter ? msgpt:getMessage('page.label.footer') : msgpt:getMessage('page.label.header')} ${msgpt:getMessage('page.label.flow.type')}"/>
                                                        </dt>
                                                        <dd class="pr-4 py-2">
                                                            <c:out value="${contentObject.flowTypeDisplay}"/>
                                                        </dd>
                                                    </div>
                                                </c:if>
                                                <c:if test="${(contentObject.isTouchpointLocal || contentObject.isMixedDataGroups) && not empty contentObject.dataGroup}">
                                                    <div class="col-lg-4 pb-1">
                                                        <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                            <fmtSpring:message code="page.label.data.group"/>
                                                        </dt>
                                                        <dd class="pr-4 py-2">
                                                            <c:out value="${contentObject.dataGroup.name}"/>
                                                        </dd>
                                                    </div>
                                                </c:if>
                                                <c:if test="${touchpointContext.nativeCompositionTouchpoint}">
                                                    <div class="col-lg-4 pb-1">
                                                        <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                            <fmtSpring:message code="page.label.keep.together"/>
                                                        </dt>
                                                        <dd class="pr-4 py-2">
                                                            <c:choose>
                                                                <c:when test="${contentObject.keepContentTogether}">
                                                                    <fmtSpring:message code="page.label.yes"/>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <fmtSpring:message code="page.label.no"/>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </dd>
                                                    </div>
                                                </c:if>
                                                <div class="col-lg-auto flex-lg-grow-1 pb-1">
                                                    <dt class="pr-4 pb-1 border-bottom text-uppercase text-muted font-weight-normal fs-xs">
                                                        <fmtSpring:message
                                                                code="page.label.last.message.workflow.action"/>
                                                    </dt>
                                                    <dd class="pr-4 py-2">
                                                        <c:choose>
                                                            <c:when test="${empty lastWorkflowAction}">
                                                                <fmtSpring:message code="page.label.none"/>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <c:out value="${lastWorkflowAction.historicalCurrentStepName}"/>&nbsp; | &nbsp;<c:out
                                                                    value="${lastWorkflowAction.user}"/>&nbsp; | &nbsp;<fmtJSTL:formatDate
                                                                    value="${lastWorkflowAction.actionDate}"
                                                                    pattern="${dateTimeFormat}"/>
                                                                <div>
                                                                    <fmtSpring:message code="page.label.note"/>:
                                                                    <c:choose>
                                                                        <c:when test="${empty lastWorkflowAction.notes}">
                                                                            <fmtSpring:message
                                                                                    code="page.label.none"/>
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            <c:out value="${lastWorkflowAction.notes}"/>
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                </div>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </dd>
                                                </div>
                                            </dl>
                                        </section>
                                        <c:if test="${not empty contentObject.comments}">
                                            <section class="mt-5">
                                                <h2 class="h4 d-flex align-items-baseline pb-3 mb-4 border-bottom border-thick font-weight-normal">
                                                    <span class="mr-auto">
                                                        <fmtSpring:message code="page.label.comment.history"/>
                                                    </span>
                                                    <span class="fs-sm text-uppercase text-muted font-weight-bold">
                                                        (<fmtSpring:message code="page.text.switch.to.master.to.edit"/>)
                                                    </span>
                                                </h2>
                                                <div class="pt-1">
                                                    <c:forEach var="comment"
                                                               items="${contentObject.commentsOrdered}"
                                                               varStatus="status">
                                                        <blockquote class="blockquote">
                                                            <p class="blockquote-body">
                                                                <span class="blockquote-text">
                                                                    <c:out value="${comment.comment}"/>
                                                                </span>
                                                            </p>
                                                            <footer class="blockquote-footer">
                                                                <span class="mr-4">
                                                                    <fmtJSTL:formatDate value="${comment.created}"
                                                                                        pattern="${dateTimeFormat}"/>
                                                                    <span class="fs-md ml-2">
                                                                        <i class="far fa-clock"
                                                                           aria-hidden="true"></i>
                                                                    </span>
                                                                </span>
                                                                <cite title="${comment.user.fullName}">
                                                                    <c:out value="${comment.user.fullName}"/>
                                                                    <span class="fs-md ml-2">
                                                                        <i class="far fa-user" aria-hidden="true"></i>
                                                                    </span>
                                                                </cite>
                                                            </footer>
                                                        </blockquote>
                                                    </c:forEach>
                                                </div>
                                            </section>
                                        </c:if>
                                    </div>
                                </div>
                                <div>
                                    <!-- POPUP DATA -->
                                    <div id="actionSpecs" style="display: none;">
                                        <!-- ACTIONS POPUP DATA -->
                                        <div id="actionSpec_7" contentWidth="425px"> <!-- View data values -->
                                            <div id="actionTitle_7"><fmtSpring:message
                                                    code="page.label.view.selection.data"/></div>
                                            <div id="actionViewDataValues_7"></div>
                                        </div>
                                    </div>
                                    <!-- POPUP INTERFACE -->
                                    <msgpt:Popup id="actionPopup">
                                        <div id="actionPopupInfoFrame">
                                            <div id="actionPopupInfo">&nbsp;</div>
                                        </div>
                                        <div id="actionPopupViewDataValues" style="padding: 15px;" align="center">

                                            <input type="hidden" id="viewDataValueTreeDiv_dataValueCollection"
                                                   value="${touchpointSelection.parameterGroupTreeNode.parameterGroupInstanceCollection.id}"/>
                                            <msgpt:CollectionDataValuesTree id="viewDataValueTreeDiv"
                                                                            style="overflow: auto; width: 375px; text-align: left; height: 150px; padding-left: 0px; border: 1px solid #848484; overflow-y: auto;"/>

                                        </div>
                                        <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                            <span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                              label="page.label.cancel"
                                                                                              disabled="true"/></span>
                                            <span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                                                      label="page.label.cancel"/></span>
                                            <span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                                label="page.label.continue"
                                                                                                                disabled="true"/></span>
                                            <span id="continueBtnEnabled"><msgpt:Button URL="#"
                                                                                        label="page.label.continue"
                                                                                        primary="true"/></span>
                                        </div>
                                        <div id="actionPopupCloseButton" class="actionPopupButtonsContainer">
                                            <msgpt:Button URL="javascript:actionCancel();" label="page.label.close"/>
                                        </div>
                                    </msgpt:Popup>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form:form>
        </div>
    </msgpt:BodyNew>
</msgpt:Html5>