<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.EDIT %>">


	<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />
    <msgpt:Script>
        <script>
            var showAllLocale = false;
            var showAllLanguage = false;

            function getParam( name )
            {
                var regexS = "[\\?&]"+name+"=([^&#]*)";
                var regex = new RegExp( regexS );
                var tmpURL = window.location.href;
                var results = regex.exec( tmpURL );
                if( results == null )
                    return "";
                else
                    return results[1];
            }

            function languageSelection(refresh, langOption, langRefresh) {
                var isNoFavouriteOptions = false;

                if (langRefresh)
                {
                    if (langOption == null)
                    {
                        isNoFavouriteOptions = false;
                        $('#languageSelect option').remove();
                        $('#languageOptions option').each(function(){
                            if ( showAllLanguage || $(this).attr('favourite') == "true" || $(this).attr('edited') == "true")
                            {
                                var currentOption = $(this).clone();
                                $('#languageSelect').append(currentOption);
                            } else {
                                isNoFavouriteOptions = true;
                            }
                        });
                        if ( !showAllLanguage && isNoFavouriteOptions)
                        {
                            $('#languageSelect').append('<option value="999">' + client_messages.text.show_all + '</option>');
                        }
                    }
                    else {
                        var value = $(langOption).find(':selected').val();
                        if (value == '999')
                        {
                            showAllLanguage = true;
                            languageSelection(true, null, true);
                        }
                    }
                }

                var previousLanguageId = $('#localeSelect option:first').attr('languageId');
                var currentLanguageId = $('#languageSelect').val();

                if (currentLanguageId != previousLanguageId || refresh)
                {
                    isNoFavouriteOptions = false;
                    $('#localeSelect option').remove();
                    $('#localeOptions option').each(function(){
                        if ( $(this).attr('languageId') == currentLanguageId ) {
                            if ( showAllLocale || $(this).attr('favourite') == "true" || $(this).attr('edited') == "true")
                            {
                                var currentOption = $(this).clone();
                                $('#localeSelect').append(currentOption);
                            } else {
                                isNoFavouriteOptions = true;
                            }
                        }
                    });
                    if ( !showAllLocale && isNoFavouriteOptions)
                    {
                        $('#localeSelect').append('<option value="999">' + client_messages.text.show_all + '</option>');
                    }
                }

            }

            function localeSelection(submitItem) {
                var value = $(submitItem).find(':selected').val();
                if (value == '999')
                {
                    showAllLocale = true;
                    languageSelection(true, null, false);
                }
            }

            $( function() {
                languageSelection(true, null, true);
            });

        </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:BodyNew theme="minimal" type="iframe">

	<form:form modelAttribute="command">

		<msgpt:iFrameContainer>
			<msgpt:ContentPanel>
				<form:errors path="*">
					<msgpt:Information errorMsgs="${messages}" type="error" />
				</form:errors>

				<msgpt:FormLayout labelWidth="80px" inputWidth="160px">

					<msgpt:FormField label="page.label.language">
						<form:select id="languageSelect" path="messagepointLocale.languageId" onchange="languageSelection(false, this, true)" disabled="${!isNewLanguage && (hasMessageDeliveries || hasCommunications)}" cssClass="inputL">
							<c:forEach var="locale" items="${languages}">
								<option value="${locale.languageId}" favourite="${locale.languageFavourite}" ${command.messagepointLocale.languageId == locale.languageId ? 'edited="true" selected="selected"' : ''}>
									<c:out value="${locale.languageDisplayName}"/>
								</option>
							</c:forEach>
						</form:select>
					</msgpt:FormField>

					<msgpt:FormField label="page.label.locale">
						<form:select id="localeSelect" path="messagepointLocale.id" onchange="localeSelection(this)" disabled="${!isNewLanguage && (hasMessageDeliveries || hasCommunications)}" cssClass="inputL">
							<c:forEach var="locale" items="${locales}">
								<c:if test="${command.messagepointLocale.languageId == locale.languageId}">
									<option value="${locale.id}" languageId="${locale.languageId}" ${(command.messagepointLocale.id == 0 && locale.defaultLocale) || command.messagepointLocale.id == locale.id ? 'edited="true" selected="selected"' : ''}>
										<c:out value="${locale.name}"/>
									</option>
								</c:if>
							</c:forEach>
						</form:select>

						<select id="languageOptions" style="display:none;">
							<c:forEach var="locale" items="${languages}">
								<option value="${locale.languageId}" favourite="${locale.languageFavourite}" ${command.messagepointLocale.languageId == locale.languageId ? 'edited="true" selected="selected"' : ''}>
									<c:out value="${locale.languageDisplayName}"/>
								</option>
							</c:forEach>
						</select>

						<select id="localeOptions" style="display:none;">
							<c:forEach var="locale" items="${locales}">
								<option value="${locale.id}" languageId="${locale.languageId}" favourite="${locale.favourite}" ${(command.messagepointLocale.id == 0 && locale.defaultLocale) || command.messagepointLocale.id == locale.id ? 'edited="true" selected="selected"' : ''}>
									<c:out value="${locale.name}"/>
								</option>
							</c:forEach>
						</select>
					</msgpt:FormField>

				</msgpt:FormLayout>
				<c:if test="${!isNewLanguage && (hasMessageDeliveries || hasCommunications)}">
					<div class="alert alert-info" role="alert">
						<strong class="mr-2">
							<i class="fas fa-info-circle fa-lg mr-2"
							   aria-hidden="true"></i><fmtSpring:message
								code="page.label.info"/>:
						</strong>
						<fmtSpring:message code="page.label.local.info"/>
					</div>
				</c:if>
				<msgpt:FlowLayout align="center">
					<msgpt:FlowLayoutItem>
						<msgpt:Button label="page.label.cancel" URL="#" flowControl="true"/>
					</msgpt:FlowLayoutItem>
					<msgpt:FlowLayoutItem>
						<msgpt:Button label="page.label.save" URL="javascript:document.getElementById('command').submit();" flowControl="true" icon="fa-save"  disabled="${!isNewLanguage && (hasMessageDeliveries || hasCommunications)}" />
					</msgpt:FlowLayoutItem>
				</msgpt:FlowLayout>
			</msgpt:ContentPanel>
		</msgpt:iFrameContainer>

	</form:form>

</msgpt:BodyNew>
</msgpt:Html5>