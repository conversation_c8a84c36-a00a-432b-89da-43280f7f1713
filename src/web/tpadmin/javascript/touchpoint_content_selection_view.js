function toggleLoadingIndicator(frame) {
    $('#loadingIndicator_' + $(frame).attr('id')).hide();
}

function toogleSearchDisplay() {
    if( $('#searchInputs').is(':visible') ) {
        $('#searchExpansionArrow').attr('class','expansionArrow_closed');
        $('#searchInputs').hide();
    } else {
        $('#searchInputs').showEle('normal');
        $('#searchExpansionArrow').attr('class','expansionArrow_open');
    }
}

function contentHistoryPopup(){
    var zonePartId = -1;
    var localeId = -1;
    var $partSelect = $("select[id^='partSelect_zone']");
    var $contentLanguageSelect = $("#contentLanguageSelectcontentData");
    if($partSelect.length){
        var zonePartValue = $partSelect.val();
        if (zonePartValue) {
            var zoneIdMatch = zonePartValue.match(/zone(\d+)/);
            var zonePartIdMatch = zonePartValue.match(/part(\d+)/);
            if (zoneIdMatch && zonePartIdMatch) {
                zoneId = zoneIdMatch[1];
                zonePartId = zonePartIdMatch[1];
            }
        }
    }
    if($contentLanguageSelect.length) {
        localeId = $contentLanguageSelect.val();
    }

    $('#contentHistoryBtn').iFramePopup({
        width			: 800,
        vertOffset		: -25,
        id				: "contentHistoryFrame",
        displayOnInit	: true,
        title			: client_messages.title.content_history,
        src				: context + "/content/content_history.form",
        appliedParams	: {zoneId : -1, tk : getParam('tk'), variantId : jsp_touchpointSelection_parameterGroupTreeNode_id, modelId : jsp_contentObjectId, zonePartId : zonePartId, localeId : localeId},
        closeBtnId		: "cancelBtn_button",
        screenMask		: false,
        draggable		: false,
        onSave			: function() {return true;}
    });
}

function variantContentComparePopup() {
    $('#variantContentCompareBtn').iFramePopup({
        width			: 800,
        vertOffset		: -50,
        id				: "contentCompareFrame",
        displayOnInit	: true,
        title			: client_messages.title.content_compare,

        src				: context + "/content/variant_content_compare.form",
        appliedParams	: {zoneId : -1, tk : getParam('tk'), variantId : jsp_touchpointSelection_parameterGroupTreeNode_id, modelId : jsp_contentObjectId, zonePartId : -1},
        closeBtnId		: "cancelBtn_button",
        screenMask		: false,
        draggable		: false,
        onSave			: function() {return true;}
    });
}

// *************** DATA VALUE INIT FUNCTIONS START ***************
function nodeDataValuesInit() {
    if ( !$('#viewDataValueTreeDiv').hasClass('treeInit') )
        initDataValues();
    $(specId).find('#actionPopupStandardButtons').hide();
    $(specId).find('#actionPopupCloseButton').show();
    $(specId).find('#actionPopupViewDataValues').show();
}

function initDataValues() {
    var dataValueCollectionId = $('#viewDataValueTreeDiv').attr('dataValueCollection');
    $('#viewDataValueTree').hide();
    $('#removeDataValueTree').hide();
    initViewDataValuesTree();
}

function initViewDataValuesTree() {

    var dataValueCollectionId = $('#viewDataValueTreeDiv_dataValueCollection').val();
    common.collectionDataValuesTreeInit('viewDataValueTreeDiv', dataValueCollectionId);
    $('#viewDataValueTreeDiv').addClass('treeInit');
    $('#viewDataValueTree').fadeIn('normal');

}
// *************** DATA VALUE INIT FUNCTIONS END ***************

// *************** MULTIPART FUNCTIONS START ***************
function partClickedAction(part) {
    // Prevent part selection in other multipart zones (TP Sel is single delivery)
    if ($(part).closest('.zoneSelectedClass').length == 0)
        return;

    var partId = $(part).attr('id');
    var partName = $('#partNameRef_'+partId).val();
    var zoneId = partId.split('_')[0];

    partName = $('#partSelect_' + zoneId + ' option:selected').html();

    $('.zonePartSelected').removeClass('zonePartSelected');
    $(part).addClass('zonePartSelected');

    $(part).siblings().css('z-index','0');
    $(part).css('z-index','1');

    $('#partSelect_'+zoneId).selectOption('partOption_'+partId);

    $('#contentData').changeContentGroup( partId.split('_')[1] );

    var partName2 = $('div.actionSelectMenuText.style_select.selectedOption').text();

    if (partName != partName2)
        $('#currentPartName').html(partName2);
    else
        $('#currentPartName').html(partName);
}
// *************** MULTIPART FUNCTIONS END ***************

function changePartSelect(partSelect) {
    var partId = $(partSelect).find(':selected').val();
    $('#'+partId).click();
    common.refreshParentIframeHeight();
}

$( function() {
    $("input:button").styleActionElement();
    $(".style_select").styleActionElement();
    
	$('#taskIndicator').taskManager({
		item_type	: (jsp_is_touchpoint_local ? 6 : 2),
		item_id		: jsp_contentObjectId,
        popup_location: 'right'
	});

    $("#contentData").contentEditor({
        text_data				: jsp_text_data,
        paragraph_data			: jsp_paragraph_data,
        charCountEnabled		: jsp_charCountEnabled,
        contextPath				: jsp_contextPath,
        defaultLocaleId 	    : jsp_defaultLocaleId,
        focusLocaleId		    : jsp_focusLocaleId,
        languages 				: jsp_languages,
        contentObjectId			: jsp_contentObjectId,
        applies_freeform		: jsp_applies_freeform,
        applies_canvas_dimension: jsp_applies_canvas_dimension,
		zone_rotation			: jsp_zone_rotation,
        applies_local_fast_edit	: true,
        size					: jsp_size,
        statusViewId            : jsp_statusViewId,
        usage					: 'view'
    });

    $("[id*='itemInProcess']").actionStatusPolling({
        itemLabel 	: client_messages.text.proof,
        type		: 'proof',
        postItemInit: function(item) {
            var menuEleId = $(item).closest('select').attr('id') + '_menuTable';
            if ( $('#'+menuEleId).find("[id*='itemInProcess']").length == 0 )
                $('#'+menuEleId).find('.menuItemInProcessIcon,.menuItemInProcessSpacer').remove();
        },
        onInit		: function(o) {
            if ( $(o.option).closest('.actionBtnTable').find('.actionBtnText').find('.menuItemInProcessIcon').length == 0 )
                $(o.option).closest('.actionBtnTable').find('.actionBtnText').prepend("<div class=\"menuItemInProcessIcon\" style=\"display: inline-block; position: absolute;\"></div><div class=\"menuItemInProcessSpacer\" style=\"display: inline-block; width: 22px; height: 1px;\"></div>");
        }
    });

    $("[id^='menuOption_selectionProofingData_']").each(function(){
        var localeId = this.id.replace('menuOption_selectionProofingData_','');
        $(this).iFramePopup({
            width			: 500,
            src				: context+"/tpadmin/selection_proofing_data_edit.form",
            appliedParams	: {'tk' : getParam('tk'), touchpointContext: true, 'documentId' : jsp_document_id, 'localeId' : localeId, 'touchpointSelectionId' : jsp_touchpoint_selection_id},
            closeBtnId		: "cancelBtn_button",
            screenMask		: false,
            draggable		: false,
            beforePopupClose: function() {
                window.location.reload();
            },
            onSave			: function() {
                window.location.reload();
            }
        });
    });

    if (exists('multipartMessage'))
        $('.zoneSelectedClass .multipartZonePart:first').each( function() {
            partClickedAction(this);
        });

    $("#variantContentCompareBtn").each( function() {
        if ( !$(this).hasClass('actionBtn_disabled') ) {
            $(this).
            click( function() {
            		variantContentComparePopup();
            });
        }
    });

});