<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
<msgpt:HeaderNew title="" extendedScripts="true">
	<link rel="stylesheet" type="text/css" href="../includes/themes/commoncss/theme_minimal.css" />
	
	<msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js" />
	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css" />


    <msgpt:Script>
        <script>
		function closeIframe_local() {
			if ( window.opener != null ) {
				window.close();
			} else {
	 			var currentURL = getTopFrame().location.href;
	    		getTopFrame().location.href = currentURL;
			}
   		}
   		
		function submitAction(callback){
			doSubmit();
			callback();
		}
		
		function initFlipToggle() {
                $(".flipToggle_syncFromOrigin").each( function() {
                    var currentBinding = $(this);
                    if ( $(this).is(":visible") && $("#syncFromOrigin:visible").closest('.ibutton-container').length == 0 )
                        $(this).iButton({
                            labelOn: $(currentBinding).attr('title').split(';')[0],
                            labelOff: $(currentBinding).attr('title').split(';')[1],
                            resizeHandle: false,
                            resizeContainer: "auto"
                        });
                    else
                        setTimeout( function() { initFlipToggle(); }, 500);
                });
            }

            // Init Javascript
            $( function() {
                // $("#objectsListAssignmentFilter").styleActionElement();

                initFlipToggle();

                // SNAP OFF: Header
    			if ( window.opener != null ) {
    				$('.iFramePopupTitle').html( $('#popupHeaderTitle .titleText').html() );
    				$('.iFramePopupHeaderContainer').show();
    			}
            });
        </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:BodyNew theme="minimal" type="iframe">

	<%--@elvariable id="command" type=""--%>
	<form:form modelAttribute="command">
		<c:if test="${empty param.nprSaveSuccess}">
		
			<div id="popupHeaderTitle" style="display: none;">
				<span class="titleText">
                    <c:choose>
                        <c:when test="${isExchangeTouchpoint != true}">
                            <c:out value='${msgpt:getMessage("page.label.touchpoint.project")} ${not empty command.document.name ? ":" : ""} ${command.document.name}' />
                        </c:when>
                        <c:otherwise>
                            <c:out value='${msgpt:getMessage("page.label.touchpoint.exchange.update")} ${not empty command.document.name ? ":" : ""} ${command.document.name}' />
                        </c:otherwise>
                    </c:choose>

				</span>
			</div>
			
			<div class="iFramePopupHeaderContainer" style="display: none;">
				<div class="iFramePopupTitleHandle">
					<span class="iFramePopupTitle"></span>
				</div>
			</div>
			
			<div class="contentTableIframeExtended">
				<div class="contentPanel backgroundTile_10p" style="min-height: 625px; padding: 0px 30px;">
				
					<form:errors path="*">
						<msgpt:Information errorMsgs="${messages}" type="error" />
					</form:errors>
					
					<!-- TABS -->
					<div align="center" style="padding: 18px;">
						<table cellspacing="0" cellpadding="0" border="0"><tr>
							<td style="padding: 0px;">
								<div align="center" class="workflowTab workflowTabFirst" onclick="javascriptHref('touchpoint_sync_project.form?documentId=${not empty param.documentId ? param.documentId : -1}');">
									<div class="workflowTabText"><fmtSpring:message code="page.label.content"/></div>
								</div>
							</td>
							<td style="padding: 0px;">
								<div align="center" class="workflowTabSelected workflowTabLast">
									<div class="workflowTabText"><fmtSpring:message code="page.label.priority"/></div>
								</div>
							</td>
						</tr></table>
					</div>
					
					<c:choose>
                        <c:when test="${isExchangeTouchpoint != true}">
                            <!-- Synchronization Settings -->
                            <msgpt:DataTable labelPosition="top">
                                <msgpt:TableItem label="page.label.sync.direction">
                                    <form:checkbox id="syncFromOrigin" class="flipToggle_syncFromOrigin" path="syncFromOrigin" title="${msgpt:getMessage('page.label.synch.in.update')};${msgpt:getMessage('page.label.synch.out.commit')}" />
                                </msgpt:TableItem>
                            </msgpt:DataTable>
                        </c:when>
                        <c:otherwise>
                            <form:checkbox id="syncFromOrigin" path="syncFromOrigin" value="true" cssStyle="display: none" />
                            <br />
                        </c:otherwise>
                    </c:choose>
					
					<div style="min-height: 400px;">
						<div class="InfoSysContainer_info" style="margin-top: 20px;">
							<fmtSpring:message code="page.text.sync.message.priority"/>
						</div>
					</div>

				</div>				
			</div>
					
			<msgpt:DataTable>
				<msgpt:TableButtons>
					<!-- Buttons -->
					<msgpt:Button label="page.label.cancel" URL="javascript:closeIframe_local()" />
					<c:choose>
                        <c:when test="${isExchangeTouchpoint != true}">
							<msgpt:Button label="action.button.label.sync" URL="javascript:submitAction(closeIframe_local());" primary="true" />
						</c:when>
                        <c:otherwise>
							<msgpt:Button label="client_messages.text.update" URL="javascript:submitAction(closeIframe_local());" primary="true" />
						</c:otherwise>
                    </c:choose>
				</msgpt:TableButtons>
			</msgpt:DataTable>
		</c:if>					
	</form:form>
</msgpt:BodyNew>
</msgpt:Html5>