<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.model.workflow.Workflow"%>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>

    <%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>
    <msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.EDIT %>">

	<msgpt:CalendarIncludes/>


	<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />
	<msgpt:Script src="includes/javascript/checkboxSelect.js" />
	
	<msgpt:Stylesheet href="includes/themes/commoncss/tinyMCEEditorContent.css" />
	<msgpt:Script src="includes/javascript/${tinymceDir}/jquery.tinymce.min.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/tinyMCEinit/jquery.tinyMCEinit.js" />

	<msgpt:Script src="includes/javascript/jQueryPlugins/hoverIntent/jquery.hoverIntent.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.editorActions.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.varTagAttrManager.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.smartTextTagAttrManager.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/disableSelection/jquery.disable.text.select.pack.js" />
	
	<msgpt:Script src="includes/javascript/jQueryPlugins/tagCloud/jquery.tagCloud.js" />
	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/tagCloud/tagCloud.css" />

	<style type="text/css">
		.contentDataContainer {
			padding: 0px;
		}
	</style>

    <msgpt:Script>
        <script>

            var editorData = {
                content_css				: "../includes/themes/commoncss/tinyMCEEditorContent.css",
                async_variables 		: { type : 'document' },
				async_embedded_content 	: { type : 'document' },
                markers_list			: ${systemVariablesData},
                applies_text_styles		: false,
                applies_paragraph_styles: false,
                applies_templates		: false,
                document_ids			: [${documentIds}],
                can_edit_source			: ${sourceEditPerm}
            };
			var pageHeight = 740;
            function toggleThrottleInput() {
                if ( $('#throttleInputDiv').is(':visible') ) {
                    $('#throttleInputDiv').hide();
                } else {
                    $('#throttleInput').val(1);
                    $('#throttleInputDiv').showEle('normal');
                }
            }

            function toggleUsageType() {
                var usageTypeId = $('#usageTypeSelect').val();
                if ( usageTypeId == 3 ) {
                    $('.locationContainer').showEle('normal');
                    $('.connectorContainer').hide();
                } else {
                    $('.locationContainer').hide();
                    $('.connectorContainer').showEle('normal');
                }
            }

            $( function() {
                $(".style_multiselect,.style_select").styleActionElement({labelAlign: true});

                tinyMCEvalueEditorInit("content", "400", "200", editorData);

                setPrimaryBtn('save');
				$('#page').height(pageHeight);

            });
        </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:BodyNew theme="minimal" type="iframe">
		
	<c:set var="isNewTag" value="${empty param.tagId}" />
	<c:set var="newEditKey" value="page.label.createnew" />
	<c:if test="${!isNewTag}">
		<c:set var="newEditKey" value="page.label.edit" />
		<c:set var="status" value=" [${command.status}]"/>
	</c:if>

	<form:form method="post" modelAttribute="command" >
		<div id="popupHeaderTitle" style="display: none;">
			<span class="titleText">
				<c:out value='${msgpt:getMessage(newEditKey)} ${msgpt:getMessage("page.label.tag")} ${command.name != null ? "-":""} ${command.name}${!isNewTag ? status : ""}' />
			</span>
		</div>				

		<msgpt:WorkflowTab disableAll="${isNewTag}" parameter="tagId" type="<%= Workflow.TAG_WORKFLOW_ID %>" edit="true" >

			<div class="contentTableIframe">
				<div class="contentPanel" style="padding: 20px 40px;">
					<form:errors path="*">
						<msgpt:Information errorMsgs="${messages}" type="error" />
					</form:errors>
									
					<msgpt:DataTable labelPosition="top" multiColumn="true">
						<!-- Name -->
						<msgpt:TableItem label="page.label.name">
							<msgpt:InputFilter type="simpleName">
								<form:input path="name" cssClass="inputXL" />
							</msgpt:InputFilter>
						</msgpt:TableItem>
						<!-- Priority -->
						<msgpt:TableItem label="page.label.priority">
							<msgpt:InputFilter type="numeric">
								<form:input path="priority" cssClass="input5digit" maxlength="4" />
							</msgpt:InputFilter>
							<span align="left" style="padding: 0px 4px; font-size: 10px;">
								<fmtSpring:message code="page.text.0.lowest.9999.highest"/>
							</span>
						</msgpt:TableItem>
						<!-- Start Date -->
						<msgpt:TableItem label="page.label.start.date">
							<msgpt:Calendar id="startDate" path="startDate" viewableDateFormat="${viewableDateFormat}" />
						</msgpt:TableItem>
						<!-- End Date -->
						<msgpt:TableItem label="page.label.end.date">
							<msgpt:Calendar id="endDate" path="endDate" viewableDateFormat="${viewableDateFormat}" />
						</msgpt:TableItem>
						<!--  Usage Type -->
						<msgpt:TableItem label="page.label.usage.type">
							<form:select id="usageTypeSelect" path="usageTypeId" items="${usageTypes}" itemLabel="name" itemValue="id" cssClass="style_select inputL" />
						</msgpt:TableItem>
						<!--  Injection Location -->
						<msgpt:TableItem label="page.label.injection.location" cssClass="locationContainer" >
							<form:select path="injectionLocationTypeId" items="${injectionLocationTypes}" itemLabel="name" itemValue="id" cssClass="style_select inputL" />
						</msgpt:TableItem>

						<!-- Touchpoints -->
						<msgpt:TableItem label="page.label.touchpoints" cssClass="touchpointSelectContainer">
							<div style="${canModifyTouchpoint ? '' : 'display: none;'}">
								<div>
									<select id="documentSelect"
											name="documents"
											class="complex-dropdown-select"
											title="${msgpt:getMessage('page.label.touchpoint.s')}"
											aria-label="${msgpt:getMessage('page.label.touchpoints')}"
											data-toggle="complex-dropdown"
											data-enablefilter="true"
											data-enable-selectall="true"
											data-enable-tag-cloud="true"
											data-enable-view-selected="true"
											data-menu-class="dropdown-menu-left"
											data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
											data-show-titles="true"
											data-titles-max-length="44"
											multiple>
										<c:forEach var="currentDocument" items="${documents}">
											<fmtSpring:bind path="documents">
												<option id="touchpointOption_${currentDocument.id}"
														class="ml-0 level_${currentDocument.projectDepth}" value="${currentDocument.id}"
														data-filtervalue="${currentDocument.metatags}"
													${msgpt:contains( command.documents, currentDocument) || currentDocument.id == documentContextId ? 'selected="selected"': ''}>
														${currentDocument.name}
												</option>
											</fmtSpring:bind>
										</c:forEach>
									</select>
								</div>
							</div>
							<div style="${canModifyTouchpoint ? 'display: none;' : ''}">
								<c:forEach var="currentDocument" items="${command.documents}">
									<c:out value="${currentDocument.name}" />&nbsp;
								</c:forEach>
							</div>
						</msgpt:TableItem>
						<!-- Touchpoint Collections -->
						<c:if test="${not empty touchpointCollections}">
							<msgpt:TableItem label="page.label.touchpoint.collection" cssClass="collectionSelectContainer">
								<div>
									<select id="collectionSelect"
											name="touchpointCollections"
											class="complex-dropdown-select"
											title="${msgpt:getMessage('page.label.collection.s')}"
											aria-label="${msgpt:getMessage('page.label.touchpoint.collections')}"
											data-toggle="complex-dropdown"
											data-enablefilter="true"
											data-enable-selectall="true"
											data-enable-view-selected="true"
											data-min-children-to-display-filter="1"
											data-class="position-absolute"
											data-menu-class="dropdown-menu-left"
											data-dropdown-class="btn-sm btn-lightest dropdown-flexblock"
											data-show-titles="true"
											data-titles-max-length="50"
											multiple>
										<c:forEach var="currentCollection" items="${touchpointCollections}">
											<fmtSpring:bind path="touchpointCollections">
												<option id="collectionOption_${currentCollection.id}"
														class="ml-0" value="${currentCollection.id}"
														${msgpt:contains( command.touchpointCollections, currentCollection) || currentCollection.id == collectionContextId ? 'selected="selected"': ''}>
														${currentCollection.name}
												</option>
											</fmtSpring:bind>
										</c:forEach>
									</select>
								</div>
							</msgpt:TableItem>
						</c:if>
						<!-- Connector -->
						<msgpt:TableItem label="page.label.connector" cssClass="connectorContainer">
							<msgpt:InputFilter type="simpleName">
								<form:input path="connectorName" cssClass="inputXL" size="96" />
							</msgpt:InputFilter>
						</msgpt:TableItem>
					</msgpt:DataTable>
					<msgpt:DataTable labelPosition="top">
						<!-- Content -->
						<msgpt:TableItem label="page.label.content">
							<msgpt:InputFilter type="code">
								<div style="border: 1px solid #bbb; border-radius: 3px; margin-bottom: 12px;">
									<form:textarea id="editor_content" cssClass="mceEditor_content" path="content.encodedValue"/>
								</div>
							</msgpt:InputFilter>
						</msgpt:TableItem>

					</msgpt:DataTable>
				</div>
			</div>
		</msgpt:WorkflowTab>
	</form:form>
</msgpt:BodyNew>
</msgpt:Html5>