<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.touchpoints" extendedScripts="true" >

	<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />

	<msgpt:Script src="includes/javascript/jQueryPlugins/previewMessageData/jquery.previewObjectInfo.js" />

	<msgpt:Script src="includes/javascript/popupActions.js" />

	<msgpt:Script src="includes/javascript/jQueryPlugins/sectionImagePopup/jquery.sectionImagePopup.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js" />

	<style type="text/css">
		.levelIndentation {
			width: 15px;
			height: 12px;
			float: left;
			background: url('../includes/themes/commonimages/icons/indentation_dot.gif') no-repeat left bottom;
		}
	</style>

    <msgpt:Script>
        <script>
            var popupLock = false;

            // *************** DATA VALUE INIT FUNCTIONS START ***************
            function viewSelectionDataValues(collectionId) {
                $('#viewDataValueTreeDiv_dataValueCollection').val(collectionId);
                actionSelected('7');
            }

            function nodeDataValuesInit() {
                initDataValues();
                $(specId).find('#actionPopupStandardButtons').hide();
                $(specId).find('#actionPopupCloseButton').show();
                $(specId).find('#actionPopupViewDataValues').show();
            }

            function initDataValues() {
                $('#viewDataValueTree').hide();
                $('#removeDataValueTree').hide();
                initViewDataValuesTree();
            }

            function initViewDataValuesTree() {

                var dataCollection = $('#viewDataValueTreeDiv_dataValueCollection').val();
                common.collectionDataValuesTreeInit('viewDataValueTreeDiv', dataCollection);
                $('#viewDataValueTreeDiv').addClass('treeInit');
                $('#viewDataValueTree').fadeIn('normal');

            }
            // *************** DATA VALUE INIT FUNCTIONS END ***************

            // ******* Language Selections Checkbox Functions *******
            function toggleChecks() {
                if ( $("#allCheck").attr("checked") )
                    $("input[id^='listItemCheck']").attr("checked","checked");
                else
                    $("input[id^='listItemCheck']").removeAttr("checked");
                validateActionReq();
            }

            function validateActionReq() {
                var singleSelect = true;
                var canRemove = true;
                var containsDefaultNode = false;

                // Resolve selection flags
                if ( $("input[id^='listItemCheck']:checked").length != 1 )
                    singleSelect = false;

                var languageSelectionId = -1;
                $("input[id^='listItemCheck']:checked").each(
                    function() {
                        languageSelectionId = this.id.replace('listItemCheck_','');
                        if ( exists('isDefaultNode_'+languageSelectionId) )
                            containsDefaultNode = true;

                        var selectedLangCode = $('#languageCode_'+languageSelectionId).val();
                        $('#languageSelect option').removeAttr('selected');
                        $('#languageSelect option').each(function(){
                            if ( $(this).val() == selectedLangCode )
                                $(this).attr('selected','selected');
                        });
                    }
                );

                // Disable all actions
                $('#updateBtn').disableElement();
                $("#actionMenu").disableAllOptions();

                if ( $("input[id^='listItemCheck']:checked").length > 0 ) {
                    if (!containsDefaultNode && singleSelect)
                        $('#updateBtn').enableElement();
                    if (singleSelect)
                        $('#actionMenu').enableOption('actionOption_3'); // Add selection
                    if (singleSelect)
                        $('#actionMenu').enableOption('actionOption_4'); // Rename selection
                    if (singleSelect)
                        $('#actionMenu').enableOption('actionOption_5'); // Modify language
                    if (!containsDefaultNode)
                        $('#actionMenu').enableOption('actionOption_6'); // Delete selection
                }

                // console.log('Single:'+singleSelect);

            }
            // ******* End Language Selection Checkbox Functions *******

            function onNavTreeLoad() {
                $('.treeNodeLink').sectionImagePopup({
                    type: 'languageSelectionList'
                });
            }

            // Init Javascript
            $( function() {
                $("#allCheck").click(toggleChecks);

                // Style action bar elements
                $("input:button").styleActionElement();
                $("#actionMenu").styleActionElement();

                validateActionReq();
            });
        </script>
    </msgpt:Script>

</msgpt:HeaderNew>

<!-- FLAGS AND PERMISSIONS -->
<!-- END FLAGS AND PERMISSIONS -->

<msgpt:BodyNew theme="minimal">
	<msgpt:BannerNew  edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>" />
	<msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_TOUCHPOINTS %>" />
	<msgpt:ContextBarNew globalContextApplied="false" languageContextApplied="false" touchpointSetupContext="true" touchpointAdminContext="true" />
	<msgpt:LowerContainer>
		<msgpt:ContentPanel>
		
			<c:if test="${not trashTpContext }">
				<form:form method="post" modelAttribute="command">
					
					<form:errors path="*">
						<msgpt:Information errorMsgs="${messages}" type="error" />
					</form:errors>	
	
					<c:set var="items" value="${command.list}" scope="request" />
					<msgpt:CalculatePageNavigation itemsName="items" page="${param.page}" pageSize="${param.pagesize}" maxPages="${param.maxpages}" outputName="languageSelections" className="com.prinova.messagepoint.model.LanguageSelection" />
	
					<c:if test="${not empty languageSelections}">
						<!-- Pagination -->		
						<msgpt:OutputPageNavigation page="${param.page}" pageSize="${param.pagesize}" maxPages="${param.maxpages}">
							<msgpt:URLBuilder page="language_selections_list.form" maintainQueryStringParms="true" queryStringParms="${allTreeParameters}"/>
						</msgpt:OutputPageNavigation>
					</c:if>

					<c:choose>
						<c:when test="${not document.isMultiLanguage}">
							<div style="margin: 10px 5px; padding: 5px 10px; background-color: #eee; border: 1px solid #bbb; color: #333;">
								<fmtSpring:message code="page.text.only.one.language.associated.with.touchpoint"/> <c:out value="${document.defaultTouchpointLanguage.name}" />.
							</div>
						</c:when>
						<c:when test="${empty document.languageParameterGroup}">
							<div style="margin: 10px 5px; padding: 5px 10px; background-color: #eee; border: 1px solid #bbb; color: #333;">
								<fmtSpring:message code="page.text.language.selector.not.defined.for.touchpoint" />
							</div>
						</c:when>
						<c:when test="${empty languageSelections}">
							<div style="margin: 10px 5px; padding: 5px 10px; background-color: #eee; border: 1px solid #bbb; color: #333;">
								<fmtSpring:message code="page.text.no.matching.language.selections" />
							</div>
						</c:when>
						<c:otherwise>
							<div class="tableComponentWrapper">
								<div class="actionsBarContainer">
									<div class="actionBarHeaderLabel featureLabelText">
										<fmtSpring:message code="page.label.language.selections"/>
									</div>
									<!-- ACTION BAR: UPDATE, ACTIONS -->
									<div class="actionBarButtonsContainer">
										<input title="${msgpt:getMessage('action.button.label.update')}" type="button" id="updateBtn" onclick="submitAction(4,this);" disabled="disabled" style="display: none;" />
										<select title="${msgpt:getMessage('page.label.actions')}" id="actionMenu" class="inputM style_menu" onchange="actionSelected(this)" style="display: none;">
											<option id="actionOption_3" disabled="disabled"><fmtSpring:message code="page.label.add.selection"/></option>
											<option id="actionOption_5" disabled="disabled"><fmtSpring:message code="page.label.modify.language"/></option>
											<option id="actionOption_6" disabled="disabled"><fmtSpring:message code="page.label.delete.selection"/></option>
											<option id="actionOption_4" disabled="disabled"><fmtSpring:message code="page.label.rename.selection"/></option>
										</select>
									</div>
								</div>
								<!-- Language Selection List Table Start -->
								<msgpt:DataTable sort="false">
									<c:forEach var="selectionVO" items="${languageSelections}" varStatus="status">
										<c:set var="languageSelection" value="${selectionVO.languageSelection}" />
										<c:set var="currentId" value="${languageSelection.id}" />
										<c:set var="selectionLinkURL" value="language_selection_selectors_edit.form?languageSelectionid=${languageSelection.id}&documentId=${param.documentId}" />

										<msgpt:TableListGroup reducedSpacing="true">
											<msgpt:TableElement align="left" width="1%" label="<input id='allCheck' type='checkbox' class='checkbox' />" style="vertical-align: middle;">
												<!-- Language Selection permission flags -->
												<c:if test="${selectionVO.treeNodeLevel == 0}">
													<input type="hidden" id="isDefaultNode_${currentId}" />
												</c:if>
												<form:checkbox path="map[${currentId}].selectedForAction" id="listItemCheck_${currentId}" cssClass="checkbox checkboxAlign" onclick="validateActionReq(${currentId});"/>
											</msgpt:TableElement>
											<!-- Name -->
											<msgpt:TableElement align="left" label="${msgpt:getMessage('page.label.selections')}: <i style='font-weight: normal'>${msgpt:getMessage('page.text.by')} ${document.languageParameterGroup.name}</i>" style="vertical-align: middle;">
												<c:forEach begin="1" end="${selectionVO.treeNodeLevel}">
													<div class="levelIndentation">&nbsp;</div>
												</c:forEach>
												<c:choose>
													<c:when test="${selectionVO.treeNodeLevel == 0}">
													<span id="actionLabel_${currentId}" itemName="${languageSelection.name}">
														<c:out value="${languageSelection.name}" />
													</span>
													</c:when>
													<c:otherwise>
														<a href="${selectionLinkURL}" id="languageSelectionLink_${currentId}">
														<span id="actionLabel_${currentId}" itemName="${languageSelection.name}">
															<c:out value="${languageSelection.name}" />
														</span>
														</a>
													</c:otherwise>
												</c:choose>
											</msgpt:TableElement>
											<!-- Language -->
											<msgpt:TableElement align="left" label="page.label.locale.code" style="vertical-align: middle;">
												<c:out value="${languageSelection.messagepointLocale.code}" />
												<input type="hidden" id="languageCode_${currentId}" value="${languageSelection.messagepointLocale.id}" />
											</msgpt:TableElement>
											<!-- Details -->
											<msgpt:TableElement align="left" width="13%" label="page.label.details">
												<!-- Selector Detail -->
												<c:choose>
													<c:when test="${selectionVO.treeNodeLevel == 0}">
														<div class="detailBackground">&nbsp;</div>
													</c:when>
													<c:otherwise>
														<c:if test="${languageSelection.hasSelectorValues}">
															<div id="selectorLink_${currentId}" type="selector" class="selectorDetail" onmouseover="this.className='selectorDetail_Hov'" onmouseout="this.className='selectorDetail'"
																 onclick="viewSelectionDataValues('${languageSelection.parameterGroupTreeNode.parameterGroupInstanceCollection.id}');">&nbsp;</div>
														</c:if>
														<c:if test="${not languageSelection.hasSelectorValues}">
															<div class="selectorDetail_Disabled">&nbsp;</div>
														</c:if>
													</c:otherwise>
												</c:choose>
											</msgpt:TableElement>
										</msgpt:TableListGroup>
									</c:forEach>
								</msgpt:DataTable>
							</div>
						</c:otherwise>
					</c:choose>

					<c:if test="${not empty languageSelections}">
						<!-- Pagination -->
						<msgpt:OutputPageNavigation page="${param.page}" pageSize="${param.pagesize}" maxPages="${param.maxpages}">
							<msgpt:URLBuilder page="language_selections_list.form" maintainQueryStringParms="true" queryStringParms="${allTreeParameters}"/>
						</msgpt:OutputPageNavigation>
					</c:if>

					<!-- POPUP DATA -->
					<div id="actionSpecs" style="display: none;">
						<!-- ACTIONS POPUP DATA -->
						<div id="actionSpec_3" submitId="1"> <!-- Add selection -->
							<div id="actionTitle_3"><fmtSpring:message code="page.label.confirm.add.selection"/></div>
							<div id="actionInfo_3"><p><b><fmtSpring:message code="page.text.add.selection.under.selected.item"/></b></p></div>
							<div id="actionRename_3" required="true" presetValue="false"></div>
							<div id="actionLanguage_3" required="true"></div>
						</div>
						<div id="actionSpec_4" submitId="2"> <!-- Rename selection -->
							<div id="actionTitle_4"><fmtSpring:message code="page.label.confirm.rename.selection"/></div>
							<div id="actionInfo_4"><p><b><fmtSpring:message code="page.text.rename.selected.selection"/></b></p></div>
							<div id="actionRename_4" required="true"></div>
						</div>
						<div id="actionSpec_5" submitId="5"> <!-- Modify language -->
							<div id="actionTitle_5"><fmtSpring:message code="page.label.confirm.modify.language"/></div>
							<div id="actionInfo_5"><p><b><fmtSpring:message code="page.text.modify.language.for.selected.variant"/></b></p></div>
							<div id="actionLanguage_5" required="true"></div>
						</div>
						<div id="actionSpec_6" type="simpleConfirm" submitId="3"> <!-- Delete selection -->
							<div id="actionTitle_6"><fmtSpring:message code="page.label.confirm.delete"/></div>
							<div id="actionInfo_6"><p><b><fmtSpring:message code="page.text.delete.selected.language.variant"/></b></p></div>
						</div>
						<div id="actionSpec_7" contentWidth="425px"> <!-- View data values -->
							<div id="actionTitle_7"><fmtSpring:message code="page.label.view.selection.data"/></div>
							<div id="actionViewDataValues_7"></div>
						</div>
					</div>

					<!-- POPUP INTERFACE -->
					<msgpt:Popup id="actionPopup">
						<div id="actionPopupInfoFrame">
							<div id="actionPopupInfo">&nbsp;</div>
						</div>
						<div id="actionPopupRename">
							<div class="formControl">
								<label><span class="labelText"><fmtSpring:message code="page.label.name"/></span></label>
								<div class="controlWrapper">
									<msgpt:InputFilter type="simpleName">
										<form:input cssClass="inputL" path="selectionName" onkeyup="validatePopupReq();" onchange="validatePopupReq();" id="rename_newNodeName" onfocus="this.select()"/>
									</msgpt:InputFilter>
								</div>
							</div>
						</div>
						<div id="actionPopupLanguage">
							<br>
							<div class="formControl">
								<label><span class="labelText"><fmtSpring:message code="page.label.language"/></span></label>
								<div class="controlWrapper">
									<form:select id="languageSelect" path="messagepointLocale" items="${locales}" itemValue="id" itemLabel="name" />
								</div>
							</div>
						</div>
						<div id="actionPopupViewDataValues">
                            <input type="hidden" id="viewDataValueTreeDiv_dataValueCollection" class="maintainValue"/>
                            <msgpt:CollectionDataValuesTree id="viewDataValueTreeDiv" style="overflow: auto; width: 375px; height: 150px; text-align; left" />
						</div>
						<div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
							<span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();" label="page.label.cancel" /></span>
							<span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#" label="page.label.cancel" disabled="true" /></span>
							<span id="continueBtnEnabled"><msgpt:Button URL="#" label="page.label.continue" primary="true" /></span>
							<span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#" label="page.label.continue" disabled="true" /></span>
						</div>
						<div id="actionPopupCloseButton" class="actionPopupButtonsContainer">
							<msgpt:Button URL="javascript:actionCancel();" label="page.label.close" />
						</div>
					</msgpt:Popup>
	
				</form:form>
			</c:if>	
				
			<c:if test="${trashTpContext}">
				<div class="InfoSysContainer_info">
					<i class="fa icon fa-info-circle" aria-hidden="true"></i>
					<p><fmtSpring:message code="page.text.trash.touchpoint.does.not.apply"/></p>
				</div>
			</c:if>
		</msgpt:ContentPanel>
	</msgpt:LowerContainer>
</msgpt:BodyNew>
</msgpt:Html5>