<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.tag.layout.MessagepointHeader" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.touchpoints" viewType="<%= MessagepointHeader.ViewType.EDIT %>">

	<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />

	<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/jquery.colorpicker.js" />
	<link href="../includes/javascript/jQueryPlugins/colorpicker_new/jquery.colorpicker.css" rel="stylesheet" type="text/css"/>
	<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/swatches/jquery.ui.colorpicker-pantone.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/parts/jquery.ui.colorpicker-memory.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/parsers/jquery.ui.colorpicker-cmyk-parser.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/colorpicker_new/parsers/jquery.ui.colorpicker-cmyk-percentage-parser.js" />

	<msgpt:Script src="includes/javascript/handlebars/handlebars-v4.7.8.js" />
	
	<msgpt:Script src="includes/javascript/${tinymceDir}/jquery.tinymce.min.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/tinyMCEinit/jquery.tinyMCEinit.js" />
	<msgpt:Script src="includes/javascript/jQueryPlugins/editorActions/jquery.editorActions.js" />

	<msgpt:Script src="includes/javascript/jQueryPlugins/listInput/jquery.listInput.js" />
	<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/listInput/listInput.css" />

	<style>
		.attrValues {
			position: relative; 
			top: -4px;
		}
	</style>

    <script id="attrToggleBtnTemplate" type="text/x-handlebars-template">
		<div id="{{eleIdx}}" class="fa-mp-container actionBtn_roundAll actionBtn {{#if selected}}actionBtn_toggleSelectHighlight{{/if}}" style="display: inline-block; text-align: center; vertical-align: middle;">
			<i class="fa {{icon}} fa-med fa-mp-btn" style="font-size: 14px; left: 1px; position: relative;"></i>
		</div>
	</script>
    <msgpt:Script>
        <script>
        var _btnTemplate = null;

        function initBtn(ele, type) {
            $(ele)
                .mouseover( function() {
                    if ( !$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight .actionBtn_toggleSelect]') ) {
                        $(this).removeClass('actionBtn');
                        $(this).addClass('actionBtn_hov');
                    }
                })
                .mouseout( function() {
                    if ( !$(this).hasClass('actionBtn_disabled') && !$(this).hasClass('actionBtn_selected') && !$(this).hasClass('actionBtn_toggleSelectHighlight .actionBtn_toggleSelect') ) {
                        $(this).removeClass('actionBtn_hov');
                        $(this).addClass('actionBtn');
                    }
                });

            if ( type == "toggle" ) {
                $(ele)
                    .click( function() {
                        if ( !$(this).hasClass('actionBtn_disabled') ) {
                            if ( $(this).hasClass('actionBtn_toggleSelectHighlight') )
                                $(this).removeClass('actionBtn_toggleSelectHighlight').addClass('actionBtn');
                            else
                                $(this).removeClass('actionBtn').addClass('actionBtn_toggleSelectHighlight');
                        }

                    });
            } else if ( type == "button" ) {
                $(ele)
                    .mousedown( function() {
                        if ( !$(this).hasClass('actionBtn_disabled') )
                            $(this).removeClass('actionBtn actionBtn_hov').addClass('actionBtn_selected');
                    })
                    .mouseup( function() {
                        if ( !$(this).hasClass('actionBtn_disabled') )
                            $(this).removeClass('actionBtn_selected').addClass('actionBtn');
                    });
            }
        }

        function initWebFontSelect(){
            initBtn($('#applyFont'),"toggle");
            if($('#applyTextStyleFont').val()=='true'){
                $('#applyFont').addClass('actionBtn_toggleSelectHighlight');
                $('#webFontName').hide();
                $('#fontSelect').styleActionElement();
                $('#fontSelect'+'_menuTable').css('display', 'inline');
            }else{
                $('#applyFont').removeClass('actionBtn_toggleSelectHighlight');
                $('#fontInput').show();
                $('#fontSelect').hide();
            }
            $('#applyFont').click(function(){
                if($(this).hasClass('actionBtn_toggleSelectHighlight')){
                    $('#applyTextStyleFont').val(true);
                    $('#webFontName').hide();
                    $('#fontSelect').styleActionElement();
                    $('#fontSelect'+'_menuTable').show();
                }else{
                    $('#applyTextStyleFont').val(false);
                    $('#webFontName').show();
                    $('#fontSelect'+'_menuTable').hide();
                }
            });
        }

        function getColorPicker(forPicker) {

            if ($('#colorPickerContainer').length == 0) {
                $('body').append('<input type="text" id="colorPickerContainer" value="000000"></input>');
            }

            var pickerInstance = $('#colorPickerContainer')
                .css('display', 'none')
                .colorpicker({
                    parts:          ['map', 'bar', 'hex', 'hsv', 'rgb', 'cmyk', 'alpha', 'preview', 'footer'],
                    alpha:          false,
                    modal:			true,
                    closeOnOutside:	true,
                    closeOnEscape:	true,
                    buttonColorize: true,
                    showNoneButton: false,
                    revert:			true,
                    close:	function(event, picker) {
                        $('#' + forPicker).css('background-color', '#' + picker.formatted);
                        $('#' + forPicker.replace('colorSelectorToggle', 'colorBinding')).val(picker.formatted);
                    },
                    open			: function(formatted, colorPicker) {
                        $(colorPicker.colorPicker.dialog).css({'z-index': 3});
                    }
                });

            pickerInstance.colorpicker('open');

            var pickerControl = $('.ui-colorpicker').first();
            pickerControl.css('position', 'absolute');
            pickerControl.css("top", Math.max(0, (($(window).height() - $(pickerControl).outerHeight()) / 2) +  $(window).scrollTop()) + "px");
            pickerControl.css("left", Math.max(0, (($(window).width() - $(pickerControl).outerWidth()) / 2) +  $(window).scrollLeft()) + "px");

            return pickerInstance;
        };

        //tinymceEditorDef_simpleText
        var editorData = {
            content_css				: "../includes/themes/commoncss/tinyMCEEditorContent.css",
            text_data				: "null",
            variables_list			: "",
            smart_text_list			: "",
            spellcheck				: false,
            symbols					: false,
            apply_menu				: false
        };
        tinyMCEsimpleTextEditorInit("350", "100", "SINGLELINE", editorData);

        function tinymceInitCallback(ed) {
            $('#taggingOverrideEditor_ifr').height(80);
        }

        function toggleFixedAttr(id) {
            var toggleEle = $('#'+id);
            var fixedEle = $(toggleEle).closest('table').find('.fixedAttr');
            var toggleContainer = $(toggleEle).closest('table').find('.attrValues');
            if ( $(toggleEle).is(':checked') ) {
                $(fixedEle).hide();
                $(toggleContainer).show();
            } else {
                $(fixedEle).show();
                $(toggleContainer).hide();
            }
        }

        $( function() {

            initWebFontSelect();
            $('#taggingOverrideEditor').tinymce(tinymceEditorDef_simpleText);

            var btnTemplate = _btnTemplate || (_btnTemplate = Handlebars.compile($('#attrToggleBtnTemplate').html()));
            $(".attrToggle").each( function() {
                var toggleEle = this;
                var eleId = $(toggleEle).attr('id');
                $(toggleEle).addClass('init');

                var btnData = {
                    eleIdx		: "attrToggleBtn_" + eleId,
                    selected	: $(toggleEle).is(':checked'),
                    icon		: "fa-adjust"
                };

                var newBtnHTML = $(btnTemplate(btnData));
                $(toggleEle).after(newBtnHTML);

                $(newBtnHTML).click( function() {
                    // toggle button style
                    if ( !$(this).hasClass('actionBtn_disabled') ) {
                        if ( $(this).hasClass('actionBtn_toggleSelectHighlight') ) {
                            $(toggleEle).removeAttr('checked');
                        } else {
                            $(toggleEle).attr('checked','checked');
                        }
                        $(toggleEle).change();
                    }
                    toggleFixedAttr(eleId);
                });
                initBtn($(newBtnHTML),"toggle");
                toggleFixedAttr(eleId);

            });

            $('#togglePointSizeValues').listInput({
                listAreaWidth	: 170
            });
            $('#toggleColorValues').listInput({
                listAreaWidth	: 170,
                type			: 'color'
            });

            $(document).on('click', "#colorSelectorToggle", function (event) {
                var colorPicker = getColorPicker(event.target.id);
                colorPicker.colorpicker('setColor', $(event.target).css('background-color'));
            });
        });

    </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:BodyNew theme="minimal" type="iframe">
			
	<form:form modelAttribute="command">
		<c:if test="${empty param.nprSaveSuccess}">
			<div id="popupHeaderTitle" style="display: none;">
				<span class="titleText">
					<c:out value='${msgpt:getMessage("page.label.edit")} ${msgpt:getMessage("page.label.text.style.customization")} ${not empty command.textStyleName ? ":" : ""} ${command.textStyleName}' />
				</span>
			</div>	
			<div class="contentTableIframe backgroundTile_10p">
				<div class="contentPanel" style="min-height: 540px;">
				
					<form:errors path="*">
						<msgpt:Information errorMsgs="${messages}" type="error" />
					</form:errors>
								
					<msgpt:DataTable labelPosition="top" style="margin-left: 30px; margin-top: 15px; width: 500px;" >
						<!-- Class Name -->
						<msgpt:TableItem label="page.label.connector.name">
							<msgpt:InputFilter type="connectorName">
								<form:input cssClass="inputL" path="connectorName"/>
							</msgpt:InputFilter>
							<form:errors path="connectorName" cssClass="errorMsg" />
						</msgpt:TableItem>
						<!-- Font Name -->						
						<msgpt:TableItem label="page.label.font.name">
							<msgpt:InputFilter type="simpleName">
								<form:input path="fontName" maxlength="96" cssClass="inputXL" />
							</msgpt:InputFilter>
						</msgpt:TableItem>
						<!-- Font Description -->
						<msgpt:TableItem label="page.label.web.font">
							<div style="white-space: nowrap;">
								<div style="display: inline-block; vertical-align: middle;">
									<msgpt:InputFilter type="description">
										<form:input path="webFontName" maxlength="255" cssClass="inputXL" />
									</msgpt:InputFilter>
									<div style="position: relative; left: -5px;">
										<form:select id="fontSelect" path="textStyleFont" cssClass="style_select input135" cssStyle="">
											<c:if test="${not empty availableFonts}">
												<option value="0"><c:out value='${msgpt:getMessage("page.label.please.select")}' /></option>
											</c:if>
											<c:forEach var="availableFont" items="${availableFonts}">
												<form:option value="${availableFont}" ><c:out value="${availableFont.name}" /></form:option>
											</c:forEach>
										</form:select>	
									</div>
								</div>
								<div class="fa-mp-container" style="display: inline-block; vertical-align: middle; position: relative; top: -1px;">
									<i id="applyFont" class="fa fa-book fa-med fa-mp-btn actionBtn_roundAll actionBtn" style="width: 17px; height: 17px; font-size: 16px; padding: 3px 1px 0px 3px;"></i>
								</div>
							</div>
							<form:hidden path="applyTextStyleFont"/>							
						</msgpt:TableItem>
						<!-- Point Size -->
						<msgpt:TableItem label="page.label.point.size" valign="top">
							<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr>
								<td style="padding: 0px;" width="50px">
									<div class="fixedAttr">
										<msgpt:InputFilter type="decimal">
											<form:input cssClass="input3digit" maxlength="5" path="pointSize" />
										</msgpt:InputFilter>
									</div>
									<div class="attrValues">
										<form:hidden id="togglePointSizeValues" cssClass="attrValues" path="togglePointSizeValues"/>
									</div>
								</td>
								<td style="padding: 0px; padding-left: 5px;" align="left">
									<form:checkbox id="togglePointSize" path="togglePointSize" cssClass="attrToggle" style="display: none;" />
								</td>
							</tr></table>
						</msgpt:TableItem>
						<!-- Bold -->
						<msgpt:TableItem label="page.label.bold">
							<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr>
								<td style="padding: 0px;" width="50px">
									<div class="fixedAttr">
										<form:checkbox cssClass="radioBtn" path="bold" />
									</div>
								</td>
								<td style="padding: 0px; padding-left: 5px;" align="left">
									<form:checkbox id="toggleBold" path="toggleBold" cssClass="attrToggle" style="display: none;" />
								</td>
							</tr></table>
						</msgpt:TableItem>
						<!-- Italic -->
						<msgpt:TableItem label="page.label.italic">
							<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr>
								<td style="padding: 0px;" width="50px">
									<div class="fixedAttr">
										<form:checkbox cssClass="radioBtn" path="italic" />
									</div>
								</td>
								<td style="padding: 0px; padding-left: 5px;" align="left">
									<form:checkbox id="toggleItalic" path="toggleItalic" cssClass="attrToggle" style="display: none;" />
								</td>
							</tr></table>
						</msgpt:TableItem>
						<!-- Underline -->
						<msgpt:TableItem label="page.label.underline">
							<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr>
								<td style="padding: 0px;" width="50px">
									<div class="fixedAttr">
										<form:checkbox cssClass="radioBtn" path="underline" />
									</div>
								</td>
								<td style="padding: 0px; padding-left: 5px;" align="left">
									<form:checkbox id="toggleUnderline" path="toggleUnderline" cssClass="attrToggle" style="display: none;" />
								</td>
							</tr></table>
						</msgpt:TableItem>
						<msgpt:TableItem label="page.label.color" valign="top">
							<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr>
								<td style="padding: 0px;" width="50px">
									<div class="fixedAttr">
										<div id="colorSelectorToggle" style="height:17px; width:17px; display: inline-block; border:1px; border-style:solid; border-color:#000; vertical-align: middle; background: #${command.color}"></div>
										<form:input id="colorBinding" path="color" cssClass="colorPickerRawBinding" cssStyle="display:none;" />
									</div>
									<div class="attrValues">
										<form:hidden id="toggleColorValues" cssClass="attrValues" path="toggleColorValues" />
									</div>
								</td>
								<td style="padding: 0px; padding-left: 5px;" align="left">
									<form:checkbox id="toggleColor" path="toggleColor" cssClass="attrToggle" style="display: none;" />				
								</td>
							</tr></table>
						</msgpt:TableItem>
						<c:if test="${document.isGMCTouchpoint}">
							<msgpt:TableItem label="page.label.tag" valign="top">
								<div style="border: 1px solid #bdbdbd">
									<form:textarea id="taggingOverrideEditor" path="taggingOverride" />
								</div>
								<span style="font-size: 10px;">ex. &lt;t fontname="Arial MT (Reg) Regular" fontsize="10pt"&gt;</span>
							</msgpt:TableItem>
						</c:if>
					</msgpt:DataTable>
				</div>
			</div>
			
			<div class="label">
				<table width="100%" class="formBorderlessTable"><tbody><tr><td align="right" style="padding: 2px 12px;">
					<!-- Buttons -->
					<msgpt:Button label="page.label.cancel" URL="javascript:closeIframe()" />
					<msgpt:Button label="page.label.save" URL="javascript:doSubmit()" primary="true" icon="fa-save" />
				</td></tr></tbody></table>
			</div>
		</c:if>
	</form:form>
</msgpt:BodyNew>
</msgpt:Html5>