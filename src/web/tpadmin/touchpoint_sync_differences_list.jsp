<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>
<%@page import="com.prinova.messagepoint.controller.tpadmin.TouchpointSyncDifferencesListController"%>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
	<msgpt:HeaderNew extendedScripts="true">
		<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />

        <c:if test="${not empty defaultViewCSSFilePath}">
            <msgpt:Stylesheet href="${defaultViewCSSFilePath}?cacheStamp=${timeStamp}" standalone="true" />
        </c:if>

        <c:if test="${not empty CSSFilename}">
            <msgpt:Stylesheet href="${CSSFilename}?cacheStamp=${timeStamp}" standalone="true"/>
        </c:if>

        <c:if test="${not empty paragraphStyles}">
            <msgpt:Stylesheet href="${paragraphCSSFilename}?cacheStamp=${timeStamp}" standalone="true"/>
        </c:if>
		
		<msgpt:Script src="includes/javascript/jQueryPlugins/styleManager/jquery.styleManager.js"/>
			
		<msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
		<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css" />
		<msgpt:Stylesheet href="includes/themes/commoncss/targeting.css" />
		
        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>
		
		<style type="text/css">
			.subTitle{
				background: url('../includes/themes/commonimages/layout/content_panel_background.gif') repeat left top;
			}
            div#differencesListPanel {
                height: 576px;
                overflow: auto;
            }
			#differencesListPanel td {
                padding: 4px;
			}
            #differencesListPanel td.invalidValue {
                background-color: #EEE;
            }
		</style>

		<msgpt:Script>
			<script>
			    function compareContent() {
			        var instId = ${param.instanceId};
			        var compareToId = ${param.compareToId};
			        var compareToNodeId = ${param.compareToNodeId};
			        var compareObjectType = '${param.objectType}';
			        var compareDataType = '${param.dataType}';
			        var direction = '${param.direction}';
			        var syncFromOrigin = '${syncFromOrigin}';
                    $('#differencesListContainer').iFramePopup({
                        width           : 800,
                        vertOffset      : -25,
                        id              : "contentComparatorFrame",
                        displayOnInit   : true,
                        title           : client_messages.title.content_compare,
                        src             : context + "/tpadmin/touchpoint_sync_content_compare.form",
                        appliedParams   : {
                            zoneId : -1,
                            tk : getParam('tk'),
                            zonePartId : -1,
                            instanceId : instId,
                            compareToId : compareToId,
                            compareToNodeId: compareToNodeId,
                            objectType: compareObjectType,
                            dataType: compareDataType,
                            direction : direction,
                            syncFromOrigin : syncFromOrigin
                        },
                        closeBtnId      : "cancelBtn_button",
                        screenMask      : false,
                        draggable       : false,
                        beforePopupClose: function() {
                             return true;
                        }
                    });
			    }

                function compareDataRecords() {
                    var instId = ${param.instanceId};
                    var compareToId = ${param.compareToId};
                    var compareToNodeId = ${param.compareToNodeId};
                    var compareObjectType = '${param.objectType}';
                    var direction = '${param.direction}';
                    var syncFromOrigin = '${syncFromOrigin}';
                    $('#differencesListContainer').iFramePopup({
                        width           : 800,
                        vertOffset      : -25,
                        id              : "dataRecordsComparatorFrame",
                        displayOnInit   : true,
                        title           : client_messages.title.datarecords_compare,
                        src             : context + "/tpadmin/touchpoint_sync_datarecords_compare.form",
                        appliedParams   : {
                            zoneId : -1,
                            tk : getParam('tk'),
                            zonePartId : -1,
                            instanceId : instId,
                            compareToId : compareToId,
                            compareToNodeId: compareToNodeId,
                            objectType: compareObjectType,
                            direction : direction,
                            syncFromOrigin : syncFromOrigin
                        },
                        closeBtnId      : "cancelBtn_button",
                        screenMask      : false,
                        draggable       : false,
                        beforePopupClose: function() {
                            return true;
                        }
                    });
                }

                function calculateHash(ele) {
					var objectId = ${model.id};
					var objectInstanceId = ${instance.id};
					var objectType = getParam('objectType');
					var objectStatus = 0; //getParam('objectStatus');

					var otherDocumentId = getParam('otherDocumentId');
					var syncInstanceId = getParam('compareToNodeId');
					var documentId = getParam('documentId');

					$("#calculateHashContainer").hide();
                    $("#processingCalculateHashContainer").show();

					$.ajax({
						type : "GET",
						url : context
							+ "/messageSyncOperations.form?action=makeHash"
							+ "&documentId=" + documentId
							+ "&otherDocumentId=" + otherDocumentId
							+ "&instanceId=" + syncInstanceId
							+ "&objectType=" + objectType
							+ "&objectId=" + objectId
							+ "&objectStatus=" + objectStatus,
						dataType : "json",
						success : function(data) {
							if (data.isSame) {
								$("#differencesListPanel table.contentTable tbody tr[isContent=true]").remove();
							}
							setTimeout(function() {
	                            $("#processingCalculateHashContainer").hide();
			                    $("#calculateHashContainer").show();
							}, 500);
						},
						error : function(data) {
                            setTimeout(function() {
                                $("#processingCalculateHashContainer").hide();
                                $("#calculateHashContainer").show();
                            }, 500);
						}
					});
				}
			</script>
		</msgpt:Script>
	</msgpt:HeaderNew>
	
	<msgpt:BodyNew theme="minimal" type="iframe">
		<form:form method="post" modelAttribute="command">
			<form:errors path="*">
				<msgpt:Information errorMsgs="${messages}" type="error" />
			</form:errors>
			
			<div id="popupHeaderTitle" style="display: none;">
				<span class="titleText">
						<fmtSpring:message code="page.label.differences.list"/>
				</span>
			</div>
			
			<div class="label" style="padding: 10px 50px 10px 50px; font-size: 13px;">
				<table width="100%" cellspacing="0" cellpadding="0" border="0">
					<tr>
						<td>
							<table width="100%" cellspacing="0" cellpadding="2px;" border="0">
								<tr>
									<td align="right" width="10%" style="padding-bottom: 8px; vertical-align: middle;"><fmtSpring:message code="page.label.type"/>:</td>
									<td style="vertical-align: middle; padding-left: 5px; padding-bottom: 8px;">
										${objectType.displayText}
									</td>
								</tr>
								<tr>
									<td align="right" width="10%" style="padding-bottom: 8px; vertical-align: middle;"><fmtSpring:message code="page.label.name"/>:</td>
									<td style="vertical-align: middle; padding-left: 5px; padding-bottom: 8px;">
										${objectName}
									</td>
								</tr>
							</table>							
						</td>
						<td valign="top">
							<table width="100%" cellspacing="0" cellpadding="2px;" border="0">
							</table>							
						</td>
					</tr>
				</table>			
			</div>				
					
			<div class="contentTableIframeExtended" style="overflow: hidden;">
				<div class="contentPanel" style="height: auto; margin: 10px 10px 10px 20px; padding: 0px; border: 1px solid #ccc; ">
					<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr>
						<td align="left" style="padding: 0px; margin: 0px; vertical-align: top; ">
							<div id="differencesListContainer" style="padding: 0px; margin: 0px; border-radius: 8px; ">
							
								<div class="subTitle" style="border-bottom: 1px solid #ccc; padding: 10px 20px 10px 15px;">
									<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr>
										<td>
											<b><fmtSpring:message code="page.label.comparison"/></b>
										</td>
										<td style="text-align: right;">
										
											<div id="compareRangeContainer" class="label" style="font-size: 12px; vertical-align: middle;">
												<span id="compareFrom" style="padding-right: 6px;">
													<c:if test="${param.direction == 'update'}">
														<fmtSpring:message code="page.label.synch.in.update"/>
													</c:if>
													<c:if test="${param.direction == 'commit'}">
														<fmtSpring:message code="page.label.synch.out.commit"/>
													</c:if>
												</span>
											</div>	
										</td>
									</tr></table>
								</div>

								<div id="differencesListPanel" class="contentContainer">
									<table class="contentTable" cellspacing="1" cellpadding="1" border="1">
									    <thead style="height: 40px; ">
                                                <tr>
                                                    <th><fmtSpring:message code="page.label.attribute"/></th>
                                                    <c:if test="${param.direction == 'update'}">
                                                        <th><fmtSpring:message code="page.label.target"/></th>
                                                        <th><fmtSpring:message code="page.label.source"/></th>
                                                    </c:if>
                                                    <c:if test="${param.direction == 'commit'}">
                                                        <th><fmtSpring:message code="page.label.source"/></th>
                                                        <th><fmtSpring:message code="page.label.target"/></th>
                                                    </c:if>
                                                </tr>
									    </thead>
									    <tbody >
                                            <c:forEach var="diff" items="${differences}" varStatus="diffStatus">
                                                <c:forEach var="title" items="${diff.titles }" varStatus="titleStatus">
                                                    <tr isContent="${diff.content}">
                                                        <c:if test="${titleStatus.first}">
                                                            <td rowspan="${diff.attributeRowSpan}"><c:out value="${diff.attributeDisplayText}" /></td>
                                                        </c:if>
                                                        
                                                        <c:if test="${title != '<NULL>'}">
                                                                <td colspan="2"> <c:out value="${title}" /></td>
                                                            </tr>
                                                            <c:if test="${diff.sourceValues.containsKey(title) || diff.targetValues.containsKey(title)}" > 
                                                                <tr>
                                                            </c:if>
                                                        </c:if>
                                                        
                                                        <c:if test="${diff.sourceValues.containsKey(title) || diff.targetValues.containsKey(title)}" > 
		                                                        <td class="${diff.sourceValues.containsKey(title) ? 'validValue' : 'invalidValue'}">
		                                                            <c:if test="${diff.sourceValues.containsKey(title)}" > 
		                                                                <div>
		                                                                    <c:forEach var="value" items="${diff.sourceValues[title]}">
                                                                                <p><c:out value="${value}" escapeXml="${diff.escape}" /></p>
		                                                                    </c:forEach>
		                                                                </div>
		                                                            </c:if>
		                                                        </td>
		                                                        
		                                                        <td class="${diff.targetValues.containsKey(title) ? 'validValue' : 'invalidValue'}">
		                                                            <c:if test="${diff.targetValues.containsKey(title)}" > 
		                                                                <div>
		                                                                    <c:forEach var="value" items="${diff.targetValues[title]}">
                                                                                <p><c:out value="${value}"  escapeXml="${diff.escape}" /></p>
		                                                                    </c:forEach>
		                                                                </div>
		                                                            </c:if>
		                                                        </td>
		                                                    </tr>
                                                        </c:if>
                                                </c:forEach>
                                            </c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</td>
					</tr></table>
				</div>
				<div style="height: 80px; margin: 10px 10px 10px 20px; padding: 0; border: 0;">
                    <msgpt:FlowLayout align="right">
	                    <c:if test="${hasContent}">
	                        <msgpt:FlowLayoutItem>
	                            <msgpt:Button label="page.label.cancel" URL="javascript:closeIframe()" />
	                        </msgpt:FlowLayoutItem>
	                        <msgpt:FlowLayoutItem>
	                           <msgpt:Button label="page.label.content.compare" URL="javascript:compareContent();" primary="true" />
	                        </msgpt:FlowLayoutItem>
	                        <msgpt:FlowLayoutItem>
                                <div id="calculateHashContainer">
                                    <msgpt:Button label="page.label.calculate.hash" URL="javascript:calculateHash(this);" />
                                </div>
                                <div id="processingCalculateHashContainer" style="display: none">
                                    <msgpt:Button label="page.label.state.processing" URL="#" disabled="true" />
                                </div>
	                        </msgpt:FlowLayoutItem>
	                    </c:if>
	                    <c:if test="${not hasContent }">
                            <msgpt:FlowLayoutItem>
                                <msgpt:Button label="page.label.close" URL="javascript:closeIframe()" />
                            </msgpt:FlowLayoutItem>
	                    </c:if>
                    </msgpt:FlowLayout>
				</div>
			</div>	
		</form:form>
	</msgpt:BodyNew>
</msgpt:Html5>