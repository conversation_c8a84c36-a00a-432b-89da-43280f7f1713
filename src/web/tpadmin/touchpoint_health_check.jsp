<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab"%>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
	<msgpt:HeaderNew title="page.label.touchpoint.health.check" extendedScripts="true">

		<msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css" />

		<msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js" />
		<msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css" />
		
		<style>
			.fa-times {
				color: #dd4343;
			}
            td {
                background: #ffffff;
            }
		</style>

        <msgpt:Script>
            <script>
                function runAllHealthCheck(){
                    $("[id^='query_']").each(function(){
                        queryId = this.id.replace('query_','');
                        runHealthCheckByQueryId(queryId);
                    });
                }

                function queryHealthCheckTaskStatus(queryId) {
                    $.ajax({
                        type 		: 'GET',
                        url  		: context + '/healthCheck.form?queryId=' + queryId + '&documentId=${param.documentId}&checkStatus=true&cacheStamp=' + cacheStamp,
                        dataType 	: 'json',
                        success		: function(data){
                            var status = data.status;

                            if(status == 0) {	// Success
                                $('#queryStatus_'+queryId).removeClass('fa-spinner fa-pulse');
                                $('#queryStatus_'+queryId).removeClass('fa-times');
                                $('#queryStatus_'+queryId).addClass('fa-check');
                                toggleRowDrillDown($('#query_'+queryId), false);
                                $('#queryStatus_'+queryId).removeClass('txtFmtTip');
                                $('#queryStatus_'+queryId).removeAttr('title');

                                $('#runHealthCheckBtn_'+queryId).removeAttr('disabled');
                                $('#runHealthCheckBtn_'+queryId+'_button').removeAttr( 'disabled' );
                                $('tr#runHealthCheckBtn_'+queryId+'_button div.actionBtn').removeClass('actionBtn_disabled');
                            }else if(status == 1) {	// Failed
                                $('#queryStatus_'+queryId).removeClass('fa-spinner fa-pulse');
                                $('#queryStatus_'+queryId).removeClass('fa-check');
                                $('#queryStatus_'+queryId).addClass('fa-times');
                                $("#repairBtn_"+queryId+"_button").show();
                                if(data.hasDrillDown) {
                                    $("#openDrillDownBtn_"+queryId+"_button").show();
                                    toggleRowDrillDown($('#query_'+queryId), true);
                                }
                                if(data.failedMsg && data.failedMsg != ''){
                                    $('#queryStatus_'+queryId).addClass('txtFmtTip');
                                    $('#queryStatus_'+queryId).attr('title', "|<div class='txtFmtTipText'>"+data.failedMsg+"</div>");
                                    initTip($('#queryStatus_'+queryId));
                                }

                                $('#runHealthCheckBtn_'+queryId).removeAttr('disabled');
                                $('#runHealthCheckBtn_'+queryId+'_button').removeAttr( 'disabled' );
                                $('tr#runHealthCheckBtn_'+queryId+'_button div.actionBtn').removeClass('actionBtn_disabled');
                            }
                            else if(status == 2) {
                                if(data.hasOwnProperty('canQueryTaskStatus')) {
                                    var canQueryTaskStatus = data.canQueryTaskStatus;
                                    if (canQueryTaskStatus) {
                                        setTimeout(function () {
                                            queryHealthCheckTaskStatus(queryId);
                                        }, 1000);
                                    }
                                }
                            }
                        }
                    });
                }

                function runHealthCheckByQueryId(queryId){
                    $('#queryStatus_'+queryId).addClass('fa-spinner fa-pulse');
                    $('#runHealthCheckBtn_'+queryId).attr( 'disabled', true );
                    $("#repairBtn_"+queryId+"_button").hide();
                    $("#openDrillDownBtn_"+queryId+"_button").hide();
                    $.ajax({
                        type 		: 'GET',
                        url  		: context + '/healthCheck.form?queryId=' + queryId + '&documentId=${param.documentId}&cacheStamp=' + cacheStamp,
                        dataType 	: 'json',
                        success		: function(data) {
                            var status = data.status;

                            if(status == 0) {	// Success
                                $('#queryStatus_'+queryId).removeClass('fa-spinner fa-pulse');
                                $('#queryStatus_'+queryId).removeClass('fa-times');
                                $('#queryStatus_'+queryId).addClass('fa-check');
                                toggleRowDrillDown($('#query_'+queryId), false);
                                $('#queryStatus_'+queryId).removeClass('txtFmtTip');
                                $('#queryStatus_'+queryId).removeAttr('title');

                                $('#runHealthCheckBtn_'+queryId).removeAttr('disabled');
                            }else if(status == 1) {	// Failed
                                $('#queryStatus_'+queryId).removeClass('fa-spinner fa-pulse');
                                $('#queryStatus_'+queryId).removeClass('fa-check');
                                $('#queryStatus_'+queryId).addClass('fa-times');
                                $("#repairBtn_"+queryId+"_button").show();
                                if(data.hasDrillDown) {
                                    $("#openDrillDownBtn_"+queryId+"_button").show();
                                    toggleRowDrillDown($('#query_'+queryId), true);
                                }
                                if(data.failedMsg && data.failedMsg != '') {
                                    $('#queryStatus_'+queryId).addClass('txtFmtTip');
                                    $('#queryStatus_'+queryId).attr('title', "|<div class='txtFmtTipText'>"+data.failedMsg+"</div>");
                                    initTip($('#queryStatus_'+queryId));
                                }

                                $('#runHealthCheckBtn_'+queryId).removeAttr('disabled');
                            }
                            else if(status == 2) {
                                $('#queryStatus_'+queryId).addClass('fa-spinner fa-pulse');

                                $('#runHealthCheckBtn_'+queryId+'_button').attr( 'disabled', 'disabled' );
                                $('#runHealthCheckBtn_'+queryId).attr( 'disabled', true );
                                $('tr#runHealthCheckBtn_'+queryId+'_button div.actionBtn').addClass('actionBtn_disabled');

                                if(data.hasOwnProperty('canQueryTaskStatus')) {
                                    var canQueryTaskStatus = data.canQueryTaskStatus;
                                    if (canQueryTaskStatus) {
                                        setTimeout(function () {
                                            queryHealthCheckTaskStatus(queryId);
                                        }, 1000);
                                    }
                                }
                            }
                        }
                    });
                }

                function runRepairByQueryId(queryId, repairQueryId){
				$('#queryStatus_'+queryId).addClass('fa-spinner fa-pulse');
				$('#runHealthCheckBtn_'+queryId).attr('disabled','disabled');
				$("#repairBtn_"+queryId+"_button").hide();
				$("#openDrillDownBtn_"+queryId+"_button").hide();
				$.ajax({
					type 		: 'GET',
					url  		: context + '/healthCheck.form?queryId=' + repairQueryId + '&documentId=${param.documentId}&cacheStamp=' + cacheStamp,
					dataType 	: 'json',
					success		: function(data){
						var status = data.status;
						$('#queryStatus_'+queryId).removeClass('fa-spinner fa-pulse');
						if(status == 0){	// Success
							$('#queryStatus_'+queryId).removeClass('fa-times');
							$('#queryStatus_'+queryId).addClass('fa-check');
							toggleRowDrillDown($('#query_'+queryId), false);
							$('#queryStatus_'+queryId).removeClass('txtFmtTip');
							$('#queryStatus_'+queryId).removeAttr('title');
						}else if(status == 1){	// Failed
							$('#queryStatus_'+queryId).removeClass('fa-check');
							$('#queryStatus_'+queryId).addClass('fa-times');
							$("#repairBtn_"+queryId+"_button").show();
							if(data.hasDrillDown){
								$("#openDrillDownBtn_"+queryId+"_button").show();
								toggleRowDrillDown($('#query_'+queryId), true);
							}
							if(data.failedMsg && data.failedMsg != ''){
								$('#queryStatus_'+queryId).addClass('txtFmtTip');
								$('#queryStatus_'+queryId).attr('title', "|<div class='txtFmtTipText'>"+data.failedMsg+"</div>");
								initTip($('#queryStatus_'+queryId));
							}
						}
						$('#runHealthCheckBtn_'+queryId).removeAttr('disabled');
					}
				});
			}

			function openDrillDown(queryId) {
                    $('#query_'+queryId).find('.drill-down-open-icon').click();
                }

                function closeIFrame_local() {
	                	if ( window.opener != null ) {
		    				window.close();
		    			} else {
		    	 			var currentURL = getTopFrame().location.href;
		    	    			getTopFrame().location.href = currentURL;
		    			}
                }

                function crossLinkOpen(targetURL){
                    if(targetURL.indexOf('?') != -1)
                        targetURL += '&tk=${param.tk}';
                    else
                        targetURL += '?tk=${param.tk}';

                    if (window.opener != undefined && window.opener != null)
                        window.opener.location.href = context+"/"+targetURL;
                    else
                        getTopFrame().location.href = context+"/"+targetURL;
                }

                function openNewFrame() {
                    // Close health check toggle popup window (if exists): Brings window to forefront.  Firefox will
                    //		                            reopen behind the current window if popup is already open
                    if ($.browser.mozilla) {
                        healthCheckTogglePopup = window.open('','healthCheckTogglePopup','');
                        healthCheckTogglePopup.close();
                    }

                    windowOpenTargets.push({
                        path		: addParam(window.location.href,"windowType=stand_alone"),
                        name		: 'healthCheckTogglePopup',
                        attr		: 'directories=no, scrollbars=no, statusbar=no, status=no, toolbar=no, menubar=no, location=no, resizable=no, alwaysRaised=yes, ' +
                        'width=' + $(window.document).find('body').width() + ', height=' + $(window.document).find('body').height(),
                        afterOpen	: function() { closeIFrame_local(); }
                    });


                }

                $( function() {

                    $("[id^='category_']").each( function() {
                        var tdIndex = 0;
                        $(this).find('td').each( function() {
                            if (tdIndex != 0)
                                $(this).css({'border-left':'none'});
                            $(this).css({'background-color':'#f9f9f9'});
                            tdIndex++;
                        });
                    });

                    $("input:button").styleActionElement();
                    // Disable all the TRs for the drill down
                    $(".listTableTR").each(function(){
                        toggleRowDrillDown($(this), false);
                    });
                    $("tr[id^='repairBtn_']").hide();
                    $("tr[id^='openDrillDownBtn_']").hide();

                });
            </script>
        </msgpt:Script>
	</msgpt:HeaderNew>
	
	<msgpt:BodyNew type="minimal">
		<form:form method="post" modelAttribute="command">
			<form:errors path="*">
				<msgpt:Information errorMsgs="${messages}" type="error" />
			</form:errors>
			
			<div id="popupHeaderTitle" style="display: none;">
				<span class="titleText">
					<fmtSpring:message code="page.label.touchpoint.health.check"/>
				</span>
			</div>

            <msgpt:iFrameContainer>
                <msgpt:ContentPanel>
                    <div class="label" style="border-bottom: 1px solid #bbb;">
                        <!-- Buttons -->
                        <table cellspacing="0" cellpadding="0" border="0" width="100%"><tbody><tr>
                            <td style="vertical-align: middle;">
                                <div style="margin-left: 30px; font-size: 15px;">
                                    <fmtSpring:message code="page.label.touchpoint"/>:
                                    <span style="font-weight: bold; font-size: 15px;"><msgpt:TxtFmt maxLength="50">${document.name}</msgpt:TxtFmt></span>
                                </div>
                            </td>
                            <td width="1%" style="padding: 6px 3px;">
                                <msgpt:Button URL="javascript:runAllHealthCheck();" label="page.label.run.all" primary="true" />
                            </td>
                            <td width="1%" style="${param.windowType != 'stand_alone' ? 'padding: 6px 3px;' : 'padding: 6px 18px 6px 3px;' }">
                                <msgpt:Button URL="javascript:closeIFrame_local();" label="page.label.close" />
                            </td>
                            <c:if test="${param.windowType != 'stand_alone'}">
                                <td width="1%" style="padding: 6px 18px 6px 3px;">
                                    <div onclick="openNewFrame()" class="btn">
                                        <div class="detailTip" title="|<div class='detailTipText'>${msgpt:getMessage('page.text.open.in.new.window')}</div>" style="line-height:38px">
                                            <i class="fa fa-lg fa-external-link"></i>
                                        </div>
                                    </div>
                                </td>
                            </c:if>
                        </tr></tbody></table>
                    </div>

                    <div id="health-checks" class="health-checks">
                        <msgpt:DataTable id="healthChecksTable" drillDown="true" isTigerStriped="false" columnVisibility="false" columnReorder="false" staticData="true" >
                            <c:forEach var="queryCategory" items="${allQueryItems}" varStatus="forStatus">
                                <!-- Category definition -->
                                <c:set var="category" value="${queryCategory.key}" />
                                <msgpt:TableListGroup id="category_${category.id}" drillDownEnabled="false">
                                    <msgpt:TableElement align="left" label="page.label.health.checks" width="80%">
                                        <b><c:out value="${category.name}" /></b>
                                    </msgpt:TableElement>
                                    <msgpt:TableElement align="center" label="page.label.status" width="5%">
                                    </msgpt:TableElement>
                                    <msgpt:TableElement align="center" width="2%">
                                    </msgpt:TableElement>
                                </msgpt:TableListGroup>
                                <!-- Queries definitions -->
                                <c:set var="queryItems" value="${queryCategory.value}" />
                                <c:forEach var="queryItem" items="${queryItems}" varStatus="forStatus2">
                                    <msgpt:TableListGroup id="query_${queryItem.id}" iFrameId="health-check-detail" iFrameSrc="touchpoint_health_check_detail.form?queryId=${queryItem.drillDownQueryId}&documentId=${param.documentId}" drillDownEnabled="false">
                                        <msgpt:TableElement align="left" width="80%">
                                            <table><tr>
                                                <td style="border: none;">
                                                    &nbsp;&nbsp;&nbsp;&nbsp;<c:out value="${queryItem.name}" />
                                                </td>
                                                <c:choose>
                                                    <c:when test="${not empty queryItem.repairLink && queryItem.drillDownQueryId > 0}">
                                                        <td width="1%" style="border: none;">
                                                            <input title="${msgpt:getMessage('page.label.repair')}" type="button" id="repairBtn_${queryItem.id}" onclick="runRepairByQueryId(${queryItem.id},${queryItem.drillDownQueryId});" style="display: none;"/>
                                                        </td>
                                                    </c:when>
                                                    <c:when test="${not empty queryItem.repairLink}">
                                                        <td width="1%" style="border: none;">
                                                            <input title="${msgpt:getMessage('page.label.repair')}" type="button" id="repairBtn_${queryItem.id}" onclick="crossLinkOpen('${queryItem.repairLink}');" style="display: none;"/>
                                                        </td>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <td width="1%" style="border: none;">
                                                            <input title="${msgpt:getMessage('page.label.show.items')}" type="button" id="openDrillDownBtn_${queryItem.id}" onclick="openDrillDown('${queryItem.id}');" style="display: none;"/>
                                                        </td>
                                                    </c:otherwise>
                                                </c:choose>
                                            </tr></table>
                                        </msgpt:TableElement>
                                        <msgpt:TableElement align="center" width="5%">
                                            <i id="queryStatus_${queryItem.id}" class="fa ${queryItem.status == 2 ? 'fa-spinner fa-pulse' : ''}"></i>
                                        </msgpt:TableElement>
                                        <msgpt:TableElement align="center" width="2%">
                                            <input title="${msgpt:getMessage('page.label.run')}" type="button" id="runHealthCheckBtn_${queryItem.id}" onclick="runHealthCheckByQueryId(${queryItem.id});"  ${(queryItem.status == 2) ? 'disabled' : ''}/>
                                        </msgpt:TableElement>
                                    </msgpt:TableListGroup>
                                </c:forEach>
                            </c:forEach>
                        </msgpt:DataTable>
                    </div>
                </msgpt:ContentPanel>
            </msgpt:iFrameContainer>
		</form:form>
	</msgpt:BodyNew>
</msgpt:Html5>