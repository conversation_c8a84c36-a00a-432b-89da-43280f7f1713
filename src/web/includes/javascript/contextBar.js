$(function () {

    $('#contextBarHomeButton').on('click', function () {
        getTopFrame().location.href = context + "/index.jsp";
    });

    $("#workflowSetupActionContainer")
        .click(function () {
            getTopFrame().location.href = addParam($(this).find('i').data('workflowurl'), 'tk=' + getParam('tk'));
        });

    $("#globalContextContainer,.contextBar_touchpointContextDiabled:not(.contextBar_passive)")
        .click(function () {
            var currentURL = window.document.URL;
            var contextParam = 'globalContext';
            currentURL = replaceUrlParameter(currentURL, contextParam, $("#globalContextContainer").is('.globalContextEnabled') ? "false" : "true");
            getTopFrame().location.href = currentURL;
        });

    $(".contextBar_touchpointContextContainer").each(function () {
        if (!$("#globalContextContainer").is('.globalContextEnabled') && $('.contextBar_touchpointContextDiabled.contextBar_passive').length == 0) {  // REVIEW THIS PASSIVE WITH DAVE
            var docId = parseId($(this));

            if (docId == null)
                return;

            var tpCollectionId = -1;
            if (docId.indexOf('collection') != -1) {
                tpCollectionId = docId.split('_')[1];
                docId = -1;
            }
            $(this).iFrameMenu({
                width: 478,
                height: 672,
                horiOffset: -8,
                vertOffset: -48,
                src: "../contextbar/touchpoint_context_menu.form",
                appliedParams: {
                    documentId: docId, collectionId: tpCollectionId, tk: getParam('tk'),
                    adminView: $(this).is('.touchpointAdminContext'),
                    collectionContext: $(this).is('.collectionContext')
                },
                onMenuOpen: function (oMenu) {
                    $(oMenu.targetEle).addClass('active');
                },
                onMenuClose: function (oMenu) {
                    $(oMenu.targetEle).removeClass('active');
                },
                beforeMenuClose: function (oMenu, e) {
                    if (e) {
                        if ($(eventTarget(e)).closest('.popupFactory_popupContainer').length != 0 ||
                            $(eventTarget(e)).closest('.project_task_taskContainer').length != 0)
                            return false;
                        return true;
                    }
                    return false;
                }
            });
        }
    });

    $("#touchpointSetupActions").each(function () {

        var docId = -1;
        if (!$(".contextBar_touchpointContextContainer").is('#noTouchpointContext')) {
            $(".contextBar_touchpointContextContainer").each(function () {
                docId = parseId($(this));
            });
        }

        $(this).iFrameMenu({
            width: 300,
            height: 660,
            horiOffset: -48,
            vertOffset: -8,
            src: "../contextbar/touchpoint_setup_context_menu.form",
            appliedParams: {tk: getParam('tk'), documentId: docId},
            onMenuOpen: function (oMenu) {
                $(oMenu.targetEle).addClass('active');
            },
            onMenuClose: function (oMenu) {
                $(oMenu.targetEle).removeClass('active');
            },
            onSave: function (oMenu, frameURL) {
                return false;
            }
        });
    });

    $("[id^='contentSearchToggle']").each(function () {
        var type = parseId($(this));
        $(this).iFrameMenu({
            width: 332,
            height: 464,
            horiOffset: -124,
            vertOffset: -8,
            src: "../contextbar/search_context_menu.form",
            appliedParams: {searchType: type, tk: getParam('tk')},
            onMenuOpen: function (oMenu) {
                $(oMenu.targetEle).addClass('active');
            },
            onMenuClose: function (oMenu) {
                $(oMenu.targetEle).removeClass('active');
            },
            onSave: function (oMenu, frameURL) {
                return false;
            }
        });
    });

    $('#attributesToggle').click(function () {
        var docId = -1;
        $(".contextBar_touchpointContextContainer").each(function () {
            docId = parseId($(this));
        });

        if (!$(this).hasClass('actionBtn_disabled')) {
            $(this).iFramePopup({
                width: 820,
                title: client_messages.title.template_modifiers,
                src: "touchpoint_variant_template_modifiers_edit.form",
                appliedParams: {documentId: docId, touchpointSelectionId: -1, selectedIds: getParam('touchpointSelectionId') != '' ? getParam('touchpointSelectionId') : -1, tk: getParam('tk')},
                closeBtnId: "cancelBtn_button",
                displayOnInit: true,
                screenMask: true,
                draggable: false
            });
        }
    });

    $("#contentViewToggleSelectionList").find(".dropdown-item").on("click", function (e) {

        e.preventDefault();

        var $listItem = $(this),
            itemViewName = $listItem.data("viewname"),
            targetURL = "",
            tpSelectionId = getParam('touchpointSelectionId');

        if (tpSelectionId == '')
            tpSelectionId = -1;

        var URLparameters = "?documentId=" + getParam('documentId') +
            "&touchpointSelectionId=" + tpSelectionId +
            "&tk=" + getParam('tk');

        switch (itemViewName) {

            case "variants":
                targetURL = "touchpoint_variant_list.form" + URLparameters;
                break;

            case "localText":
                targetURL = "local_content_list.form" + URLparameters + "&localContext=1";
                break;

            case "localImage":
                targetURL = "local_content_list.form" + URLparameters + "&localContext=2";
                break;

            case "localCanvas":
                targetURL = "local_content_list.form" + URLparameters + "&localContext=1&isFreeform=true";
                break;

            case "messages":
                targetURL = "touchpoint_content_object_list.form" + URLparameters;
                break;

        }

        getTopFrame().location.href = context + "/touchpoints/" + targetURL;

    });


    $("[id^='selectionToggle']").each(function () {
        var selId = parseId($(this));
        $(this).iFrameMenu({
            width: 316,
            height: 464,
            horiOffset: -116,
            vertOffset: -8,
            src: "../contextbar/selection_toggle_context_menu.form",
            appliedParams: {
                touchpointSelectionId: selId,
                documentId: gup('documentId') !== "" ? gup('documentId') : -1,
                tk: getParam('tk'),
                localContext: getParam('localContext') != "" ? getParam('localContext') : 0,
                isFreeform: getParam('isFreeform') != "" ? getParam('isFreeform') : false
            },
            onMenuOpen: function (oMenu) {
                $(oMenu.targetEle).addClass('active');
            },
            onMenuClose: function (oMenu) {
                $(oMenu.targetEle).removeClass('active');
            },
            onSave: function (oMenu, frameURL) {
                return false;
            }
        });
    });


    $("[id^='selectionActions']").each(function () {
        var selId = parseId($(this));
        $(this).iFrameMenu({
            width: 240,
            height: $(document).height(),
            fullWidthIframe: true,
            horiOffset: -64,
            vertOffset: -8,
            src: "../contextbar/selection_actns_context_menu.form",
            appliedParams: {touchpointSelectionId: selId, tk: getParam('tk'), documentId: getParam('documentId')},
            onMenuOpen: function (oMenu) {
                $(oMenu.targetEle).addClass('active');

                $('.contextMenuPageScreen', getTopFrame().document).remove();
                $("body", getTopFrame().document).append("<div class=\"contextMenuPageScreen bg-transparent\"></div>");

                $('.contextMenuPageScreen', getTopFrame().document)
                    .css({
                        height: ($(getTopFrame().document).height())
                    })
                    .click(function () {
                        oMenu.closeAction();
                        $('.iFramePopupContainer').remove();
                    });
            },
            beforeMenuClose: function (oMenu, e) {
                if (e) {
                    if ($(eventTarget(e)).closest('.iFramePopupContainer,.popupFactory_popupContainer').length != 0)
                        return false;
                    if (getTopFrame().datePickerToken != undefined && getTopFrame().datePickerToken != null) {
                        getTopFrame().datePickerToken = null;
                        return false;
                    }
                    return true;
                }
                return false;
            },
            onMenuClose: function (oMenu) {
                $(oMenu.targetEle).removeClass('active');

                $('.contextMenuPageScreen', getTopFrame().document).remove();
                $('.iFramePopupContainer').remove();
            },
            onSave: function () {
                return false;
            }
        });
    });

    $("[id^='selectionStatusToggle']").each(function () {
        $(this)
            .mouseover(function () {
                if (!($(this).is('.actionBtn_toggleSelect,.actionBtnError_toggleSelect')) && !$(this).hasClass('actionBtn_disabled')) {
                    if ($(this).hasClass('actionBtnError')) {
                        $(this).removeClass('actionBtnError');
                        $(this).addClass('actionBtnError_hov');
                    } else {
                        $(this).removeClass('actionBtn');
                        $(this).addClass('actionBtn_hov');
                    }
                }
            })
            .mouseout(function () {
                if (!($(this).is('.actionBtn_toggleSelect,.actionBtnError_toggleSelect')) && !$(this).hasClass('actionBtn_disabled')) {
                    if ($(this).hasClass('actionBtnError_hov')) {
                        $(this).removeClass('actionBtnError_hov');
                        $(this).addClass('actionBtnError');
                    } else {
                        $(this).removeClass('actionBtn_hov');
                        $(this).addClass('actionBtn');
                    }

                }
            })
            .click(function () {
                if (!($(this).is('.actionBtn_toggleSelect,.actionBtnError_toggleSelect')) && !$(this).hasClass('actionBtn_disabled')) {
                    $(".actionBtnError_toggleSelect[id^='selectionStatusToggle']").removeClass('actionBtnError_toggleSelect').addClass('actionBtnError');
                    $(".actionBtn_toggleSelect[id^='selectionStatusToggle']").removeClass('actionBtn_toggleSelect').addClass('actionBtn');

                    if ($(this).is('.actionBtnError,.actionBtnError_hov')) {
                        $(this).removeClass('actionBtnError,actionBtnError_hov').addClass('actionBtnError_toggleSelect');
                    } else {
                        $(this).removeClass('actionBtn actionBtn_hov').addClass('actionBtn_toggleSelect');
                    }

                    var selectedContextValue = parseId($("[class$='toggleSelect'][id^='selectionStatusToggle']"));
                    var currentURL = window.document.URL;
                    var contextParam = 'currentSelectionStatusFilterId';

                    currentURL = removeParam(currentURL, "contentObjectId");
                    currentURL = removeParam(currentURL, "paramInstId");
                    currentURL = removeParam(currentURL, "statusViewId");
                    currentURL = removeParam(currentURL, "contentSelectionId");

                    currentURL = replaceUrlParameter(currentURL, contextParam, selectedContextValue);

                    getTopFrame().location.href = currentURL;
                }
            });
    });

    $("#touchpointChannelMenu").children().click(function () {

        var channelContextTarget = $(this).data('channel');

        $.ajax({
            type: "GET",
            url: context + "/context.form?type=setChannelContext&context=" + channelContextTarget +
            "&cacheStamp=" + cacheStamp +
            "&tk=" + getParam('tk'),
            dataType: "json",
            success: function (data) {
                getTopFrame().location = getTopFrame().location.href;
                getTopFrame().location.reload();
            }
        });

    });

    $("[id^='languageContext']").each(function () {
        var langId = parseId($(this));
        $(this).iFrameMenu({
            width: 230,
            height: 500,
            horiOffset: -68,
            vertOffset: -8,
            src: "../contextbar/language_context_menu.form",
            appliedParams: {languageId: langId, tk: getParam('tk')},
            onMenuOpen: function (oMenu) {
                $(oMenu.targetEle).addClass('active');
            },
            onMenuClose: function (oMenu) {
                $(oMenu.targetEle).removeClass('active');
            }
        });
    });

    $('#selectionStatusActive, #selectionStatusWorking').on('change', function () {

        var TARGET_STATUS_WORKING = 1,
            TARGET_STATUS_ACTIVE = 2,
            $this = $(this),
            param = ($this.is('#selectionStatusWorking')) ? TARGET_STATUS_WORKING : TARGET_STATUS_ACTIVE;

        if ($this.is(':checked')) {

            var currentURL = getTopFrame().document.URL,
                contextParam = 'currentSelectionStatusFilterId';

            currentURL = replaceUrlParameter(currentURL, contextParam, param);
            replaceUrlParameter(currentURL, contextParam, 2);
            getTopFrame().location.href = currentURL;

        }

    });

});

function getSelectionStatusId() {
    if ($('#selectionStatusActive').is(":checked")) {
        return 2;
    } else if ($('#selectionStatusWorking').is(':checked')) {
        return 1
    } else {
        return -1;
    }
}

function replaceUrlParameter(currentURL, contextParam, contextParamValue) {
    if ( contextParamValue != '' ) {
        if (currentURL.indexOf(contextParam) != -1) {
            var startIndex = currentURL.indexOf(contextParam);
            if (startIndex >= 0) {
                var endIndex = currentURL.indexOf('&', currentURL.indexOf(contextParam));
                if (endIndex == -1) {
                    endIndex = currentURL.length;
                }
                currentURL = currentURL.replace(currentURL.substring(startIndex - 1, endIndex), '');
            }
            if (currentURL.indexOf('&') != -1 && currentURL.indexOf('?') == -1)
                currentURL = currentURL.replace('&', '?');
        }
        currentURL = addParam(currentURL, contextParam + '=' + contextParamValue);
        currentURL = currentURL.replace('#', '');
    }
    return currentURL;
}