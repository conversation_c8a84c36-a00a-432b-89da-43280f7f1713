!function (e) {
    var t = {};

    function n(o) {
        if (t[o]) return t[o].exports;
        var r = t[o] = {i: o, l: !1, exports: {}};
        return e[o].call(r.exports, r, r.exports, n), r.l = !0, r.exports
    }

    n.m = e, n.c = t, n.d = function (e, t, o) {
        n.o(e, t) || Object.defineProperty(e, t, {enumerable: !0, get: o})
    }, n.r = function (e) {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {value: "Module"}), Object.defineProperty(e, "__esModule", {value: !0})
    }, n.t = function (e, t) {
        if (1 & t && (e = n(e)), 8 & t) return e;
        if (4 & t && "object" == typeof e && e && e.__esModule) return e;
        var o = Object.create(null);
        if (n.r(o), Object.defineProperty(o, "default", {
            enumerable: !0,
            value: e
        }), 2 & t && "string" != typeof e) for (var r in e) n.d(o, r, function (t) {
            return e[t]
        }.bind(null, r));
        return o
    }, n.n = function (e) {
        var t = e && e.__esModule ? function () {
            return e.default
        } : function () {
            return e
        };
        return n.d(t, "a", t), t
    }, n.o = function (e, t) {
        return Object.prototype.hasOwnProperty.call(e, t)
    }, n.p = "/", n(n.s = 0)
}([function (e, t, n) {
    e.exports = n(1)
}, function (e, t, n) {
    "use strict";
    n.r(t);
    var o = {
            name: null,
            iconClass: null,
            uniqueClassName: null,
            canBeSavedToLibrary: !1,
            settingsCssPath: "/assets/css/settings.css",
            previewCssPath: "/assets/css/preview.css",
            i18n: {},
            blockName: null,
            emptyContainerIcon: !1,
            blockType: null,
            blockConfigAttributeNames: [],
            controlsToCreate: [],
            blockControls: [],
            isEnabled: function () {
                return !0
            },
            emailInitialized: function (e) {
            },
            onSelectBlock: function (e) {
            },
            getBlockLayoutToDrop: function () {
                return "<td>Default block</td>"
            },
            blockDropped: function (e) {
            },
            getBlockLabel: function (e) {
                return "Default block"
            },
            getDefaultSettingsPanelState: function (e) {
                return {}
            },
            onCleanLayout: function (e, t) {
            },
            onBlockCopy: function (e, t) {
            }
        }, r = "".concat("mpZoneBlock", "_zoneName_control"), l = {
            en: {"block.name": "MP Zone", "settings.controls.zoneNameText.label": "Messagepoint Zone Name"},
            ru: {"block.name": "Зона сообщений", "settings.controls.zoneNameText.label": "Название зоны"}
        }, a = {
            name: r, BLOCK_UNIQUE_CLASS_LABEL: "esd-mp-zone-label", render: function () {
                this.jImage = this.jElement.find("img"), this.jLabel = this.jElement.find('span[class="esd-mp-zone-label"]'), this.jContainer.html(this.getBlockMarkup()), this.jAltInput = this.jContainer.find("#mbZoneName"), this.jAltInput.on("change", this.onChange.bind(this))
            }, onDeactivate: function () {
                this.jAltInput.off("change")
            }, isControlVisible: function () {
                return !0
            }, onChange: function (e) {
                console.log("Changing the Zone Name: " + e.target.value), console.log(this.jImage.html), console.log(this.jLabel.html), this.jImage.attr("alt", e.target.value || ""), this.jLabel.html(e.target.value || ""), this.applyChanges()
            }, getBlockMarkup: function () {
                return '\n            <div class="form-group">\n                <div class="col-xs-12">\n                    <label for="mbZoneName">'.concat(this.extension.stripoApi.translate("settings.controls.zoneNameText.label"), '</label>\n                    <input id="mbZoneName" type="text" class="form-control" value="').concat(this.jImage.attr("alt") || "", '">\n                </div>\n            </div>')
            }
        },
        i = '<td>\n  <table  align="center" cellpadding="0" cellspacing="0" width="100%" style="background-color:#7c1a87;">\n    <tbody>\n    <tr>\n      <td align="center">\n        <img src="#IMAGE_BASE_PATH#/assets/images/MPLogo.png" alt="MP ZONE NAME" width="80px" height="80px" />\n      </td>\n    </tr>\n    <tr>\n      <td align="center" >\n        <span class="esd-mp-zone-label" style="color:#FFFFFF;">MP ZONE NAME</span>\n      </td>\n    </tr>\n    </tbody>\n  </table>\n</td>\n';

    function c(e, t) {
        var n = Object.keys(e);
        if (Object.getOwnPropertySymbols) {
            var o = Object.getOwnPropertySymbols(e);
            t && (o = o.filter((function (t) {
                return Object.getOwnPropertyDescriptor(e, t).enumerable
            }))), n.push.apply(n, o)
        }
        return n
    }

    function s(e) {
        for (var t = 1; t < arguments.length; t++) {
            var n = null != arguments[t] ? arguments[t] : {};
            t % 2 ? c(Object(n), !0).forEach((function (t) {
                u(e, t, n[t])
            })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : c(Object(n)).forEach((function (t) {
                Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
            }))
        }
        return e
    }

    function u(e, t, n) {
        return t in e ? Object.defineProperty(e, t, {
            value: n,
            enumerable: !0,
            configurable: !0,
            writable: !0
        }) : e[t] = n, e
    }

    var p = {
        create: function (e, t, n) {
            return Object.assign(s(s({stripoConfig: e, stripoApi: t, extensionBasePath: n}, o), function (e, t, n) {
                return {
                    name: "MPZoneBlockExtension",
                    iconClass: "icon-mplogo",
                    uniqueClassName: "esd-mp-zone-block",
                    canBeSavedToLibrary: !1,
                    settingsCssPath: "/assets/css/settings.css",
                    previewCssPath: "/assets/css/preview.css",
                    i18n: l,
                    blockName: "block.name",
                    emptyContainerIcon: !1,
                    blockType: "block",
                    controlsToCreate: [{control: a}],
                    blockControls: [r],
                    getBlockLayoutToDrop: function () {
                        return i.replace(/#IMAGE_BASE_PATH#/g, n)
                    },
                    getBlockLabel: function (e) {
                        return t.translate("block.name")
                    },
                    emailInitialized: function (e) {
                        e.find(".".concat("esd-mp-zone-block")).each((function () {
                            t.jQuery(this).children().map((function () {
                                if ("IMG" == this.tagName.toUpperCase()) {
                                    var e = this.alt, t = this.parentNode, o = i.replace(/#IMAGE_BASE_PATH#/g, n);
                                    o = o.replace(/MP ZONE NAME/g, e), t.insertAdjacentHTML("beforeend", o, "text/html"), t.removeChild(this)
                                }
                            }))
                        }))
                    },
                    onCleanLayout: function (e, t) {
                        e.find(".".concat("esd-mp-zone-block")).each((function () {
                            var e = t(this, undefined, undefined, {decodeEntities: !1}).parent(), n = e.find("img").attr("alt");
                            e.find("span").html("%%mp_zone_name%%");
                            e.html("<td class=\"esd-mp-zone-block\"><img src=\"https://images/messagepoint_zone.gif\" alt=\"" + n + "\" title=\"" + n + "\" /></td>");
                        }))
                    }
                }
            }(0, t, n)))
        }
    };
    self.MpZoneBlockExtension = p
}]);