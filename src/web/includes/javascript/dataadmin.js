
function appSelected( select, pagename ) {
	var newURL = pagename;
	if ( select.selectedIndex != 0 ) {
		newURL = newURL + '?appid=' + select.options[select.selectedIndex].value;
	    javascriptHref( newURL );
	}
}

function appendParam( paramName, URL, concatChar ) {
	var paramValue = getParam( paramName );
	if ( (paramValue != null) && (paramValue != '') ) {
		URL = URL + concatChar + paramName + '=' + paramValue;
	}
	return URL;
}

function dataSourceSelected( select, pagename ) {
	var newURL = pagename;
	newURL = appendParam( 'appid', newURL, '?' );
	if ( select.selectedIndex != 0 ) {
		newURL = newURL + '&dsid=' + select.options[select.selectedIndex].value;
	}


	// test
	javascriptHref( newURL );
}

function dataRecordSelected( select, pagename ) {
	var newURL = pagename;
	if ( select.selectedIndex != 0 ) {
		newURL = appendParam( 'appid', newURL, '?' );
		newURL = appendParam( 'dsid', newURL, '&' );
		newURL = newURL + '&drid=' + select.options[select.selectedIndex].value;
	}
	javascriptHref( newURL );
}
