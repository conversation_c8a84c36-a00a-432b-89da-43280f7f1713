var onSelectColor; // Callback placehold

function initColorPicker(id){
	$('#colorpickerHolder_'+id).ColorPicker({
		flat: true,
		color: $('.colorPickerRawBinding_'+id).length > 0 ? $('.colorPickerRawBinding_'+id).val() : $('.colorPickerBinding_'+id).val(),
		onChange: function(hsb, hex, rgb, el) {
			$('#colorSelector_'+id+' div').css('backgroundColor', '#' + hex);
			$('.colorPickerBinding_'+id).val('#' + hex);
			$('.colorPickerRawBinding_'+id).val(hex);
			if ($.isFunction(onSelectColor))
				onSelectColor(hsb, hex, rgb, el);
		},

		onSubmit: function (hsb, hex, rgb, el) {
			$('#colorpickerHolder_'+id).stop().animate({height: 0}, 500);
		}
	});
}

(function($){
	var initLayout = function() {
		$('[id^="colorpickerHolder_"]').each(function(){
			var id = this.id.replace('colorpickerHolder_','');
			initColorPicker(id);
		});
		
		$(document).bind('click', function(e) {
			if ($(eventTarget(e)).closest('.colorSelector').length > 0) {
				var id = $(eventTarget(e)).closest('.colorSelector').attr('id').replace('colorSelector_','');
				$('.colorpickerHolder').stop().animate({height: 0}, 500);
				$('#colorpickerHolder_'+id).stop().animate({height: 173}, 500);
			} else if ( $(eventTarget(e)).closest('.colorpicker').length == 0 ) {
				$('.colorpickerHolder').stop().animate({height: 0}, 500);
			}
		});
	};

	EYE.register(initLayout, 'init');
})(jQuery)