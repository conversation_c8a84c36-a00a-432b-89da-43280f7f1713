var onSelectColor; // Callback placehold

(function($){
	var initLayout = function() {

		$('#colorpickerHolder').ColorPicker({flat: true});
		
		var holder2color = "#000000";
		if ( $('.colorPickerBinding2').is('.rgb_value') || $('.colorPickerRawBinding2').is('.rgb_value') ) {
			var colorValue = $('.colorPickerRawBinding2').length > 0 ? $('.colorPickerRawBinding2').val() : $('.colorPickerBinding2').val();
			if ( colorValue.split(',').length != 3 )
				holder2color = { r: 0 , g: 0, b: 0 };
			else
				holder2color = { r: colorValue.split(',')[0] , g: colorValue.split(',')[1], b: colorValue.split(',')[2] };
		} else {
			holder2color = $('.colorPickerRawBinding2').length > 0 ? $('.colorPickerRawBinding2').val() : $('.colorPickerBinding2').val();
		}

		$('#colorpickerHolder2').ColorPicker({
			flat: true,
			color: holder2color,
			onChange: function(hsb, hex, rgb, el) {
				$('#colorSelector2 div').css('backgroundColor', '#' + hex);
				if ( $('.colorPickerBinding2').is('.rgb_value') || $('.colorPickerRawBinding2').is('.rgb_value') ) {
					$('.colorPickerBinding2').val(rgb.r + ',' + rgb.g + ',' + rgb.b);
					$('.colorPickerRawBinding2').val(rgb.r + ',' + rgb.g + ',' + rgb.b);
				} else {
					$('.colorPickerBinding2').val('#' + hex);
					$('.colorPickerRawBinding2').val(hex);
				}
				if ($.isFunction(onSelectColor))
					onSelectColor(hsb, hex, rgb, el);
			},

			onSubmit: function (hsb, hex, rgb, el) {
				if ( $('#colorpickerHolder2').is('.open_left') )
					$('#colorpickerHolder2').stop().animate({height: 0, left: 0, width: 0}, 500);
				else
					$('#colorpickerHolder2').stop().animate({height: 0}, 500);
			}
		});
		$('#colorpickerHolder2>div').css('position', 'absolute');
		if ( $('#colorpickerHolder2').is('.open_left') )
			$('#colorpickerHolder2').css({height: 0, left: 0, width: 0});
		else
			$('#colorpickerHolder2').css({height: 0});
		$(document).bind('click', function(e) {
			if ($(eventTarget(e)).closest('#colorSelector2').length > 0) {
				if ( $('#colorpickerHolder2').is('.open_left') )
					$('#colorpickerHolder2').stop().animate({height: 173, left: -320, width: 356}, 500);
				else
					$('#colorpickerHolder2').stop().animate({height: 173}, 500);
			} else if ( $(eventTarget(e)).closest('.colorpicker').length == 0 ) {
				if ( $('#colorpickerHolder2').is('.open_left') )
					$('#colorpickerHolder2').stop().animate({height: 0, left: 0, width: 0}, 500);
				else
					$('#colorpickerHolder2').stop().animate({height: 0}, 500);
			}
		});

		$('#colorpickerField1, #colorpickerField2, #colorpickerField3').ColorPicker({
			onSubmit: function(hsb, hex, rgb, el) {
				$(el).val(hex);
				$(el).ColorPickerHide();
			},
			onBeforeShow: function () {
				$(this).ColorPickerSetColor(this.value);
			}
		})
		.bind('keyup', function(){
			$(this).ColorPickerSetColor(this.value);
		});

		$('#colorSelector').ColorPicker({
			color: '#0000ff',
			onShow: function (colpkr) {
				$(colpkr).fadeIn(500);
				return false;
			},
			onHide: function (colpkr) {
				$(colpkr).fadeOut(500);
				return false;
			},
			onChange: function (hsb, hex, rgb) {
				$('#colorSelector div').css('backgroundColor', '#' + hex);
			}
		});
	};

	EYE.register(initLayout, 'init');
})(jQuery)