#colorSelector {
	position: relative;
	width: 36px;
	height: 36px;
	background: url(../images/select.png);
}
#colorSelector div {
	position: absolute;
	top: 3px;
	left: 3px;
	width: 30px;
	height: 30px;
	background: url(../images/select.png) center;
}
#colorSelector2 {
	position: absolute;
	top: 0;
	left: 0;
	width: 36px;
	height: 36px;
	background: url(../images/select2.png);
}
#colorSelector2 div {
	position: absolute;
	top: 4px;
	left: 4px;
	width: 28px;
	height: 28px;
	background: url(../images/select2.png) center;
}
#colorpickerHolder2 {
	top: 32px;
	left: 0;
	width: 356px;
	height: 0;
	overflow: hidden;
	position: absolute;
}
#colorpickerHolder2 .colorpicker {
	background-image: url(../images/custom_background.png);
	position: absolute;
	bottom: 0;
	left: 0;
}
#colorpickerHolder2 .colorpicker_hue div {
	background-image: url(../images/custom_indic.gif);
}
#colorpickerHolder2 .colorpicker_hex {
	background-image: url(../images/custom_hex.png);
}
#colorpickerHolder2 .colorpicker_rgb_r {
	background-image: url(../images/custom_rgb_r.png);
}
#colorpickerHolder2 .colorpicker_rgb_g {
	background-image: url(../images/custom_rgb_g.png);
}
#colorpickerHolder2 .colorpicker_rgb_b {
	background-image: url(../images/custom_rgb_b.png);
}
#colorpickerHolder2 .colorpicker_hsb_s {
	background-image: url(../images/custom_hsb_s.png);
	display: none;
}
#colorpickerHolder2 .colorpicker_hsb_h {
	background-image: url(../images/custom_hsb_h.png);
	display: none;
}
#colorpickerHolder2 .colorpicker_hsb_b {
	background-image: url(../images/custom_hsb_b.png);
	display: none;
}
#colorpickerHolder2 .colorpicker_submit {
	background-image: url(../images/custom_submit.png);
}
#colorpickerHolder2 .colorpicker input {
	color: #778398;
}
#customWidget {
	position: relative;
	height: 36px;
}
