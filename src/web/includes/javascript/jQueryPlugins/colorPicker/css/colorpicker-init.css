.colorSelector {
	position: absolute;
	top: 0;
	left: 0;
	width: 36px;
	height: 36px;
	background: url(../images/select2.png);
}
.colorSelector div {
	position: absolute;
	top: 4px;
	left: 4px;
	width: 28px;
	height: 28px;
	background: url(../images/select2.png) center;
}
.colorpickerHolder {
	top: 32px;
	left: 0;
	width: 356px;
	height: 0;
	overflow: hidden;
	position: absolute;
}
.colorpickerHolder .colorpicker {
	background-image: url(../images/custom_background.png);
	position: absolute;
	bottom: 0;
	left: 0;
	z-index: 1;
}
.colorpickerHolder .colorpicker_hue div {
	background-image: url(../images/custom_indic.gif);
}
.colorpickerHolder .colorpicker_hex {
	background-image: url(../images/custom_hex.png);
}
.colorpickerHolder .colorpicker_rgb_r {
	background-image: url(../images/custom_rgb_r.png);
}
.colorpickerHolder .colorpicker_rgb_g {
	background-image: url(../images/custom_rgb_g.png);
}
.colorpickerHolder .colorpicker_rgb_b {
	background-image: url(../images/custom_rgb_b.png);
}
.colorpickerHolder .colorpicker_hsb_s {
	background-image: url(../images/custom_hsb_s.png);
	display: none;
}
.colorpickerHolder .colorpicker_hsb_h {
	background-image: url(../images/custom_hsb_h.png);
	display: none;
}
.colorpickerHolder .colorpicker_hsb_b {
	background-image: url(../images/custom_hsb_b.png);
	display: none;
}
.colorpickerHolder .colorpicker_submit {
	background-image: url(../images/custom_submit.png);
}
.colorpickerHolder .colorpicker input {
	color: #778398;
}
.customWidget {
	position: relative;
	height: 36px;
}
