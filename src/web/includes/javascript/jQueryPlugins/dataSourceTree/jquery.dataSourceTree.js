'use strict';
(function ($, window, document, _, undefined) {
    var instances = {};
    var defaults = {
        context: '',
        data: {
            nodeList: [],
            tree: null
        },
        dataSourceId: '',
        dataSourceUrl: '',
        selectedNodeId: -1,
        state: {
            activeHoverPopup: null,
            activeHoverPopupInUse: false,
            activeTreeElementId: '',
            nodeType: '',
            contextMenuVisible: false,
            currentPopup: null,
            currentPopupInstanceId: '',
            showChildrenAsTable: true,
            filterOptions: {
                anonymized: true,
                filterText: '',
                sortBy: 'name'
            },
            mouseX: 0,
            mouseY: 0,
            renderSelectedNode: true,
            scrollPosition: {
                X: window.scrollX,
                Y: window.scrollY
            },
            selectedNode: null
        },
        strings: {},
        typeSpecificHandler: null,
        isViewOnly: true
    };
    var DataSourceTree = /** @class */ (function () {
        function DataSourceTree(element, options) {
            this._openNodeCtr = 0;
            this._refreshOpenCtr = 0;
            this.jsTree = null;
            if (options.typeSpecificHandler != null) {
                this.settings = $.extend({}, defaults, options);
                this.element = $(element);
                this.container = $(element).parent();
                this._name = $(element).attr('id');
                this._typeImplementation = options.typeSpecificHandler;
                this._typeImplementation.setSettings(this.settings);
                this._typeImplementation.setCommon(this);
                this.initialize();
                this.initDocumentEvents();
            }
        }
        DataSourceTree.getDataTree = function (url) {
            return $.post(url);
        };
        //region DataSourceTreeAPI Implementation
        DataSourceTree.prototype.addNodeHoverPopup = function (node, contentUrl, template, variableTemplate) {
            contentUrl += '&dsid=' + this.settings.dataSourceId;
            CommonImplementation.attachHoverPopup(node, contentUrl, this.settings, template, variableTemplate);
        };
        DataSourceTree.prototype.applyFilter = function (filterOptions) {
            this.settings.state.filterOptions = filterOptions;
            this.saveScrollPosition();
            this._typeImplementation.applyFilter();
            this.restoreScrollPosition();
        };
        DataSourceTree.prototype.closePopups = function () {
            if (this.settings.state.currentPopup != null) {
                $.popupFactory.get(this.settings.state.currentPopup).closePopup();
            }
            if (this.settings.state.activeHoverPopup != null) {
                $.popupFactory.get(this.settings.state.activeHoverPopup).closePopup();
            }
        };
        DataSourceTree.prototype.filterAndSortDataElements = function (data, filterOptions) {
            var results = data;
            if (filterOptions.anonymized === false) {
                results = _.filter(results, function (x) { return x.isAnonymized !== true; });
            }
            else if (filterOptions.anonymized == null) {
                results = _.filter(results, function (x) { return x.isAnonymized === true; });
            }
            if (!_.isEmpty(filterOptions.filterText)) {
                results = _.filter(results, function (x) { return x.name.toLowerCase().indexOf(filterOptions.filterText) !== -1; });
            }
            results = _.sortBy(results, filterOptions.sortBy);
            return results;
        };
        DataSourceTree.prototype.refresh = function (restoreSelection) {
            var _this = this;
            return this.getDataSourceTreeJson().then(function () {
                var tree;
                if (_this.jsTree == null) {
                    tree = _this.initJSTree();
                }
                else {
                    tree = _this.jsTree;
                }
                tree.settings.core.data = _this.settings.data.tree;
                if (restoreSelection) {
                    _this.settings.selectedNodeId = _this.settings.state.selectedNode.id;
                }
                _this.container.css('height', _this.container.height() + 'px');
                _this._refreshOpenCtr = _this._openNodeCtr;
                _this._openNodeCtr = 0;
                _this.element.fadeOut('fast', function () { return tree.refresh(true, false); });
            });
        };
        DataSourceTree.prototype.restoreScrollPosition = function () {
            var position = this.settings.state.scrollPosition;
            window.scrollTo(position.X, position.Y);
        };
        DataSourceTree.prototype.saveScrollPosition = function () {
            this.settings.state.scrollPosition = {
                X: window.scrollX,
                Y: window.scrollY
            };
        };
        DataSourceTree.prototype.setShowChildrenAsTable = function (value) {
            this.settings.state.showChildrenAsTable = value;
            this.applyFilter(this.settings.state.filterOptions);
        };
        DataSourceTree.prototype.triggerPopup = function (menuItem) {
            this.saveScrollPosition();
            menuItem.displayOnInit = true;
            menuItem.closeBtnId = 'cancelBtn_button';
            menuItem.closeBeforeOnSave = true;
            menuItem.appliedParams.tk = gup('tk');
            $('#context-menu-action-type-id').iFramePopup(menuItem);
        };
        //endregion
        DataSourceTree.prototype.getDataSourceTreeJson = function () {
            var _this = this;
            return DataSourceTree
                .getDataTree(this.settings.dataSourceUrl)
                .done(function (tree) {
                if (typeof tree !== 'undefined') {
                    _this.settings.data.tree = tree;
                    _this.settings.data.nodeList = CommonImplementation.flattenDataTree(tree);
                }
            });
        };
        DataSourceTree.prototype.initialize = function () {
            var _this = this;
            this.getDataSourceTreeJson().done(function () {
                _this.jsTree = _this.initJSTree();
                _this.attachJSTreeEvents();
            });
        };
        DataSourceTree.prototype.initJSTree = function () {
            var _this = this;
            var jsTreeOptions = {
                'state': {
                    'key': 'datasources_tree_state_' + this.settings.dataSourceId
                },
                'core': {
                    'animation': 0,
                    'data': this.settings.data.tree,
                    'themes': {
                        'name': 'proton',
                        'responsive': false,
                    },
                },
                'plugins': ['wholerow', 'state']
            };
            if (!this.settings.isViewOnly) {
                jsTreeOptions['contextmenu'] = {
                    'items': function () { return _this.getContextMenuOptions(); },
                    'show_at_node': false
                };
                jsTreeOptions.plugins.push('contextmenu');
            }
            $(this.element).jstree(jsTreeOptions);
            return $.jstree.reference(this.element);
        };
        DataSourceTree.prototype.attachJSTreeEvents = function () {
            var _this = this;
            var tree = $(this.element);
            tree.on('ready.jstree after_open.jstree redraw.jstree', function (event) {
                _this.closePopups();
                _this.renderJsTreeNodes();
                if (event.type === 'redraw' && _this._openNodeCtr === _this._refreshOpenCtr) {
                    _this.container.css('height', 'auto');
                    _this.element.fadeIn('slow');
                    _this._refreshOpenCtr = 0;
                }
                else if (event.type === 'open') {
                    _this._openNodeCtr++;
                }
                if (_this.settings.selectedNodeId !== -1) {
                    var target = _.find(_this.settings.data.nodeList, function (x) { return x.id.toString().localeCompare(_this.settings.selectedNodeId.toString()) === 0; });
                    $.jstree.reference(tree).select_node(target);
                }
            });
            tree.on('after_close.jstree', function () { return _this.closePopups(); });
            tree.on('show_contextmenu.jstree', function (e, data) {
                var target = _.find(_this.settings.data.nodeList, function (x) { return x.id.toString().localeCompare(data.node.id) === 0; });
                _this.closePopups();
                _this.showContextMenu(target);
            });
            tree.on('select_node.jstree', function (e, data) {
                _this.settings.state.selectedNode = data.node;
            });
            tree.on('hover_node.jstree', function (e, data) { return CommonImplementation.hoverNode(data.node.id); });
            tree.on('dehover_node.jstree', function (e, data) { return CommonImplementation.dehoverNode(data.node.id); });
        };
        DataSourceTree.prototype.showContextMenu = function (target) {
            window.getSelection().removeAllRanges();
            this._typeImplementation.setActiveNode(target);
            var contextMenu = $('.jstree-contextmenu');
            contextMenu.css({ left: this.settings.state.mouseX, top: this.settings.state.mouseY });
        };
        DataSourceTree.prototype.getContextMenuOptions = function () {
            var _this = this;
            var menuOptions = {};
            _.each(this._typeImplementation.getContentMenuOptions(), function (item, index) {
                menuOptions['item' + index] = {
                    action: function () { return _this.triggerPopup(_this._typeImplementation.getContextMenuItem(item)); },
                    label: item
                };
            });
            return menuOptions;
        };
        DataSourceTree.prototype.initDocumentEvents = function () {
            var _this = this;
            $(document)
                .on('mousemove', function (event) {
                _this.settings.state.mouseX = event.pageX;
                _this.settings.state.mouseY = event.pageY;
                if (_this.settings.state.activeHoverPopup != null) {
                    var distance = _this.calculateHorizonalDistance($(_this.settings.state.activeHoverPopup));
                    if (distance > 50 && !_this.settings.state.activeHoverPopupInUse) {
                        $.popupFactory.get(_this.settings.state.activeHoverPopup).closePopup();
                    }
                }
            })
                .on('context_show.vakata', function () { return _this.settings.state.contextMenuVisible = true; })
                .on('context_hide.vakata', function () { return _this.settings.state.contextMenuVisible = false; });
        };
        DataSourceTree.prototype.calculateHorizonalDistance = function (element) {
            return Math.floor(Math.sqrt(Math.pow(this.settings.state.mouseY - (element.offset().top + (element.height() / 2)), 2)));
        };
        DataSourceTree.prototype.renderJsTreeNodes = function () {
            var _this = this;
            var scrollToView = null;
            $('.jstree-anchor').each(function (event, node) {
                var target = _.find(_this.settings.data.nodeList, function (x) { return node.id.replace('_anchor', '').localeCompare(x.id) === 0; });
                if (!_.isUndefined(target) && !$(node).hasClass('dataSourceTreeNode')) {
                    var newView = CommonImplementation.renderNode(target, _this._typeImplementation);
                    _this._typeImplementation.attachEvents(target, newView, _this.settings.state.renderSelectedNode && target.extras.selected);
                    if (target.extras.selected === true) {
                        scrollToView = $(newView);
                    }
                }
            });
            if (this.settings.state.renderSelectedNode && scrollToView != null) {
                this.settings.state.renderSelectedNode = false;
                $('html, body').animate({
                    scrollTop: scrollToView.offset().top
                }, 2000);
            }
            $('.jstree-node').css('height', 'auto');
        };
        return DataSourceTree;
    }());
    var CommonImplementation = /** @class */ (function () {
        function CommonImplementation() {
        }
        CommonImplementation.renderNode = function (node, typeSpecific) {
            var html = typeSpecific.renderNodeHtml(node);
            var element = $(html);
            var anchor = $('#' + node.id + '_anchor');
            anchor.empty();
            anchor.append(element);
            anchor.css('height', 'auto');
            anchor.addClass('dataSourceTreeNode');
            return element;
        };
        CommonImplementation.hoverNode = function (id) {
            $('#nodeSummaryLabel_' + id + '.nodeLabelDisabled').removeClass('nodeLabelDisabled').addClass('nodeLabelDisabledHover');
        };
        CommonImplementation.dehoverNode = function (id) {
            $('#nodeSummaryLabel_' + id + '.nodeLabelDisabledHover').removeClass('nodeLabelDisabledHover').addClass('nodeLabelDisabled');
        };
        CommonImplementation.flattenDataTree = function (data, list) {
            var _this = this;
            var nodeList = arguments[1] || [];
            if (data instanceof Array) {
                _.each(data, function (node) { return _this.flattenDataTree(node, nodeList); });
            }
            else {
                nodeList.push({
                    extras: data.extras,
                    id: data.id,
                    text: data.text,
                });
                if (data.children instanceof Array) {
                    _.each(data.children, function (node) { return _this.flattenDataTree(node, nodeList); });
                }
            }
            return nodeList;
        };
        CommonImplementation.attachHoverPopup = function (node, contentUrl, settings, template, variableTemplate) {
            var summaryLabel = $('#nodeDisplayDataSummary_' + node.id);
            summaryLabel.hoverIntent(function () {
                if (!settings.state.contextMenuVisible) {
                    settings.state.activeHoverPopup = $('#nodeSummaryLabel_' + node.id).popupFactory({
                        asyncSetContentHandler: function (o, data) {
                            var content = '';
                            var variableContent = '';
                            if (data.hasVariables) {
                                variableContent = '<div>';
                                _.each(data.variables, function (x) { return variableContent += variableTemplate(x); });
                                variableContent += '</div>';
                            }
                            data.variableContent = variableContent;
                            content += template($.extend({}, settings.strings, data));
                            return '<div style="margin: 10px; text-align: left">' + content + '</div>';
                        },
                        asyncSetContentURL: contentUrl
                            + '&cacheStamp=' + (stampDate.getTime()),
                        beforePopupClose: function () { return settings.state.activeHoverPopup = null; },
                        popupLocation: 'right',
                        title: client_messages.title.summary,
                        trigger: 'instant',
                        width: 350,
                    });
                    var instance = $.popupFactory.get(settings.state.activeHoverPopup);
                    $(instance.popupEle).on('mouseover', function () { return settings.state.activeHoverPopupInUse = true; });
                    $(instance.popupEle).on('mouseout', function () { return settings.state.activeHoverPopupInUse = false; });
                }
            }, function () { return; });
        };
        return CommonImplementation;
    }());
    $.fn.dataSourceTree = function (options, data) {
        var result = null;
        if (_.isUndefined(this[0])) {
            return $(this);
        }
        if (!_.has(instances, this[0].id)) {
            instances[this[0].id] = new DataSourceTree(this, options);
            $(document).trigger('dataSourceTree.ready');
        }
        if (_.isString(options)) {
            result = instances[this[0].id][options](data);
        }
        else if (_.isUndefined(options)) {
            result = instances[this[0].id];
        }
        return result || $(this);
    };
})(jQuery, window, document, _);
