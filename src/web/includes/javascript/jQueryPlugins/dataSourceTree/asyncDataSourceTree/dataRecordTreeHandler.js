(function () {
    var DataRecordSourceHandler = /** @class */ (function () {
        function DataRecordSourceHandler() {
            this.elementListVisible = {};
            this.elementListData = {};
            this.elementTemplate = Handlebars.compile($('#nodeDisplayElementView').html());
            this.nodeDisplayDataTemplate = Handlebars.compile($('#nodeDisplayDataTemplate').html());
            this.summaryTemplate = Handlebars.compile($('#nodeDisplaySummaryView').html());
            this.recordTemplate = Handlebars.compile($('#nodeDisplayRecordView').html());
            this.variableTemplate = Handlebars.compile($('#nodeDisplayVariableView').html());
        }
        DataRecordSourceHandler.prototype.applyFilter = function () {
            for (var nodeId in this.elementListVisible) {
                if (this.elementListVisible[nodeId]) {
                    $('#nodeDisplayDetailView_' + nodeId).remove();
                    this.renderElementList(nodeId);
                }
            }
        };
        DataRecordSourceHandler.prototype.renderNodeHtml = function (node) {
            var data = $.extend({}, this.settings.strings, node);
            return this.nodeDisplayDataTemplate(data);
        };
        DataRecordSourceHandler.prototype.attachEvents = function (node, element, openSelected) {
            var _this = this;
            element.on('mouseover', function () {
                if (!_this.settings.state.contextMenuVisible) {
                    _this.settings.state.activeTreeElementId = 'nodeDisplayData_' + node.id;
                    _this.activeDataRecord = node.id;
                }
            });
            $('#nodeDisplayDataSummary_' + node.id).on('click', function () {
                var editArgs = _this.getContextMenuItem(_this.settings.strings.page.label._edit);
                _this.common.triggerPopup(editArgs);
            });
            this.initAttributeDetails(node.id);
            this.attachHoverPopup(node);
            if (this.elementListVisible[node.id.toString()] || (openSelected && node.extras.selected)) {
                this.renderElementList(node.id, openSelected);
            }
        };
        DataRecordSourceHandler.prototype.getContentMenuOptions = function () {
            var strings = this.settings.strings;
            return [strings.page.label._edit, strings.page.label.add.element, strings.page.label.delete];
        };
        DataRecordSourceHandler.prototype.getContextMenuItem = function (action) {
            var _this = this;
            var forElement = this.settings.state.activeTreeElementId.startsWith('nodeDisplayElement');
            var onSaveCallback = _.wrap(customCallback, function (callback, iframe) {
                delete _this.elementListData[_this.activeDataRecord];
                callback(iframe);
                _this.common.refresh();
            });
            if (action === this.settings.strings.page.label.add.element) {
                return {
                    appliedParams: {
                        'new': true,
                        drid: this.activeDataRecord,
                        dsid: this.settings.dataSourceId,
                        cacheStamp: new Date().getTime()
                    },
                    onSave: onSaveCallback,
                    src: 'data_element_edit.form',
                    title: client_messages.title.insert_data_element,
                    width: 800,
                };
            }
            else if (forElement && action === this.settings.strings.page.label._edit) {
                return {
                    appliedParams: {
                        deid: this.activeDataElement,
                        drid: this.activeDataRecord,
                        dsid: this.settings.dataSourceId,
                        cacheStamp: new Date().getTime()
                    },
                    onSave: onSaveCallback,
                    src: 'data_element_edit.form',
                    title: client_messages.title.edit_data_element,
                    width: 800,
                };
            }
            else if (forElement && action === this.settings.strings.page.label.delete) {
                return {
                    appliedParams: {
                        deid: this.activeDataElement,
                        drid: this.activeDataRecord,
                        dsid: this.settings.dataSourceId,
                        parameter: 'deid',
                        successParameter: 'drid',
                        cacheStamp: new Date().getTime()
                    },
                    height: 100,
                    onSave: onSaveCallback,
                    src: 'data_element_delete.form',
                    title: client_messages.title.delete_data_element,
                    width: 800,
                };
            }
            else if (!forElement && action === this.settings.strings.page.label._edit) {
                return {
                    appliedParams: {
                        drid: this.activeDataRecord,
                        dsid: this.settings.dataSourceId,
                        parameter: 'drid',
                        successParameter: 'dsid'
                    },
                    height: 200,
                    onSave: onSaveCallback,
                    src: 'data_record_edit.form',
                    title: client_messages.title.edit_data_record,
                    width: 800,
                };
            }
            else if (!forElement && action === this.settings.strings.page.label.delete) {
                return {
                    appliedParams: {
                        cancelView: 'datarecords.jsp',
                        drid: this.activeDataRecord,
                        dsid: this.settings.dataSourceId,
                        parameter: 'drid',
                        successParameter: 'dsid',
                    },
                    height: 100,
                    onSave: onSaveCallback,
                    src: 'data_record_delete.form',
                    title: client_messages.title.delete_deta_record,
                    width: 800,
                };
            }
            throw 'invalid context menu item: ' + action;
        };
        DataRecordSourceHandler.prototype.setActiveNode = function (value) {
            this.activeDataRecord = value.id;
        };
        DataRecordSourceHandler.prototype.setCommon = function (value) {
            if (value != null) {
                this.common = value;
            }
        };
        DataRecordSourceHandler.prototype.setSettings = function (value) {
            if (value != null) {
                this.settings = value;
            }
        };
        DataRecordSourceHandler.prototype.initAttributeDetails = function (nodeId) {
            var _this = this;
            $('#attributeExpandIcon_' + nodeId).on('click', function () {
                _this.common.closePopups();
                var detailView = $('#nodeDisplayDetailView_' + nodeId);
                if (detailView.length > 0) {
                    $('#attributeExpandIcon_' + nodeId).removeClass('fa-minus-square').addClass('fa-plus-square');
                    var summary = void 0;
                    if (_this.elementListData.hasOwnProperty(nodeId)) {
                        summary = "".concat(_this.elementListData[nodeId].length, " ").concat(_this.settings.strings.page.label.elements);
                    }
                    else {
                        summary = "".concat(_this.settings.strings.page.label.elements);
                    }
                    $('#detailElements_' + nodeId).text(summary);
                    _this.elementListVisible[nodeId] = false;
                    detailView.remove();
                }
                else {
                    _this.renderElementList(nodeId);
                }
            });
        };
        DataRecordSourceHandler.prototype.attachHoverPopup = function (node) {
            var contentUrl = this.settings.context + 'dataSourceTree.form?method=datarecordsummary&drid=' + node.id;
            this.common.addNodeHoverPopup(node, contentUrl, this.summaryTemplate, this.variableTemplate);
        };
        DataRecordSourceHandler.prototype.renderElementList = function (nodeId, openSelected) {
            var _this = this;
            $('#attributeExpandIcon_' + nodeId).removeClass('fa-plus-square').addClass('fa-spinner fa-spin');
            this.elementListVisible[nodeId] = true;
            var newDetailView = document.createElement('div');
            newDetailView.id = 'nodeDisplayDetailView_' + nodeId;
            newDetailView.style.marginLeft = '1.5rem';
            newDetailView.style.height = 'auto';
            newDetailView.style.color = '#000';
            $('#' + nodeId + '_anchor').append(newDetailView);
            return this.asyncGetElementValues(nodeId).done(function (data) {
                var detailsView = $('#nodeDisplayDetailView_' + nodeId);
                if (_.isArray(data) && data.length === 0) {
                    detailsView.remove();
                    delete _this.elementListVisible[nodeId];
                    delete _this.elementListData[nodeId];
                    return;
                }
                var maxWidth = 0;
                _this.elementListData[nodeId] = data;
                var startCount = data.length;
                var displayData = _this.common.filterAndSortDataElements(data, _this.settings.state.filterOptions);
                var isFiltered = startCount !== displayData.length;
                if (_this.settings.state.showChildrenAsTable) {
                    var elementTable = document.createElement('table');
                    var thead = elementTable.createTHead();
                    var tbody_1 = elementTable.createTBody();
                    elementTable.classList.add('table', 'table-hover');
                    var theadRow = thead.insertRow();
                    theadRow.insertCell().appendChild(document.createTextNode(_this.settings.strings.page.label.name));
                    theadRow.insertCell().appendChild(document.createTextNode(_this.settings.strings.page.label.datatype));
                    theadRow.insertCell().appendChild(document.createTextNode(_this.settings.strings.page.label.location));
                    var ctr_1 = 0;
                    _.each(displayData, function (value) {
                        var row = tbody_1.insertRow();
                        var nameCell = row.insertCell();
                        nameCell.appendChild(document.createTextNode(value.name));
                        if (value.isAnonymized) {
                            var anonIcon = document.createElement('i');
                            anonIcon.classList.add('fas', 'fa-user-secret');
                            anonIcon.style.paddingLeft = '.75rem';
                            nameCell.appendChild(anonIcon);
                        }
                        row.insertCell().appendChild(document.createTextNode(value.type));
                        row.insertCell().appendChild(document.createTextNode(value.startLocation));
                        _this.setDataElementEventHandlers($(row), value, nodeId);
                        if (openSelected && value.id === _this.settings.selectedNodeId) {
                            row.classList.add('row_selected');
                        }
                        ctr_1++;
                    });
                    $(newDetailView).append(elementTable);
                }
                else {
                    _.each(displayData, function (value) {
                        var attrData = {
                            isSelected: openSelected && value.id === _this.settings.selectedNodeId
                        };
                        var rendered = $(_this.elementTemplate($.extend(attrData, _this.settings.strings, value)));
                        _this.setDataElementEventHandlers(rendered, value, nodeId);
                        $(newDetailView).append(rendered);
                        var actualWidth = $(rendered).actual('width');
                        if (actualWidth > maxWidth) {
                            maxWidth = actualWidth;
                        }
                    });
                }
                var countText;
                if (isFiltered) {
                    countText = "".concat(displayData.length, " ").concat(_this.settings.strings.page.label.of, " ").concat(startCount, " ").concat(_this.settings.strings.page.label.elements);
                }
                else {
                    countText = "".concat(startCount, " ").concat(_this.settings.strings.page.label.elements);
                }
                $('#detailElements_' + nodeId).text(countText);
                detailsView.find('.dataTreeNodeElement').css('width', maxWidth + 'px');
                $('#attributeExpandIcon_' + nodeId).removeClass('fa-spinner fa-spin').addClass('fa-minus-square');
            });
        };
        DataRecordSourceHandler.prototype.setDataElementEventHandlers = function (renderElement, value, nodeId) {
            var _this = this;
            renderElement.on('mouseover', function () {
                _this.settings.state.activeTreeElementId = 'nodeDisplayElement_' + value.id;
                _this.activeDataElement = value.id;
                if (!_this.settings.state.contextMenuVisible) {
                    _this.activeDataRecord = nodeId;
                }
            });
            renderElement.on('click', function () {
                var item = _this.getContextMenuItem(_this.settings.strings.page.label._edit);
                delete _this.elementListData[nodeId];
                _this.common.triggerPopup(item);
            });
        };
        DataRecordSourceHandler.prototype.asyncGetElementValues = function (id) {
            if (this.elementListData.hasOwnProperty(id)) {
                var result = jQuery.Deferred();
                result.resolve(this.elementListData[id]);
                return result.promise();
            }
            else {
                return $.post(this.settings.context + 'dataSourceTree.form', {
                    drid: id,
                    dsid: this.settings.dataSourceId,
                    method: 'elements',
                }).promise();
            }
        };
        return DataRecordSourceHandler;
    }());
    return new DataRecordSourceHandler();
})();
