(function ($, _) {
    var XmlDataSourceHandler = /** @class */ (function () {
        function XmlDataSourceHandler() {
            this.dataElementVisible = {};
            this.attributeListVisible = {};
            this.attributeListData = {};
            this.template = Handlebars.compile($('#nodeDisplayDataXmlTemplate').html());
            this.tagTemplate = Handlebars.compile($('#nodeDisplayTagView').html());
            this.elementTemplate = Handlebars.compile($('#nodeDisplayElementView').html());
            this.attributeTemplate = Handlebars.compile($('#nodeDisplayAttributeView').html());
            this.variableTemplate = Handlebars.compile($('#nodeDisplayVariableView').html());
        }
        XmlDataSourceHandler.prototype.applyFilter = function () {
            for (var nodeId in this.attributeListVisible) {
                if (this.attributeListVisible[nodeId]) {
                    $('#nodeAttributeDetailView_' + nodeId).remove();
                    this.renderAttributeList(nodeId);
                }
            }
        };
        XmlDataSourceHandler.prototype.setAnonymized = function (showAnonymized) {
            this.settings.state.filterOptions.anonymized = showAnonymized;
        };
        XmlDataSourceHandler.prototype.setSortBy = function (sortBy) {
            this.settings.state.filterOptions.sortBy = sortBy;
        };
        XmlDataSourceHandler.prototype.renderNodeHtml = function (node) {
            var data = $.extend({}, this.settings.strings, node);
            return this.template(data);
        };
        XmlDataSourceHandler.prototype.attachEvents = function (node, element, openSelected) {
            var _this = this;
            var nodeDisplay = $('#nodeDisplayDataSummary_' + node.id);
            nodeDisplay.on('mouseover', function () {
                _this.settings.state.activeTreeElementId = 'nodeDisplayDataSummary_' + node.id;
                _this.activeXmlTagId = node.id;
            });
            nodeDisplay.on('click', function () {
                var editArgs = _this.getContextMenuItem(_this.settings.strings.page.label._edit);
                _this.common.triggerPopup(editArgs);
            });
            this.attachHoverPopup(node);
            this.initAttributeDetails(node);
            this.initElementDetails(node);
            if (this.attributeListVisible[node.id.toString()] || (openSelected && node.extras.selected)) {
                this.renderAttributeList(node.id, openSelected);
            }
            if (this.dataElementVisible[node.id] || (openSelected && node.extras.selected)) {
                this.renderDataElement(node.id, openSelected);
            }
        };
        XmlDataSourceHandler.prototype.getContentMenuOptions = function () {
            var strings = this.settings.strings;
            if (this.settings.state.activeTreeElementId.startsWith('nodeDisplayAttribute')
                || this.settings.state.activeTreeElementId.startsWith('nodeDisplayAttribute')) {
                return [strings.page.label._edit, strings.page.label.delete];
            }
            else {
                return [strings.page.label._edit,
                    strings.page.label.delete,
                    strings.page.label.add.tag,
                    strings.page.label.upload.tags,
                    strings.page.label.add.attribute,
                    strings.page.label.add.element];
            }
        };
        XmlDataSourceHandler.prototype.getContextMenuItem = function (action) {
            var forAttribute = this.settings.state.activeTreeElementId.startsWith('nodeDisplayAttribute');
            var forElement = this.settings.state.activeTreeElementId.startsWith('nodeDisplayElement');
            var menuArgs;
            if (forAttribute || forElement) {
                menuArgs = this.getAttributeOrElementContextMenuItem(action, forAttribute);
            }
            else {
                menuArgs = this.getXmlTagContextMenuItem(action);
            }
            return menuArgs;
        };
        XmlDataSourceHandler.prototype.setActiveNode = function (value) {
            this.activeXmlTagId = value.id;
        };
        XmlDataSourceHandler.prototype.setCommon = function (value) {
            if (value != null) {
                this.common = value;
            }
        };
        XmlDataSourceHandler.prototype.setSettings = function (value) {
            if (value != null) {
                this.settings = value;
            }
        };
        XmlDataSourceHandler.prototype.getAttributeOrElementContextMenuItem = function (action, forAttribute) {
            var _this = this;
            var onSaveCallback = _.wrap(customCallback, function (callback, iframe) {
                delete _this.attributeListData[_this.activeXmlTagId];
                callback(iframe);
                _this.common.refresh();
            });
            if (action === this.settings.strings.page.label._edit) {
                return {
                    appliedParams: {
                        deid: this.activeAttribute,
                        dsid: this.settings.dataSourceId,
                        isAttributeDataElement: forAttribute,
                        xdtid: this.activeXmlTagId,
                        cacheStamp: new Date().getTime()
                    },
                    onSave: onSaveCallback,
                    src: 'xml_data_element_edit.form',
                    title: forAttribute ? client_messages.title.edit_attribute : client_messages.title.edit_data_element,
                    width: 800
                };
            }
            else if (action === this.settings.strings.page.label.delete) {
                return {
                    appliedParams: {
                        cancelView: 'datasources.form',
                        deid: this.activeAttribute,
                        dsid: this.settings.dataSourceId,
                        parameter: 'deid',
                        successParameter: 'xdtid',
                        xdtid: this.activeXmlTagId,
                        cacheStamp: new Date().getTime()
                    },
                    onSave: onSaveCallback,
                    src: 'xml_data_element_delete.form',
                    title: client_messages.title.delete_data_xml_element,
                    width: 800,
                };
            }
            else {
                throw 'invalid context menu action: ' + action;
            }
        };
        XmlDataSourceHandler.prototype.getXmlTagContextMenuItem = function (action) {
            var _this = this;
            var onSaveCallback = _.wrap(customCallback, function (callback, iframe) {
                delete _this.attributeListData[_this.activeXmlTagId];
                callback(iframe);
                _this.common.refresh();
            });
            switch (action) {
                case this.settings.strings.page.label._edit:
                    return {
                        appliedParams: {
                            action: 'updatexmldatatag',
                            dsid: this.settings.dataSourceId,
                            xdtid: this.activeXmlTagId,
                        },
                        onSave: onSaveCallback,
                        src: 'xml_data_tag_definition_edit.form',
                        title: client_messages.title.edit_data_tag,
                        width: 800,
                    };
                case this.settings.strings.page.label.delete:
                    return {
                        appliedParams: {
                            cancelView: 'datasources.form',
                            dsid: this.settings.dataSourceId,
                            parameter: 'xdtid',
                            successParameter: 'dsid',
                            xdtid: this.activeXmlTagId,
                        },
                        onSave: onSaveCallback,
                        src: 'xml_data_tag_definition_delete.form',
                        title: client_messages.title.delete_data_tag,
                        width: 800,
                    };
                case this.settings.strings.page.label.add.tag:
                    return {
                        appliedParams: {
                            action: 'insertxmldatatag',
                            dsid: this.settings.dataSourceId,
                            'new': true,
                            parentid: this.activeXmlTagId,
                        },
                        onSave: onSaveCallback,
                        src: 'xml_data_tag_definition_edit.form',
                        title: client_messages.title.insert_data_tag,
                        width: 800,
                    };
                case this.settings.strings.page.label.upload.tags:
                    return {
                        appliedParams: {
                            'new': true,
                            action: 'uploadxmldatatags',
                            dsid: this.settings.dataSourceId,
                            parentid: this.activeXmlTagId,
                        },
                        onSave: onSaveCallback,
                        src: 'xml_data_tag_upload_edit.form',
                        title: client_messages.title.insert_data_tag,
                        width: 800,
                    };
                case this.settings.strings.page.label.add.attribute:
                    return {
                        appliedParams: {
                            action: 'updatexmldataattribute',
                            dsid: this.settings.dataSourceId,
                            isAttributeDataElement: true,
                            'new': true,
                            xdtid: this.activeXmlTagId,
                            cacheStamp: new Date().getTime()
                        },
                        onSave: onSaveCallback,
                        src: 'xml_data_element_edit.form',
                        title: client_messages.title.edit_attribute,
                        width: 800,
                    };
                case this.settings.strings.page.label.add.element:
                    return {
                        appliedParams: {
                            action: 'updatexmldataelement',
                            dsid: this.settings.dataSourceId,
                            isAttributeDataElement: false,
                            'new': true,
                            xdtid: this.activeXmlTagId,
                            cacheStamp: new Date().getTime()
                        },
                        onSave: onSaveCallback,
                        src: 'xml_data_element_edit.form',
                        title: client_messages.title.insert_data_element,
                        width: 800,
                    };
            }
            throw 'invalid context menu action: ' + action;
        };
        XmlDataSourceHandler.prototype.initAttributeDetails = function (node) {
            var _this = this;
            $('#attributeExpandIcon_' + node.id).on('click', function () {
                _this.common.closePopups();
                var detailView = $('#nodeAttributeDetailView_' + node.id);
                _this.common.saveScrollPosition();
                if (detailView.length > 0) {
                    $('#attributeExpandIcon_' + node.id).removeClass('fa-minus-square fa-spinner fa-spin').addClass('fa-plus-square');
                    var summary = void 0;
                    if (_this.attributeListData.hasOwnProperty(node.id)) {
                        summary = "".concat(_this.attributeListData[node.id].length, " ").concat(_this.settings.strings.page.label.attributes);
                    }
                    else {
                        summary = "".concat(_this.settings.strings.page.label.attributes);
                    }
                    $('#attributeCount_' + node.id).text(summary);
                    _this.attributeListVisible[node.id] = false;
                    detailView.remove();
                }
                else {
                    _this.renderAttributeList(node.id);
                }
            });
        };
        XmlDataSourceHandler.prototype.initElementDetails = function (node) {
            var _this = this;
            $('#elementExpandIcon_' + node.id).on('click', function () {
                _this.common.closePopups();
                var detailView = $('#nodeElementDetailView_' + node.id);
                _this.common.saveScrollPosition();
                if (detailView.length > 0) {
                    $('#elementExpandIcon_' + node.id).removeClass('fa-minus-square fa-spinner fa-spin').addClass('fa-plus-square');
                    _this.dataElementVisible[node.id] = false;
                    detailView.remove();
                }
                else {
                    _this.renderDataElement(node.id);
                }
            });
        };
        XmlDataSourceHandler.prototype.renderAttributeList = function (nodeId, openSelected) {
            var _this = this;
            $('#attributeExpandIcon_' + nodeId).removeClass('fa-plus-square').addClass('fa-spinner fa-spin');
            this.attributeListVisible[nodeId] = true;
            var newDetailView = document.createElement('div');
            newDetailView.id = 'nodeAttributeDetailView_' + nodeId;
            newDetailView.style.marginLeft = '1.5rem';
            newDetailView.style.height = 'auto';
            $('#attributeExpandIconContainer_' + nodeId).append(newDetailView);
            return this.asyncGetAttributeValues(nodeId).done(function (data) {
                var attributeView = $('#nodeAttributeDetailView_' + nodeId);
                if (_.isArray(data) && data.length === 0) {
                    attributeView.remove();
                    delete _this.attributeListVisible[nodeId];
                    return;
                }
                var maxWidth = 0;
                _this.attributeListData[nodeId] = data;
                var startCount = data.length;
                var displayData = _this.common.filterAndSortDataElements(data, _this.settings.state.filterOptions);
                var isFiltered = startCount !== displayData.length;
                if (_this.settings.state.showChildrenAsTable) {
                    var elementTable = document.createElement('table');
                    var thead = elementTable.createTHead();
                    var tbody_1 = elementTable.createTBody();
                    elementTable.classList.add('table', 'table-hover');
                    var theadRow = thead.insertRow();
                    theadRow.insertCell().appendChild(document.createTextNode(_this.settings.strings.page.label.name));
                    theadRow.insertCell().appendChild(document.createTextNode(_this.settings.strings.page.label.edit.xml.element.attribute.name));
                    theadRow.insertCell().appendChild(document.createTextNode(_this.settings.strings.page.label.datatype));
                    theadRow.insertCell().appendChild(document.createTextNode(_this.settings.strings.page.label.input.formatting));
                    var ctr_1 = 0;
                    _.each(displayData, function (value) {
                        var row = tbody_1.insertRow();
                        var nameCell = row.insertCell();
                        nameCell.appendChild(document.createTextNode(value.name));
                        if (value.isAnonymized) {
                            var anonIcon = document.createElement('i');
                            anonIcon.classList.add('fas', 'fa-user-secret');
                            anonIcon.style.paddingLeft = '.75rem';
                            nameCell.appendChild(anonIcon);
                        }
                        row.insertCell().appendChild(document.createTextNode(value.attributeName));
                        row.insertCell().appendChild(document.createTextNode(value.type));
                        row.insertCell().appendChild(document.createTextNode(value.externalFormatText));
                        _this.setAttributeEventHandlers($(row), value, nodeId);
                        if (openSelected && value.id === _this.settings.selectedNodeId) {
                            row.classList.add('row_selected');
                        }
                        ctr_1++;
                    });
                    $(newDetailView).append(elementTable);
                }
                else {
                    _.each(displayData, function (value) {
                        var attrData = {
                            isSelected: openSelected && value.id === _this.settings.selectedNodeId
                        };
                        var rendered = $(_this.attributeTemplate($.extend(attrData, _this.settings.strings, value)));
                        _this.setAttributeEventHandlers(rendered, value, nodeId);
                        $(newDetailView).append(rendered);
                        var actualWidth = $(rendered).actual('width');
                        if (actualWidth > maxWidth) {
                            maxWidth = actualWidth;
                        }
                    });
                }
                var countText;
                if (isFiltered) {
                    countText = "".concat(displayData.length, " ").concat(_this.settings.strings.page.label.of, " ").concat(startCount, " ").concat(_this.settings.strings.page.label.attributes);
                }
                else {
                    countText = "".concat(startCount, " ").concat(_this.settings.strings.page.label.attributes);
                }
                $('#attributeExpandIcon_' + nodeId).removeClass('fa-spinner fa-spin').addClass('fa-minus-square');
                $('#attributeCount_' + nodeId).text(countText);
                attributeView.find('.dataTreeNodeAttribute').css('width', maxWidth + 'px');
                _this.common.restoreScrollPosition();
            });
        };
        XmlDataSourceHandler.prototype.setAttributeEventHandlers = function (element, value, nodeId) {
            var _this = this;
            element.on('mouseover', function () {
                _this.settings.state.activeTreeElementId = 'nodeDisplayAttribute_' + value.id;
                _this.activeAttribute = value.id;
                _this.activeXmlTagId = nodeId;
            });
            element.on('click', function () {
                var item = _this.getContextMenuItem(_this.settings.strings.page.label._edit);
                delete _this.attributeListData[nodeId];
                _this.common.triggerPopup(item);
            });
        };
        XmlDataSourceHandler.prototype.renderDataElement = function (nodeId, openSelected) {
            var _this = this;
            $('#elementExpandIcon_' + nodeId).removeClass('fa-plus-square').addClass('fa-minus-square');
            this.dataElementVisible[nodeId] = true;
            var newDetailView = document.createElement('div');
            newDetailView.id = 'nodeElementDetailView_' + nodeId;
            newDetailView.style.marginLeft = '1.5rem';
            newDetailView.style.height = '50px';
            $('#elementExpandIconContainer_' + nodeId).append(newDetailView);
            return this.asyncGetDataElementValue(nodeId).done(function (data) {
                _.each(data, function (value) {
                    if (_.isArray(data) && data.length === 0) {
                        $('#nodeElementDetailView_' + nodeId).remove();
                        delete _this.dataElementVisible[nodeId];
                        return;
                    }
                    var attrData = {
                        isSelected: openSelected && value.id === _this.settings.selectedNodeId
                    };
                    var rendered = $(_this.elementTemplate($.extend(attrData, _this.settings.strings, value)));
                    rendered.on('mouseover', function () {
                        _this.settings.state.activeTreeElementId = 'nodeDisplayElement_' + value.id;
                        _this.activeAttribute = value.id;
                        _this.activeXmlTagId = nodeId;
                    });
                    rendered.on('click', function () {
                        var item = _this.getContextMenuItem(_this.settings.strings.page.label._edit);
                        _this.common.triggerPopup(item);
                    });
                    $(newDetailView).append(rendered);
                    _this.common.restoreScrollPosition();
                });
                $('#elementExpandIcon_' + nodeId).removeClass('fa-spinner fa-spin').addClass('fa-minus-square');
            });
        };
        XmlDataSourceHandler.prototype.attachHoverPopup = function (node) {
            var contentUrl = this.settings.context + 'dataSourceTree.form?&method=xmltagsummary&xdtid=' + node.id;
            this.common.addNodeHoverPopup(node, contentUrl, this.tagTemplate, this.variableTemplate);
        };
        XmlDataSourceHandler.prototype.asyncGetAttributeValues = function (nodeId) {
            if (this.attributeListData.hasOwnProperty(nodeId)) {
                var result = jQuery.Deferred();
                result.resolve(this.attributeListData[nodeId]);
                return result.promise();
            }
            else {
                return $.post(this.settings.context + 'dataSourceTree.form', {
                    dsid: this.settings.dataSourceId,
                    method: 'attributes',
                    xdtid: nodeId
                });
            }
        };
        XmlDataSourceHandler.prototype.asyncGetDataElementValue = function (nodeId) {
            return $.post(this.settings.context + 'dataSourceTree.form', {
                dsid: this.settings.dataSourceId,
                method: 'xmldataelements',
                xdtid: nodeId
            });
        };
        return XmlDataSourceHandler;
    }());
    return new XmlDataSourceHandler();
})(jQuery, _);
