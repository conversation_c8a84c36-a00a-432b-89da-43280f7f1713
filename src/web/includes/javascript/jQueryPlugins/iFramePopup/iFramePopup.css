.iFramePopupContainer, .iFramePopupTransitionContainer {
	background-color: #fff;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	-moz-box-shadow: 0 1px 8px 0 rgba(0, 0, 0, .3);
	-webkit-box-shadow: 0 1px 8px 0 rgba(0, 0, 0, .3);
	box-shadow: 0 1px 8px 0 rgba(0, 0, 0, .3);
	position: absolute;
	z-index: 1001;
	margin-bottom: 32px;
	margin-top: 32px;
}
.fullModalOpen  .iFramePopupContainer, .fullModalOpen .iFramePopupTransitionContainer {
	margin-top: 32px;
}

.iFramePopupTransitionContainer {
	background-color: #fafafa;
	opacity: 0.5;
}
.iFramePopupContainer TD {
	padding: 0px 15px 0px 15px;
	height: 32px;
}

.iFramePopupFrame {
	display: block;
}
.iFramePopupHeaderContainer {
    -webkit-border-radius: 3px 3px 0 0;
    -moz-border-radius: 3px 3px 0 0;
    border-radius: 3px 3px 0 0;
	height: 56px;
	background-color: #6d3075;
	vertical-align: middle;
}
.iFramePopupSavingIcon {
	background: url('images/inProcess_clock.gif') no-repeat center center;
	width: 14px;
	height: 14px;
}
.iFramePopupSavingLabel{
	padding: 7px;
	font-size: 13px;
	font-weight: bold;
	color: #5a5a5a;
	font-family: Arial;
}
.iFramePopupPageScreen, .iFramePopupSubPageScreen {
	background-color: #000;
	z-index: 49;
	width: 100%;
	height: 100%;
	position: fixed;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
}

.iFramePopupTitleHandle {
	height: 56px;
	line-height: 56px;
	padding: 0 24px;
	font-weight: 300;
}


.fullModalOpen div[class^="iFramePopupWrapper_"] {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 50;
	overflow-x: hidden;
	overflow-y: auto;
}

.iFramePopupTitle {
	font-size: 18px;
	color: #fff;
}

.iFramePopupCloseBtn, .iFramePopupSnapOffBtn {
	width: 12px;
	cursor: pointer;
	position: absolute;
	top: 12px;
	right: 24px;
	font-size: 18px;
}
.iFramePopupSnapOffBtn {
	right: 60px;	
}
.iFramePopupCloseBtn i, .iFramePopupSnapOffBtn i {
	color: #fff;
}
.iFramePopupCloseBtn:hover i, .iFramePopupSnapOffBtn:hover i {
	color: #fff;
	opacity: 0.9;
}
.iFramePopupCloseBtn:active i, .iFramePopupSnapOffBtn:active i {
	color: #fff;
	opacity: 0.7;
}