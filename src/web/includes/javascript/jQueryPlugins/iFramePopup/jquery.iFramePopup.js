/** iFramePopup r1 // 2011.06.29 // jQuery 1.4 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.3 or later
 * 
 */

if ( window == getTopFrame())
	var iFramePopupX, iFramePopupY = -1;
var iFramePopupMousePolling = false;

function iFramePopup(data) {
	$(data.target).iFramePopup(data);
}

function iFramePopupReq(clickedEleId) {
	var _this = $.iFramePopup.get( clickedEleId );

	_this.initPopup();

	return this;
};

var iFramePopup_fullFrameAttr = {
									width			: 976,
									vertOffset		: -104,
									closeBtnId		: "cancelBtn_button"						
								};

(function($) {
	
	if ( !window.iFramePopupMousePolling ) {
		iFramePopupMousePolling = true;
		$(window.document).mousedown( function(e) {
			var clickPos = $.iFramePopup.computeFrameOffset( window, {left: e.pageX, top: e.pageY} );
			getTopFrame().iFramePopupX = clickPos.left;
			getTopFrame().iFramePopupY = clickPos.top;
		});
	}
	
	$.iFramePopup = {
		get	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return iFramePopup_component.inst[o.attr('id')] || null; 
		},
		defaults : {
			width				: 300,
			height 				: 300,
			displayOnInit		: false,
			vertOffset			: 0,
			id					: null,
			title				: "",
			src					: "#",
			type				: "modal",
			draggable			: true,
			clearOnClose 		: true,
			screenMask			: true,
			devMode				: null,
			onSave				: null,
			closeBeforeOnSave	: false,
			beforePopup			: null,
			beforePopupClose	: null,
			afterPopup			: null,
            afterPopupClose     : null,
			applyCloseToggle	: true,
			matchContentHeight 	: true,
            setFullModalPopup 	: true,
            applySnapOffToggle	: false
			
		},
		computeFrameOffset 		: function(win, dims) {

		    // Init
		    if ( typeof dims === 'undefined' )
		        var dims = { top: 0, left: 0 };

		    // Resolve target iFrame: Find <iframe> within parent window
		    var frames = isFrameAccessible(win.parent) ? win.parent.document.getElementsByTagName('iframe') : new Array();
		    var frame;
		    var found = false;

		    for (var i=0, len=frames.length; i<len; i++) {
		        frame = frames[i];
		        if (frame.contentWindow == win) {
		            found = true;
		            break;
		        }
		    }

		    // Calculate and Recurse
		    if (found) {
		        var rect = $(frame).offset();
		        dims.left += rect.left - frame.contentWindow.pageXOffset;
		        dims.top += rect.top - frame.contentWindow.pageYOffset;
		        if (win !== top) {
		        	$.iFramePopup.computeFrameOffset(win.parent, dims);
		        }
		    }
		    
		    return dims;
		}
	};
	
	$.fn.iFramePopup = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if(conf !== false) new iFramePopup_component().init(this, conf);
		});
	};
	
	function iFramePopup_component () {
		return {
			
		data : $.extend({},$.iFramePopup.defaults),	

		init : function(elem, conf) {
			var _this = this;
			
			_this.data.devMode = _.isFunction(getTopFrame().MAIN_DEV_MODE) ? getTopFrame().MAIN_DEV_MODE() : null;

			iFramePopup_component.inst[$(elem).attr("id")] = _this;
			_this.data = $.extend(false, {}, this.data, conf);
			_this.targetEle = $(elem);
			
			if (_this.data.id == null)
				_this.data.frameId = _this.targetEle.attr('id');
			else
				_this.data.frameId = _this.data.id;

			if (_this.data.displayOnInit) {
				if ($.isFunction(_this.data.beforePopup))
					if (_this.data.beforePopup(_this) == false)
						return;
				iFramePopupReq($(_this.targetEle).attr('id'));
			} else {
				$(_this.targetEle).click( function(){
					if ($.isFunction(_this.data.beforePopup))
						if (_this.data.beforePopup(_this) == false)
							return;
					iFramePopupReq($(_this.targetEle).attr('id')); 
				});
			}

		},

		initPopup : function() {
			var _this = this;

			if (_this.data.screenMask) {
				if ( $("#iFramePopup_"+_this.data.frameId, getTopFrame().document).length === 0 ) {
					$("body", getTopFrame().document).append("<div class=\"iFramePopupPageScreen\"></div>");
                	$('.iFramePopupPageScreen', getTopFrame().document).css({opacity: 0}).fadeTo(400, 0.6);
				}
            }
			
			if ( _this.data.type == "panelRight" )
				_this.data.height = "100%";

            if (_this.data.setFullModalPopup){
				if ( $("#iFramePopup_"+_this.data.frameId, getTopFrame().document).length === 0 ) {
					common.applyTopFrameBodyClass('fullModalOpen');
				}
			}

            if ( $("#iFramePopup_"+_this.data.frameId, getTopFrame().document).length === 0 ) {
					
				// GENERATE POPUP
				var iFramePopupHTML = 	_this.data.setFullModalPopup ? "<div id=\"iFramePopupWrapper_" + _this.data.frameId + "\" class=\"iFramePopupWrapper_" + _this.data.frameId + "\">" : "";
                    iFramePopupHTML += 		"<div id=\"iFramePopup_"+_this.data.frameId+"\" class=\"iFramePopupContainer shiftFrameContainer\" style=\"visibility: hidden;\">" +
                    							"<div class=\"iFramePopupHeaderContainer\">" +
													"<div class=\"iFramePopupTitleHandle\">" +
														"<span class=\"iFramePopupTitle\" style='white-space: nowrap; display: inline-block; max-width: " + (this.data.width - 90) + "px; text-overflow: ellipsis; overflow: hidden;'>"+_this.data.title+"</span>" +
													"</div>";
				if ( _this.data.applySnapOffToggle )
					iFramePopupHTML +=				"<div class=\"iFramePopupSnapOffBtn\">" +
														"<i class=\"far fa-window-restore\"></i>" +
													"</div>";
				if ( _this.data.applyCloseToggle )
					iFramePopupHTML +=				"<div class=\"iFramePopupCloseBtn\">" +
														"<i class=\"far fa-times\"></i>" +
													"</div>";
				iFramePopupHTML +=				"</div>";

				iFramePopupHTML +=				"<div class=\"iFramePopupFrameContainer\">" +
													"<iFrame class=\"iFramePopupFrame\" frameborder=0 width=\""+_this.data.width+"\" height=\""+_this.data.height+"\" src=\""+_this.getIframePath()+"\"></iframe>" +
												"</div>" +
                    						"</div>";
                iFramePopupHTML +=		_this.data.setFullModalPopup ? "</div>" : "";

				$("body", getTopFrame().document).append( iFramePopupHTML );
				// Hide cluetip popup: Mouseout not called when iFrame loads
				$("body", getTopFrame().document).find('#cluetip,.styledMenuContentDiv').hide();
				$("body", getTopFrame().document).find('.contentPopup_popupContainer').remove();
				popupFactoryRemove('iFramePopup_init');
				
				// START iFrame ONLOAD EVENT HANDLER
				$("#iFramePopup_"+_this.data.frameId+" .iFramePopupFrame", getTopFrame().document).each( function() {
					// Resolve document object (access point determined by browser)
				    var doc = this.contentWindow || this.contentDocument;
				    if (doc.document) {
				        doc = doc.document;
				    }

				    // Continue init when iFrame has loaded
					if ( doc.readyState == 'complete' && !$.browser.chrome ) {
						_this.iFrameOnLoad($(this));
					} else {
						$(this).load( function() {
							_this.iFrameOnLoad($(this));
						});
					}
				});
				// END iFrame ONLOAD EVENT HANDLER
				
				if ( _this.data.type == "panelRight" ) {
					$("#iFramePopup_"+_this.data.frameId, getTopFrame().document).css({'position':'fixed', 'right':'0px', 'top':'0px', 'margin':'0px', 'height':'100%', 'visibility': 'visible'});
					$("#iFramePopup_"+_this.data.frameId, getTopFrame().document).find('.iFramePopupFrameContainer').css({'height':'100%'});
					$("#iFramePopup_"+_this.data.frameId, getTopFrame().document).find('.iFramePopupHeaderContainer').css({'background-color':'#666'});
				}
					
				if (_this.data.draggable)
					$("#iFramePopup_"+_this.data.frameId, getTopFrame().document).draggable({handle: '#iFramePopupTitle'});
				
				$("#iFramePopup_"+_this.data.frameId+" .iFramePopupCloseBtn", getTopFrame().document)
					.click(		function(){_this.closeAction({close_action: "frame"});});
				$("#iFramePopup_"+_this.data.frameId+" .iFramePopupSnapOffBtn", getTopFrame().document)
					.click(		function(){_this.snapOffAction();});

				var currentPopups = $("div[class^='iFramePopupWrapper_']", getTopFrame().document);

				if (currentPopups.length > 1) {
					currentPopups.each(function (index, elem) {
						var $elem = $(elem);
						if ($elem.attr('id') !== "iFramePopupWrapper_"+_this.data.frameId) {
							$($elem.css('z-index', 48 + index));
						} else {
							$elem.attr('data-stackpos', currentPopups.length);
						}
                    });
				} else {
					$("#iFramePopupWrapper_"+_this.data.frameId, getTopFrame().document).attr('data-stackpos', 1);
				}
			}

            // Positioning: Modal
            if ( _this.data.type == "modal" ) {
				setTimeout( function() {
					_this.data.pageX = getTopFrame().iFramePopupX;
					_this.data.pageY = getTopFrame().iFramePopupY;
					
					// Calculate popup position (horizontally centered, vertical offset accounting for scroll position)
					var iframeContainerWidth = _this.data.width;
					var horiMarginOffset	= -1 * Math.round( iframeContainerWidth/ 2 );
					var scrollTop           = parseInt($(getTopFrame().document).scrollTop());
					var marginTop			= _this.data.setFullModalPopup ? 0 : ((!isNaN(scrollTop) ? scrollTop : 0) + _this.data.vertOffset + 120);
	                
					$("body", getTopFrame().document).append( "<div class=\"iFramePopupTransitionContainer\" style=\"width: 1px; height: 1px; top: " + _this.data.pageY + "px; left: " + _this.data.pageX + "px;\"></div>" );
					$("#iFramePopup_"+_this.data.frameId, getTopFrame().document)
						.attr("style", "width: " + iframeContainerWidth + "px; left: 50%; margin-left: " + horiMarginOffset + "px; top: " + marginTop + "px; opacity: 0;");
	
	                if (!_this.data.setFullModalPopup){
	
	                	$(".iFramePopupTransitionContainer", getTopFrame().document)
						.animate({
							"width": iframeContainerWidth + "px",
							"height": (_this.data.height + 30) + "px",
							"left": "50%",
							"margin-left": horiMarginOffset + "px",
							"top" : marginTop + "px"
						}, 300, function() {
							$(".iFramePopupTransitionContainer", getTopFrame().document).css({'z-index':49});
	
							$("#iFramePopup_"+_this.data.frameId, getTopFrame().document)
								.animate({
									"opacity": 1,
									"visibility": "visible"
								}, 800, function() {
									$(".iFramePopupTransitionContainer", getTopFrame().document).remove();
							});
						});
	
					}
	
					$("#iFramePopup_"+_this.data.frameId, getTopFrame().document)
						.animate({
							"opacity": 1,
							"visibility": "visible"
						}, 300, function() {
							$(".iFramePopupTransitionContainer", getTopFrame().document).remove();
					});
	
				}, 10);
            }

		},
		
		closeAction : function(o) {
			var _this = this;

			if ($.isFunction( _this.data.beforePopupClose ))
				if ( _this.data.beforePopupClose(_this,o) == false )
					return;

			if (_this.data.clearOnClose)
			{
                if (_this.data.setFullModalPopup) {
                    common.removeTopFrameBodyClass('fullModalOpen');
                    $("#iFramePopup_"+_this.data.frameId, getTopFrame().document).parent().remove();
                } else
					$("#iFramePopup_"+_this.data.frameId, getTopFrame().document).remove();
            } else {
                if (_this.data.setFullModalPopup) {
                    common.removeTopFrameBodyClass('fullModalOpen');
                    $("#iFramePopup_"+_this.data.frameId, getTopFrame().document).parent().hide();
                } else
                	$("#iFramePopup_"+_this.data.frameId, getTopFrame().document).hide();
            }

            // Hide cluetip popup: Mouseout not called when iFrame closes
			$("body", getTopFrame().document).find('#cluetip').hide();
			$("body", getTopFrame().document).find('.contentPopup_popupContainer,.mce-custompanel.mce-menu').remove();
			popupFactoryRemove('iFramePopup_closeAction');

			if (_this.data.screenMask) {
				var iFramePopupPageScreenArray = $('.iFramePopupPageScreen', getTopFrame().document);
				var objFromArr;
				if(iFramePopupPageScreenArray.length > 1) {
					objFromArr = iFramePopupPageScreenArray[iFramePopupPageScreenArray.length - 1];
					objFromArr.remove();
				} else {
					objFromArr = iFramePopupPageScreenArray[0];
					objFromArr.remove();
				}
			}

            var popups = $("div[class^='iFramePopupWrapper_']", getTopFrame().document);

			var topPosition = 0;
            var topPopup = null;

			popups.each(function (index, elem) {
				var stackPosition = parseInt($(elem).attr('data-stackpos'));
            	if (stackPosition > topPosition) {
            		topPosition = stackPosition;
            		topPopup = $(elem);
				}
			});

			if (topPopup) {
				topPopup.css('z-index', '');
                _this.iFrameOnLoad(topPopup.find('iframe'));
			}

            if ($.isFunction(_this.data.afterPopupClose)) {
                _this.data.afterPopupClose(_this,o);
            }
		},
		
		getIframePath : function() {
			var _this = this;
			
			var path = _this.data.src;
			
			// If there are params
			if (_this.data.appliedParams != undefined) {
				
				// How many params?
				var paramCount = 0;
				jQuery.each(_this.data.appliedParams, function(currentParam, val) {
					paramCount++;
				});
				
				// Iterate parameters and build path
				if (paramCount > 0) {
					if (path.indexOf("?") == -1)
						path += "?";
					else
						path += "&";
					jQuery.each(_this.data.appliedParams, function(currentParam, val) {
						if ( $.isFunction(val) )
							path += currentParam + "=" + val() + "&";
						else
							path += currentParam + "=" + val + "&";
					});
					// Remove dangling '&'
					path = path.substring(0,path.length-1);
				}
			}

			return path;
		},
		
		getUrlParam : function (url, paramName) {
			var regexS = "[\\?&]"+paramName+"=([^&#]*)";
			var regex = new RegExp(regexS);
			var results = regex.exec(url);
			if (results == null)
				return "";
			else
				return results[1];
		},
		
		iFrameOnLoad : function(iFrameEle) {
			
			var _this = this;

			var frameContent = null;
			var isPopupWindow = false;
        	if ( $(iFrameEle.document).find('body') && $(iFrameEle.document).find('body').length > 0 ) {
        		isPopupWindow = true;
        		
        		if ( $(iFrameEle.document).find('.iFramePopupHeaderContainer').length == 0 ) {
					var header = $("#iFramePopup_"+_this.data.frameId, getTopFrame().document).find('.iFramePopupHeaderContainer').clone();
					header = $(header)
					$(header).find('.iFramePopupSnapOffBtn,.iFramePopupCloseBtn').remove();
					$(iFrameEle.document).find('body').prepend( header );
        		}
				
				frameContent = iFrameEle.document;
				
				_this.closeAction({close_action: "iframe_load"});
        	} else {
        		frameContent = $(iFrameEle).contents();
        	}

			if ( _this.data.type == "modal" )
            	$(getTopFrame().document).find('.iFramePopupButtonBar').remove();

			if (!isPopupWindow && _this.data.matchContentHeight) {
            	common.setIframeHeightToMatchContent(iFrameEle);

            	// clone and hide the buttons
				var workFlowButtons = frameContent.find('.workflowButtonsContainer, .fixedButtonBar');

				if (workFlowButtons.length > 0) {
					var buttonsCopy = workFlowButtons.clone(false, false);

					// ensure iFramePopupButtonBar has precedence over other class styles
					buttonsCopy.attr('class', "iFramePopupButtonBar " + buttonsCopy.attr('class'));

					var frameWidth = $(iFrameEle).width();
					var frameHeight = $(iFrameEle).height();
					var outerHeight = workFlowButtons.outerHeight();

					workFlowButtons.hide();
                    iFrameEle[0].contentWindow.common.refreshParentIframeHeight();

                    $(buttonsCopy).find('.btn, .primaryBtn')
						.attr('onclick', '')
						.on('click', function (eventObject) {
							iFramePopup_component.inst[_this.data.id] = null;
							iFramePopup_component.inst[_this.targetEle.attr('id')] = null;

							var id = $(eventObject.currentTarget).find('a').attr('id');
							$(frameContent).find('#' + id).closest('.btn, .primaryBtn').click();
							if (!$(eventObject.currentTarget).hasClass('staticButton')) {
								$(buttonsCopy).remove();
							}
						});

                    var container = $(iFrameEle).closest('.iFramePopupFrameContainer');
                    container.find('.iFramePopupButtonBar').remove();

                    if (document.documentElement.clientHeight > frameHeight) {
                    	buttonsCopy.css({
							bottom: Math.max((document.documentElement.clientHeight - frameHeight - 56 - 32), 0) + 'px'
                    	})
					}

                    container.append(buttonsCopy);
                    buttonsCopy.width(frameWidth);
                    var outerWidth = buttonsCopy.outerWidth();
                    buttonsCopy.width((frameWidth - (outerWidth - frameWidth)) + 'px');
                    buttonsCopy.show();
				}



        	}

            var cssLink = $("<link rel=\"stylesheet\" type=\"text/css\" href=\"" + context + "/includes/javascript/jQueryPlugins/iFramePopup/iFramePopup.css\" />");

			if ( getTopFrame() != null )
				$(frameContent).find('head', getTopFrame().document).append(cssLink);

			// Bind close action to user defined close button contained in iFrame
			if (_this.data.closeBtnId != undefined)
				$(frameContent).find("#"+_this.data.closeBtnId).click( function() {
					_this.closeAction({close_action: "btn_click"});
				});

			// Detect successful save action of iFrame popup, reload parent page
			var iFrameDoc = $(iFrameEle).get(0).contentWindow || $(iFrameEle).get(0).contentDocument;
			if ( !isPopupWindow && iFrameDoc.location != undefined ) {
				var saveSuccessParamValue = _this.getUrlParam(iFrameDoc.location.href, "nprSaveSuccess");
				if (saveSuccessParamValue == "true") {
					if ($.isFunction( _this.data.onSave ))
					{
						//Close iFrame
						if ( _this.data.closeBeforeOnSave )
							_this.closeAction({close_action: "before_on_save"});
						else
							setTimeout( function() { _this.closeAction({close_action: "on_save"}); }, 10 );
						//Refresh View iFrame						
						_this.data.onSave(iFrameEle);
					}
					else
					{
						
						if ( $(frameContent).find('body #closeFrame').length > 0 ) {
							if ( _this.data.closeBeforeOnSave )
								_this.closeAction({close_action: "before_on_save"});
							else
								setTimeout( function() { _this.closeAction({close_action: "on_save"}); }, 10 );
						}
						
						// Display 'Saving' indicator
						$(frameContent).find('body').children().wrapAll("<div style=\"display: none;\" />");
						$(frameContent).find('body')
							.append("<div style=\"display: table-cell; vertical-align: middle; height: "+($(frameContent).height()-20)+"px; width: "+$(frameContent).width()+"px;\">" +
										"<table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"><tr>" +
											"<td align=\"right\" width=\"50%\" class=\"iFramePopupSavingLabel\">Saving</td>" +
											"<td align=\"left\" width=\"50%\"><div class=\"iFramePopupSavingIcon\"></div></td>" +
										"</tr></table>" +
									"</div>");
						
						// Reload parent
						//getTopFrame().location.reload();
					}
				}
			}
			
			// DEV MODE: Display reload button for iFrame
			if (_this.data.devMode)
				$(frameContent).find('body')
					.append("<div style=\"padding: 5px; position: absolute; top: "+($(frameContent).find('body').height()-45)+"px; left: 0px;\">" +
								"<input id=\"devModeBtn\" type=\"button\" value=\"DEV MODE: RELOAD FRAME\" style=\"width: 150px;\" title=\"Toggle 'devMode' in iFramePopup init\" onclick=\"javascript:window.location.reload()\" />" +
							"</div>");
			
			$(frameContent).find('body').append("<input type=\"hidden\" id=\"popupFrameId\" value=\""+_this.data.frameId+"\" />");

			if ( getTopFrame() != null && $(frameContent).find('body #popupHeaderTitle').length > 0 )
				$(getTopFrame().document)
					.find('#iFramePopup_'+_this.data.frameId+' .iFramePopupTitle')
					.html($(frameContent).find('body #popupHeaderTitle').html());
			
			if ( $.isFunction(_this.data.afterPopup) )
				_this.data.afterPopup(_this);
		},
		
		snapOffAction: function() {
			var _this = this;

		    // Close popup window (if exists): Brings window to forefront.  Firefox will
		    //		                           reopen behind the current window if popup is already open
		    if ($.browser.mozilla || $.browser.chrome) {
		        snapOffWindow = getTopFrame().open('', 'iFramePopup_' + _this.data.frameId, '');
		        if ( snapOffWindow != null )
		        	snapOffWindow.close();
		    }

		    var currentFrame = $("#iFramePopup_"+_this.data.frameId+" .iFramePopupFrame", getTopFrame().document);
		    getTopFrame().windowOpenTargets.push({
		        path: addParam(_this.getIframePath(), "windowType=stand_alone"),
		        name: 'iFramePopup_' + _this.data.frameId,
		        attr: 'directories=no, scrollbars=yes, statusbar=no, status=no, toolbar=no, menubar=no, location=no, resizable=no, alwaysRaised=yes, ' +
		        'width=' + $(currentFrame).outerWidth() + ', height=' + $(currentFrame).outerHeight(),
		        afterOpen: function () {
		        	var popup = getTopFrame().open('', 'iFramePopup_' + _this.data.frameId, '');
		        	if ( popup != null ) {
					    var doc = popup.document;
	
						$(popup).load( function() {
							_this.iFrameOnLoad(popup);
						});
		        	}
		        }
		    });

		}
		
		}; // end component
	};
	
	// instance manager
	iFramePopup_component.inst = {};
	
})(jQuery);	