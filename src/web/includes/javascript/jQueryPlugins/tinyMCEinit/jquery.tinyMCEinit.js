var tinymceEditorDef_standard;
var tinymceEditorDef_simpleText;
var tinymceEditorDef_value;
var tinymceInitCallback 			= null;
var tinymceDir						= "tinymce_4.7.4";
var tinymce_fontawesome_path		= context + "/_ux/scss/themes/messagepoint/styles.css";
var tinymce_new_content_editor_path		= context + "/includes/themes/messagepoint.contenteditor.css";
var tinymce_content_css_path		= context + "/includes/themes/messagepoint_content.css";
var tinymce_content_edit_css_path 	= context + "/includes/themes/messagepoint_content_edit.css";

var codemirror_config		= {
	indentOnInit	: true,
	config			: {
		indentUnit		: 4
	},
	path			: context + '/includes/javascript/codeMirror-5.10.1'
}

var webSpellCheckerURL = "webspellchecker-pvi.messagepoint.com";
var applyWebSpellChecker = global_data.applyInlineSpellchecking;
if (applyWebSpellChecker && $('head').find('#wscJavascript').length == 0 )
	$('head').append("<script type=\"text/javascript\" id=\"wscJavascript\" src=\"https://" + webSpellCheckerURL + "/wscservice/wscbundle/wscbundle.js\"></script>");

if ( applyWebSpellChecker ) {

	$.get("https://" + webSpellCheckerURL + "/wscservice/wscbundle/wscbundle.js")
		.done(function () {

			var applied_lang = "en_US";
			if (global_data.spellcheck_languages != undefined && global_data.spellcheck_languages != null) {
				for (var i = 0; i < global_data.spellcheck_languages.length; i++)
					if (global_data.spellcheck_languages[i].default == true)
						applied_lang = global_data.spellcheck_languages[i].code + "_" + global_data.spellcheck_languages[i].locale;
			}
			global_data.lang_support_conversions = [
				{target: "es_US", replacement: "es_ES"}
			];

			// Convert unsupported lang/locale combinations
			for (var i = 0; i < global_data.lang_support_conversions.length; i++)
				if (global_data.lang_support_conversions[i].target == applied_lang)
					applied_lang = global_data.lang_support_conversions[i].replacement;

			window.WEBSPELLCHECKER_CONFIG = {
				autoSearch: true,
				autoDestroy: true,
				cache: true,
				enableGrammar: true,
				lang: applied_lang,
				serviceProtocol: 'https',
				serviceHost: webSpellCheckerURL,
				servicePort: '443',
				servicePath: '/wscservice/api',
				localization: global_data.userContextLangCode,
				userDictionaryName: global_data.spellcheckDictionaryIdentifier,
				theme: 'ckeditor5',
				ignoreClasses: ['staticContentItem', 'mceNonEditable'],
				requestTokensCount: 1,
				disableDictionariesPreferences:true,
				enforceAI: true,
				autocorrect: false,
				settingsSections: ['languages','options'],
				actionItems: ['ignoreAll','settings','toggle'],
				globalBadge: window.IS_INTERACTIVE === true,
				checkInNonEditableElements: true,
				customStyle: {
					container: {
						margin: '64px' // Add margin to the container
					}
				}
			};
			if (global_data.dictionaryEdit) {
				window.WEBSPELLCHECKER_CONFIG.actionItems.push('addWord');
				window.WEBSPELLCHECKER_CONFIG.settingsSections.push('dictionaries');
			}

		}).fail(function () {
		applyWebSpellChecker = false;
		console.log("Warning: WebSpellChecker not reachable");
	});
} else {
	window.WEBSPELLCHECKER_CONFIG = undefined;
}

$( function () {
	// Remove TRIM indicators from content on load (may appear after a validation or message content)
	$('.textContentInput textarea').each( function() {
		var content = $(this).val();
		content = content.replace("#MP_TRIM_0", "").replace("#MP_TRIM_1", "").replace("#MP_TRIM_2", "").replace("#MP_TRIM_3", "");
		$(this).val(content);
	});
});

function tinyMCEinit(width, height, data) {

	// Text Styles: Format JSON
	if ( data.text_data != undefined && data.text_data != null)
		for (var i=0; i < data.text_data.length; i++)
			data.text_data[i] = mpParseJSON( data.text_data[i] );
	if ( data.paragraph_style_data != undefined && data.paragraph_style_data != null)
		for (var i=0; i < data.paragraph_style_data.length; i++)
			data.paragraph_style_data[i] = mpParseJSON( data.paragraph_style_data[i] );

	var applyMenu = data.apply_menu != undefined ? data.apply_menu : true;

	tinymceEditorDef_standard =
		{
			// Location of TinyMCE script
			script_url					: context + '/includes/javascript/' + tinymceDir + '/tinymce.min.js',
			inline						: data.inline !== undefined? data.inline: false,
			language					: data.context_lang != undefined ? (data.context_lang == 'zz' || data.context_lang == 'xx' || data.context_lang == '' ? 'en' : data.context_lang) :
				global_data.userContextLangCode,

			// Skin options
			theme						: "modern",
			skin						: "lightgray",

			width						: width,
			height						: height,
			resize						: false,
			plugins						: [	"mp_session,mp_zone,mp_custommenu,mp_clipboard,mp_clear,mp_textstyles,mp_paragraphstyle,mp_tagpanel," +
			"mp_liststyle," +
			(data.is_new_editor ? "mp_canvasrulers," : "mp_canvasguides,") +
			(data.apply_rotation ? " mp_rotate," : "") +
			(data.applies_freeform || data.applies_forms || data.applies_images || data.is_dynamic_canvas || data.applies_barcodes ? "mp_canvas," : "") +
			(data.applies_freeform || data.applies_forms ? "mp_canvasforms," : "") +
			(data.applies_freeform || data.applies_images ? "mp_canvasimages," : "") +
			(data.applies_freeform ? "mp_canvaspanel,mp_divider," : "") +
			(data.applies_barcodes ? "mp_barcode," : "") +
			(data.can_edit_source == undefined || data.can_edit_source ? "mp_codemirror," : "") +
			( qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [9,16,18,19,((data.is_exstream_runtime_dxf || data.is_exstream_html) ? 3 : 0)] }) ? "mp_repeatingtablerows," : "") +
			( qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [3,4],'connectors': [18,19,16,(data.is_exstream_dxf || data.is_exstream_html ? 3 : 0)] }) ? "mp_links," : "") +
			"mp_paragraphlistproperties,mp_paragraph," +
			"advlist,mp_listproperties,mp_customlistproperties," +
			(data.supports_custom_paragraph ? "mp_customparagraphstyles," : "") +
			(data.apply_reset ? "mp_reset," : "") +
			(data.variant_id && data.variant_id != null && data.content_object_id ? "mp_variants," : "") +
			(data.rationalizer_application_id ? "mp_history," : data.content_object_id ? ("mp_history," + (data.is_translation_compare ? "mp_defaultcontentcompare," : "")) : "") +
			"mp_noeditselect,mp_markeditable," +
			(data.supports_custom_paragraph && qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [(data.is_exstream_dxf || data.is_exstream_runtime_dxf ? 3 : 0),18,19] }) ? "mp_custompasteastext," : "mp_pastecheck,paste,") +
			(data.table_of_contents_enabled ? "mp_tableofcontents," : "") +
			( !applyWebSpellChecker ? "spellchecker," : "" ) +
			"searchreplace,table,lists,noneditable,mp_nonbreaking,fullscreen,mp_charmap",
				"mp_insertchar,mp_insertvariable,mp_insertembeddedcontent,mp_insertplaceholders,colorpicker,textcolor",
				"mp_tableproperties,mp_cleaner,mp_menus,mp_contentmenu,mp_textdirection" +
				(data.isVariableContentEnabled == undefined || data.isVariableContentEnabled ? ",mp_contenttargeting" : "") +
				(common.isFeatureEnabled('GPTRewriteUsingMarkup') ? ",mp_contentintel_proto" : ",mp_contentintel")
			],
			init_instance_callback		: "editorActionsInit",
			setup						: function(ed) {
				tinyMCEsetup(ed);
			},
			paste_preprocess			: function(o,a) {
				tinyMCEpastePreprocess(o,a);
			},
			relative_urls				: true,
			content_css					: data.content_css +
				( cssApplied(data.paragraph_style_css) ? ',' + data.paragraph_style_css : '') +
				( cssApplied(data.list_style_css) ? ',' + data.list_style_css : '' ),
			paragraph_style_css			: data.paragraph_style_css,
			paragraph_style_data		: data.paragraph_style_data,
			list_style_css				: data.list_style_css,
			list_style_data				: data.list_style_data,
			advlist_number_styles		: data.connector == 18 || data.connector == 19 ? 'decimal,lower-alpha,lower-roman,upper-alpha,upper-roman' : 'decimal,lower-alpha,lower-greek,lower-roman,upper-alpha,upper-roman',
			style_formats				: ( attrApplied(data.text_data) ? data.text_data : []),
			menubar						: applyMenu ? 'edit,format,content,insert,tables,freeform,forms,tools' : false,
			menu	 					: {
				edit: {title: client_messages.content_editor.menu_edit, items:
						"undo redo | cut copy paste pastetext clear " +
						(data.apply_reset ? " reset " : "") +
						" | selectall | searchreplace "
				},
				format: {title: client_messages.content_editor.menu_format, items:
						(data.apply_super_sub || qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [3] }) ? " subscript superscript | " : "") +
						(data.apply_rotation ? " rotateleft rotateright | " : "") +
						(!data.snippet_mode ? " mp_paragraph " : "") +
						(data.applies_freeform || data.applies_images ? " mp_imageproperties " : "") +
						(!data.snippet_mode  ? " mp_listproperties " : "") +
						(data.is_dynamic_canvas ? " | canvassize " : "")
				},
				insert: {title: client_messages.content_editor.menu_insert, items:
						(qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [3,4],'connectors': [18,19,16,(data.is_exstream_dxf || data.is_exstream_html ? 3 : 0)] }) ? " mp_link mp_unlink " : "") +
						(qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [16] }) ? " mp_anchor mp_bookmark " : "") +
						(qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [(data.is_exstream_dxf || data.is_exstream_html ? 3 : 0), 16, 18, 19] }) ? " mp_alttext mp_removealttext" : "") +
						(data.applies_freeform || data.applies_images ? " imageupload " : "") +
						(data.applies_barcodes ? " mp_barcode " : "") +
						(data.content_menu && data.content_menu.mode == "edit" && data.content_menu.applied ? " contentmenu " : "") +
						" inlinetargeting " +
						(qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [18, 19] }) ? " tableofcontents " : "") +
						" | nonbreaking"
				},
				tables: {title: client_messages.content_editor.menu_tables, items:
						(data.applies_tables ? "inserttable tableprops deletetable tabletoolbartoggle | cell row column " : "") +
						(data.applies_tables && qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [3,4],'connectors': [9,16,18,19,((data.is_exstream_runtime_dxf || data.is_exstream_html) ? 3 : 0)] }) ? " rowtargeting " : "" ) +
						(data.applies_tables && qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [18,19,((data.is_exstream_runtime_dxf || data.is_exstream_html) ? 3 : 0)] }) ? " columntargeting " : "" )
				},
				freeform: {title: client_messages.content_editor.menu_freeform, items:
						(data.applies_freeform ? " canvaspanel guides " : "")
				},
				forms: {title: client_messages.content_editor.menu_forms, items:
						(data.applies_freeform || data.applies_forms ? " textfield menu checkboxinput radioinput submitbutton | " : "") +
						(data.applies_freeform ? " divider " : "")
				},
				tools: {title: client_messages.content_editor.menu_tools, items:
						'code ' +
						(!applyWebSpellChecker ? ' spellchecker ' : '') +
						' tagpanel clipboard mp_contentintel tooltipstoggle clickthroughtoggle resetworkspace'},
				content: {title: client_messages.content_editor.menu_content, items:
						' insertembeddedcontent insertlocalcontent ' +
						(data.applies_freeform ? " insertsmartcanvas " : "") +
						(data.applies_freeform ? " insertlocalsmartcanvas " : "") +
						'insertvariable ' +
						'insertconstants insertplaceholders insertmarker ' +
						(data.support_unicode ? "mp_charmap" : "insertchar") +
						(data.variant_id && data.variant_id != null && data.content_object_id ? " | mp_variants " : "") +
						(data.rationalizer_application_id ? " | mp_history " : data.content_object_id ? (" mp_history " + (data.is_translation_compare ? " mp_defaultcontentcompare " : "")) : "") +
						' | contentrendering samplevaluerendering '
				}
			},
			toolbar1					: (!applyMenu ? "clear " : "") +
				(!applyMenu && data.apply_reset ? "reset " : "") +
				(!applyMenu ? " cut copy pastetext | undo redo | " : "") +
				(data.applies_text_styles ? " styleselect mp_textstyle mp_bold mp_italic mp_underline mp_pointsize mp_color | " : "") +
				(data.applies_paragraph_styles ? " paragraphstyle mp_alignleft mp_aligncenter mp_alignright mp_alignjustify mp_linespacing | " : "") +
				(!data.snippet_mode ? " mp_outdent mp_indent | " : " ") +
				(!data.snippet_mode ? " bullist numlist | " : " ") +
				(!applyMenu && qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [18,19,16] }) ? " mp_link mp_unlink mp_anchor mp_bookmark " : "") +
				(!applyMenu && qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [(data.is_exstream_dxf || data.is_exstream_html ? 3 : 0),16,18,19] }) ? " mp_alttext mp_removealttext" : "") +
				(!applyMenu  ? " mp_paragraph | " : "") +
				(!applyMenu && (data.apply_super_sub || qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [3] }) ? " subscript superscript | " : "" )) +
				(!applyMenu && data.is_dynamic_canvas ? " canvassize " : "") +
				(!applyMenu && (data.applies_freeform || data.applies_images) ? " imageupload " : "") +
				(!applyMenu && data.applies_freeform ? " canvaspanel guides " : "") +
				(!applyMenu && (data.applies_freeform || data.applies_images) ? " | " : "") +
				(!applyMenu && data.applies_barcodes ? " mp_barcode | " : "") +
				(!applyMenu && (data.applies_freeform || data.applies_forms) ? " textfield menu checkboxinput radioinput submitbutton " : "") +
				(!applyMenu && data.content_menu && data.content_menu.mode == "edit" && data.content_menu.applied ? " contentmenu " : "") +
				(!applyMenu && data.applies_freeform ? " divider | " : "") +
				(!applyMenu ? " nonbreaking | searchreplace code | fullscreen " : "") +
				(!applyMenu && data.applies_tables ? " | mp_table " : "") +
				(!applyMenu && data.applies_tables && qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [3,4],'connectors': [9,16,((data.is_exstream_runtime_dxf || data.is_exstream_html) ? 3 : 0)] }) ? " targeting " : "" ) +
				(data.applies_list_styles  || qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [(data.is_exstream_dxf || data.is_exstream_html ? 3 : 0),19] }) ? " liststyle | " : "") +
				(!applyMenu || qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [3,4],'connectors': [9,((data.is_exstream_dxf || data.is_exstream_html) ? 3 : 0),16,18,19] }) ? " mp_paragraphlistproperties " : "") +
				(!applyMenu && data.apply_rotation ? " rotateleft rotateright " : "") +
				(!applyMenu ? " | insertembeddedcontent insertlocalcontent insertplaceholders " : "") +
				(!applyMenu && data.applies_freeform ? " insertsmartcanvas " : "") +
				(!applyMenu && data.applies_freeform ? " insertlocalsmartcanvas " : "") +
				(!applyMenu ? " | insertvariable insertmarker " : "") +
				(!applyMenu ? (data.support_unicode ? "| mp_charmap" : "| insertchar") : "") +
				(data.applies_templates ? "| noeditselect " : "") +
				(data.applies_connected_authoring === true ? " markeditable inserteditpoint " : "") +
				" | inlineembeddedcontent ",



			table_toolbar				: "tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol | tablegridtoggle tabletoolbarremove",
			valid_elements				: "span[test-id|class|paragraphclass|style|id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|nthelement|novaluedisqualify|type|sys_type_id|group_id|noedit|template_fixed_content|delimiter|is_compound|compound_fmt|smart_text_compound_type|include_type|insert_as_paragraph|inline_first|contenteditable|inst_id|merge_tables|block_content|drop_zero_decimals|content_menu_id|direction|dna]," +
				"var[test-id|class|style|id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|nthelement|novaluedisqualify|type|sys_type_id|group_id|delimiter|is_compound|compound_fmt|smart_text_compound_type|include_type|insert_as_paragraph|inline_first|contenteditable|inst_id|custom_value|empty_value|content_menu_id|description|name|mandatory|merge_tables|block_content|drop_zero_decimals|dna]," +
				"sup,sub,br,nbsp[class],i[class|style|contenteditable]," +
				(!data.snippet_mode ?
					"div[test-id|class|paragraphclass|style|id|decimals|thousandssep|negative_value|currencysymbol|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|nthelement|novaluedisqualify|type|sys_type_id|group_id|delimiter|is_compound|compound_fmt|smart_text_compound_type|include_type|insert_as_paragraph|inline_first|contenteditable|inst_id|r_dim|aspect_locked|orig_size|ondragstart|border_width|border_color|font_size|cell_count|max_length|size|color|div_width|border_type|radio_group|tab_order|rotate|container_name|merge_tables|block_content|drop_zero_decimals|is_selected|content_targeting_id|convert_targeting_id|selected_targeting_id|is_checked|selected_variable_id|submit_format|submit_method|content_menu_id|fit_to_width|minimize_width|mandatory|multiline|vertical_align|align|deselectable|operator|play_with_var_content|data-edit-mode|data-default-content|data-default-text-style|data-default-paragraph-style|segment_id|name|description|formatter-init|backer|direction|dna]," +
					"p[test-id|class|paragraphclass|style|lang|keep_with_next|keep_with_prev|keep_together|type|play_with_var_content|page_break|accessibility_suppressed|force_wrapping|start_front_facing|direction|text_align|spacing|tab_incr|line_spacing|indent_type|special_indent|custom_paragraph_id]," +
					"li[test-id|class|paragraphclass|style|keep_with_next|keep_with_prev|keep_together|accessibility_suppressed|force_wrapping|play_with_var_content]," +
					"ul[test-id|style|class|listclass|data-list-style-type|bullet_spacing|list_spacing|bullet_symbol|bullet_color|bullet_font|bullet_size|line_spacing|text_align|custom_list_id]," +
					"ol[test-id|style|class|listclass|continue|start|data-list-style-type|bullet_spacing|list_spacing|bullet_symbol|bullet_color|bullet_font|bullet_size|line_spacing|text_align|custom_list_id]," +
					"a[test-id|href|title|target|style|type|class]," +
					"img[test-id|class|style|src|rel_src|width|height|sandbox_file_id|image_file_id|orig_size|contenteditable|type|rotate|barcode_id|alt|dna]," +
					"table[test-id|mp_paste_table_column_widths|mp_paste_tab_stop_table|mp_paste_table_font_size_scaling_factor|mp_paste_table_align|mp_paste_table_margins|mp_paste_table_top_level_index|mp_paste_table_original_width|mp_paste_td_original_width|mp_paste_td_original_width_as_percent|mp_paste_table_max_allowed_width|class|style|t_align|cellpadding|cellspacing|c_dim|t_dim|t_width|mce_obj_id|omit_initial_header|omit_last_footer|id|full_width|data_group_id|table_margins|new_table|read_table|keep_with_next|keep_with_prev|keep_together|column_fixed_width|column_targeting_ids|bgcolor|border]," +
					"tr[test-id|class|style|content_targeting_id|data_group_id|row_type|repeat|row_height|row_height_type|keep_together_threshold|keep_with_next|keep_with_prev|keep_together|mce_action|alt_color_odd|alt_color_even|align|bgcolor|valign]," +
					"td[test-id|mp_paste_td|class|style|colspan|rowspan|cell_padding|cell_border|collapsed_cell_border|repeat_cell|on_flow|mce_action|align|valign|width|spanup|bgcolor]," +
					"th[test-id|class|style|colspan|rowspan|cell_padding|cell_border|repeat_cell|on_flow|mce_action|align|valign|width|spanup|bgcolor]," +
					"thead[test-id|class|style|align|valign]," +
					"tfoot[test-id|class|style|align|valign]," +
					"ins[test-id|segment_id|data-edit-mode|formatter-init|group_id|name|description|mandatory|content_targeting_id|convert_targeting_id],"+
					"data[test-id|content_targeting_id|convert_targeting_id]," +
					"mpr_variable[type],mpr_list_item[start|bullet]"
					:
					"p"),
			insertvariable_variablelist						: data.variables_list,
			async_variables									: data.async_variables,
			insertvariable_markerlist						: data.markers_list,
			insertembeddedcontent_embeddedcontentlist		: data.smart_text_list,
			async_embedded_content							: data.async_embedded_content,
			insertembeddedcontent_localcontentlist			: data.local_content_list,
			async_local_embedded_content					: data.async_local_embedded_content,
			insertplaceholders_placeholderslist				: data.placeholders_list,
			insertembeddedcontent_smartcanvaslist			: data.smart_canvas_list,
			insertembeddedcontent_localsmartcanvaslist		: data.local_smart_canvas_list,
			insertchar_charlist			: "&euro;&pound;&yen;&copy;&reg;&trade;" +
				(data.connector == 18 || data.connector == 19 ?  "" : "&#8480;") +
				"&lsquo;&rsquo;&ldquo;&rdquo;&div;",
			spellchecker_language		: 'CONTENT PLUGIN SPECIFIED',
			spellchecker_languages		: 'CONTENT PLUGIN SPECIFIED',
			spellchecker_rpc_url		: context + "/getSpellcheckData.form",
			img_upload_url				: context + "/content/tinymce_image_upload.form?tk=" + getParam('tk'),
			img_properties_url			: context + "/content/tinymce_image_properties.form?tk=" + getParam('tk'),
			table_properties_url		: context + "/content/tinymce_table_properties.form?tk=" + getParam('tk'),
			toc_properties_url		    : context + "/content/tinymce_toc_properties.form?tk=" + getParam('tk'),
			custom_list_properties_url	: context + "/content/tinymce_custom_list_properties.form?tk=" + getParam('tk'),
			custom_paragraph_styles_url	: context + "/content/tinymce_custom_paragraph_styles.form?tk=" + getParam('tk'),
			text_field_properties_url	: context + "/content/tinymce_text_field_properties.form?tk=" + getParam('tk'),
			menu_properties_url			: context + "/content/tinymce_menu_properties.form?tk=" + getParam('tk'),
			check_radio_properties_url	: context + "/content/tinymce_checkbox_radio_properties.form?tk=" + getParam('tk'),
			submit_button_properties_url: context + "/content/tinymce_submit_button_properties.form?tk=" + getParam('tk'),
			divider_url					: context + "/content/tinymce_divider_properties.jsp?tk=" + getParam('tk'),
			canvas_panel_url			: context + "/content/tinymce_canvas_panel.jsp?tk=" + getParam('tk'),
			content_targeting_url		: context + "/content/content_targeting_edit.form?tk=" + getParam('tk'),
			content_menu_url			: context + "/content/tinymce_content_menu.form?tk=" + getParam('tk'),
			links_url					: context + "/content/tinymce_links.form?tk=" + getParam('tk'),
			paragraph_properties_url	: context + "/content/tinymce_paragraph_properties.form?tk=" + getParam('tk'),
			barcodes_url				: context + "/content/tinymce_barcodes.form?tk=" + getParam('tk'),
			variant_compare_url			: context + "/content/variant_content_compare.form?tk=" + getParam('tk'),
			history_compare_url			: (data.rationalizer_application_id > 0 ? context + "/rationalizer/rationalizer_content_history.form?tk=" + getParam('tk') : context + "/content/content_history.form?tk=" + getParam('tk')),
			content_compare_url			: context + "/content/content_compare.form?tk=" + getParam('tk'),
			brand_check_url				: context + "/content/brand_check.form?tk=" + getParam('tk'),
			mark_editable_properties_url: context + "/content/tinymce_mark_editable_properties.form?tk=" + getParam('tk'),
			content_menu				: data.content_menu,
			dynamic_content_data		: data.dynamic_content_data,
			pre_proof_id				: data.pre_proof_id,
			communication_id			: data.communication_id,
			content_targeting_type		: "message",
			is_smart_text				: (data.is_smart_text ? true : false),
			canvas_inline_only			: (data.applies_forms || data.applies_images || data.is_dynamic_canvas || data.applies_barcodes) && !data.applies_freeform,
			canvas_freeform				: data.applies_freeform,
			canvas_dimensions			: data.canvas_dimensions,
			canvas_rotation				: data.canvas_rotation ? data.canvas_rotation : 0,
			canvas_forms				: data.applies_forms,
			zone_rotation				: data.zone_rotation,
			unrestricted_frame_width	: data.unrestricted_frame_width,
			is_dynamic_canvas			: data.is_dynamic_canvas,
			dynamic_canvas_bindings		: data.dynamic_canvas_bindings,
			zone_id						: data.zone_id,
			connector					: data.connector,
			channel						: data.channel,
			is_exstream_html			: data.is_exstream_html,
			is_exstream_dxf				: data.is_exstream_dxf,
			is_exstream_runtime_dxf		: data.is_exstream_runtime_dxf,
			entity_encoding				: "named+numeric",
			entities					: tinymce_entities,
			codemirror					: codemirror_config,
			content_object_id			: data.content_object_id,
			variant_id					: data.variant_id,
			document_ids				: data.document_ids,
			isVariableContentEnabled    : data.isVariableContentEnabled,
			rationalizer_application_id	: data.rationalizer_application_id,
			snippet_mode				: data.snippet_mode,
			menu_max_height				: data.menu_max_height,
			is_test_context				: data.is_test_context,
			is_translation_compare		: data.is_translation_compare,
			reset_data					: data.reset_data,
			branding					: false,
			apply_rulers				: data.apply_rulers,
			enforce_max_height			: data.is_new_editor ? false : data.enforce_max_height != undefined ? data.enforce_max_height : true,
			table_default_attributes	: {
				'new_table' : 'true'
			},
			content_menu				: {
				applied: data.content_menu ? data.content_menu.applied: false,
				mode: data.content_menu ? data.content_menu.mode : null
			},
			variable_data				: data.variable_data,
			allow_conditional_comments	: true,
			marcie_flags				: data.marcie_flags,
			applies_connected_authoring : data.applies_connected_authoring,
			is_render_as_tagged_text    : data.is_render_as_tagged_text,
			starter_style               : data.starter_style != undefined ? data.starter_style : null,
			supports_custom_paragraph   : data.supports_custom_paragraph != undefined ? data.supports_custom_paragraph : false,
			is_new_editor				: data.is_new_editor
		};
}

function tinyMCEemailInit(width, height, data) {

	// Text Styles: Format JSON
	if ( data.text_data != undefined && data.text_data != null)
		for (var i=0; i < data.text_data.length; i++)
			data.text_data[i] = mpParseJSON( data.text_data[i] );
	if ( data.paragraph_style_data != undefined && data.paragraph_style_data != null)
		for (var i=0; i < data.paragraph_style_data.length; i++)
			data.paragraph_style_data[i] = mpParseJSON( data.paragraph_style_data[i] );

	var applyMenu = data.apply_menu != undefined ? data.apply_menu : true;

	tinymceEditorDef_standard =
		{
			// Location of TinyMCE script
			script_url					: context + '/includes/javascript/' + tinymceDir + '/tinymce.min.js',

			language					: data.context_lang != undefined ? (data.context_lang == 'zz' || data.context_lang == 'xx' || data.context_lang == '' ? 'en' : data.context_lang) :
				global_data.userContextLangCode,

			// Skin options
			theme						: "modern",
			skin						: "lightgray",

			width						: "100%",
			height						: height,
			resize						: false,
			plugins: 					[	"mp_session,mp_zone,mp_custommenu,mp_clipboard,mp_clear,mp_textstyles,mp_paragraphstyle,mp_tagpanel," +
			"mp_liststyle," +
			(data.applies_freeform || data.applies_forms || data.applies_images || data.is_dynamic_canvas || data.applies_barcodes ? "mp_canvas," : "") +
			(data.applies_freeform || data.applies_forms ? "mp_canvasforms," : "") +
			(data.applies_freeform || data.applies_images ? "mp_canvasimages," : "") +
			(data.can_edit_source == undefined || data.can_edit_source ? "mp_codemirror," : "") +
			"mp_repeatingtablerows," +
			"mp_links," +
			"mp_paragraphlistproperties,mp_paragraph," +
			"advlist,mp_listproperties,mp_customlistproperties," +
			(data.supports_custom_paragraph ? "mp_customparagraphstyles," : "") +
			(data.apply_reset ? "mp_reset," : "") +
			(data.variant_id && data.variant_id != null && data.content_object_id ? "mp_variants," : "") +
			(data.content_object_id ? ("mp_history," + (data.is_translation_compare ? "mp_defaultcontentcompare," : "")) : "") +
			( !applyWebSpellChecker ? "spellchecker," : "" ) +
			"searchreplace,table,lists,paste,noneditable,mp_nonbreaking,fullscreen,mp_charmap",
				"mp_insertchar,mp_insertvariable,mp_insertembeddedcontent,mp_insertplaceholders,colorpicker,textcolor",
				"mp_tableproperties,mp_cleaner,mp_menus,mp_contentmenu,mp_textdirection," +
				(data.isVariableContentEnabled == undefined || data.isVariableContentEnabled ? "mp_contenttargeting," : "") +
				(common.isFeatureEnabled('GPTRewriteUsingMarkup') ? "mp_contentintel_proto," : "mp_contentintel,") +
				"mp_markeditable,mp_pastecheck," +
				(data.table_of_contents_enabled ? "mp_tableofcontents," : "")
			],
			init_instance_callback		: "editorActionsInit",
			setup						: function(ed) {
				tinyMCEsetup(ed);
			},
			paste_preprocess			: function(o,a) {
				tinyMCEpastePreprocess(o,a);
			},
			relative_urls				: true,
			content_css					: data.content_css +
				( cssApplied(data.paragraph_style_css) ? ',' + data.paragraph_style_css : '') +
				( cssApplied(data.list_style_css) ? ',' + data.list_style_css : '' ),
			paragraph_style_css			: data.paragraph_style_css,
			paragraph_style_data		: data.paragraph_style_data,
			list_style_css				: data.list_style_css,
			list_style_data				: data.list_style_data,
			style_formats				: ( attrApplied(data.text_data) ? data.text_data : []),

			menubar						: applyMenu ? 'edit,format,content,insert,tables,freeform,forms,tools' : false,
			menu	 					: {
				edit: {title: client_messages.content_editor.menu_edit, items:
						"undo redo | cut copy paste pastetext clear " +
						(data.apply_reset ? " reset " : "") +
						" | selectall | searchreplace "
				},
				format: {title: client_messages.content_editor.menu_format, items:
						" subscript superscript | mp_paragraph " +
						(data.applies_freeform || data.applies_images ? " mp_imageproperties " : "") +
						" mp_listproperties " +
						(data.is_dynamic_canvas ? " | canvassize " : "")
				},
				insert: {title: client_messages.content_editor.menu_insert, items:
						" mp_link mp_unlink mp_anchor mp_bookmark mp_alttext mp_removealttext | " +
						(data.applies_freeform || data.applies_images ? " imageupload " : "") +
						(data.content_menu && data.content_menu.mode == "edit" && data.content_menu.applied ? " contentmenu " : "") +
						" inlinetargeting " +
						(qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [18, 19] }) ? " tableofcontents " : "") +
						" nonbreaking"
				},
				tables: {title: client_messages.content_editor.menu_tables, items:
						(data.applies_tables ? "inserttable tableprops deletetable tabletoolbartoggle | cell row column " : "") +
						(data.applies_tables && qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [3,4],'connectors': [9,16,18,19,((data.is_exstream_runtime_dxf || data.is_exstream_html) ? 3 : 0)] }) ? " rowtargeting " : "" ) +
						(data.applies_tables && qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [18,19,((data.is_exstream_runtime_dxf || data.is_exstream_html) ? 3 : 0)] }) ? " columntargeting " : "" )
				},
				freeform: {title: client_messages.content_editor.menu_freeform, items:
						(data.applies_freeform ? " canvaspanel guides " : "")
				},
				tools: {title: client_messages.content_editor.menu_tools, items:
						'code ' +
						(!applyWebSpellChecker ? ' spellchecker ' : '') +
						' tagpanel clipboard mp_contentintel tooltipstoggle resetworkspace'},
				content: {title: client_messages.content_editor.menu_content, items:
						'insertembeddedcontent insertlocalcontent insertconstants insertvariable insertplaceholders ' +
						(data.support_unicode ? 'mp_charmap' : 'insertchar') +
						(data.variant_id && data.variant_id != null && data.content_object_id ? ' | mp_variants ' : '') +
						(data.content_object_id ? (' mp_history ' + (data.is_translation_compare ? ' mp_defaultcontentcompare ' : '')) : '') +
						' | contentrendering samplevaluerendering '
				}
			},
			toolbar1					: (!applyMenu ? "clear " : "") +
				(!applyMenu && data.apply_reset ? "reset " : "") +
				(!applyMenu ? "cut copy pastetext | undo redo | " : "") +
				(data.applies_text_styles ? "styleselect mp_textstyle mp_bold mp_italic mp_underline mp_pointsize mp_color" : "") +
				(data.applies_paragraph_styles ? " paragraphstyle mp_alignleft mp_aligncenter mp_alignright mp_alignjustify mp_linespacing | " : "") +
				(!data.snippet_mode ? " mp_outdent mp_indent | " : " ") +
				" bullist numlist | " +
				(!applyMenu ? " mp_link mp_unlink mp_anchor mp_bookmark mp_alttext mp_removealttext mp_paragraph | " : "") +
				(!applyMenu ? " subscript superscript | " : "") +
				(!applyMenu && data.is_dynamic_canvas ? "canvassize " : "") +
				(!applyMenu && (data.applies_freeform || data.applies_images) ? " imageupload " : "") +
				(!applyMenu && data.applies_freeform ? " canvaspanel guides " : "") +
				(!applyMenu && data.applies_freeform || data.applies_images ? " | " : "") +
				// mp_barcode textfield menu checkboxinput radioinput submitbutton
				(!applyMenu && (data.content_menu && data.content_menu.mode == "edit" && data.content_menu.applied) ? " contentmenu " : "") +
				(!applyMenu ? " | nonbreaking | searchreplace code | fullscreen " : "") +
				(!applyMenu && data.applies_tables ? " | mp_table " : "") +
				(!applyMenu && data.applies_tables && qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [3,4],'connectors': [9,16,((data.is_exstream_runtime_dxf || data.is_exstream_html) ? 3 : 0)] }) ? "targeting " : "" ) +
				(data.applies_list_styles  || qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [(data.is_exstream_dxf || data.is_exstream_html ? 3 : 0),19] }) ? "liststyle" : "") +
				(!applyMenu || qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [3,4],'connectors': [9,((data.is_exstream_dxf || data.is_exstream_html) ? 3 : 0),16,18,19] }) ? " | mp_paragraphlistproperties " : "" ) +
				// rotateleft rotateright
				(!applyMenu ? " | insertembeddedcontent insertlocalcontent insertplaceholders " : "") +
				// insertsmartcanvas insertlocalsmartcanvas
				(!applyMenu && data.applies_freeform ? " smartcanvas " : "") +
				(!applyMenu ? " | insertvariable " : "") +
				// insertmarker
				(!applyMenu ? (data.support_unicode ? "| mp_charmap" : "| insertchar") : "") +
				(data.applies_connected_authoring === true ? "| markeditable inserteditpoint " : "") +
				" | inlineembeddedcontent ",
			table_toolbar				: "tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol | tablegridtoggle tabletoolbarremove",
			valid_elements				: "span[test-id|class|paragraphclass|style|id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|nthelement|novaluedisqualify|type|sys_type_id|group_id|style|noedit|template_fixed_content|delimiter|is_compound|compound_fmt|smart_text_compound_type|include_type|insert_as_paragraph|inline_first|contenteditable|inst_id|merge_tables|block_content|drop_zero_decimals|content_menu_id|direction|dna]," +
				"var[test-id|id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|nthelement|novaluedisqualify|type|sys_type_id|group_id|class|style|delimiter|is_compound|compound_fmt|smart_text_compound_type|include_type|insert_as_paragraph|inline_first|inst_id|custom_value|empty_value|content_menu_id|description|name|mandatory|merge_tables|block_content|drop_zero_decimals|dna]," +
				"div[test-id|paragraphclass|id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|nthelement|novaluedisqualify|type|sys_type_id|group_id|class|style|delimiter|is_compound|compound_fmt|smart_text_compound_type|include_type|insert_as_paragraph|inline_first|inst_id|r_dim|aspect_locked|orig_size|ondragstart|size|color|div_width|border_type|radio_group|rotate|container_name|merge_tables|block_content|drop_zero_decimals|is_selected|content_targeting_id|convert_targeting_id|selected_targeting_id|is_checked|selected_variable_id|submit_format|submit_method|content_menu_id|deselectable|operator|play_with_var_content|data-edit-mode|data-default-content|data-default-text-style|data-default-paragraph-style|name|description|mandatory|formatter-init|backer|direction|dna]," +
				"sup,sub,br,nbsp[class]," +
				"p[test-id|class|paragraphclass|style|lang|type|play_with_var_content|page_break|start_front_facing|direction|text_align|spacing|tab_incr|line_spacing|indent_type|special_indent|custom_paragraph_id]," +
				"li[test-id|class|paragraphclass|style|play_with_var_content]," +
				"ul[test-id|style|class|listclass|data-list-style-type|bullet_spacing|list_spacing|bullet_symbol|bullet_color|bullet_font|bullet_size|line_spacing|text_align|custom_list_id]," +
				"ol[test-id|style|class|listclass|continue|start|data-list-style-type|bullet_spacing|list_spacing|bullet_symbol|bullet_color|bullet_font|bullet_size|line_spacing|text_align|custom_list_id]," +
				"a[test-id|href|title|target|style|type|class]," +
				"img[test-id|class|style|src|rel_src|width|height|sandbox_file_id|orig_size|image_file_id|contenteditable|type|rotate|barcode_id|alt|dna]," +
				"table[test-id|class|style|t_align|cellpadding|cellspacing|c_dim|t_dim|t_width|mce_obj_id|omit_initial_header|omit_last_footer|id|full_width|data_group_id|table_margins|new_table|read_table|keep_with_next|keep_with_prev|keep_together|column_fixed_width|column_targeting_ids]," +
				"tr[test-id|class|style|content_targeting_id|data_group_id|row_type|repeat|row_height|row_height_type|keep_together_threshold|keep_with_next|keep_with_prev|keep_together|mce_action|alt_color_odd|alt_color_even]," +
				"td[test-id|class|style|colspan|rowspan|cell_padding|cell_border|collapsed_cell_border|repeat_cell|on_flow|mce_action|align|valign|width],th[test-id|class|style|colspan|rowspan|cell_padding|cell_border|repeat_cell|on_flow|mce_action|align|valign|width]," +
				"thead[test-id|class|style],tfoot[test-id|class|style],i[class|style|contenteditable]," +
				"ins[test-id|segment_id|data-edit-mode|formatter-init|group_id|name|description|mandatory|class|content_targeting_id|convert_targeting_id]," +
				"data[test-id|content_targeting_id|convert_targeting_id],",

			insertvariable_variablelist						: data.variables_list,
			async_variables									: data.async_variables,
			insertembeddedcontent_embeddedcontentlist		: data.smart_text_list,
			async_embedded_content							: data.async_embedded_content,
			insertembeddedcontent_localcontentlist			: data.local_content_list,
			async_local_embedded_content					: data.async_local_embedded_content,
			insertplaceholders_placeholderslist				: data.placeholders_list,
			insertembeddedcontent_smartcanvaslist			: data.smart_canvas_list,
			insertembeddedcontent_localsmartcanvaslist		: data.local_smart_canvas_list,
			insertchar_charlist			: "&euro;&pound;&yen;&copy;&reg;&trade;&#8480;&lsquo;&rsquo;&ldquo;&rdquo;&div;",
			spellchecker_language		: 'CONTENT PLUGIN SPECIFIED',
			spellchecker_languages		: 'CONTENT PLUGIN SPECIFIED',
			spellchecker_rpc_url		: context+"/getSpellcheckData.form",
			img_upload_url				: context + "/content/tinymce_image_upload.form?tk=" + getParam('tk'),
			img_properties_url			: context + "/content/tinymce_image_properties.form?tk=" + getParam('tk'),
			table_properties_url		: context + "/content/tinymce_table_properties.form?tk=" + getParam('tk'),
			toc_properties_url		    : context + "/content/tinymce_toc_properties.form?tk=" + getParam('tk'),
			custom_list_properties_url	: context + "/content/tinymce_custom_list_properties.form?tk=" + getParam('tk'),
			custom_paragraph_styles_url	: context + "/content/tinymce_custom_paragraph_styles.form?tk=" + getParam('tk'),
			text_field_properties_url	: context + "/content/tinymce_text_field_properties.form?tk=" + getParam('tk'),
			menu_properties_url			: context + "/content/tinymce_menu_properties.form?tk=" + getParam('tk'),
			check_radio_properties_url	: context + "/content/tinymce_checkbox_radio_properties.form?tk=" + getParam('tk'),
			submit_button_properties_url: context + "/content/tinymce_submit_button_properties.form?tk=" + getParam('tk'),
			divider_url					: context + "/content/tinymce_divider_properties.jsp?tk=" + getParam('tk'),
			content_targeting_url		: context + "/content/content_targeting_edit.form?tk=" + getParam('tk'),
			content_menu_url			: context + "/content/tinymce_content_menu.form?tk=" + getParam('tk'),
			links_url					: context + "/content/tinymce_links.form?tk=" + getParam('tk'),
			paragraph_properties_url	: context + "/content/tinymce_paragraph_properties.form?tk=" + getParam('tk'),
			variant_compare_url			: context + "/content/variant_content_compare.form?tk=" + getParam('tk'),
			history_compare_url			: context + "/content/content_history.form?tk=" + getParam('tk'),
			content_compare_url			: context + "/content/content_compare.form?tk=" + getParam('tk'),
			brand_check_url				: context + "/content/brand_check.form?tk=" + getParam('tk'),
			mark_editable_properties_url: context + "/content/tinymce_mark_editable_properties.form?tk=" + getParam('tk'),
			content_menu				: data.content_menu,
			dynamic_content_data		: data.dynamic_content_data,
			pre_proof_id				: data.pre_proof_id,
			communication_id			: data.communication_id,
			content_targeting_type		: "message",
			canvas_dimensions			: data.canvas_dimensions,
			canvas_rotation				: data.canvas_rotation ? data.canvas_rotation : 0,
			canvas_freeform				: data.applies_freeform,
			zone_rotation				: data.zone_rotation,
			unrestricted_frame_width	: data.unrestricted_frame_width,
			is_dynamic_canvas			: data.is_dynamic_canvas,
			dynamic_canvas_bindings		: data.dynamic_canvas_bindings,
			is_smart_text				: (data.is_smart_text ? true : false),
			canvas_inline_only			: (data.applies_forms || data.applies_images || data.is_dynamic_canvas || data.applies_barcodes) && !data.applies_freeform,
			zone_id						: data.zone_id,
			connector					: data.connector,
			channel						: data.channel,
			is_exstream_html 			: data.is_exstream_html,
			is_exstream_dxf				: data.is_exstream_dxf,
			is_exstream_runtime_dxf		: data.is_exstream_runtime_dxf,
			entity_encoding				: "named+numeric",
			entities					: tinymce_entities,
			codemirror					: codemirror_config,
			content_object_id			: data.content_object_id,
			variant_id					: data.variant_id,
			document_ids				: data.document_ids,
			isVariableContentEnabled    : data.isVariableContentEnabled,
			rationalizer_application_id	: data.rationalizer_application_id,
			menu_max_height				: data.menu_max_height,
			is_test_context				: data.is_test_context,
			is_translation_compare		: data.is_translation_compare,
			reset_data					: data.reset_data,
			branding					: false,
			apply_rulers				: data.apply_rulers,
			enforce_max_height			: data.enforce_max_height != undefined ? data.enforce_max_height : true,
			table_default_attributes	: {
				'new_table' : 'true'
			},
			content_menu				: {
				applied: data.content_menu ? data.content_menu.applied: false,
				mode: data.content_menu ? data.content_menu.mode : null
			},
			variable_data				: data.variable_data,
			allow_conditional_comments	: true,
			marcie_flags				: data.marcie_flags,
			allow_conditional_comments	: true,
			applies_connected_authoring : data.applies_connected_authoring
		};
}

function tinyMCEsimpleTextEditorInit(width, height, editorType, data) {

	var applyMenu = data.apply_menu != undefined ? data.apply_menu : true;

	tinymceEditorDef_simpleText =
		{
			// Location of TinyMCE script
			script_url					: context + '/includes/javascript/' + tinymceDir + '/tinymce.min.js',

			language					: data.context_lang != undefined ? (data.context_lang == 'zz' || data.context_lang == 'xx' || data.context_lang == '' ? 'en' : data.context_lang) :
				global_data.userContextLangCode,

			// Skin options
			theme						: "modern",
			skin						: "lightgray",

			width						: "100%",
			height						: height,
			resize						: false,
			plugins						: [	(data.symbols == undefined || data.symbols ? "mp_insertchar," : "") +
			"mp_zone,mp_custommenu,mp_insertvariable,mp_insertembeddedcontent," +
			(!applyWebSpellChecker && (data.spellcheck == undefined || data.spellcheck) ? "spellchecker," : "") +
			(data.can_edit_source == undefined || data.can_edit_source ? "mp_codemirror," : "") +
			"searchreplace,table",
				"mp_simpletext,paste,noneditable,mp_cleaner,mp_charmap,mp_menus"
			],
			init_instance_callback		: "editorActionsInit",
			relative_urls				: true,
			content_css					: data.content_css,
			menubar						: applyMenu ? 'edit,content,tools' : false,
			menu	 					: {
				edit: {title: client_messages.content_editor.menu_edit, items:
						"undo redo | cut copy paste pastetext clear " +
						" | selectall | searchreplace "
				},
				tools: {title: client_messages.content_editor.menu_tools, items:
						'code ' +
						(!applyWebSpellChecker && (data.spellcheck == undefined || data.spellcheck) ? " spellchecker " : "") +
						'tooltipstoggle'
				},
				content: {title: client_messages.content_editor.menu_content, items:
						'insertembeddedcontent insertlocalcontent insertvariable insertconstants ' +
						(data.symbols == undefined || data.symbols ?
							(data.support_unicode ? "| mp_charmap" : "| insertchar") :
							"") +
						' | contentrendering samplevaluerendering '
				}
			},
			toolbar1					: (!applyMenu ?
				"cut copy | undo redo | searchreplace | code "  +
				(!applyWebSpellChecker && (data.spellcheck == undefined || data.spellcheck) ? "spellchecker " : "") +
				"| insertembeddedcontent insertlocalcontent | insertconstants | insertvariable " +
				((data.symbols == undefined || data.symbols) ?
					(data.support_unicode ? "| mp_charmap" : "| insertchar") :
					"")
				: false),
			valid_elements				: "span[test-id|id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|nthelement|novaluedisqualify|type|sys_type_id|group_id|class|delimiter|is_compound|compound_fmt|smart_text_compound_type|include_type|insert_as_paragraph|inline_first|inst_id|block_content|drop_zero_decimals|dna]," +
				"var[test-id|id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|nthelement|novaluedisqualify|type|sys_type_id|group_id|class|delimiter|is_compound|compound_fmt|smart_text_compound_type|include_type|insert_as_paragraph|inline_first|inst_id|block_content|drop_zero_decimals|dna]," +
				"i[test-id|class|style|contenteditable]" +
				(editorType == "MULTILINE" ? ",p" : ""),
			insertvariable_variablelist						: data.variables_list,
			async_variables									: data.applies_smart_text == false ? null : data.async_variables,
			insertembeddedcontent_embeddedcontentlist		: data.applies_smart_text == false ? null : data.smart_text_list,
			async_embedded_content							: data.applies_smart_text == false ? null : data.async_embedded_content,
			insertembeddedcontent_localcontentlist			: data.applies_smart_text == false ? null : data.local_content_list,
			async_local_embedded_content					: data.applies_smart_text == false ? null : data.async_local_embedded_content,
			paste_as_text				: true,
			paste_retain_style_properties	: "",
			paste_preprocess			: function(plugin, args) {

			},
			is_smart_text				: (data.is_smart_text ? true : false),
			zone_id						: data.zone_id,
			document_ids				: data.document_ids,
			isVariableContentEnabled    : data.isVariableContentEnabled,
			connector					: data.connector,
			channel						: data.channel,
			insertchar_charlist			: "&euro;&pound;&yen;&copy;&reg;&trade;&#8480;&lsquo;&rsquo;&ldquo;&rdquo;&div;",
			spellchecker_language		: 'CONTENT PLUGIN SPECIFIED',
			spellchecker_languages		: 'CONTENT PLUGIN SPECIFIED',
			spellchecker_rpc_url		: context+"/getSpellcheckData.form",
			enable_multi_line			: editorType == "MULTILINE" ? true : false,
			entity_encoding				: "named+numeric",
			entities					: tinymce_entities,
			codemirror					: codemirror_config,
			menu_max_height				: data.menu_max_height,
			branding					: false,
			variable_data				: data.variable_data,
			persist_workspace			: false,
			is_render_as_tagged_text    : data.is_render_as_tagged_text,
			content_object_id			: data.content_object_id
		};
}

function tinyMCEvalueEditorInit(editorId, width, height, data) {

	var applyMenu = data.apply_menu != undefined ? data.apply_menu : true;

	tinymceEditorDef_value =
		{
			// Location of TinyMCE script
			script_url						: context + '/includes/javascript/' + tinymceDir + '/tinymce.min.js',

			language					: data.context_lang != undefined ? (data.context_lang == 'zz' || data.context_lang == 'xx' || data.context_lang == '' ? 'en' : data.context_lang) :
				global_data.userContextLangCode,

			// Skin options
			theme							: "modern",
			skin							: "lightgray",

			width							: "100%",
			height							: height,
			resize							: false,
			plugins							: (data.can_edit_source == undefined || data.can_edit_source ? "mp_codemirror," : "") +
				"paste,noneditable,mp_zone,mp_custommenu,mp_insertvariable,mp_insertembeddedcontent,mp_cleaner,mp_charmap,mp_menus,mp_lookupexpressions,mp_contentmenu," +
				((editorId == 'graphicImageAltText' && data.is_translation_compare) ? "mp_defaultcontentcompare," : ""),
			init_instance_callback			: "editorActionsInit",
			relative_urls					: true,
			content_css						: data.content_css,
			menubar							: applyMenu ? 'edit,content,insert,tools' : false,
			menu	 						: {
				edit: {title: client_messages.content_editor.menu_edit, items:
						"undo redo | cut copy paste pastetext clear " +
						" | selectall | searchreplace "
				},
				tools: {title: client_messages.content_editor.menu_tools, items: 'code tooltipstoggle'},
				content: {title: client_messages.content_editor.menu_content, items:
						'insertembeddedcontent insertlocalcontent insertvariable insertconstants insertmarker ' +
						((editorId == 'graphicImageAltText' && data.is_translation_compare) ? ' mp_defaultcontentcompare ' : '') +
						' | contentrendering samplevaluerendering '
				},
				insert: {title: client_messages.content_editor.menu_insert, items:
						(data.content_menu && data.content_menu.mode == "edit" && data.content_menu.applied ? " contentmenu " : "")
				}
			},
			toolbar							: (!applyMenu || data.lookup_table_data || data.data_element_data ?
				(!applyMenu ? "cut copy | undo redo | insertembeddedcontent insertlocalcontent insertconstants insertvariable insertmarker" : "") +
				(data.lookup_table_data || data.data_element_data ? " insertlookuptable insertdataelement " : "") +
				(!applyMenu ? " | code" : "") :
				false ),
			valid_elements					: "span[test-id|id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|nthelement|novaluedisqualify|type|sys_type_id|group_id|class|delimiter|is_compound|compound_fmt|smart_text_compound_type|include_type|insert_as_paragraph|inline_first|inst_id|block_content|drop_zero_decimals|content_menu_id|dna]," +
				"var[test-id|id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|nthelement|novaluedisqualify|type|sys_type_id|group_id|class|delimiter|is_compound|compound_fmt|smart_text_compound_type|include_type|insert_as_paragraph|inline_first|inst_id|block_content|drop_zero_decimals|content_menu_id|dna]," +
				"div[test-id|id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|nthelement|novaluedisqualify|type|sys_type_id|group_id|class|delimiter|is_compound|compound_fmt|smart_text_compound_type|include_type|insert_as_paragraph|inline_first|inst_id|block_content|drop_zero_decimals|content_menu_id|dna]," +
				"p,i[test-id|class|style|contenteditable]",
			insertvariable_variablelist						: data.variables_list,
			async_variables									: data.async_variables,
			insertvariable_markerlist						: data.markers_list,
			insertembeddedcontent_embeddedcontentlist		: data.smart_text_list,
			async_embedded_content							: data.async_embedded_content,
			insertembeddedcontent_localcontentlist			: data.local_content_list,
			async_local_embedded_content					: data.async_local_embedded_content,
			document_ids					: data.document_ids,
			isVariableContentEnabled        : data.isVariableContentEnabled,
			lookup_table_data				: data.lookup_table_data,
			data_element_data				: data.data_element_data,
			entity_encoding					: "named+numeric",
			entities						: tinymce_entities,
			codemirror						: codemirror_config,
			menu_max_height					: data.menu_max_height,
			zone_id							: data.zone_id,
			connector						: data.connector,
			channel							: data.channel,
			content_object_id				: data.content_object_id,
			variant_id						: data.variant_id,
			document_ids					: data.document_ids,
			branding						: false,
			history_compare_url				: context + "/content/content_history.form?tk=" + getParam('tk') + ((editorId == 'graphicImageAltText')?'&altTextCompare=true':''),
			content_menu_url				: context + "/content/tinymce_content_menu.form?tk=" + getParam('tk'),
			content_menu					: {
				applied: data.content_menu ? data.content_menu.applied: false,
				mode: data.content_menu ? data.content_menu.mode : null
			},
			persist_workspace				: false,
			is_render_as_tagged_text        : data.is_render_as_tagged_text,
			enforce_max_height				: data.enforce_max_height != undefined ? data.enforce_max_height : true,
		};

	if ( data.is_plugin_editor != undefined && data.is_plugin_editor )
		$('#'+editorId).tinymce(tinymceEditorDef_value);
	else
		$('.mceEditor_'+editorId).tinymce(tinymceEditorDef_value);
}

function tinyMCEdescriptionEditorInit(editorId, width, height, data) {
	tinymceEditorDef_value =
		{
			// Location of TinyMCE script
			script_url					: context + '/includes/javascript/' + tinymceDir + '/tinymce.min.js',
			language					: data.context_lang != undefined ? (data.context_lang == 'zz' || data.context_lang == 'xx' || data.context_lang == '' ? 'en' : data.context_lang) :
				global_data.userContextLangCode,
			inline						: data.inline !== undefined? data.inline: false,
			// Skin options
			theme						: "modern",
			skin						: "lightgray",

			width						: "100%",
			height						: height,
			resize						: false,
			plugins						: (data.can_edit_source == undefined || data.can_edit_source ? "mp_codemirror," : "") +
				"autolink,paste,table,lists,advlist,fullscreen,link,colorpicker,textcolor,mp_cleaner,lists",
			relative_urls				: true,
			content_css					: data.content_css,
			menubar 					: false,
			toolbar1					: "cut copy | bold italic underline strikethrough forecolor backcolor | subscript  superscript | alignleft aligncenter alignright alignjustify",
			toolbar2					: "undo redo | bullist numlist | link unlink | table | code fullscreen",
			valid_elements				: "span[id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|type|sys_type_id|group_id|class|style|dna]," +
				"div[id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|type|sys_type_id|group_id|class|style|contenteditable|dna]," +
				"br,nbsp[class],p[class|paragraphclass|style|direction|text_align|spacing|tab_incr|line_spacing|indent_type|special_indent|custom_paragraph_id],li[class|paragraphclass|style]," +
				"ul[style|class|listclass|data-list-style-type|bullet_spacing|list_spacing|bullet_symbol|bullet_color|bullet_font|bullet_size|line_spacing|text_align|custom_list_id]," +
				"ol[style|class|listclass|data-list-style-type|continue|start|bullet_spacing|list_spacing|bullet_symbol|bullet_color|bullet_font|bullet_size|line_spacing|text_align|custom_list_id]," +
				"a[href|title|target|class]," +
				"table[class|style|t_align|bgcolor|border|cellpadding|cellspacing|c_dim|t_dim|t_width|mce_obj_id|data_group_id],tr[class|style|align|bgcolor|valign|mce_action]," +
				"td[class|style|align|bgcolor|colspan|rowspan|valign|mce_action],th[class|style|align|bgcolor|colspan|rowspan|valign|mce_action]," +
				"thead[class|style|align|valign],tfoot[class|style|align|valign],sup,sub,strong,em,i[class|style]",
			target_list 				: [ {text: 'New window', value: '_blank'} ],
			entity_encoding				: "named+numeric",
			entities					: tinymce_entities,
			codemirror					: codemirror_config,
			menu_max_height				: data.menu_max_height,
			zone_id						: data.zone_id,
			connector					: data.connector,
			channel						: data.channel,
			content_object_id			: data.content_object_id,
			variant_id					: data.variant_id,
			document_ids				: data.document_ids,
			isVariableContentEnabled    : data.isVariableContentEnabled,
			branding					: false
		};

	$('.mceEditor_'+editorId).tinymce(tinymceEditorDef_value);
}

function tinyMCEprimitiveInit(editorId) {
	tinymceEditorDef_primitive =
		{
			// Location of TinyMCE script
			script_url					: context + '/includes/javascript/' + tinymceDir + '/tinymce.min.js',
			language					: global_data.userContextLangCode,
			inline						: false,
			// Skin options
			theme						: "modern",
			skin						: "lightgray",
			valid_elements				: "*[*]",
			entity_encoding				: "named+numeric",
			entities					: tinymce_entities,
		};

	$(editorId).tinymce(tinymceEditorDef_primitive);
}

function renderingManagerTinyMceInit(data, postInitCallback) {
	var applyMenu = true;
	tinymceEditorDef_value =
		{
			connected: true,
			// Location of TinyMCE script
			script_url					: context + '/includes/javascript/' + tinymceDir + '/tinymce.min.js',
			language					: data.context_lang != undefined ? (data.context_lang == 'zz' || data.context_lang == 'xx' || data.context_lang == '' ? 'en' : data.context_lang) :
				global_data.userContextLangCode,
			inline						: data.inline !== undefined? data.inline: false,
			// Skin options
			theme						: "modern",
			skin						: "lightgray",

			width						: data.width,
			height						: data.height,
			resize						: false,
			plugins						:["mp_connectedutils,mp_zone,mp_connectedcustommenu,mp_clear,mp_connectedtextstyles," +
			(data.applies_paragraph_styles ? "mp_connectedparagraphstyle," : "") +
			(data.applies_interactive_images ? "mp_connectedimages," : "") +
			"mp_connectedliststyle, mp_connectedpasteastext, mp_connectedcontinuousedit, mp_connectedpagededit, mp_paragraph," + (data.new_undo_disabled ? "" : "mp_connectedundo,mp_connectedredo,") +
			"advlist,mp_connectedcustomlistproperties,mp_connectedcustomparagraphstyles,mp_connectedparagraphlistproperties," +
			"mp_noeditselect," +
			"spellchecker,searchreplace,table,lists,noneditable,mp_nonbreaking,mp_charmap",
				"mp_insertchar,mp_insertvariable,mp_connectededitvariables,mp_connectedinsertembeddedcontent,mp_insertplaceholders,colorpicker,textcolor",
				"mp_connectedtableproperties,mp_contenttargeting,mp_menus,mp_contentmenu,mp_textdirection," +
				(common.isFeatureEnabled('GPTRewriteUsingMarkup') ? "mp_contentintel_proto," : "mp_contentintel,") +
				(!applyMenu && data.content_menu && data.content_menu.applied ? " ,contentmenu " : "")
			],
			relative_urls				: true,
			content_css					: data.content_css,
			menubar						: false,
			toolbar1					: (data.applies_text_styles ? " styleselect mp_textstyle mp_bold mp_italic mp_underline mp_pointsize mp_color | " : "") +
				(data.applies_paragraph_styles ? " paragraphstyle mp_alignleft mp_aligncenter mp_alignright mp_alignjustify mp_linespacing | mp_indent mp_outdent | " : "")
				+ (data.is_paged_view ? " continuousedit | " : "pagededit | ") ,
			toolbar2					: "pastetext | " + (data.new_undo_disabled ? "undo redo | " : "mp_connectedundo mp_connectedredo | ") + "bullist numlist | " +
				(data.applies_list_styles ? " liststyle | " : "") +
				// (data.applies_tables && qualifyChannelAndConnectors(data.channel, data.connector, {'channels': [],'connectors': [9,16,18,19,0] }) ? " | targeting " : "" ) +
				(data.applies_table ? " | mp_table " : "")  +
				("| insertvariable | editvariables | insertembeddedcontent " ) +
				(data.applies_interactive_images ? " | interactiveimageupload " : "") +
				(data.applies_list_styles || data.applies_list_styles ? " | mp_connectedparagraphlistproperties" : "")
			,

			table_toolbar				: "",
			valid_elements				: "span[test-id|id|noedit|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|type|sys_type_id|group_id|class|style|data-content-piece-id|direction|dna]," +
				"div[test-id|id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|type|sys_type_id|group_id|class|style|contenteditable|r_dim|aspect_locked|content_targeting_id|convert_targeting_id|data-edit-mode|data-content-piece-id|data-inline-image|data-default-content|data-default-text-style|data-default-paragraph-style|direction|dna]," +
				"br[test-id|class|data-content-piece-id],nbsp[class|data-content-piece-id]," +
				"p[test-id|class|paragraphclass|style|data-content-piece-id|direction|keep_with_next|keep_with_prev|keep_together|text_align|spacing|tab_incr|line_spacing|indent_type|special_indent|custom_paragraph_id]," +
				"li[test-id|class|paragraphclass|style|data-content-piece-id]," +
				"ul[test-id|style|class|listclass|data-list-style-type|data-content-piece-id|bullet_spacing|list_spacing|bullet_symbol|bullet_color|bullet_font|bullet_size|line_spacing|text_align|custom_list_id]," +
				"ol[test-id|style|class|listclass|continue|start|data-list-style-type|data-content-piece-id|bullet_spacing|list_spacing|bullet_symbol|bullet_color|bullet_font|bullet_size|line_spacing|text_align|custom_list_id|start-from-previous|previous-start|continue-from-previous]," +
				"a[test-id|href|title|target|class|data-content-piece-id]," +
				"table[test-id|mp_paste_table_column_widths|mp_paste_tab_stop_table|mp_paste_table_font_size_scaling_factor|mp_paste_table_align|mp_paste_table_margins|mp_paste_table_top_level_index|mp_paste_table_original_width|mp_paste_td_original_width|mp_paste_td_original_width_as_percent|mp_paste_table_max_allowed_width|class|style|t_align|bgcolor|border|cellpadding|cellspacing|c_dim|t_dim|t_width|mce_obj_id|omit_initial_header|omit_last_footer|id|full_width|data_group_id|table_margins|new_table|read_table|keep_with_next|keep_with_prev|keep_together|data-content-piece-id|column_fixed_width|column_targeting_ids]," +
				"tr[test-id|id|class|style|align|bgcolor|valign|content_targeting_id|data_group_id|row_type|repeat|row_height|row_height_type|keep_together_threshold|keep_with_next|keep_with_prev|keep_together|mce_action|alt_color_odd|alt_color_even|data-content-piece-id]," +
				"td[test-id|mp_paste_td|id|class|style|bgcolor|colspan|rowspan|cell_padding|cell_border|collapsed_cell_border|repeat_cell|on_flow|mce_action|align|valign|width|spanup|data-content-piece-id]," +
				"th[test-id|id|class|style|bgcolor|colspan|rowspan|cell_padding|cell_border|repeat_cell|on_flow|mce_action|align|valign|width|spanup|data-content-piece-id]," +
				"thead[test-id|id|class|style|align|valign|data-content-piece-id]," +
				"tfoot[test-id|class|style|align|valign|data-content-piece-id],sup,sub,i[test-id|class|style|data-content-piece-id]," +
				"ins[test-id|segment_id|data-edit-mode|group_id|name|description|mandatory|content_targeting_id|convert_targeting_id|data-content-piece-id],"+
				"data[test-id|content_targeting_id|convert_targeting_id|data-content-piece-id]," +
				"img[test-id|class|style|src|rel_src|width|height|sandbox_file_id|orig_size|user_defined_size|image_file_id|contenteditable|type|rotate|barcode_id|alt|data-content-piece-id|data-inline-image-guid|data-inline-image-file-name|data-inline-image-file-type|dna]," +
				"var[test-id|class|data-content-piece-id|style|id|decimals|thousandssep|negative_value|fmtcat|currencysymbol|symbol_location|neg_parenthesis|neg_red_text|mask_type|mask|case|datedelta|fmt|trimonnull|applypossessive|substr_1based|nthelement|novaluedisqualify|type|sys_type_id|group_id|delimiter|is_compound|compound_fmt|smart_text_compound_type|include_type|insert_as_paragraph|inline_first|contenteditable|inst_id|custom_value|empty_value|content_menu_id|description|name|mandatory|merge_tables|block_content|drop_zero_decimals|dna]",
			target_list 				: [ {text: 'New window', value: '_blank'} ],
			entity_encoding				: "named+numeric",
			entities					: tinymce_entities,
			codemirror					: codemirror_config,
			zone_id						: data.zone_id,
			style_formats				: ( attrApplied(data.text_data) ? data.text_data : []),
			paragraph_style_css			: "no_css",
			paragraph_style_data		: data.paragraph_style_data,
			list_style_css				: "no_css",
			list_style_data				: data.list_style_data,
			list_properties				: data.list_properties,
			custom_list_properties_url	: context + "/content/tinymce_custom_list_properties.form?tk=" + getParam('tk'),
			custom_paragraph_styles_url	: context + "/content/tinymce_custom_paragraph_styles.form?tk=" + getParam('tk'),
			connected_custom_list_properties_url	: context + "/content/tinymce_connected_custom_list_properties.form?tk=" + getParam('tk'),
			connected_custom_paragraph_styles_url	: context + "/content/tinymce_connected_custom_paragraph_styles.form?tk=" + getParam('tk'),
			content_targeting			: data.content_targeting,
			branding					: false,
			insertvariable_variablelist	: data.variables_list,
			insertembeddedcontent_embeddedcontentlist: data.smart_text,
			table_properties_url		: context + "/content/tinymce_table_properties.form?tk=" + getParam('tk'),
			toc_properties_url		    : context + "/content/tinymce_toc_properties.form?tk=" + getParam('tk'),
			init_instance_callback      : postInitCallback? postInitCallback: function (editor) {

			},
			paste_as_text				: false,
			setup: function (editor) {
				if (!data.new_undo_disabled) {
					editor.on('init', function () {
						editor.undoManager.clear();
					});
					editor.addCommand('Undo', function () { });
					editor.addCommand('Redo', function () { });
					editor.on('BeforeExecCommand', function(e) {
						if (e.command.toLowerCase() === 'undo') {
							e.preventDefault(); // Prevent the default behavior
						}
						if (e.command.toLowerCase() === 'redo') {
							e.preventDefault(); // Prevent the default behavior
						}
					});
				}
				if (data.setup) {
					data.setup(editor);
				}
			},
			marcie_flags: data.marcie_flags,
			connectedContentIntel: data.connectedContentIntel,
			brand_check_url				: context + "/content/brand_check.form?tk=" + getParam('tk'),
			img_properties_url			: context + "/content/tinymce_connected_image_properties.form?tk=" + getParam('tk'),
			images_dataimg_filter		: data.applies_interactive_images ? () => { return false; } : null,
			communication_id			: data.communication_id
		};

	$('[data-tiny-mce-id='+"'"+data.tinyMceId+"']").tinymce(tinymceEditorDef_value);
}

window.connected_document_history_url	= context + "/content/tinymce_connected_document_history.form?tk=" + getParam('tk');

function attrApplied(attr) {
	return attr != undefined && attr != null && attr != '';
}

function cssApplied(css_path) {
	return attrApplied(css_path) && css_path !== "no_css";
}

function spellCheckInit(inst) {
	if ( applyWebSpellChecker ) {
		if (window.WEBSPELLCHECKER != undefined && $('#wscJavascript').length != 0) {
			window.WEBSPELLCHECKER_CONTAINER = inst.iframeElement;
			window.WEBSPELLCHECKER.init({
				container: window.WEBSPELLCHECKER_CONTAINER,
			});
			// Enforce default lang: Previous context may override otherwise
			var wscInstances = window.WEBSPELLCHECKER.getInstances();
			if (wscInstances != undefined && wscInstances != null && wscInstances.length > 0)
				if(window.spellcheckLang){
					wscInstances[0].setLang(window.spellcheckLang);
					window.WEBSPELLCHECKER_CONFIG.lang = window.spellcheckLang;
				} else {
					wscInstances[0].setLang(window.WEBSPELLCHECKER_CONFIG.lang);
				}
		} else {
			setTimeout(function () {
				spellCheckInit(inst);
			}, 250);
		}
	}
}

function editorActionsInit(inst) {
	if ( tinyMCE.get(inst.id) != null ) {
		$(tinyMCE.get(inst.id).getBody()).editorAction({editorId: inst.id});
		spellCheckInit(inst);
	} else {
		console.log("NULL editor instance: " + inst.id);
	}

	if ( $.isFunction(tinymceInitCallback) )
		tinymceInitCallback(inst);
}

function tinyMCEsetup(ed) {
	ed.prinovaPlugins = {};

	ed.settings.paste_as_text = true;
	ed.on('init', function(args) {
		ed.settings.paste_as_text = true;
	});
}

function tinyMCEpastePreprocess(plugin, args) {
	while ( args.content.indexOf('content_targeting_id') != -1 ) {
		var endIndex = args.content.indexOf('"', args.content.indexOf('content_targeting_id="') + ('content_targeting_id="').length);
		var targetingStr = args.content.substring( args.content.indexOf('content_targeting_id="'), endIndex + 1);
		args.content = args.content.replace(targetingStr,"");
	};
	while ( args.content.indexOf('mce_obj_id') != -1 ) {
		var endIndex = args.content.indexOf('"', args.content.indexOf('mce_obj_id="') + ('mce_obj_id="').length);
		var objIdStr = args.content.substring( args.content.indexOf('mce_obj_id="'), endIndex + 1);
		args.content = args.content.replace(objIdStr,"");
	};
}

function qualifyChannelAndConnectors(channel,connector,o) {
	var channelsQualify = null;
	var connectorsQualify = null;

	var contextChannels = channel;
	if ( contextChannels && !$.isArray(contextChannels))
		contextChannels = [contextChannels];
	var contextConnectors = connector;
	if ( contextConnectors && !$.isArray(contextConnectors))
		contextConnectors = [contextConnectors];

	if (o.channels && o.channels.length > 0 && contextChannels && contextChannels.length > 0 ) {
		channelsQualify = false;
		for ( var i = 0; i < contextChannels.length; i++ )
			if ( o.channels.indexOf(contextChannels[i]) != -1 || o.channels.indexOf(parseInt(contextChannels[i])) != -1 )
				channelsQualify = true;
	}
	if (o.connectors && o.connectors.length > 0 && contextConnectors && contextConnectors.length > 0   ) {
		connectorsQualify = false;
		for ( var i = 0; i < contextConnectors.length; i++ )
			if ( o.connectors.indexOf(contextConnectors[i]) != -1 || o.connectors.indexOf(parseInt(contextConnectors[i])) != -1 )
				connectorsQualify = true;
	}
	return (channelsQualify != null && channelsQualify) || (connectorsQualify != null && connectorsQualify);
}

var tinymce_entities = '160,nbsp,161,iexcl,162,cent,163,pound,164,curren,165,yen,166,brvbar,167,sect,168,uml,169,copy,170,ordf,'
	+ '171,laquo,172,not,173,shy,174,reg,175,macr,176,deg,177,plusmn,178,sup2,179,sup3,180,acute,181,micro,182,para,'
	+ '183,middot,184,cedil,185,sup1,186,ordm,187,raquo,188,frac14,189,frac12,190,frac34,191,iquest,192,Agrave,193,Aacute,'
	+ '194,Acirc,195,Atilde,196,Auml,197,Aring,198,AElig,199,Ccedil,200,Egrave,201,Eacute,202,Ecirc,203,Euml,204,Igrave,'
	+ '205,Iacute,206,Icirc,207,Iuml,208,ETH,209,Ntilde,210,Ograve,211,Oacute,212,Ocirc,213,Otilde,214,Ouml,215,times,'
	+ '216,Oslash,217,Ugrave,218,Uacute,219,Ucirc,220,Uuml,221,Yacute,222,THORN,223,szlig,224,agrave,225,aacute,226,acirc,'
	+ '227,atilde,228,auml,229,aring,230,aelig,231,ccedil,232,egrave,233,eacute,234,ecirc,235,euml,236,igrave,237,iacute,'
	+ '238,icirc,239,iuml,240,eth,241,ntilde,242,ograve,243,oacute,244,ocirc,245,otilde,246,ouml,247,divide,248,oslash,'
	+ '249,ugrave,250,uacute,251,ucirc,252,uuml,253,yacute,254,thorn,255,yuml,402,fnof,913,Alpha,914,Beta,915,Gamma,916,Delta,'
	+ '917,Epsilon,918,Zeta,919,Eta,920,Theta,921,Iota,922,Kappa,923,Lambda,924,Mu,925,Nu,926,Xi,927,Omicron,928,Pi,929,Rho,'
	+ '931,Sigma,932,Tau,933,Upsilon,934,Phi,935,Chi,936,Psi,937,Omega,945,alpha,946,beta,947,gamma,948,delta,949,epsilon,'
	+ '950,zeta,951,eta,952,theta,953,iota,954,kappa,955,lambda,956,mu,957,nu,958,xi,959,omicron,960,pi,961,rho,962,sigmaf,'
	+ '963,sigma,964,tau,965,upsilon,966,phi,967,chi,968,psi,969,omega,977,thetasym,978,upsih,982,piv,8226,bull,8230,hellip,'
	+ '8242,prime,8243,Prime,8254,oline,8260,frasl,8472,weierp,8465,image,8476,real,8482,trade,8501,alefsym,8592,larr,8593,uarr,'
	+ '8594,rarr,8595,darr,8596,harr,8629,crarr,8656,lArr,8657,uArr,8658,rArr,8659,dArr,8660,hArr,8704,forall,8706,part,8707,exist,'
	+ '8709,empty,8711,nabla,8712,isin,8713,notin,8715,ni,8719,prod,8721,sum,8722,minus,8727,lowast,8730,radic,8733,prop,8734,infin,'
	+ '8736,ang,8743,and,8744,or,8745,cap,8746,cup,8747,int,8756,there4,8764,sim,8773,cong,8776,asymp,8800,ne,8801,equiv,8804,le,8805,ge,'
	+ '8834,sub,8835,sup,8836,nsub,8838,sube,8839,supe,8853,oplus,8855,otimes,8869,perp,8901,sdot,8968,lceil,8969,rceil,8970,lfloor,'
	+ '8971,rfloor,9001,lang,9002,rang,9674,loz,9824,spades,9827,clubs,9829,hearts,9830,diams,338,OElig,339,oelig,352,Scaron,353,scaron,'
	+ '376,Yuml,710,circ,732,tilde,8194,ensp,8195,emsp,8201,thinsp,8204,zwnj,8205,zwj,8206,lrm,8207,rlm,8211,ndash,8212,mdash,8216,lsquo,'
	+ '8217,rsquo,8218,sbquo,8220,ldquo,8221,rdquo,8222,bdquo,8224,dagger,8225,Dagger,8240,permil,8249,lsaquo,8250,rsaquo,8364,euro,'
	+ '381,#381,382,#382,'
	+ '258,#258,259,#259,260,#260,261,#261,262,#262,263,#263,268,#268,269,#269,270,#270,271,#271,272,#272,273,#273,280,#280,281,#281,282,#282,283,#283,'
	+ '313,#313,314,#314,317,#317,318,#318,321,#321,322,#322,323,#323,324,#324,327,#327,328,#328,336,#336,337,#337,340,#340,341,#341,344,#344,345,#345,'
	+ '346,#346,347,#347,350,#350,351,#351,354,#354,355,#355,356,#356,357,#357,366,#366,367,#367,368,#368,369,#369,377,#377,378,#378,379,#379,380,#380,729,#729';