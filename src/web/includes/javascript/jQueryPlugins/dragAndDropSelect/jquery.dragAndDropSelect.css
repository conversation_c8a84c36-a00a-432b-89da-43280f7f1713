.dragAndDropLevelIcon {
	display: inline-block;
	margin-right: 4px;
	margin-left: 2px;
	color: #eee;
	font-size: 10px;
}

.dragAndDropBackground {
	background: url('images/connector_background.gif') no-repeat center 65px;
}
.dragAndDropItem {
	padding: 2px 8px;
	font-size: 12px;
	color: #454545;
	cursor: pointer;
}
.dragAndDropItemsContainer {
	background-color: #fff;
	border: #ccc solid 1px;
	width: 340px;
	height: 150px;
	overflow: auto;
}
.dragAndDropSizing_small .dragAndDropItemsContainer {
	height: 120px;
}
.dragAndDropItemHighlight {
	color: #fff;
	background-color: #777;
}
.dragAndDropItemHov {
	background-color: #999;
	color: #fff;
}
.dragAndDropItemSelected {
	color: #999;
	cursor: default;
}
.dragAndDropOrderBtnDown, .dragAndDropOrderBtnDown_hover, .dragAndDropOrderBtnDown_mousedown, .dragAndDropOrderBtnDown_disabled,
.dragAndDropOrderBtnUp, .dragAndDropOrderBtnUp_hover, .dragAndDropOrderBtnUp_mousedown, .dragAndDropOrderBtnUp_disabled {
	width: 47px;
	height: 27px;
	margin: 5px 3px;	
}
.dragAndDropOrderBtnDown {
	background: url('images/orderingButtons.gif') no-repeat 0px -27px;
}
.dragAndDropOrderBtnDown_hover {
	background: url('images/orderingButtons.gif') no-repeat -47px -27px;
}
.dragAndDropOrderBtnDown_mousedown {
	background: url('images/orderingButtons.gif') no-repeat -94px -27px;
}
.dragAndDropOrderBtnDown_disabled {
	background: url('images/orderingButtons.gif') no-repeat -141px -27px;
}
.dragAndDropOrderBtnUp {
	background: url('images/orderingButtons.gif') no-repeat 0px 0px;
}
.dragAndDropOrderBtnUp_hover {
	background: url('images/orderingButtons.gif') no-repeat -47px 0px;
}
.dragAndDropOrderBtnUp_mousedown {
	background: url('images/orderingButtons.gif') no-repeat -94px 0px;
}
.dragAndDropOrderBtnUp_disabled {
	background: url('images/orderingButtons.gif') no-repeat -141px 0px;
}
.dragAndDropOrderIndicator {
	text-align: center;
	color: #454545;
	vertical-align: middle;
	float: left;
	margin-right: 5px;
	width: 33px;
	height: 15px;
	background: url('images/orderIndicator.gif') no-repeat center center;
}
.dragAndDropOrderingTable TD {
	padding: 0px;	
}
.dragAndDropTokenCheck, .dragAndDropTokenX {
	width: 40px;
	height: 40px;
	font-weight: bold;
	font-size: 18px;
	color: #777;
	z-index: 1000;
}
.dragAndDropTokenCheck {
	background: url('images/dragAndDropToken.gif') no-repeat center top;
}
.dragAndDropTokenX {
	background: url('images/dragAndDropToken.gif') no-repeat center bottom;
}
.dragAndDropSearchValue {
    padding: 3.5px 3px;
    font-size: 12px;  
    text-shadow: 0px 1px 0px #fff;  
    outline: none;  
    background: -webkit-gradient(linear, left top, left bottombottom, from(#efefef), to(#ffffff));  
    background: -moz-linear-gradient(top,  #efefef,  #ffffff);  
    -webkit-border-radius: 3px;  
    -moz-border-radius: 3px;  
    border-radius: 3px;  
    border: 1px solid #bdbdbd;  
    -webkit-box-shadow: 1px 1px 0px #efefef;  
    -moz-box-shadow: 1px 1px 0px #efefef;  
    box-shadow:  1px 1px 0px #efefef;  	
}
.dragAndDropSearchValue:focus {  
    -webkit-box-shadow: 0px 0px 5px #007eff;  
    -moz-box-shadow: 0px 0px 5px #007eff;  
    box-shadow: 0px 0px 5px #007eff;  
}
.dragAndDropMask {
	position: absolute;
	background-color: #999;
	z-index: 49;
    -webkit-border-radius: 6px;  
    -moz-border-radius: 6px;  
    border-radius: 6px;  
background-image: -moz-radial-gradient(#bbb, #f5f5f5);
background-image: -webkit-radial-gradient(#bbb, #f5f5f5);
}