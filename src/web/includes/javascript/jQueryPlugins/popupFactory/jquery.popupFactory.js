/** popupFactory r1 // 2012.11.08 // jQuery 1.7.2 // Prinova */

function popupFactoryRemove(id) {
    var currentWindow = window;
    var nestedFrameInPopup = false;
    while ( isFrameAccessible(currentWindow.parent) && currentWindow.parent && currentWindow.parent != null && currentWindow != getTopFrame() ) {
        if ( $(currentWindow.parent.document).find($(currentWindow.frameElement)).closest('.popupFactory_contentContainer,.contentPopup_popupContainer').length != 0 )
            nestedFrameInPopup = true;
        currentWindow = currentWindow.parent;
    }

    if ( !nestedFrameInPopup )
        $(getTopFrame().document).find('.popupFactory_popupContainer,.contentPopup_popupContainer').remove();
}

(function scrollManager() {

    var scrollTop = 0;
    var $document = $(document);

    $document.on('scroll mousewheel DOMMouseScroll popupFactory.scroll', function (e, documentScrollTop) {
        _.defer(function() {
            if (window !== getTopFrame()) {
                getTopFrame().$(getTopFrame().document).trigger('popupFactory.scroll', [$(this).scrollTop()]);
                return;
            }

            var iFramePopupWrapperScrollTop =  $('div[class^="iFramePopupWrapper_"]').scrollTop();
            var currentScrollTop = documentScrollTop || iFramePopupWrapperScrollTop || 0;
            if (currentScrollTop && scrollTop !== currentScrollTop) {

                $('.popupFactory_popupContainer').each(function () {
                    var popupTop = $(this).position().top;
                    $(this).css('top', popupTop - currentScrollTop + scrollTop + 'px');
                });

                scrollTop = currentScrollTop;
            }
        });
    });
})();




(function($) {
    $.popupFactory = {
        get	: function (obj) {
            var o = $(obj);
            if(!o.size()) o = $("#" + obj);
            if(!o.size()) return null;
            return popupFactory_component.inst[o.attr('id')] || null;
        },
        defaults : {
            popup_id				: null,
            trigger					: 'hover',
            offsetWindow			: window,
            referenceWindow			: 'top',
            popupLocation			: 'bottom',
            popupTopPosition		: null,
            arrowPosition			: null,
            cssPath					: "../includes/javascript/jQueryPlugins/popupFactory/popupFactory.css",
            content					: "",
            width					: 200,
            title					: client_messages.title.note,
            fnSetContent 			: null,
            asyncSetContentURL		: null,
            asyncSetContentHandler  : null,
            asyncDataType			: 'json',
            beforePopupClose		: null,
            beforePopupOpen			: null,
            afterPopup				: null,
            preventPopup			: null,
            stopPropagation			: false,
            pollForRemove			: false,
            concurrentPopups		: false,
            popupOffset             : null,
            zIndex                  : null,
            documentClickListener   : null,
            documentClickTarget     : null
        }
    };

    $.fn.popupFactory = function (opts) {
        let result = this.each(function() {
            var conf = $.extend({},opts);
            if (conf !== false) {
                let popupFactoryComponent = new popupFactory_component();
                popupFactoryComponent.init(this, conf);
            }
        });

        return result;
    };

    function popupFactory_component () {
        return {

            data : $.extend({},$.popupFactory.defaults),

            init : function(elem, conf) {
                var _this = this;

                _this.data = $.extend(true, {}, this.data, conf);
                _this.targetEle = $(elem);

                if ( _this.data.referenceWindow == "local")
                    _this.appendToWindow = window;
                else
                    _this.appendToWindow = getTopFrame();

                popupFactory_component.inst[$(elem).attr("id")] = _this;

                $(_this.targetEle).css({'cursor':'default'});

                // CSS Include: Popup is built top window
                if ( $(_this.appendToWindow.document).find('#popupFactoryCSS').length == 0 && ( "no_css" !== _this.data.cssPath ) ) {
                    $(_this.appendToWindow.document).find('head').append("<link id=\"popupFactoryCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + _this.data.cssPath + "\" />");
                }

                // Init triggers
                if ( _this.data.trigger == "dual" || _this.data.trigger == "hover" )
                    $(_this.targetEle).hoverIntent({
                        sensitivity: 50, // number = sensitivity threshold (must be 1 or higher)
                        interval: 700,   // number = milliseconds of polling interval
                        over: function(e) {
                            if ( $(_this.popupEle).is(':visible') ) return; else _this.initPopup(e);
                        },  // function = onMouseOver callback (required)
                        out: function() {
                            if ( $(_this.popupEle).is(':visible') && _this.triggerEvent != "hover" ) return; else _this.closePopup('hover');
                        }    // function = onMouseOut callback (required)
                    });
                if ( _this.data.trigger == "dual" || _this.data.trigger == "dblclick" )
                    $(_this.targetEle).dblclick( function(e) {
                        _this.initPopup(e);
                    });
                else if ( _this.data.trigger == "click" )
                    $(_this.targetEle).click( function(e) {
                        _this.initPopup(e);
                    });
                else if ( _this.data.trigger == "instant" )
                    _this.initPopup(null);

                if ( _this.data.pollForRemove )
                    setTimeout( function() {
                        _this.pollTargetEle();
                    }, 250);
            },

            closePopup : function(type) {
                var _this = this;

                if ( $.isFunction(_this.data.beforePopupClose) )
                    _this.data.beforePopupClose(_this);

                // Prevent popup clear on multi init of same element (ex. dbl click and hover for two different uses)
                if ( !(_this.data.trigger == "dual" || _this.data.trigger == "hover") && type == "hover" )
                    return;
                else
                    $(_this.appendToWindow.document).find('#' + _this.data.popup_id).each( function() {
                        $(this).remove();
                    });
            },

		getVisibility : function(ele) {
			if ( !ele )
				return ["none","none"];

			var $element = $(ele);
			var element = $element[0];
			var doc = element.ownerDocument;
			var win = doc.defaultView || doc.parentWindow;

			var visArray = new Array();
	        var pos = $(element).offset(),
	        	wY = $(win).scrollTop() == undefined && win != undefined ? win.scrollY : $(win).scrollTop(),
	            wH = $(win).height(),
	            oH = $(element).outerHeight();

			if (pos.top >= wY && oH + pos.top <= wY + wH )
		        	visArray[1] = "full";
	        else if ( ((pos.top <= wY && pos.top + oH > wY) ||
	             (pos.top  >= wY && pos.top  <= wY + wH)) )
	        	visArray[1] = "partial";
	        else
	        	visArray[1] = "none";

	        var wX = $(window).scrollLeft(),
            	wW = $(window).width(),
            	oW = $(element).outerWidth();

	        if (pos.left >= wX && oW + pos.left <= wX + wW )
	        	visArray[0] = "full";
	        else if ( ((pos.left <= wX && pos.left + oW > wX) ||
	             (pos.left >= wX && pos.left <= wX + wW)) )
	        	visArray[0] = "partial";
	        else
	        	visArray[0] = "none";

	        return visArray;
		},

        calculatePopupOffset : function() {
            var _this = this;

            _this.data.popupOffset = $(_this.targetEle).offset();
            if ( _this.data.referenceWindow == "top" )
                _this.data.popupOffset =_this.computeFrameOffset( _this.data.offsetWindow, $(_this.targetEle).offset() );

            // Target item is no longer visible: Abort init
            if ( $(_this.targetEle).outerHeight() == 0 || $(_this.targetEle).outerWidth() == 0 )
                return;

            if ( _this.data.popupLocation == 'bottom' ) {
                // If popup location is bottom but element bottom is not visible, switch to location left
                if ( _this.getVisibility(_this.targetEle)[1] == "full" ) {
                    _this.data.popupOffset.top += $(_this.targetEle).outerHeight() + 6;
                }
            }
            if ( _this.data.popupLocation == 'bottom-left' ) {
                _this.data.popupOffset.top += $(_this.targetEle).outerHeight() + 6;
                _this.data.popupOffset.left += $(_this.targetEle).outerWidth() - _this.data.width + 6;
            }
            if ( _this.data.popupLocation == 'right' ) {
                _this.data.popupOffset.left += $(_this.targetEle).outerWidth() + 6;
                popupArrowClass = "popupFactory_leftArrow";
            }
            if ( _this.data.popupLocation == 'left' ) {
                _this.data.popupOffset.left += -_this.data.width - 6;

                // In case popup is out of bounds
                _this.data.popupOffset.left = _this.data.popupOffset.left > 6 ? _this.data.popupOffset.left : 6;
            } else if ( _this.data.popupLocation == 'custom' ) {
                _this.data.popupOffset.top = _this.data.popupTopPosition;
                _this.data.popupOffset.left = ($(_this.targetEle).position().left + ($(_this.targetEle).outerWidth() / 2)) - (_this.data.width / 2);
            }

            return _this.data.popupOffset;
        },
		
		initPopup : function(e) {
			var _this = this;

			if ( $.isFunction(_this.data.preventPopup) )
				if ( _this.data.preventPopup(_this) )
					return;

			if ( $.isFunction(_this.data.beforePopupOpen) )
				_this.data.beforePopupOpen(_this)

			_this.is_animation_init = false;

			_this.triggerEvent = _this.data.trigger;
			if (e != null && e.type == "mouseover")
				_this.triggerEvent = "hover";

			// Target item is no longer visible: Abort init
			if ( $(_this.targetEle).outerHeight() == 0 || $(_this.targetEle).outerWidth() == 0 )
				return;

			// Popup arrow: Resolve arrow type and positioning (based on popup positioning)
			var popupArrowClass = "", arrowLeft = 0, arrowTop = 0;
			if ( _this.data.popupLocation == 'bottom' ) {
				// If popup location is bottom but element bottom is not visible, switch to location left
				if ( _this.getVisibility(_this.targetEle)[1] == "full" ) {
					popupArrowClass = "popupFactory_topArrow";
					arrowTop = - 7;
					arrowLeft = Math.min((_this.targetEle).outerWidth() / 2, 150);
				} else {
					_this.data.popupLocation = 'left';
				}
			}
			if ( _this.data.popupLocation == 'bottom-left' ) {
				popupArrowClass = "popupFactory_topArrow";
				arrowTop = - 7;
				arrowLeft = _this.data.width - 20;
			}  
			if ( _this.data.popupLocation == 'right' ) {
				popupArrowClass = "popupFactory_leftArrow";
				arrowTop = 5;
				arrowLeft = - 7;
			}  
			if ( _this.data.popupLocation == 'left' ) {
				popupArrowClass = "popupFactory_rightArrow";
				arrowTop = 5;
				arrowLeft = _this.data.width;
			} else if ( _this.data.popupLocation == 'custom' ) {
                popupArrowClass = "popupFactory_topArrow";
                arrowTop = _this.data.arrowPosition.top;
                arrowLeft = _this.data.arrowPosition.left;
            }

			_this.calculatePopupOffset();

			_this.closePopup('clear_stale');

			if ( _this.data.popup_id == null )
				_this.data.popup_id = "popupFactory_popupContainer_" + _this.data.trigger + "_" + (new Date()).getTime();

			// HTML: Popup definition
			var popupWidth = (isNaN(_this.data.width) ? _this.data.width :_this.data.width + "px");
			var popupHTML = "<div id=\"" + _this.data.popup_id + "\" class=\"popupFactory_popupContainer current_popup\" style=\"" +
                " width: " + popupWidth + "; " +
                ( _this.data.zIndex != null ? " z-index: " + _this.data.zIndex + ";" : "") +
                " left: " + _this.data.popupOffset.left + "px; top: " + _this.data.popupOffset.top + "px;\">" +
								"<div style=\"width: " + popupWidth + "; height: 10px; position: absolute; left: 0px; top: " + arrowTop + "px;\">" +
									"<div class=\"" + popupArrowClass + "\" style=\"left:" + arrowLeft + "px; top: 0px;\"></div>" +
								"</div>" +
								"<div class=\"popupFactory_headerContainer\">" +
									"<div  class=\"popupFactory_titleContainer\" >" +
											client_messages.text.loading +
										"</div>" +
										(_this.triggerEvent != "hover" ?
											("<i class=\"fa fa-times popupFactory_closeIcon\"></i>") : "") +
											"</div>" +
								"<div class=\"popupFactory_loadingContainer\" >" +
										"<div class=\"popupFactory_loadingIcon\"><i class=\"fa fa-spinner fa-pulse\" ></i></div>" +
									"</div>" +
								"<div class=\"popupFactory_contentContainer\" >" +
								"</div>" +
							"</div>";
			
			if ( !_this.data.concurrentPopups )
				$(_this.appendToWindow.document).find('.popupFactory_popupContainer,.contentPopup_popupContainer').remove();
			$(_this.appendToWindow.document).find('.popupFactory_popupContainer').removeClass('current_popup');
			$(_this.appendToWindow.document).find('body').append(popupHTML);

			if ( _this.data.stopPropagation )
				$(_this.appendToWindow.document).find('.popupFactory_popupContainer.current_popup').click( function(e) {
					e.stopPropagation();
				});

                // Close popup: If click anywhere outside popup
                if ( _this.triggerEvent != "hover" && _this.triggerEvent != "click" )
                    _this.initFrameClickHandler(_this.data.offsetWindow);

			_this.popupEle = $(_this.appendToWindow.document).find('.popupFactory_popupContainer.current_popup');
			


                // Close Icon: Init close on click
                if ( _this.triggerEvent != "hover" )
                    $(_this.popupEle).find('.popupFactory_closeIcon').click( function(e) {
                        _this.closePopup('closeBtn');
                        if (_this.data.stopPropagation) {
                            e.stopPropagation();
                        }
                    });

                // Set content
                if ( _this.data.asyncSetContentURL != null ) {

                    _this.popupEle.find('.popupFactory_loadingContainer').show();

                    // Set content: Async
                    $.ajax({
                        type: "GET",
                        url: _this.data.asyncSetContentURL,
                        dataType: _this.data.asyncDataType,
                        success: function(data) {
                            _this.processContent(data);
                            resizePopupCheck(_this);
                            setTimeout( function() { _this.popupEle.find('.popupFactory_contentContainer input:visible:first,.popupFactory_contentContainer textarea:visible:first,.popupFactory_contentContainer select:visible:first').focus(); }, 300 );
                        }
                    });

                } else {

                    // Set content: Static
                    if ( $.isFunction(_this.data.fnSetContent) )
                        _this.data.content = _this.data.fnSetContent(_this);

                    _this.popupEle.find('.popupFactory_loadingContainer').hide();
                    let _contentContainer = _this.popupEle.find('.popupFactory_contentContainer');
                    _contentContainer.append( $(_this.data.content) ).showEle('normal');
                    if (_this.appendToWindow.IS_INTERACTIVE) {
                        _contentContainer.css("max-height", "200px");
                        _contentContainer.css("overflow-x", "hidden");
                        _contentContainer.css("overflow-y", "scroll");
                    }

                    resizePopupCheck(_this);

                    setTimeout( function() { _this.popupEle.find('.popupFactory_contentContainer input:visible:first,.popupFactory_contentContainer textarea:visible:first,.popupFactory_contentContainer select:visible:first').focus(); }, 300 );

                }

                // Set title
                _this.popupEle.find('.popupFactory_titleContainer').html(_this.data.title);

                $(getTopFrame().document).find('div[class^="iFramePopupWrapper_"]').scroll( function() {
                    var offset = _this.calculatePopupOffset();
                    $(_this.popupEle).css({'top' : offset.top+'px', 'left' : offset.left+'px'});
                });

                if ( $.isFunction(_this.data.afterPopup) )
                    _this.data.afterPopup(_this);

            },

            computeFrameOffset : function(win, dims) {
                var _this = this;

                // Init
                if (typeof dims === 'undefined') {
                    var dims = { top: 0, left: 0 };
                }

                // Resolve target iFrame: Find <iframe> within parent window
                var frames = win.parent.document.getElementsByTagName('iframe');
                var frame;
                var found = false;

                for (var i=0, len=frames.length; i<len; i++) {
                    frame = frames[i];
                    if (frame.contentWindow == win) {
                        found = true;
                        break;
                    }
                }

                // Calculate and Recurse
                if (found) {
                    var rect = $(frame).offset();
                    dims.left += rect.left - frame.contentWindow.pageXOffset + parseFloat($(frame).css('padding-left').replace('px',''));
                    dims.top += rect.top - frame.contentWindow.pageYOffset + parseFloat($(frame).css('padding-top').replace('px',''));
                    if (win !== top) {
                        _this.computeFrameOffset(win.parent, dims);
                    }
                }
                return dims;
            },

            initFrameClickHandler : function(win) {
                var _this = this;

                var doc = win.contentWindow || win.contentDocument || win;
                if (doc.document)
                    doc = doc.document;

                this.data.documentClickListener = function(e) {
                    if ( !e || !e.target)
                        return;

                    var targetEle = eventTarget(e);
                    if ( !($(targetEle).is('.popupFactory_popupContainer') || $(targetEle).closest('.popupFactory_popupContainer').length > 0 || $('.ui-colorpicker-dialog:visible')) )
                        _this.closePopup('docClick');

                };
                this.data.documentClickTarget = doc;
                this.data.documentClickTarget.addEventListener('click', this.data.documentClickListener);

                // find our <iframe> tag within our parent window
                var frames = win.parent.document.getElementsByTagName('iframe');
                var frame;
                var found = false;

                for (var i=0, len=frames.length; i<len; i++) {
                    frame = frames[i];
                    if (frame.contentWindow == win) {
                        found = true;
                        break;
                    }
                }

                // Recur up the frame chain
                if (found)
                    if (win !== top)
                        _this.initFrameClickHandler(win.parent);
            },

            pollTargetEle: function() {
                var _this = this;

                if ( $(_this.targetEle) != undefined && $(_this.targetEle) != null && $(_this.targetEle).is(':visible') )
                    setTimeout( function() { _this.pollTargetEle(); }, 250);
                else
                    _this.closePopup('poll');
            },

            processContent: function(data) {
                var _this = this;

                if ( $.isFunction(_this.data.asyncSetContentHandler) )
                    _this.data.content = _this.data.asyncSetContentHandler(_this, data);

                _this.popupEle.find('.popupFactory_loadingContainer').hide();
                _this.popupEle.find('.popupFactory_contentContainer').append( $(_this.data.content) ).showEle('normal');

            }

        }; // end component

        function resizePopupCheck(instance) {
            var insertedPopup = $("#" + instance.data.popup_id, getTopFrame().document);
            var titleWidth = $("#" + instance.data.popup_id + " .popupFactory_titleContainer", getTopFrame().document).width();

            if (insertedPopup.width() < titleWidth) {
                insertedPopup.width(titleWidth + 54);
            }
        }

    };

    // instance manager
    popupFactory_component.inst = {};

})(jQuery);