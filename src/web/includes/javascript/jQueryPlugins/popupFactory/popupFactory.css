.popupFactory_loadingIcon {
	min-height: 20px;
	min-width: 200px;
	margin: 8px 0;
	text-align: center;
	font-size: 18px;
	color: #a2a2a2;
}
.popupFactory_closeIcon {
	font-size: 12px;
	color: #444;
	cursor: pointer;
	position: absolute;
	right: 12px;
	top: 14px;
	padding: 4px;
}
.popupFactory_closeIcon:hover {
	color: #737373;
}
.popupFactory_closeIcon:active {
	color: #858585;
}
.popupFactory_contentContainer,
.popupFactory_loadingContainer {
	display: none;
	padding: 16px 18px;
}

.conditionDisplayContainer td:not(:last-child) {
	word-break: normal;
}

.popupFactory_contentContainer, .conditionDisplayContainer td:last-child {
	word-break: break-word;
}

.popupFactory_popupContainer_static .popupFactory_contentContainer {
	display: block;
}

.popupFactory_contentContainer > p {
	margin: 0 0 16px;
	line-height: 24px;
}

.popupFactory_contentContainer .popupFactoryButtonsContainer {
	margin: 32px 0 0;
	text-align: right;
}

.popupFactory_contentContainer .popupFactoryButtonsContainer > * {
	display: inline-block;
	margin: 0 0 0 12px;
}

.popupFactory_contentContainer .tabWarningPopupToggle {
	background: #f5f5f5;
	line-height: 20px;
	border-radius: 3px;
	margin: 0 0 -8px;
}

.popupFactory_topArrow, .popupFactory_bottomArrow, .popupFactory_leftArrow, .popupFactory_rightArrow {
	position: absolute;
	width: 0;
	height: 0;
}
.popupFactory_topArrow {
	border-left: 8px solid transparent;
	border-right: 8px solid transparent;
	border-bottom: 8px solid #fff;
}
.popupFactory_bottomArrow {
	border-left: 8px solid transparent;
	border-right: 8px solid transparent;
	border-top: 8px solid #fff;
}
.popupFactory_leftArrow {
	border-right: 8px solid #fff;
	border-bottom: 8px solid transparent;
	border-top: 8px solid transparent;
}
.popupFactory_rightArrow {
	border-left: 8px solid #fff;
	border-bottom: 8px solid transparent;
	border-top: 8px solid transparent;
}

.popupFactory_headerContainer {
	border-bottom: 1px solid #dadada;
}
.popupFactory_titleContainer {
	padding: 18px 36px 16px 18px;
	font-size: 14px;
	font-weight: 600;
	text-align: left;
	color: #6d3075;
	display: inline-block;
}

.popupFactory_titleContainer.hasButtons {
	padding-top: 22px;
	padding-bottom: 20px;
}

.popupFactory_popupContainer,
.popupFactory_popupContainer_static {
	position: absolute;
	width: 200px;
	background: #fff;
	z-index: 99999;
	border-radius: 3px;
	box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
}

.popupFactory_popupContainer_static {
	width: auto;
}