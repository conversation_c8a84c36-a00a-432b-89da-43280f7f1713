var persistedValueIndicator 	= ".persistedValue";
var persistedClassIndicator 	= ".persistedClass";
var persistedScrollIndicator 	= ".persistedScroll";
var persistedCheckboxIndicator 	= ".persistedCheckbox";

$( function() {
	
	// Is local storage supported by client?
	if( typeof(Storage)!=="undefined" && ($(persistedValueIndicator) || $(persistedClassIndicator) || $(persistedCheckboxIndicator)) ) {
	
		// Init local storage persistence for target inputs
		$(persistedValueIndicator).each(function(){
            var $this = $(this),
                isSelect = $this.is('select'),
                key = "msgpt_persistedValue_" + $(this).attr('id'),
                value = localStorage.getItem(key);

            if (value) {

                var hasTheOption = false;

                if (isSelect)
                    hasTheOption = !!$(this).children().filter(function () {
                        return $(this).val() === value;
                    }).length;


                if (!isSelect || (isSelect && hasTheOption))
                    $(this).val(value);

            }
			
			$(this).change( function() {
				localStorage.setItem(key, $(this).val());
			});
		});
		// Init local storage persistence for target checkboxes
		$(persistedCheckboxIndicator).each(function(){
			if ( localStorage.getItem("msgpt_persistedCheckbox_"+$(this).attr('id')) != undefined ) {
				if ( localStorage.getItem("msgpt_persistedCheckbox_"+$(this).attr('id')) == "true" )
					$(this).attr('checked','checked');
				else
					$(this).removeAttr('checked');
			}
			
			$(this).click( function() {
				localStorage.setItem("msgpt_persistedCheckbox_"+$(this).attr('id'),$(this).is(':checked'));
			});
			$(this).change( function() {
				localStorage.setItem("msgpt_persistedCheckbox_"+$(this).attr('id'),$(this).is(':checked'));
			});
		});
		// Init local storage persistence (class) for target elements
		$(persistedClassIndicator).each(function(){
			if ( localStorage.getItem("msgpt_persistedClass_"+$(this).attr('id') ) )
				$(this).attr('class', localStorage.getItem("msgpt_persistedClass_"+$(this).attr('id')) );
		});
		// Init local storage persistence (scroll) for target elements
		$(persistedScrollIndicator).each(function(){
			if ( localStorage.getItem("msgpt_persistedScroll_"+$(this).attr('id') ) )
				$(this).scrollTop( localStorage.getItem("msgpt_persistedScroll_"+$(this).attr('id')) );
			$(this).scroll( function() { updatePersistedScroll(this); } );
		});

	}
		
});

function updatePersistedValue(ele) {
	if( typeof(Storage)!=="undefined" ) {
		localStorage.setItem("msgpt_persistedValue_"+$(ele).attr('id'),$(ele).val());
	}
}
function updatePersistedClass(ele) {
	if( typeof(Storage)!=="undefined" ) {
		localStorage.setItem("msgpt_persistedClass_"+$(ele).attr('id'),$(ele).attr('class'));
	}
}
function getPersistedClass(ele) {
	if( typeof(Storage)!=="undefined" ) {
		if ( $(ele).length != 0 )
			return localStorage.getItem("msgpt_persistedClass_"+$(ele).attr('id'));
		else 
			return localStorage.getItem("msgpt_persistedClass_"+ele);
	}
	return null;
}
function getPersistedValue(ele) {
	if( typeof(Storage)!=="undefined" ) {
		if ( $(ele).length != 0 )
			return localStorage.getItem("msgpt_persistedValue_"+$(ele).attr('id'));
		else 
			return localStorage.getItem("msgpt_persistedValue_"+ele);
	}
	return null;
}
function updatePersistedScroll(ele) {
	if( typeof(Storage)!=="undefined" ) {
		localStorage.setItem("msgpt_persistedScroll_"+$(ele).attr('id'),$(ele).scrollTop());
	}
}

function resetPersistedValue(id) {
	if( typeof(Storage)!=="undefined" && localStorage.getItem("msgpt_persistedValue_"+id) ) {
		localStorage.removeItem("msgpt_persistedValue_"+id);
	}
}
function resetPersistedValues(idArray) {
	for (var i=0; i<idArray.length; i++)
		resetPersistedValue(idArray[i]);
}