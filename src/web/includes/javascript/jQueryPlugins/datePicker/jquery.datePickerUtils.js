// datepicker Init
var idPrefix = 'dateDisplay_';
var defaultDateFormat = 'M dd, yy';
var datePickerToken = null;

var dayNamesMinArray = 	[client_messages.calendar.day_name_min.sunday, 
			              client_messages.calendar.day_name_min.monday, 
			              client_messages.calendar.day_name_min.tuesday, 
			              client_messages.calendar.day_name_min.wednesday, 
			              client_messages.calendar.day_name_min.thursday, 
			              client_messages.calendar.day_name_min.friday, 
			              client_messages.calendar.day_name_min.saturday];
var dayNamesArray =		[client_messages.calendar.day_name.sunday, 
			              client_messages.calendar.day_name.monday, 
			              client_messages.calendar.day_name.tuesday, 
			              client_messages.calendar.day_name.wednesday, 
			              client_messages.calendar.day_name.thursday, 
			              client_messages.calendar.day_name.friday, 
			              client_messages.calendar.day_name.saturday];
var monthNamesShortArray=[client_messages.calendar.month_name_short.january,
		                  client_messages.calendar.month_name_short.february,
		                  client_messages.calendar.month_name_short.march,
		                  client_messages.calendar.month_name_short.april,
		                  client_messages.calendar.month_name_short.may,
		                  client_messages.calendar.month_name_short.june,
		                  client_messages.calendar.month_name_short.july,
		                  client_messages.calendar.month_name_short.august,
		                  client_messages.calendar.month_name_short.september,
		                  client_messages.calendar.month_name_short.october,
		                  client_messages.calendar.month_name_short.november,
		                  client_messages.calendar.month_name_short.december
		                ];

$( function() {

	$(".monthPicker").each(function(){
		var boundDatepickerInputId = $(this).getBoundDatepickerInputId();		
		$(this).monthpicker({
			showOn			: "both",
			buttonImage		: "data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'%3E%3Cpath class='fill-info' d='M148 288h-40c-6.6 0-12-5.4-12-12v-40c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12zm108-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 96v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm192 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96-260v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V112c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48zm-48 346V160H48v298c0 3.3 2.7 6 6 6h340c3.3 0 6-2.7 6-6z'/%3E%3C/svg%3E",
            buttonImageOnly	: true,
	        dateFormat		: 'M yy',
	        changeMonth		: true,
	        changeYear		: true,	
			altFormat		: 'M yy',
			altField		: '#'+boundDatepickerInputId,
			showButtonPanel	: true,
			showAnim		: "",
			buttonText		: client_messages.text.select_date,
			prevText		: client_messages.calendar.prev,
			nextText		: client_messages.calendar.next,
			dayNamesMin		: dayNamesMinArray,
  			dayNames		: dayNamesArray,     
			monthNamesShort	: monthNamesShortArray,
			beforeShow		: function(input, inst) {	
				getTopFrame().datePickerToken = null;
			},
			onSelect		: function(dateText, inst) { 
				getTopFrame().datePickerToken = dateText; $(inst.input).change(); 
			}
		});

	});

	$(".monthPicker").datepickerCustom();
	
	
	$(".datePicker").each(function(){
		var boundDatepickerInputId = $(this).getBoundDatepickerInputId();		
		$(this).datepicker({
			showOn			: "both",
			buttonImage		: "data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' width='16'%3E%3Cpath class='fill-info' d='M148 288h-40c-6.6 0-12-5.4-12-12v-40c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12zm108-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 96v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm192 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96-260v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V112c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48zm-48 346V160H48v298c0 3.3 2.7 6 6 6h340c3.3 0 6-2.7 6-6z'/%3E%3C/svg%3E",
			buttonImageOnly	: true,
			dateFormat		: defaultDateFormat,		
			altFormat		: defaultDateFormat,
			altField		: '#'+boundDatepickerInputId,
			buttonText		: client_messages.text.select_date,
			prevText		: client_messages.calendar.prev,
			nextText		: client_messages.calendar.next,
			dayNamesMin		: dayNamesMinArray,
  			dayNames		: dayNamesArray,     
			monthNamesShort	: monthNamesShortArray,
			changeMonth		: true,
			changeYear		: true,
			beforeShow		: function(input, inst) {	
				getTopFrame().datePickerToken = null;
			},
			onSelect		: function(dateText, inst) { 
				getTopFrame().datePickerToken = dateText; $(inst.input).change(); 
			}
		});
	});
		
	$(".datePicker").datepickerCustom();

	$(".datePickerInline").each(function(){
		var boundDatepickerInputId = $(this).getBoundDatepickerInputId();
		var defaultDate = $('#'+boundDatepickerInputId).val();
		$(this).datepicker({
			dateFormat		: defaultDateFormat,
			altFormat		: defaultDateFormat,
			altField		: '#'+boundDatepickerInputId,
			defaultDate		: defaultDate,
			prevText		: client_messages.calendar.prev,
			nextText		: client_messages.calendar.next,
			dayNamesMin		: dayNamesMinArray,
  			dayNames		: dayNamesArray,     
			monthNamesShort	: monthNamesShortArray,
			changeMonth		: true,
			changeYear		: true,
			onSelect		: function(dateText, inst) {
				onDateSelect(dateText,inst);
			}
		});
	});

});

// Init date fields, disable user input
(function($){
	
	$.fn.getBoundDatepickerInputId = function() {
		if ($(this).get(0)) {
			return boundDatepickerInputId = $(this).attr('id').replace('dateDisplay_','');
		}
		return null;
	};

	$.fn.datepickerCustom = function() { 

		return this.each
			(
				function() 
				{
					if (this.id.indexOf(idPrefix) != -1) {
						// Disable datepicker interface
						if ( document.getElementById(this.id).disabled )
							$(this).datepicker("disable");
							
						var prefixLength = idPrefix.length;
						var rootId = this.id.substring(prefixLength,this.id.length);
						var initDate = document.getElementById(rootId).value;
						
						if ( validDate(initDate) ) {
							var saveFormat = document.getElementById(this.id).getAttribute('saveFormat');
							if (saveFormat != null && saveFormat != '') {
								if (initDate != '')
									document.getElementById(this.id).value = $.datepicker.formatDate(defaultDateFormat, new Date(initDate));				
							} else {
								document.getElementById(this.id).value = initDate;
							}
						} else {
							document.getElementById(rootId).value = '';
						}
					}
					// Block all user input events for date field
					$(this).keypress ( function (e) { e.preventDefault(); } ); // Block keyboard
					$(this).bind("paste", function () { return false; }); // Block IE menu paste
					$(this).keydown ( function(e) { preventActionOrClearField(e, this); } ); // Block keyboard
					if (this.addEventListener) { // Block input events
						this.addEventListener('input', function (e) {
							var prefixLength = idPrefix.length;
							var dateValue = document.getElementById(this.id.substring(prefixLength, this.id.length)).value;
							var saveFormat = document.getElementById(this.id).getAttribute('saveFormat');
							if (saveFormat != null && saveFormat != '')
								dateValue = $.datepicker.formatDate(defaultDateFormat, new Date(dateValue));
							this.value = dateValue;
						}, false);
					}
				}
			);

	};
	
	$.fn.calendarTipAction = function() {
		// Pulse filter information icon
		var element = this;
		this.parent().find(".infoIcon").each(
			function() {
				if (  $(element).attr("id").indexOf( $(this).attr("filterid") ) != -1 ) {
					$(this).effect("pulsate", {times: 1}, 450);
					if ($(this).attr("clueTipEnabled") != "true") {
						$(this).cluetip({
							cluetipClass: 'jtip',
							topOffset: 20,
							splitTitle: '|',
							hoverIntent: {    
								sensitivity:  10,
								interval:     50,
								timeout:      0
							},
								fx: {
								open:       'fadeIn',
								openSpeed:  '300'
							}
						});
						// Mark clueTip as applied for this icon
						$(this).attr("clueTipEnabled", "true");
					}
				}
			}
		);
	};
	
	// Hide filter tip associated with element
	$.fn.hideCalendarTip = function() {
		$(this).parent().find(".infoIcon").each(
			function() {
				if ( $(this).attr("filterid") == $(this).attr("filterid") )
					$(this).hide();
			}
		);
	};
})(jQuery);

function preventActionOrClearField(e, inputField) {
	// Clear input field on space, backspace and delete
	if (!e.charCode)
		var isAcceptedChar = (e.which == '32' || e.which == '8' || e.which == '46');
	else
		var isAcceptedChar = ((e.charCode == '32' && e.keyCode == '0') || e.keyCode == '8' || e.keyCode == '46');

	if (isAcceptedChar) {
		inputField.value = '';
		if (inputField.id.indexOf(idPrefix) != -1) {
			var prefixLength = idPrefix.length;
			var rootId = inputField.id.substring(prefixLength,inputField.id.length);
			$('#'+rootId).val('');
			$(inputField).change();
		}
		$(inputField).hideCalendarTip();
	} else {
		$(inputField).calendarTipAction();
	}
	e.preventDefault();
}

function validDate(date) {
	for (var i=0; i<date.length; i++) {
		if ( !(isCharAlphaNum(date.charCodeAt(i)) || date.charAt(i) == ' ' || date.charAt(i) == ',' || date.charAt(i) == '/') )
			return false;
	}
	return true;
}

function isCharAlphaNum( char ) {
	// 192 - 255: Extended lang chars
	if ((char < 48) || (char > 255) || 
	   ((char > 57) && (char < 65)) || 
	   ((char > 90) && (char < 97)) ||
	   ((char > 122) && (char < 192)) ) {
		return false;
	} else {
		return true;
	}
}

function onDateSelect() {
	return;	
}