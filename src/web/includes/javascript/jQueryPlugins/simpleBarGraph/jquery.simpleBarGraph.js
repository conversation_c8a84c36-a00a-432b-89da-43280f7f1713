jQuery(document).ready(function($){
	$('.simpleBarGraph').simpleBarGraph();	
});

(function($){
	$.fn.simpleBarGraph = function() {
		return this.each (
			function() {
				var fillPercent = Math.min( Math.round($(this).attr('fillPercentage')), 100 );
				if (fillPercent > 0) {
					var barFill = "<span style='padding-left: 1px; width: 1px;' class='barGraphFill'/>";
					var barShadow = "<span class='barGraphShadow'/>";
					$(this).html(barFill+barShadow);
					if ($.browser.msie)
						$(this).find('.barGraphFill').animate({width: fillPercent+"px"}, 750);
					else
						$(this).find('.barGraphFill').animate({paddingLeft: fillPercent+"px"}, 750);
				} else {
					$(this).html("<span>&nbsp;</span>");
				}
			}
		);
	};
})(jQuery);