jQuery(document).ready(function($){
	initInputFilters();
});

function initInputFilters() {
	jQuery("input.address").alphanumeric({address:true});
	jQuery("input.className").alphanumeric({className:true});
	jQuery("input.code").alphanumeric({code:true});
	jQuery("textarea.code").alphanumeric({code:true});
	jQuery("input.comment").alphanumeric({comment:true});
	jQuery("textarea.comment").alphanumeric({comment:true});
	jQuery("input.connectorName").alphanumeric({connectorName:true});
	jQuery("input.content").alphanumeric({content:true});
	jQuery("input.dataElementVariable").alphanumeric({dataElementVariable:true});
	jQuery("textarea.dataElementVariable").alphanumeric({dataElementVariable:true});
	jQuery("input.decimal").alphanumeric({decimal:true});
	jQuery("input.negativeDecimal").alphanumeric({negativeDecimal:true});
	jQuery("input.defaultFilter").alphanumeric({defaultFilter:true});
	jQuery("textarea.defaultFilter").alphanumeric({defaultFilter:true});
	jQuery("input.delimiter").alphanumeric({delimiter:true});
	jQuery("input.demographicName").alphanumeric({demographicName:true});
	jQuery("input.description").alphanumeric({description:true});
	jQuery("textarea.description").alphanumeric({description:true});
	jQuery("input.dictionaryName").alphanumeric({dictionaryName:true});
	jQuery("input.domainName").alphanumeric({domainName:true});
	jQuery("input.email").alphanumeric({email:true});
	jQuery("input.filename").alphanumeric({filename:true});
	jQuery("input.filterField").alphanumeric({filterField:true});
	jQuery("input.imageName").alphanumeric({imageName:true});
	jQuery("input.indicator").alphanumeric({indicator:true});
	jQuery("input.indicatorWithSpace").alphanumeric({indicatorWithSpace:true});
	jQuery("input.integer").alphanumeric({integer:true});
	jQuery("input.namespace").alphanumeric({namespace:true});
	jQuery("input.note").alphanumeric({note:true});
	jQuery("textarea.note").alphanumeric({note:true});
	jQuery("input.numeric").alphanumeric({numeric:true});
	jQuery("input.password").alphanumeric({password:true});
	jQuery("input.phoneNumber").alphanumeric({phoneNumber:true});
	jQuery("input.postalCode").alphanumeric({postalCode:true});
	jQuery("input.separator").alphanumeric({separator:true});
	jQuery("input.simpleName").alphanumeric({simpleName:true});
	jQuery("input.testName").alphanumeric({testName:true});
	jQuery("input.rationalizerSharedContentName").alphanumeric({rationalizerSharedContentName:true});
	jQuery("input.zoneConnectorValue").alphanumeric({zoneConnectorValue:true});
	jQuery("input.extendedName").alphanumeric({extendedName:true});
	jQuery("textarea.simpleName").alphanumeric({simpleName:true});
	jQuery("textarea.rationalizerSharedContentName").alphanumeric({rationalizerSharedContentName:true});
	jQuery("textarea.zoneConnectorValue").alphanumeric({zoneConnectorValue:true});
	jQuery("textarea.extendedName").alphanumeric({extendedName:true});
	jQuery("input.simpleText").alphanumeric({simpleText:true});				
	jQuery("input.simpleTextNoApos").alphanumeric({simpleTextNoApos:true});	
	jQuery("input.suite").alphanumeric({suite:true});	
	jQuery("input.targetingValue").alphanumeric({targetingValue:true});	
	jQuery("input.username").alphanumeric({username:true});
	jQuery("input.variableDefault").alphanumeric({variableDefault:true});
	jQuery("input.version").alphanumeric({version:true});
	jQuery("input.webSite").alphanumeric({webSite:true});	
	jQuery("input.xmlName").alphanumeric({xmlName:true});
	jQuery("input.keyName").alphanumeric({keyName:true});
	jQuery("input.alphaNum").alphanumeric();
}

/** alphanumeric r1 // 2010.08.16 // jQuery 1.3 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.3 or later
 * 
 */

(function($) {

	$.alphanumeric = {
		ref	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return alphanumeric_component.inst[o.attr('id')] || null; 
		},
		defaults : {
			extended_chars : ""
		},
		generateFilterTooltip : function (id) {
			return 	"<span filterid=\""+id+"\" class=\"filterTip\" style=\"display: none;\" " +
					"title=\"<div align='left' class='clueTipTitle'>" + client_messages.title.input_filter + "</div>|<div align='left' class='clueTipContent'>" + client_messages.text.permitted_characters + ": <b>CHARS</b></div>\" >" +
					"<i class=\"fa fa-exclamation-circle\" aria-hidden=\"true\"></i></span>";
		}
	};
	
	$.fn.alphanumeric = function (opts) {
		return this.each(function() {
			var conf = $.extend({
					inputEle: this,
					parentContainer: $(this).parent()
				},opts);
			if(conf !== false) new alphanumeric_component().init(this, conf);
		});
	};
	
	function alphanumeric_component () {
		return {
			
		settings : $.extend({},$.alphanumeric.defaults),	

		init : function(elem, conf) {

			var _this = this;
			alphanumeric_component.inst[$(elem).attr("id")] = this;
			$.extend(_this, conf);
			if (  conf.extended_chars )
				this.settings.extended_chars = conf.extended_chars;
			
			var alphaNumAllow = ""; 
			var numbers = "1234567890";
			var alpha = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
			
			// allowed all characters with code >= alphaExtendedStartCode 
			var alphaExtendedStartCode = 65536;
			
			_this.allow = "";
			
			if (_this.numeric) alphaNumAllow += numbers;
			if (_this.integer) alphaNumAllow += numbers+"-";
			if (_this.decimal) alphaNumAllow += numbers+".";
			if (_this.negativeDecimal) alphaNumAllow += numbers+".-";
			if (_this.alpha) { alphaNumAllow += alpha; alphaExtendedStartCode = 128; }
			if (_this.alphaNum) { alphaNumAllow += alpha; alphaNumAllow += numbers; }
			if (!_this.numeric && !_this.integer && !_this.alpha && !_this.decimal && !_this.negativeDecimal && !_this.delimiter) { alphaNumAllow += numbers + alpha; alphaExtendedStartCode = 128; }
			
			if (_this.nocaps) alphaNumAllow = alphaNumAllow.replace("ABCDEFGHIJKLMNOPQRSTUVWXYZ",'');
			if (_this.allcaps) alphaNumAllow = alphaNumAllow.replace("abcdefghijklmnopqrstuvwxyz",'');
			
			// Defined character allowances
			if (_this.address) { _this.allow 			 = " _-'.,:@#[]"; }
			if (_this.imageName) { _this.allow 			 = " _-'!.$+=,:;@[]"; alphaExtendedStartCode = 65536; }
			if (_this.className) { _this.allow 			 = "_-"; }
			if (_this.code) { _this.allow 				 = "_-"; }
			if (_this.comment) { _this.allow 			 = " _-'*!.$+=?,:;@#[]\\/"; }
			if (_this.connectorName) { _this.allow 		 = "_-.|"; }
			if (_this.content) { _this.allow 			 = " _-'*!.$+=?,:;@#[]\\/"; }
			if (_this.dataElementVariable) { _this.allow = " _-'*!.$+=?,:;@#[]\\/"; }

			if (_this.defaultFilter) { _this.allow 		= ""; }
			if (_this.delimiter) { _this.allow 			= " _-:;,|#"; }
			if (_this.demographicName) { _this.allow 	= "_-'. "; }
			if (_this.description) { _this.allow 		= " _-'*!.$+=?,:;@#[]\\/"; }
			if (_this.dictionaryName) { _this.allow 	= "_-'() "; }
			if (_this.domainName) { _this.allow 		= "-"; }
			if (_this.email) { _this.allow 				= "!#$*+=?_-@./{}^~'"; alphaExtendedStartCode = 65536; }
			if (_this.filename) { _this.allow 			= " ()_-.:\\/"; }
			if (_this.filterField) { _this.allow		= " _-'*!.$+=?,:;@[]\\/"; }
			if (_this.indicator) { _this.allow 			= "_-."; }
			if (_this.indicatorWithSpace) { _this.allow = "_-. "; }
			if (_this.namespace) { _this.allow 			= "_-'. "; }
			if (_this.note) { _this.allow 				= " _-'*!.$+=?,:;@#[]\\/"; }
			if (_this.password) { _this.allow 			= " _-'*!.$+=?,:;@#/\\[](){}%&~"; alphaExtendedStartCode = 65536; }
			if (_this.phoneNumber) { _this.allow 		= "()- "; alphaExtendedStartCode = 65536; }
			if (_this.postalCode) { _this.allow 		= "- "; alphaExtendedStartCode = 65536; }
			if (_this.separator) { _this.allow 			= "_,:;-."; alphaExtendedStartCode = 65536; }
			if (_this.simpleName) { _this.allow 		= "_-' "; }
			if (_this.testName) { _this.allow = "_-' "; alphaExtendedStartCode = 65536; }
			if (_this.rationalizerSharedContentName) { _this.allow = "_-' "; alphaExtendedStartCode = 65536; }
			if (_this.zoneConnectorValue) { _this.allow = "_-' ";}
			if (_this.extendedName) { _this.allow 		= "?_-' "; }
			if (_this.simpleText) { _this.allow 		= "_-' "; }
			if (_this.simpleTextNoApos) { _this.allow 	= "_- "; }
			if (_this.suite) { _this.allow 				= "_-# "; }
			if (_this.targetingValue) { _this.allow 	= " _-'*!.$+=?,:;@#[]<>()%\\/{}~^&|`\""; }
			if (_this.username) { _this.allow 			= "_-.@'#"; alphaExtendedStartCode = 65536; }
			if (_this.variableDefault) { _this.allow 	= "./"; }
			if (_this.version) { _this.allow 			= "."; alphaExtendedStartCode = 65536; }
			if (_this.webSite) { _this.allow 			= "?=:/_-.% "; alphaExtendedStartCode = 65536; }
			if (_this.xmlName) { _this.allow 			= "_-.:"; }
			if (_this.keyName) { _this.allow 			= "_-'*!.$+=?,:;@#[]\\/"; }

			_this.ch = alphaNumAllow + _this.allow;
			_this.currentValue = $(_this.inputEle).val();
			_this.alphaExtendedStartCode = alphaExtendedStartCode;
			
			if ( $(_this.inputEle).attr('adp_placeholder') && $(_this.inputEle).attr('adp_placeholder').length != "" ) {
				$(_this.inputEle).attr('required','true');
				$(_this.inputEle).after("<label placeholder=\""+$(_this.inputEle).attr('adp_placeholder')+"\" alt=\""+$(_this.inputEle).attr('adp_placeholder')+"\"></label>");
			}
			
			// Validate key press
			$(_this.inputEle).keypress
				(
					function (e)
						{
							// No action on 'Enter' key
							if (e.keyCode == 13)
								return;
							if (!e.charCode) k = String.fromCharCode(e.which);
								else k = String.fromCharCode(e.charCode);
							// Allow Back, Del, Tab, etc.
							if (e.charCode == '0') {
								return;	
							}
							// Allow ctrl for paste
							if (e.ctrlKey || e.metaKey) {
								return;
							}

							// Check for allowed char
							if ( (_this.ch + _this.settings.extended_chars).indexOf(k) == -1 && k.charCodeAt(0) < _this.alphaExtendedStartCode) {
								e.preventDefault();
								_this.filterTipAction();
								return;
							}
							_this.hideFilterTip();
						}
				);
			
			// IE: On menu paste action remove invalid chars from clipboard
			$(_this.inputEle).bind("paste", function () {
				var pasteStatus = _this.clipboardFilter();
				if (pasteStatus == "BLOCK") {
					_this.currentValue = $(_this.inputEle).val();
					setTimeout(function(){_this.cleanPasteValue();},100);
				}
			});
			
			// IE: On paste (crtl+v) remove invalid chars from clipboard
			$(_this.inputEle).keydown
				(
					function(e)
					{
						if (e.ctrlKey || e.metaKey) {
							if (e.keyCode == '86') {
								var filterStatus = _this.clipboardFilter();
								if (filterStatus == 'BLOCK') {
									_this.currentValue = $(_this.inputEle).val();
									setTimeout(function(){_this.cleanPasteValue();},100);
								}
							}
						} else {
							// Preventing holding down of keys to write accents or other characters
							_this.currentValue = $(_this.inputEle).val();
							setTimeout(function(){_this.cleanPasteValue();},100);
						}
					}
				);
				
			if (_this.inputEle.addEventListener) {
				_this.inputEle.addEventListener('input', function (e) { 
					_this.cleanValue();
				}, false);
			};
			
		},
		
		clipboardFilter : function() {
			var _this = this;
			var cbTxt = '';
			if(window.clipboardData) {
				cbTxt = window.clipboardData.getData('Text');
													
				cleanedTxt = _this.getFilteredText(cbTxt);
				if (cbTxt != cleanedTxt)
					_this.filterTipAction();
														
				if (cleanedTxt == '') {
					return "BLOCK";
				} else {
					window.clipboardData.setData('Text' , cleanedTxt );
				}
			} else {
				return "BLOCK";
			}
		},
		
		cleanPasteValue : function() {
			var _this = this;
			var pasteValue = $(_this.inputEle).val();
			var cleanValue = _this.getFilteredText(pasteValue);
			if ( pasteValue != cleanValue ) {
				$(_this.inputEle).val( cleanValue );
				_this.currentValue = cleanValue;
				_this.filterTipAction();
			}
		},
		
		cleanValue : function() {
			var _this = this;
			if ($(_this.inputEle).val().length > _this.currentValue.length+1) {
				var searchStr = _this.inputEle.value; var compareStr = _this.currentValue;
				for (var i=0; searchStr.charAt(i) == compareStr.charAt(i); i++) {  }
				for (var j=0; searchStr.charAt(searchStr.length-j) == compareStr.charAt(compareStr.length-j); j++) {  }
				var pastedText = searchStr.substring(i,searchStr.length-j+1);
				var cleanedTxt = _this.getFilteredText(pastedText);
				if (pastedText != cleanedTxt) {
					_this.filterTipAction();
					_this.inputEle.value=_this.currentValue.substring(0,i) + cleanedTxt + _this.currentValue.substring(i,_this.currentValue.length);
				}
				_this.currentValue = _this.inputEle.value;
			}
			_this.currentValue = _this.inputEle.value;
		},
		
		filterTipAction : function () {
			// Pulse filter information icon
			var _this = this;
			var element = _this.inputEle;
			var filterId = $(element).attr('filterid');
			
			$(".filterTip[filterid=" + filterId + "]").each(
				function() {
					if ( $(this).attr("filterid") == $(element).attr("filterid") ) {
						$(this).effect("pulsate", {times: 1}, 450);
						if ($(this).attr("clueTipEnabled") != "true") {
							var permittedChars = _this.allow ;
							var charSplitArray = permittedChars.split('');
							// Explicitly label Space character
							for (var i=0; i<charSplitArray.length; i++) {
								if (charSplitArray[i] == ' ')
									charSplitArray[i] = 'Space';
							}
							if (_this.numeric)
								permittedChars = client_messages.text.numeric + " &nbsp;"+charSplitArray.join(' &nbsp;');
							else if (_this.decimal || _this.negativeDecimal)
								permittedChars = client_messages.text.decimal + " &nbsp;"+charSplitArray.join(' &nbsp;');
							else if (_this.integer)
								permittedChars = client_messages.text.integer + " &nbsp;"+charSplitArray.join(' &nbsp;');
							else if (_this.content)
								permittedChars = client_messages.text.alphanumeric + " (" + client_messages.text.multi_language + ")&nbsp;&nbsp;"+charSplitArray.join(' &nbsp;');
							else if (_this.delimiter)
								permittedChars = charSplitArray.join(' &nbsp;');
							else 
								permittedChars = client_messages.text.alphanumeric + " &nbsp;"+charSplitArray.join(' &nbsp;');
							// Define clueTip
							var newTitle = $(this).attr("title").replace("CHARS",permittedChars);
							$(this).attr("title", newTitle);
							$(this).cluetip({
								cluetipClass: 'jtip',
								topOffset: 20,
								cluezIndex: 100000,
								splitTitle: '|',
								hoverIntent: {    
									sensitivity:  10,
									interval:     50,
									timeout:      0
								},
									fx: {
									open:       'fadeIn',
									openSpeed:  '300'
								}
							});
							// Mark clueTip as applied for this icon
							$(this).attr("clueTipEnabled", "true");
						}
					}
				}
			);
		},
		
		getFilteredText : function (inputTxt) {
			var _this = this;
			cbCharArray = inputTxt.split('');
			for (var i=0; i<cbCharArray.length; i++)
			{
				if ( (_this.ch + _this.settings.extended_chars).indexOf(cbCharArray[i]) == -1 && cbCharArray[i].charCodeAt(0) < _this.alphaExtendedStartCode)
				{
					cbCharArray[i] = '';
				}
			}
			return cbCharArray.join('');
		},
		
		// Hide filter tip associated with element
		hideFilterTip : function() {
			var _this = this;
			$(_this.inputEle).parent().find(".filterTip").each(
				function() {
					if ( $(this).attr("filterid") == $(this).attr("filterid") )
						$(this).hide();
				}
			);
		}

		}; // END COMPONENT
	};
	
	// instance manager
	alphanumeric_component.inst = {};

})(jQuery);