(function ( $ ) {

    function generatePanel() {

        var menuHTML = "";

        menuHTML += "<div id=\"mce-custompanel-tags\"class=\"mce-container mce-panel mce-custompanel mce-menu\" role=\"application\" style=\"position: absolute; border-width: 1px; max-height: none; min-width: 400px; padding-top: 0px; padding-bottom: 0px; z-index: 9999;\">" +
            "<div class=\"mcePanelNameContainer mce-window-head\">" +
            "<div class=\"mce-title\">" +
            client_messages.content_editor.tag_panel +
            "</div>" +
            "<button type=\"button\" class=\"mce-close\" aria-hidden=\"true\"><i class=\"mce-ico mce-i-remove\"></i></button>" +
            "</div>" +
            "<div class=\"mceMenuSearchContainer\">" +
            "<div style=\"display: inline-block; position: relative; width: 100%;\">" +
            "<select class=\"mcePanelFilterSelect\" style=\"width: 388px; font-size: 16px; height: 38px;\">" +
            "<option value=\"all\">All</option>" +
            "<option value=\"table\">" + client_messages.content_editor.tags.table + "</option>" +
            "<option value=\"row\">" + client_messages.content_editor.tags.row + "</option>" +
            "<option value=\"cell\">" + client_messages.content_editor.tags.cell + "</option>" +
            "<option value=\"paragraph\">" + client_messages.content_editor.tags.paragraph + "</option>" +
            "<option value=\"smart_text\">" + client_messages.content_editor.tags.smart_text + "</option>" +
            "<option value=\"local_smart_text\">" + client_messages.content_editor.tags.local_smart_text + "</option>" +
            "<option value=\"text_field\">" + client_messages.content_editor.tags.text_field + "</option>" +
            "<option value=\"menu\">" + client_messages.content_editor.tags.menu + "</option>" +
            "<option value=\"checkbox\">" + client_messages.content_editor.tags.checkbox + "</option>" +
            "<option value=\"radio\">" + client_messages.content_editor.tags.radio + "</option>" +
            "<option value=\"submit_button\">" + client_messages.content_editor.tags.submit_button + "</option>" +
            "<option value=\"freeform_container\">" + client_messages.content_editor.tags.freeform_container + "</option>" +
            "<option value=\"insert_point\">" + client_messages.content_editor.tags.insert_point + "</option>" +
            "<option value=\"edit_point\">" + client_messages.content_editor.tags.edit_point + "</option>" +
            "</select>" +
            "</div>" +
            "</div>" +
            "<div class=\"mceNoPanelFilterItems\" style=\"display: none; font-size: 12px; color: #555; padding: 8px 8px 0px 8px; font-style: italic;\">" +
            client_messages.content_editor.no_matching_entries +
            "</div>" +
            "<div class=\"mce-container-body mce-stack-layout\" role=\"menu\" style=\"max-height: 500px; padding-top: 5px; padding-bottom: 120px; overflow-y: auto; overflow-x: auto;\">" +
            "<div class=\"mce-loading-container\" style=\"text-align: center; margin: 8px 0px; min-height: 25px;\"><i class=\"fas fa-spinner fa-spin\" style=\"color: #999; display: inline-block; font-size: 24px; position: absolute; font-family: 'Font Awesome 6 Pro';\"></i></div>" +
            "<div id=\"tagTree\" style=\"display: none;\"></div>" +
            "</div>" +
            "</div>";

        menuHTML = $(menuHTML);


        $(menuHTML).find('[data-toggle="tooltip"]').tooltip({container: $(getTopFrame().document).find('body')});
        $(menuHTML).find('[data-toggle="tooltip"]').on('shown.bs.tooltip', function(o) {
            $(getTopFrame().document).find('#'+$(this).attr('aria-describedby')).css({'z-index':(parseInt($(menuHTML).css('z-index'))+1)});
        });

        $(menuHTML).find('.mce-close').click( function() {
            $(tagpanel.treePanel).hide();
            storeOnLoadState(false, 0, 0);
        });

        $(menuHTML).find('.mcePanelNameContainer').disableSelection();

        $(menuHTML).find('.mcePanelFilterSelect').change( function() {
            toggleFilter();
        });

        return menuHTML;

    };

    var tagpanel = {
        treePanel 				: null,
        indexedTagMap 			: null,
        tagTreeJSON 			: null,
        selectedTreeNode		: null,
        tagCount				: null,
        currentFilter			: null,
        containerEditor         : null
    }

    function togglePanel(persistedState) {

        if ( tagpanel.treePanel != null ) {

            if ( $(tagpanel.treePanel).is(':visible') ) {
                // Highlight on parent menu item hover
                $(tagpanel.treePanel).animate({
                    'border-color': 'purple'
                }, 250, function() {
                    $(tagpanel.treePanel).animate({
                        'border-color': '#e2e4e7'
                    }, 250);
                });
            } else {
                $(tagpanel.treePanel).show();
            }

        } else {

            tagpanel.treePanel = $(generatePanel());

            generateTagTree(false);

            $(getTopFrame().document).find('#mce-custompanel-tags').remove();
            $(getTopFrame().document).find('body').append(tagpanel.treePanel);

            getTopFrame().initDraggable($(tagpanel.treePanel),{
                iframeFix : true,
                handle : '.mcePanelNameContainer',
                containment: 'document',
                stop: function(event, ui) {

                    if ( typeof(Storage)!=="undefined" ) {
                        var htmlTag = $(getTopFrame().document).find('html');
                        var sT = $(htmlTag).scrollTop();
                        storeOnLoadState(true, $(tagpanel.treePanel).offset().left, $(tagpanel.treePanel).offset().top - sT);
                    }

                }
            });

        }

        if ( persistedState && persistedState.state == true ) {
            var htmlTag = $(getTopFrame().document).find('html');
            var sT = $(htmlTag).scrollTop();

            var panel = $(tagpanel.treePanel);
            var topFrameDocument = $(getTopFrame().document);
            var panelWidth = panel.width();
            var panelHeight = panel.width();

            if (persistedState.x < 0) {
                persistedState.x = 0;
            } else if (persistedState.x > topFrameDocument.width() - panelWidth ) {
                persistedState.x = topFrameDocument.width() - panelWidth;
            }

            if (persistedState.y < 0) {
                persistedState.y = 0;
            } else if (persistedState.y > topFrameDocument.height() - panelHeight) {
                persistedState.y = topFrameDocument.height() - panelHeight;
            }

            panel.css({'top': (persistedState.y + sT) + 'px', 'left': persistedState.x + 'px'});

            storeOnLoadState(true, persistedState.x, persistedState.y);
        } else {
            positionPanel();
        }

    }

    function generateTagTree(isRefresh) {

        function indexChildren(parent, treeParentIndex, children) {

            $(children).each( function() {

                var tagLabel = null;
                var tagType = null;

                if ($(this).is('table,tr,td,ul,ol,li,p,.mceInline,.staticContentItem[type],.mceDraggable,[data-edit-mode]') ) {
                    if ( !($(this).parent().length != 0 && $(this).parent().closest('.staticContentItem').length != 0) ) {

                        if ( $(this).is('table') ) {
                            tagLabel = client_messages.content_editor.tags.table;
                            tagType = 'table';
                        } else if ( $(this).is('tr') ) {
                            tagLabel = client_messages.content_editor.tags.row;
                            tagType = 'row';
                        } else if ( $(this).is('td') ) {
                            tagLabel = client_messages.content_editor.tags.cell;
                            tagType = 'cell';
                        } else if ( $(this).is('p') ) {
                            tagLabel = client_messages.content_editor.tags.paragraph;
                            tagType = 'paragraph';
                        } else if ( $(this).is('ol') ) {
                            tagLabel = client_messages.content_editor.tags.ordered_list;
                            tagType = 'ordered_list';
                        } else if ( $(this).is('ul') ) {
                            tagLabel = client_messages.content_editor.tags.bulleted_list;
                            tagType = 'bulleted_list';
                        } else if ( $(this).is('li') ) {
                            tagLabel = client_messages.content_editor.tags.list_item;
                            tagType = 'list_item';
                        } else if ( $(this).is('.staticContentItem[type=4]') ) {
                            tagLabel = client_messages.content_editor.tags.smart_text;
                            tagType = 'smart_text';
                        } else if ( $(this).is('.staticContentItem[type=10]') ) {
                            tagLabel = client_messages.content_editor.tags.local_smart_text;
                            tagType = 'local_smart_text';
                        } else if ( $(this).is('.mceTextFieldElement') ) {
                            tagLabel = client_messages.content_editor.tags.text_field;
                            tagType = 'text_field';
                        } else if ( $(this).is('.mceCheckboxElement') ) {
                            tagLabel = client_messages.content_editor.tags.checkbox;
                            tagType = 'checkbox';
                        } else if ( $(this).is('.mceRadioElement') ) {
                            tagLabel = client_messages.content_editor.tags.radio;
                            tagType = 'radio';
                        } else if ( $(this).is('.mceMenuElement') ) {
                            tagLabel = client_messages.content_editor.tags.menu;
                            tagType = 'menu';
                        } else if ( $(this).is('.mceSubmitButtonElement') ) {
                            tagLabel = client_messages.content_editor.tags.submit_button;
                            tagType = 'submit_button';
                        } else if ( $(this).is('.mceDraggable') ) {
                            tagLabel = client_messages.content_editor.tags.freeform_container;
                            tagType = 'freeform_container';
                        } else if ( $(this).is('div[data-edit-mode=block_insert_point]') ) {
                            tagLabel = client_messages.content_editor.tags.insert_point;
                            tagType = 'insert_point';
                        } else if ( $(this).is('[data-edit-mode=inline_insert_point]') ) {
                            tagLabel = client_messages.content_editor.tags.edit_point;
                            tagType = 'edit_point';
                        }

                    }
                }

                if ( tagLabel != null && $(this).is(':visible') ) {

                    tagpanel.indexedTagMap[index] = $(this);

                    var nodeJSON = {
                        id 		: "tag_" + index,
                        parent 	: treeParentIndex != null ? "tag_" + treeParentIndex : "#",
                        text 	: tagLabel,
                        state	: {
                            opened: true
                        },
                        li_attr	: {
                            tag : tagType
                        }
                    }
                    tagpanel.tagTreeJSON[tagpanel.tagTreeJSON.length] = nodeJSON;

                    index++;
                }

                indexChildren(this, $(this).is('table,tr,td,ul,ol,li,p,.mceDraggable,[data-edit-mode]') ? index - 1 : treeParentIndex, $(this).children());

            });

        }

        var index = 0;

        tagpanel.indexedTagMap = new Array();
        tagpanel.tagTreeJSON = [];
        var index = 0;
        tagpanel.tagCount = tagpanel.containerEditor.find('*').length;

        indexChildren( null, null,  tagpanel.containerEditor.children() );

        if ( isRefresh ) {

            $(tagpanel.treePanel).find('#tagTree').jstree(true).settings.core.data = tagpanel.tagTreeJSON;
            $(tagpanel.treePanel).find('#tagTree').jstree(true).refresh();

        } else {
            getTopFrame().initTree(function (o) {

                $(tagpanel.treePanel).find('#tagTree')
                    .on('hover_node.jstree', function (e, data) {

                        var nodeIndex = parseId(data.node.id);
                        var node = tagpanel.indexedTagMap[nodeIndex];

                        $(node).addClass('tagPanelHighlight');
                    })
                    .on('dehover_node.jstree', function (e, data) {

                        var nodeIndex = parseId(data.node.id);
                        var node = tagpanel.indexedTagMap[nodeIndex];

                        $(node).removeClass('tagPanelHighlight');

                    })
                    .on('select_node.jstree', function (e, data) {
                        var nodeIndex = parseId(data.node.id);
                        var node = tagpanel.indexedTagMap[nodeIndex];

                        tagpanel.selectedTreeNode = $(tagpanel.treePanel).find('#' + data.node.id);

                        toggleActionsMenu(tagpanel.selectedTreeNode, node)
                    })
                    .on('deselect_node.jstree', function (e, data) {
                        tagpanel.selectedTreeNode = null;
                    })
                    .on('refresh.jstree', function (e, data) {

                        if (tagpanel.currentFilter != null && tagpanel.currentFilter != $(tagpanel.treePanel).find('.mcePanelFilterSelect').val()) {
                            $(tagpanel.treePanel).find('.mcePanelFilterSelect').val(tagpanel.currentFilter)
                            $(tagpanel.treePanel).find('#tagTree').jstree(true).search(tagpanel.currentFilter);
                        }


                    })
                    .jstree({
                        'plugins': ['wholerow', 'search'],
                        'core': {
                            'themes': {
                                'name': 'proton',
                                'responsive': false,
                                'icons': false
                            },
                            'data': tagpanel.tagTreeJSON
                        },
                        'search': {
                            'show_only_matches': true,
                            'show_at_node': false
                        }
                    })
                    .show();

            }, tagpanel.containerEditor);

            $(tagpanel.treePanel).find('.mce-loading-container').hide();
            $(tagpanel.treePanel).find('#tagTree')
                .on('search.jstree', function(e, data) {
                    if (data.res.length == 0 && data.str.toLowerCase() != "all") {
                        $(tagpanel.treePanel).find('#tagTree').css("visibility", "hidden");
                        $(tagpanel.treePanel).find('.mceNoPanelFilterItems').show();
                    } else {
                        $(tagpanel.treePanel).find('#tagTree').css("visibility", "visible");
                        (tagpanel.treePanel).find('.mceNoPanelFilterItems').hide();
                    }
                });
        }

    }

    function initTagPanel() {
        $(getTopFrame().document).find('head').append("<script id=\"jsTreeJavascript\" src=\"" + context + "/includes/javascript/jstree/jstree.js\" >");
        if ( $(getTopFrame().document).find('#jsTreeCSS').length == 0 )
            $(getTopFrame().document).find('head').append("<link id=\"jsTreeCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + context + "/includes/javascript/jQueryPlugins/jstree/themes/proton/style.css\" >");
        if ( $(getTopFrame().document).find('#jsTreePrinovaCSS').length == 0 )
            $(getTopFrame().document).find('head').append("<link id=\"jsTreePrinovaCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + context + "/includes/javascript/jQueryPlugins/jstree/themes/proton/style.prinova.css\" >");
        if ( $(getTopFrame().document).find('#tinymceTagPanelCSS').length == 0 )
            $(getTopFrame().document).find('head').append(
                "<style id=\"tinymceTagPanelCSS\">" +
                ".mce-container ul {" +
                "	margin-left: 0px;" +
                "}\n" +
                ".mce-container .mcePanelAction {" +
                "	cursor: pointer;" +
                "	padding: 2px;" +
                "}\n" +
                ".mce-container .mcePanelAction:hover {" +
                "	background-color: #f5f5f5;" +
                "}\n" +
                ".mce-container .mcePanelAction:active {" +
                "	background-color: #eee;" +
                "}\n" +
                ".mce-container a.jstree-hovered {" +
                "	text-decoration: none;" +
                "}\n" +
                ".jstree-clicked:not(.jstree-hovered) {" +
                "color: #fff;" +
                "}\n" +
                ".jstree-proton .jstree-search:not(.jstree-clicked) {" +
                "color: #6d3075;" +
                "}\n" +
                ".jstree-proton .jstree-search {" +
                "font-style: normal;" +
                "}\n" +
                "</style>");

        $('#contentLanguageSelectcontentData').change( function() {
            refreshTree();
        });

        // Set tag panel based on persisted state
        if (localStorage.getItem("msgpt_view_onloadstate_tagpanel") != null) {
            try {
                var tagPanelState = JSON.parse(localStorage.getItem("msgpt_view_onloadstate_tagpanel"));
                if (tagPanelState.state == true)
                    togglePanel(tagPanelState);
            } catch (e) {
                console.log(e)
            }
        }


    };


    function positionPanel() {
        function computeFrameOffset (win, dims) {
            var _this = this;
            // Init
            if (typeof dims === 'undefined') {
                var dims = { top: 0, left: 0 };
            }

            // Resolve target iFrame: Find <iframe> within parent window
            var frames = win.parent.document.getElementsByTagName('iframe');
            var frame;
            var found = false;

            for (var i=0, len=frames.length; i<len; i++) {
                frame = frames[i];
                if (frame.contentWindow == win) {
                    found = true;
                    break;
                }
            }

            // Calculate and Recurse
            if (found) {
                var rect = $(frame).offset();
                dims.left += rect.left + parseFloat($(frame).css('padding-left').replace('px',''));
                dims.top += rect.top + parseFloat($(frame).css('padding-top').replace('px',''));
                if (win !== top) {
                    computeFrameOffset(win.parent, dims);
                }
            }
            return dims;
        }

        if ( tagpanel.treePanel == null )
            return;

        var pos_x, pos_y;

        var popupOffset = computeFrameOffset( window, $(tagpanel.containerEditor).offset() );
        pos_x = popupOffset.left  - $(tagpanel.treePanel).outerWidth() - 45;
        pos_y = popupOffset.top ;


        $(tagpanel.treePanel).css({
            top: pos_y + 'px',
            left: pos_x + 'px'
        });
    }

    function refreshTree() {
        if (
            tagpanel.tagCount != null &&
            tagpanel.tagCount != tagpanel.containerEditor.find('*').length &&
            $(tagpanel.treePanel).length > 0 &&
            $(getTopFrame().document).find('#mce-custompanel-tags').length > 0
        ) {
            resetActionPanel();
            tagpanel.currentFilter = $(tagpanel.treePanel).find('.mcePanelFilterSelect').val();
            $(tagpanel.treePanel).find('.mcePanelFilterSelect').val('all');
            $(tagpanel.treePanel).find('#tagTree').jstree(true).search( 'all' );
            generateTagTree(true);
        }

    }

    function resetActionPanel() {
        $(tagpanel.treePanel).find('.mcePanelActionsContainer').hide();
        $(tagpanel.treePanel).find('#tagTree').parent().append( $(tagpanel.treePanel).find('.mcePanelActionsContainer') );
    }

    function storeOnLoadState(toggleState, left, top) {
        if ( typeof(Storage)!=="undefined" ) {
            localStorage.setItem("msgpt_view_onloadstate_tagpanel", JSON.stringify({
                state: toggleState,
                x: left,
                y: top
            }));
        }
    }

    function toggleActionsMenu(n,o) {
        var actionPanel = $(tagpanel.treePanel).find('.mcePanelActionsContainer');

        $(tagpanel.treePanel).find('.mcePanelActionsContainer').find('.mcePanelAction').each( function() {
            var applicableTags = $(this).attr('tags') ? $(this).attr('tags').split(',') : new Array();
            $(this).hide();
            for ( i = 0; i < applicableTags.length; i++ ) {
                if ( $(o).is(applicableTags[i]) ) {
                    $(this).show();
                    break;
                }
            }
        });

        $(n).append( actionPanel );
        $(actionPanel).show();
    }

    function toggleFilter() {
        resetActionPanel();
        var searchTag = $(tagpanel.treePanel).find('.mcePanelFilterSelect option:selected').text();
        $(tagpanel.treePanel).find('#tagTree').jstree(true).search( searchTag );
    }



    if ( window.document != getTopFrame().document )
        $(getTopFrame().document).find('.mce-custompanel').remove();

    $.fn.viewTagPanel = function(containerEditor) {
        tagpanel.containerEditor = containerEditor;
        initTagPanel();
        var tagPanelState = JSON.parse(localStorage.getItem("msgpt_view_onloadstate_tagpanel"));
        togglePanel(tagPanelState);
        this.append(tagpanel.treePanel)
        return this;
    };

}( jQuery ));