/** popupBoundary r1 // 2010.01.04 // jQuery 1.3 // Prinova
 * 
 * Usage:
 * 
 * Dependencies:
 * jQuery 1.3 or later
 * 
 */

(function($) {

	$.fn.popupBoundary = function(boundaryEle) {

		return this.each ( function() {
			
			// If no boundary ele, use page as reference
			if (!boundaryEle)
				boundaryEle = document;
			
			// Calculate ele top/left pixel position in window
			var getElePos = function(ele) {
				if (ele != undefined && $(ele).position() != undefined) {
					var posArray = new Array(0,0);
					posArray[1] += $(ele).position().top;
					posArray[0] += $(ele).position().left;
					return posArray;
				} else {
					return new Array(0,0);
				}
			};
			
			// Return width/height in array for ele
			var getEleDimensions = function(ele) {
				if (ele != undefined) {	
					var dimArray = new Array(0,0);
					dimArray[0] = $(ele).outerWidth();
					dimArray[1] = $(ele).outerHeight();
					return dimArray;
				} else {
					return new Array(0,0);
				}
			};
			
			// Parse int values array from left/top CSS
			var getEleLeftTopPos = function(ele) {
				var cssPosArray = new Array(0,0);
				var eleLeft = $(ele).css("left");
				var eleTop = $(ele).css("top");
				if (eleLeft.indexOf("px") != -1)
					cssPosArray[0] = parseInt(eleLeft.replace("px",""));
				if (eleTop.indexOf("px") != -1)
					cssPosArray[1] = parseInt(eleTop.replace("px",""));
				return cssPosArray;	
			};
			
			// Parse int values array from marginLeft/marginTop CSS
			var getEleMarginPos = function(ele) {
				var cssPosArray = new Array(0,0);
				var eleLeft = $(ele).css("marginLeft");
				var eleTop = $(ele).css("marginTop");
				if (eleLeft.indexOf("px") != -1)
					cssPosArray[0] = parseInt(eleLeft.replace("px",""));
				if (eleTop.indexOf("px") != -1)
					cssPosArray[1] = parseInt(eleTop.replace("px",""));
				return cssPosArray;	
			};
			
			if (this == undefined || boundaryEle == undefined)
				return;
			
			var elePosArray = getElePos(this);
			var eleDimArray = getEleDimensions(this);
			
			var boundPosArray = getElePos(boundaryEle);
			var boundDimArray = getEleDimensions(boundaryEle);
			
			var posDeltaArray = new Array(0,0);
			// Calculate boundary element left edge position - element left edge position:
			// Left edge is overflowing boundary when value is negative: Num represents amount of overflow
			posDeltaArray[0] = (boundPosArray[0]+boundDimArray[0]) - (elePosArray[0]+eleDimArray[0]);
			// Boundary ele bottom edge - Element bottom edge:  Num represents overflow amount
			posDeltaArray[1] = (boundPosArray[1]+boundDimArray[1]) - (elePosArray[1]+eleDimArray[1]);
			
			// No overflow: Return
			if (posDeltaArray[0] > 0 && posDeltaArray[1] > 0)
				return;

			var eleLeftTopPos = getEleLeftTopPos(this);
			var eleMarginPos = getEleMarginPos(this);
			// Determine positioning used: Either left/top or margin-left/margin-top
			if (eleLeftTopPos[0] != 0 || eleLeftTopPos[1] != 0) {
				if (posDeltaArray[0] < 0)
					$(this).css("left",eleLeftTopPos[0]+posDeltaArray[0]-20); // -20 Avoid vert scroll
				if (posDeltaArray[1] < 0)
					$(this).css("top",eleLeftTopPos[1]+posDeltaArray[1]-5);
			} else {
				if (posDeltaArray[0] < 0)
					$(this).css("marginLeft",eleMarginPos[0]+posDeltaArray[0]-20); // -20 Avoid vert scroll
				if (posDeltaArray[1] < 0)
					$(this).css("marginTop",eleMarginPos[1]+posDeltaArray[1]-5);
			}

			
		});
		
	};
})(jQuery);