{"name": "jqcolorpicker", "version": "1.0.16", "title": "Van<PERSON><PERSON> Colorpicker", "description": "JQuery colorpicker with themeroller styling, RGB, HSL, CMYK and L*A*B support. Standard look & feel, configurable layout. Works as a popup or inline.", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "http://blog.vanderlee.com", "email": "<EMAIL>"}, "licenses": [{"type": "MIT", "url": "http://opensource.org/licenses/MIT"}], "repository": {"type": "git", "url": "https://github.com/vanderlee/colorpicker.git"}, "dependencies": {"jquery": ">=1.5", "jquery-ui": ">=1.7"}, "homepage": "http://www.vanderlee.com/martijn/?page_id=314", "demo": "http://vanderlee.github.com/colorpicker/", "download": "https://github.com/vanderlee/colorpicker", "bugs": "https://github.com/vanderlee/colorpicker/issues", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "url": "http://blog.vanderlee.com", "email": "<EMAIL>"}], "keywords": ["color", "picker", "colorpicker", "dialog", "ui", "widget"]}