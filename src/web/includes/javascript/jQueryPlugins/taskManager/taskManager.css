.project_task_listContainer {
	font-size: 11px; 
	padding: 8px;
	max-height: 350px;
	overflow: auto;
}

i.project_task_trayIcon {
	font-size: 11px;
	cursor: pointer;
}
i.project_task_trayIcon:hover {
	color: #fff;
}

i.project_task_icon {
	font-size: 12px;
	margin: 0px 6px 0px 2px; 
	position: relative;
	top: -1px;
}
i.project_task_icon.fa-times {
	color: red;
}
i.project_task_overdue {
	color: red;
}
i.project_task_nearTerm {
	color: orange;
}
i.project_task_new {
	color: green;
}
i.project_task_completeTaskIcon {
	float: right;
	display: none;
	padding-right: 6px;
	cursor: pointer;
	font-size: 12px;
}
.project_task_taskContainer:hover i.project_task_completeTaskIcon {
	display: block;
}

.project_task_taskLabel {
	font-size: 12px;
	color: #333;
	margin-bottom: 3px;
}
.project_task_taskRequirement {
	font-size: 10px;
	color: #555;
	margin-bottom: 2px;
}
.project_task_taskContainer {
	padding: 6px 6px 4px 6px;
	border-radius: 3px;
}
.project_task_taskContainer:hover {
	background-color: #f9f9f9;
}

.project_task_header {
	color: #333;
	font-size: 14px;
	border-bottom: 1px solid #ddd;
	margin: 0px 6px;
	margin-top: 6px;
	padding-bottom: 1px;
}


.project_task_summaryContainer {
	font-size: 11px; 
	margin: 12px;
}

.project_task_summaryItemContainer {
	display: inline-block;
	min-width: 150px;
	margin: 4px 8px;
}
.project_task_taskSummaryLabel {
	font-size: 11px;
	color: #555;
	margin-bottom: 2px;
}

.project_task_taskCategoryLabel {
	font-size: 11px;
	color: #555;
	margin: 4px 0px 2px 0px;
}

.project_task_taskSummaryValue {
	font-size: 13px;
	color: #333;
}

.project_task_countContainer {
	display: inline-block;
}

.project_task_taskCount {
	position: absolute;
	right: -.90em;
	top: 1.15em;
	font-size: 10px;
}

td .project_task_countContainer {
	position: relative;
	font-size: 14px;
	line-height: 1;
}

td .project_task_taskCount {
	font-size: 8px;
}