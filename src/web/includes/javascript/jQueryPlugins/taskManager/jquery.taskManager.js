/** taskManager r1 // 2015.09.10 // jQuery 1.11 // Prinova
 *
 * Dependencies:
 * jQuery 1.11 or later
 *
 */

(function ($) {

    $.fn.taskSummary = function (opts) {
        return this.each(function () {
            var conf = $.extend({}, opts);

            conf.popup_location = "right";
            conf.clear_on_mouseout = true;
            conf.offset_window = "window";

            var comp = null;
            if (conf !== false) comp = new taskManager_component();

            comp.data = $.extend({}, $.taskManager.defaults);
            comp.data = $.extend(false, {}, comp.data, conf);
            comp.initTaskSummaryPopup(this, conf.task_id);
        });
    };

    $.fn.refreshTaskManager = function () {
        var _this = getTopFrame().$.taskManager.get($(this));
        _this.requestTaskData();
    };

    $.taskManager = {
        get: function (obj) {
            var o = $(obj);
            if (!o.size()) o = $("#" + obj);
            if (!o.size()) return null;
            return taskManager_component.inst[o.attr('id')] || null;
        },
        defaults: {
            button_type: "default",
            popup_location: "left"
        }
    };

    $.fn.taskManager = function (opts) {
        return this.each(function () {
            var conf = $.extend({}, opts);
            if (conf !== false) new taskManager_component().init(this, conf);
        });
    };

    function taskManager_component() {
        return {

            data: $.extend({}, $.taskManager.defaults),

            init: function (elem, conf) {
                var _this = this;

                taskManager_component.inst[$(elem).attr("id")] = _this;
                _this.data = $.extend(false, {}, this.data, conf);
                _this.targetEle = $(elem);

                // CSS Include: Popup is built top window
                if ($(getTopFrame().document).find('#taskManagerCSS').length == 0)
                    $(getTopFrame().document).find('head').append("<link id=\"taskManagerCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + context + "/includes/javascript/jQueryPlugins/taskManager/taskManager.css\" />");

                if (_this.data.button_type == "icon_only") {
                    $(_this.targetEle).append("<div class=\"project_task_countContainer\"><i class=\"far fa-calendar-check detailTip\" " +
                        "title=\"|<div class='detailTipText'>" + client_messages.text.view_tasks + "</div>\"></i></div>");
                } else {
                    $(_this.targetEle).append("<div class=\"detailTip fa-mp-container project_task_countContainer\" title=\"|<div class='detailTipText'>" + client_messages.text.view_tasks + "</div>\">" +
                        "<i class=\"far fa-calendar-check\"></i>" +
                        "</div>");
                }

                _this.requestTaskCount();

                // Init tool tip
                $(_this.targetEle).find('.detailTip').each(function () {
                    initTip(this);
                });

                _this.targetEle.click(function () {
                    _this.requestTaskData();
                });

            },

            initTaskSummaryPopup: function (ele, taskId) {
                var _this = this;

                $(ele).hoverIntent({
                    sensitivity: 50, // number = sensitivity threshold (must be 1 or higher)
                    interval: 700,   // number = milliseconds of polling interval
                    over: function (e) {
                        _this.triggerSummaryPopup(e, ele, taskId);
                    },  // function = onMouseOver callback (required)
                    out: function () {
                        if (_this.data.clear_on_mouseout)
                            $(getTopFrame().document).find('#projectTasksSummaryPopup').remove();
                    }    // function = onMouseOut callback (required)
                });
            },

            getTaskDisplayItem: function (taskId) {
                var _this = this;

                for (var i = 0; i < _this.task_data.tasks.length; i++) {
                    var task = _this.task_data.tasks[i];
                    if (task.id == taskId) {

                        var iconHTML = "";
                        if (task.status == "complete") {
                            iconHTML = "<i class=\"far fa-check project_task_icon\"></i>";
                        } else if (task.status == "active") {
                            var statusMarker = "";
                            var taskTipText = client_messages.text.active;
                            if (task.near_term) {
                                statusMarker = "project_task_nearTerm";
                                taskTipText = client_messages.text.due_soon;
                            } else if (task.overdue) {
                                statusMarker = "project_task_overdue";
                                taskTipText = client_messages.text.overdue;
                            } else if (task.is_new) {
                                statusMarker = "project_task_new";
                                taskTipText = client_messages.text.new_task;
                            }

                            iconHTML = "<i class=\"fas fa-wrench project_task_icon detailTip " + statusMarker + "\" " +
                                "title=\"|<div class='detailTipText'>" + taskTipText + "</div>\"></i>";
                        }

                        var completeTaskIconHTML = "";
                        if (task.status == "active" && task.is_mine && task.can_mark_complete)
                            completeTaskIconHTML = "<i class=\"far fa-check-circle project_task_completeTaskIcon detailTip\" " +
                                "title=\"|<div class='detailTipText'>" + client_messages.text.mark_task_complete + "</div>\">" +
                                "</i>";

                        var taskHTML = "<div class=\"project_task_taskLabel\">" +
                            iconHTML +
                            task.task_name +
                            completeTaskIconHTML +
                            "</div>";
                        if (task.status == "active")
                            taskHTML += "<div class=\"project_task_taskRequirement\">" +
                                "(" + client_messages.text.due + " - " + task.due_date + ") " + task.requirement +
                                "</div>";

                        taskHTML = "<div id=\"project_task_" + task.id + "\" class=\"project_task_taskContainer\">" + taskHTML + "</div>";

                        return taskHTML;
                    }
                }
            },

            getTaskListDisplay: function () {
                var _this = this;

                var taskList = "";
                var completedTaskCount = 0;

                if (_this.task_data.tasks.length != 0) {

                    var myActiveTasksHTML = "";
                    var otherActiveTasksHTML = "";
                    var completeTasksHTML = "";
                    for (var i = 0; i < _this.task_data.tasks.length; i++) {
                        var task = _this.task_data.tasks[i];
                        if (task.status == "complete")
                            completeTasksHTML += _this.getTaskDisplayItem(task.id);
                        else if (task.status == "active" && task.is_mine)
                            myActiveTasksHTML += _this.getTaskDisplayItem(task.id);
                        else if (task.status == "active" && !task.is_mine)
                            otherActiveTasksHTML += _this.getTaskDisplayItem(task.id);
                    }

                    var taskListHTML = "";
                    if (myActiveTasksHTML != "") {
                        taskListHTML += "<div class=\"project_task_header\">" + client_messages.text.my_active_task + "</div>";
                        taskListHTML += myActiveTasksHTML;
                    }
                    if (otherActiveTasksHTML != "") {
                        taskListHTML += "<div class=\"project_task_header\">" + client_messages.text.other_active_task + "</div>";
                        taskListHTML += otherActiveTasksHTML;
                    }
                    if (completeTasksHTML != "" && completedTaskCount < 2) {
                        taskListHTML += "<div class=\"project_task_header\">" + client_messages.text.completed + "</div>";
                        taskListHTML += completeTasksHTML;
                        completedTaskCount++;
                    }

                    taskList = $("<div align=\"left\" class=\"project_task_listContainer\">" + taskListHTML + "</div>");

                    $(taskList).find('.project_task_completeTaskIcon').click(function () {
                        _this.markTaskComplete($(this).closest('.project_task_taskContainer').attr('id').split('_')[2]);
                        $(this).closest('.project_task_taskContainer').remove();
                    });

                    // Init task summary popup
                    $(taskList).find('.project_task_taskContainer').each(function () {
                        _this.initTaskSummaryPopup(this, $(this).attr('id').split('_')[2]);
                    });
                    // Init item detail tool tip
                    $(taskList).find('.detailTip').each(function () {
                        getTopFrame().initTip(this);
                    });

                } else {

                    taskList = $('<div class="InfoSysContainer_info">' +
                                    '<i class="far icon fa-info-circle" aria-hidden="true"></i>' +
                                    '<p>' + client_messages.text.no_tasks + '</p>' +
                                  '</div>');

                }

                return taskList;
            },

            markTaskComplete: function (id) {
                var _this = this;

                var stampDate = new Date();
                $.ajax({
                    type: "GET",
                    url: context + "/getProjectTask.form" +
                    "?action=complete" +
                    "&task_id=" + id +
                    "&tk=" + getParam('tk') +
                    "&cacheStamp=" + (stampDate.getTime()),
                    dataType: "json",
                    success: function (data) {
                        _this.requestTaskData();
                        _this.requestTaskCount();
                    }
                });
            },

            processTaskCount: function (data) {
                var _this = this;

                $(_this.targetEle).find(".project_task_taskCount").remove();
                $(_this.targetEle).find(".project_task_countContainer")
                    .append("<div class=\"project_task_taskCount\">" + data.task_count + "</div>");

            },

            requestTaskCount: function () {
                var _this = this;

                var stampDate = new Date();
                $.ajax({
                    type: "GET",
                    url: context + "/getProjectTask.form" +
                    "?action=task_count" +
                    "&item_type=" + _this.data.item_type +
                    "&item_id=" + _this.data.item_id +
                    "&tk=" + getParam('tk') +
                    "&cacheStamp=" + (stampDate.getTime()),
                    dataType: "json",
                    success: function (data) {
                        _this.processTaskCount(data);
                    }
                });

            },

            requestTaskData: function () {
                var _this = this;

                var stampDate = new Date();
                $.ajax({
                    type: "GET",
                    url: context + "/getProjectTask.form" +
                    "?action=task_data" +
                    "&item_type=" + _this.data.item_type +
                    "&item_id=" + _this.data.item_id +
                    "&tk=" + getParam('tk') +
                    "&cacheStamp=" + (stampDate.getTime()),
                    dataType: "json",
                    success: function (data) {
                        _this.task_data = data;

                        if ($(getTopFrame().document).find('#projectTasksList_' + $(_this.targetEle).attr('id')).length == 0) {
                            $(_this.targetEle)
                                .popupFactory({
                                    title: client_messages.title.tasks,
                                    popup_id: "projectTasksList_" + $(_this.targetEle).attr('id'),
                                    popupLocation: _this.data.popup_location == "right" ? "right" : "bottom-left",
                                    trigger: "instant",
                                    width: 300,
                                    afterPopup: function (o) {
                                        o.popupEle.css({'z-index': 1000});
                                    },
                                    fnSetContent: function (o) {
                                        return _this.getTaskListDisplay();
                                    },
                                    beforePopupClose: function () {
                                        popupFactoryRemove('taskManager_taskListClose')
                                    }
                                });
                        } else {
                            $(getTopFrame().document).find('#projectTasksList_' + $(_this.targetEle).attr('id') + ' .popupFactory_contentContainer').html(_this.getTaskListDisplay());
                        }
                    }
                });
            },

            triggerSummaryPopup: function (e, ele, taskId) {

                var _this = this;

                var stampDate = new Date();
                $.ajax({
                    type: "GET",
                    url: context + "/getProjectTask.form" +
                    "?action=task_summary" +
                    "&task_id=" + taskId +
                    "&tk=" + getParam('tk') +
                    "&cacheStamp=" + (stampDate.getTime()),
                    dataType: "json",
                    success: function (data) {

                        $(ele)
                            .popupFactory({
                                title: data.task_name,
                                popup_id: "projectTasksSummaryPopup",
                                concurrentPopups: true,
                                offsetWindow: _this.data.offset_window == "window" ? window : getTopFrame(),
                                popupLocation: _this.data.popup_location == "right" ? "right" : "left",
                                trigger: "instant",
                                width: 400,
                                afterPopup: function (o) {
                                    o.popupEle.css({'z-index': 1001});
                                    var resizeWidth = Math.max(400, $('#projectTasksSummaryPopup').find('.popupFactory_titleContainer').innerWidth());
                                    $('#projectTasksSummaryPopup').width(resizeWidth);
                                },
                                fnSetContent: function (o) {

                                    let dueClass = '';
                                    if (data.status !== 'Complete') {
                                        if (data.near_term) {
                                            dueClass = 'text-warning';
                                        } else if (data.overdue) {
                                            dueClass = 'text-danger';
                                        }
                                    }

                                    var summaryHTML =
                                        "<div align=\"left\" class=\"project_task_summaryContainer\">" +

                                        "<div class=\"project_task_summaryItemContainer\">" +
                                        "<div class=\"project_task_taskSummaryLabel\">" + client_messages.text.owner + "</div>" +
                                        "<div class=\"project_task_taskSummaryValue\">" + data.assignee + "</div>" +
                                        "</div>" +
                                        "<div class=\"project_task_summaryItemContainer\">" +
                                        "<div class=\"project_task_taskSummaryLabel\">" + client_messages.text.status + "</div>" +
                                        "<div class=\"project_task_taskSummaryValue\">" + data.status + "</div>" +
                                        "</div>" +
                                        "<div class=\"project_task_summaryItemContainer\">" +
                                        "<div class=\"project_task_taskSummaryLabel\">" + client_messages.text.due + "</div>" +
                                        "<div class=\"project_task_taskSummaryValue " + dueClass + " + \">" + data.due + "</div>" +
                                        "</div>" +
                                        "<div class=\"project_task_summaryItemContainer\">" +
                                        "<div class=\"project_task_taskSummaryLabel\">" + client_messages.text.created + "</div>" +
                                        "<div class=\"project_task_taskSummaryValue\">" + data.created + "</div>" +
                                        "</div>" +
                                        "<div class=\"project_task_summaryItemContainer\">" +
                                        "<div class=\"project_task_taskSummaryLabel\">" + client_messages.text.description + "</div>" +
                                        "<div class=\"project_task_taskSummaryValue\"><pre>" + data.requirement + "</pre></div>" +
                                        "</div>";
                                    if (data.project_requirement) {
                                        summaryHTML += "<div class=\"project_task_summaryItemContainer\">" +
                                            "<div class=\"project_task_taskSummaryLabel\">" + client_messages.text.project_requirement + "</div>" +
                                            "<div class=\"project_task_taskSummaryValue\">" + data.project_requirement + "</div>" +
                                            "</div>";
                                    }

                                    if (data.metadata && data.metadata.length > 0)
                                        for (var i = 0; i < data.metadata.length; i++)
                                            summaryHTML += "<div class=\"project_task_summaryItemContainer\">" +
                                                "<div class=\"project_task_taskSummaryLabel\">" + data.metadata[i].label + "</div>" +
                                                "<div class=\"project_task_taskSummaryValue\">" + data.metadata[i].value + "</div>" +
                                                "</div>";

                                    if (data.assets && data.assets.length > 0) {

                                        summaryHTML += "<div style=\"margin: 4px 8px; border-top: 1px solid #ccc; padding-top: 8px;\">";

                                        summaryHTML += "<div class=\"project_task_taskSummaryLabel\">" +
                                            client_messages.text.task_assets +
                                            "</div>";

                                        for (var j = 0; j < data.assets.length; j++) {

                                            if (data.assets[j].groups && data.assets[j].groups.length > 0) {
                                                var itemsHTML = "", tpList = "";
                                                for (var l = 0; l < data.assets[j].groups.length; l++) {
                                                    tpList += (tpList != "" ? ", " + data.assets[j].groups[l].label : data.assets[j].groups[l].label);
                                                    for (var k = 0; k < data.assets[j].groups[l].items.length; k++) {
                                                        itemsHTML += "<div class=\"project_task_taskSummaryValue\" style=\"margin-left: 18px;\">" +
                                                            data.assets[j].groups[l].items[k] + " (" + data.assets[j].groups[l].label + ")" +
                                                            "</div>";
                                                    }
                                                }
                                                summaryHTML += "<div class=\"project_task_taskCategoryLabel\">" +
                                                    data.assets[j].label + " (" + tpList + ")" +
                                                    "</div>";
                                                summaryHTML += itemsHTML;
                                            } else {
                                                summaryHTML += "<div class=\"project_task_taskCategoryLabel\">" +
                                                    data.assets[j].label +
                                                    "</div>";

                                                for (var k = 0; k < data.assets[j].items.length; k++) {
                                                    summaryHTML += "<div class=\"project_task_taskSummaryValue\" style=\"margin-left: 18px;\">" +
                                                        data.assets[j].items[k] +
                                                        "</div>";
                                                }
                                            }

                                        } // End Assets list

                                        summaryHTML += "<div>";
                                    }

                                    summaryHTML += "</div>";


                                    return summaryHTML;
                                }
                            });

                    }
                });

            }

        }; // end component
    };

    // instance manager
    taskManager_component.inst = {};

})(jQuery);	