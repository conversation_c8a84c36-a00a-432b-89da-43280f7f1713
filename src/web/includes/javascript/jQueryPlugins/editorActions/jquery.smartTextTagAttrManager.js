/** smartTextTagAttrManager r1 // 2013.05.30 // jQuery 1.8 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.8 or later
 * 
 */

(function($) {
	
	$.smartTextTagAttrManager = {
		get	: function (obj) { 
			var o = $(obj);
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null;
			return smartTextTagAttrManager_component.inst[o.attr('id')] || null;
		},
		defaults : {
			offsetWindow			: window,
			langCode				: 'en',
			objectDna				: '',
			contentObjectId			: -1,
			mode					: 'edit',
			preventPopup			: null,
			beforePopupClose		: null,
			afterPopup				: null,
			compoundFmtTypes		: [
			              		    	{ id: '0', value: client_messages.compound_fmt.none },
			              		    	{ id: '1', value: client_messages.compound_fmt.and },
			              		    	{ id: '2', value: client_messages.compound_fmt.or }
			              		      ],
			contentTypes			: [ 
			                 		    { id: '1'	, value: client_messages.compound_content_type.all},
			                 		    { id: '2'	, value: client_messages.compound_content_type.distinct},
			                 		    { id: '3'	, value: client_messages.compound_content_type.non_blank},
			                 		    { id: '4'	, value: client_messages.compound_content_type.first_non_blank},
			                 		    { id: '5'	, value: client_messages.compound_content_type.non_blank_and_duplicate}
			                 		  ]
		}
	};
	
	$.fn.smartTextTagAttrManager = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if(conf !== false) new smartTextTagAttrManager_component().init(this, conf);
		});
	};
	
	function smartTextTagAttrManager_component () {
		return {
			
		data : $.extend({},$.smartTextTagAttrManager.defaults),

		init : function(elem, conf) {
			var _this = this;

			smartTextTagAttrManager_component.inst[$(elem).attr("id")] = _this;
			_this.data = $.extend(false, {}, this.data, conf);
			_this.targetEle = $(elem);
			_this.targetEle.popupFactory({
				title					: client_messages.title.compound_smart_text,
				trigger					: (_this.data.mode == "edit" || _this.data.mode == "view_instant") ? "instant" : "hover",
				width					: 300,
				zIndex                  : 1049,
				popupLocation			: "right",
				offsetWindow			: _this.data.offsetWindow,
				preventPopup			: _this.data.preventPopup,
				afterPopup				: _this.data.afterPopup,
				beforePopupClose		: _this.data.beforePopupClose,
				pollForRemove			: true,
				asyncSetContentURL		: context + "/getContentObject.form?type=embeddedContentInfo&objectDna=" + _this.data.objectDna +  "&cacheStamp=" + (stampDate.getTime()),
				asyncSetContentHandler	: function(o, data) {
					
											_this.popupWidget 		= o;

											var returnHTML = 	"<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" +
																	"<table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" class=\"innerContentTable\">";

											// EDIT: HTML layout
											if ( _this.data.mode == "edit" ) {
												
												returnHTML +=				"<tr>" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.compound_values + ":</td>" +
																				"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" + 
																					"<input type=\"checkbox\" class=\"checkbox\" id=\"enableCompoundValuesCheckbox\" />" + 
																				"</td>" +
																			"</tr>" +
																			"<tr id=\"compoundValueStartsNewParagraphContainer\">" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.insert.as.paragraphs + ":</td>" +
																				"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" + 
																					"<input type=\"checkbox\" class=\"checkbox\" id=\"compoundValueInsertAsParagraphCheckbox\" />" + 
																				"</td>" +
																			"</tr>";
													returnHTML +=			"<tr id=\"compoundValueDelimiterContainer\">" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.delimiter + ":</td>" +
																				"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																					"<input id=\"compoundValueDelimiterInput\" class=\"input2digit\" maxlength=\"2\" />" +
																				"</td>" +
																			"</tr>" +
																			"<tr id=\"compoundValueListEndDelimiterContainer\">" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.list_end_delimiter + ":</td>" +
																				"<td  align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																					"<select id=\"compoundValueListEndDelimiterSelect\" class=\"inputS\">";
													for (var i=0; i < _this.data.compoundFmtTypes.length; i++)
														returnHTML +=					"<option value=\"" + _this.data.compoundFmtTypes[i].id + "\">" + _this.data.compoundFmtTypes[i].value + "</option>";
													returnHTML +=					"</select>" +
																				"</td>" +
																			"</tr>";
												returnHTML +=				"<tr id=\"compoundValueContentTypeContainer\">" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.content + ":</td>" +
																				"<td  align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																					"<select id=\"compoundValueContentTypeSelect\" class=\"inputM\">";
												for (var i=0; i < _this.data.contentTypes.length; i++)
													returnHTML +=						"<option value=\"" + _this.data.contentTypes[i].id + "\">" + _this.data.contentTypes[i].value + "</option>";
												returnHTML +=						"</select>" +
																				"</td>" +
																			"</tr>" +
																			"<tr>" +
																				"<td/>" +
																				"<td align=\"left\" style=\"vertical-align: middle; white-space: nowrap; padding: 2px; padding-top: 8px;\">" +
																					"<span style=\"display: inline-block;\"><input title=\"" + client_messages.button.ok_upper + "\" type=\"button\" id=\"smartTextTagAttrSaveButton\" style=\"display: none;\" /></span>" +
																					"<span style=\"display: inline-block;\"><input title=\"" + client_messages.button.cancel_upper + "\" type=\"button\" id=\"smartTextTagAttrCloseButton\" style=\"display: none;\" /></span>" +
																				"</td>" +
																			"</tr>";

											// VIEW: HTML layout 
											} else {
												
												// VIEW: Compound Values
												var applyCompoundValues = false;
												if ( $(o.targetEle).attr('is_compound') != undefined && $(o.targetEle).attr('is_compound') != '' )
													applyCompoundValues = $(o.targetEle).attr('is_compound') == "true";
												
												returnHTML +=	"<tr>" +
																	"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.compound_values + ":</td>" +
																	"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																		( applyCompoundValues ? client_messages.text.yes : client_messages.text.no ) +
																	"</td>" +
																"</tr>";
												
												if ( applyCompoundValues ) {
												
													var isInsertAsParagraph = data.insert_as_paragraph;
													if ( $(o.targetEle).attr('insert_as_paragraph') != undefined && $(o.targetEle).attr('insert_as_paragraph') != '' )
														isInsertAsParagraph = ( $(o.targetEle).attr('insert_as_paragraph') == "true" );
													
													returnHTML +=	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.insert.as.paragraphs + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																			( isInsertAsParagraph ? client_messages.text.yes : client_messages.text.no ) +
																		"</td>" +
																	"</tr>";
													
													if ( !isInsertAsParagraph ) {	

														// VIEW: Delimiter
														var delimiterValue = '<i>None</i>';
														if ( $(o.targetEle).attr('delimiter') != undefined && $(o.targetEle).attr('delimiter') != '' )
															delimiterValue = $(o.targetEle).attr('delimiter');
														
														returnHTML +=	"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.delimiter + ":</td>" +
																			"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																				delimiterValue +
																			"</td>" +
																		"</tr>";
		
														// VIEW: List End Delimiter
														var compoundFmtTypeValue = _this.data.compoundFmtTypes[0].id;
														if ( $(o.targetEle).attr('compound_fmt') != undefined && $(o.targetEle).attr('compound_fmt') != '' )
															compoundFmtTypeValue = $(o.targetEle).attr('compound_fmt');
														var compoundFmtTypeDisplayValue = "";
														for ( var i=0; i < _this.data.compoundFmtTypes.length; i++)
															if ( _this.data.compoundFmtTypes[i].id == compoundFmtTypeValue )
																compoundFmtTypeDisplayValue += _this.data.compoundFmtTypes[i].value;
														
														returnHTML +=	"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.list_end_delimiter + ":</td>" +
																			"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																				compoundFmtTypeDisplayValue +
																			"</td>" +
																		"</tr>";

													}
	
													// VIEW: Content
													var contentTypeValue = _this.data.contentTypes[0].id;
													if ( $(o.targetEle).attr('include_type') != undefined && $(o.targetEle).attr('include_type') != '' )
														contentTypeValue = $(o.targetEle).attr('include_type');
													var contentTypeDisplayValue = "";
													for ( var i=0; i < _this.data.contentTypes.length; i++)
														if ( _this.data.contentTypes[i].id == contentTypeValue )
															contentTypeDisplayValue += _this.data.contentTypes[i].value;
													
													returnHTML +=	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.content + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																			contentTypeDisplayValue +
																		"</td>" +
																	"</tr>";

												}
												
											}

											returnHTML += 		"</table></div>";
											
											
											var returnObj = $(returnHTML);
											_this.attrDisplayContainer = returnObj;

											// EDIT: Init element script
											if ( _this.data.mode == "edit" ) {
												
												var toggleInsertAsParagraph = function() {
													if ( !$(returnObj).find('#compoundValueInsertAsParagraphCheckbox').is(':checked') )
														$(returnObj).find('#compoundValueDelimiterContainer,#compoundValueListEndDelimiterContainer').showEle('normal');
													else
														$(returnObj).find('#compoundValueDelimiterContainer,#compoundValueListEndDelimiterContainer').hide();
												};
												
												var applyCompoundValues = false;
												if ( $(o.targetEle).attr('is_compound') != undefined && $(o.targetEle).attr('is_compound') != '' )
													applyCompoundValues = $(o.targetEle).attr('is_compound') == "true";

												if ( applyCompoundValues )
													$(returnObj).find('#enableCompoundValuesCheckbox').attr('checked','checked');
												
												var isInsertAsParagraph = data.insert_as_paragraph;
												if ( $(o.targetEle).attr('insert_as_paragraph') != undefined && $(o.targetEle).attr('insert_as_paragraph') != '' )
													isInsertAsParagraph = ( $(o.targetEle).attr('insert_as_paragraph') == "true" );
												
												if ( isInsertAsParagraph )
													$(returnObj).find('#compoundValueInsertAsParagraphCheckbox').attr('checked','checked');
												toggleInsertAsParagraph();
												
												if ( !applyCompoundValues )
													$(returnObj).find('#compoundValueStartsNewParagraphContainer,#compoundValueDelimiterContainer,#compoundValueListEndDelimiterContainer,#compoundValueContentTypeContainer').hide();
												
												// INIT Value: Delimiter
												if ( $(o.targetEle).attr('delimiter') != undefined && $(o.targetEle).attr('delimiter') != '' )
													$(returnObj).find('#compoundValueDelimiterInput').val( $(o.targetEle).attr('delimiter') );
												
												// INIT Value: List End Delimiter
												if ( $(o.targetEle).attr('compound_fmt') != undefined && $(o.targetEle).attr('compound_fmt') != '' )
													$(returnObj).find("#compoundValueListEndDelimiterSelect option[value='" + $(o.targetEle).attr('compound_fmt') + "']").attr('selected','selected');
												
												// INIT Value: Content
												if ( $(o.targetEle).attr('include_type') != undefined && $(o.targetEle).attr('include_type') != '' )
													$(returnObj).find("#compoundValueContentTypeSelect option[value='" + $(o.targetEle).attr('include_type') + "']").attr('selected','selected');

												$(returnObj).find('#smartTextTagAttrSaveButton').click( function() {
													_this.setVarAttr();
												});
												$(returnObj).find('#smartTextTagAttrCloseButton').click( function() {
													_this.popupWidget.closePopup();
												});

												$(returnObj).find('#compoundValueInsertAsParagraphCheckbox').click( function() {
													toggleInsertAsParagraph();
												});
												
												$(returnObj).find('#enableCompoundValuesCheckbox').click( function() {
													if ( $(this).is(':checked') )
														$(returnObj).find('#compoundValueStartsNewParagraphContainer,#compoundValueContentTypeContainer').showEle('normal');
													else
														$(returnObj).find('#compoundValueStartsNewParagraphContainer,#compoundValueContentTypeContainer').hide();
													toggleInsertAsParagraph();
												});

												$(returnObj).find('#smartTextTagAttrSaveButton,#smartTextTagAttrCloseButton').styleActionElement();
												
												// INPUT FOCUS: First visible input
												var inputs = $(returnObj).find('input:visible:first,textarea:visible:first,select:visible:first,a:visible:first');
												for (var j=0; j < inputs.length; j++) {
													$(inputs[j]).focus();
													break;
												}
												
												$(returnObj).find('.infoIconDiv').each( function() {
													$(this).attr('title',_this.data.infoTxt[$(this).attr('infoTag')]);
												});
												
											}

											return $(returnObj);

										  }
			});

		},
		
		setVarAttr : function() {
			var _this = this;
			
			$(_this.targetEle).removeAttr('is_compound insert_as_paragraph delimiter compound_fmt include_type');
			
			var isCompoundValue = _this.attrDisplayContainer.find('#enableCompoundValuesCheckbox').is(':checked');
			$(_this.targetEle).attr('is_compound', isCompoundValue);
			
			if ( isCompoundValue ) {
				
				var isInsertAsParagraph = _this.attrDisplayContainer.find('#compoundValueInsertAsParagraphCheckbox').is(':checked');
				$(_this.targetEle).attr('insert_as_paragraph', isInsertAsParagraph);
				
				if ( !isInsertAsParagraph ) {
					// SET Value: Delimiter
					var delimiterValue = _this.attrDisplayContainer.find('#compoundValueDelimiterInput').val();
					$(_this.targetEle).attr('delimiter', delimiterValue);
					
					// SET Value: End List Delimiter
					var endListDelimiterValue = _this.attrDisplayContainer.find('#compoundValueListEndDelimiterSelect option:selected').val();
					$(_this.targetEle).attr('compound_fmt', endListDelimiterValue);
				}
				
				// SET Value: Content
				var contentTypeValue = _this.attrDisplayContainer.find('#compoundValueContentTypeSelect option:selected').val();
				$(_this.targetEle).attr('include_type', contentTypeValue);
				
			}
		
			_this.popupWidget.closePopup();
		}

		}; // end component
	};
	
	// instance manager
	smartTextTagAttrManager_component.inst = {};
	
})(jQuery);	