/** placeholderTagAttrManager r1 // 2013.05.30 // jQuery 1.8 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.8 or later
 * 
 */

(function($) {
	
	$.placeholderTagAttrManager = {
		get	: function (obj) { 
			var o = $(obj);
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null;
			return placeholderTagAttrManager_component.inst[o.attr('id')] || null;
		},
		defaults : {
			offsetWindow			: window,
			langCode				: 'en',
			objectDna				: '',
			mode					: 'edit',
			preventPopup			: null,
			beforePopupClose		: null,
			afterPopup				: null
		}
	};
	
	$.fn.placeholderTagAttrManager = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if(conf !== false) new placeholderTagAttrManager_component().init(this, conf);
		});
	};
	
	function placeholderTagAttrManager_component () {
		return {
			
		data : $.extend({},$.placeholderTagAttrManager.defaults),

		init : function(elem, conf) {
			var _this = this;

			placeholderTagAttrManager_component.inst[$(elem).attr("id")] = _this;
			_this.data = $.extend(false, {}, this.data, conf);
			_this.targetEle = $(elem);

			_this.targetEle.popupFactory({
				title					: client_messages.title.placeholder,
				trigger					: (_this.data.mode == "edit" || _this.data.mode == "view_instant") ? "instant" : "hover",
				width					: 300,
				offsetWindow			: _this.data.offsetWindow,
				preventPopup			: _this.data.preventPopup,
				afterPopup				: _this.data.afterPopup,
				beforePopupClose		: _this.data.beforePopupClose,
				pollForRemove			: true,
				asyncSetContentURL		: context + "/getObjectInfo.form?type=zoneSummary&objectDna=" + _this.data.objectDna + "&cacheStamp=" + (stampDate.getTime()),
				asyncSetContentHandler	: function(o, data) {
					
											_this.popupWidget 		= o;

											var returnHTML = 	"<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" +
																	"<table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" class=\"innerContentTable\">";

											// EDIT: HTML layout
											if ( _this.data.mode == "edit" ) {
												var mergeTables = false;
												if ( $(o.targetEle).attr('merge_tables') != undefined && $(o.targetEle).attr('merge_tables') != '' )
													mergeTables = $(o.targetEle).attr('merge_tables') == "true";

												returnHTML +=				"<tr>" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.merge_tables + ":</td>" +
																				"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" + 
																					"<input type=\"checkbox\" class=\"checkbox\" id=\"mergeTablesCheckbox\" " + (mergeTables ? "checked" : "") + " />" +
																				"</td>" +
																			"</tr>" +
																			"<tr>" +
																				"<td/>" +
																				"<td align=\"left\" style=\"vertical-align: middle; white-space: nowrap; padding: 2px; padding-top: 8px;\">" +
																					"<span style=\"display: inline-block;\"><input title=\"" + client_messages.button.ok_upper + "\" type=\"button\" id=\"placeholderTagAttrSaveButton\" style=\"display: none;\" /></span>" +
																					"<span style=\"display: inline-block;\"><input title=\"" + client_messages.button.cancel_upper + "\" type=\"button\" id=\"placeholderTagAttrCloseButton\" style=\"display: none;\" /></span>" +
																				"</td>" +
																			"</tr>";

											// VIEW: HTML layout 
											} else {
												
												// VIEW: Compound Values
												var mergeTables = false;
												if ( $(o.targetEle).attr('merge_tables') != undefined && $(o.targetEle).attr('merge_tables') != '' )
													mergeTables = $(o.targetEle).attr('merge_tables') == "true";
												
												returnHTML +=	"<tr>" +
																	"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.merge_tables + ":</td>" +
																	"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																		( mergeTables ? client_messages.text.yes : client_messages.text.no ) +
																	"</td>" +
																"</tr>";

											}

											returnHTML += 		"</table></div>";
											
											
											var returnObj = $(returnHTML);
											_this.attrDisplayContainer = returnObj;

											// EDIT: Init element script
											if ( _this.data.mode == "edit" ) {

												var mergeTables = false;
												if ( $(o.targetEle).attr('merge_tables') != undefined && $(o.targetEle).attr('merge_tables') != '' )
													mergeTables = $(o.targetEle).attr('merge_tables') == "true";

												// INPUT FOCUS: First visible input
												var inputs = $(returnObj).find('input:visible:first,textarea:visible:first,select:visible:first,a:visible:first');
												for (var j=0; j < inputs.length; j++) {
													$(inputs[j]).focus();
													break;
												}
												
												$(returnObj).find('.infoIconDiv').each( function() {
													$(this).attr('title',_this.data.infoTxt[$(this).attr('infoTag')]);
												});
												
												$(returnObj).find('#placeholderTagAttrSaveButton').click( function() {
													_this.setVarAttr();
												});
												$(returnObj).find('#placeholderTagAttrCloseButton').click( function() {
													_this.popupWidget.closePopup();
												});
												
												$(returnObj).find('#placeholderTagAttrSaveButton,#placeholderTagAttrCloseButton').styleActionElement();
												
											}

											return $(returnObj);

										  }
			});

		},
		
		setVarAttr : function() {
			var _this = this;
			
			$(_this.targetEle).removeAttr('merge_tables');
			
			var mergeTables = _this.attrDisplayContainer.find('#mergeTablesCheckbox').is(':checked');
			if ( mergeTables )
				$(_this.targetEle).attr('merge_tables', mergeTables);
		
			_this.popupWidget.closePopup();
		}

		}; // end component
	};
	
	// instance manager
	placeholderTagAttrManager_component.inst = {};
	
})(jQuery);	