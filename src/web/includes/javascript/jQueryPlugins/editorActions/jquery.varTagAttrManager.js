/** varTagAttrManager r1 // 2013.05.30 // jQuery 1.8 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.8 or later
 * 
 */

(function($) {
	
	$.varTagAttrManager = {
		get	: function (obj) { 
			var o = $(obj);
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null;
			return varTagAttrManager_component.inst[o.attr('id')] || null;
		},
		defaults : {
			offsetWindow			: window,
			trigger					: 'hover',
			localeId				: '1',
			objectDna				: '',
			mode					: 'edit',
			preventPopup			: null,
			beforePopupClose		: null,
			afterPopup				: null,
			is_smart_text			: false,
			date_value_separator_str: '@#@',
			dateFormats				: [ 'm/d/yy' , 'm/d/yyyy' , 'mm/dd/yy', 'mm/dd/yyyy' , 'mm-dd-yy' , 
			                            'mm-dd-yyyy', 'mmddyy' , 'mmddyyyy' , 'd/m/yyyy', 'dd/mm/yy' , 
			                            'dd/mm/yyyy' , 'dd-mm-yy', 'dd-mm-yyyy', 'ddmmyy' , 'ddmmyyyy' , 'yyddmm', 'yymmdd' , 'yyyyddmm',
			                            'yyyymmdd' , 'yyyy-mm-dd' , 'yyyy/mm/dd' , 'm/yy', 'mm/yy', 'm/yyyy' , 'mm/yyyy' ,
			                            'mmyy', 'dd/mm' , 'mm/dd' , 'M dd', 'M dd, yyyy', 'M d, yyyy', 'MM dd, yyyy', 'MM d, yyyy' , 'MM yyyy' ,
			                            'E, MM d, yyyy', 'dd MM yyyy', 'd MM yyyy', 'd M yyyy', 'MM d' , 'yyyy' , 'm', 'mm', 'MM', 'dd' , 'd', 'N', 'E', 'M' ],
			dateDeltaTypes			: [
			              		    	{ id: 'd', value: client_messages.text.targeting.days },
			              		    	{ id: 'm', value: client_messages.text.targeting.months },
			              		    	{ id: 'y', value: client_messages.text.targeting.years }
			              		      ],
			stringCaseFormats		: [ 
			                 		    { id: 'none'	, value: client_messages.format.unmodified},
			                 		    { id: 'upper'	, value: client_messages.format.uppercase},
			                 		    { id: 'lower'	, value: client_messages.format.lowercase},
			                 		    { id: 'firstcap', value: client_messages.format.uppercase_first_character},
			                 		    { id: 'initcap'	, value: client_messages.format.camel_case}
			                 		  ],
			stringTrimTypes			: [ 
			               			    { id: 'none'	, value: client_messages.format.no_trim},
			               			    { id: 'leading'	, value: client_messages.format.trim_before},
			               			    { id: 'trailing', value: client_messages.format.trim_after},
			               			    { id: 'all'		, value: client_messages.format.trim_all}
			               			  ],
			infoTxt					: {
										"possessive": client_messages.text.variable_make_possessive,
										"trim"		: client_messages.text.variable_trim,
										"disqualify": client_messages.text.disqualify_smart_text
									  },
			compoundFmtTypes		: [
			              		    	{ id: '0', value: client_messages.compound_fmt.none },
			              		    	{ id: '1', value: client_messages.compound_fmt.and },
			              		    	{ id: '2', value: client_messages.compound_fmt.or }
			              		      ],
			contentTypes			: [ 
			                 		    { id: '1'	, value: client_messages.compound_content_type.all},
			                 		    { id: '2'	, value: client_messages.compound_content_type.distinct},
			                 		    { id: '3'	, value: client_messages.compound_content_type.non_blank},
			                 		    { id: '4'	, value: client_messages.compound_content_type.first_non_blank},
			                 		    { id: '5'	, value: client_messages.compound_content_type.non_blank_and_duplicate}
			                 		  ]
		}
	};
	
	$.fn.varTagAttrManager = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if(conf !== false) new varTagAttrManager_component().init(this, conf);
		});
	};
	
	function varTagAttrManager_component () {
		return {
			
		data : $.extend({},$.varTagAttrManager.defaults),

		init : function(elem, conf) {
			var _this = this;

			varTagAttrManager_component.inst[$(elem).attr("id")] = _this;
			_this.data = $.extend(false, {}, this.data, conf);
			_this.targetEle = $(elem);

			_this.targetEle.popupFactory({
				title					: $(_this.targetEle).is("[sys_type_id]") ? client_messages.title.system_variable : client_messages.title.variable,
				trigger					: _this.data.mode == "edit" || _this.data.trigger == "instant" ? "instant" : "hover",
				offsetWindow			: _this.data.offsetWindow,
				preventPopup			: _this.data.preventPopup,
				pollForRemove			: true,
				afterPopup				: _this.data.afterPopup,
				beforePopupClose		: _this.data.beforePopupClose,
				asyncSetContentURL		: context + "/getVariables.form?type=variableInfo&localeId=" + _this.data.localeId + "&objectDna=" + _this.data.objectDna + "&cacheStamp=" + (stampDate.getTime()),
				asyncSetContentHandler	: function(o, varData) {
					
											_this.popupWidget 		= o;
											_this.data_subtype_id 	= varData.data_subtype_id;
											_this.system_type_id	= -1;
											if ( varData.is_system_variable && varData.system_type_id )
													_this.system_type_id	= varData.system_type_id;
											
											var popupWidth = 300;
											if ( _this.data.mode == "view" )
												popupWidth = 300;
											else if (_this.data_subtype_id == 600 || _this.data_subtype_id == 100)
												popupWidth = 380;

											o.popupEle.animate({'width': popupWidth});
											
											// SYSTEM VARIABLES
											if ( varData.is_system_variable && varData.system_type_id != 11 && varData.system_type_id != 12 && varData.system_type_id != 7 ) {
												
												var returnHTML = 	"<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" +
																		varData.name + 
																	"</div>";
												
												return $(returnHTML);
											}
					
											// USER VARIABLES
											var returnHTML = 	"<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" +
																	"<table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\">";
											
											if ( varData.data_subtype_name != undefined && varData.data_subtype_name.length > 0 ) {
												returnHTML +=			"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.type + ":</td>" +
																			"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" + varData.data_subtype_name + "</td>" +
																		"</tr>";
											}
											// EDIT: HTML layout
											if ( _this.data.mode == "edit" ) {
												
												// EDIT HTML: TYPE: Date
												if ( _this.data_subtype_id == 600 ) {

													// Edit: Date: Format
													returnHTML +=		"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.format + ":</td>" +
																			"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																				"<select id=\"dateFormatSelect\" class=\"input100\" style=\"width: 150px;\">";
													for (var i=0; i < _this.data.dateFormats.length; i++)
														returnHTML +=				"<option value=\"" + _this.data.dateFormats[i] + "\">" + _this.data.dateFormats[i] + "</option>";
													returnHTML +=				"</select>" +
																			"</td>" +
																		"</tr>";
													
													// Edit: Date: Delta
													returnHTML +=		"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.delta + ":</td>" +
																			"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																				"<table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"><tr>" +
																					"<td width=\"1%\" style=\"vertical-align: middle; padding: 2px;\">" +
																						"<select id=\"dateDeltaOperator\" class=\"input35\" style=\"width: 70px;\">" +
																							"<option value=\"+\">+</option>" +
																							"<option value=\"-\">-</option>" +
																						"</select>" +
																					"</td>" +
																					"<td width=\"1%\" style=\"vertical-align: middle; padding: 2px;\">" +
																						"<input id=\"dateDeltaValue\" class=\"input2digit\" maxlength=\"3\">" +
																					"</td>" +
																					"<td align=\"left\" style=\"vertical-align: middle; padding: 2px;\">" +
																						"<select id=\"dateDeltaPeriod\" class=\"input185\" style=\"width: 105px;\">";
													for (var i=0; i < _this.data.dateDeltaTypes.length; i++)
														returnHTML +=						"<option value=\"" + _this.data.dateDeltaTypes[i].id + "\">" + _this.data.dateDeltaTypes[i].value + "</option>";
													returnHTML +=						"</select>" +
																					"</td>" +
																				"</tr></table>" +
																			"</td>" +
																		"</tr>";

													//'<div>The format of this variable occurence may optionally be customized by selecting a value from the menu below.</div>');

												}
												
												// EDIT HTML: TYPE: String or Date
												if ( _this.data_subtype_id == 100 || _this.data_subtype_id == 600 ) {
													
													// Edit: String: Case
													returnHTML +=		"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.letter_case + ":</td>" +
																			"<td  align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																				"<select id=\"caseFormatSelect\" class=\"input185\" style=\"width: 225px;\">";
													for (var i=0; i < _this.data.stringCaseFormats.length; i++)
														returnHTML +=				"<option value=\"" + _this.data.stringCaseFormats[i].id + "\">" + _this.data.stringCaseFormats[i].value + "</option>";
													returnHTML +=				"</select>" +
																			"</td>" +
																		"</tr>";
													
												}

												// EDIT HTML: TYPE: String
												if ( _this.data_subtype_id == 100 ) {

													// Edit: String: Trim
													returnHTML +=		"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.trim_on_empty + ":&nbsp;&nbsp;<div class=\"infoIconDiv\" infoTag=\"trim\" style=\"display: inline-block; position: relative; top: 2px;\"></div></td>" +
																			"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																				"<select id=\"trimTypeSelect\" class=\"input185\">";
													for (var i=0; i < _this.data.stringTrimTypes.length; i++)
														returnHTML +=				"<option value=\"" + _this.data.stringTrimTypes[i].id + "\">" + _this.data.stringTrimTypes[i].value + "</option>";
													returnHTML +=				"</select>" +
																			"</td>" +
																		"</tr>";
													
													// Edit: String: Make possessive
													returnHTML +=		"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.make + " <span style=\"white-space: nowrap;\">" + client_messages.text.possessive + ":&nbsp;<div class=\"infoIconDiv\" infoTag=\"possessive\" style=\"display: inline-block; position: relative; top: 2px;\"></div></span></td>" +
																			"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																				"<select id=\"makePossessiveSelect\" class=\"input185\">" +
																					"<option value=\"false\">" + client_messages.text.no + "</option>" +
																					"<option value=\"true\">" + client_messages.text.yes + "</option>" +
																				"</select>" +
																			"</td>" +
																		"</tr>";
													
													
													// Smart text: Disqualify on no value
													if ( varData.is_compound_variable && _this.data.is_smart_text ) {
														returnHTML +=	"<tr id=\"disqualifySmartTextContainer\" class=\"nthElementItem\">" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.disqualify_on_no_ + " <span style=\"white-space: nowrap;\">" + client_messages.text._value + ":&nbsp;<div class=\"infoIconDiv\" infoTag=\"disqualify\" style=\"display: inline-block; position: relative; top: 2px;\"></div></span></td>" +
																				"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																					"<input type=\"checkbox\" class=\"checkbox\" id=\"disqualifySmartTextCheckbox\" />" +
																				"</td>" +
																		"</tr>";
													}

													returnHTML += "<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.substring + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																		"<div id=\"errorSubstring\" class=\"text-danger\"></div>" +
																		"</td>" +
																	"</tr>" +
																	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle pl-3\">" + client_messages.text.start_position + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																		"<input type=\"number\" min=\"1\" id=\"startPositionInput\" class=\"input10digit numeric mr-1\" filterId=\"startPositionInput\" />" +
																		$.alphanumeric.generateFilterTooltip("startPositionInput") +
																		"</td>" +
																	"</tr>" +
																	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle pl-3\">" + client_messages.text.substring_length + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																			"<input type=\"number\" min=\"1\" id=\"endPositionInput\" class=\"input10digit numeric mr-1\" filterId=\"endPositionInput\" />" +
																			$.alphanumeric.generateFilterTooltip("endPositionInput") +
																		"</td>" +
																	"</tr>" +
																	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\"></td>" +
																		"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																			"<input type=\"checkbox\" class=\"checkbox\" id=\"endOfStringCheckbox\" />" + client_messages.text.end_of_string +
																		"</td>" +
																	"</tr>";
													
												}
												
												// Edit: String: Select n-th element
												if ( varData.is_compound_variable ) {
													returnHTML +=		"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.select_nth_element + ":</td>" +
																			"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																				"<input type=\"checkbox\" class=\"checkbox\" id=\"selectNthElementCheckbox\" />" +
																			"</td>" +
																		"</tr>" +
																		"<tr id=\"elementContainer\" class=\"nthElementItem\">" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.element + ":</td>" +
																			"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																				"<input id=\"elementInput\" class=\"input2digit numeric\" maxlength=\"2\" filterId=\"elementInput\" />" +
																				$.alphanumeric.generateFilterTooltip("elementInput") +
																			"</td>" +
																		"</tr>";
												}
												
												// EDIT HTML: TYPE: Currency
												if ( _this.data_subtype_id == 400 ) {
													
													// Edit: Currency: Currency symbol
													returnHTML +=		"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.currency_symbol + ":</td>" +
																			"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																				"<input id=\"currencySymbolInput\" class=\"input2digit\" maxlength=\"2\" />" +
																			"</td>" +
																		"</tr>";
												}
												
												// EDIT HTML: TYPE: Decimal numbers	
												if ( 	_this.data_subtype_id == 400 ||
														_this.data_subtype_id == 401 ||
														_this.data_subtype_id == 402 ||
														_this.data_subtype_id == 499 ) {	
													
													// Edit: Decimal numbers: Number of decimals
													returnHTML +=		"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.decimal_places + ":</td>" +
																			"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																				"<input id=\"decimalsInput\" class=\"input1digit\" maxlength=\"1\" />" +
																			"</td>" +
																		"</tr>";
													
													// Edit: Decimal numbers: Drop decimals for round numbers
													returnHTML +=		"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\" style=\"white-space: normal;\">" + client_messages.text.drop_decimals_for_round_numbers + ":</td>" +
																			"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																				"<input type=\"checkbox\" class=\"checkbox\" id=\"dropDecimalsCheckbox\" />" +
																			"</td>" +
																		"</tr>";
												}
												
												// EDIT HTML: TYPE: Number	
												if ( 	_this.data_subtype_id == 200 ||
														_this.data_subtype_id == 201 ||
														_this.data_subtype_id == 202 ||
														_this.data_subtype_id == 203 ||
														_this.data_subtype_id == 400 ||
														_this.data_subtype_id == 401 ||
														_this.data_subtype_id == 402 ||
														_this.data_subtype_id == 499 ) {
													
													// Edit: Numbers: Thousands separator
													returnHTML +=		"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.thousands_separator + ":</td>" +
																			"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																				"<input id=\"thousandsSeparatorInput\" class=\"input1digit\" maxlength=\"1\" />" +
																			"</td>" +
																		"</tr>";
													
												}

												if ( 	_this.data_subtype_id == 200 ||
														_this.data_subtype_id == 202 ||
														_this.data_subtype_id == 400 ||
														_this.data_subtype_id == 401 ||
														_this.data_subtype_id == 402 ||
														_this.data_subtype_id == 499 ) {

													// Edit: Numbers: Format negative values
													returnHTML += "<tr>" +
																	"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.format_negative_values + ":</td>" +
																	"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																		"<select id=\"negativeValueFormat\" style=\"width: 100px\">";

													var minusOption = _this.data_subtype_id !== 400 ? "-1234.10" : "-$1234.10";

													returnHTML += "<option value='minus'>" + minusOption + "</option>";

													if( _this.data_subtype_id !== 400 ) {
														returnHTML += 	"<option value='paren'>(1234.10)</option>";
													} else {
														returnHTML += 	"<option value=\"paren_inside\">($1234.10)</option>" +
																		"<option value=\"paren_outside\">$(1234.10)</option>";
													}
													returnHTML += "</select>" +
																"</td>" +
															   "</tr>";

												}

												
												if ( varData.is_compound_variable ) {
													// Edit: Insert as paragraph
													returnHTML +=			"<tr>" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.insert.as.paragraphs + ":</td>" +
																				"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																					"<input type=\"checkbox\" class=\"checkbox\" id=\"insertAsParagraphCheckbox\" />" +
																				"</td>" +
																			"</tr>" +
																			"<tr id=\"compoundValueInlineFirstValueContainer\" class=\"stringListItem\">" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.inline.first.value + ":</td>" +
																				"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																					"<input type=\"checkbox\" class=\"checkbox\" id=\"inlineFirstValueCheckbox\" />" +
																				"</td>" +
																			"</tr>" +
																			"<tr id=\"compoundValueDelimiterContainer\" class=\"stringListItem\">" +
																					"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.delimiter + ":</td>" +
																					"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																						"<input id=\"compoundValueDelimiterInput\" class=\"input2digit\" maxlength=\"2\" />" +
																					"</td>" +
																			"</tr>" +
																			"<tr id=\"compoundValueListEndDelimiterContainer\" class=\"stringListItem\">" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.list_end_delimiter + ":</td>" +
																				"<td  align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																					"<select id=\"compoundValueListEndDelimiterSelect\" class=\"input185\">";
													for (var i=0; i < _this.data.compoundFmtTypes.length; i++)
														returnHTML +=					"<option value=\"" + _this.data.compoundFmtTypes[i].id + "\">" + _this.data.compoundFmtTypes[i].value + "</option>";
													returnHTML +=					"</select>" +
																				"</td>" +
																			"</tr>";
													returnHTML +=			"<tr id=\"compoundValueContentTypeContainer\" class=\"stringListItem\">" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.content + ":</td>" +
																				"<td  align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																					"<select id=\"compoundValueContentTypeSelect\" class=\"input185\">";
													for (var i=0; i < _this.data.contentTypes.length; i++)
														returnHTML +=					"<option value=\"" + _this.data.contentTypes[i].id + "\">" + _this.data.contentTypes[i].value + "</option>";
													returnHTML +=					"</select>" +
																				"</td>" +
																			"</tr>";
												}
												
												// SYSTEM VARIABLES
												
												// Edit: Counter
												if ( varData.is_system_variable && (varData.system_type_id == 11 || varData.system_type_id == 12) ) {
													returnHTML +=			"<tr>" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.group_id + ":</td>" +
																				"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																					"<input id=\"groupIdInput\" class=\"inputS simpleName\" maxlength=\"100\" filterId=\"groupIdInput\" />" +
																					$.alphanumeric.generateFilterTooltip("groupIdInput") +
																				"</td>" +
																			"</tr>";
												}
												
												// Edit: Buttons
												returnHTML +=			"<tr>" +
																			"<td/>" +
																			"<td align=\"left\" style=\"vertical-align: middle; white-space: nowrap; padding: 2px; padding-top: 8px;\">" +
																				"<span style=\"display: inline-block;\"><input title=\"" + client_messages.button.ok_upper + "\" type=\"button\" id=\"varTagAttrSaveButton\" style=\"display: none;\" /></span>" +
																				"<span style=\"display: inline-block;\"><input title=\"" + client_messages.button.cancel_upper + "\" type=\"button\" id=\"varTagAttrCloseButton\" style=\"display: none;\" /></span>" +
																			"</td>" +
																		"</tr>";
												
											
											// VIEW: HTML layout 
											} else {
												
												// VIEW HTML: TYPE: Date
												if ( _this.data_subtype_id == 600 ) {

													// VIEW: Date: Format
													returnHTML +=	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.format + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">";
													if ( $(o.targetEle).attr('fmt') != undefined && $(o.targetEle).attr('fmt') != '' )
														returnHTML += 		$(o.targetEle).attr('fmt') + ($(o.targetEle).attr('fmt') == varData.default_date_format ? " (" + client_messages.text.default_state + ")" : "");
													else
														returnHTML += 		varData.default_date_format + " (" + client_messages.text.default_state + ")";
													returnHTML +=		"</td>" +
																	"</tr>";
													
													// VIEW: Date: Format
													var deltaValueStr = client_messages.content_editor.none;
													if ( $(o.targetEle).attr('datedelta') != undefined && $(o.targetEle).attr('datedelta') != '' ) {
														var dateDeltaValuesArray = $(o.targetEle).attr('datedelta').split(_this.data.date_value_separator_str);
														
														deltaValueStr = (dateDeltaValuesArray[0] == '+' ? client_messages.text.targeting.plus : client_messages.text.targeting.minus) + ' ';
														deltaValueStr += dateDeltaValuesArray[1] + " ";
														for ( var i=0; i < _this.data.dateDeltaTypes.length; i++)
															if ( _this.data.dateDeltaTypes[i].id == dateDeltaValuesArray[2] )
																deltaValueStr += _this.data.dateDeltaTypes[i].value;
													}
													returnHTML +=	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.delta + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																			deltaValueStr +
																		"</td>" +
																	"</tr>";
												}
												
												// VIEW HTML: TYPE: String or Date
												if ( _this.data_subtype_id == 100 || _this.data_subtype_id == 600 ) {
													
													// VIEW: String: Case
													var caseValue = _this.data.stringCaseFormats[0].id;
													if ( $(o.targetEle).attr('case') != undefined && $(o.targetEle).attr('case') != '' )
														caseValue = $(o.targetEle).attr('case');
													var caseDisplayValue = "";
													for ( var i=0; i < _this.data.stringCaseFormats.length; i++)
														if ( _this.data.stringCaseFormats[i].id == caseValue )
															caseDisplayValue += _this.data.stringCaseFormats[i].value;
													
													returnHTML +=	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.letter_case + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																			caseDisplayValue +
																		"</td>" +
																	"</tr>";
													
												}

												// VIEW HTML: TYPE: String
												if ( _this.data_subtype_id == 100 ) {
												
													// VIEW: String: Trim
													var trimValue = _this.data.stringTrimTypes[0].id;
													if ( $(o.targetEle).attr('trimonnull') != undefined && $(o.targetEle).attr('trimonnull') != '' )
														trimValue = $(o.targetEle).attr('trimonnull');
													var trimDisplayValue = "";
													for ( var i=0; i < _this.data.stringTrimTypes.length; i++)
														if ( _this.data.stringTrimTypes[i].id == trimValue )
															trimDisplayValue += _this.data.stringTrimTypes[i].value;
													
													returnHTML +=	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.trim_on_empty + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																			trimDisplayValue +
																		"</td>" +
																	"</tr>";
													
													// VIEW: String: Make possessive
													var applyPossessive = client_messages.text.no;
													if ( $(o.targetEle).attr('applypossessive') != undefined && $(o.targetEle).attr('applypossessive') != '' )
														applyPossessive = $(o.targetEle).attr('applypossessive') == "true" ? client_messages.text.yes : client_messages.text.no;
													
													returnHTML +=	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.make_possessive + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																			applyPossessive +
																		"</td>" +
																	"</tr>";
	
												}
													
												// VIEW HTML: TYPE: Currency
												if ( _this.data_subtype_id == 400 ) {
													
													// VIEW: String: Currency symbol
													var currencySymbol = varData.default_currency_symbol + " (" + client_messages.text.default_state + ")";
													if ( $(o.targetEle).attr('currencysymbol') != undefined && $(o.targetEle).attr('currencysymbol') != '' )
														currencySymbol = $(o.targetEle).attr('currencysymbol') + 
																		($(o.targetEle).attr('currencysymbol') == varData.default_currency_symbol ? " (" + client_messages.text.default_state + ")" : "");
													
													returnHTML +=	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.currency_symbol + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																			currencySymbol +
																		"</td>" +
																	"</tr>";
												}
												
												// VIEW HTML: TYPE: Decimal numbers	
												if ( 	_this.data_subtype_id == 400 ||
														_this.data_subtype_id == 401 ||
														_this.data_subtype_id == 402 ||
														_this.data_subtype_id == 499 ) {	
													
													// VIEW: Decimal numbers: Number of decimals
													var decimals = varData.default_decimal_places + " (" + client_messages.text.default_state + ")";
													if ( $(o.targetEle).attr('decimals') != undefined && $(o.targetEle).attr('decimals') != '' )
														decimals = 	$(o.targetEle).attr('decimals') + 
																	($(o.targetEle).attr('decimals') == varData.default_decimal_places ? " (" + client_messages.text.default_state + ")" : "");
													
													returnHTML +=	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.decimal_places + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																			decimals +
																		"</td>" +
																	"</tr>";
													
													// VIEW: Decimal numbers: Drop decimals for round numbers
													var dropDecimals = (varData.drop_decimals_for_round_numbers ? client_messages.text.yes : client_messages.text.no) + " (" + client_messages.text.default_state + ")";
													if ( $(o.targetEle).attr('drop_zero_decimals') != undefined && $(o.targetEle).attr('drop_zero_decimals') != '' )
														dropDecimals = 	($(o.targetEle).attr('drop_zero_decimals') == 'true' ? client_messages.text.yes : client_messages.text.no) + 
																	(($(o.targetEle).attr('drop_zero_decimals') == 'true') == varData.drop_decimals_for_round_numbers ? " (" + client_messages.text.default_state + ")" : "");
													
													returnHTML +=	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\" style=\"white-space: normal;\">" + client_messages.text.drop_decimals_for_round_numbers + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																			dropDecimals +
																		"</td>" +
																	"</tr>";
												}
												
												// VIEW HTML: TYPE: Number	
												if ( 	_this.data_subtype_id == 200 ||
														_this.data_subtype_id == 201 ||
														_this.data_subtype_id == 202 ||
														_this.data_subtype_id == 203 ||
														_this.data_subtype_id == 400 ||
														_this.data_subtype_id == 401 ||
														_this.data_subtype_id == 402 ||
														_this.data_subtype_id == 499 ) {
													
													// VIEW: Numbers: Thousands separator
													var thousandsSeparator = varData.default_thousands_separator + " (" + client_messages.text.default_state + ")";
													if ( $(o.targetEle).attr('thousandssep') != undefined )
														thousandsSeparator = 	$(o.targetEle).attr('thousandssep') + 
																				($(o.targetEle).attr('thousandssep') == varData.default_thousands_separator ? " (" + client_messages.text.default_state + ")" : "");
													
													returnHTML +=	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.thousands_separator + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																			thousandsSeparator +
																		"</td>" +
																	"</tr>";
													
												}
												
												if ( varData.is_compound_variable ) {

													// VIEW: String: N-th element
													var nthElement = null;
													var isNthElement = $(o.targetEle).attr('nthelement') != undefined && $(o.targetEle).attr('nthelement') != '';
													if ( isNthElement )
														nthElement = $(o.targetEle).attr('nthelement');
													else
														nthElement = client_messages.text.no;

													returnHTML +=	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.select_nth_element + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																			nthElement +
																		"</td>" +
																	"</tr>";
													
													if ( isNthElement ) {	
														// VIEW: String: Disqualify smart text
														if ( $(o.targetEle).attr('novaluedisqualify') != undefined && $(o.targetEle).attr('novaluedisqualify') != '' ) {
															var disqualifyIfNoValue = $(o.targetEle).attr('novaluedisqualify') == "true" ? client_messages.text.yes : client_messages.text.no;
														
															returnHTML +=	"<tr>" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.disqualify_on_no_value + ":</td>" +
																				"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																					disqualifyIfNoValue +
																				"</td>" +
																			"</tr>";
														}
													}
													
													// VIEW: All: Insert as paragraph
													var isInsertAsParagraph = false;
													if ( $(o.targetEle).attr('insert_as_paragraph') != undefined && $(o.targetEle).attr('insert_as_paragraph') != '' )
														isInsertAsParagraph = ( $(o.targetEle).attr('insert_as_paragraph') == "true" );
													
													returnHTML +=	"<tr>" +
																		"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.insert.as.paragraphs + ":</td>" +
																		"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																			( isInsertAsParagraph ? client_messages.text.yes : client_messages.text.no ) +
																		"</td>" +
																	"</tr>";
													
													if ( !isNthElement ) {
														
														if ( !isInsertAsParagraph ) {
	
															// VIEW: Delimiter
															var delimiterValue = '<i>None</i>';
															if ( $(o.targetEle).attr('delimiter') != undefined && $(o.targetEle).attr('delimiter') != '' )
																delimiterValue = $(o.targetEle).attr('delimiter');
															
															returnHTML +=	"<tr>" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.delimiter + ":</td>" +
																				"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																					delimiterValue +
																				"</td>" +
																			"</tr>";
			
															// VIEW: List End Delimiter
															var compoundFmtTypeValue = _this.data.compoundFmtTypes[0].id;
															if ( $(o.targetEle).attr('compound_fmt') != undefined && $(o.targetEle).attr('compound_fmt') != '' )
																compoundFmtTypeValue = $(o.targetEle).attr('compound_fmt');
															var compoundFmtTypeDisplayValue = "";
															for ( var i=0; i < _this.data.compoundFmtTypes.length; i++)
																if ( _this.data.compoundFmtTypes[i].id == compoundFmtTypeValue )
																	compoundFmtTypeDisplayValue += _this.data.compoundFmtTypes[i].value;
															
															returnHTML +=	"<tr>" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.list_end_delimiter + ":</td>" +
																				"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																					compoundFmtTypeDisplayValue +
																				"</td>" +
																			"</tr>";
	
														} else {
															
															// VIEW: Inline first value
															var inlineFirstValue = false;
															if ( $(o.targetEle).attr('inline_first') != undefined && $(o.targetEle).attr('inline_first') != '' )
																inlineFirstValue = ( $(o.targetEle).attr('inline_first') == "true" );
																
															returnHTML +=	"<tr>" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.inline.first.value + ":</td>" +
																				"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																					( inlineFirstValue ? client_messages.text.yes : client_messages.text.no ) +
																				"</td>" +
																			"</tr>";	
															
														}
		
														// VIEW: Content
														var contentTypeValue = _this.data.contentTypes[0].id;
														if ( $(o.targetEle).attr('include_type') != undefined && $(o.targetEle).attr('include_type') != '' )
															contentTypeValue = $(o.targetEle).attr('include_type');
														var contentTypeDisplayValue = "";
														for ( var i=0; i < _this.data.contentTypes.length; i++)
															if ( _this.data.contentTypes[i].id == contentTypeValue )
																contentTypeDisplayValue += _this.data.contentTypes[i].value;
														
														returnHTML +=	"<tr>" +
																			"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelTop\">" + client_messages.text.content + ":</td>" +
																			"<td align=\"left\" style=\"vertical-align: top; padding: 2px; padding-left: 8px;\">" +
																				contentTypeDisplayValue +
																			"</td>" +
																		"</tr>";
													} // END: If not nth element
													
												} // END: If compound variable
												
												// SYSTEM VARIABLES
												
												// View: Counter
												if ( varData.is_system_variable && (varData.system_type_id == 11 || varData.system_type_id == 12) ) {
													var groupId = "";
													if ( $(o.targetEle).attr('group_id') != undefined && $(o.targetEle).attr('group_id') != '' )
														groupId = $(o.targetEle).attr('group_id');
													else
														groupId = client_messages.text.default_state;
													
													returnHTML +=			"<tr>" +
																				"<td width=\"1%\" align=\"left\" class=\"tagAttrManagerLabelMiddle\">" + client_messages.text.group_id + ":</td>" +
																				"<td align=\"left\" style=\"vertical-align: middle; padding: 2px; padding-left: 8px;\">" +
																					groupId +
																				"</td>" +
																			"</tr>";
												}
												
												
											}

											returnHTML += 		"</table></div>";

											var returnObj = $(returnHTML);
											_this.attrDisplayContainer = returnObj;
											
											// EDIT: Init element script
											if ( _this.data.mode == "edit" ) {
												
												// EDIT: TYPE: Date
												if ( _this.data_subtype_id == 600 ) {

													// INIT Value: Date: Format
													var selectedFmt = null;
													if ( $(o.targetEle).attr('fmt') != undefined && $(o.targetEle).attr('fmt') != '' )
														selectedFmt = $(o.targetEle).attr('fmt');
													else
														selectedFmt = varData.default_date_format;
													$(returnObj).find("#dateFormatSelect option[value='" + selectedFmt + "']").attr('selected','selected');
													
													// INIT Value: Date: Delta
													var appliedDeltaValuesArray = null;
													if ( $(o.targetEle).attr('datedelta') != undefined && $(o.targetEle).attr('datedelta') != '' )
														appliedDeltaValuesArray = $(o.targetEle).attr('datedelta').split(_this.data.date_value_separator_str);
													else
														appliedDeltaValuesArray = ['+',0,'d'];
													$(returnObj).find("#dateDeltaOperator option[value='" + appliedDeltaValuesArray[0] + "']").attr('selected','selected');
													$(returnObj).find('#dateDeltaValue').val( appliedDeltaValuesArray[1] );
													$(returnObj).find("#dateDeltaPeriod option[value='" + appliedDeltaValuesArray[2] + "']").attr('selected','selected');
												
												}
												
												// EDIT: TYPE: String or Date
												if ( _this.data_subtype_id == 100 || _this.data_subtype_id == 600 ) {
													
													// INIT Value: String: Case
													if ( $(o.targetEle).attr('case') != undefined && $(o.targetEle).attr('case') != '' )
														$(returnObj).find("#caseFormatSelect option[value='" + $(o.targetEle).attr('case') + "']").attr('selected','selected');
												
												}
													
												// EDIT: TYPE: String
												if ( _this.data_subtype_id == 100 ) {

													// INIT Value: String: Trim
													if ( $(o.targetEle).attr('trimonnull') != undefined && $(o.targetEle).attr('trimonnull') != '' )
														$(returnObj).find("#trimTypeSelect option[value='" + $(o.targetEle).attr('trimonnull') + "']").attr('selected','selected');	
													
													// INIT Value: String: Make possessive
													if ( $(o.targetEle).attr('applypossessive') != undefined && $(o.targetEle).attr('applypossessive') != '' )
														$(returnObj).find("#makePossessiveSelect option[value='" + $(o.targetEle).attr('applypossessive') + "']").attr('selected','selected');

													// INIT Value: String: Substring
													var substrAttr = $(o.targetEle).attr('substr_1based');
													if (substrAttr && substrAttr.split(",").length === 2) {
														var parts = substrAttr.split(",");
														var startPosition = parts[0];
														var endPosition = parts[1];

														// Handle the case where endPosition is '-1' (end of string)
														if (endPosition === '-1') {
															endPosition = '';
															$(returnObj).find("#endOfStringCheckbox").attr("checked", "checked");
														}

														$(returnObj).find("#startPositionInput").val(startPosition);
														$(returnObj).find("#endPositionInput").val(endPosition);
													}

													$(returnObj).find(".numeric").alphanumeric({numeric: true});
													$(returnObj).find("#endPositionInput").on('keyup change', function() {
														if ($(this).val() !== '') {
															$(returnObj).find("#endOfStringCheckbox").removeAttr("checked");
														}
													});

													// Clear endPositionInput if endOfStringCheckbox is checked
													$(returnObj).find("#endOfStringCheckbox").click(function() {
														if ($(this).is(":checked")) {
															$(returnObj).find("#endPositionInput").val('');
														}
													});


												}	
													
												// EDIT: TYPE: Currency
												if ( _this.data_subtype_id == 400 ) {
													
													// INIT Value: Currency: Currency symbol
													var currencySymbol = $("<div/>").html( varData.default_currency_symbol ).text();
													if ( $(o.targetEle).attr('currencysymbol') != undefined && $(o.targetEle).attr('currencysymbol') != '' )
														currencySymbol = $(o.targetEle).attr('currencysymbol');
													$(returnObj).find('#currencySymbolInput').val( currencySymbol );
												}
												
												// EDIT: TYPE: Decimal numbers	
												if ( 	_this.data_subtype_id == 400 ||
														_this.data_subtype_id == 401 ||
														_this.data_subtype_id == 402 ||
														_this.data_subtype_id == 499 ) {
													
													// INIT Value: Decimal numbers: Decimal places
													var decimals = varData.default_decimal_places;
													if ( $(o.targetEle).attr('decimals') != undefined && $(o.targetEle).attr('decimals') != '' )
														decimals = $(o.targetEle).attr('decimals');
													$(returnObj).find('#decimalsInput').val( decimals );
													
													// INIT Value: Decimal numbers: Drop decimals for round numbers
													var dropDecimals = varData.drop_decimals_for_round_numbers;
													if ( $(o.targetEle).attr('drop_zero_decimals') != undefined && $(o.targetEle).attr('drop_zero_decimals') != '' )
														dropDecimals = $(o.targetEle).attr('drop_zero_decimals') == 'true';
													if ( dropDecimals )
														$(returnObj).find('#dropDecimalsCheckbox').attr('checked','checked');

												}
												
												// EDIT: TYPE: Number	
												if ( 	_this.data_subtype_id == 200 ||
														_this.data_subtype_id == 201 ||
														_this.data_subtype_id == 202 ||
														_this.data_subtype_id == 203 ||
														_this.data_subtype_id == 400 ||
														_this.data_subtype_id == 401 ||
														_this.data_subtype_id == 402 ||
														_this.data_subtype_id == 499 ) {
													
													// INIT Value: Numbers: Thousands separator
													var thousandsSeparator = $("<div/>").html( varData.default_thousands_separator ).text();
													if ( $(o.targetEle).attr('thousandssep') != undefined )
														thousandsSeparator = $(o.targetEle).attr('thousandssep');
													$(returnObj).find('#thousandsSeparatorInput').val( thousandsSeparator );

													// INIT Value: Numbers: Negative value format
													if ( $(o.targetEle).attr('negative_value') != undefined ) {
														var negativeValueFormat = $(o.targetEle).attr('negative_value');
														$(returnObj).find('#negativeValueFormat').val( negativeValueFormat );
													}
												}
												
												// EDIT: ALL: Insert as paragraph
												if ( varData.is_compound_variable ) {
													
													// TOGGLES
													var toggleInsertAsParagraph = function(isInit) {
														if ( !isInit || !$(returnObj).find('#selectNthElementCheckbox').is(':checked') ) {
															if ( !$(returnObj).find('#insertAsParagraphCheckbox').is(':checked') ) {
																$(returnObj).find('#compoundValueInlineFirstValueContainer').hide();
																$(returnObj).find('#compoundValueDelimiterContainer,#compoundValueListEndDelimiterContainer,#compoundValueContentTypeContainer').showEle('normal');
															} else {
																$(returnObj).find('.nthElementItem').hide();
																$(returnObj).find('#selectNthElementCheckbox').removeAttr('checked');
																$(returnObj).find('#compoundValueInlineFirstValueContainer,#compoundValueDelimiterContainer,#compoundValueListEndDelimiterContainer').hide();
																$(returnObj).find('#compoundValueInlineFirstValueContainer,#compoundValueContentTypeContainer').showEle('normal');
															}
														}
													};
													
													var toggleNthElement = function(isInit) {
														if ( !isInit || !$(returnObj).find('#insertAsParagraphCheckbox').is(':checked') ) {
															if ( !$(returnObj).find('#selectNthElementCheckbox').is(':checked') ) {
																$(returnObj).find('#disqualifySmartTextContainer,#elementContainer').hide();
																toggleInsertAsParagraph(false);
															} else {
																$(returnObj).find('.stringListItem').hide();
																$(returnObj).find('#insertAsParagraphCheckbox').removeAttr('checked');
																$(returnObj).find('#disqualifySmartTextContainer,#elementContainer').showEle('normal');
															}
														}
													};
													
													// LIST ITEMS
													var isInsertAsParagraph = false;
													if ( $(o.targetEle).attr('insert_as_paragraph') != undefined && $(o.targetEle).attr('insert_as_paragraph') != '' )
														isInsertAsParagraph = ( $(o.targetEle).attr('insert_as_paragraph') == "true" );

													if ( isInsertAsParagraph )
														$(returnObj).find('#insertAsParagraphCheckbox').attr('checked','checked');
													toggleInsertAsParagraph(true);
													
													$(returnObj).find('#insertAsParagraphCheckbox').click( function() {
														toggleInsertAsParagraph(false);
													});
													
													// INIT Value: Inline first value
													var inlineFirstValue = false;
													if ( $(o.targetEle).attr('inline_first') != undefined && $(o.targetEle).attr('inline_first') != '' )
														inlineFirstValue = ( $(o.targetEle).attr('inline_first') == "true" );
													if ( inlineFirstValue )
														$(returnObj).find('#inlineFirstValueCheckbox').attr('checked','checked');
													
													// INIT Value: Delimiter
													if ( $(o.targetEle).attr('delimiter') != undefined && $(o.targetEle).attr('delimiter') != '' )
														$(returnObj).find('#compoundValueDelimiterInput').val( $(o.targetEle).attr('delimiter') );
													
													// INIT Value: List End Delimiter
													if ( $(o.targetEle).attr('compound_fmt') != undefined && $(o.targetEle).attr('compound_fmt') != '' )
														$(returnObj).find("#compoundValueListEndDelimiterSelect option[value='" + $(o.targetEle).attr('compound_fmt') + "']").attr('selected','selected');
													
													// INIT Value: Content
													if ( $(o.targetEle).attr('include_type') != undefined && $(o.targetEle).attr('include_type') != '' )
														$(returnObj).find("#compoundValueContentTypeSelect option[value='" + $(o.targetEle).attr('include_type') + "']").attr('selected','selected');
													
													// END - LIST ITEMS
													
													// N-TH ELEMENT
													
													// INIT Value: String: N-th element
													var isSelectNthElement = false;
													if ( $(o.targetEle).attr('nthelement') != undefined && $(o.targetEle).attr('nthelement') != '' ) {
														isSelectNthElement = true;
														$(returnObj).find('#elementInput').val( $(o.targetEle).attr('nthelement') );
													}

													if ( isSelectNthElement ) {
														$(returnObj).find('#selectNthElementCheckbox').attr('checked','checked');
													
														if ( _this.data.is_smart_text ) {
															// INIT Value: String: Disqualify smart text
															var disqualifySmartText = false;
															if ( $(o.targetEle).attr('novaluedisqualify') != undefined && $(o.targetEle).attr('novaluedisqualify') != '' )
																disqualifySmartText = $(o.targetEle).attr('novaluedisqualify') == "true";
															
															if ( disqualifySmartText )
																$(returnObj).find('#disqualifySmartTextCheckbox').attr('checked','checked');
														}
													}
													toggleNthElement(true);
													
													$(returnObj).find('#selectNthElementCheckbox').click( function() {
														toggleNthElement(false);
													});
													
													// END - N-TH ELEMENT
													
													$(returnObj).find('.numeric').alphanumeric({numeric: true});

												}
												
												// SYSTEM VARIABLES
												
												// EDIT: Counter
												if ( varData.is_system_variable && (varData.system_type_id == 11 || varData.system_type_id == 12) ) {
													
													if ( $(o.targetEle).attr('group_id') != undefined && $(o.targetEle).attr('group_id') != '' ) {
														$(returnObj).find('#groupIdInput').val( $(o.targetEle).attr('group_id') );
													}

												}
												
												$(returnObj).find('#varTagAttrSaveButton').click( function() {
													if (_this.validateSubStringInputValues()) {
														_this.setVarAttr();
													}
												});
												$(returnObj).find('#varTagAttrCloseButton').click( function() {
													_this.popupWidget.closePopup();
												});

												$(returnObj).find('#varTagAttrSaveButton,#varTagAttrCloseButton').styleActionElement();
												
												// INPUT FOCUS: First visible input
												var inputs = $(returnObj).find('input:visible:first,textarea:visible:first,select:visible:first,a:visible:first');
												for (var j=0; j < inputs.length; j++) {
													$(inputs[j]).focus();
													break;
												}
												
												$(returnObj).find('.infoIconDiv').each( function() {
													$(this).attr('title',_this.data.infoTxt[$(this).attr('infoTag')]);
												});
												
											}

											return $(returnObj);

										  }
			});

			var count = 0;
			function waitForTinymce() {
				if (typeof window.parent.tinymce !== "undefined" && typeof window.parent.tinymce.activeEditor !== "undefined" ) {

					// Hide popup on editor scroll
					$(window.parent.tinymce.activeEditor.getBody()).parent().on("mousewheel DOMMouseScroll popupFactory.scroll", function () {
						$('.popupFactory_popupContainer').each(function () {
							$(this).remove();
						});
					});
				} else if (count < 50) {
					count++;
					setTimeout(waitForTinymce, 250);
				}
			};

			waitForTinymce();
		},

		validateSubStringInputValues: function() {
			var _this = this;
			var isValid = true;
			var startPosition = _this.attrDisplayContainer.find("#startPositionInput").val();
			var endPosition = _this.attrDisplayContainer.find("#endOfStringCheckbox").is(":checked") ? "-1" : _this.attrDisplayContainer.find("#endPositionInput").val();
			var errorContainer = _this.attrDisplayContainer.find('#errorSubstring');

			function showError(message) {
				isValid = false;
				errorContainer.text(message).show();
			}

			if (startPosition && !endPosition) {
				showError(client_messages.text.substring_length_required);
			} else if (!startPosition && endPosition) {
				showError(client_messages.text.start_position_required);
			} else if (startPosition && parseInt(startPosition, 10) === 0) {
				showError(client_messages.text.start_position_must_be_greater_than_zero);
			} else if (endPosition && parseInt(endPosition, 10) === 0) {
				showError(client_messages.text.substring_length_must_be_greater_than_zero);
			} else {
				errorContainer.text('').hide();
			}

			return isValid;
		},
		setVarAttr : function() {
			var _this = this;
			
			if ( _this.data.mode == "edit" ) {
				
				$(_this.targetEle).removeAttr('datedelta case trimonnull applypossessive currencysymbol decimals thousandssep negative_value insert_as_paragraph delimiter compound_fmt include_type inline_first');
				
				// DATE
				if ( _this.data_subtype_id == 600 ) {
					
					// SET Value: Date: Format
					var selectedFormat = _this.attrDisplayContainer.find('#dateFormatSelect option:selected').val();
					$(_this.targetEle).attr('fmt',selectedFormat);
					
					// SET Value: Date: Delta
					var appliedDeltaValuesArray = [ _this.attrDisplayContainer.find("#dateDeltaOperator option:selected").val(),
						    						_this.attrDisplayContainer.find('#dateDeltaValue').val(),
						    						_this.attrDisplayContainer.find("#dateDeltaPeriod option:selected").val()
					                               ];
					$(_this.targetEle).attr('datedelta',appliedDeltaValuesArray.join(_this.data.date_value_separator_str));	

				}
				
				// STRING or DATE
				if ( _this.data_subtype_id == 600 || _this.data_subtype_id == 100) {
					
					// SET Value: String: Case
					var selectedCase = _this.attrDisplayContainer.find('#caseFormatSelect option:selected').val();
					$(_this.targetEle).attr('case',selectedCase);
					
				}
					
				// STRING
				if ( _this.data_subtype_id == 100 ) {

					// SET Value: String: Trim
					var selectedTrim = _this.attrDisplayContainer.find('#trimTypeSelect option:selected').val();
					$(_this.targetEle).attr('trimonnull',selectedTrim);
					
					// SET Value: String: Make Possessive
					var applyPossessive = _this.attrDisplayContainer.find('#makePossessiveSelect option:selected').val();
					$(_this.targetEle).attr('applypossessive',applyPossessive);

					// SET Value: String: Substring
					if (_this.attrDisplayContainer.find('#startPositionInput').val()) {
						var startPosition = _this.attrDisplayContainer.find('#startPositionInput').val();
						var endPosition = _this.attrDisplayContainer.find('#endPositionInput').val() ? _this.attrDisplayContainer.find('#endPositionInput').val() : '-1';
						$(_this.targetEle).attr('substr_1based',startPosition+","+endPosition);
					} else {
						$(_this.targetEle).removeAttr('substr_1based');
					}

				}
				
				// ALL: Nth-element
				if ( _this.attrDisplayContainer.find('#selectNthElementCheckbox').length != 0 ) {
					var isNthElement = _this.attrDisplayContainer.find('#selectNthElementCheckbox').is(':checked');
					if ( isNthElement ) {
						$(_this.targetEle).attr('nthelement', _this.attrDisplayContainer.find('#elementInput').val());
						if ( _this.data.is_smart_text ) {
							var isNoValueDisqualify = _this.attrDisplayContainer.find('#disqualifySmartTextCheckbox').is(':checked');
							$(_this.targetEle).attr('novaluedisqualify',isNoValueDisqualify);
						}
					} else {
						$(_this.targetEle).removeAttr('nthelement');
					}
				}
				
				// CURRENCY
				if ( _this.data_subtype_id == 400 ) {
					
					// SET Value: Currency: Currency symbol
					var currencySymbol = _this.attrDisplayContainer.find('#currencySymbolInput').val();
					$(_this.targetEle).attr('currencysymbol',currencySymbol);
				}
				
				// DECIMAL NUMBERS
				if ( 	_this.data_subtype_id == 400 ||
						_this.data_subtype_id == 401 ||
						_this.data_subtype_id == 402 ||
						_this.data_subtype_id == 499 ) {
					
					// SET Value: Decimal numbers: Decimal places
					var decimals = _this.attrDisplayContainer.find('#decimalsInput').val();
					$(_this.targetEle).attr('decimals',decimals);
					
					var dropDecimals = _this.attrDisplayContainer.find('#dropDecimalsCheckbox').is(':checked');
					$(_this.targetEle).attr('drop_zero_decimals', dropDecimals);
				}
					
				// NUMBER	
				if ( 	_this.data_subtype_id == 200 ||
						_this.data_subtype_id == 201 ||
						_this.data_subtype_id == 202 ||
						_this.data_subtype_id == 203 ||
						_this.data_subtype_id == 400 ||
						_this.data_subtype_id == 401 ||
						_this.data_subtype_id == 402 ||
						_this.data_subtype_id == 499 ) {
					
					// SET Value: Numbers: Thousands separator
					var thousandsSeparator = _this.attrDisplayContainer.find('#thousandsSeparatorInput').val();
					$(_this.targetEle).attr('thousandssep',thousandsSeparator);

					// SET Value: Numbers: Negative number format
					var thousandsSeparator = _this.attrDisplayContainer.find('#negativeValueFormat').val();
					$(_this.targetEle).attr('negative_value',thousandsSeparator);
					
				}
				
				// ALL: Set Value: Insert as paragraph
				if ( _this.attrDisplayContainer.find('#insertAsParagraphCheckbox').length != 0 ) {
					var isInsertAsParagraph = _this.attrDisplayContainer.find('#insertAsParagraphCheckbox').is(':checked');
					$(_this.targetEle).attr('insert_as_paragraph', isInsertAsParagraph);
					
					if ( !isInsertAsParagraph ) {
						// SET Value: Delimiter
						var delimiterValue = _this.attrDisplayContainer.find('#compoundValueDelimiterInput').val();
						if(delimiterValue == ""){
							$(_this.targetEle).removeAttr('delimiter');
						} else {
							$(_this.targetEle).attr('delimiter', delimiterValue);
						}
						
						// SET Value: End List Delimiter
						var endListDelimiterValue = _this.attrDisplayContainer.find('#compoundValueListEndDelimiterSelect option:selected').val();
						$(_this.targetEle).attr('compound_fmt', endListDelimiterValue);
					} else {
						// SET Value: Inline first value
						var inlineFirstValue = _this.attrDisplayContainer.find('#inlineFirstValueCheckbox').is(':checked');
						$(_this.targetEle).attr('inline_first', inlineFirstValue);
					}
					
					// SET Value: Content
					var contentTypeValue = _this.attrDisplayContainer.find('#compoundValueContentTypeSelect option:selected').val();
					$(_this.targetEle).attr('include_type', contentTypeValue);
				}
				
				// SYSTEM VARIABLES
				
				// Counter: Set group ID
				if ( _this.system_type_id == 11 || _this.system_type_id == 12 ) {
					var groupId = _this.attrDisplayContainer.find('#groupIdInput').val();
					$(_this.targetEle).attr('group_id', groupId);
				}
			}
			
			$(_this.targetEle).click();
		
			_this.popupWidget.closePopup();
		}

		}; // end component
	};
	
	// instance manager
	varTagAttrManager_component.inst = {};
	
})(jQuery);	


function applyVariableFormatting(o, defaults, el) {
	
	function toTitleCase(str) {
	    return str.replace(/\w\S*/g, function(txt){
	        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
	    });
	}

	var values = o.value.split('#@#');
	
	for (var i = 0; i < values.length; i++ ) {
		
		var v = values[i];
	
		// DATES
		if ( $(el).is('[fmt]') || $(el).is('[datedelta]') || o.sub_type_id == 600 ) {
			var m = moment(v,"YYYY/MM/DD");
			if ( $(el).is('[datedelta]') ) {
				var delta = $(el).attr('datedelta').split('@#@');
				var p;
				switch ( delta[2] ) {
					case "d":
						p = "days";
						break;
					case "m":
						p = "months";
						break;
					case "y":
						p = "years";
						break;
				}
				
				if ( delta[0] == '+' )
					m.add(parseFloat(delta[1]), p)
				else if ( delta[0] == '-' )
					m.subtract(parseFloat(delta[1]), p)
				
			}

			var fmtOut = $(el).is('[fmt]') ? $(el).attr('fmt') : defaults.default_date_format;

			if ( fmtOut.indexOf('MM') != -1 )
				fmtOut = fmtOut.replace('MM','MMMM');
			else if ( fmtOut.indexOf('M') != -1 )
				fmtOut = fmtOut.replace('M','MMM');
			
			fmtOut = fmtOut.replace(/y/g,'Y').replace(/m/g,'M').replace(/d/g,'D');

			var dayOfMonth = m.date();

			v = m.format(fmtOut);
			
			var dateNValues = ["first", "second", "third", "forth", "fifth", "sixth", "seventh", "eighth", "nineth", "tenth",
			        		   "eleventh", "twelfth", "thirteenth", "fourteenth", "fifteenth", "sixteenth", "seventeenth", "eighteenth",
			        		   "nineteenth", "twentieth", "twenty-first", "twenty-second", "twenty-third", "twenty-fourth", "twenty-fifth", "twenty-sixth",
			        		   "twenty-seventh", "twenty-eighth", "twenty-nineth",  "thirtieth", "thirty-first"];
			if ( fmtOut.indexOf('N') != -1 ) 
				fmtOut.replace('N',dateNValues[dayOfMonth]);

		}
		
		if ( $(el).is('[case]') ) {
			switch ( $(el).attr('case') ) {
				case "upper":
					v = v.toUpperCase();
					break;
				case "lower":
					v = v.toLowerCase();
					break;
				case "firstcap":
					v = v && v.charAt(0).toUpperCase() + v.slice(1);
					break;
				case "initcap":
					v = toTitleCase(v);
					break;
			}
		}
		
//		Attribute trims space outside of VAR tag not the value itself; needs to be conducted post value insert
//		if ( $(el).is('[trimonnull]') ) {
//			switch ( $(el).attr('case') ) {
//				case "leading":
//					v = v.replace(/^\s+/g, "");
//					break;
//				case "trailing":
//					v = v.replace(/\s+$/g, "");
//					break;
//				case "all":
//					v = v.replace(/^\s+|\s+$/g, "");
//					break;
//	
//			}
//		}
		
		//Make this variable possessive by automatically adding an apostrophe and s or 
		//apostrophe when a noun ends in s (i.e. David's car or Jess' car). David David's Jess Jess'
		if ( $(el).is('[applypossessive]') && $(el).attr('applypossessive') == 'true' ) {
			if ( v.charAt(v.length-1) == 's' )
				v += "'";
			else
				v = v + "'s";
		}
		
		// CURRENCY/NUMBERS
		var numberTypes = [200,201,202,203,400,401,402,499];
		if ( $(el).is('[currencysymbol]') || $(el).is('[decimals]') || $(el).is('[drop_zero_decimals]') || $(el).is('[thousandssep]') || numberTypes.indexOf(o.sub_type_id) != -1 ) {

			var cursym = defaults.default_currency_symbol,
				decimals = defaults.default_decimal_places > 0 ? '0'.repeat(defaults.default_decimal_places) : "",
				tsep = defaults.default_thousands_separator,
				decsym=".";
			
			if ( $(el).is('[currencysymbol]') ) 
				cursym = $(el).attr('currencysymbol');


			if ( $(el).is('[decimals]') && parseInt($(el).attr('decimals')) > 0 ) {
				decimals = '';
				for (j = 0; j < parseInt($(el).attr('decimals')); j++)
					decimals += '0';
			}
			if ( $(el).is('[thousandssep]') )
				tsep = $(el).attr('thousandssep');
			
			var nummask = (tsep.length == 0 ? '0' : '#,##0');
			if ( [400,401,402,499].indexOf(o.sub_type_id) != -1 )
				nummask = (tsep.length == 0 ? '0' : '#,##0') + decsym + decimals;

			v = format(nummask, v.replace(/[^\d.-]/g, ''));
			v = v.replace(',',tsep);

			if ( o.sub_type_id === 400 )
				v = cursym + v;

			if ( [400,401,402,499].indexOf(o.sub_type_id) != -1 ) {
				if ((defaults.drop_decimals_for_round_numbers === true || $(el).is('[drop_zero_decimals]')) && v.split('.').length == 2) {
					if (parseInt(v.split('.')[1]) == 0)
						v = v.split('.')[0];
				}
			}

		}
		
		values[i] = v;
	
	}

	if ( $(el).is('[nthelement]') )
		return values[ parseInt($(el).attr('nthelement') - 1 ) ];
	
	if ( $(el).is('[include_type]') ) {
		var trimmedValues = new Array();
		for ( var j = 0; j < values.length; j++ ) {
			switch ( $(el).attr('include_type') ) {
				case "1":
					// all
					trimmedValues[trimmedValues.length] = values[j];
					break;
				case "2":
					// distinct
					if ( trimmedValues.indexOf(values[j]) == -1 )
						trimmedValues[trimmedValues.length] = values[j];
					break;
				case "3":
					// non blank
					if ( values[j].length != 0 )
						trimmedValues[trimmedValues.length] = values[j];
					break;
				case "4":
					// first non blank
					if ( trimmedValues.length == 0 && values[j].length != 0 )
						trimmedValues[trimmedValues.length] = values[j];
					break;
				case "5":
					// not empty or duplicate
					if ( trimmedValues.indexOf(values[j]) == -1 && values[j].length != 0 )
						trimmedValues[trimmedValues.length] = values[j];
					break;
			}
		}
		values = trimmedValues;
	}
	
	
	if ( $(el).is('[insert_as_paragraph]') ) {
	
		return $(el).is('[inline_first]') ?  values.join('<p>') : '<p>' + values.join('<p>');
		
	} else {

		var compFmt = "";
		if ( $(el).is('[compound_fmt]') ) {
			switch ( $(el).attr('compound_fmt') ) {
				case '1':
					compFmt = client_messages.text.and;
					break;
				case '2':
					compFmt = client_messages.text.or;
					break;
			}
			if ( values.length > 1 )
				values[values.length-1] = compFmt + " " + values[values.length-1];
		}
		
		var del = $(el).is('[delimiter]') ? $(el).attr('delimiter') : ", ";
		if ( values.length == 2 && compFmt != "" )
			return values.join();
		else
			return values.join(del);
	
	}
	
}