/** editorActions r1.mce3 // 2009.06.29 // jQuery 1.3 // Prinova
 * 
 * This plugin interacts with the insertvariable tinyMCE plugin to provide the following
 * functionality:
 * 		- Atomic variable interaction (no editing or partial selection)
 * 		- Mouseover popups indicating variable properties
 * 		- Popups on double-click action for specified variable types
 * 
 * Dependencies:
 * jQuery 1.3 or later
 * tinyMCE 3.2 or later
 * insertvariable: tinyMCE Plugin
 * disable.text.select: jQuery Plugin
 * hoverIntent: jQuery Plugin
 * 
 */

var actionLock = false;
var deleteAction = false;
var varTagAttr_popupLock = false;

(function($) {

	$.fn.editorAction = function(p) {
		var editorInst = tinyMCE.get(p.editorId);
		
		p = $.extend({
			style_select: null
		  }, p);

		// ***************************************
		// *** START FEATURE: ATOMIC VARIABLES ***
		// ***************************************

		var setVarPopupListener = function(varEle) {

			if ( $(varEle).attr('type') == "2" || $(varEle).attr('type') == "3" ) {
				$(varEle).popupFactory({
							title					: client_messages.title.whats_this,
							offsetWindow			: editorInst.contentWindow,
							pollForRemove			: true,
							fnSetContent			: function(o) {
														return "<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\"><b><i>" + client_messages.text.constant + "</i></b></div>";
													  }
				});
			}

		};
		
		// !IMPORTANT!  This function is called repetitively.  Minimize executed code.
		var pollContent = function(editor) {
			// Init any variables not yet designated as an 'actionVariable'
			if ( $(editor).is(':visible') ) {
				$(editor).find('var,.staticContentItem').each(function() {
					if ( ($(this).attr('type') == '4' || $(this).attr('type') == '5') && !$(this).is('.embedded_content_tag') )
						$(this).addClass('embedded_content_tag');
					if ( !$(this).is('.mceNonEditable') )
						$(this).addClass('mceNonEditable');
					if ( !$(this).is('.staticContentItem') )
						$(this).addClass('staticContentItem');
					if ( !$(this).attr('contenteditable') ) {
						editorInst.dom.setAttrib( $(this).get(0), 'contenteditable', false);
						editorInst.dom.setAttrib( $(this).get(0), 'data-mce-contenteditable', false);
					}
					
					if (!$(this).hasClass('actionVariable')) {
						$(this).addClass('actionVariable');
						setVarPopupListener(this);
					}

					if ($(this).position().left === 0 && $(this).width() < $(this).closest('#tinymce').width()) {
						$(this).addClass('leftPositioned');
					} else {
						$(this).removeClass('leftPositioned');
					}
				});

				$(editor).find('var:not(var *, .staticContentItem *), .staticContentItem:not(var *, .staticContentItem *)').each(function() {
					if ( $.isFunction($(this).fastNavigation) ) {
						var fastNavigationEnabled = localStorage.getItem("msgpt_click_through_enable") == "true";
						if (fastNavigationEnabled){
							if (!$(this).hasClass('fastNavigation')) {
								var fastEdit = new URL(window.location.href).searchParams.get('fastEdit');
								if (!fastEdit) {
									$(this).addClass('fastNavigation');
									$(this).fastNavigation();
								}
							}
						} else {
							$(this).unbind('mouseenter mouseleave');
							$(this).removeClass('fastNavigation');
						}
					}
				});
			}

			// Prevent re-init on fullscreen
			if ( $('.mce-fullscreen').length == 0 ) {
				// COMMUNICATIONS: Protect template content noneditable elements
				if ( $(editor).find("[template_fixed_content='true']").length > 0 && !isTemplateMonitorActive ) {
					contentStore = $(editorInst.getBody()).contents().clone();
					initTemplateMonitor();
				}
			}
		};
		
		var migrateTextStyles = function() {
			// Post 5.2 all text styles are applied to SPAN tags: Transform preexisting content accordingly
			$(editorInst.getDoc()).find('p,li,ul,ol').each( function() {
				if ( $(this).attr('class') != undefined && $(this).attr('class').length != 0 ) {
					var ele = this;
					if ( $(ele).is('ol') || $(ele).is('ul') ) {
						$(ele).find('li').each( function() {
							var spanEle = $('<span></span>').attr('class', $(ele).attr('class'));
							$(spanEle).append( $(this).html() );
							$(this).html( spanEle );
						});
					} else {
						var spanEle = $('<span></span>').attr('class', $(ele).attr('class'));
						$(spanEle).append( $(ele).html() );
						$(ele).html( spanEle );
					}
					$(ele).removeAttr('class');
				}
			});
		};
		
		// Custom style implementation for styling variables
		var variableStyleAction = function(o) {
			// Get selected variable
			var selectedVar = null;
			$(editorInst.getDoc()).find('.staticContentItem').each(function() {
				if ( $(this).hasClass('active') )
					selectedVar = $(this);
			});

			while ( $(selectedVar).parent().closest('.staticContentItem').length != 0 )
				selectedVar = $(selectedVar).parent();

			var selectedStyleText = $(o.target).text();
			var varParent = $(selectedVar).parent();
			var preStyled = false;
			if ( $(varParent).is('span') )
				preStyled = true;
			
			var styleItems = editorInst.buttons.styleselect.menu.items;
			var selectedStyleItem = null;
			for ( var i=0; i < styleItems.length; i++ ) {
				if ( styleItems[i].text == selectedStyleText )
					selectedStyleItem = styleItems[i];
			}
			if ( selectedStyleItem != null )
				p.style_select.value( selectedStyleItem.text );
			$('#'+p.style_select.rootControl._id).click();
			
			if ( !preStyled ) { // If variable not previously styled, wrap in style span
				$(selectedVar).wrap('<span class="' + $.trim(selectedStyleText) + '"></span>');
			} else { // Else modify existing style span with newly selected style
				$(selectedVar).parent().attr('class',$.trim(selectedStyleText));
			}

		};
		// *****************************************
		// *** END FEATURE: ATOMIC VARIABLES     ***
		// *****************************************
		
		// ********************************************
		// *** START FEATURE: UNEDITABLE TEMPLATES  ***
		// ********************************************
		var initTemplateMonitor = function() {

			// IE performance issue tied to listeners - try $(editorInst.getDoc()).keydown();
			editorInst.on('keydown', function(e) {

				if ( $(contentStore).find("[template_fixed_content='true']").length > 0 ) {
					var fixedContentCountBefore = $(contentStore).find("[template_fixed_content='true']").length;
					var fixedContentCountAfter = $(editorInst.getBody()).find("[template_fixed_content='true']").length;
					
					if (fixedContentCountBefore > fixedContentCountAfter && $(editorInst.getBody()).text().length != 0) {
						$(editorInst.getBody()).html(contentStore);
						e.preventDefault();
						return;
					}
				}
				
				contentStore = $(editorInst.getBody()).contents().clone();			
			});
			editorInst.on('keyup', function(e) {

				if ( $(contentStore).find("[template_fixed_content='true']").length > 0 ) {
					var fixedContentCountBefore = $(contentStore).find("[template_fixed_content='true']").length;
					var fixedContentCountAfter = $(editorInst.getBody()).find("[template_fixed_content='true']").length;

					if (fixedContentCountBefore > fixedContentCountAfter && $(editorInst.getBody()).text().length != 0)
						$(editorInst.getBody()).html(contentStore);
				}
				
			});
			editorInst.on('change', function(e) {

				$(editorInst.getBody()).find("[template_fixed_content='true']").each( function() {
					if ( !$(this).is('.mceNonEditable') )
						$(this).addClass('mceNonEditable');
				});

				if ( $(contentStore).find("[template_fixed_content='true']").length > 0 ) {
					var fixedContentCountBefore = $(contentStore).find("[template_fixed_content='true']").length;
					var fixedContentCountAfter = $(editorInst.getBody()).find("[template_fixed_content='true']").length;

					if (fixedContentCountBefore > fixedContentCountAfter && $(editorInst.getBody()).text().length != 0)
						$(editorInst.getBody()).html(contentStore);
				}
				
			});
			
			isTemplateMonitorActive = true;

		};
		
		// ********************************************
		// *** END FEATURE: UNEDITABLE TEMPLATES    ***
		// ********************************************
		
		// Init editor actions
		var contentStore;
		var isTemplateMonitorActive = false;
		return this.each ( function() {
			var editorBody = $(this);
			
			$('.mce-tinymce').css({border: 'none'});

			// Store style action function call: Used to inject custom styling for variables
			if ( editorInst.buttons.styleselect ) {
				var style_onPostRender = editorInst.buttons.styleselect.menu.onPostRender;
				editorInst.buttons.styleselect.menu.onPostRender = function() {
					style_onPostRender.apply(this,arguments);
					p.style_select = this;
					editorInst.execCommand("mceEnhanceMenu", false, this);

					for (var i=0; i < p.style_select._items.length; i++) {
						var currentFireEvent = p.style_select._items[i].fire;
						p.style_select._items[i].fire = function() {

							var applyCustomStyle = false;
							if ( arguments[0] == "click" )
								$(editorBody).find('.staticContentItem').each(function() {
									if ( $(this).is('.active') ) 
										applyCustomStyle = true;
								});
							
							if ( applyCustomStyle )
								variableStyleAction(arguments[1]);
							else
								currentFireEvent.apply(this,arguments);
						};
					}

				};
			}

			$(editorInst.getDoc()).find('head')
				.prepend("<script type=\"text/javascript\" src=\""+context+"/includes/javascript/jQueryPlugins/hoverIntent/jquery.hoverIntent.js\"></script>" +
						"<script language=\"javascript\" type=\"text/javascript\" src=\""+context+"/includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js\"></script>" +
						"<link id=\"css_fontawesome\" rel=\"stylesheet\" type=\"text/css\" href=\"" + tinymce_fontawesome_path + "\"></link>" +
						"<link id=\"css_content\" rel=\"stylesheet\" type=\"text/css\" href=\"" + tinymce_content_css_path + "\"></link>" +
						"<link id=\"css_content\" rel=\"stylesheet\" type=\"text/css\" href=\"" + tinymce_new_content_editor_path + "\"></link>" +
						"<link id=\"css_content\" rel=\"stylesheet\" type=\"text/css\" href=\"" + tinymce_content_edit_css_path + "\"></link>");

			// COMMUNICATIONS: Protect template content noneditable elements
			contentStore = $(editorInst.getBody()).contents().clone();
			if ( $(contentStore).find("[template_fixed_content='true']").length > 0 && !isTemplateMonitorActive )
				initTemplateMonitor();
			
			migrateTextStyles();

			$(editorBody).find('.staticContentItem').removeClass('actionVariable');
			// Poll for required actions every 500ms
			window.setInterval( function(){ pollContent(editorBody); }, 500 );

			// Making sure to set initial state after custom variables classes have been added
			setTimeout(function() {
				if(window.serializedObservableForm !== undefined) {
					serializedObservableForm.setInitialSerialization();
					serializedObservableForm.checkIfNeedSave();
				}
			}, 850);
			
		});
	};

})(jQuery);	