/** tagCloud r1 // 2013.05.30 // jQuery 1.8 // Prinova
 *
 * Dependencies:
 * jQuery 1.8 or later
 *
 */

(function ($) {

    $.tagCloud = {
        get: function (obj) {
            var o = $(obj);
            if (!o.size()) o = $("#" + obj);
            if (!o.size()) return null;
            return tagCloud_component.inst[o.attr('id')] || null;
        },
        defaults: {
            documentId: -1,
            rationalizerApplicationId: -1,
            zoneId: -1,
            tagCloudType: -1,
            inputType: 'metatags',
            searchTagPrefix: 'tag:',
            rightOffsetAdj: 18,
            topOffsetAdj: 12,
            popupLocation: "right",
            afterInputValueChange: null,
            beforeInputValueChange: null,
            offsetWindow: window,
            customCloudTags: {},
            zIndex: null
        }
    };

    $.fn.tagCloud = function (opts) {
        return this.each(function () {
            var conf = $.extend({}, opts);
            if (conf !== false) new tagCloud_component().init(this, conf);
        });
    };

    function tagCloud_component() {
        return {

            data: $.extend({}, $.tagCloud.defaults),

            init: function (elem, conf) {
                var _this = this;

                tagCloud_component.inst[$(elem).attr("id")] = _this;
                _this.data = $.extend(false, {}, this.data, conf);
                _this.targetEle = $(elem);

                if (!_this.targetEle.is(":visible"))
                    _this.waitForInputLoad();
                else
                    _this.initInput();

            },

            initInput: function () {
                var _this = this;

                // Wrap input in container: Required for cloud icon placement
                if (_this.targetEle.parent().css('display') == "inline-block" &&
                    _this.targetEle.parent().css('position') == "relative") {
                    _this.inputContainer = _this.targetEle.parent();
                } else {
                    var inputContainer = $("<div class=\"tagCloudInputContainer\"></div>");
                    _this.inputContainer = inputContainer;
                    _this.targetEle.before(_this.inputContainer);
                    $(_this.inputContainer).append(_this.targetEle);
                }

                // Build cloud icon
                $(_this.inputContainer).find('input, textarea')
                    .after("<div class=\"cloudIconDiv detailTip fa-mp-container\" title=\"|<div class='detailTipText'>" + client_messages.text.tag_cloud + "</div>\" style=\"display: inline-block; position: absolute; right: " + _this.data.rightOffsetAdj + "px; top: " + _this.data.topOffsetAdj + "px;\">" +
                        "<i class=\"far fa-cloud text-primary\" style=\"display: inline-block; position: absolute;\" aria-hidden=\"true\"></i>" +
                        "</div>");

                // Set input padding right to accommodate cloud icon
                var inputPaddingRight = parseInt(_this.targetEle.css(_this.targetEle.css("padding-right").replace('px', '')));
                if (inputPaddingRight < 25)
                    _this.targetEle.css({
                        'padding-right': '25px',
                        'width': (_this.targetEle.width() - (25 - inputPaddingRight)) + 'px'
                    });

                // Init item detail tool tip
                initTip($(_this.inputContainer).find('.cloudIconDiv '));

                $(_this.inputContainer).find('.cloudIconDiv').click(function () {
                    if (!_.isEmpty(_this.data.customCloudTags)) {
                        _this.processCloudTags(_this.data.customCloudTags);

                        return;
                    }

                    var contextAttr = "";
                    if (_this.data.rationalizerApplicationId > -1)
                        contextAttr = "rationalizerApplicationId=" + _this.data.rationalizerApplicationId;
                    else
                        contextAttr = "documentId=" + _this.data.documentId;

                    var stampDate = new Date();
                    $.ajax({
                        type: "GET",
                        url: context + "/getCloudTags.form?" + contextAttr + "&zoneId=" + _this.data.zoneId + "&tagCloudType=" + _this.data.tagCloudType + "&cacheStamp=" + (stampDate.getTime()),
                        dataType: "json",
                        success: function (data) {
                            _this.processCloudTags(data);
                        }
                    });
                });

            },

            processCloudTags: function (data) {
                var _this = this;

                function escapeRegExp(str) {
                    return str.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, "\\$&");
                }

                _this.targetEle.popupFactory({
                    title: data.cloudTypeLabel + " " + client_messages.title.tags,
                    popupLocation: _this.data.popupLocation,
                    trigger: "instant",
                    width: 235,
                    zIndex: _this.data.zIndex,
                    stopPropagation: true,
                    offsetWindow: _this.data.offsetWindow,
                    fnSetContent: function (o) {

                        var tagHTML = "<div style=\"padding: 4px;\">";

                        var currentTagsArray = new Array();
                        var inputTokenArray = _this.data.inputType == "search" ? _this.targetEle.val().split(',') : _this.targetEle.val().split(' ');
                        for (var j = 0; j < inputTokenArray.length; j++)
                            if (jQuery.trim(inputTokenArray[j] != ''))
                                if (_this.data.inputType == "metatags")
                                    currentTagsArray[currentTagsArray.length] = jQuery.trim(inputTokenArray[j]);
                                else if (_this.data.inputType == "search" && inputTokenArray[j].indexOf(_this.data.searchTagPrefix) != -1)
                                    currentTagsArray[currentTagsArray.length] = jQuery.trim(inputTokenArray[j].replace(_this.data.searchTagPrefix, ''));

                        if (data.cloudTags.length == 0) {
                            tagHTML += "<div align=\"left\" class=\"InfoSysContainer_info\" style=\"margin: 3px 8px;\">" + client_messages.text.no_tags + "</div>";
                        } else {
                            for (var i = 0; i < data.cloudTags.length; i++) {
                                var disableToken = false;
                                for (var k = 0; k < currentTagsArray.length; k++)
                                    if (currentTagsArray[k].toLowerCase() == data.cloudTags[i].name.toLowerCase())
                                        disableToken = true;

                                tagHTML += "<div class=\"" + (disableToken ? 'tokenContainerDisabled' : 'tokenContainer') + "\">" +
                                    data.cloudTags[i].name +
                                    "</div>";
                            }
                        }

                        tagHTML += "</div>";

                        var returnTagsEle = $(tagHTML);

                        $(returnTagsEle).find('.tokenContainer,.tokenContainerDisabled').click(function (e) {

                            if ($.isFunction(_this.data.beforeInputValueChange))
                                _this.data.beforeInputValueChange(_this, $(this).text());

                            if ($(this).is('.tokenContainerDisabled')) {
                                var searchTxt = ((_this.data.inputType == "search" ? _this.data.searchTagPrefix : '' ) + $(this).text()).toLowerCase();
                                //removes all occurrences of this exact text
                                var regex = new RegExp('(\\b' + escapeRegExp(searchTxt) + '\\b)', 'gi');
                                _this.targetEle.val(_this.targetEle.val().replace(regex, ''));

                                while (_this.targetEle.val().indexOf('  ') != -1)
                                    _this.targetEle.val(jQuery.trim(_this.targetEle.val().replace('  ', ' ')));
                                _this.targetEle.val(jQuery.trim(_this.targetEle.val().replace(/, ,/, ',')));
                                _this.targetEle.val(jQuery.trim(_this.targetEle.val().replace(/,,/, ',')));
                                if (_this.targetEle.val().charAt(0) == ',')
                                    _this.targetEle.val(_this.targetEle.val().substring(1, _this.targetEle.val().length));
                                if (_this.targetEle.val().charAt(_this.targetEle.val().length - 1) == ',')
                                    _this.targetEle.val(_this.targetEle.val().substring(0, _this.targetEle.val().length - 1));

                                $(this).removeClass('tokenContainerDisabled').addClass('tokenContainer');
                            } else {
                                _this.targetEle.val(_this.targetEle.val() +
                                    ( _this.targetEle.val() != '' && _this.data.inputType == "search" ? ', ' : '' ) +
                                    ( _this.targetEle.val() != '' && _this.data.inputType == "metatags" ? ' ' : '' ) +
                                    ( _this.data.inputType == "search" ? _this.data.searchTagPrefix : '' ) +
                                    $(this).text());
                                $(this).removeClass('tokenContainer').addClass('tokenContainerDisabled');
                            }

                            if ($.isFunction(_this.data.afterInputValueChange))
                                _this.data.afterInputValueChange(_this, $(this).text());
                        });

                        return returnTagsEle;
                    }
                });


            },

            waitForInputLoad: function () {
                var _this = this;

                if (!_this.targetEle.is(":visible"))
                    setTimeout(function () {
                        _this.waitForInputLoad();
                    }, 250);
                else
                    _this.initInput();
            }


        }; // end component
    }

    // instance manager
    tagCloud_component.inst = {};

})(jQuery);	