/** tableActions r1 // 2010.03.05 // jQuery 1.3 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.3 or later
 * 
 */

(function($) {

	$.tableActions = {
		ref	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return tableActions_component.inst[o.attr('id')] || null; 
		},
		defaults : {
			defaultRows: 3,
			hasHeaderRow: true
		}
	};
	
	$.fn.tableActions = function (opts) {
		return this.each(function() {
			var conf = $.extend({
					tableEle: this,
					parentContainer: $(this).parent()
				},opts);
			if(conf !== false) new tableActions_component().init(this, conf);
		});
	};
	
	function tableActions_component () {
		return {
			
		settings : $.extend({},$.tableActions.defaults),	

		init : function(elem, conf) {

			var _this = this;
			tableActions_component.inst[$(elem).attr("id")] = this;
			this.settings = $.extend(true, {}, this.settings, conf);

			var rowModifier = _this.settings.hasHeaderRow ? 1 : 0;
			if ($(_this.settings.tableEle).find('tr').length > _this.settings.defaultRows + rowModifier) {
			
				var row = _this.settings.hasHeaderRow ? 0 : 1;
				$(_this.settings.tableEle).find('tr').each(function(){
					if (row++ > _this.settings.defaultRows)
						$(this).hide();
				});
				
				var colspan = $(_this.settings.tableEle).find('tr.listTableTR:first td').length;
	
				var tableControl = 	"<tr class=\"tableToggleRow\"><td colspan=\""+colspan+"\" style=\"vertical-align: middle; padding: 0px;\">" +
										"<div class=\"tableToggleDiv\">" +
											"<table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"><tr class=\"tableToggleRow\">" +
												"<td width=\"49%\" align=\"right\" style=\"vertical-align: middle; padding: 0px;\">" +
													"<div class=\"tableToggleDownArrow\">&nbsp;</div>" +
												"</td>" +
												"<td align=\"center\" width=\"2%\" style=\"vertical-align: middle; padding: 0px;\">" +
													"<div class=\"tableToggleLabel\">" + client_messages.text.more + "</div>" +
												"</td>" +
												"<td width=\"49%\" align=\"left\" style=\"vertical-align: middle; padding: 0px;\">" +
													"<div class=\"tableToggleDownArrow\">&nbsp;</div>" +
												"</td>" +
											"</tr></table>" +
										"</div>" +
									"</td>";

				// datatable compatibility - add hidden cells to match the number of columns since colspans are not supported
				for (var i = 0; i < colspan - 1; i++){
					tableControl += "<td style='display: none;'></td>";
				}

				tableControl += "</tr>";

				$(_this.settings.tableEle).append(tableControl);
				
				$(_this.settings.tableEle).find('.tableToggleDiv').click(function(){_this.toggleTable()});
				
			}
			
			$(_this.settings.tableEle).show();
			
		},
		
		toggleTable : function() {
			var _this = this;
			var toggleDiv = $(_this.settings.tableEle).find('.tableToggleDiv');
			
			if ($(toggleDiv).find('.tableToggleDownArrow').length > 0) {
				$(toggleDiv).find('.tableToggleDownArrow').each(function(){
					$(this).removeClass('tableToggleDownArrow').addClass('tableToggleUpArrow');
				});
				$(toggleDiv).find('.tableToggleLabel').html(client_messages.text.less);
				var row = _this.settings.hasHeaderRow ? 0 : 1;
				$(_this.settings.tableEle).find('tr').each(function(){
					$(this).show();
				});
			} else {
				$(toggleDiv).find('.tableToggleUpArrow').each(function(){
					$(this).removeClass('tableToggleUpArrow').addClass('tableToggleDownArrow');
				});
				$(toggleDiv).find('.tableToggleLabel').html(client_messages.text.more);
				var row = _this.settings.hasHeaderRow ? 0 : 1;
				$(_this.settings.tableEle).find('tr').each(function(){
					if ( $(this).hasClass('tableToggleRow') )
						return;
					if (row++ > _this.settings.defaultRows)
						$(this).hide();
				});
			}
		}

		}
	};
	
	// instance manager
	tableActions_component.inst = {};

})(jQuery);	