.contentPopup_loadingIcon {
	min-height: 20px;
	min-width: 200px;
	margin: 8px 0;
	text-align: center;
	font-size: 18px;
	color: #a2a2a2;
}
.contentPopup_closeIcon {
	font-size: 12px;
	color: #444;
	cursor: pointer;
	position: absolute;
	right: 12px;
	top: 9px;
	padding: 4px;
}
.contentPopup_closeIcon:hover {
	color: #737373;
}
.contentPopup_closeIcon:active {
	color: #858585;
}
.contentPopup_loadingContainer
.contentPopup_contentContainer {
	display: none;
	padding: 16px 18px;
}
.contentPopup_imageContent {
	border: 1px solid #f5f5f5;
	-moz-box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
	-webkit-box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
	box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
}
.contentPopup_contentFrame {
	position: relative;
	text-align: left;
}
.contentPopup_infoContainer {
	padding: 3px 4px;
	border: 1px solid #dadada;
	background-color: #f5f5f5;
	margin: 5px 0px;;
	font-size: 11px;
	-webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    z-index: 1;
    position: relative;
    max-width: 300px;
}
.contentPopup_textContentContainer {
	-webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    position: relative;
    line-height: 1em;
}
.contentPopup_topArrow, .contentPopup_leftArrow, .contentPopup_rightArrow {
	position: absolute;
	width: 0;
	height: 0;
}
.contentPopup_topArrow {
	border-left: 8px solid transparent;
	border-right: 8px solid transparent;
	border-bottom: 8px solid #fff;
}
.contentPopup_leftArrow {
	border-right: 8px solid #fff;
	border-bottom: 8px solid transparent;
	border-top: 8px solid transparent;
}
.contentPopup_rightArrow {
	border-left: 8px solid #fff;
	border-bottom: 8px solid transparent;
	border-top: 8px solid transparent;
}

.contentPopup_headerContainer {
	border-bottom: 1px solid #dadada;
}
.contentPopup_titleContainer {
	padding: 18px 36px 16px 18px;
	font-size: 14px;
	font-weight: 600;
	text-align: left;
	color: #6d3075;
	white-space: nowrap;
}
.contentPopup_popupContainer {
	position: absolute;
	background: #fff;
	z-index: 999999;
	border-radius: 3px;
	box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
}