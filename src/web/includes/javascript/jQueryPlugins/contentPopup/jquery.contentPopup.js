/** contentPopup r1 // 2012.11.08 // jQuery 1.7.2 // Prinova */

(function($) {
	$.contentPopup = {
		get	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return contentPopup_component.inst[o.attr('id')] || null; 
		},
		defaults : {
			trigger					: 'hover',
			offsetWindow			: window,
			popupLocation			: 'bottom',
			contentType				: 'message',
			contentItemId			: null,
			objectDna				: null,
			contentLocaleId			: 1,
			requestParams			: null,
			cssPath					: context + "/includes/javascript/jQueryPlugins/contentPopup/contentPopup.css",
			fnBeforeContentRequest 	: null,
			fnExtServerParams		: null,
			maxWidth				: 300,
			maxHeight				: 150,
			statusViewId			: 2,
			text					: {
										suppressed			: client_messages.text.content_suppressed,
										inheriting			: client_messages.text.inherit_from,
										empty				: client_messages.text.content_empty,
										referencing_image	: client_messages.text.referencing_image
									  }
		}
	};
	
	$.fn.contentPopup = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if(conf !== false) new contentPopup_component().init(this, conf);
		});
	};
	
	function contentPopup_component () {
		return {
			
		data : $.extend({},$.contentPopup.defaults),

		init : function(elem, conf) {
			var _this = this;

			contentPopup_component.inst[$(elem).attr("id")] = _this;
			_this.data = $.extend(true, {}, this.data, conf);
			_this.targetEle = $(elem);
			
			$(_this.targetEle).css({'cursor':'default'});
			
			// CSS Include: Popup is built top window
			if ( $(getTopFrame().document).find('#contentPopupCSS').length == 0  && ( "no_css" !== _this.data.cssPath ) ) {
				$(getTopFrame().document).find('head').append("<link id=\"contentPopupCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + _this.data.cssPath + "\" />");
			}
			
			// Init triggers
			if ( _this.data.trigger == "dual" || _this.data.trigger == "hover" )
				$(_this.targetEle).hoverIntent({
					sensitivity: 50, // number = sensitivity threshold (must be 1 or higher)
					interval: 700,   // number = milliseconds of polling interval
					over: function(e) { if ( $(_this.popupEle).is(':visible') ) return; else _this.initPopup(e); },  // function = onMouseOver callback (required)
					out: function() { if ( $(_this.popupEle).is(':visible') && _this.triggerEvent != "hover" ) return; else _this.closePopup('hover'); }    // function = onMouseOut callback (required)
				 });
			if ( _this.data.trigger == "dual" || _this.data.trigger == "dblclick" )
				$(_this.targetEle).dblclick( function(e) {
					_this.initPopup(e);
				});
			else if ( _this.data.trigger == "click" )
				$(_this.targetEle).click( function(e) {
					_this.initPopup(e);
				});
			else if ( _this.data.trigger == "instant" )
				_this.initPopup(null);
		},
		
		closePopup : function(type) {
			var _this = this;
			$(_this.popupEle).remove();
		},

		getVisibility : function(ele) {
			if ( !ele )
				return ["none","none"];

			var $element = $(ele);
			var element = $element[0];
			var doc = element.ownerDocument;
			var win = doc.defaultView || doc.parentWindow;

			var visArray = new Array();
	        var pos = $(element).offset(),
	            wY = $(win).scrollTop(),
	            wH = $(win).height(),
	            oH = $(element).outerHeight();

	        if (pos.top >= wY && oH + pos.top <= wY + wH )
	        	visArray[1] = "full";
	        else if ( ((pos.top <= wY && pos.top + oH > wY) ||
	             (pos.top  >= wY && pos.top  <= wY + wH)) )
	        	visArray[1] = "partial";
	        else
	        	visArray[1] = "none";

	        var wX = $(window).scrollLeft(),
            	wW = $(window).width(),
            	oW = $(element).outerWidth();

	        if (pos.left >= wX && oW + pos.left <= wX + wW )
	        	visArray[0] = "full";
	        else if ( ((pos.left <= wX && pos.left + oW > wX) ||
	             (pos.left >= wX && pos.left <= wX + wW)) )
	        	visArray[0] = "partial";
	        else
	        	visArray[0] = "none";

	        return visArray;
		},

		initPopup : function(e) {
			var _this = this;
			
			_this.is_animation_init = false;
			
			_this.triggerEvent = _this.data.trigger;
			if (e != null && e.type == "mouseover")
				_this.triggerEvent = "hover";
			
			var popupOffset = _this.computeFrameOffset( _this.data.offsetWindow, $(_this.targetEle).offset() );
			
			// Target item is no longer visible: Abort init
			if ( $(_this.targetEle).outerHeight() == 0 || $(_this.targetEle).outerWidth() == 0 )
				return;
			
			// Popup arrow: Resolve arrow type and positioning (based on popup positioning)
			var popupArrowClass = "", arrowLeft = 0, arrowTop = 0;
			if ( _this.data.popupLocation == 'bottom' ) {
				if ( _this.getVisibility(_this.targetEle)[1] == "full" ) {
					popupOffset.top += $(_this.targetEle).outerHeight() + 6;
					popupArrowClass = "contentPopup_topArrow";
					arrowTop = - 7;
					arrowLeft = Math.min((_this.targetEle).outerWidth() / 2, 150);
				} else {
					_this.data.popupLocation = 'left';
				}
			}
			if ( _this.data.popupLocation == 'right' ) {
				popupOffset.left += $(_this.targetEle).outerWidth() + 6;
				popupArrowClass = "contentPopup_leftArrow";
				arrowTop = 5;
				arrowLeft = - 7;
			}
			if ( _this.data.popupLocation == 'left' ) {
				popupOffset.left += -300 - 6;
				popupArrowClass = "contentPopup_rightArrow";
				arrowTop = 5;
				arrowLeft = 300;
			}
			
			// HTML: Popup definition
			var popupHTML = "<div class=\"contentPopup_popupContainer\" style=\"position: absolute; left: " + popupOffset.left + "px; top: " + popupOffset.top + "px;\">" +
								"<div class=\"" + popupArrowClass + "\" style=\"left:" + arrowLeft + "px; top:" + arrowTop + "px;\"></div>" +
								"<div class=\"contentPopup_headerContainer\">" +
										"<div class=\"contentPopup_titleContainer\">" +
											client_messages.text.loading +
										"</div>" +
										(_this.triggerEvent != "hover" ?
                                            ("<i class=\"fa fa-times contentPopup_closeIcon\"></i>") : "") +
								"</div>" +
								"<div class=\"contentPopup_loadingContainer\">" +
									"<div class=\"contentPopup_loadingIcon\">" +
										"<i class=\"fa fa-spinner fa-pulse\"></i>" +
									"</div>" +
								"</div>" +
								"<div class=\"contentPopup_contentContainer\">" +
									"<iFrame class=\"contentPopup_iFrame\" frameborder=0 width=\"100%\" height=\"20px\" " +
											"src=\"" + context + "/content_popup_container.jsp?tk=" + gup('tk') +"\" allowtransparency=\"true\" scrolling=\"" + (_this.triggerEvent == "hover" ? 'no' : 'auto') + "\" >" +
									"</iframe>" +
								"</div>" +
							"</div>";
			
			$(getTopFrame().document).find('.contentPopup_popupContainer').remove();
			$(getTopFrame().document).find('body').append(popupHTML);

			// Close popup: If click anywhere outside popup
			if ( _this.triggerEvent != "hover" )
				_this.initFrameClickHandler(_this.data.offsetWindow);
			
			_this.popupEle = $(getTopFrame().document).find('.contentPopup_popupContainer');
			
			// Popup Content Init: iFrame onLoad
			$(_this.popupEle).find(".contentPopup_iFrame").each( function() {
				
				// Resolve document object (access point determined by browser)
			    var doc = $(this).contents();

			    // CAUSES IE ERROR: Not sure what this was supposed to be doing
//			    try {
//					$(doc).height($(this).height());
//				} catch (err) {
//
//				}

			    if (doc.document)
			        doc = doc.document;

			    // Continue init when iFrame has loaded
				if ( doc.readyState == 'complete' ) {
					_this.iFrameOnLoad(this);
				} else {
					$(this).load( function() {
						_this.iFrameOnLoad(this);
					});
				}
			});

			// Close Icon: Init close on click
			if ( _this.triggerEvent != "hover" )
				$(_this.popupEle).find('.contentPopup_closeIcon').click( function() {
					_this.closePopup('close')
				});

		},
		
		computeFrameOffset : function(win, dims) {
			var _this = this;
			
		    // Init
		    if (typeof dims === 'undefined') {
		        var dims = { top: 0, left: 0 };
		    }

		    // Resolve target iFrame: Find <iframe> within parent window
		    var frames = win.parent.document.getElementsByTagName('iframe');
		    var frame;
		    var found = false;

		    for (var i=0, len=frames.length; i<len; i++) {
		        frame = frames[i];
		        if (frame.contentWindow == win) {
		            found = true;
		            break;
		        }
		    }

		    // Calculate and Recurse
		    if (found) {
		        var rect = $(frame).offset();
		        dims.left += rect.left - frame.contentWindow.pageXOffset;
		        dims.top += rect.top - frame.contentWindow.pageYOffset;
		        if (win !== top) {
		            _this.computeFrameOffset(win.parent, dims);
		        }
		    }
		    return dims;
		},
		
		iFrameOnLoad : function(frameEle) {
			var _this = this;
			
			// External Callback
			if ( $.isFunction(_this.data.fnBeforeContentRequest) )
				_this.data.fnBeforeContentRequest(_this);
			
			if ( _this.data.contentType == 'history' ) {
				if(_this.data.contentItemId > 0){
					// Parameter building
					var requestParam = 	"type=histSumm" + 
										"&hca1Id=" + _this.data.contentItemId;
					
					if(_this.data.updatedBy){
						requestParam += "&updatedBy=" + _this.data.updatedBy;
					}

					if(_this.data.actionType){
						requestParam += "&actionType=" + _this.data.actionType;
					}

					if(_this.data.markerType){
						requestParam += "&markerType=" + _this.data.markerType;
					}

					if(_this.data.isSynced){
						requestParam += "&isSynced=" + _this.data.isSynced;
					}

					if(_this.data.timestamp){
						requestParam += "&timestamp1=" + _this.data.timestamp;
					}

					if(_this.data.isLatest){
						requestParam += "&isLatest1=" + _this.data.isLatest;
					}
					
					// Async Request
					var stampDate = new Date();
					$.ajax({
						type: "GET",
						url: context + "/getContentHistory.form?" + requestParam + "&cacheStamp=" + (stampDate.getTime()),
						dataType: "json",
						success: function(data) {
							_this.processContent(data, frameEle);
						}
					});					
				}
			} 	else if ( _this.data.contentType == 'rationalizer_content_history' ) {
				if(_this.data.contentItemId > 0){
					// Parameter building
					var requestParam = 	"type=rationalizerContentHistorySummary" +
						"&hcId=" + _this.data.contentItemId;

					if(_this.data.updatedBy){
						requestParam += "&updatedBy=" + _this.data.updatedBy;
					}

					// Async Request
					var stampDate = new Date();
					$.ajax({
						type: "GET",
						url: context + "/getRationalizerContentHistory.form?" + requestParam + "&cacheStamp=" + (stampDate.getTime()),
						dataType: "json",
						success: function(data) {
							_this.processContent(data, frameEle);
						}
					});
				}
			}else {
				// Parameter building
				var requestParam = 	"contentType=" + _this.data.contentType +
									(_this.data.contentItemId?("&contentItemId=" + _this.data.contentItemId):"") +
									(_this.data.objectDna?("&objectDna=" + _this.data.objectDna):"") +
									"&statusViewId=" + _this.data.statusViewId +
									"&localeId=" + _this.data.contentLocaleId;
				
				if ( $.isFunction(_this.data.fnExtServerParams) ) {
					var extParams = _this.data.fnExtServerParams(_this);
					for (var currentIndex = 0; currentIndex < extParams.length; currentIndex++)
						requestParam += "&" + extParams[currentIndex].name + "=" + extParams[currentIndex].value;
				}
				
				// Async Request
				var stampDate = new Date();
				$.ajax({
					type: "GET",
					url: context + "/getContentPreview.form?" + requestParam + "&cacheStamp=" + (stampDate.getTime()),
					dataType: "json",
					success: function(data) {
						_this.processContent(data, frameEle);
					}
				});				
			}
		},
		
		initFrameClickHandler : function(win) {
			var _this = this;

		    var doc = win.contentWindow || win.contentDocument || win;
		    if (doc.document)
		        doc = doc.document;
		
			$(doc).click( function(e) {
				var targetEle = eventTarget(e);
				if ( !($(targetEle).is('.contentPopup_popupContainer') || $(targetEle).closest('.contentPopup_popupContainer').length > 0) )
					_this.closePopup('docClick')
			});
			
		    // find our <iframe> tag within our parent window
		    var frames = win.parent.document.getElementsByTagName('iframe');
		    var frame;
		    var found = false;

		    for (var i=0, len=frames.length; i<len; i++) {
		        frame = frames[i];
		        if (frame.contentWindow == win) {
		            found = true;
		            break;
		        }
		    }

		    // Recur up the frame chain
		    if (found)
		        if (win !== top)
		            _this.initFrameClickHandler(win.parent);
		},
		refreshTargetingIconDisplay: function(doc) {
			var $contentFrame = $(doc).find('.contentPopup_contentFrame');

			$contentFrame.find('[content_targeting_id]').each( function() {
				var $el = $(this);
				var isInitialized = $el.attr('mce_mp_target_event_init') !== undefined;
				var hasIcon = $el.find('.contentTargetingIcon').length > 0;
				var hasTopInline = $el.find('.mceInlineTargeting').filter(function () {
					return $(this).closest('.mceInlineTargeting').get(0) === this;
				}).length > 0;

				if (!isInitialized || (!hasIcon && !hasTopInline)) {
					$el.find('.contentTargetingIcon').parent().remove();
					$el.children('.mceInlineTargeting').remove();

					var isTableRowTargeting = $el.is('tr');
					var appendCell = isTableRowTargeting ? $el.find('td:first') : $el;
					var $table = $el.closest('table');
					var tablePadding = isTableRowTargeting && $table.attr('cellpadding') ? parseInt($table.attr('cellpadding'), 10) : 0;
					var cellPaddingLeft = isTableRowTargeting ? parseInt(appendCell.css('padding-left').replace('px', ''), 10) : 0;
					var cellPaddingTop = isTableRowTargeting ? parseInt(appendCell.css('padding-top').replace('px', ''), 10) : 0;

					var leftPadding = 0;
					var topPadding = 0;

					if (tablePadding > 0 || cellPaddingLeft > 0) {
						leftPadding = -1 * (tablePadding + cellPaddingLeft - 1);
					}
					if (tablePadding > 0 || cellPaddingTop > 0) {
						topPadding = -1 * (tablePadding + cellPaddingTop - 1);
					}

					if ( isTableRowTargeting ) {
						$(appendCell)
							.prepend("<div class=\"mceStaticElement mceContentTargetingContainer\" contenteditable=\"false\" style=\"margin-left: " + leftPadding + "px; margin-top: " + topPadding + "px; width: 0px; height: 0px; position: absolute;\">" +
								"<div class=\"mceStaticElement contentTargetingIcon\" contenteditable=\"false\"></div>" +
								"</div>");
					} else {
						$(appendCell)
							.prepend("<i class=\"far fa-bullseye mp-ico-targeting fa-mp-ed-ico mceInlineTargeting\" contenteditable=\"false\" style=\"cursor: default;\">&nbsp;</i>");
					}
				}

			});

			$contentFrame.find('[column_targeting_ids]').each( function() {
				var $table = $(this);
				var idsAttr = $table.attr('column_targeting_ids');
				var columnIds = idsAttr ? idsAttr.split(':') : [];

				if ($table.attr('mce_mp_target_event_init') !== undefined &&
					$table.find('.columnTargetingIcon').length === countValid(columnIds)) {
					return;
				}

				if ($table.find('.columnTargetingIcon').length > 0) {
					$table.find('.columnTargetingIcon').parent().remove();
				}

				var rows = [];

				// Build a matrix of cells to track columns properly
				$table.find('tr').each(function() {
					var cells = [];
					$(this).find('td').each(function() {
						var colspan = parseInt($(this).attr('colspan')) || 1;
						for (var j = 0; j < colspan; j++) {
							cells.push($(this));
						}
					});
					rows.push(cells);
				});

				$.each(columnIds, function(i, id) {
					if (id === "0") return;

					var $row = null;
					$.each(rows, function(_, _cells) {
						if (_cells[i] && !_cells[i].attr('colspan')) {
							$row = _cells;
							return false; // break
						}
					});
					if (!$row) {
						columnIds[i] = "0";

						// Remove attribute if empty or contains only "0"
						if (!columnIds.length || allZero(columnIds)) {
							$table.removeAttr('column_targeting_ids');
							$table.removeAttr('column_fixed_width');
						}

						return;
					}

					var $cell = $row[i];

					$cell.prepend(
						"<div class=\"mceStaticElement mceColumnTargetingContainer\" contenteditable=\"false\" style=\"position: absolute; left: 50%; top: -11px; margin-left: 10px;\">" +
						"<div class=\"mceStaticElement columnTargetingIcon\" contenteditable=\"false\"></div>" +
						"</div>"
					);
				});

			});

			// Apply padding if necessary
			if ($contentFrame.find('[content_targeting_id]').length > 0) {
				$contentFrame.css('padding-left', '25px');
			}
			if ($contentFrame.find('[column_targeting_ids]').length > 0) {
				$contentFrame.css('padding-top', '25px');
			}
		},
		
		processContent: function(data, frameEle) {
			var _this = this;
			
			// Resolve iFrame Document
		    var doc = frameEle.contentWindow || frameEle.contentDocument;
		    if (doc == null)
		    	return;
		    else if (doc.document)
		        doc = doc.document;

			$(doc).ready(function() {
				// Set Popup Title
				$(_this.popupEle).find('.contentPopup_titleContainer').html($('<div/>').html(data.name).text());

				// Init iFrame Content CSS
				if (data.text_style_css_path)
					$(doc).find('head').append("<link id=\"textStyleCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + data.text_style_css_path + "\" />");
				if (data.paragraph_style_css_path)
					$(doc).find('head').append("<link id=\"paragraphStyleCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + data.paragraph_style_css_path + "\" />");
				if (data.list_style_css_path)
					$(doc).find('head').append("<link id=\"listStyleCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + data.list_style_css_path + "\" />");
				if (data.default_style_css_path)
					$(doc).find('head').append("<link id=\"defaultStyleCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + data.default_style_css_path + "\" />");
				if  ( "no_css" !== _this.data.cssPath ) {
					$(doc).find('head').append("<link id=\"contentPopupCSS\" rel=\"stylesheet\" type=\"text/css\" href=\"" + _this.data.cssPath + "\" />");
				}
		    
				if ( data.text_style_data || data.paragraph_style_data )
					$(doc).find('head').append("<script type=\"text/javascript\" src=\"" + context + "/includes/javascript/jQueryPlugins/styleManager/jquery.styleManager.js\"></script>");

				var contentDisplayHTML = "";

				// HTML: Build content display
				contentDisplayHTML += "<div class=\"contentPopup_contentFrame\">";

				var canvasWidth = null;
				var canvasHeight = null;
				var hasTextItem = false;
				
				for (var contentIndex = 0; contentIndex < data.contents.length; contentIndex++) {
					var currentContent = data.contents[contentIndex];

					if ( currentContent.canvas_size != undefined && currentContent.canvas_size != null ) {
						if ( !currentContent.graphic_path ) {
							var dim = currentContent.canvas_size.split(':');
							canvasWidth = Math.max(dim[0] * 100, canvasWidth);
							canvasHeight = Math.max(dim[1] * 100, canvasHeight);
						} else {
							canvasWidth = 300;
						}
					}
				}

				for (var contentIndex = 0; contentIndex < data.contents.length; contentIndex++) {
					var currentContent = data.contents[contentIndex];

					if ( currentContent.updated_by ) {	// Content history "Updated By" information
						contentDisplayHTML += 	"<div class=\"contentPopup_infoContainer\" style=\"background-color: #f5f5f5;\">" + currentContent.action_by + ": <i>" +
													currentContent.updated_by +
												"</i></div>";
					}

					if ( currentContent.is_reference && !currentContent.is_image_library_reference ) {
						contentDisplayHTML += 	"<div class=\"contentPopup_infoContainer\" style=\"background-color: #f5f5f5;\">" + _this.data.text.inheriting + ": <i>" +
													$('<div/>').html(currentContent.reference_node_name).text() +
												"</i></div>";
						break;  // Inheritance is global feature of content: Display only once
					}
					if ( currentContent.is_suppressed ) {
						contentDisplayHTML += 	"<div class=\"contentPopup_infoContainer\" style=\"background-color: #f5f5f5;\">" + _this.data.text.suppressed + "</div>";
						break;  // Suppression is global feature of content: Display only once
					}
				}
				if ( canvasWidth != null && canvasWidth !== 0)
					$(_this.popupEle).animate({'width': Math.min( canvasWidth, _this.data.maxWidth) +'px'});

				_this.maxImgWidth = $(_this.popupEle).outerWidth() - 20;
				for (var contentIndex = 0; contentIndex < data.contents.length; contentIndex++) {
					var currentContent = data.contents[contentIndex];

					if (currentContent.content_name)
						contentDisplayHTML += 	"<div class=\"contentPopup_infoContainer\" style=\"background-color: #fff;\">" + currentContent.content_name + "</div>";

					if (currentContent.is_image_library_reference)
						contentDisplayHTML += 	"<div class=\"contentPopup_infoContainer\" style=\"background-color: #f5f5f5;\">" + _this.data.text.referencing_image + ": <i>" +
													$('<div/>').html(currentContent.image_library_name).text() +
												"</i></div>";

					if (currentContent.is_same_as_system_default)
						contentDisplayHTML += 	"<div class=\"contentPopup_infoContainer\">" + client_messages.text.referencing_system_default_content + "</div>";

					if (currentContent.is_same_as_touchpoint_default)
						contentDisplayHTML += 	"<div class=\"contentPopup_infoContainer\">" + client_messages.text.referencing_touchpoint_default_content + "</div>";

					if (currentContent.is_empty_content)
						contentDisplayHTML += 	"<div class=\"contentPopup_infoContainer\" style=\"background-color: #f5f5f5;\">" + _this.data.text.empty + "</div>";
					else {
						if (currentContent.text_content) {
                            if(currentContent.is_rationalizer_content) {
									var summaryHTML = "<div align=\"left\" class=\"project_task_summaryContainer\" style=\"overflow-y:auto\">";
									if (currentContent.requirement) {
										summaryHTML += "<div class=\"project_task_summaryItemContainer\" style=\"overflow-wrap:break-word; inline-size: 380px; white-space: break-spaces; height:100%; margin-bottom: 15px;\">" +
											"<div style=\"font-size: 13px; color:#6d3075;; margin-bottom: 10px; font-weight: bold;\">" + client_messages.text.description + "</div>" +
											"<div  style=\"overflow-wrap:break-word; inline-size: 380px; white-space: break-spaces; height:100%; font-size: 12px; color: black\">" + currentContent.requirement + "</div>" +
											"</div>";
									}
									if (currentContent.text_content) {
										summaryHTML += "<div class=\"project_task_summaryItemContainer\" style=\"overflow-wrap:break-word; inline-size: 380px; white-space: break-spaces; margin-top: 15px;\">" +
											"<div style=\"font-size: 13px; color:#6d3075;; margin-bottom: 10px; font-weight: bold;\">" + client_messages.text.content + "</div>" +
											"<div class=\"project_task_taskSummaryValue\" style=\"overflow-wrap:break-word; inline-size: 380px; white-space: break-spaces\">" + currentContent.text_content + "</div>" +
											"</div>";
								  }
								summaryHTML += "</div>";
								$($(getTopFrame().document).find('.contentPopup_popupContainer')[0]).css('width', '400px');
								$($(getTopFrame().document).find('.contentPopup_titleContainer')[0]).css('overflow-wrap', 'break-word').css('inline-size', '380px').css('white-space', 'break-spaces');
								hasTextItem = true;
								contentDisplayHTML += "<div class=\"contentPopup_textContentContainer contentContainer\" style=\" background-color: #" + currentContent.background_color + ";\">" +
									summaryHTML +
									"</div>";
							} else {
								hasTextItem = true;

								contentDisplayHTML += "<div class=\"contentPopup_textContentContainer contentContainer\" style=\"background-color: #" + currentContent.background_color + ";\">" +
									$('<div/>').html(currentContent.text_content).text() +
									"</div>";
							}
						} else if (currentContent.graphic_path) {
							var stampDate = new Date();

							var src = null;
							if ( currentContent.graphic_path.match(/\.pdf$|\.pdf&|\.pdf\?/i) )
								src = context + '/download/image.form?file=' + currentContent.graphic_path + "&type=rendered_pdf&tk=" + getParam('tk') + "&cacheStamp=" + stampDate.getTime();
							else if ( currentContent.graphic_path.match(/\.rtf$|\.rtf&|\.rtf\?/i) )
								src = "../includes/themes/commonimages/icons/rtf_large_icon.png";
							else if ( currentContent.graphic_path.match(/\.img$|\.img&|\.img\?/i) )
								src = "../includes/themes/commonimages/icons/img_large_icon.png";
							else if ( currentContent.graphic_path.match(/\.eps$|\.eps&|\.eps\?/i) )
								src = "../includes/themes/commonimages/icons/eps_large_icon.png";
							else if ( currentContent.graphic_path.match(/\.dxf$|\.dxf&|\.dxf\?/i) )
								src = "../includes/themes/commonimages/icons/dxf_large_icon.png";
							else if ( currentContent.graphic_path.match(/\.dlf$|\.dlf&|\.dlf\?/i) )
								src = "../includes/themes/commonimages/icons/dlf_large_icon.png";
							else if ( currentContent.graphic_path.match(/\.docx$|\.docx&|\.docx\?/i) )
								src = "../includes/themes/commonimages/icons/docx_large_icon.png";
							else
								src = context + '/download/image.form?file=' + currentContent.graphic_path + "&tk=" + getParam('tk') + "&cacheStamp=" + stampDate.getTime();

							contentDisplayHTML += 	"<div align=\"left\">" +
														"<img class=\"contentPopup_imageContent\" src=\"" + src + "\" style=\"max-width: "+_this.maxImgWidth+"px; max-height: 125px;\" />" +
													"</div>";
						}
					}

					// Separator
					if (parseInt(contentIndex) + 1 != data.contents.length)
						contentDisplayHTML += 	"<div style=\"height: 12px;\"></div>";
				}
				contentDisplayHTML += "</div>";
				
				contentDisplayHTML = $(contentDisplayHTML);
				$(contentDisplayHTML).find('.mceBarcode img').each( function() {
					var src = $(this).attr('src');
					$(this).attr('src',src.replace('../',context+'/'));
				});

				$(doc).find('body')
					.html(contentDisplayHTML);

				_this.refreshTargetingIconDisplay(doc);
				
                // Remove presentational wrapper to reveal content
				$(doc).find('.contentPopup_textContentContainer').children().each(function() {
					if ( $(this).is('i,.renderedLabelContainer,.fa-stack') )
						$(this).remove();
					else if ( $(this).is('.renderedContentContainer') )
						$(this).contents().unwrap();
                });

				$(doc).find('img').each( function() {
					_this.animateFrameHeightOnImageLoad(this, frameEle, doc);
				});

				$(doc).find('.contentPopup_textContentContainer').styleManager({
					document			: doc,
					channel				: data.channel,
					apply_global_styles	: true
				});

				$(_this.popupEle).find('.contentPopup_loadingContainer').hide();
				$(_this.popupEle).find('.contentPopup_contentContainer').showEle('normal');

				var contentHeight = $(doc).height() + 20;
				if ( canvasHeight != null )
					contentHeight = Math.max( canvasHeight + 20, contentHeight);

				var frameHeight = Math.min( contentHeight, _this.data.maxHeight );
				$(frameEle).animate({height: frameHeight}, 300);

				if (hasTextItem) {
                    $(doc).smartTextContentViewer('init', frameEle.contentWindow);
				}

				if ( _this.triggerEvent == "hover" && !_this.is_animation_init )
					setTimeout( function() { _this.animateContentScroll($(doc).find('.contentPopup_contentFrame'), frameEle); }, 1000 );

				// SMART TEXT: Label/content rendering
				$(doc).find('.renderedContentContainer').each( function() {
					var varEle = $(this).closest('.staticContentItem');
					if ( localStorage.getItem("msgpt_contentrendering_enable") == null || localStorage.getItem("msgpt_contentrendering_enable") == "true" ) {
						$(varEle).children('.renderedLabelContainer').hide();
						$(varEle).children('.renderedContentContainer').show();
					} else {
						$(varEle).children('.renderedContentContainer').hide();
						$(varEle).children('.renderedLabelContainer').show();
					}
				});
				// VARIABLES: Label/sample value rendering
				$(doc).find('.renderedSampleValueContainer').each( function() {
					var varEle = $(this).closest("[type='1']");
					if ( localStorage.getItem("msgpt_samplevaluerendering_enable") == "true" ) {
						$(varEle).children('.renderedLabelContainer').hide();
						$(varEle).children('.renderedSampleValueContainer').show();
					} else {
						$(varEle).children('.renderedSampleValueContainer').hide();
						$(varEle).children('.renderedLabelContainer').show();
					}
				});

			});
		},
		
		animateFrameHeightOnImageLoad : function(img, frameEle, doc) {
			var _this = this;

			if ( !img.complete ) {
				setTimeout( function() { _this.animateFrameHeightOnImageLoad(img, frameEle, doc); }, 50 );
			} else {
				var contentHeight = $(doc).find('.contentPopup_contentFrame').get(0).scrollHeight + 20;
				var frameHeight = Math.min( contentHeight, _this.data.maxHeight );
				$(frameEle).animate({height: frameHeight}, 300);
				
				// IE: Fix for IMG max-width
				if ( $(img).width() > _this.maxImgWidth )
					$(img).attr('width', _this.maxImgWidth);		
				
				if ( _this.triggerEvent == "hover" && !_this.is_animation_init )
					setTimeout( function() { _this.animateContentScroll($(doc).find('.contentPopup_contentFrame'), frameEle); }, 1000 );
			}
		},
		
		animateContentScroll : function(contentEle, frameEle, offSet) {
			var _this = this;
			
			var contentHeight = $(contentEle).closest('.contentPopup_contentFrame').get(0).scrollHeight + 20;
			var frameHeight = Math.min( contentHeight, _this.data.maxHeight );
			
			// Popup removed: Stop animation
			if ( contentEle == undefined || !$(contentEle).is(':visible') )
				return;
			
			// IE Adjustment: Resize post load
			if (!_this.is_animation_init)
				$(frameEle).animate({height: frameHeight}, 300);

			_this.is_animation_init = true;

			// Scolling needed? Content height larger than frame height
			if (contentHeight <= frameHeight)
				return;

			// First iteration: Init offset
			if (offSet == undefined)
				offSet = 1;

			// Direction toggle: Toggle between 'scroll' up and down
			var directionToggle = $(contentEle).position().top > 0 || $(contentEle).position().top <  (frameHeight - contentHeight);
			offSet = directionToggle ? (-1 * parseInt(offSet)) : offSet;
			$(contentEle).animate({top: ($(contentEle).position().top - offSet)+'px'}, 15);
			
			// Looping timeout:  Pause briefly on direction toggle 
			setTimeout( function() { _this.animateContentScroll(contentEle, frameEle, offSet); }, directionToggle ? 1000: 75 );
		}
		
		}; // end component
	};

	function computeFrameOffset(win, dims ) {
		dims = (typeof dims === 'undefined')?{ top: 0, left: 0}:dims;
		if (win !== top) {
			var rect = win.frameElement.getBoundingClientRect();
			dims.left += rect.left;
			dims.top += rect.top;
			computeFrameOffset(win.parent, dims );
		}
		return dims;
	};

	// instance manager
	contentPopup_component.inst = {};
	
})(jQuery);	