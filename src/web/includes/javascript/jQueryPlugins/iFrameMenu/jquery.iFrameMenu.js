/** iFrameMenu r1 // 2012.05.30 // jQuery 1.4 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.3 or later
 * 
 */

function iFrameMenuToggle(e, clickedEleId) {
	var _this = $.iFrameMenu.get( clickedEleId );

	if ( $("#iFrameMenu_"+_this.data.frameId, getTopFrame().document).is(':visible') )
		_this.closeAction(e);
	else
		_this.initPopup();

	return this;
};

(function($) {
	$.iFrameMenu = {
		get	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return iFrameMenu_component.inst[o.attr('id')] || null; 
		},
		defaults : {
			width			: 300,
			height 			: 300,
			horiOffset		: 0,
			vertOffset		: 0,
			fullWidthIframe	: false,
			src				: "#",
			clearOnClose 	: true,
			onMenuOpen		: null,
			beforeMenuClose	: null,
			onMenuClose		: null,
			onTargetHover	: null,
			onTargetHoverOut: null,
			devMode			: null
		}
	};
	
	$.fn.iFrameMenu = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if(conf !== false) new iFrameMenu_component().init(this, conf);
		});
	};
	
	function iFrameMenu_component () {
		return {
			
		data : $.extend({},$.iFrameMenu.defaults),	

		init : function(elem, conf) {
			var _this = this;
			
			_this.data.devMode = _.isFunction(getTopFrame().MAIN_DEV_MODE) ? getTopFrame().MAIN_DEV_MODE() : null;

			iFrameMenu_component.inst[$(elem).attr("id")] = _this;
			_this.data = $.extend(false, {}, this.data, conf);
			_this.targetEle = $(elem);
			
			_this.data.frameId = _this.targetEle.attr('id');

			$(_this.targetEle).addClass('iFrameMenu_targetEle');
			
			if ($.isFunction( _this.data.onTargetHover ))
				$(_this.targetEle).mouseover(function(){_this.data.onTargetHover(_this);});
			if ($.isFunction( _this.data.onTargetHoverOut ))
				$(_this.targetEle).mouseout(function(){_this.data.onTargetHoverOut(_this);});
			
			$(_this.targetEle).click( function(e){ iFrameMenuToggle(e, $(_this.targetEle).attr('id')); } );
		},
		
		initPopup : function() {
			var _this = this;

			if ( $("#iFrameMenu_"+_this.data.frameId, getTopFrame().document).length == 0 ) {

				// GENERATE POPUP
				var iFrameMenuHTML =
										"<div id=\"iFrameMenu_"+_this.data.frameId+"\" class=\"iFrameMenuContainer\" style=\"display: none;\" >" +
											"<div class='iframeMenuContentWrapper'>" +
												"<div class=\"iFrameMenuProcessingContainer\" style=\"width: "+_this.data.width+"px;\">" +
													"<i class=\"fa fa-spinner fa-pulse\" style=\"font-size: 20px; color: #aaa;\"></i>" +
												"</div>" +
												"<div class=\"iFrameMenuFrameContainer\" style=\"visibility: hidden;\">" +
													"<iFrame id=\"iFrameMenuFrame_"+_this.data.frameId+"\" class=\"iFrameMenuFrame\" frameborder=0 width=\""+_this.data.width+"\" height=\""+_this.data.height+"\" src=\""+_this.getIframePath()+"\" allowtransparency=\"true\"></iframe>" +
												"</div>" +
											"</div>";
										"</div>"

				$("body", getTopFrame().document).append( $(iFrameMenuHTML) );
				
				// Hide cluetip popup: Mouseout not called when iFrame loads
				$("body", getTopFrame().document).find('#cluetip').hide();
				$("body", getTopFrame().document).find('.contentPopup_popupContainer').remove();
				popupFactoryRemove('iFrameMenu_initPopup');

				// START iFrame ONLOAD EVENT HANDLER
				$("#iFrameMenu_"+_this.data.frameId+" .iFrameMenuFrame", getTopFrame().document).each( function() {

                    $(this).load(function() {
                        _this.iFrameOnLoad($(this));
                    });

				});
				// END iFrame ONLOAD EVENT HANDLER
	
				if ( _this.hasDocumentClickHandler == undefined ) {
					$(document).click( function(e) { 
					  _this.hideMenu(e);
					});
					_this.hasDocumentClickHandler = true;
				}
				// BUG - ON IE RESIZE IS BEING CALLED ON CLICK EVENTS
				//$(window).resize(function(){alert('resize close');_this.closeAction()});
				
				if ($.isFunction( _this.data.onMenuOpen ))
					_this.data.onMenuOpen(_this);
			}
			
			$("#iFrameMenu_"+_this.data.frameId, getTopFrame().document).showEle('normal');

			// Calculate popup position (horizontally centered, vertical offset accounting for scroll position)
			if (_this.data.fullWidthIframe) {
				var iframeContainerWidth 	= $('.containerLowerFull', getTopFrame().document).outerWidth();
				var horiOffset				= $('.containerLowerFull', getTopFrame().document).position().left;
				var vertOffset				= $('.containerLowerFull', getTopFrame().document).position().top + _this.data.vertOffset;
				var frameContainer			= $("#iFrameMenu_"+_this.data.frameId, getTopFrame().document);
				$(frameContainer).attr("style", "width: " + iframeContainerWidth + "px; left: " + horiOffset + "px; top: " + vertOffset + "px;");
				$(frameContainer).find('.iFrameMenuProcessingContainer')
								 .css({
									 width: _this.data.width + "px",
									 left : $(_this.targetEle).position().left + _this.data.horiOffset + "px",
									 top : "0px",
									 position: "relative"});
				$(frameContainer).find('.iFrameMenuFrame').attr('width',iframeContainerWidth);
				$(frameContainer).find('.iFrameMenuFrame').attr('menuOffset',($(_this.targetEle).position().left + _this.data.horiOffset));
			} else {
				var iframeContainerWidth 	= _this.data.width;
				var horiOffset				= $(_this.targetEle).position().left + _this.data.horiOffset;
				var vertOffset				= $(_this.targetEle).position().top + $(_this.targetEle).outerHeight() + _this.data.vertOffset;
				$("#iFrameMenu_"+_this.data.frameId, getTopFrame().document).attr("style", "width: " + iframeContainerWidth + "px; left: " + horiOffset + "px; top: " + vertOffset + "px;");
			}
		},
		
		closeAction : function(e) {
			var _this = this;

			if ($.isFunction( _this.data.beforeMenuClose ))
				if ( _this.data.beforeMenuClose(_this, e) == false )
					return;
			
			if ( $("#iFrameMenu_"+_this.data.frameId, getTopFrame().document).is(':visible') ) {
				if (_this.data.clearOnClose)
					$("#iFrameMenu_"+_this.data.frameId, getTopFrame().document).remove();
				else
					$("#iFrameMenu_"+_this.data.frameId, getTopFrame().document).hide();
				
				// Hide cluetip popup: Mouseout not called when iFrame closes
				$("body", getTopFrame().document).find('#cluetip').hide();
				$("body", getTopFrame().document).find('.contentPopup_popupContainer').remove();
				popupFactoryRemove('iFrameMenu_closePopup');
				
				if ($.isFunction( _this.data.onMenuClose ))
					_this.data.onMenuClose(_this);

				$('.contextMenuPageScreen', getTopFrame().document).remove();
			}
		},
		
		getIframePath : function() {
			var _this = this;

			var path = _this.data.src;
			
			// If there are params
			if (_this.data.appliedParams != undefined) {
				
				// How many params?
				var paramCount = 0;
				jQuery.each(_this.data.appliedParams, function(currentParam, val) {
					paramCount++;
				});
				
				// Iterate parameters and build path
				if (paramCount > 0) {
					if (path.indexOf("?") == -1)
						path += "?";
					else
						path += "&";
					jQuery.each(_this.data.appliedParams, function(currentParam, val) {
						if ( $.isFunction(val) )
							path += currentParam + "=" + val() + "&";
						else
							path += currentParam + "=" + val + "&";
					});
					// Remove dangling '&'
					path = path.substring(0,path.length-1);
				}
			}

			return path;
		},
		
		getUrlParam : function (url, paramName) {
			var regexS = "[\\?&]"+paramName+"=([^&#]*)";
			var regex = new RegExp(regexS);
			var results = regex.exec(url);
			if (results == null)
				return "";
			else
				return results[1];
		},
		
		hideMenu : function(e) {
			var _this = this;

			var element;
			if (window.event != null) 
				element = event.srcElement;
			else
				element = e.target;
			element = $(element).closest(".iFrameMenu_targetEle");

			if ($(element).attr('id') && $(element).attr('id') == _this.data.frameId)
				return;
			if ( $(element).closest('.iFrameMenuContainer').length != 0) {
				var clickedMenuId = parseId( $(element).closest('.iFrameMenuContainer') );
				if ( clickedMenuId == _this.data.frameId )
					return;
			}

			_this.closeAction(e);
		},
		
		iFrameOnLoad : function(iFrameEle) {
			var _this = this;
			var frameContent = $(iFrameEle).contents();

			// Detect successful save action of iFrame popup, reload parent page
			var iFrameDoc = $(iFrameEle).get(0).contentWindow || $(iFrameEle).get(0).contentDocument;
			if ( iFrameDoc.location != undefined ) {
				var saveSuccessParamValue = _this.getUrlParam(iFrameDoc.location.href, "nprSaveSuccess");
				if (saveSuccessParamValue == "true") {
					if ($.isFunction( _this.data.onSave )) {
						$("#iFrameMenu_"+_this.data.frameId, getTopFrame().document).find('.iFrameMenuFrameContainer').css('visibility', 'hidden');
						$("#iFrameMenu_"+_this.data.frameId, getTopFrame().document).find('.iFrameMenuProcessingContainer').show();
						//Refresh View iFrame						
						if ( !_this.data.onSave(_this, iFrameDoc.location.href) )
							return;
						//Close iFrame
						_this.closeAction();
					}
				}
			}
			
			$("#iFrameMenu_"+_this.data.frameId, getTopFrame().document).find('.iFrameMenuProcessingContainer').hide();
			$("#iFrameMenu_"+_this.data.frameId, getTopFrame().document).find('.iFrameMenuFrameContainer').css('visibility', '');

			// DEV MODE: Display reload button for iFrame
			if (_this.data.devMode)
				$(frameContent).find('body')
					.append("<div style=\"padding: 5px;\">" +
								"<input id=\"devModeBtn\" type=\"button\" value=\"DEV MODE: RELOAD FRAME\" style=\"width: 150px;\" title=\"Toggle 'devMode' in iFrameMenu init\" onclick=\"javascript:window.location.reload()\" />" +
							"</div>");			
		}
		
		}; // end component
	};
	
	// instance manager
	iFrameMenu_component.inst = {};
	
})(jQuery);	