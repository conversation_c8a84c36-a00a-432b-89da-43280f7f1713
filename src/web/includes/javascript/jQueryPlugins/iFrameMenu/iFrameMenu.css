.iFrameMenuContainer {
	background-color: transparent;
	position: absolute; 
	z-index: 50;
}
.iFrameMenuFrame {
	background-color: transparent;
}

.iFrameMenuFrameContainer {
	background-color: transparent;
}

.fullModalOpen .iFrameMenuFrameContainer {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 50;
	overflow-x: hidden;
	overflow-y: auto;
}

.fullModalOpen .iFrameMenuFrameContainer .iframeMenuContentWrapper {
	position: relative;
}

.iFrameMenuProcessingContainer {
	width: 300px;
	background-color: #fff;
	border: 1px solid #bbb;
    -webkit-border-radius: 10px;  
    -moz-border-radius: 10px;  
    border-radius: 10px;  
	-moz-box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
	-webkit-box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
	box-shadow: 3px 3px 3px 0px rgba(119, 119, 119, 0.15);
	text-align: center;
	color: #999;
	padding: 10px 0px;
}