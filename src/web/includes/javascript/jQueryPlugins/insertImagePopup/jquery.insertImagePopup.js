/** insertImagePopup r1 // 2009.12.01 // jQuery 1.3 // Prinova
 * 
 * Usage:
 * 
 * Dependencies:
 * jQuery 1.3 or later
 * 
 */

var insertImageIntervalSet = false;
var insertPopupJustOpened = false;
var popupLock = false;

(function($) {

	$.fn.insertImagePopup = function(p) {

		p = $.extend({
			type: 'imageOnly'
		  }, p);
		
		return this.each ( function() {
			
			if ( $(this).hasClass('insertPopupInit') )
				return;
			
			var getParameter = function(node) {
				return $(node).attr('insertId');
			};
			
			var parameter = getParameter(this);
			var element = this;

			// Reveal popup if already init or generate new popup
			var initInsertImagePopup = function() {
				if ( !($('#insertImagePopup').length > 0) ) {
					var popupWidth = p.type == 'imageOnly' ? 150 : 300;
					var	popupHTML = '<div id="insertImagePopup" class="popupWindow" style="position: absolute; display: none; width: '+popupWidth+'px;">'+
										'<div align="center" id="loadingContentDiv" class="loadingContent" style="height: auto;">'+
											'<img src="'+context+'/includes/themes/commonimages/tree/loadingAnimation.gif" />'+
										'</div>'+
										'<div align="center" id="insertImageDiv" class="popupContent" style="background: #fff; padding: 5px;">' +
											'INSERT IMAGE HTML' +
										'</div>' +
									'</div>';
					$('body').append(popupHTML);
				}
				var elementPos = getInsertImagePopupPosition(element,-10,-20);
				$('#insertImagePopup').css({top : elementPos[1], left : elementPos[0]});
			};
			
			var rotateImageDisplay = function() {
				if (insertPopupJustOpened) {
					insertPopupJustOpened = false;
					return;
				}
				if ( $('#insertThumbnailDiv_front').length > 0 && $('#insertThumbnailDiv_back').length )
					if ( $('#insertThumbnailDiv_front').is(':visible') ) {
						$('#insertThumbnailDiv_front').hide();
						$('#insertThumbnailDiv_back').showEle('normal');
					} else if ( $('#insertThumbnailDiv_back').is(':visible') ) {
						$('#insertThumbnailDiv_back').hide();
						$('#insertThumbnailDiv_front').showEle('normal');
					}
			};
			
			// Populate popup with retrieved insert image data
			var processInsertImage = function(data) {
				var insertImageHTML = '';
				if (data.getElementsByTagName("insertImageHTML").length > 0) {
					$('#insertImagePopup').find('#loadingContentDiv').hide();
					insertImageHTML = $(data).find('insertImageHTML').text();
					$('#insertImageDiv').html(insertImageHTML);
					$('#insertThumbnailDiv_front').show();
					$('#insertImagePopup').find('#insertImageDiv').showEle("normal");
					$('#insertImagePopup').popupBoundary($('#container'));
					insertPopupJustOpened = true;
					if (!insertImageIntervalSet) {
						window.setInterval(function(){rotateImageDisplay();},2500);
						insertImageIntervalSet = true;
					}
				} 
			};
			
			// Make async call for Insert Image thumbnail
			var requestInsertImageThumbnail = function() {
				if (popupLock)
					return;
				
				initInsertImagePopup();
				
				$('#insertImagePopup').find('#insertImageDiv').hide();
				$('#insertImagePopup').find('#loadingContentDiv').show();
				if (p.marginLeft)
					$('#insertImagePopup').css({marginLeft: p.marginLeft});
				else
					$('#insertImagePopup').css({marginLeft: '100px'});
				$('#insertImagePopup').showEle("normal").popupBoundary($('#container'));

				var stampDate = new Date();
				$.ajax({
					type: "GET",
					url: context+"/getInsertImageThumbnail.form?insertId="+parameter+"&type="+p.type+"&cacheStamp="+(stampDate.getTime()),
					dataType: "xml",
					success: function(data) {
						processInsertImage(data);
					}
				});
			};

			// Hide popup
			var hideInsertImagePopup = function() {
				$('#insertImagePopup').hide();	
			};
			
			var getEventTargetElement = function(e) {
				if (window.event != null) 
					return event.srcElement;
				else
					return e.target;
			};

			var triggerPopupOnHoverIntent = function(hoverTarget) {
				var currentTarget = mouseTarget;
				if (currentTarget == hoverTarget)
					requestInsertImageThumbnail();
			};	
			
			var simpleHoverIntent = function(e) {
				var hoverTarget = getEventTargetElement(e);
				window.setTimeout(function(){triggerPopupOnHoverIntent(hoverTarget);},p.delay ? p.delay : 750);
			};

			$(element).addClass('insertPopupInit');
			$(element).mouseover(simpleHoverIntent).mouseout(hideInsertImagePopup);

		});
	};
})(jQuery);

var mouseTarget;
if (window.attachEvent)
    document.attachEvent("onmousemove",getMouseTarget);
else
    document.addEventListener("mousemove",getMouseTarget,false);

function getMouseTarget(e) {
	if (window.event != null) 
		mouseTarget = event.srcElement;
	else
		mouseTarget = e.target;
}

var mouseX;
if (window.attachEvent)
    document.attachEvent("onmousemove",getMouseX);
else
    document.addEventListener("mousemove",getMouseX,false);

function getMouseX(e) {
	if (!e) e = window.event;
	if (typeof e.pageX == "number")
  		mouseX = e.pageX;
	else
 	 	mouseX = e.clientX;
}

function getInsertImagePopupPosition(element, offsetX, offsetY) {
	var a = new Array(offsetX, offsetY);
	while(element) {
		a[1] += element.offsetTop || 0;
		element = element.offsetParent || null;
	}
	a[0] = mouseX + offsetX - 30;
	return a;
}