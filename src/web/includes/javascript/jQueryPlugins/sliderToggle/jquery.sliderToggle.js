/**
 *  sliderToggle jQuery plugin, targets an action button instance to trigger slider toggle between two divs
 */

$.holdReady(true);

// the semi-colon before function invocation is a safety net against concatenated
// scripts and/or other plugins which may not be closed properly.
;(function ( $, window, document, undefined ) {

    var defaults = {
        toggled: false,
        stateStorageKey	: null,
        actionButton	: null,
        firstPanel		: null,
        secondPanel		: null,
        contentPanel	: null,
        fnBeforeSlide	: null,
        fnAfterSlide   : null,
    };

    // The actual plugin constructor
    function Plugin ( element, options ) {
        this.element = element;
        // jQuery has an extend method which merges the contents of two or
        // more objects, storing the result in the first object. The first object
        // is generally empty as we don't want to alter the default options for
        // future instances of the plugin
        this.settings = $.extend( {}, defaults, options );
        this._defaults = defaults;
        this._name = 'sliderToggle';
        this.init();
    }

    // Avoid Plugin.prototype conflicts
    $.extend(Plugin.prototype, {
        init: function () {

            var settings = this.settings;

            if (!$(this.element).hasClass('iconOnlyToggle')) {
                settings.actionButton = $('#' + $(this.element).attr('id') + "_button");

                if (settings.actionButton.length === 0) {
                    $(this.element).styleActionElement();
                    settings.actionButton = $('#' + $(this.element).attr('id') + "_button");
                }
            } else {
                settings.actionButton = $(this.element);
            }



            settings.stateStorageKey = 'sliderToggle_' + window.location.pathname + '|' + $('#' + settings.firstPanel).attr('id');

            if (settings.secondPanel !== null) {
                settings.stateStorageKey += '|' + $('#' + settings.secondPanel).attr('id');
            }

            var savedState = localStorage.getItem(settings.stateStorageKey);
            settings.toggled = (savedState === "true");

            if (settings.secondPanel !== null) {
                twoPanelToggleInit(settings, settings.toggled);
            } else {
                singlePanelToggleInit(settings, settings.toggled);
            }

            setToggleIcon(settings, savedState);
        }
    });

    // Wrapper to prevent multiple instances of plugin per object
    $.fn['sliderToggle'] = function (method, options) {
        return this.each(function() {
            if ( !$.data( this, "plugin_sliderToggle") ) {
                $.data( this, "plugin_sliderToggle", new Plugin( this, method ) );
            }
            else if (_.isString(method)) {
                $.data(this, "plugin_sliderToggle")[method](options);
            }
        });
    };

    function setToggleIcon(settings) {
        var currentIcon = settings.toggled ? 'fa-toggle-on' : 'fa-toggle-off';
        $(settings.actionButton).find('i').removeClass('fa-toggle-on fa-toggle-off').addClass(currentIcon);
    }

    function twoPanelToggleInit(settings, savedState) {
        var firstPanel = $('#' + settings.firstPanel);
        var secondPanel = $('#' + settings.secondPanel);

        var width = Math.max(firstPanel.outerWidth(), secondPanel.outerWidth(), firstPanel.parent().outerWidth(), secondPanel.parent().outerWidth());
        firstPanel.width(width);
        secondPanel.width(width);

        doBeforeSlide(settings);

        if (savedState !== null) {
            (settings.toggled ? secondPanel : firstPanel).hide();
            (settings.toggled ? firstPanel : secondPanel).css('overflow', 'scroll');
        } else {
            firstPanel.hide();
            secondPanel.css('overflow', 'scroll');
        }

        settings.actionButton.click(function() {
            settings.toggled = !settings.toggled;
            localStorage.setItem(settings.stateStorageKey, settings.toggled);
            setToggleIcon(settings, settings.toggled);
            (settings.toggled ? secondPanel : firstPanel).toggle('slide', function () {
                doBeforeSlide(settings);
                (settings.toggled ? firstPanel : secondPanel).show().effect('slide', function() {
                    (settings.toggled ? firstPanel : secondPanel).css('overflow', 'scroll');
                    doAfterSlide(settings);
                });
            });
        });
    }

    function doBeforeSlide(settings) {
        if (settings.fnBeforeSlide !== null && $.isFunction(settings.fnBeforeSlide)) {
            try {
                settings.fnBeforeSlide(settings);
            } catch (e) {

            }
        }
    }

    function doAfterSlide(settings) {
        if (settings.fnAfterSlide !== null && $.isFunction(settings.fnAfterSlide)) {
            try {
                settings.fnAfterSlide(settings);
            } catch (e) {

            }
        }

        common.refreshParentIframeHeight();
    }

    function singlePanelToggleInit(settings, savedState) {
        var firstPanel = $('#' + settings.firstPanel);
        var contentPanel = $('#' + settings.contentPanel);
        var width = firstPanel.outerWidth(true);
        var contentPanelWidth = contentPanel.outerWidth(true);

        var outsideWidth = width - firstPanel.width();


        firstPanel.css('overflow', 'auto');
        setPanelVisible(savedState, false);

        settings.actionButton.click(function() {
            settings.toggled = !settings.toggled;
            localStorage.setItem(settings.stateStorageKey, settings.toggled);
            setToggleIcon(settings, settings.toggled);
            setPanelVisible(settings.toggled, true);
        });

        function setPanelVisible(visible, animate) {

            firstPanel.animate({
                width: (visible ? + (width - outsideWidth) + 'px' : '0px')
            }, {
                duration: animate ? 400 : 0,
                start: function() {

                    if (visible) {
                        contentPanel.css('max-width', (contentPanelWidth - width - outsideWidth) + 'px');
                    }

                    doBeforeSlide(settings);

                    if (visible) {
                        firstPanel.css('display', 'inline');
                        firstPanel.css('overflow', 'scroll');
                    }
                },
                done: function() {
                    if (!visible) {
                        firstPanel.css('display', 'none');
                        contentPanel.css('max-width', '');
                    }

                    doAfterSlide(settings);
                }
            }) ;

            contentPanel.animate({
                marginLeft: (visible ? width + 'px' : '0px' )
            }, animate ? 400 : 0);
        }
    }

    $.holdReady(false);

})( jQuery, window, document );

//# sourceURL=jquery.sliderToggle.js