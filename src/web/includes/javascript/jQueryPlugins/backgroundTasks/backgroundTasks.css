.bgTask_listContainer {
	position: relative;
	max-height: 350px;
	overflow: auto;
}

.bgTask_listContainer .bgTask_header:first-child {
	border-top: 0;
}

i.bgtask_trayIcon {
	font-size: 16px;
	cursor: pointer;
}
i.bgtask_trayIcon:hover {
	color: #fff;
}

i.bgtask_icon {
	position: relative;
	top: 9px;
	font-size: 14px;
	margin: 0px 14px 0px 0;
	width: 14px;
	height: 14px;
	line-height: 14px;
	padding: 4px;
	text-align: center;
	background: #606060;
	color: #fff;
	border-radius: 50%;
}
i.bgtask_icon.fa-ban {
	background: #c93237;
}
i.bgtask_icon.fa-check {
	background: #459e49;
	font-size: 12px;
	line-height: 14px;
}
i.bgtask_removeTaskIcon {
	display: none;
	position: absolute;
	right: 12px;
	top: 14px;
	padding: 4px;
	cursor: pointer;
	font-size: 12px;
}
i.bgtask_downloadIcon {
	padding-left: 10px;
	cursor: pointer;
	font-size: 12px;
	position: relative;
	top: 1px;
}
i.bgtask_removeTaskIcon:hover, .i.bgtask_downloadIcon:hover {
	color: #606060;
}
.bgTask_taskContainer:hover i.bgtask_removeTaskIcon {
}

.bgTask_taskLabel {
	font-size: 12px;
	color: #444;
	font-weight: 600;
}
.bgTask_taskDescription {
	font-size: 11px;
	color: #444;
	margin: 0 0 0 36px;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}
.bgTask_taskContainer {
}
.bgTask_taskContainer:hover {
}
.bgTask_progressContainer {
	white-space: nowrap;
	margin: 8px 0 0px;
	height: 8px;
	line-height: 6px;
}
.bgTask_progressBar {
	width: 200px;
	height: 8px;
	background-color: #d6c7de;
	border-radius: 4px;
	display: inline-block;
}
.bgTask_progressIndicator {
	background-color: #6d3075;
	font-weight: 700;
	text-align: center;
	vertical-align: middle;
	color: #fff;
	font-size: 6px;
	border-radius: 4px;
	line-height: 8px;
}
.bgTask_runtime {
	padding-left: 6px;
	color: #606060;
	font-size: 8px;
}
.bgTask_header {
	color: #444;
	font-size: 12px;
	font-weight: 600;
	padding: 10px 18px 9px;
	background: #f5f5f5;
	border-top: solid 1px #dadada;
}