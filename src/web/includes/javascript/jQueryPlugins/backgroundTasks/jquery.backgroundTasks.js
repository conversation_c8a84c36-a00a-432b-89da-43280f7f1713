/** backgroundTasks r1 // 2015.09.10 // jQuery 1.11 // Prinova
 *
 * Dependencies:
 * jQuery 1.11 or later
 *
 */

(function ($) {
    var $backgroundTasksPlaceholder = $('#backgroundTasksPlaceholder');
    if ( $backgroundTasksPlaceholder.length === 0 ){
        return;
    }

    $backgroundTasksPlaceholder.dropdown();

    var taskDetailsTemplate =
        '<div class="modal fade" id="taskDetailsModal" tabindex="-1" role="dialog" aria-labelledby="taskDetailsModalLabel" aria-hidden="true">' +
            '<div class="modal-dialog modal-sm" role="document">' +
                '<div class="modal-content">' +
                    '<div class="bg-ribbon position-absolute rounded-top py-1 w-100" role="presentation"></div>' +
                    '<div class="modal-header border-0">' +
                        '<h4 class="modal-title" id="taskDetailsModalLabel">' + client_messages.text.task_details + '</h4>' +
                        '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
                            '<i class="far fa-times m-2" aria-hidden="true"></i>' +
                        '</button>' +
                    '</div>' +
                    '<div class="modal-body pt-0">' +
                        '<div class="row no-gutters">' +
                            '<div class="col pr-3">' +
                                '<span class="text-info" aria-label="' + client_messages.text.date + '">' +
                                    '<span class="task-date mr-2"></span>' +
                                    '<i class="task-date-icon far fs-md ml-1" aria-hidden="true"></i>' +
                                '</span>' +
                                '<div class="task-name" aria-label="' + client_messages.text.name + '"></div>' +
                                '<div class="task-description text-muted" aria-label="' + client_messages.text.description + '"></div>' +
                            '</div>' +
                            '<div class="col-auto pt-1" aria-label="' + client_messages.text.status + '">' +
                                '<div class="task-icon square-lg rounded-circle p-2 text-center text-white fs-md" role="presentation">' +
                                    '<i class="fas" aria-hidden="true"></i>' +
                                '</div>' +
                                '<span class="task-status sr-only"></span>' +
                            '</div>' +
                        '</div>' +
                        '<div class="task-report d-none bg-lightest rounded mt-3 py-2">' +
                            '<div class="px-3 py-1">' +
                                '<small class="d-block text-uppercase text-muted font-weight-bold">' + client_messages.text.report + '</small>' +
                                '<div class="overflow-auto">' +
                                    '<a class="d-inline-block text-decoration-none text-nowrap">' +
                                        '<i class="far fa-download mr-2" aria-hidden="true"></i>' +
                                        '<span class="task-file fs-xs text-monospace"></span>' +
                                    '</a>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>' +
        '</div>',
        $taskDetailsModal = $(taskDetailsTemplate);

    $taskDetailsModal.on('show.bs.modal', function () {

        var $this = $(this),
            $trigger = $($this.data('trigger')),
            taskName = $trigger.data('taskname'),
            taskDescription = $trigger.data('taskdescription'),
            taskDate = $trigger.data('taskdate'),
            taskStatus = $trigger.data('taskstatus'),
            dateTimeIcon = 'fa-clock',
            taskPath = $trigger.data('taskpath'),
            fileName = $trigger.data('filetitle');

        $trigger.closest('.dropdown-menu').removeClass('show').closest('.nav-item').removeClass('show');

        if (taskStatus === "complete") {
            if (fileName.indexOf(".xml") !== -1) {
                taskPath = context + '/download/xml.form?resource=' + taskPath;
            } else if (fileName.indexOf(".xlsx") !== -1) {
                taskPath = context + '/download/excel.form?resource=' + taskPath;
            } else if (fileName.indexOf(".zip") !== -1) {
                taskPath = context + '/download/zip.form?resource=' + taskPath;
            } else if (fileName.indexOf(".log") !== -1) {
                taskPath = context + '/download/log.form?resource=' + taskPath;
            } else {
                taskPath = context + '/download/pdf.form?resource=' + taskPath;
            }
        } else {
            taskPath = context + '/download/xml.form?resource=' + taskPath;
        }

        $this.find('.task-name').text(taskName);
        $this.find('.task-description').text(taskDescription);
        $this.find('.task-date').text(taskDate);
        $this.find('.task-status').text(taskStatus);
        $this.find('.task-file').text(fileName).closest('a').attr('href', taskPath);

        if (fileName !== 'undefined') {
            $this.find('.task-report').removeClass('d-none');
        } else {
            $this.find('.task-report').addClass('d-none');
        }

        if(!taskDate.match(/(1[0-2]|0?[1-9]):([0-5][0-9]) ([AaPp][Mm])/g)){
            dateTimeIcon = 'fa-calendar-alt';
        }

        var icon = '',
            iconBg = '';

        if (taskStatus === "complete") {
            icon = 'fa-check';
            iconBg = 'bg-success';
        } else if (taskStatus === "error") {
            icon= 'fa-times';
            iconBg = 'bg-danger';
        } else if (taskStatus === "in_process") {
            icon = 'fa-cog fa-spin';
            iconBg = 'bg-secondary';
        }

        $this.find('.task-date-icon')
            .removeClass('fa-clock fa-calendar-alt')
            .addClass(dateTimeIcon);

        $this.find('.task-icon').removeClass('bg-success bg-danger bg-secondary').addClass(iconBg)
            .children().removeClass('fa-check fa-times fa-cog fa-spin').addClass(icon);

    }).on('hidden.bs.modal', function () {

        var $this = $(this),
            $trigger = $($this.data('trigger'));

        $trigger.closest('.dropdown-menu').addClass('show').closest('.nav-item').addClass('show');
        $trigger.trigger('focus');

    }).on('click', function (e) {

        e.stopPropagation();

    });

    $('body').append($taskDetailsModal);

    $.fn.refresh = function () {
        var _this = getTopFrame().$.backgroundTasks.get($(this));
        if (_this) {
            _this.requestTaskData();
        }
    };

    $.fn.refreshIfNotInProgress = function () {
        var _this = getTopFrame().$.backgroundTasks.get($(this));
        if (_this) {
        	if (!_this.isInProgress()) {
                var $dropdownMenu = _this.targetEle.siblings('.dropdown-menu');
                var $dropdownContent = $dropdownMenu.children('.dropdown-content');
                $dropdownContent.html('<div id="progress-loader" class="text-center pt-3 pb-2 mb-1">' +
                    '<div class="progress-loader progress-loader-md">' +
                    '<i class="progress-loader-icon far fa-spinner-third" aria-hidden="true"></i>' +
                    '</div>' +
                    '</div>');

                _this.requestTaskData();
        	}
        }
    };
    
    $.backgroundTasks = {
        get: function (obj) {
            var o = $(obj);
            if (!o.size()){
                o = $("#" + obj);
            }
            if (!o.size()) {
                return null;
            }
            return backgroundTasks_component.inst[o.attr('id')] || null;
        },
        defaults: {}
    };

    $.fn.backgroundTasks = function (opts) {
        return this.each(function () {
            var conf = $.extend({}, opts);
            if (conf !== false) {
                new backgroundTasks_component().init(this, conf);
            }
        });
    };

    function backgroundTasks_component() {
        return {

            data: $.extend({}, $.backgroundTasks.defaults),

            init: function (elem, conf) {
                var _this = this;

                backgroundTasks_component.inst[$(elem).attr("id")] = _this;
                _this.data = $.extend(false, {}, this.data, conf);
                _this.targetEle = $(elem);

                _this.inProcessTasks = [];
                _this.isInit = true;

                _this.requestTaskData();

                _this.targetEle.parent().off('show.bs.dropdown').on('show.bs.dropdown', function () {

                    _this.popupTasksList();

                }).off('hidden.bs.dropdown').on('hidden.bs.dropdown', function () {

                    var $dropdownContent = _this.targetEle.siblings('.dropdown-menu').children('.dropdown-content');
                    $dropdownContent.removeClass('completed-task');

                });
            },

            downloadTaskResult: function (taskId) {
                var _this = this;

                for (var i = 0; i < _this.task_data.tasks.length; i++) {
                    var task = _this.task_data.tasks[i];
                    if (task.id == taskId) {
                        var downloadForm;
                        if (task.status === "complete") {
                            var downloadType = task.downloadType.toLowerCase();
                            if (downloadType === "xml" || downloadType === "zip" || downloadType === "log") {
                                downloadForm = downloadType;
                            } else if (downloadType === "xlsx" || downloadType === "csv") {
                                downloadForm = "excel";
                            } else {
                                downloadForm = "pdf";
                            }
                        }
                        if (task.status === "error") {
                            downloadForm = "xml";
                        }

                        javascriptHref(context + '/download/' + downloadForm + '.form?resource=' + task.resource);
                    }
                }
            },

            showDetailTask : function(taskId) {
                var _this = this;
                for (var i = 0; i < _this.task_data.tasks.length; i++) {
                    var task = _this.task_data.tasks[i];
                    if (task.id == taskId && task.status === "in_process" && task.hasDetailInfo) {
                        javascriptHref(context + '/getBackgroundTaskDetail.form?task_thread_id=' + task.thread_id );
                        break;
                    }
                }
            },

            getTaskDisplayItem: function (task) {
                var icon = '',
                    iconBg = '';
                if (task.status === "complete") {
                    icon = 'fa-check';
                    iconBg = 'bg-success';
                } else if (task.status === "error") {
                    icon= 'fa-times';
                    iconBg = 'bg-danger';
                } else if (task.status === "in_process") {
                    icon = 'fa-cog';
                    iconBg = 'bg-secondary fa-spin';
                }

                var iconHTML = '<div class="flex-shrink-0" aria-label="' + client_messages.text.status + '">' +
                        '<div class="square-lg rounded-circle ' + iconBg + ' text-center text-white" role="presentation">' +
                            '<i class="fas ' + icon + ' fa-xs" aria-hidden="true"></i>' +
                        '</div>' +
                        '<span class="sr-only">' + task.status + '</span>' +
                    '</div>';

                var removeTaskIconHTML = "";
                if (task.my_task) {
                    if (task.status === "complete" || task.status === "error") {
                        removeTaskIconHTML = '<span class="ml-2 pl-1">' +
                                '<a href="#" class="bgtask_removeTaskIcon" role="button" data-toggle="tooltip" title="' + client_messages.text.remove_item + '">' +
                                    '<i class="far fa-trash-alt" aria-hidden="true"></i>' +
                                    '<span class="sr-only">' + client_messages.text.remove_item + '</span>' +
                                '</a>' +
                            '</span>';
                    }
                }
                var downloadIconHTML = "";
                if (task.resource || task.logFilePath) {
                    if (task.logFilePath && !task.resource) {
                        task.resource = task.logFilePath;
                        task.downloadType = "log";
                        task.fileTitle = task.logFileTitle;
                    }

                    downloadIconHTML = '<span class="ml-2 pl-1">' +
                            '<a href="#" class="bgtask_downloadIcon" role="button" data-toggle="tooltip" title="' + client_messages.text.download + '">' +
                                '<i class="far fa-download" aria-hidden="true"></i>' +
                                '<span class="sr-only">' + client_messages.text.download + '</span>' +
                            '</a>' +
                        '</span>';
                }

                var detailIconHTML = "";
                if ( task.status === "in_process" && task.hasDetailInfo ) {
                    detailIconHTML = '<span class="ml-2 pl-1">' +
                                        '<a href="#" class="bgtask_detailIcon" role="button" data-toggle="tooltip" title="' + client_messages.text.show_details + '">' +
                                            '<i class="far fa-window-restore" aria-hidden="true"></i>' +
                                            '<span class="sr-only">' + client_messages.text.show_details + '</span>' +
                                        '</a>' +
                                    '</span>';
                }

                var progressBarHTML = '';

                if (task.status === "in_process") {
                    progressBarHTML = '<div class="d-flex align-items-center w-100">' +
                                            '<div class="progress w-100 mr-3" style="height: 13px; font-size: 0.65rem;">' +
                                                '<div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ' +
                                                'style="width: ' + task.progress + '%" aria-valuenow="' + task.progress + '" aria-valuemin="0" aria-valuemax="100">' +
                                                    '<span style="margin-top: 2px;">' + task.progress + '%' + '</span>' +
                                                '</div>' +
                                            '</div>' +
                                            '<small class="text-muted">' +
                                                '<span style="font-size:85%; font-weight: 500;">' + task.execution_time + '</span>' +
                                            '</small>' +
                                        '</div>';
                }

                var taskHTML = '<div class="dropdown-item dropdown-item-multiline c-pointer" tabindex="0" data-toggle="modal"' +
                                    ' data-target="#taskDetailsModal" data-taskname="' + task.task_name + '" data-filetitle="' + task.fileTitle + '" data-taskpath="' + task.resource + '"' +
                                    ' data-taskdescription="' + task.description + '" data-taskdate="' + task.request_date + '" data-taskstatus="' + task.status + '">' +
                                        '<div class="d-flex align-items-center py-1">' +
                                            iconHTML +
                                            '<div class="w-100 pl-3 overflow-hidden">' +
                                                '<div class="row no-gutters align-items-center">' +
                                                    '<span class="col text-truncate" aria-label="' + client_messages.text.name + '">' + task.task_name + '</span>' +
                                                '<div class="col-auto">' +
                                                    detailIconHTML +
                                                    downloadIconHTML +
                                                    removeTaskIconHTML +
                                                '</div>' +
                                            '</div>' +
                                            '<div class="text-muted text-nowrap text-truncate" aria-label="' + client_messages.text.date_and_description + '">' +
                                                '<span class="font-weight-light">' + task.request_date + ((task.description)? ' &mdash; </span>' + task.description : '</span>') +
                                            '</div>' +
                                            progressBarHTML +
                                            '</div>' +
                                        '</div>' +
                                '</div>';

                return taskHTML;
            },

            renderTaskListDisplay: function () {
                var _this = this,
                    $dropdownMenu = _this.targetEle.siblings('.dropdown-menu'),
                    $dropdownContent = $dropdownMenu.children('.dropdown-content');
                var tasks = _this.task_data.tasks;
                if (tasks.length !== 0) {
                    var taskList = null;
                    if (!$('.bgTask_listContainer').length) {
                        taskList = $("<div class=\"bgTask_listContainer\"></div>");
                        $dropdownContent.append(taskList);
                    } else {
                        taskList = $('.bgTask_listContainer');
                    }
                    if ($('.justCompleted').length) {
                        $('.justCompleted').next('div').remove();
                        $('.justCompleted').remove();
                    }
                    var hasTaskInProcessState = tasks.some(function(task){
                        return task.status === 'in_process';
                    });
                    var hasTaskInCompleteState = tasks.some(function(task){
                        return task.status === 'complete';
                    });
                    var hasTaskInErrorState = tasks.some(function(task){
                        return task.status === 'error';
                    });
                    if ($('#inProcessTaskList').length === 0 && hasTaskInProcessState) {
                        var processTaskListHTML =
                            '<div id="inProcessTaskList">' +
                                '<div id="inProcessTaskTitle" class="bg-lightest px-4 py-2 border-bottom border-light" role="heading">' +
                                    '<b>' + client_messages.text.in_progress + '</b>' +
                                '</div>' +
                            '</div>';
                        if ($('#completeTaskList').length) {
                            $(processTaskListHTML).insertBefore($('#completeTaskList'));
                        } else if ($('#errorTaskList').length) {
                            $(processTaskListHTML).insertBefore($('#errorTaskList'));
                        } else {
                            taskList.append(processTaskListHTML);
                        }
                    } else if (!hasTaskInProcessState) {
                        $('#inProcessTaskList').remove();
                    }

                    if ($('#completeTaskList').length === 0 && hasTaskInCompleteState) {
                        var completeTaskListHTML =
                            '<div id="completeTaskList">' +
                                '<div id="completeTaskTitle" class="bg-lightest px-4 py-2 border-bottom border-light" role="heading">' +
                                    '<b>' + client_messages.text.complete + '</b>' +
                                '</div>' +
                            '</div>';
                        if ($('#errorTaskList').length) {
                            $(completeTaskListHTML).insertBefore($('#errorTaskList'));
                        } else {
                            taskList.append(completeTaskListHTML);
                        }
                    } else if (!hasTaskInCompleteState) {
                        $('#completeTaskList').remove();
                    }
                    if ($('#errorTaskList').length === 0 && hasTaskInErrorState) {
                        taskList.append(
                            '<div id="errorTaskList">' +
                                '<div id="errorTaskTitle" class="bg-lightest px-4 py-2 border-bottom border-light" role="heading">' +
                                    '<b>' + client_messages.text.error + '</b>' +
                                '</div>' +
                            '</div>'
                        );
                    } else if (!hasTaskInErrorState) {
                        $('#errorTaskList').remove();
                    }

                    tasks.reverse().map(function(task) {
                        var taskList = "#inProcessTaskList";
                        if (task.status === "complete") {
                            taskList = "#completeTaskList";
                        } else if (task.status === "error") {
                            taskList = "#errorTaskList";
                        }
                        var taskContainer = '<div ' +
                            'id="bgTask_' + task.id + '"' +
                            'class="bgTask_taskContainer"' +
                            'data-taskprogress="' + task.progress + '"' +
                            'data-taskstatus="' + task.status + '">' + _this.getTaskDisplayItem(task) + '</div>' +
                            '<div class="dropdown-divider m-0" role="separator"></div>';

                        var $task = $("#bgTask_" + task.id);

                        if ($task.length === 0) {
                            $(taskList + '>div:first-child').after(taskContainer);
                        } else {
                            if ($task.data('taskprogress') !== task.progress) {
                                $task.html(_this.getTaskDisplayItem(task));
                            }
                            if ($task.data('taskstatus') !== task.status) {
                                $task.next('div').remove();
                                $task.remove();
                                $(taskList + '>div:first-child').after(taskContainer);
                            }
                        }
                    });


                    taskList.find('.bgtask_removeTaskIcon:not(.clickInit)').addClass('clickInit').click(function (e) {

                        var $this = $(this),
                            $taskContainer = $this.closest('.bgTask_taskContainer');

                        $this.tooltip('dispose').off();
                        _this.removeTask(parseId($taskContainer));

                        $taskContainer.next('.dropdown-divider').remove();
                        $taskContainer.remove();

                        e.stopPropagation();

                    });

                    taskList.find('.bgtask_downloadIcon').off('click.downloadTask').on('click.downloadTask', function (e) {

                        _this.downloadTaskResult(parseId($(this).closest('.bgTask_taskContainer')));
                        e.stopPropagation();

                    });

                    taskList.find('.bgtask_detailIcon').click(function(e) {

                        _this.showDetailTask(parseId($(this).closest('.bgTask_taskContainer')));
                        e.stopPropagation();

                    });

                    // Init item detail tool tip
                    taskList.find('[data-toggle="tooltip"]').each(function () {

                        $(this).tooltip();

                    });

                    taskList.find('.dropdown-item').on('click keypress', function(e) {

                        e.stopPropagation();

                        if(e.type === 'click' || (e.type === 'keypress' && e.which === 13)) {

                            $taskDetailsModal.data('trigger', this);
                            $taskDetailsModal.modal('show');

                        }

                    });

                    $('#backgroundTaskManagerNav').removeClass('d-none').addClass('d-flex');

                } else {

                    taskList = $('<div class="bgTask_listContainer">' +
                        '<div class="mt-3 mb-2 mx-4 py-2 bg-lightest text-muted rounded text-center">' + client_messages.text.none + '</div>' +
                        '</div>');

                }

                return taskList;
            },

            popupStatus: function () {
                var _this = this,
                    $dropdownMenu = _this.targetEle.siblings('.dropdown-menu'),
                    $dropdownContent = $dropdownMenu.children('.dropdown-content');

                var scrollPosition = null;

                if ($('.bgTask_listContainer').length) {
                    scrollPosition = $('.bgTask_listContainer').scrollTop();
                }

                var taskListVisible = $dropdownContent.is('.tasks-list') && _this.targetEle.parent().is('.show');
                if (_this.completeTasks.length !== 0 && !taskListVisible) {
                    var tasksHTML = "";
                    for (var i = 0; i < _this.completeTasks.length; i++) {
                        var task = _this.task_data.tasks.find(function(t) {
                            return t.id === _this.completeTasks[i];
                        });
                        if (task) {
                            tasksHTML +='<div ' +
                                    'id="bgTask_' + task.id + '"' +
                                    'class="bgTask_taskContainer justCompleted"' +
                                    'data-taskprogress="' + task.progress + '"' +
                                    'data-taskstatus="' + task.status + '">' + _this.getTaskDisplayItem(task) + '</div>'+
                                    '<div class="dropdown-divider m-0" role="separator"></div>';
                        }
                    }

                    var taskList = $("<div class=\"bgTask_listContainer\"></div>");
                    taskList.html(tasksHTML);

                    taskList.find('.bgtask_removeTaskIcon').click(function (e) {
                        var $this = $(this),
                            $taskContainer = $this.closest('.bgTask_taskContainer');

                        $this.tooltip('dispose').off();
                        _this.removeTask(parseId($taskContainer));

                        $taskContainer.next('.dropdown-divider').remove();
                        $taskContainer.remove();
                        e.stopPropagation();

                    });

                    taskList.find('.bgtask_downloadIcon').off('click.downloadTask').on('click.downloadTask', function (e) {
                        _this.downloadTaskResult(parseId($(this).closest('.bgTask_taskContainer')));
                        e.stopPropagation();
                    });

                    taskList.find('.dropdown-item').on('click keypress', function(e) {

                        e.stopPropagation();

                        if(e.type === 'click' || (e.type === 'keypress' && e.which === 13)) {

                            $taskDetailsModal.data('trigger', this);
                            $taskDetailsModal.modal('show');

                        }

                    });

                    $dropdownMenu.children('.dropdown-header').text(client_messages.title.task_complete);

                    $dropdownContent.find('[data-toggle="tooltip"]').tooltip('dispose');
                    $dropdownContent.html(taskList).removeClass('tasks-list').addClass('completed-task');
                    $('#backgroundTaskManagerNav').addClass('d-none').removeClass('d-flex');

                    $taskDetailsModal.modal('hide');
                    _this.targetEle.dropdown('toggle');

                } else if (taskListVisible || (_this.newTasks.length !== 0 && _this.isInit)) {
                    _this.popupTasksList();

                    if($dropdownContent.is(':hidden')){
                        _this.targetEle.dropdown('toggle');
                    }

                    _this.targetEle.parent().tooltip('hide').tooltip('show');

                }

                if (scrollPosition !== null) {
                    $('.bgTask_listContainer').scrollTop(scrollPosition);
                }
            },

            popupTasksList: function () {
                var _this = this,
                    $dropdownMenu = _this.targetEle.siblings('.dropdown-menu'),
                    $dropdownContent = $dropdownMenu.children('.dropdown-content');

                $dropdownContent.find('[data-toggle="tooltip"]').tooltip('dispose');

                if(!$dropdownContent.is('.completed-task') && (typeof _this.task_data !== 'undefined')) {
                    $dropdownMenu.children('.dropdown-header').text(client_messages.title.background_processes);
                    _this.renderTaskListDisplay();
                    $dropdownContent.removeClass('completed-task').addClass('tasks-list');
                }
            },

            isInProgress: function() {
            	var data = this.task_data;
            	if(data && data.tasks) {
                    return data.tasks.some(function(task) {
                        return task.status === "in_process";
                    });
            	}
                
                return false;
            },
            
            processTaskData: function (data) {
                var _this = this;

                _this.task_data = data;

                var inProcess = false;
                var refreshPage = false;
                var currentInProcess = [];
                var completeTasks = [];
                var newTasks = [];

                for (var i = 0; i < data.tasks.length; i++) {
                    if (data.tasks[i].status === "in_process") {
                        inProcess = true;
                        currentInProcess.push(data.tasks[i].id);
                    }
                    if (data.tasks[i].is_new){
                        newTasks.push(data.tasks[i].id);
                    }

                    if (data.tasks[i].refresh_page) {
                        refreshPage = true;
                        if (data.tasks[i].task_type === 8) {		// Rationalizer Application Clone
                            var url = window.location.href;
                            if (url.indexOf('rationalizer_documents_list.form') >= 0 || url.indexOf('rationalizer_query_list.form') >= 0) {
                                if (getParam('rationalizerApplicationId') !== data.tasks[i].target_obj_id) {
                                    refreshPage = false;
                                }
                            } else {
                                refreshPage = false;
                            }
                        }
                    }
                }

                var $indicator = _this.targetEle.children('.indicator');
                $indicator.removeClass('fa-cog fa-spin fa-bell fa-bell-slash fa-spinner fa-swap-opacity').addClass('fa-spinner fa-spin');
                $('#progress-loader').remove();
                if (inProcess) {
                    $indicator.removeClass('fa-cog fa-spin fa-bell fa-bell-slash fa-spinner fa-swap-opacity').addClass('fa-cog' + (!$.browser.chrome ? ' fa-spin' : ''));
                    _this.targetEle.parent().attr('data-original-title', common.fmtClientMessage(client_messages.text.background_tasks_in_progress, currentInProcess.length));
                    $('.tooltip .tooltip-inner').html(common.fmtClientMessage(client_messages.text.background_tasks_in_progress, currentInProcess.length));
                } else if (! data.has_task) {
                    $indicator.removeClass('fa-cog fa-spin fa-bell fa-bell-slash fa-spinner fa-swap-opacity').addClass('fa-bell-slash fa-swap-opacity');
                    common.disableElement(_this.targetEle);
                    _this.targetEle.parent().attr('data-original-title', common.fmtClientMessage(client_messages.text.no_background_tasks));
                    _this.targetEle.append('<span class="sr-only">' + common.fmtClientMessage(client_messages.text.no_background_tasks) + '</span>');
                } else {
                    $indicator.removeClass('fa-cog fa-spin fa-bell fa-bell-slash fa-spinner fa-swap-opacity').addClass('fa-bell fa-swap-opacity');
                    _this.targetEle.parent().attr('data-original-title', common.fmtClientMessage(client_messages.text.background_tasks, data.tasks.length))
                }

                if (data.tasks.length > 0){
                    _this.targetEle.find('.badge').html(data.tasks.length + ' <span class="sr-only">' + client_messages.title.tasks + '</span>');
                } else {
                    _this.targetEle.find('.badge').html('');
                }

                if (!_this.isInit) {
                    for (var i = 0; i < _this.inProcessTasks.length; i++){
                        if (currentInProcess.indexOf(_this.inProcessTasks[i]) === -1){
                            completeTasks.push(_this.inProcessTasks[i]);
                        }
                    }
                }

                _this.inProcessTasks = currentInProcess;
                _this.newTasks = newTasks;
                _this.completeTasks = completeTasks;


                // TODO: Verify this
                // Verify if the popup tasklist is not present in the DOM (visible) and if the popup taskComplete os visible in the main document(no iframe)
                // If it's visible remove it from the dom y load it again
                // If not load it again

                /*if ($('#tasksList').length == 0 && $(getTopFrame().document).find('#tasksComplete.popupFactory_popupContainer').length != 0)
                    $(getTopFrame().document).find('#tasksComplete.popupFactory_popupContainer').fadeOut(500, function () {
                        $(this).remove();
                        _this.popupStatus();
                    });
                else
                    _this.popupStatus();*/

                _this.popupStatus();

                if (inProcess) {
                    setTimeout(function () {
                        _this.isInit = false;
                        _this.requestTaskData();
                    }, 3000);
                }

                if (refreshPage) {
                    getTopFrame().location.reload();
                }
            },

            removeTask: function (id) {
                var _this = this;

                var stampDate = new Date();

                var csrfToken = getParam('tk');

                if (csrfToken === "") {
                    return;
                }

                $.ajax({
                    type: "GET",
                    url: context + "/getBackgroundTask.form" +
                    "?action=remove" +
                    "&task_id=" + id +
                    "&tk=" + csrfToken +
                    "&cacheStamp=" + (stampDate.getTime()),
                    dataType: "json",
                    success: function () {
                        for (var i = 0; i < _this.task_data.tasks.length; i++) {
                            var task = _this.task_data.tasks[i];
                            if (task.id === id) {
                                _this.task_data.tasks.splice(i, 1);
                                break;
                            }
                        }
                        _this.requestTaskData();
                        _this.targetEle.parent().tooltip('hide');
                    }
                });
            },

            requestTaskData: function () {
                var _this = this;

                var stampDate = new Date();

                var csrfToken = getParam('tk');

                if (csrfToken === "") {
                    return;
                }

                var targetEleId = _this.targetEle.attr('id');
                var myTask = $('div[aria-labelledby="' + targetEleId + '"]').children().find('.selectMyTask').hasClass('active');
                
                if(_this.data.hasOwnProperty('wasMyTask') && _this.data.wasMyTask !== myTask) {
                	_this.isInit = true;
                }
                _this.data.wasMyTask = myTask;
                
                $.ajax({
                    type: "GET",
                    url: context + "/getBackgroundTask.form" +
                    "?action=task_data" +
                    "&my_task=" + myTask +
                    "&tk=" + csrfToken +
                    "&cacheStamp=" + (stampDate.getTime()),
                    dataType: "json",
                    success: function (data) {
                        _this.processTaskData(data);
                    }
                });
            }

        }; // end component
    }

    // instance manager
    backgroundTasks_component.inst = {};

})(jQuery);	