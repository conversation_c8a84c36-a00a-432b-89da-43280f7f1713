/** fastNavigation r1 // 2022.08.10 // jQuery 1.11 // Prinova
 *
 * Dependencies:
 * jQuery 1.11 or later
 *
 */

(function ($) {

    $.fastNavigation = {
        get: function (obj) {
            var o = $(obj);
            if (!o.size()) o = $("#" + obj);
            if (!o.size()) return null;
            return fastNavigation_component.inst[o.attr('id')] || null;
        },
        defaults: {}
    };

    $.fn.fastNavigation = function (opts) {
        return this.each(function () {
            var conf = $.extend({}, opts);
            if (conf !== false) new fastNavigation_component().init(this, conf);
        });
    };

    function fastNavigation_component() {
        return {

            data: $.extend({}, $.fastNavigation.defaults),

            linkAction: function (href) {
                var callback = function() {
                    window.location.href = href;
                };

                // Adding a delay to prevent false-positives with serializedObservableForm
                setTimeout(function(){
                    if(window.serializedObservableForm !== undefined) {
                        var saveSuccess = new URL(window.location.href).searchParams.get('saveSuccess');
                        var isViewMode = window.location.href.includes("content_object_view");
                        serializedObservableForm.checkIfNeedSave();

                        if (serializedObservableForm.needsSave && saveSuccess === null && !isViewMode) {
                            historyQueue.showContinueConfirmationModal(callback);
                        } else {
                            callback();
                        }

                    } else {
                        callback();
                    }
                }, 200);

            },

            init: function (elem, conf) {
                var _this = this;
                fastNavigation_component.inst[$(elem).attr("id")] = _this;
                _this.data = $.extend(false, {}, this.data, conf);
                _this.targetEle = $(elem);

                var buttons = "<button type='button' class='btn-fast-view btn btn-info btn-sm px-1 py-0' aria-label='Fast View' style='box-sizing: border-box;'>" +
                    "<i class='fad fa-spinner-third fa-spin fa-fw fa-sm' aria-hidden='true'></i>" +
                    "</button>";

                buttons += "<button type='button' class='btn-fast-edit btn btn-info btn-sm px-1 py-0' aria-label='Fast Edit' style='box-sizing: border-box;'>" +
                    "<i class='fad fa-spinner-third fa-spin fa-fw fa-sm' aria-hidden='true'></i>" +
                    "</button>";

                var $group = $("<div class='position-absolute btn-group-vertical pr-1 z-index-1 fast-nav-container' contenteditable='false' style='transform: translate(-85%, 3%);'></div>");

                $group.append(buttons);

                _this.data.type = $(elem).attr('type') == "10" ? 'local_content' : 'embedded_content';

                var mouseLeaveDelay;

                $(_this.targetEle)
                    .css({'position': 'relative'})
                    .on('mouseenter', function () {
                        if($(this).hasClass('fastNavigation')) {
                            clearTimeout(mouseLeaveDelay);

                            // Adding a delay to display buttons to prevent false-positives with serializedObservableForm
                            setTimeout(function () {
                                $(_this.targetEle).prepend($group);
                                _this.btnGroup = $(_this.targetEle).children('.btn-group-vertical');
                                _this.viewBtn = _this.btnGroup.children('.btn-fast-view');
                                _this.editBtn = _this.btnGroup.children('.btn-fast-edit');
                                if (!_this.btnGroup.is('.init') && !_this.btnGroup.is('.loading')) {
                                    _this.requestEditableStatus();
                                }
                                _this.setupGotoLinks();
                            }, 400);
                        }
                    })
                    .on('mouseleave', function () {
                        function removeBtnGroup() {
                            if (_this.btnGroup && !_this.btnGroup.is('.loading')) {
                                _this.btnGroup.remove();
                            } else {
                                mouseLeaveDelay = setTimeout(removeBtnGroup, 400);
                            }
                        }
                        mouseLeaveDelay = setTimeout(removeBtnGroup, 400);
                    });

            },

            setupGotoLinks: function () {
                var _this = this,
                    modelId = $(_this.targetEle).attr('id'),
                    objectDna = $(_this.targetEle).attr('dna'),
                    docId = $(_this.targetEle).data('document_id');

                if (_this.data && _this.data.working_copy_init_required !== undefined && _this.data.reassign_required !== undefined) {
                    var params = {
                        statusViewId: '1',
                        initWorkingCopy: _this.data.working_copy_init_required.toString(),
                        reassign: _this.data.reassign_required.toString(),
                        tk: gup('tk'),
                    };
                    if(modelId){
                        $.extend(params, {modelId: modelId});
                    }
                    if(objectDna) {
                        $.extend(params, {objectDna: objectDna});
                    }
                    var queryString = Object.keys(params).map(key => key + '=' + params[key]).join('&');

                    $(_this.viewBtn).click(function () {
                        _this.btnGroup.remove();
                        var href = context + "/content/content_object_view.form?documentId=" + docId + "&" + queryString;
                        _this.linkAction(href);
                    });

                    $(_this.editBtn).click(function () {
                        _this.btnGroup.remove();
                        var href = context + "/content/content_object_edit_content.form?" + queryString;
                        _this.linkAction(href);
                    });
                }
            },

            processStatusResponse: function (data) {
                var _this = this;
                _this.data.contentSelectionId = data.contentSelectionId;
                _this.data.editable = data.editable;
                _this.data.working_copy_init_required = data.working_copy_init_required;
                _this.data.reassign_required = data.reassign_required;
                var viewBtn = _this.viewBtn.children('i');
                var editBtn = _this.editBtn.children('i');

                if (!data.editable) {
                    common.disableElement(_this.editBtn);
                    _this.viewBtn.addClass("d-none");
                    _this.editBtn.addClass("d-none");
                    $(_this.btnGroup)
                        .popupFactory({
                            title: client_messages.content_editor.oops,
                            popupLocation: "right",
                            fnSetContent: function (o) {
                                return "<div align=\"left\" style=\"font-size: 11px; margin: 8px;\">" + data.message + "</div>";
                            }
                        });
                }

                viewBtn.removeClass('fad fa-spinner-third fa-spin');
                viewBtn.addClass('fas fa-glasses-alt');
                editBtn.removeClass('fad fa-spinner-third fa-spin');
                editBtn.addClass('fas fa-pencil');
                _this.btnGroup.removeClass('loading');
                _this.btnGroup.addClass('init');
                _this.setupGotoLinks();
            },

            requestEditableStatus: function () {
                var _this = this;

                var type = $(_this.targetEle).attr('type') == "10" ? 'localContentStatus' : 'embeddedContentStatus';

                var stampDate = new Date();

                _this.btnGroup.addClass('loading');

                $.ajax({
                    type: "GET",
                    url: context + "/getObjectInfo.form" +
                        "?type=" + type +
                        "&objectDna=" + $(_this.targetEle).attr('dna') +
                        "&touchpointSelectionContextId=" + ($('#touchpointSelectionContextId').length != 0 ? $('#touchpointSelectionContextId').val() : -1) +
                        "&tk=" + getParam('tk') +
                        "&cacheStamp=" + (stampDate.getTime()),
                    dataType: "json",
                    success: function (data) {
                        _this.processStatusResponse(data);
                    }
                });
            },

        }; // end component
    }

    // instance manager
    fastNavigation_component.inst = {};

})(jQuery);