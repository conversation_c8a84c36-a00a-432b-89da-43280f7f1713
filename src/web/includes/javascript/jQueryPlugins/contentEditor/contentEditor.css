.contentEditor_CharacterCount {
	padding-left: 6px;	
}

.contentEditor_CharacterCountContainer {
	padding: 1px 11px;
}

.contentEditor_ContainerBorder {
	border: 1px solid #dadada;
	border-radius: 3px;
}

.contentEditor_DisplayContainer {
	height: 100%;
	text-align: left;
}

.contentEditor_GraphicImg {
	max-width: 100% !important;
}

.contentEditor_EditorActionBarContainer {
	padding: 5px 0;
	background-color: #f0f0f0;
}

.contentEditor_GraphicName, .contentEditor_GraphicUploadDate, .contentEditor_GraphicCmsLastUpdateDate, .contentEditor_GraphicCmsLastSyncDate, .contentEditor_GraphicAppliedImageName {
	white-space: nowrap;
	padding-left: 7px;
}

.contentEditor_GraphicInputTable .graphicAppliedImageName {
	display: inline-block;
	margin-left: 12px;
}

.contentEditor_GraphicImageLinkContainer, .contentEditor_GraphicImageAltTextContainer, .contentEditor_GraphicImageExtLinkContainer, .contentEditor_GraphicImageExtPathContainer {
    /*
	background-color: #f5f5f5;
    color: #333;
    margin: 0;
    padding-right: 0;
    */
}
.contentEditor_GraphicImageLinkContainer > div, .contentEditor_GraphicImageAltTextContainer > div, .contentEditor_GraphicImageExtLinkContainer > div, .contentEditor_GraphicImageExtPathContainer > div {
	/*
	border-radius: 0 0 3px 3px;
	border: 1px solid #a0a0a0;
	border-top: none;
	*/
}

.contentEditor_MiniEditorsContainer {
	margin-top: 1.5rem;
}

.contentEditor_MiniEditorsContainer > :not([hidden]) ~ :not([hidden]) {
	margin-top: 1.5rem;
}

.contentEditor_ImageAltTextTable,
.contentEditor_ImageLinkTable,
.contentEditor_ImageExtLinkTable,
.contentEditor_ImageExtPathTable {
	width: 100%;
	border: 0;
	margin: 0;
	padding: 0;
}


.contentEditor_ImageLinkTable .workflowTabText, .contentEditor_ImageAltTextTable .workflowTabText {
	padding-left: 10px;
	text-align: left;
}

.contentEditor_InfoDivContainer {
    background-color: #f5f5f5;
    border: 1px solid #dadada;
    border-radius: 4px;
    color: #444;
    padding: 0 20px;
}
.contentEditor_GraphicInputsContainer {
    background-color: #f5f5f5;
    border-radius: 3px 3px 0 0;
    border-bottom: 1px solid #dadada;
    color: #444;
    padding: 0 20px;
}
.contentEditor_ReferencingImageLibraryText, .contentEditor_SameAsParentInputContainer {
	border: none;
	border-bottom: 1px solid #dadada;
	border-radius: 3px 3px 0 0;
	margin: 0;
}
.contentEditor_SameAsContainer, .contentEditor_SharedContainer {
	background-color: #f5f5f5;
	border: none;
	border-bottom: 1px solid #dadada;
	border-radius: 3px 3px 0 0;
	font-size: 11px;
	color: #444;
	margin: 0;
	padding: 0 20px;
	line-height: 31px;
	font-weight: 600;
}
.contentEditor_SameAsDefaultInputContainer, .contentEditor_SameAsDefaultBarContainer {
	white-space: nowrap; 
	margin: 0;
	border-radius: 3px 3px 0 0;
	border-top:none; 
	border-left: none; 
	border-right: none; 
}

.contentEditor_StaticGraphicInfoContainer {
	background-color: #f5f5f5;
}

.contentEditor_ImageContainer {
	overflow: auto;	
	height: 210px;
	border-top: 1px solid #dadada;
	background-color: #fff;
	border-radius: 0 0 4px 4px;
}
.contentEditor_FileUploadInput {
	opacity: 0; 
	filter:alpha(opacity: 0);
	position:absolute;
	z-index: 1;
	top: 0; 
	right: 0;
	left: 0;
	margin: 0; 
	font-size: 13px; 
	height: 26px;
	direction: ltr; 
	cursor: pointer;
	/* opacity: 0; filter:alpha(opacity: 0); position:absolute; width: 80px;
		position: absolute; top: 0; right: 0; margin: 0; opacity: 0; filter: alpha(opacity=0); 
		transform: translate(-300px, 0) scale(4); font-size: 23px; direction: ltr; cursor: pointer; */
}

.contentEditor_InnerContentContainer, .contentEditor_LoadingIndicatorContainer {
	background-color: #fff;
}
.contentEditor_InnerContentContainer {
	border: solid 1px #dadada;
	border-radius: 4px;
}
.contentEditor_LoadingIndicatorContainer {
	background-color: #fff; 
	border: 1px solid #bbb;
	border-radius: 3px;
}

.contentEditor_OuterContentActionsContainer {
	padding: 0 0 24px;
}
* html .contentEditor_OuterContentActionsContainer {
	padding-bottom: 0;
}

.contentEditor_OuterContentContainer {
	height: 100%;
}

.contentEditor_TabContainer {
}
.contentEditorTab {
	display: inline-block;	
	margin-top: 8px;
}

.contentEditor_TabContainer, .contentEditor_ActionBarTable, .contentEditor_GraphicInputTable, 
.contentEditor_StaticGraphicInfoContainer, .contentEditor_SameAsTable {
    font-size: 11px;	
    border: none;
}

.contentEditor_StaticGraphicInfoContainer {
	padding: 0 20px;
}

.contentEditor_StaticGraphicInfoContainer DIV {
	border: none;
    padding: 0;
    vertical-align: bottom;
    display: inline-block;	
}
.contentEditor_ActionBarTable TD, .contentEditor_GraphicInputTable TD, .contentEditor_SameAsTable TD  {
	border: none;
	padding: 0;
	vertical-align: middle;
	white-space: nowrap;
	line-height: 32px;
}

.contentEditor_StaticGraphicContentContainer {
	line-height: 32px;
	
}
.contentEditor_StaticTextContentContainer {
	overflow: auto;
	background-color: #f5f5f5;
	border-radius: 3px;
}

.contentEditor_OuterPageContainer {
	height: 100%;
	border-right: 1px solid #dadada;
	margin-left: 32px;
	margin-right: 32px;
	border-left: 1px dotted #dadada;
	padding-top: 24px;
	padding-bottom: 24px;
}

.contentEditor_OuterFreeformContainer {
	padding: 20px;
}
.contentEditor_FreeformContainer {
	padding : 0;
	width : 827px;
	height : 1169px;
	border : 1px solid #d2d5d8;
	box-shadow : 3.5px 3.5px 5.5px #ccc;
	position : relative;
	background-color: #fff;
}

.contentEditor_btnContainer {
	opacity: 0;
	width: 150px;
	height: 32px;
	-webkit-transition: top .1s linear, opacity .1s linear;
}
.contentEditor_tagTextToggle, .contentEditor_encodedContentToggle, .contentEditor_fullScreenToggle, .contentEditor_sampleValueToggle, .contentEditor_contentRenderToggle, .contentEditor_viewTagPanel {
	border-radius: 50%;
	text-align: center;
	width: 32px;
	height: 32px;
	display: inline-block;
	vertical-align: top;
}

.contentEditor_tagTextToggle .fa, .contentEditor_encodedContentToggle .fa, .contentEditor_fullScreenToggle .fa, .contentEditor_sampleValueToggle .fa, .contentEditor_contentRenderToggle .fa, .contentEditor_viewTagPanel .fa {
	line-height: 32px;
	padding: 0;
}
.contentEditor_OuterContentContainer:hover .contentEditor_btnContainer {
    opacity: 1;
}

.mce-tinymce {
	border: none;
}

.actionVariable {
	cursor: pointer !important;
}