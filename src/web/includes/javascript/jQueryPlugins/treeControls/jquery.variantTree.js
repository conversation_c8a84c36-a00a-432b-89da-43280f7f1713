;(function ($, window, document, undefined) {

    'use strict';

    const instances = {};

    const defaults = {
            documentId: -1,
            searchable: false,
            async: false,
            treeData: [],
            onchange: null,
            onContextMenuSelect: null,
            onresize: null,
            selectedNodeLevel: -1,
            showContentStatus: false,
            contentCompareContext: false,
            messages: {},
            selectedNodeId: null,
            requestId: null,
            bindingType: "none",
            initializeFetch: true
    };

    // The actual plugin constructor
    function Plugin ( element, options ) {
        this.element = element;
        // jQuery has an extend method which merges the contents of two or
        // more objects, storing the result in the first object. The first object
        // is generally empty as we don't want to alter the default options for
        // future instances of the plugin
        this.settings = $.extend( {}, defaults, options );
        this._defaults = defaults;
        this._name = $(element).attr('id');

        if(this.settings.initializeFetch)
            this.init();

        var _this = this;
        var updateButton = $('#' + this._name + '_updateReady');
        updateButton.on('click', function (event, data) {
            updateButton.hide();
            var isUpdateOnly = (data && data.isUpdateOnly !== undefined) ? data.isUpdateOnly : true;
            // send the request without a request id, will return from cache only
            $.ajax({
                type: 'GET',
                url: getRequestUrl(_this.settings, isUpdateOnly),
                success: function(res) {
                    _this.settings.treeData = res.tree;
                    processTreeData(_this.settings);
                    _this.refresh(_this.settings.treeData);
                }
            });


        });
    }

    function processTreeData(settings) {
        if ( settings.treeData[0] !== undefined ) {
            settings.treeData = settings.treeData[0];
        }

        markDisabledBranches(settings.treeData);
        removeDeadBranches(settings.treeData);
    }

    // Avoid Plugin.prototype conflicts
    $.extend(Plugin.prototype, {
        init: function () {
            const _this = this;

            if (!_this.fetched) {
                $.ajax({
                    type: 'GET',
                    url: getRequestUrl(_this.settings, false),
                    success: function (res) {
                        let tree = res.tree;
                        if (_this.settings.selectedNodeId.indexOf(',') > -1) {
                            //branch ids separated by , for rationalizer
                            setSelectedNodeForBranch(tree, _this.settings.selectedNodeId);
                        } else {
                            setSelectedNode(tree, parseInt(_this.settings.selectedNodeId));
                        }

                        _this.settings.masterVariantId = res.masterVariantId;
                        _this.settings.treeData = tree;
                        setup();
                    }
                });
            }

            function setup() {
                let settings = _this.settings;
                let element = _this.element;

                let hasIconData = _.has(settings.treeData, 'icon');

                processTreeData(settings);

                let jsTreeOptions = {
                    'plugins': ['wholerow'],
                    'core': {
                        'themes': {
                            'name': 'proton',
                            'responsive': false,
                            'icons': hasIconData
                        },
                        'load_open': true,
                        'data': settings.treeData
                    }
                };

                let hasContextMenu = _.isFunction(window[settings.onContextMenuSelect]);

                if (hasContextMenu) {
                    let contextMenuItems = treeContextMenu(settings, window[_this.settings.onContextMenuSelect]);
                    if (window.location.href.includes("content_object_view") || window.location.href.includes("formatting_selections_list")) {
                        if (!settings.canUpdate) {
                            delete contextMenuItems.goTo;
                        }
                        delete contextMenuItems.goToSave;
                    }
                    if(Object.keys(contextMenuItems).length){
                        jsTreeOptions.plugins.push('contextmenu');
                    }
                    jsTreeOptions = _.extend(jsTreeOptions, {'contextmenu': {
                        items: function(data) {
                            if (!data.state.disabled) {
                                if (data.id == "-9" || data.id == settings.masterVariantId) {
                                    var contextMenuItemsForMaster = Object.keys(contextMenuItems).filter(function(e) {
                                        return !contextMenuItems[e].variantOnly;
                                    }).reduce(function(obj, key){
                                        obj[key] = contextMenuItems[key];
                                        return obj;
                                    }, {});
                                    return contextMenuItemsForMaster;
                                }
                                return contextMenuItems;
                            }
                            return {};
                        }
                    }});
                }

                const tree = $(element).jstree(jsTreeOptions);
                let isVariantProcessing = false;

                tree.bind("loaded.jstree", function () {

                    let instance = tree.jstree();

                    if (settings.expanded) {
                        if (Object.keys(instance._model.data).length < 100) {
                            instance.open_all();
                        }
                    }

                    let selectedNode = instance.get_selected(true);

                    if (selectedNode && selectedNode.length > 0) {
                        settings.selectedNodeLevel = selectedNode[0].parents.length - 2;
                    }

                    if (hasIconData) {
                        // hide master level icon
                        $(".jstree-anchor:first .jstree-icon", element).remove();
                    }
                });

                tree.bind("changed.jstree", function (event, data) {

                    if (_.isUndefined(data) || _.isUndefined(data.event) || data.event.type === "contextmenu") {
                        return;
                    }

                    if (_.isFunction(window[settings.onchange])) {
                        if (settings.onchange === 'variantSelected') {
                            if (isVariantProcessing) {
                                return;
                            }
                            isVariantProcessing = true;

                            window[settings.onchange](event, data, settings);
                        } else {
                            window[settings.onchange](event, data, settings);
                        }
                    }
                    else if (_.isEmpty(settings.onchange)) {
                        javascriptHref(getVariantNodeHref(settings, parseInt(data.node.id)));
                    }
                });

                tree.bind('open_node.jstree ready.jstree', function () {
                    $('.activeCopyIcon, .workingCopyIcon', element).each(function (index, value) {
                        const node = $(value);

                        if (node.hasClass('activeCopyIcon')) {
                            node.parent().prepend('<span style="margin:0 2px 0 2px" class="stateIconContainer activeIconDiv fa fa-check"></span>');
                        } else {
                            node.parent().prepend('<span style="margin:0 2px 0 2px" class="stateIconContainer"></span>');
                        }

                        if (node.hasClass('workingCopyIcon')) {
                            node.parent().prepend('<span style="" class="stateIconContainer workingCopyIconDiv fa fa-pencil"></span>');
                        } else {
                            node.parent().prepend('<span class="stateIconContainer"></span>');
                        }

                        if (!settings.contentCompareContext) {
                            if (node.parent().hasClass('jstree-clicked')) {
                                $('.workingCopyIconDiv, .activeIconDiv', node.parent()).addClass('selectedStateIcon');
                            }
                        }

                        node.remove();
                    });

                    // makes sure the selected node is visible
                    const targetNode = $('#' + tree.jstree('get_selected')[0]);
                    const offset = targetNode.offset();
                    const scrollTop = element.scrollTop();

                    if (!_.isUndefined(offset) && offset.top > scrollTop) {
                        const parentsCount = $(targetNode).parents('li').length;
                        element.scrollTop(offset.top - (targetNode.height() * parentsCount) - 2);
                    }

                    if (settings.showContentStatus) {
                        tree.find('a').each(function (index, elem) {

                            const $elem = $(elem);
                            const contentStatus = $elem.data('contentstatus');

                            if ( contentStatus !== null && contentStatus !== undefined) {
                                var contentStatusSplit = contentStatus.split(';');
                                contentStatusSplit.forEach(function(contentStatus){
                                    switch (contentStatus) {
                                        case 'references':
                                            $elem.addClass('contentStatusReferences');
                                            break;
                                        case 'dirty':
                                            $elem.addClass('contentStatusDirty');
                                            break;
                                        case 'suppresses':
                                            $elem.addClass('contentStatusSuppresses');
                                            if(settings.contentCompareContext){
                                                tree.jstree().disable_node(this);
                                            }
                                            break;
                                        case 'outOfSync':
                                            $elem.addClass('contentStatusOutOfSync');
                                            tree.jstree().disable_node(this);
                                            break;
                                    }
                                });
                            }
                        });

                        tree.find(".contentStatusOutOfSync")
                            .filter(":not(:has(.fa-exclamation-triangle))")
                            .attr({
                                'title': settings.messages.page.text.message.out.of.sync,
                                'data-toggle': 'tooltip',
                                'data-placement': 'bottom',
                                'data-trigger': 'hover'})
                            .prepend('<i style="margin-right: 8px" data-toggle="tooltip" data-placement="bottom" class="fa fa-exclamation-triangle detailTip dangerTxt"></i>')
                            .each(function(index, value) {
                                $(value).tooltip();
                            });
                    }
                });

                tree.bind('ready.jstree', function() {
                    setDefaultSelectedNode(tree);
                });

                tree.bind('after_open.jstree after_close.jstree ready.jstree', function () {
                    if (settings.onresize !== null && _.isFunction(window[settings.onresize])) {
                        window[settings.onresize]();
                    }
                    setDefaultSelectedNode(tree);
                });

                if (!settings.contentCompareContext) {
                    tree.bind('select_node.jstree', function(event, data) {
                        settings.selectedNodeLevel = _.isArray(data.node.parents) ? data.node.parents.length : -1;
                        $('.selectedStateIcon', element).removeClass('selectedStateIcon');
                        $('.jstree-clicked').children('.stateIconContainer').addClass('selectedStateIcon');
                    });
                }

                if (hasContextMenu) {
                    tree.bind('click.jstree', function() {
                        $('.jstree-contextmenu').remove();
                    });
                    tree.bind('show_contextmenu.jstree', function(event, data) {
                        settings.contextMenuSelectedId = data.node.id;
                    });
                }

                $(document).trigger('initialized.variantTree');

                _this.fetched = true;
            }

            function setSelectedNodeForBranch(tree, selectedPathIds) {
                var selectedNode = false;
                removeSelection(tree[0]);
                setSelection(tree[0]);
                function removeSelection(tree) {
                    if (tree.hasOwnProperty('state')) {
                        if (tree.state.hasOwnProperty("selected")) {
                            delete tree.state.selected;
                        }
                    }

                    if (!_.isArray(tree.children)) {
                        return;
                    }

                    tree.children.forEach(function (node) {
                        removeSelection(node);
                    });
                }

                function setSelection(tree) {
                    if (tree.id === selectedPathIds) {
                            tree.state = {
                                selected: true
                            };
                    }

                    if (!_.isArray(tree.children)) {
                        return;
                    }

                    tree.children.forEach(function (node) {
                        setSelection(node);
                    });
                }
            }

            function setSelectedNode(tree, selectedNodeId) {

                removeSelection(tree[0]);
                setSelection(tree[0]);

                function removeSelection(tree) {
                    if (tree.hasOwnProperty('state')) {
                        if (tree.state.hasOwnProperty("selected")) {
                            delete tree.state.selected;
                        }
                    }

                    if (!_.isArray(tree.children)) {
                        return;
                    }

                    tree.children.forEach(function (node) {
                        removeSelection(node);
                    });
                }

                function setSelection(tree) {
                    if (tree.id === parseInt(selectedNodeId)) {
                        tree.state = {
                            selected: true
                        };
                    }

                    if (!_.isArray(tree.children)) {
                        return;
                    }

                    tree.children.forEach(function (node) {
                        setSelection(node);
                    });
                }
            }

            function setDefaultSelectedNode(tree) {
                let instance = tree.jstree();
                let nodeId = _this.settings.selectedNodeId == '-9' ? _this.settings.masterVariantId : _this.settings.selectedNodeId;
                let domNode = instance.get_node(nodeId, true);
                if (domNode) {
                    domNode.children('.jstree-wholerow').addClass('jstree-wholerow-default-selected');
                    domNode.children('.jstree-anchor').addClass('jstree-default-selected');
                }
            }

        },

        fetched: false,

        getChildren: function() {
            return this.settings.treeData.hasOwnProperty('children') ? this.settings.treeData.children : [];
        },
        getSettings: function() {
            return this.settings;
        },
        closeAll: function() {
            if (this.fetched)
                $(this.element).jstree().close_all();
        },
        deselectAll: function () {
            if (this.fetched)
                $(this.element).jstree().deselect_all();
        },
        refresh: function (data) {
            if (this.fetched) {
                $(this.element).jstree(true).settings.core.data = data;
                $(this.element).jstree().refresh();
            }
        }
    });

    // Wrapper to prevent multiple instances of plugin per object
    $.fn.variantTree = function (options, data) {

        let result = null;

        if (_.isUndefined(this[0])) {
            return $(this);
        }

        if (!_.has(instances, this[0].id)) {
            instances[this[0].id] = new Plugin( this, options );
        }

        if (_.isString(options)) {
            result = instances[this[0].id][options](data);
        }

        return result || $(this);
    };

    function getRequestUrl(settings, isUpdateOnly) {

        let url = context + '/variantTree/data/asyncVariantTree.form?binding=' + settings.bindingType;

        let variables = ['documentId', 'contentObjectId', 'statusViewId', 'tk'];

        variables.forEach(function (variable) {
            let value = gup(variable);

            if (value !== null && value !== "") {
                url += '&' + variable + '=' + value;
            }
        });

        if (!isUpdateOnly) {
            url += '&requestId=' + settings.requestId;
        }

        return url;
    }

    function treeContextMenu(settings, selectionHandler) {
        const actions = {
            ACTION_GO_TO: 0,
            ACTION_GO_TO_SAVE: 1,
            ACTION_CREATE_TREE_NODE: 4,
            ACTION_ADD_TO_TREE_NODE: 8,
            ACTION_REMOVE_FROM_TREE_NODE: 9,
            ACTION_RENAME_TREE_NODE: 12,
            ACTION_REMOVE_TREE_NODE: 13,
            ACTION_MANAGE_FROM_TREE_NODE: 14,
            ACTION_EDIT_FORMATTING_SELECTION: 15,
        };
        var goToLabel = (window.location.pathname.includes("_view") || window.location.pathname.includes("formatting_selections_list")) ? "Edit" : "Go to..";

        var treeContextMenu = {
            goTo: {
                label: goToLabel,
                action: function() {
                    if(window.location.pathname.includes("formatting_selections_list")){
                        selectionHandler(actions.ACTION_EDIT_FORMATTING_SELECTION, settings.contextMenuSelectedId);
                    }else{
                        selectionHandler(actions.ACTION_GO_TO, settings.contextMenuSelectedId);
                    }
                }
            },
            goToSave: {
                label: 'Save and go to..',
                action: function() {
                    selectionHandler(actions.ACTION_GO_TO_SAVE, settings.contextMenuSelectedId);
                }
            },
        };
        if((settings.bindingType === 'ContentObject' || settings.bindingType === 'FormattingSelection') && settings.canUpdate) {
            var treeContextVariantMenu = {
                addSubVariant: {
                    label: 'Add variant',
                    action: function() {
                        selectionHandler(actions.ACTION_CREATE_TREE_NODE, settings.contextMenuSelectedId);
                    }
                },
                renameVariant: {
                    label: 'Rename',
                    variantOnly: true,
                    action: function () {
                        selectionHandler(actions.ACTION_RENAME_TREE_NODE, settings.contextMenuSelectedId);
                    }
                },
                deleteVariant: {
                    label: 'Delete',
                    variantOnly: true,
                    action: function () {
                        selectionHandler(actions.ACTION_REMOVE_TREE_NODE, settings.contextMenuSelectedId);
                    }
                },
                manageData: {
                    label: 'Manage data',
                    variantOnly: true,
                    action: function () {
                        selectionHandler(actions.ACTION_MANAGE_FROM_TREE_NODE, settings.contextMenuSelectedId);
                    }
                }
            }
            treeContextMenu = $.extend({}, treeContextMenu, treeContextVariantMenu);
        }
        return treeContextMenu;
    }

    function getVariantNodeHref(settings, id) {

        let result = '';
        const statusViewParam = _.isEmpty(settings.statusViewId) ? '' : '&statusViewId=' + settings.statusViewId;

        var cfSource = getParam('cfSource') === "true";
        if (id === settings.masterVariantId) {
            result = context + "/content/content_object_view.form?contentObjectId=" + settings.instanceId + "&paramInstId=" + id + statusViewParam + (cfSource ? '&cfSource=true' : '');
        } else {
            result = context + "/content/content_object_view_content_dynamic_variant.form?contentObjectId=" + settings.instanceId + "&paramInstId=" + id + statusViewParam + (cfSource ? '&cfSource=true' : '');
        }

        return result + '&tk=' + gup('tk');
    }
    
    function markDisabledBranches(data) {
    	
    	function hasEnabledChild(data) {
    		if ( data.children && data.children !== undefined ) {
	    		for ( let i = 0; i < data.children.length; i++ ) {
	    			if (_.has(data.children[i], 'state') && (data.children[i].state.disabled === false || data.children[i].state.selected === true)) {
                        return true;
                    }
	    			else if ( hasEnabledChild(data.children[i]) ) {
                        return true;
                    }
	    		}
    		}
    		return false;
    	}
    	
    	if ( data.children && data.children !== undefined ) {
	    	for (let i = 0; i < data.children.length; i++) {
	    	    // check disabled
	    		const itemDisabled = (_.has(data.children[i], 'state') && data.children[i].state.disabled !== undefined) ? data.children[i].state.disabled : false;
                data.children[i].remove = !hasEnabledChild(data.children[i]) && itemDisabled;
	    		markDisabledBranches(data.children[i]);
	    	}
    	}
    }
    
    function removeDeadBranches(data) {
     	if ( data.children && data.children !== undefined ) {
	    	data.children = data.children.filter( function(el) {
	    		return !el.remove;
	    	});
	    	for ( let i = 0; i < data.children.length; i++ ) {
                removeDeadBranches(data.children[i]);
            }
     	}
    }

    function toggleUpdateCheckMessage(visible) {

        _.keys(instances).forEach(function (id) {

            let $tree = $('#' + id);
                if (!visible) {
                    let removeUpdateCheckInterval = null;

                    if (!removeUpdateCheckIcon($tree)) {
                        removeUpdateCheckInterval = setInterval(function () {
                            if (removeUpdateCheckIcon($tree)) {
                                clearInterval(removeUpdateCheckInterval);
                            }

                        }, 25);
                    }
                } else {
                    $tree.on('loaded.jstree', function () {
                        $tree.find('.jstree-anchor').first().append('<i style="margin-left: 8px" class="updateCheck fas fa-spinner-third fa-spin"></i>');
                    });
                }

        });

        function removeUpdateCheckIcon($tree) {
            if ($tree.find('.updateCheck').length > 0) {
                $tree.find('.updateCheck').remove();
                return true;
            }

            return false;
        }
    }

    function toggleUpdateReadyMessage(visible) {
        _.keys(instances).forEach(function (id) {
            if (visible) {
                $('#' + id + '_updateReady').show();
            }
        });
    }


    function onServiceWorkerPostMessage(event) {

        let data = event.detail;

        if (data.path === '/variantTree/data/asyncVariantTree.form') {
            switch (data.action) {
                case 'start-fetch':
                    toggleUpdateCheckMessage(true);
                    break;
                case 'end-fetch':
                    toggleUpdateCheckMessage(false);

                    if (data.hasUpdate) {
                        toggleUpdateReadyMessage(true);
                    }
                    break;
            }
        }
    }

    document.addEventListener('mp-service-worker', onServiceWorkerPostMessage);

})(jQuery, window, document);

//# sourceURL=jquery.variantTree.js