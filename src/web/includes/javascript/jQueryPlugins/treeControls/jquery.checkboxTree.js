;(function ($, window, document, undefined) {

    'use strict';

    const instances = {};

    const defaults = {
            searchable: false,
            async: false,
            treeData: [],
            onchange: null,
            onresize: null,
            selectedNodesList: [],
            requestId: null,
            bindingType: "none",
            multiselect: true
    };

    // The actual plugin constructor
    function Plugin ( element, options ) {
        this.element = element;
        // jQuery has an extend method which merges the contents of two or
        // more objects, storing the result in the first object. The first object
        // is generally empty as we don't want to alter the default options for
        // future instances of the plugin
        this.settings = $.extend( {}, defaults, options );
        this._defaults = defaults;
        this._name = $(element).attr('id');
        this.init();
    }

    // Avoid Plugin.prototype conflicts
    $.extend(Plugin.prototype, {
        init: function () {
            const _this = this;

            $.ajax({
                type: 'GET',
                url: getRequestUrl(_this.settings, false),
                success: function(res) {
                    let tree = res.tree;
                    if (_this.settings.selectedNodesList) {
                        setSelectedNodeForBranch(tree, _this.settings.selectedNodesList);
                    }

                    _this.settings.masterVariantId = res.masterVariantId;
                    _this.settings.treeData = tree;
                    setup();
                }
            });

            function setup() {
                let settings = _this.settings;
                let element = _this.element;

                let jsTreeOptions = {
                    'plugins': ['checkbox', 'search'],
                    'core': {
                        'themes': {
                            'name': 'proton',
                            'icons': false,
                            'responsive': false,
                            'dots' : false
                        },
                        'load_open': false,
                        'expand_selected_onload' : false,
                        'data': settings.treeData
                    },'checkbox' : {
                        three_state : settings.multiselect ? true: false,
                        cascade: settings.multiselect ? 'up+down' : '',
                        visible: true
                    }
                };

                const tree = $(element).jstree(jsTreeOptions);

                tree.bind("loaded.jstree", function () {

                    let instance = tree.jstree();
                     processTree();
               });
                
                tree.bind("changed.jstree", function (event, data) {
                    if (_.isUndefined(data) || _.isUndefined(data.event)) {
                        return;
                    }
                    processTree();
                    if (_.isFunction(window[settings.onchange])) {
                        window[settings.onchange](event, data, settings);
                    }
                    else if (_.isEmpty(settings.onchange)) {
                        javascriptHref(getVariantNodeHref(settings, parseInt(data.node.id)));
                    }
                });

                tree.bind('open_node.jstree', function (event, data) {
                    var id = data.node.id;
                    var liNode = $('#' + id);
                    if($(liNode)[0] != undefined) {
                        if ($(liNode)[0].getAttribute('aria-selected') =="true") {
                            $(liNode.children()[1]).addClass('parent-selection');
                            $('.jstree-anchor', $(liNode.children()[2])).addClass('default-selected-child');

                        } else {
                            $(liNode.children()[1]).removeClass('parent-selection');
                            $('.jstree-anchor', $(liNode.children()[2])).removeClass('default-selected-child');

                        }
                    }
                    if(settings.multiselect) {
                        if($(liNode)[0] != undefined) {
                            processChecksonAnchor(  $('#' + id).find('.jstree-anchor'));
                        }
                    }
                    // makes sure the first selected node is visible
                    const targetNode = $('#' + tree.jstree('get_selected')[0]);
                    const offset = targetNode.offset();
                    const scrollTop = element.scrollTop();

                    if (!_.isUndefined(offset) && offset.top > scrollTop) {
                        const parentsCount = $(targetNode).parents('li').length;
                        element.scrollTop(offset.top - (targetNode.height() * parentsCount) - 2);
                    }

                });

                tree.bind('after_open.jstree after_close.jstree ready.jstree', function () {
                    if (settings.onresize !== null && _.isFunction(window[settings.onresize])) {
                        window[settings.onresize]();
                    }
                });
                
                function processChecksonAnchor(selection) {
                    selection.each(function (index, value) {
                        const node = $(value);
                        if (node.hasClass('jstree-clicked')) {
                            node.children('i.jstree-icon.jstree-checkbox').removeClass('far fa-square');
                            node.children('i.jstree-icon.jstree-checkbox').addClass('far fa-check-square');
                            node.addClass('jstree-checked');

                        } else {
                            node.children('i.jstree-icon.jstree-checkbox').removeClass('fa-check-square');
                            node.children('i.jstree-icon.jstree-checkbox').addClass('far fa-square');
                            node.removeClass('jstree-checked');
                        }
                    });
                }

                function processTree() {
                    if (settings.multiselect) {
                        $('li').each(function (index, value) {
                            const node = $(value);
                            $(node.children()[1]).removeClass('parent-selection');
                            $('.jstree-anchor', $(node.children()[2])).removeClass('default-selected-child');
                        });


                        $('li[aria-selected=true]').each(function (index, value) {
                            const node = $(value);
                            $(node.children()[1]).addClass('parent-selection');
                            $('.jstree-anchor', $(node.children()[2])).addClass('default-selected-child');
                        });
                        processChecksonAnchor( $('.jstree-anchor', element));
                    }
                }

            }

            function setSelectedNodeForBranch(tree, selectedNodesList) {
                removeSelection(tree[0]);

                var selectionsArray = selectedNodesList.split(",");

                    selectionsArray.forEach(function (item) {
                        setSelection(tree[0], item);
                    });

                function removeSelection(tree) {
                    if (tree.hasOwnProperty('state')) {
                        if (tree.state.hasOwnProperty("selected")) {
                            delete tree.state.selected;
                            delete tree.state.checked;
                        }
                    }

                    if (!_.isArray(tree.children)) {
                        return;
                    }

                    tree.children.forEach(function (node) {
                        removeSelection(node);
                    });
                }

                function setSelection(tree, selection) {
                    if (selection === "-9") {
                                tree.state = {selected: true, checked: true, opened: false};
                    } else   if (tree.id === selection) {
                        tree.state = { selected: true, checked: true};
                    }
                    else if (selection.includes(tree.id)) {
                        tree.state = {  opened:true};
                    }

                    if (!_.isArray(tree.children)) {
                        return;
                    }

                    tree.children.forEach(function (node) {
                        setSelection(node, selection);
                    });
                }
            }

        },
        getChildren: function() {
            return this.settings.treeData.hasOwnProperty('children') ? this.settings.treeData.children : [];
        },
        getSettings: function() {
            return this.settings;
        },
        closeAll: function() {
            $(this.element).jstree().close_all();
        },
        deselectAll: function () {
            $(this.element).jstree().deselect_all();
        },
        refresh: function (data) {

            $(this.element).jstree(true).settings.core.data = data;
            $(this.element).jstree().refresh();
        }
    });

    // Wrapper to prevent multiple instances of plugin per object
    $.fn.checkboxTree = function (options, data) {

        let result = null;

        if (_.isUndefined(this[0])) {
            return $(this);
        }

        if (!_.has(instances, this[0].id)) {
            instances[this[0].id] = new Plugin( this, options );
        }

        if (_.isString(options)) {
            result = instances[this[0].id][options](data);
        }

        return result || $(this);
    };

    function getRequestUrl(settings, isUpdateOnly) {

        let url = context + '/variantTree/data/asyncCheckboxTree.form?binding=' + settings.bindingType;

        let variables = ['tk'];

        variables.forEach(function (variable) {
            let value = gup(variable);

            if (value !== null && value !== "") {
                url += '&' + variable + '=' + value;
            }
        });

        if (!isUpdateOnly) {
            url += '&requestId=' + settings.requestId;
        }

        return url;
    }

    function getVariantNodeHref(settings, id) {

        let result = '';
        return result + '&tk=' + gup('tk');
    }


})(jQuery, window, document);

//# sourceURL=jquery.checkboxTree.js