common.collectionDataValuesTreeInit = function(treeId, sharedItemId) {

    $('#' + treeId).jstree('destroy');
    $('#' + treeId).jstree({
        'plugins': ['wholerow'],
        'core': {
            'check_callback': true,
            'themes': {
                'name': 'proton',
                'responsive': false,
                'icons': false
            },
            'load_open': true,
            'data': {
                'url': function () {
                    return context + "/getCollectionDataValuesTreeNodes.form";
                },
                'data': function (n) {
                    if (n.id === '#')
                        return {
                            "collectionId": sharedItemId,
                            "verboseData": true,
                            "cacheStamp": cacheStamp
                        };
                    else
                        return {
                            "collectionId": sharedItemId,
                            "verboseData": true,
                            "cacheStamp": cacheStamp,
                            "fullValue": window.encodeURIComponent(n.id),
                            "nodeLevel": n.original.level
                        }
                }
            }
        }
    });
};