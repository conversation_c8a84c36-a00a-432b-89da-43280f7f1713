{"name": "blueimp-file-upload-j<PERSON>y-ui", "version": "7.2.1+jquery.ui", "title": "jQuery File Upload - jQuery UI version", "author": {"name": "<PERSON>", "url": "https://blueimp.net"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "dependencies": {"jquery": ">=1.6", "jquery.ui": ">=1.8"}, "description": "File Upload widget with multiple file selection, drag&drop support, progress bars and preview images for jQuery. Supports cross-domain, chunked and resumable file uploads and client-side image resizing. Works with any server-side platform (PHP, Python, Ruby on Rails, Java, Node.js, Go etc.) that supports standard HTML form file uploads.", "keywords": ["j<PERSON>y", "file", "upload", "widget", "multiple", "selection", "drag", "drop", "progress", "preview", "cross-domain", "cross-site", "chunk", "resume", "gae", "go", "python", "php", "ui"], "homepage": "https://github.com/blueimp/jQuery-File-Upload", "docs": "https://github.com/blueimp/jQuery-File-Upload/wiki", "demo": "http://blueimp.github.com/jQuery-File-Upload/", "bugs": "https://github.com/blueimp/jQuery-File-Upload/issues", "maintainers": [{"name": "<PERSON>", "url": "https://blueimp.net"}]}