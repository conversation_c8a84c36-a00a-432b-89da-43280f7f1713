(function(a){"use strict",typeof define=="function"&&define.amd?define(["jquery","./load-image.js","jquery-ui"],a):a(window.jQuery,window.loadImage)})(function(a,b){"use strict",a.widget("blueimp.imagegallery",{options:{selector:'a[data-gallery="gallery"]',namespace:"imagegallery",slideshow:0,offsetWidth:100,offsetHeight:100,fullscreen:!1,canvas:!1,imageClickDivision:.5,bodyClass:"gallery-body",loaderId:"gallery-loader",effects:["blind","clip","drop","explode","fade","fold","puff","slide","scale"],modal:!0,resizable:!1,width:"auto",height:"auto",show:"fade",hide:"fade",dialogClass:"gallery-dialog"},_getOverlay:function(){return a(document.body).children(".ui-widget-overlay").last()},_openSibling:function(a){var b=this,c=this._dialog;clearTimeout(this._slideShow),this._slideShow=null,a.href!==this._link.href?c.dialog("widget").hide(this.options.hide,function(){b._overlay=b._getOverlay().clone().appendTo(document.body),c.dialog("option","hide",null).dialog("close"),b._callback=function(){b._overlay.remove(),b._overlay=null},b._open(a)}):c.dialog("close")},_prev:function(){this._openSibling(this._prevLink)},_next:function(){this._openSibling(this._nextLink)},_keyHandler:function(a){var b=a.data.imagegallery;switch(a.which){case 37:case 38:return b._prev(),!1;case 39:case 40:return b._next(),!1}},_wheelHandler:function(a){var b=a.data.imagegallery;a=a.originalEvent,b._wheelCounter+=a.wheelDelta||a.detail||0;if(a.wheelDelta&&b._wheelCounter>=120||!a.wheelDelta&&b._wheelCounter<0)b._prev(),b._wheelCounter=0;else if(a.wheelDelta&&b._wheelCounter<=-120||!a.wheelDelta&&b._wheelCounter>0)b._next(),b._wheelCounter=0;return!1},_clickHandler:function(a){var b=a.data.imagegallery,c=b._dialog;(a.pageX-c.offset().left)/c.width()<b.options.imageClickDivision?b._prev():b._next()},_overlayClickHandler:function(b){var c=b.data.imagegallery;a(this).unbind("click."+c.options.namespace,c._overlayClickHandler),c._dialog.dialog("close")},_openHandler:function(b){var c=b.data.imagegallery,d=c.options;a(document.body).addClass(d.bodyClass),c._getOverlay().bind("click."+d.namespace,b.data,c._overlayClickHandler),c._callback&&(c._callback(),c._callback=null),d.slideshow&&(c._slideShow=setTimeout(function(){c._next()},d.slideshow))},_closeHandler:function(b){var c=b.data.imagegallery,d=c.options;a(document).unbind("keydown."+d.namespace,c._keyHandler).unbind("mousewheel."+d.namespace+", DOMMouseScroll."+d.namespace,c._wheelHandler),clearTimeout(c._slideShow),c._slideShow=null,c._overlay||(a(document.body).removeClass(d.bodyClass),c._position=null),c._dialog.remove(),c._dialog=null},_dragStopHandler:function(a,b){var c=a.data.imagegallery;c._position=b.position},_initDialogHandlers:function(){var b=this,c=this.options,d={imagegallery:this};a(document).bind("keydown."+c.namespace,d,this._keyHandler).bind("mousewheel."+c.namespace+", DOMMouseScroll."+c.namespace,d,this._wheelHandler),b._dialog.bind("click."+c.namespace,d,this._clickHandler).bind("dialogopen."+c.namespace,d,this._openHandler).bind("dialogclose."+c.namespace,d,this._closeHandler).bind("dialogdragstop."+c.namespace,d,this._dragStopHandler)},_loadHandler:function(b){var c=this.options,d=c.offsetWidth,e=c.offsetHeight;this._dialog=a("<div></div>"),this._loaded=!0,a(document).unbind("keydown."+c.namespace,this._escapeHandler).unbind("click."+c.namespace,this._documentClickHandler),this._img=null,this._loadingAnimation.hide(),this._initDialogHandlers(),this._position&&(c=a.extend({},c,{position:[this._position.left,this._position.top]})),this._dialog.append(b).appendTo(document.body).toggleClass("gallery-dialog-single",this._prevLink===this._link&&this._nextLink===this._link).dialog(c)},_abortLoading:function(){var b=this.options;this._img.unbind(),a(document).unbind("keydown."+b.namespace,this._escapeHandler).unbind("click."+b.namespace,this._documentClickHandler),this._getOverlay().remove(),this._loadingAnimation.hide(),a(document.body).removeClass(b.bodyClass)},_escapeHandler:function(a){a.keyCode===27&&a.data.imagegallery._abortLoading()},_documentClickHandler:function(b){var c=b.data.imagegallery;a(b.target).closest(c._link).length||c._abortLoading()},_loadImage:function(){var c=this,d=this.options,e={imagegallery:this},f=a(window).width(),g=a(window).height(),h;d.fullscreen?h={minWidth:f,minHeight:g,maxWidth:f,maxHeight:g,canvas:d.canvas}:h={maxWidth:f-d.offsetWidth,maxHeight:g-d.offsetHeight,canvas:d.canvas},a(document).bind("keydown."+d.namespace,e,this._escapeHandler).bind("click."+d.namespace,e,this._documentClickHandler),c._loaded=null,this._img=a(b(this._link.href,function(a){c._loadHandler(a)},h)),setTimeout(function(){c._loaded||c._loadingAnimation.show()},100)},_preloadSiblings:function(){a("<img>").prop("src",this._nextLink.href),a("<img>").prop("src",this._prevLink.href)},_initSiblings:function(){var a=this,b=this._link,c=this.element.find(this.options.selector);this._prevLink=null,this._nextLink=null,c.each(function(d){(c[d+1]===b||c[d+2]===b)&&this.href!==b.href&&(a._prevLink=this);if((c[d-1]===b||c[d-2]===b)&&this.href!==b.href)return a._nextLink=this,!1}),this._prevLink||(this._prevLink=c[c.length-1]),this._nextLink||(this._nextLink=c[0])},_getRandomEffect:function(){var a=this.options.effects;return a[Math.floor(Math.random()*a.length)]},_initEffects:function(){var a=this.options;if(a.show==="random"||this._show==="random")this._show="random",a.show=this._getRandomEffect();if(a.hide==="random"||this._hide==="random")this._hide="random",a.hide=this._getRandomEffect()},_open:function(b){if(this._dialog){var c=a.extend({},this);c._dialog=null,c._position=null,c._open(b);return}this.options.title=b.title||decodeURIComponent(b.href.split("/").pop()),this._link=b,this._initEffects(),this._loadImage(),this._initSiblings(),this._preloadSiblings()},_initFullscreenOptions:function(){var a=this.options;a.fullscreen?(/-fullscreen$/.test(a.dialogClass)||(a.dialogClass+="-fullscreen"),/-fullscreen$/.test(a.bodyClass)||(a.bodyClass+="-fullscreen")):(a.dialogClass=a.dialogClass.replace(/-fullscreen$/,""),a.bodyClass=a.bodyClass.replace(/-fullscreen$/,""))},_initLoadingAnimation:function(){this._loadingAnimation=a('<div id="'+this.options.loaderId+'"></div>').hide().appendTo(document.body)},_destroyLoadingAnimation:function(){this._loadingAnimation.remove(),this._loadingAnimation=null},_delegate:function(){var a=this,b=this.options;this.element.delegate(b.selector,"click."+b.namespace,function(b){b.preventDefault(),a._open(this)})},_undelegate:function(){this.element.undelegate(this.options.selector,"click."+this.options.namespace)},_setOption:function(b,c){this._show=this._hide=null;var d=b==="namespace"||b==="selector";d&&this._undelegate(),a.Widget.prototype._setOption.call(this,b,c),d&&this._delegate(),a.inArray(b,["fullscreen","dialogClass","bodyClass"])!==-1&&this._initFullscreenOptions()},_create:function(){this._wheelCounter=0,this._initLoadingAnimation(),this._initFullscreenOptions(),this._delegate()},destroy:function(){clearTimeout(this._slideShow),this._slideShow=null,this._dialog&&this._dialog.dialog("close"),this._undelegate(),this._destroyLoadingAnimation(),a.Widget.prototype.destroy.call(this)},enable:function(){a.Widget.prototype.enable.call(this),this._delegate()},disable:function(){clearTimeout(this._slideShow),this._slideShow=null,this._undelegate(),a.Widget.prototype.disable.call(this)}})});