$(function(){
	$(document).editManager();
});

(function($) {

	$.editManager = {
		ref	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return editManager_component.inst[o.attr('id')] || null; 
		},
		defaults : {

		}
	};
	
	$.fn.editManager = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if(conf !== false) new editManager_component().init(this, conf);
		});
	};
	
	function editManager_component () {
		return {
			
		settings : $.extend({},$.editManager.defaults),	

		init : function(elem, conf) {

			var _this = this;
			editManager_component.inst[$(elem).attr("id")] = _this;
			this.settings = $.extend(true, {}, _this.settings, conf);

			_this.editPolling();
		},

		editPolling : function() {
			var _this = this;
			
			var params = "";
			if ( getParam('contentObjectId') && getParam('contentObjectId') != "" )
				params = 'contentObjectId=' + getParam('contentObjectId') + '&';
			if ( getParam('communicationId') && getParam('communicationId') != "" )
				params = 'communicationId=' + getParam('communicationId') + '&';

			function sendPoll() {
				var stampDate = new Date();
				$.ajax({
					type: "GET",
					url: context + "/getSessionKeepAlive.form?" + params +"cacheStamp=" + (stampDate.getTime()),
					dataType: "json",
					success: function(data) {
						if ( data.status != "alive" )
							clearInterval(_this.settings.polling_interval);
					},
					error: function(data) {
						clearInterval(_this.settings.polling_interval);
					}
				});
			}

			_this.settings.polling_interval = setInterval( function() {
				sendPoll();					
			}, 15000);
			sendPoll();

		}
		
		}; // end component
	};
	
	// instance manager
	editManager_component.inst = {};
	
})(jQuery);	