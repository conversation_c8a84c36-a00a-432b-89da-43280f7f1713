/** ruleValueManager r1 // 2013.03.27 // jQuery 1.4 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.3 or later
 * 
 */

// ********************** Condition Data Element Value Inferface Overview ***********************
// *** The first two fields of a condition 'Data Element Variable' (i.e. dataElementSelect and 
// *** comparator) are bound and managed by Spring.  The value portion is dependant on a custom 
// *** interface.  The values are submitted and loaded using a string.  The expected string 
// *** formats are as follows:
// ***   Basic (Non-date):  value1@~@value2
// ***   Specific Date: 	dateType1@#@dateValue1@~@dateType2@#@dateValue2
// ***   Delta Date:
// ***		dateType1@#@operator1@#@value1@#@delta1@~@dateType2@#@operator2@#@value2@#@delta2
// ***	 Variable:			VAR%varId1@~@VAR%varId2
// ***	 Date Variable:
// ***		VAR%varId1@#@operator1@#@value1@#@delta1@~@VAR%varId2@#@operator2@#@value2@#@delta2
// *** Note: Date formats can be mixed.  For example a date may have a SpecificDate first 
// ***       component followed by a Delta Date second component.  The secondary component of
// ***       any date is optional.  In such instances no rangeSeparator is required
// ***       For example, the simple Specific Date 'dateType1@#@dateValue1'.
// **********************************************************************************************

function ruleValueManagerFunc(id) {
	var _this = $.ruleValueManager.get( id );

	return this;
};

(function($) {
	$.ruleValueManager = {
		get	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return ruleValueManager_component.inst[o.attr('id')] || null; 
		},
		defaults : {
			comparatorTypes				: [ 
				               		     	{ id: 1, value: client_messages.text.targeting.value },
				               		     	{ id: 3, value: client_messages.text.targeting.user_specified } 
				               		      ],
			dateDeltaTypes				: [
				              		    	{ id: 'd', value: client_messages.text.targeting.days },
				              		    	{ id: 'm', value: client_messages.text.targeting.months },
				              		    	{ id: 'y', value: client_messages.text.targeting.years }
				              		      ],
			dateSubtypeId				: '600',
			dateType_specificDateId		: '15',
			dateType_specificDateStr	: 'SpecificDate',	
			range_separator_str			: '@~@',
			rule_value_separator_str	: '@#@',
			externalUpdateTrigger		: null,
			type						: 'default'
		}
	};

	$.fn.ruleValueManager = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if(conf !== false) new ruleValueManager_component().init(this, conf);
		});
	};
	
	function ruleValueManager_component () {
		return {
			
		data : $.extend({},$.ruleValueManager.defaults),	

		init : function(elem, conf) {
			var _this = this;

			ruleValueManager_component.inst[$(elem).attr("id")] = _this;
			_this.data = $.extend(false, {}, this.data, conf);
			
			_this.elemId = $(elem).attr("id");
			_this.targetEle = $(elem);
			
			_this.initialComparedToValue = _this.targetEle.find('.variableComparedToValue').val();
			
			_this.currentComparatorState_isRange = _this.targetEle.attr('isRangeValue') == "true";
			_this.currentDataState_isDate = _this.targetEle.attr('isDateValue') == "true";
			
			if ( _this.data.externalUpdateTrigger != null )
				$(_this.data.externalUpdateTrigger).hover( function() { _this.updateRuleValuesBinding(); } );

			_this.initDisplay();
		},
		
		initDisplay : function() {
			var _this = this;
			
			var displayHTML = "";
			displayHTML +=  '<div id="ruleValueManagerContainer_' + _this.elemId + '" class="ruleValueManagerContainer">' +
								'<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr>' +
									'<td style="vertical-align: middle;">';
			if (_this.data.type == "user_specified_toggle")
				displayHTML +=  		'<div id="comparatorTypesContainer_' + _this.elemId + '" style="display: inline-block; vertical-align: middle;">' +
											'<select id="comparatorTypesSelect_' + _this.elemId + '" title="" class="comparatorTypesSelect style_select inputS" style="display: none;">' +
											'</select>' + 
										'</div>';
			displayHTML +=  			'<div style="display: inline-block; vertical-align: middle;">' +
											'<div id="ruleValuesContainer_' + _this.elemId + '">' +
											'</div>' + 
										'</div>' +
									'</td>' +
								'</tr></table>' +
							'</div>';
			
			_this.targetEle.append(displayHTML);

			_this.ruleValueManagerContainer = _this.targetEle.find('#ruleValueManagerContainer_'+_this.elemId);
		
			_this.initRuleValueInputs();
			
			// If: comparator type User specified: Keep rule value inputs hidden
			var comparatorType = (_this.getEle('comparatorTypesSelect').length != 0 ? _this.getEle('comparatorTypesSelect').val() : 1);
			if (comparatorType != 3) {
				// Show: Comparator Select/Values inputs
				if ( !_this.getEle('valuesDisplayContainer').is(':visible') )
					_this.getEle('valuesDisplayContainer').showEle('normal');
				if ( !_this.getEle('comparatorTypesContainer').is(':visible') )
					_this.getEle('comparatorTypesContainer').showEle('normal');
				
				// Show: If range: Range inputs
				if ( _this.currentComparatorState_isRange ) {
					if ( !_this.getEle('rangeValuesContainer').is(':visible') ) {
						_this.getEle('rangeValuesContainer').showEle('normal');
						_this.getEle('ruleRangeJoinerContainer').showEle('normal');
						_this.initDateDeltaOperatorIButton('b');
					}
				} else {
					_this.getEle('rangeValuesContainer').hide();
					_this.getEle('ruleRangeJoinerContainer').hide();
				};
			}

			_this.comparatorTypeSelectToggle();
			
			// INIT: Rule Value Inputs
			_this.setRuleValueInputsFromBinding();
			
			if (_this.data.type == "user_specified_toggle") {
				// SELECT: COMPARATOR TYPES: Static definition
				var comparatorTypes = _this.data.comparatorTypes;
				for (var currentIndex = 0; currentIndex < comparatorTypes.length; currentIndex++)
					_this.getEle('comparatorTypesSelect')
						.append("<option value=\"" + comparatorTypes[currentIndex].id + "\">" + comparatorTypes[currentIndex].value + "</option>");
				
				_this.getEle('comparatorTypesSelect')
					.change( function(){ 
						_this.comparatorTypeSelectToggle(); 
						_this.updateRuleValuesBinding();
					})
					.styleActionElement();

				// INIT: Comparator Type select value and display
				//COMPARE_INPUT = 1, COMPARE_PARAMETERIZED	= 3;
				if ( _this.targetEle.find('.parameterizedRuleCheckbox').is(':checked') )
					_this.getEle('comparatorTypesSelect').selectOptionByAttr('value',3);
				else
					_this.getEle('comparatorTypesSelect').selectOptionByAttr('value',1);				
			}
			
			_this.compareToInputsToggle();

		},

		compareToInputsToggle : function() {
			var _this = this;
			
			// State Control: Manage input displays based on compareTo selection
			// Refresh values display for updates that may have occurred while inputs were hidden 
			// (i.e. 'User specified' comparator type)
			if (_this.currentDataState_isDate) {
				_this.getEle('ruleValueInputContainer_a').hide();
				_this.getEle('ruleValueInputContainer_b').hide();
				_this.getEle('ruleDateValueTypeSelectContainer_a').showEle('normal');
				_this.getEle('ruleDateValueTypeSelect_a').change();
				_this.getEle('ruleDateValueTypeSelectContainer_b').showEle('normal');
				_this.getEle('ruleDateValueTypeSelect_b').change();
			} else {
				_this.hideDateInputComponents('a');
				_this.hideDateInputComponents('b');
				_this.getEle('ruleValueInputContainer_a').showEle('normal');
				_this.getEle('ruleValueInputContainer_b').showEle('normal');
			}
		},
		
		comparatorTypeSelectToggle : function() {
			// State Control: Manage all changes in compareTo selection
			var _this = this;
			
			var comparatorType = (_this.getEle('comparatorTypesSelect').length != 0 ? _this.getEle('comparatorTypesSelect').val() : 1);
			
			_this.compareToInputsToggle();
			if (comparatorType == 1) { // Value
				_this.setParameterized('off');
			} else if (comparatorType == 3) {  // User specified
				_this.setParameterized('on');
			}
		},
		
		getRuleValueInputHTML : function(valueId) {
			var _this = this;
			
			// SELECT: DATE VALUE TYPES: Static definition
			var dateValueTypes = _this.data.dateValueTypes;
			var dateValueTypesOptionHTML = "";
			for (var currentIndex = 0; currentIndex < dateValueTypes.length; currentIndex++)
				dateValueTypesOptionHTML += "<option value=\"" + dateValueTypes[currentIndex].id + "\" stringBuilderValue=\"" + dateValueTypes[currentIndex].value + "\">" + dateValueTypes[currentIndex].label + "</option>";
			
			// SELECT: DATE VALUE DELTA TYPES: Static definition
			var dateValueDeltaTypes = _this.data.dateDeltaTypes;
			var dateValueDeltaTypesOptionHTML = "";
			for (var currentIndex = 0; currentIndex < dateValueDeltaTypes.length; currentIndex++)
				dateValueDeltaTypesOptionHTML += "<option value=\"" + dateValueDeltaTypes[currentIndex].id + "\">" + dateValueDeltaTypes[currentIndex].value + "</option>";
			
			var ruleValueInputHTML =
				'<div style="display: inline-block; vertical-align: middle;"><div id="ruleValueInputContainer_' + valueId + '_' + _this.elemId + '" class="ruleValueInputContainer" style="display:none; margin: 0px 2px;">' +
					'<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr><td style="padding: 0px;">' +
						'<div class="grow-wrap">' +
							'<textarea class="ruleValueInput targetingValue fontSet" id="ruleValueInput_' + valueId + '_' + _this.elemId + '" filterId="ruleValueInput_' + valueId + '_' + _this.elemId + '" type="text" rows="1" onInput="this.parentNode.dataset.replicatedValue = this.value" /></textarea>' +
						'</div>' +
					'</td></tr></table>' +
				'</div></div>' +
				'<div style="display: inline-block; vertical-align: middle;"><div id="ruleDateValueTypeSelectContainer_' + valueId + '_' + _this.elemId + '" class="ruleDateValueTypeSelectContainer" style="display:none;">' +
					'<select id="ruleDateValueTypeSelect_' + valueId + '_' + _this.elemId + '" class="ruleDateValueTypeSelect style_select input160" >' +
						dateValueTypesOptionHTML +
					'</select>' +
				'</div></div>' +
				'<div style="display: inline-block; vertical-align: middle;"><div id="ruleDateValueInputContainer_' + valueId + '_' + _this.elemId + '" class="ruleDateValueInputContainer" style="display:none; margin: 0px 2px;">' +
					'<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr><td style="padding: 0px;">' +
						'<input id="ruleDateValueInput_' + valueId + '_' + _this.elemId + '" class="datePicker inputDate fontSet" />' +
					'</td></tr></table>' +
				'</div></div>' +
				'<div style="display: inline-block; vertical-align: middle;"><div id="ruleDateValueDeltaOperatorContainer_' + valueId + '_' + _this.elemId + '" class="ruleDateValueDeltaOperatorContainer" style="display:none; margin: 0px 2px;">' +
					'<input type="checkbox" id="ruleDateValueDeltaOperator_' + valueId + '_' + _this.elemId + '" title="' + client_messages.text.targeting.plus +';' + client_messages.text.targeting.minus + '" />' +
				'</div></div>' +
				'<div style="display: inline-block; vertical-align: middle;"><div id="ruleDateDeltaValueInputContainer_' + valueId + '_' + _this.elemId + '" class="ruleDateDeltaValueInputContainer" style="display:none; margin: 0px 2px;">' +
					'<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr><td style="padding: 0px;">' +
						'<input id="ruleDateDeltaValueInput_' + valueId + '_' + _this.elemId + '" filterId="ruleDateDeltaValueInput_' + valueId + '_' + _this.elemId + '" class="numeric fontSet input3digit" type="text" value="0" />' +
						$.alphanumeric.generateFilterTooltip('ruleDateDeltaValueInput_' + valueId + '_' + _this.elemId) +
					'</td></tr></table>' +
				'</div></div>' +
				'<div style="display: inline-block; vertical-align: middle;"><div id="ruleDateValueDeltaTypeContainer_' + valueId + '_' + _this.elemId + '" class="ruleDateValueDeltaTypeContainer" style="display:none;">' +
					'<select id="ruleDateValueDeltaTypeSelect_' + valueId + '_' + _this.elemId + '" class="ruleDateValueDeltaTypeSelect style_select input45">' +
						dateValueDeltaTypesOptionHTML +
					'</select>' +
				'</div></div>';
			
			return ruleValueInputHTML;
		},
		
		hideDateInputComponents : function(indicator) {
			// State Control: Hide rule date inputs 
			var _this = this;
			
			_this.getEle('ruleDateValueTypeSelectContainer_'+indicator).hide();
			_this.getEle('ruleDateValueInputContainer_'+indicator).hide();
			_this.getEle('ruleDateValueDeltaOperatorContainer_'+indicator).hide();
			_this.getEle('ruleDateDeltaValueInputContainer_'+indicator).hide();
			_this.getEle('ruleDateValueDeltaTypeContainer_'+indicator).hide();
		},
		
		initDateDeltaOperatorIButton : function (indicator) {
			var _this = this;

			if ( _this.getEle('ruleDateValueDeltaOperatorContainer_'+indicator).is(':visible') && 
					 _this.getEle('ruleDateValueDeltaOperatorContainer_'+indicator).find('.ibutton-container').length == 0)
					_this.getEle('ruleDateValueDeltaOperator_'+indicator).iButton({
						labelOn: _this.getEle('ruleDateValueDeltaOperator_'+indicator).attr('title').split(';')[1],
						labelOff: _this.getEle('ruleDateValueDeltaOperator_'+indicator).attr('title').split(';')[0],
						resizeHandle: false,
						resizeContainer: "auto",
						change: function(checkEle) {
							_this.updateRuleValuesBinding();
						}
			  		});
		},
		
		initRuleValueInputs : function() {
			var _this = this;

			var ruleValueHTML =	'<div id="valuesDisplayContainer_' + _this.elemId + '" style="display: none;">' +
									'<div style="display: inline-block; vertical-align: middle;">' +
										_this.getRuleValueInputHTML('a') +
										'<div style="display: inline-block; vertical-align: middle;">' +
											'<div id="ruleRangeJoinerContainer_' + _this.elemId + '" class="ruleRangeJoinerContainer" style="display:none; position: relative; top: 2px;">' +
												'&nbsp;' + client_messages.text.and + '&nbsp;' +
											'</div>' +
										'</div>' +
									'</div>' +
									'<div style="display: inline-block; vertical-align: middle;">' +
										'<div id="rangeValuesContainer_' + _this.elemId + '" style="display: none;">' +
											_this.getRuleValueInputHTML('b') +
										'</div>' +
									'</div>' +
								'</div>';

			_this.getEle('ruleValuesContainer').append(ruleValueHTML);
			
			$(_this.ruleValueManagerContainer).find('.ruleDateValueTypeSelect')
				.change( function(){ 
					_this.ruleDateValueTypeSelectToggle(this); 
					_this.updateRuleValuesBinding();
				})
				.styleActionElement();
			$(_this.ruleValueManagerContainer).find('.ruleDateValueDeltaTypeSelect')
				.change( function(){
					_this.updateRuleValuesBinding();
				})
				.styleActionElement();
			
			$(_this.ruleValueManagerContainer).find('.targetingValue')
				.keyup( function() {
					_this.updateRuleValuesBinding();
				})
				.bind('paste', function() {
					_this.updateRuleValuesBinding();
				})
				.alphanumeric({targetingValue:true});
			$(_this.ruleValueManagerContainer).find('.numeric')
				.keyup( function() {
					_this.updateRuleValuesBinding();
				})
				.bind('paste', function() {
					_this.updateRuleValuesBinding();
				})
				.alphanumeric({numeric:true});
			$(_this.ruleValueManagerContainer).find('.datePicker').datepicker({
				showOn			: "both",
                buttonImage		: 'data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="16"><path class="fill-info" d="M148 288h-40c-6.6 0-12-5.4-12-12v-40c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12zm108-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 96v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm192 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96-260v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V112c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48zm-48 346V160H48v298c0 3.3 2.7 6 6 6h340c3.3 0 6-2.7 6-6z"/></svg>',
                buttonImageOnly	: true,
				dateFormat		: defaultDateFormat,
				bindField		: true,
				altFormat		: defaultDateFormat,
				buttonText		: "Select a date",
				dayNamesMin		: ['S', 'M', 'T', 'W', 'T', 'F', 'S'],
				changeMonth		: true,
				changeYear		: true,
				onSelect		: function(dateText, inst) {
										_this.updateRuleValuesBinding();
								  }
			});
			$(_this.ruleValueManagerContainer).find('.datePicker').datepickerCustom();
		},
		
		ruleDateValueTypeSelectToggle : function(selectEle) {
			// Reveal/Hide Date Data Element inputs depending on dateType value (i.e. 'SpecificDate')
			var _this = this;
			
			var indicator = _this.getRangeInd(selectEle);
			var dateTypeValue = $(selectEle).find('option:selected').val();

			if (dateTypeValue == _this.data.dateType_specificDateId) {
				_this.getEle('ruleDateValueDeltaOperatorContainer_'+indicator).hide();
				_this.getEle('ruleDateDeltaValueInputContainer_'+indicator).hide();
				_this.getEle('ruleDateValueDeltaTypeContainer_'+indicator).hide();
				if ( !_this.getEle('ruleDateValueInputContainer_'+indicator).is(':visible') )
					_this.getEle('ruleDateValueInputContainer_'+indicator).showEle('normal');
			} else {
				_this.getEle('ruleDateValueInputContainer_'+indicator).hide();
				if ( !_this.getEle('ruleDateValueDeltaOperatorContainer_'+indicator).is(':visible') ) {
					_this.getEle('ruleDateValueDeltaOperatorContainer_'+indicator).showEle('normal');
					_this.getEle('ruleDateDeltaValueInputContainer_'+indicator).showEle('normal');
					_this.getEle('ruleDateValueDeltaTypeContainer_'+indicator).showEle('normal');
				};
			}
			
			_this.initDateDeltaOperatorIButton(indicator);

			// Hide all filter info icons
			$(_this.ruleValueManagerContainer).find('.infoIcon').hide();
		},
		
		setParameterized : function(state) {
			// State Control: Toggle parameterized flag/state
			var _this = this;

			var parameterizedInput = _this.targetEle.find('.parameterizedRuleCheckbox');

			if (state == 'on') {
				// Turn on parameterized
				if (!$(parameterizedInput).is(':checked')) {
					$(parameterizedInput).attr('checked','checked');
					// Hide: Rule values container
					_this.getEle('valuesDisplayContainer').hide();
				};
			} else if (state == 'off') {
				// Turn off parameterized
				if ( $(parameterizedInput).is(':checked')) {
					$(parameterizedInput).removeAttr('checked');
					// Show: Rule values container
					if ( !_this.getEle('valuesDisplayContainer').is(':visible') )
						_this.getEle('valuesDisplayContainer').showEle('normal');
				};
			};
		},

		// ******** END - Input Management APIs ***********
		// ************************************************
		
		// ******** BEGIN - Rule value parser APIs ********
		// ************************************************

		updateRuleValuesBinding : function() {
			// Parse and build 'dataElementValuesString' value.  See introduction
			// at top of this script for string format details.
			var _this = this;

			var rangeValue_a = ""; var rangeValue_b = "";
		
			// If: Date Type
			if ( _this.currentDataState_isDate ) {
				var dateTypeBuilderValue_a 	= _this.getEle('ruleDateValueTypeSelect_a').find('option:selected').attr('stringBuilderValue');
				var dateTypeId_a 			= _this.getEle('ruleDateValueTypeSelect_a').find('option:selected').val();

				if (dateTypeId_a == _this.data.dateType_specificDateId) {
					rangeValue_a += dateTypeBuilderValue_a + _this.data.rule_value_separator_str;
					rangeValue_a += _this.getDateValue('a');
				} else {
					rangeValue_a += dateTypeBuilderValue_a + _this.data.rule_value_separator_str;
					rangeValue_a += _this.getDateComponentValues('a').join(_this.data.rule_value_separator_str);
				}

				// If: Between
				if (_this.currentComparatorState_isRange) {
					var dateTypeBuilderValue_b 	= _this.getEle('ruleDateValueTypeSelect_b').find('option:selected').attr('stringBuilderValue');
					var dateTypeId_b 			= _this.getEle('ruleDateValueTypeSelect_b').find('option:selected').val();			
					rangeValue_b += _this.data.range_separator_str;
					if (dateTypeId_b == _this.data.dateType_specificDateId) {
						rangeValue_b += dateTypeBuilderValue_b + _this.data.rule_value_separator_str;
						rangeValue_b += _this.getDateValue('b');
					} else {
						rangeValue_b += dateTypeBuilderValue_b + _this.data.rule_value_separator_str;
						rangeValue_b += _this.getDateComponentValues('b').join(_this.data.rule_value_separator_str);
					}
				};
			// Else: Simple Type
			} else {
				
				rangeValue_a =  _this.getEle('ruleValueInput_a').val();
				// If: Between
				if (_this.currentComparatorState_isRange) {
					rangeValue_b = _this.getEle('ruleValueInput_b').val();
					rangeValue_b = _this.data.range_separator_str + rangeValue_b;
				};		
			}
			_this.targetEle.find('.variableComparedToValue').val( rangeValue_a + rangeValue_b );
		},

		getDateComponentValues : function (indicator) {
			// Get values for range date component [0] (+,-) [1] value [2] (d,m,y)
			var _this = this;
			var element = new Array();
			
			element[0] = _this.getEle('ruleDateValueDeltaOperator_'+indicator).is(':checked') ? '-' : '+';
			element[1] = _this.getEle('ruleDateDeltaValueInput_'+indicator).val(); 
			element[2] = _this.getEle('ruleDateValueDeltaTypeSelect_'+indicator).find('option:selected').val();
			return element;
		},

		getDateValue : function (indicator) {
			// Get value from a date picker interface
			var _this = this;

			var dateValue = _this.getEle('ruleDateValueInput_'+indicator).val();
			if (dateValue != '')
				return $.datepicker.formatDate('yy/mm/dd', new Date(dateValue));
			else 
				return '';
		},

		// ******** END - Rule value parser APIs **********
		// ************************************************
		
		// ** START - Rule value init from binding APIs ***
		// ************************************************

		setRuleValueInputsFromBinding : function() {
			// Parse values from 'dataElementValuesString' and set associated input values.  
			// See introduction at top of this script for string format details.
			var _this = this;

			var isRange			= _this.currentComparatorState_isRange;
			
			var rangeValue_a 	= _this.initialComparedToValue.split(_this.data.range_separator_str)[0];
			var rangeValue_b 	= _this.initialComparedToValue.split(_this.data.range_separator_str)[1];

			// Parameterized or Default value init
			if (rangeValue_b == null) 
				rangeValue_b = '';

			// If: Date Type
			if (_this.currentDataState_isDate) {
				var dateValue_a = rangeValue_a.split(_this.data.rule_value_separator_str)[0];
				
				if ( dateValue_a != '' )
					_this.getEle('ruleDateValueTypeSelect_a').selectOptionByAttr('stringBuilderValue', dateValue_a);
				else
					_this.defaultToSpecificDate('a');
					
				if (dateValue_a == _this.data.dateType_specificDateId || dateValue_a == _this.data.dateType_specificDateStr) {
					_this.defaultToSpecificDate('a');
					if (rangeValue_a.split(_this.data.rule_value_separator_str)[1] != '') {
						var myDate = new Date(rangeValue_a.split(_this.data.rule_value_separator_str)[1]);
						var dateValue = $.datepicker.formatDate('M, dd yy', myDate);
						_this.getEle('ruleDateValueInput_a').val( dateValue );
					}
				} else if (rangeValue_a != '') {
					_this.toggleIButton( _this.getEle('ruleDateValueDeltaOperator_a'), rangeValue_a.split(_this.data.rule_value_separator_str)[1] == '-', 'a' );
					_this.getEle('ruleDateDeltaValueInput_a').val( rangeValue_a.split(_this.data.rule_value_separator_str)[2] );
					_this.getEle('ruleDateValueDeltaTypeSelect_a').selectOptionByAttr('value', rangeValue_a.split(_this.data.rule_value_separator_str)[3] );
				}

				// If: Between
				if ( isRange ) {
					var dateValue_b = rangeValue_b.split(_this.data.rule_value_separator_str)[0];
					
					if ( dateValue_b != '' )
						_this.getEle('ruleDateValueTypeSelect_b').selectOptionByAttr('stringBuilderValue', dateValue_b);
					else
						_this.defaultToSpecificDate('b');

					if (dateValue_b == _this.data.dateType_specificDateId || dateValue_b == _this.data.dateType_specificDateStr) {
						_this.defaultToSpecificDate('b');
						if (rangeValue_b.split(_this.data.rule_value_separator_str)[1] != '') {
							var myDate = new Date(rangeValue_b.split(_this.data.rule_value_separator_str)[1]);
							var dateValue = $.datepicker.formatDate('M, dd yy', myDate);
							_this.getEle('ruleDateValueInput_b').val( dateValue );
						}
					} else if (rangeValue_b != '') {
						_this.toggleIButton( _this.getEle('ruleDateValueDeltaOperator_b'), rangeValue_b.split(_this.data.rule_value_separator_str)[1] == '-', 'b' );
						_this.getEle('ruleDateDeltaValueInput_b').val( rangeValue_b.split(_this.data.rule_value_separator_str)[2] );
						_this.getEle('ruleDateValueDeltaTypeSelect_b').selectOptionByAttr('value', rangeValue_b.split(_this.data.rule_value_separator_str)[3] );
					}
				} else {
					_this.defaultToSpecificDate('b');
				}
			// Else: Simple Type
			} else {
				
				_this.getEle('ruleValueInput_a').val(rangeValue_a);
				// If: Between
				if ( isRange )		
					_this.getEle('ruleValueInput_b').val(rangeValue_b);

				if ( _this.getEle('ruleValueInput_a').parent().is('.grow-wrap') ) {
					_this.getEle('ruleValueInput_a').parent().attr('data-replicated-value', rangeValue_a);
				}
			}

			_this.updateRuleValuesBinding();

		},
		
		toggleIButton: function(ele, toggleValue, indicator) {
			var _this = this;
			if ( $(ele).parent().is('.ibutton-container') )
				$(ele).iButton("toggle", toggleValue );
			else {
				_this.initDateDeltaOperatorIButton(indicator);
				setTimeout( function() { _this.toggleIButton(ele, toggleValue, indicator); }, 250 );
			}
		},
		
		defaultToSpecificDate : function(indicator) {
			// Set Data Element Value to SpecificDate by default
			// (For param date and initial date conditions)
			var _this = this;
			
			_this.getEle('ruleDateValueTypeSelect_'+indicator).selectOptionByAttr('value',_this.data.dateType_specificDateId);
			_this.getEle('ruleDateValueTypeSelect_'+indicator).change();
		},
		
		// **** END - Rule value init from binding APIs ***
		// ************************************************
		
		// ******** BEGIN - Common APIs *******************
		// ************************************************
		
		getEle : function(eleId) {
			var _this = this;
			return $(_this.ruleValueManagerContainer).find('#' + eleId + '_'+_this.elemId);
		},
		
		getRangeInd : function(ele) {
			return $(ele).attr('id').split('_')[1];
		}
		
		// ******** END - Common APIs *********************
		// ************************************************
		
		}; // end component
	};
	
	// instance manager
	ruleValueManager_component.inst = {};
	
})(jQuery);	