/** ruleBuilder r1 // 2013.03.27 // jQuery 1.4 // Prinova 
 * 
 * Dependencies:
 * jQuery 1.3 or later
 * 
 */

// ********************** Condition Data Element Value Inferface Overview ***********************
// *** The first two fields of a condition 'Data Element Variable' (i.e. dataElementSelect and 
// *** comparator) are bound and managed by Spring.  The value portion is dependant on a custom 
// *** interface.  The values are submitted and loaded using a string.  The expected string 
// *** formats are as follows:
// ***   Basic (Non-date):  value1@~@value2
// ***   Specific Date: 	dateType1@#@dateValue1@~@dateType2@#@dateValue2
// ***   Delta Date:
// ***		dateType1@#@operator1@#@value1@#@delta1@~@dateType2@#@operator2@#@value2@#@delta2
// ***	 Variable:			VAR%varId1@~@VAR%varId2
// ***	 Date Variable:
// ***		VAR%varId1@#@operator1@#@value1@#@delta1@~@VAR%varId2@#@operator2@#@value2@#@delta2
// *** Note: Date formats can be mixed.  For example a date may have a SpecificDate first 
// ***       component followed by a Delta Date second component.  The secondary component of
// ***       any date is optional.  In such instances no rangeSeparator is required
// ***       For example, the simple Specific Date 'dateType1@#@dateValue1'.
// **********************************************************************************************

function ruleBuilderFunc(id) {
	var _this = $.ruleBuilder.get( id );

	return this;
};

(function($) {
	$.ruleBuilder = {
		get	: function (obj) { 
			var o = $(obj); 
			if(!o.size()) o = $("#" + obj);
			if(!o.size()) return null; 
			return ruleBuilder_component.inst[o.attr('id')] || null; 
		},
		defaults : {
			comparatorTypes				: [ 
				               		     	{ id: 1, value: client_messages.text.targeting.value },
				               		     	{ id: 4, value: client_messages.text.targeting.value_from_file },
				               		     	{ id: 2, value: client_messages.text.targeting.variable },
				               		     	{ id: 3, value: client_messages.text.targeting.user_specified }
				               		      ],
			comparatorTriggers			: {
										 	range 		: 17,
										 	notEmpty 	: 16,
										 	empty 		: 7,
										 	isOneOf		: 4,
										 	isNotOneOf	: 18
									  	},
			dateDeltaTypes				: [
				              		    	{ id: 'd', value: client_messages.text.targeting.days },
				              		    	{ id: 'm', value: client_messages.text.targeting.months },
				              		    	{ id: 'y', value: client_messages.text.targeting.years }
				              		      ],
			dateSubtypeId				: '600',
			dateType_specificDateId		: '15',
			dateType_specificDateStr	: 'SpecificDate',	
			range_separator_str			: '@~@',
			rule_value_separator_str	: '@#@',	
			rule_variable_indicator		: 'VAR%',
			data_file_indicator			: '%DATA_FILE%',
			type						: 'default',
			externalUpdateTrigger		: null,
			afterPrimaryVariableToggle	: null,
			debug						: false
		}
	};

	$.fn.ruleBuilder = function (opts) {
		return this.each(function() {
			var conf = $.extend({},opts);
			if(conf !== false) new ruleBuilder_component().init(this, conf);
		});
	};
	
	function ruleBuilder_component () {
		return {
			
		data : $.extend({},$.ruleBuilder.defaults),	

		init : function(elem, conf) {
			var _this = this;

			ruleBuilder_component.inst[$(elem).attr("id")] = _this;
			_this.data = $.extend(false, {}, this.data, conf);
			
			_this.elemId = $(elem).attr("id");
			_this.targetEle = $(elem);
			
			_this.debug = _this.data.debug;
			
			_this.currentComparatorState_isRange = false;
			_this.currentDataState_isDate = false;
			
			_this.ruleValueVariableSelectInit_a = false;
			_this.ruleValueVariableSelectInit_b = false;
			_this.ruleValueInitComplete = false;
			
			_this.initialComparedToValue = _this.targetEle.find('.variableComparedToValue').val();

			if ( _this.data.externalUpdateTrigger != null)
				$(_this.data.externalUpdateTrigger).hover( function() { _this.updateRuleValuesBinding(); } );

			_this.initDisplay();
		},
		
		initDisplay : function() {
			var _this = this;
			
			var displayHTML = "";
			displayHTML +=  '<div id="ruleBuilderContainer_' + _this.elemId + '" class="ruleBuilderContainer pddng-vertical-lv1">' +
								'<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr>' +
									'<td width="1%" style="padding: 0px; vertical-align: middle;">' +
										'<select id="variableSelect_' + _this.elemId + '" title="" class="variableSelect style_select inputXL" style="display: none;">' +
											'<option value="0">' + client_messages.text.loading + '</option>' +
										'</select>' + 
									'</td>' +
									'<td width="1%" style="padding: 0px; vertical-align: middle;">' +
										'<select id="comparatorSelect_' + _this.elemId + '" title="" class="comparatorSelect style_select input135" style="display: none;">' +
											'<option value="0">' + client_messages.text.loading + '</option>' +
										'</select>' + 
									'</td>' +
									'<td style="padding: 0px; vertical-align: middle;">' +
										'<div id="comparatorTypesContainer_' + _this.elemId + '" style="display: inline-block; vertical-align: middle;">' +
											'<select id="comparatorTypesSelect_' + _this.elemId + '" title="" class="comparatorTypesSelect style_select input135" style="display: none;">' +
											'</select>' + 
										'</div>' +
										'<div style="display: inline-block; vertical-align: middle;">' +
											'<div id="ruleValuesContainer_' + _this.elemId + '">' +
											'</div>' + 
										'</div>' +
										'<div style="display: inline-block; vertical-align: middle;">' +
											'<div id="dataFileInterfaceContainer_' + _this.elemId + '" style="display: none;">' +
												'<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr>' +
													'<td>' +
														'<div class="dataFileMessageContainer" style=" margin: 0 8px; padding: 0 12px; border: 1px solid #bbb; border-radius: 3px; color: #444; font-size: 12px; background-color: #f5f5f5; display: none;">' +
														'</div>' + 
														'<div class="dataFileUploadIndicator" align="center" style="display: none;">' +
															'<div class="fileProcessing"></div>' +
														'</div>' + 
													'</td>' +
													'<td width="1%" align="left" style="vertical-align: middle;">' +
														'<form id="fileUpload_dataFile_' + _this.elemId + '" method="POST" enctype="multipart/form-data" class="position-relative overflow-hidden">' +
															'<input id="fileInput_dataFile_' + _this.elemId + '" type="file" name="files[]" class="dataFileUploadInput" />' +
															'<div id="dataFileUploadBtn_' + _this.elemId + '" class="actionBtn_roundAll actionBtn" style="white-space: nowrap;">' +
																'<div class="addIcon detailTip" title="|<div class=\'detailTipText\'>' + client_messages.text.targeting.upload_data_file + '</div>" style="margin: 6px 6px 0px 6px;"></div>' +
															'</div>' +
														'</form>' +
													'</td>' +
												'</tr></table>' +
											'</div>' + 
										'</div>' +
									'</td>' +
								'</tr></table>' +
							'</div>';
			
			_this.targetEle.append(displayHTML);

			_this.ruleBuilderContainer = _this.targetEle.find('#ruleBuilderContainer_'+_this.elemId);
			
			if ( _this.data.type == "filter" )
				_this.getEle('comparatorTypesContainer').remove();
			
			_this.initRuleValueInputs();
			
			// SELECT: VARIABLES: Async type ahead search
			var isVariableSelectInit = true;
			_this.getEle('variableSelect')
				.change( function(){ 
					_this.variableSelectToggle(); 
					_this.updateRuleValuesBinding();
				})
				.styleActionElement({
					maxItemsInList	: 30,
                    maxItemDisplay	: 5,
					isAsync			: true,
					getItemsAsync 	: {
						loadOnDemand		: false,
						getItemsURL			: context+"/getVariables.form",
						fnExtServerParams	: function () {
													var stampDate = new Date();
													var currentVariableId = _this.targetEle.find('.variableId').val();
													
													var obj = 	[
																	{"name" : "type", "value" : "variablesList"},
																	{"name" : "documentId", "value" :  _this.data.touchpointContextId},
																	{"name" : "selectedVariableId", "value" : (currentVariableId && currentVariableId != "" && currentVariableId != null ? currentVariableId : -1) },
																	{"name" : "targetingFiltered", "value" : "true"}
																];
													return obj;
											  }
					  	},
					afterFilter		: function(ele, o) {
							if (isVariableSelectInit) {
								isVariableSelectInit = false;

								// INIT: Set Variable Select
								_this.variableSelectToggle();
								// INIT: Rule Value Inputs
								_this.setRuleValueInputsFromBinding();
							}
						}
				});

			// SELECT: COMPARATORS
			_this.getEle('comparatorSelect')
				.change( function(){ 
					_this.comparatorSelectToggle(); 
					_this.updateRuleValuesBinding();
				})
				.styleActionElement();
				
			if ( _this.data.type != "filter" ) {				
				// SELECT: COMPARATOR TYPES: Static definition
				var comparatorTypes = _this.data.comparatorTypes;
				for (var currentIndex = 0; currentIndex < comparatorTypes.length; currentIndex++)
					_this.getEle('comparatorTypesSelect')
						.append("<option value=\"" + comparatorTypes[currentIndex].id + "\">" + comparatorTypes[currentIndex].value + "</option>");
				
				_this.getEle('comparatorTypesSelect')
					.change( function(){ 
						_this.comparatorTypeSelectToggle(); 
						_this.updateRuleValuesBinding();
					})
					.styleActionElement();
			
				// INIT: Comparator Type select value and display
				//COMPARE_INPUT = 1, COMPARE_VARIABLE = 2, COMPARE_PARAMETERIZED = 3, COMPARE_DATA_FILE = 4;
				if ( _this.initialComparedToValue.indexOf(_this.data.data_file_indicator) != -1 ) {
					_this.getEle('comparatorTypesSelect').selectOptionByAttr('value',4);
				} else if ( _this.targetEle.find('.parameterizedRuleCheckbox').is(':checked') ) {
					_this.getEle('comparatorTypesSelect').selectOptionByAttr('value',3);
					_this.compareToInputsToggle('input');
				} else if ( _this.initialComparedToValue.indexOf(_this.data.rule_variable_indicator) != -1 ) {
					_this.getEle('comparatorTypesSelect').selectOptionByAttr('value',2);
					_this.compareToInputsToggle('variable');
				} else {
					_this.getEle('comparatorTypesSelect').selectOptionByAttr('value',1);
					_this.compareToInputsToggle('input');
				}
			}
			
			if ( _this.initialComparedToValue.indexOf(_this.data.data_file_indicator) != -1 &&
				 _this.targetEle.find('.dataFilePath').val().length > 0 ) {
				var stampDate = new Date();
				var filePath = _this.initialComparedToValue.replace(_this.data.data_file_indicator,'');
				var filePathParts = filePath.split(/[\/\\]+/);
				var filename = filePathParts[filePathParts.length -1];
				var dataFileLinkHTML = 
					'<span style="font-size: 11px; font-weight: bold; padding-right: 8px;">' + client_messages.text.targeting.file + ':</span>' +
					'<a href="javascript:javascriptHref(\'' + context + '/download/xml.form?file=' + filePath.replace(/\\/g,"/") + "&action=saveFile&cacheStamp=" + stampDate.getTime() + '\');">' +
						filename + 
					'</a>';
				
				_this.getEle('dataFileInterfaceContainer').find('.dataFileMessageContainer').html( dataFileLinkHTML );
			} else
				_this.getEle('dataFileInterfaceContainer').find('.dataFileMessageContainer').html( '<span style="font-size: 11px; font-weight: bold; padding-right: 8px;">' + client_messages.text.targeting.file + ':</span>' + client_messages.text.targeting.click_to_upload );
			_this.getEle('dataFileInterfaceContainer').find('.dataFileMessageContainer').show();

		},
		
		// ******** BEGIN - Input Management APIs *********
		// ************************************************
		
		comparatorSelectToggle : function() {
			var _this = this;
			
			if ( _this.debug )
				console.log("comparatorSelectToggle");
			
			var currentComparatorValue = _this.getEle('comparatorSelect').val();
			_this.currentComparatorState_isRange = (currentComparatorValue == _this.data.comparatorTriggers.range);
			
			// If comparator select has been initialized:  Update value based on selection
			if ( _this.getEle('comparatorSelect').find('option').length > 0 && _this.getEle('comparatorSelect').val() > 0 )
				_this.targetEle.find('.variableComparatorId').val( _this.getEle('comparatorSelect').val() );
			
			var comparatorType = (_this.getEle('comparatorTypesSelect').length != 0 ? _this.getEle('comparatorTypesSelect').val() : 1);

			// If: IsOneOf or IsNotOneOf: Add comparator type option 'value from data file'(ID:4); else hide option
			if ( currentComparatorValue == _this.data.comparatorTriggers.isOneOf || currentComparatorValue == _this.data.comparatorTriggers.isNotOneOf )
				_this.getEle('comparatorTypesSelect').showOption('comparatorTypesSelect_' + _this.elemId + '_4');
			else {
				if ( comparatorType == 4 )
					_this.getEle('comparatorTypesSelect').selectOption('comparatorTypesSelect_' + _this.elemId + '_1')
				_this.getEle('comparatorTypesSelect').hideOption('comparatorTypesSelect_' + _this.elemId + '_4');
			}


			if ( currentComparatorValue != _this.data.comparatorTriggers.notEmpty && currentComparatorValue != _this.data.comparatorTriggers.empty ) {
				
				// If: IsOneOf or IsNotOneOf use larger textarea input, else use standard input
				var toggleInput = false;
				var valueInput = $('#ruleValueInput_a_'+ _this.elemId);
				var valueInputHTML = "";
				if ( (currentComparatorValue == _this.data.comparatorTriggers.isOneOf || currentComparatorValue == _this.data.comparatorTriggers.isNotOneOf) && !$(valueInput).is('textarea') ) {
					toggleInput 	= true;
					valueInputHTML 	= '<textarea class="ruleValueInput targetingValue fontSet" id="ruleValueInput_a_' + _this.elemId + '" filterId="ruleValueInput_a_' + _this.elemId + '" rows="3" style="width: 330px;"></textarea>';
				} else if ( currentComparatorValue != _this.data.comparatorTriggers.isOneOf && currentComparatorValue != _this.data.comparatorTriggers.isNotOneOf && $(valueInput).is('textarea') ) {
					toggleInput 	= true;
					valueInputHTML 	= '<input class="ruleValueInput targetingValue fontSet inputL" id="ruleValueInput_a_' + _this.elemId + '" filterId="ruleValueInput_a_' + _this.elemId + '" type="text" />';
				}
				if ( toggleInput ) {
					valueInputHTML = $(valueInputHTML).val($(valueInput).val());
					$(valueInput).after(valueInputHTML);
					$(valueInput).remove();
					$('#ruleValueInput_a_'+ _this.elemId)
						.keyup( function() {
							_this.updateRuleValuesBinding();
						})
						.bind('paste', function() {
							_this.updateRuleValuesBinding();
						})
						.alphanumeric({targetingValue:true});
				}
				
				// Show: Comparator Select/Values inputs
				if ( !_this.getEle('valuesDisplayContainer').is(':visible') )
					_this.getEle('valuesDisplayContainer').show();
				if ( !_this.getEle('comparatorTypesContainer').is(':visible') )
					_this.getEle('comparatorTypesContainer').showEle('normal');
				
				// Show: If range: Range inputs
				if ( _this.currentComparatorState_isRange ) {
					if ( !_this.getEle('rangeValuesContainer').is(':visible') ) {
						_this.getEle('rangeValuesContainer').showEle('normal');
						_this.getEle('ruleRangeJoinerContainer').showEle('normal');
						_this.initDateDeltaOperatorIButton('b');
					}
				} else {
					_this.getEle('rangeValuesContainer').hide();
					_this.getEle('ruleRangeJoinerContainer').hide();
				};

				if (comparatorType == 3) {
					_this.getEle('ruleValueInputContainer_a').hide();
					_this.getEle('ruleValueInputContainer_b').hide();
				}
			} else {
				_this.getEle('valuesDisplayContainer').hide();
				_this.getEle('comparatorTypesContainer').hide();
			}
			
			_this.toggleInputTypeDisplay();
			
		},
		
		compareToInputsToggle : function(type) {
			var _this = this;
			
			if ( _this.debug )
				console.log("compareToInputsToggle");
			
			// State Control: Manage input displays based on compareTo selection
			// Refresh values display for updates that may have occurred while inputs were hidden 
			// (i.e. 'User specified' comparator type)
			_this.comparatorSelectToggle();

			if (type == 'variable') {
				if (_this.currentDataState_isDate) {
					_this.hideDateInputComponents('a');
					_this.hideDateInputComponents('b');
				} else {
					_this.getEle('ruleValueInputContainer_a').hide();
					_this.getEle('ruleValueInputContainer_b').hide();
				}
				_this.getEle('ruleValueVariableSelectContainer_a').showEle('normal');
				_this.getEle('ruleValueVariableSelectContainer_b').showEle('normal');			
			} else if (type == 'input') {
				_this.getEle('ruleValueVariableSelectContainer_a').hide();
				_this.getEle('ruleValueVariableSelectContainer_b').hide();
				if (_this.currentDataState_isDate) {
					_this.getEle('ruleValueInputContainer_a').hide();
					_this.getEle('ruleValueInputContainer_b').hide();
					_this.getEle('ruleDateValueTypeSelectContainer_a').showEle('normal');
					_this.getEle('ruleDateValueTypeSelect_a').change();
					_this.getEle('ruleDateValueTypeSelectContainer_b').showEle('normal');
					_this.getEle('ruleDateValueTypeSelect_b').change();
				} else {
					_this.hideDateInputComponents('a');
					_this.hideDateInputComponents('b');
					_this.getEle('ruleValueInputContainer_a').showEle('normal');
					_this.getEle('ruleValueInputContainer_b').showEle('normal');
				};
			};
		},
		
		comparatorTypeSelectToggle : function() {
			// State Control: Manage all changes in compareTo selection
			var _this = this;
			
			if ( _this.debug )
				console.log("comparatorTypeSelectToggle");
			
			var comparatorType = (_this.getEle('comparatorTypesSelect').length != 0 ? _this.getEle('comparatorTypesSelect').val() : 1);

			_this.toggleInputTypeDisplay();
			
			if (comparatorType == 1) { // Value
				_this.compareToInputsToggle('input');
				_this.setParameterized('off');
			} else if (comparatorType == 2) { // Variable
				// INIT: Default rule value variable select (no preset value)
				if (_this.ruleValueInitComplete) {
					if (!_this.ruleValueVariableSelectInit_a) {
						_this.initRuleValueVariableSelect('a',null);
						_this.ruleValueVariableSelectInit_a = true;
					}
					if (!_this.ruleValueVariableSelectInit_b) {
						_this.initRuleValueVariableSelect('b',null);
						_this.ruleValueVariableSelectInit_b = true;
					}
				}

				_this.compareToInputsToggle('variable');
				_this.setParameterized('off');
			} else if (comparatorType == 3) {  // User specified
				_this.compareToInputsToggle('input');
				_this.setParameterized('on');
			} else if (comparatorType == 4) {  // Value from data file
				_this.initFileUploadInterface();
				_this.setParameterized('off');
			}
			
			// Data Management: Set compareTo variable selects to show only matching
			//					variables for selected primary variable selection
			_this.getEle('ruleValueVariableSelect_a').refreshOptions();
			_this.getEle('ruleValueVariableSelect_b').refreshOptions();
		},
		
		getRuleValueInputHTML : function(valueId) {
			var _this = this;
			
			// SELECT: DATE VALUE TYPES: Static definition
			var dateValueTypes = _this.data.dateValueTypes;
			var dateValueTypesOptionHTML = "";
			for (var currentIndex = 0; currentIndex < dateValueTypes.length; currentIndex++)
				dateValueTypesOptionHTML += "<option value=\"" + dateValueTypes[currentIndex].id + "\" stringBuilderValue=\"" + dateValueTypes[currentIndex].value + "\">" + dateValueTypes[currentIndex].label + "</option>";
			
			// SELECT: DATE VALUE DELTA TYPES: Static definition
			var dateValueDeltaTypes = _this.data.dateDeltaTypes;
			var dateValueDeltaTypesOptionHTML = "";
			for (var currentIndex = 0; currentIndex < dateValueDeltaTypes.length; currentIndex++)
				dateValueDeltaTypesOptionHTML += "<option value=\"" + dateValueDeltaTypes[currentIndex].id + "\">" + dateValueDeltaTypes[currentIndex].value + "</option>";
			
			var ruleValueInputHTML =
				'<div style="display: inline-block; vertical-align: middle;"><div id="ruleValueVariableSelectContainer_' + valueId + '_' + _this.elemId + '" class="ruleValueVariableSelectContainer" style="display:none;">' +
					'<select id="ruleValueVariableSelect_' + valueId + '_' + _this.elemId + '" title="" class="ruleValueVariableSelect style_select inputL" style="display: none;">' +
						'<option value="0">' + client_messages.text.loading + '</option>' +
					'</select>' + 
				'</div></div>' +
				'<div style="display: inline-block; vertical-align: middle;"><div id="ruleValueInputContainer_' + valueId + '_' + _this.elemId + '" class="ruleValueInputContainer" style="display:none;">' +
					'<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr><td>' +
						'<input class="ruleValueInput targetingValue fontSet inputL" id="ruleValueInput_' + valueId + '_' + _this.elemId + '" filterId="ruleValueInput_' + valueId + '_' + _this.elemId + '" type="text" />' +
						$.alphanumeric.generateFilterTooltip('ruleValueInput_' + valueId + '_' + _this.elemId) +
					'</td></tr></table>' +
				'</div></div>' +
				'<div style="display: inline-block; vertical-align: middle;"><div id="ruleDateValueTypeSelectContainer_' + valueId + '_' + _this.elemId + '" class="ruleDateValueTypeSelectContainer" style="display:none;">' +
					'<select id="ruleDateValueTypeSelect_' + valueId + '_' + _this.elemId + '" class="ruleDateValueTypeSelect style_select inputL" >' +
						dateValueTypesOptionHTML +
					'</select>' +
				'</div></div>' +
				'<div style="display: inline-block; vertical-align: middle;"><div id="ruleDateValueInputContainer_' + valueId + '_' + _this.elemId + '" class="ruleDateValueInputContainer" style="display:none;">' +
					'<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr><td>' +
						'<input id="ruleDateValueInput_' + valueId + '_' + _this.elemId + '" class="datePicker inputDate fontSet" />' +
					'</td></tr></table>' +
				'</div></div>' +
				'<div style="display: inline-block; vertical-align: middle;"><div id="ruleDateValueDeltaOperatorContainer_' + valueId + '_' + _this.elemId + '" class="ruleDateValueDeltaOperatorContainer" style="display:none;">' +
					'<input type="checkbox" id="ruleDateValueDeltaOperator_' + valueId + '_' + _this.elemId + '" title="' + client_messages.text.targeting.plus +';' + client_messages.text.targeting.minus + '" />' +
				'</div></div>' +
				'<div style="display: inline-block; vertical-align: middle;"><div id="ruleDateDeltaValueInputContainer_' + valueId + '_' + _this.elemId + '" class="ruleDateDeltaValueInputContainer" style="display:none;">' +
					'<table width="100%" cellspacing="0" cellpadding="0" border="0"><tr><td>' +
						'<input id="ruleDateDeltaValueInput_' + valueId + '_' + _this.elemId + '" filterId="ruleDateDeltaValueInput_' + valueId + '_' + _this.elemId + '" class="numeric fontSet input3digit" type="text" value="0" />' +
						$.alphanumeric.generateFilterTooltip('ruleDateDeltaValueInput_' + valueId + '_' + _this.elemId) +
					'</td></tr></table>' +
				'</div></div>' +
				'<div style="display: inline-block; vertical-align: middle;"><div id="ruleDateValueDeltaTypeContainer_' + valueId + '_' + _this.elemId + '" class="ruleDateValueDeltaTypeContainer" style="display:none;">' +
					'<select id="ruleDateValueDeltaTypeSelect_' + valueId + '_' + _this.elemId + '" class="ruleDateValueDeltaTypeSelect style_select inputS">' +
						dateValueDeltaTypesOptionHTML +
					'</select>' +
				'</div></div>';
			
			return ruleValueInputHTML;
		},
		
		hideDateInputComponents : function(indicator) {
			// State Control: Hide rule date inputs 
			var _this = this;
			
			_this.getEle('ruleDateValueTypeSelectContainer_'+indicator).hide();
			_this.getEle('ruleDateValueInputContainer_'+indicator).hide();
			_this.getEle('ruleDateValueDeltaOperatorContainer_'+indicator).hide();
			_this.getEle('ruleDateDeltaValueInputContainer_'+indicator).hide();
			_this.getEle('ruleDateValueDeltaTypeContainer_'+indicator).hide();
		},
		
		initDateDeltaOperatorIButton : function (indicator) {
			var _this = this;

			if ( _this.getEle('ruleDateValueDeltaOperatorContainer_'+indicator).is(':visible') && 
					 _this.getEle('ruleDateValueDeltaOperatorContainer_'+indicator).find('.ibutton-container').length == 0)
					_this.getEle('ruleDateValueDeltaOperator_'+indicator).iButton({
						labelOn: _this.getEle('ruleDateValueDeltaOperator_'+indicator).attr('title').split(';')[1],
						labelOff: _this.getEle('ruleDateValueDeltaOperator_'+indicator).attr('title').split(';')[0],
						resizeHandle: false,
						resizeContainer: "auto",
						change: function(checkEle) {
							_this.updateRuleValuesBinding();
						}
			  		});
		},
		
		initFileUploadInterface: function() {
			var _this = this;
			
			if ( _this.debug )
				console.log("initFileUploadInterface");

			if ( !_this.getEle('dataFileInterfaceContainer').is('.fileUploadInit') ) {

				_this.getEle('fileInput_dataFile')
					.fileupload({
				        url				: context+'/uploadFileHandler.form?type=targetingData&action=upload&tk='+getParam('tk'),
				        dataType		: 'json',
			            add: function (e, data) {

			                
			                // Validation: Prevent multiple file upload
			                if ( data.files.length > 1 ) {
			                	_this.getEle('dataFileUploadBtn').popupFactory({
									title				: client_messages.content_editor.oops,
									trigger				: "instant",
									fnSetContent		: function(o) {
															return "<div align=\"left\" style=\"padding: 5px 15px; font-size: 11px;\">" + client_messages.text.too_many_files_selected + "</div>";
														  }
		    						});
		                		
		                		return;
			                }
			                	
			                data.submit();
			            },
				        done			: function (e, data) {
				        					
				        					var stampDate = new Date();
				        					var fileHrefHtml = '<span style="font-size: 11px; font-weight: bold; padding-right: 8px;">' + client_messages.text.pending_save + ':</span><a href="javascript:javascriptHref(\'' + context + '/download/xml.form?file=' + data.result.files[0].url + "&action=saveFile&cacheStamp=" + stampDate.getTime() + '\');">' +
				        											data.result.files[0].name + 
																'</a>';
				        					_this.getEle('dataFileInterfaceContainer').find('.dataFileMessageContainer').html( fileHrefHtml );
				        					
				        					_this.targetEle.find('.dataFilePath').val( data.result.files[0].url );
				        					_this.targetEle.find('.dataFileSandboxFileId').val( data.result.files[0].sandboxId );
				        					
				        				  }
				    })
				    .bind('fileuploadstart', function (e) {
						_this.getEle('dataFileInterfaceContainer').find('.dataFileMessageContainer').hide();
						_this.getEle('dataFileInterfaceContainer').find('.dataFileUploadIndicator').show();
				    })
				    .bind('fileuploadalways', function (e, data) {
						_this.getEle('dataFileInterfaceContainer').find('.dataFileUploadIndicator').hide();
						_this.getEle('dataFileInterfaceContainer').find('.dataFileMessageContainer').show();
				    })
				    .mouseover( function() {
				    	var btn = _this.getEle('dataFileUploadBtn');
						if ( !$(btn).hasClass('actionBtn_disabled') && !$(btn).hasClass('actionBtn_selected') ) {
							$(btn).removeClass('actionBtn');
							$(btn).addClass('actionBtn_hov');
						}
					})
					.mouseout( function() {
						var btn = _this.getEle('dataFileUploadBtn');
						if ( !$(btn).hasClass('actionBtn_disabled') && !$(btn).hasClass('actionBtn_selected') ) {
							$(btn).removeClass('actionBtn_hov');
							$(btn).addClass('actionBtn');
						}
					})
					.mousedown( function() {
						var btn = _this.getEle('dataFileUploadBtn');
						if ( !$(btn).hasClass('actionBtn_disabled') )
							$(btn).removeClass('actionBtn actionBtn_hov').addClass('actionBtn_selected');
					})
					.mouseup( function() {
						var btn = _this.getEle('dataFileUploadBtn');
						if ( !$(btn).hasClass('actionBtn_disabled') )
							$(btn).removeClass('actionBtn_selected').addClass('actionBtn');
					});
				
					_this.getEle('dataFileUploadBtn')
						.mouseover( function() {
					    	var btn = _this.getEle('dataFileUploadBtn');
							// File Upload Button: Position hidden file upload field over button
					    	_this.getEle('fileInput_dataFile').css({'top': $(btn).position().top+'px', 'left': ($(btn).position().left)+'px'});
						});
				
					_this.getEle('dataFileInterfaceContainer').addClass('fileUploadInit');
			}

		},
		
		initRuleValueInputs : function() {
			var _this = this;
			
			if ( _this.debug )
				console.log("initRuleValueInputs");

			var ruleValueHTML =	'<div id="valuesDisplayContainer_' + _this.elemId + '" style="display: none;">' +
									'<div style="display: inline-block; vertical-align: middle;">' +
										_this.getRuleValueInputHTML('a') +
										'<div style="display: inline-block; vertical-align: middle;">' +
											'<div id="ruleRangeJoinerContainer_' + _this.elemId + '" class="ruleRangeJoinerContainer" style="display:none;">' +
												'&nbsp;' + client_messages.text.and + '&nbsp;' +
											'</div>' +
										'</div>' +
									'</div>' +
									'<div style="display: inline-block; vertical-align: middle;">' +
										'<div id="rangeValuesContainer_' + _this.elemId + '" style="display: none;">' +
											_this.getRuleValueInputHTML('b') +
										'</div>' +
									'</div>' +
								'</div>';

			_this.getEle('ruleValuesContainer').append(ruleValueHTML);
			
			$(_this.ruleBuilderContainer).find('.ruleDateValueTypeSelect')
				.change( function(){ 
					_this.ruleDateValueTypeSelectToggle(this); 
					_this.updateRuleValuesBinding();
				})
				.styleActionElement();
			$(_this.ruleBuilderContainer).find('.ruleDateValueDeltaTypeSelect')
				.change( function(){
					_this.updateRuleValuesBinding();
				})
				.styleActionElement();
			
			$(_this.ruleBuilderContainer).find('.targetingValue')
				.keyup( function() {
					_this.updateRuleValuesBinding();
				})
				.bind('paste', function() {
					_this.updateRuleValuesBinding();
				})
				.alphanumeric({targetingValue:true});
			$(_this.ruleBuilderContainer).find('.numeric')
				.keyup( function() {
					_this.updateRuleValuesBinding();
				})
				.bind('paste', function() {
					_this.updateRuleValuesBinding();
				})
				.alphanumeric({numeric:true});
			$(_this.ruleBuilderContainer).find('.datePicker').datepicker({
				showOn			: "both",
                buttonImage		: 'data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="16"><path class="fill-info" d="M148 288h-40c-6.6 0-12-5.4-12-12v-40c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12zm108-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 96v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm192 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96-260v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V112c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48zm-48 346V160H48v298c0 3.3 2.7 6 6 6h340c3.3 0 6-2.7 6-6z"/></svg>',
                buttonImageOnly	: true,
				dateFormat		: defaultDateFormat,
				bindField		: true,
				altFormat		: defaultDateFormat,
				buttonText		: client_messages.text.select_date,
				dayNamesMin		: ['S', 'M', 'T', 'W', 'T', 'F', 'S'],
				changeMonth		: true,
				changeYear		: true,
				onSelect		: function(dateText, inst) {
										_this.updateRuleValuesBinding();
								  }
			});
			$(_this.ruleBuilderContainer).find('.datePicker').datepickerCustom();
		},
		
		ruleDateValueTypeSelectToggle : function(selectEle) {
			// Reveal/Hide Date Data Element inputs depending on dateType value (i.e. 'SpecificDate')
			var _this = this;
			
			if ( _this.debug )
				console.log("ruleDateValueTypeSelectToggle");
			
			var indicator = _this.getRangeInd(selectEle);
			var dateTypeValue = $(selectEle).find('option:selected').val();

			if (dateTypeValue == _this.data.dateType_specificDateId) {
				_this.getEle('ruleDateValueDeltaOperatorContainer_'+indicator).hide();
				_this.getEle('ruleDateDeltaValueInputContainer_'+indicator).hide();
				_this.getEle('ruleDateValueDeltaTypeContainer_'+indicator).hide();
				if ( !_this.getEle('ruleDateValueInputContainer_'+indicator).is(':visible') )
					_this.getEle('ruleDateValueInputContainer_'+indicator).showEle('normal');
			} else {
				_this.getEle('ruleDateValueInputContainer_'+indicator).hide();
				if ( !_this.getEle('ruleDateValueDeltaOperatorContainer_'+indicator).is(':visible') ) {
					_this.getEle('ruleDateValueDeltaOperatorContainer_'+indicator).showEle('normal');
					_this.getEle('ruleDateDeltaValueInputContainer_'+indicator).showEle('normal');
					_this.getEle('ruleDateValueDeltaTypeContainer_'+indicator).showEle('normal');
				};
			}
			
			_this.initDateDeltaOperatorIButton(indicator);

			// Hide all filter info icons
			$(_this.ruleBuilderContainer).find('.infoIcon').hide();
		},
		
		setParameterized : function(state) {
			// State Control: Toggle parameterized flag/state
			var _this = this;
			
			if ( _this.debug )
				console.log("setParameterized");

			var parameterizedInput = _this.targetEle.find('.parameterizedRuleCheckbox');

			if (state == 'on') {
				
				// Turn on parameterized
				if (!$(parameterizedInput).is(':checked'))
					$(parameterizedInput).attr('checked','checked');

				// Hide: Rule values container/data file container
				_this.getEle('valuesDisplayContainer').hide();
				_this.getEle('dataFileInterfaceContainer').hide();
				
			} else if (state == 'off') {
				// Turn off parameterized
				if ( $(parameterizedInput).is(':checked')) {
					$(parameterizedInput).removeAttr('checked');
					
					_this.toggleInputTypeDisplay();
				};
			};
		},
		
		toggleInputTypeDisplay: function() {
			var _this = this;
			
			if ( _this.debug )
				console.log("toggleInputTypeDisplay");
			
			var comparatorType = (_this.getEle('comparatorTypesSelect').length != 0 ? _this.getEle('comparatorTypesSelect').val() : 1);
			var currentComparatorValue = _this.getEle('comparatorSelect').val();
			
			if (currentComparatorValue == _this.data.comparatorTriggers.empty || currentComparatorValue == _this.data.comparatorTriggers.notEmpty) {
				_this.getEle('valuesDisplayContainer').hide();
				_this.getEle('dataFileInterfaceContainer').hide();
				return;
			}
			
			if (comparatorType == 4 && ( currentComparatorValue == _this.data.comparatorTriggers.isOneOf || currentComparatorValue == _this.data.comparatorTriggers.isNotOneOf ) ) {
				_this.getEle('valuesDisplayContainer').hide();
				if ( !_this.getEle('dataFileInterfaceContainer').is(':visible') )
					_this.getEle('dataFileInterfaceContainer').showEle('normal');
			} else {
				_this.getEle('dataFileInterfaceContainer').hide();
				if ( !_this.getEle('valuesDisplayContainer').is(':visible') )
					_this.getEle('valuesDisplayContainer').show();
			}
		},

		variableSelectToggle : function() {
			var _this = this;
			
			if ( _this.debug )
				console.log("variableSelectToggle");
			
			var selectedOption = _this.getEle('variableSelect').find('option:selected');
			
			_this.currentDataState_isDate = ( selectedOption.attr('dataSubtype') == _this.data.dateSubtypeId );

			_this.targetEle.find('.variableId').val( $(selectedOption).val() );

			var comparatorOptions = "";
			if ( $(selectedOption).attr('dataSubtype') == 0) {
				comparatorOptions += "<option id=\"0\" value=\"0\">" + client_messages.text.targeting.unknown_type + "</option>";
			} else {
				if ( !$(selectedOption).attr('dataSubtype') || _this.data.comparators[ $(selectedOption).attr('dataSubtype') ].length == 0 ) {
					comparatorOptions += "<option id=\"0\" value=\"0\">" + client_messages.text.targeting.unknown_type + "</option>";
				} else {
					for (var value = 0; value < _this.data.comparators[ $(selectedOption).attr('dataSubtype') ].length; value++) {
						var currentComparator = _this.data.comparators[ $(selectedOption).attr('dataSubtype') ][ value ];
						comparatorOptions += "<option id=\"" + currentComparator.id + "\" value=\"" + currentComparator.id + "\">" + currentComparator.name + "</option>";
					}
				}
			}

			var currentComparatorId = _this.targetEle.find('.variableComparatorId').val();
			
			var comparatorSelect = _this.getEle('comparatorSelect');
			$(comparatorSelect).html(comparatorOptions);
			$(comparatorSelect).replaceAllOptions(comparatorOptions);
			// Default comparator selection: Use prior selection if set; otherwise, select Equals:1
			$(comparatorSelect).selectOption(currentComparatorId != null && currentComparatorId != "" && currentComparatorId > 0 ? currentComparatorId : 1);
			
			_this.comparatorSelectToggle();
			_this.comparatorTypeSelectToggle();
			
			if ( $.isFunction(_this.data.afterPrimaryVariableToggle) )
				_this.data.afterPrimaryVariableToggle(_this, _this.getEle('variableSelect'));
		},

		// ******** END - Input Management APIs ***********
		// ************************************************
		
		// ******** BEGIN - Rule value parser APIs ********
		// ************************************************

		updateRuleValuesBinding : function() {
			// Parse and build 'dataElementValuesString' value.  See introduction
			// at top of this script for string format details.
			var _this = this;
			
			if ( _this.debug )
				console.log("updateRuleValuesBinding");

			var comparatorTypesSelectVal 	= _this.getEle('comparatorTypesSelect').find('option:selected').val();
			var comparatorTypeIsVariable 	= ( comparatorTypesSelectVal == 2 );
			var comparatorTypeIsDataFile 	= ( comparatorTypesSelectVal == 4 );

			var rangeValue_a = ""; var rangeValue_b = "";
		
			// If: Date Type
			if ( _this.currentDataState_isDate ) {
				var dateTypeBuilderValue_a 	= _this.getEle('ruleDateValueTypeSelect_a').find('option:selected').attr('stringBuilderValue');
				var dateTypeId_a 			= _this.getEle('ruleDateValueTypeSelect_a').find('option:selected').val();

				if (comparatorTypeIsVariable) {
					rangeValue_a += _this.data.rule_variable_indicator + 
									_this.getEle('ruleValueVariableSelect_a').find('option:selected').val() + 
									_this.data.rule_value_separator_str;
					rangeValue_a += _this.getDateComponentValues('a').join(_this.data.rule_value_separator_str);
				} else if (dateTypeId_a == _this.data.dateType_specificDateId) {
					rangeValue_a += dateTypeBuilderValue_a + _this.data.rule_value_separator_str;
					rangeValue_a += _this.getDateValue('a');
				} else {
					rangeValue_a += dateTypeBuilderValue_a + _this.data.rule_value_separator_str;
					rangeValue_a += _this.getDateComponentValues('a').join(_this.data.rule_value_separator_str);
				}

				// If: Between
				if (_this.currentComparatorState_isRange) {
					var dateTypeBuilderValue_b 	= _this.getEle('ruleDateValueTypeSelect_b').find('option:selected').attr('stringBuilderValue');
					var dateTypeId_b 			= _this.getEle('ruleDateValueTypeSelect_b').find('option:selected').val();			
					rangeValue_b += _this.data.range_separator_str;
					if (comparatorTypeIsVariable) {
						rangeValue_b += _this.data.rule_variable_indicator + 
										_this.getEle('ruleValueVariableSelect_b').find('option:selected').val() + 
										_this.data.rule_value_separator_str;
						rangeValue_b += _this.getDateComponentValues('b').join(_this.data.rule_value_separator_str);
					} else if (dateTypeId_b == _this.data.dateType_specificDateId) {
						rangeValue_b += dateTypeBuilderValue_b + _this.data.rule_value_separator_str;
						rangeValue_b += _this.getDateValue('b');
					} else {
						rangeValue_b += dateTypeBuilderValue_b + _this.data.rule_value_separator_str;
						rangeValue_b += _this.getDateComponentValues('b').join(_this.data.rule_value_separator_str);
					}
				};
			// Else: Simple Type
			} else {
				if (comparatorTypeIsVariable)
					rangeValue_a = 	_this.data.rule_variable_indicator + 
									_this.getEle('ruleValueVariableSelect_a').find('option:selected').val();
				else if (comparatorTypeIsDataFile)
					rangeValue_a = 	_this.data.data_file_indicator + _this.targetEle.find('.dataFilePath').val();
				else
					rangeValue_a =  _this.getEle('ruleValueInput_a').val();
				// If: Between
				if (_this.currentComparatorState_isRange) {
					if (comparatorTypeIsVariable)
						rangeValue_b = 	_this.data.rule_variable_indicator + 
										_this.getEle('ruleValueVariableSelect_b').find('option:selected').val();
					else
						rangeValue_b = _this.getEle('ruleValueInput_b').val();
					rangeValue_b = _this.data.range_separator_str + rangeValue_b;
				};		
			}
			_this.targetEle.find('.variableComparedToValue').val( rangeValue_a + rangeValue_b );

		},

		getDateComponentValues : function (indicator) {
			// Get values for range date component [0] (+,-) [1] value [2] (d,m,y)
			var _this = this;
			var element = new Array();
			
			element[0] = _this.getEle('ruleDateValueDeltaOperator_'+indicator).is(':checked') ? '-' : '+';
			element[1] = _this.getEle('ruleDateDeltaValueInput_'+indicator).val(); 
			element[2] = _this.getEle('ruleDateValueDeltaTypeSelect_'+indicator).find('option:selected').val();
			return element;
		},

		getDateValue : function (indicator) {
			// Get value from a date picker interface
			var _this = this;

			var dateValue = _this.getEle('ruleDateValueInput_'+indicator).val();
			if (dateValue != '')
				return $.datepicker.formatDate('yy/mm/dd', new Date(dateValue));
			else 
				return '';
		},

		// ******** END - Rule value parser APIs **********
		// ************************************************
		
		// ** START - Rule value init from binding APIs ***
		// ************************************************

		setRuleValueInputsFromBinding : function() {
			// Parse values from 'dataElementValuesString' and set associated input values.  
			// See introduction at top of this script for string format details.
			var _this = this;
			
			if ( _this.debug )
				console.log("setRuleValueInputsFromBinding");

			var isRange					= (_this.targetEle.find('.variableComparatorId').val() == _this.data.comparatorTriggers.range);
			var dataElementSubtype 		= _this.getEle('variableSelect').find('option:selected').attr('dataSubtype');
			
			_this.currentDataState_isDate = ( dataElementSubtype == _this.data.dateSubtypeId );

			var rangeValue_a = _this.initialComparedToValue.split(_this.data.range_separator_str)[0];
			var rangeValue_b = _this.initialComparedToValue.split(_this.data.range_separator_str)[1];

			// Parameterized or Default value init
			if (rangeValue_b == null) 
				rangeValue_b = '';

			// If: Date Type
			if (_this.currentDataState_isDate) {
				var dateValue_a = rangeValue_a.split(_this.data.rule_value_separator_str)[0];
				
				if ( dateValue_a != '' )
					if (dateValue_a.indexOf(_this.data.rule_variable_indicator) != -1) {
						_this.initRuleValueVariableSelect( 'a', dateValue_a.replace(_this.data.rule_variable_indicator,'') );
						_this.ruleValueVariableSelectInit_a = true;
					} else {
						_this.getEle('ruleDateValueTypeSelect_a').selectOptionByAttr('stringBuilderValue', dateValue_a);
					}
				else
					_this.defaultToSpecificDate('a');
					
				if (dateValue_a == _this.data.dateType_specificDateId || dateValue_a == _this.data.dateType_specificDateStr) {
					_this.defaultToSpecificDate('a');
					if (rangeValue_a.split(_this.data.rule_value_separator_str)[1] != '') {
						var myDate = new Date(rangeValue_a.split(_this.data.rule_value_separator_str)[1]);
						var dateValue = $.datepicker.formatDate('M, dd yy', myDate);
						_this.getEle('ruleDateValueInput_a').val( dateValue );
					}
				} else if (rangeValue_a != '') {
					_this.toggleIButton( _this.getEle('ruleDateValueDeltaOperator_a'), rangeValue_a.split(_this.data.rule_value_separator_str)[1] == '-', 'a' ); 
					_this.getEle('ruleDateDeltaValueInput_a').val( rangeValue_a.split(_this.data.rule_value_separator_str)[2] );
					_this.getEle('ruleDateValueDeltaTypeSelect_a').selectOptionByAttr('value', rangeValue_a.split(_this.data.rule_value_separator_str)[3] );
				}

				// If: Between
				if ( isRange ) {
					var dateValue_b = rangeValue_b.split(_this.data.rule_value_separator_str)[0];
					
					if ( dateValue_b != '' ) {
						if (dateValue_b.indexOf(_this.data.rule_variable_indicator) != -1) {
							_this.initRuleValueVariableSelect( 'b', dateValue_b.replace(_this.data.rule_variable_indicator,'') );
							_this.ruleValueVariableSelectInit_b = true;
						} else {
							_this.getEle('ruleDateValueTypeSelect_b').selectOptionByAttr('stringBuilderValue', dateValue_b);
						}
					} else {
						_this.defaultToSpecificDate('b');
					}
					
					if (dateValue_b == _this.data.dateType_specificDateId || dateValue_b == _this.data.dateType_specificDateStr) {
						_this.defaultToSpecificDate('b');
						if (rangeValue_b.split(_this.data.rule_value_separator_str)[1] != '') {
							var myDate = new Date(rangeValue_b.split(_this.data.rule_value_separator_str)[1]);
							var dateValue = $.datepicker.formatDate('M, dd yy', myDate);
							_this.getEle('ruleDateValueInput_b').val( dateValue );
						}
					} else if (rangeValue_b != '') {
						_this.toggleIButton( _this.getEle('ruleDateValueDeltaOperator_b'), rangeValue_b.split(_this.data.rule_value_separator_str)[1] == '-', 'b' );
						_this.getEle('ruleDateDeltaValueInput_b').val( rangeValue_b.split(_this.data.rule_value_separator_str)[2] );
						_this.getEle('ruleDateValueDeltaTypeSelect_b').selectOptionByAttr('value', rangeValue_b.split(_this.data.rule_value_separator_str)[3] );
					}
				} else {
					_this.defaultToSpecificDate('b');
				}
			// Else: Simple Type
			} else {
				if (rangeValue_a.indexOf(_this.data.rule_variable_indicator) != -1) {
					_this.initRuleValueVariableSelect( 'a', rangeValue_a.replace(_this.data.rule_variable_indicator,'') );
					_this.ruleValueVariableSelectInit_a = true;
				} else {
					if ( rangeValue_a.indexOf(_this.data.data_file_indicator) == -1 )
						_this.getEle('ruleValueInput_a').val(rangeValue_a);
				}
				// If: Between
				if ( isRange ) {
					if (rangeValue_b.indexOf(_this.data.rule_variable_indicator) != -1) {
						_this.initRuleValueVariableSelect( 'b', rangeValue_b.replace(_this.data.rule_variable_indicator,'') );
						_this.ruleValueVariableSelectInit_b = true;
					} else {
						_this.getEle('ruleValueInput_b').val(rangeValue_b);
					}
				}		
			}
			
			_this.updateRuleValuesBinding();
			
			_this.ruleValueInitComplete = true;
		},
		
		toggleIButton: function(ele, toggleValue, indicator) {
			var _this = this;
			if ( $(ele).parent().is('.ibutton-container') )
				$(ele).iButton("toggle", toggleValue );
			else {
				_this.initDateDeltaOperatorIButton(indicator);
				setTimeout( function() { _this.toggleIButton(ele, toggleValue, indicator); }, 250 );
			}
		},
		
		defaultToSpecificDate : function(indicator) {
			// Set Data Element Value to SpecificDate by default
			// (For param date and initial date conditions)
			var _this = this;
			
			_this.getEle('ruleDateValueTypeSelect_'+indicator).selectOptionByAttr('value',_this.data.dateType_specificDateId);
			_this.getEle('ruleDateValueTypeSelect_'+indicator).change();
		},
		
		initRuleValueVariableSelect : function(indicator, value) {
			var _this = this;
			
			if ( _this.debug )
				console.log("initRuleValueVariableSelect");

			// INIT: Config rule value Variable Selects (a and b)
			_this.getEle('ruleValueVariableSelect_'+indicator)
				.change( function() {
					_this.updateRuleValuesBinding();
				})
				.styleActionElement({
					maxItemsInList	: 30,
					isAsync			: true,
					getItemsAsync 	: {
						loadOnDemand		: false,
						getItemsURL			: context+"/getVariables.form",
						fnExtServerParams	: function () {
													var stampDate = new Date();
													var currentDataSubtypeId = _this.getEle('variableSelect').find('option:selected').attr('dataSubtype');
													var obj = 	[
																	{"name" : "type", "value" : "variablesList"},
																	{"name" : "documentId", "value" :  _this.data.touchpointContextId},
																	{"name" : "selectedVariableId", "value" : (value && value != "" && value != null ? value : -1) },
																	{"name" : "dataSubtypeId", "value" : currentDataSubtypeId},
																	{"name" : "targetingFiltered", "value" : "true"}
																];
													return obj;
											  }
					  	},
					afterFilter		: function(ele, o) {
							_this.updateRuleValuesBinding();
						}
				});

		},
		
		// **** END - Rule value init from binding APIs ***
		// ************************************************
		
		// ******** BEGIN - Common APIs *******************
		// ************************************************
		
		getEle : function(eleId) {
			var _this = this;
			return $(_this.ruleBuilderContainer).find('#' + eleId + '_'+_this.elemId);
		},
		
		getRangeInd : function(ele) {
			return $(ele).attr('id').split('_')[1];
		}
		
		// ******** END - Common APIs *********************
		// ************************************************
		
		}; // end component
	};
	
	// instance manager
	ruleBuilder_component.inst = {};
	
})(jQuery);	