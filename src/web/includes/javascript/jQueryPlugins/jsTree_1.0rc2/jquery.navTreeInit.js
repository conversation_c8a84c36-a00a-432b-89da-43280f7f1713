var onNavTreeLoad;

$( function() {
	$('.asyncNavTree')
		.bind("loaded.jstree", function (event, data) {
			if ($.isFunction( onNavTreeLoad ))
				onNavTreeLoad(data);

			// Set selected node
			//data.inst.data.ui.to_select = $(this).find('.selectedTreeNode').closest('li').attr('id');
			//$(this).find('.selectedTreeNode').closest('li').addClass('selectedTreeNodeLi');

			// Hide icons if no custom icons defined
			//var noIconNodes = $(this).find('.noIcon');
			//var nodes = $(this).find('a');
			//if (noIconNodes.length == nodes.length)
				//data.inst.hide_icons();

			// Init node click events


			var jsTree = $(this).jstree();
			jsTree.open_all();

            initNodeLinks($(this));
            tokenInjectionTargets.push(this); // CSRF

            jsTree.select_node($('.selectedTreeNode').attr('id'));
            jsTree.close_all();
            $(this).show();

		})
		.bind("ready.jstree", function (event, data) {

        })
		.jstree({
            'plugins': ['wholerow'],
			"core" : {
                'load_open': true,
                'themes': {
                    'name': 'proton',
                    'responsive': false,
                    'icons': true
                },
                "data": {
                    "url": function (data) {

                            return context + "/getChildren.form";

                            /*
                            {
                                    "url" 	: context+"/getChildren.form",
                                    "data"	: function (n){
                                                    var data = {
                                                                    "root" 			: !n.attr ? "source" : $(n).attr('id'),
                                                                    "treeId" 		: $('.asyncNavTree').attr('id'),
                                                                    "cacheStamp" 	: cacheStamp
                                                                };
                                                    // $.query.get() - Third party plug-in used to get existing page params
                                                    return $.extend($.query.get(),data);
                                              },
                                    "complete" : function (req, status) {
                                                    // Init newly loaded nodes
                                                    if ($.isFunction( onNavTreeLoad ))
                                                        onNavTreeLoad();
                                              }
                                }
                             */
                    },
                    "data": function (n) {
                        var data = {
                            "root": !n.attr ? "source" : $(n).attr('id'),
                            "treeId": $('.asyncNavTree').attr('id'),
                            "cacheStamp": cacheStamp
                        };
                        // $.query.get() - Third party plug-in used to get existing page params
                        return $.extend($.query.get(), data);
                    }
                }
            }
		});
	
	configStandardTree($('.customTree'));
});

function configStandardTree(ele) {
	ele.jstree({
		"plugins" 	: [ "themes", "html_data", "ui" ],
		"themes" 	: { "theme" : "default", "dots" : false, "icons" : false },
		"ui"		: { "select_limit" : 1 }
	});
	
	ele.bind("loaded.jstree", function (event, data) {
		if ($.isFunction( onNavTreeLoad ))
			onNavTreeLoad(data);
		
		// Set selected node
		//data.inst.data.ui.to_select = $(this).find('.selectedTreeNode').closest('li').attr('id');
		$(this).find('.selectedTreeNode').closest('li').addClass('selectedTreeNodeLi');

		// Init node click events
		initNodeLinks($(this));
		
		tokenInjectionTargets.push(this); // CSRF
		$(this).show();
	});
	
	ele.bind("before.jstree", function (e, data) {
		if(data.func === "select_node") {
	      	if ( $(data.args[0]).hasClass('disabledTreeNode') ) {
		        e.stopImmediatePropagation();
		        return false;
	      	}
		}
	});	
}

// Redirect page when a:href is clicked
function initNodeLinks(tree) {
	$(tree).click(function(e){
		var target;
		if (window.event != null) 
			target = event.srcElement;
		else
			target = e.target;
		if ($(target).is('a'))
			javascriptHref( $(target).attr('href') + (_.isString(gup('viewid')) ? "&viewid=" + gup('viewid') : "" ) );
	});
}

function getSelectionParam(ele) {
	if ( $(ele).hasClass('selectionNav') )
		return 'selectionId';
	else
		return 'contentSelectionId';
}

var loadingImgTag = "<img src=\""+context+"/includes/themes/commonimages/tree/loading.gif\" height=\"15\" width=\"15\" align=\"top\">";

function startBuildSelectionTreeAsync(treeContainer, nodeId){
	
	var statusViewId = -1;
	if ( getParam('statusViewId') && getParam('statusViewId') != "" )
		statusViewId = getParam('statusViewId');
	
	var stampDate = new Date();
	$.ajax({
		type: "GET",
		url: context+"/getSelectionTree.form?"+getSelectionParam(treeContainer)+"="+nodeId+"&getParents=true&partialReq=true&statusViewId=" + statusViewId + "&cacheStamp="+(stampDate.getTime()),
		dataType: "json",
		success: function(data_ajax) {
			var obj = $(data_ajax).get(0);
			if (obj.result == "COMPLETE") {
				var parentsArr = obj.TPCSNodeParents.split(" ");
				// revert the array for pop to pop the parent node from the end
				parentsArr.reverse();
				buildSelectionNavTreeRec(nodeId, 0, $(treeContainer).find("ul"), parentsArr);
			}
		}
	});
}

// Build TPCS nav tree recursively 
function buildSelectionNavTreeRec(userSelectId, parentSelId, targetObj, parentsArr){
	if(parentSelId==0){
		$('div#tpcsNavTreeHeader').hide();
		var loadingImage = "<div class=\"loadingHeader\" style=\"padding: 3px 20px;\">" + loadingImgTag + "&nbsp;" + client_messages.text.loading + "</div>";
		$("#selectionsNavTree").parent().append(loadingImage);		
	}

	var statusViewId = -1;
	if ( getParam('statusViewId') && getParam('statusViewId') != "" )
		statusViewId = getParam('statusViewId');
	
	var stampDate = new Date();
	$.ajax({
		type: "GET",
		url: context+"/getSelectionTree.form?"+getSelectionParam($('#selectionsNavTree'))+"="+userSelectId+"&parentSelectedId="+parentSelId+"&partialReq=true&statusViewId=" + statusViewId + "&cacheStamp="+(stampDate.getTime()),
		dataType: "json",
		success: function(data_ajax) {
			var obj = $(data_ajax).get(0);
			if (obj.result == "COMPLETE") {
				var nodes = obj.TPCSNavTreeNodes;
				targetObj.append(obj.TPCSNavTreeNodes);
				if(nodes.indexOf("selectedTreeNode") ==-1){	
					// Selected node is not in this level, continue to build the selection tree
					if(parentSelId == 0){	// The root node
						// Pop the root
						parentsArr.pop();
						parentSelId = $('#selectionsNavTree li').attr('id');
						buildSelectionNavTreeRec(userSelectId, parentSelId, $("#"+parentSelId + " ul"), parentsArr);
					}else{
						var parent = parentsArr.pop();
						if(parent){
							// Find all the li with children
							$("#"+parentSelId+" li.jstree-open").each(function(){
								if(parent == $(this).attr('id')){
									buildSelectionNavTreeRec(userSelectId, $(this).attr('id'), $("#"+$(this).attr('id') + " ul"), parentsArr);
								}
							});
						}
					}
				}else{
					// Only configure the nav tree if current level has selected tree node
					configSelectionNavTree($('#selectionsNavTree'));
					//$('div#tpcsNavTreeHeader').hide();
					$(".loadingHeader").remove();
				}
			}
		}
	});
}

function configSelectionNavTree(ele) {
	//alert("invoke");
	ele.jstree({
		"plugins" 	: [ "themes", "html_data", "ui" ],
		"themes" 	: { "theme" : "default", "dots" : false, "icons" : false },
		"ui"		: { "select_limit" : 1, "selected_parent_close" : false }
	});
	
	ele.bind("loaded.jstree", function (event, data) {		
		// Close all nodes
		data.inst.close_all();
		
		// Set selected node
		//data.inst.data.ui.to_select = $(this).find('.selectedTreeNode').closest('li').attr('id');
		$(this).find('.selectedTreeNode').closest('li').addClass('selectedTreeNodeLi');

		$("#selectionsNavTree").jstree("open_node", $('.selectedTreeNodeLi'), false, false);
		
		// Init node click events
		initNodeLinks($(this));
		
		tokenInjectionTargets.push(this); // CSRF
		$(this).show();
	});
	
	ele.bind("open_node.jstree", function(event, data) {
		var targetObj = data.rslt.obj;
		$("#"+targetObj.attr('id')+" ul " + "li.dummy").remove();
		if(targetObj.find("li").length == 0){	
			var loadingImage = "<div class=\"loadingHeader\" style=\"padding: 3px 20px;\">" + loadingImgTag + "&nbsp;" + client_messages.text.loading + "</div>";
			$("#"+targetObj.attr('id')).append(loadingImage);
			
			var statusViewId = -1;
			if ( getParam('statusViewId') && getParam('statusViewId') != "" )
				statusViewId = getParam('statusViewId');
			
			var stampDate = new Date();
			$.ajax({
				type: "GET",
				url: context+"/getSelectionTree.form?"+getSelectionParam($('#selectionsNavTree'))+"="+targetObj.attr('id')+"&parentSelectedId="+targetObj.attr('id')+"&partialReq=true&statusViewId=" + statusViewId + "&cacheStamp="+(stampDate.getTime()),
				dataType: "json",
				success: function(data_ajax) {
					var obj = $(data_ajax).get(0);
					if (obj.result == "COMPLETE") {
						var nodes = obj.TPCSNavTreeNodes;
						$('#'+targetObj.attr('id')+' ul').append(nodes);						
						$("#selectionsNavTree").jstree("refresh");
						//close all the child nodes
						$("#selectionsNavTree").jstree("close_all", targetObj, false);
						$("#selectionsNavTree").jstree("open_node", targetObj, false, false);
					}
					$(".loadingHeader").remove();
				}
			});
		}
	});
}
