function isFrameAccessible(win) {
    try {
        win.document;
    } catch ( err ) {
        return false;
    }
    return true;
}

function getTopFrame() {
    if ( isFrameAccessible(window.top) )
        return window.top;

    var win = window;
    while ( win.parent && win.parent != null && isFrameAccessible(win.parent) )
        win = win.parent;
    return win;
}

function popItUp(URL) {
    windowOpenTargets.push({path: URL, name: 'Popup_Popup', attr: 'scrollbars=yes, statusbar=no, toolbar=no, menubar=no, location=no, resizable=yes, width=724, height=680'});
}
function popItUpWithDimensions(URL, width, height) {
    windowOpenTargets.push({path:URL, name: 'Popup_Popup', attr: 'scrollbars=yes, statusbar=no, toolbar=no, menubar=no, location=no, resizable=yes, width='+(width+50)+', height='+(height+200)});
}

function popNamedUpWithDimensions(URL, name, width, height) {
    windowOpenTargets.push({path:URL, name: name, attr: 'scrollbars=yes, statusbar=no, toolbar=no, menubar=no, location=no, resizable=yes, width='+(width+50)+', height='+(height+200)});
}

function popItUpWithDimensions2(URL, width, height) {
    windowOpenTargets.push({path: URL, name: 'Popup_Popup', attr: 'scrollbars=yes, statusbar=no, toolbar=no, menubar=no, location=no, resizable=yes, width='+(width)+', height='+(height), afterOpen: function(newWindow) { newWindow.focus(); }});
}

function popItUpLevel2(URL) {
    windowOpenTargets.push({path: URL, name:'Popup_Popup_Popup', attr: 'scrollbars=yes, statusbar=no, toolbar=no, menubar=no, location=no, resizable=yes, width=724, height=680'});
}
function popItUpLevel2WithDimensions(URL, width, height) {
    windowOpenTargets.push({path: URL, name:'Popup_Popup_Popup', attr: 'scrollbars=yes, statusbar=no, toolbar=no, menubar=no, location=no, resizable=yes, width='+(width+50)+', height='+(height+200)});
}

function popItUpEx(widthOf, URL) {

    var widthValue = 0;
    var heightValue = 0;

    if ( widthOf == "site" ) {
        widthValue = 988;
        heightValue = 550;
    }
    else {
        widthValue = 624;
        heightValue = 480;
    }

    windowOpenTargets.push({path: URL, name: 'Popup', attr: 'scrollbars=yes, statusbar=yes, toolbar=no, menubar=no, location=no, resizable=yes, width=' + widthValue + ', height=' + heightValue + ''});
}
function largePopup(URL) {
    windowOpenTargets.push({path: URL, name: 'Popup', attr: 'scrollbars=yes, statusbar=yes, toolbar=no, menubar=no, location=no, resizable=yes, width=1005, height=600'});
}
function largePopupExternal(URL) {
    var newWindow = getTopFrame().open( URL, 'Popup', 'scrollbars=yes, statusbar=yes, toolbar=no, menubar=no, location=no, resizable=yes, width=1005, height=600' );
    if (newWindow != undefined)
        newWindow.focus();
}

function popupWorkflowIframe(ele, wfType, wfUsageType, mId, applyMask) {
    //Workflow History Popup
    $(ele).iFramePopup({
        width			: 650,
        id				: "workflowHistoryFrame",
        vertOffset		: -25,
        displayOnInit	: true,
        title			: client_messages.title.workflow_history,
        src				: "../workflow/workflow_history.form",
        appliedParams	: {workflowType : wfType, usageType: wfUsageType, modelId : mId, tk : getParam('tk')},
        closeBtnId		: "cancelBtn_button",
        screenMask		: applyMask,
        draggable		: false,
        beforePopupClose: function() {
                             $('.iFramePopupSubPageScreen').remove();
                          }
    });
}

function popupSourceEditorIframe(ele, type, resource, applyMask, source, params = {}) {
    //Source Editor Popup
    $(ele).iFramePopup({
        width			: 800,
        id				: "sourceEditorFrame",
        vertOffset		: -25,
        displayOnInit	: true,
        title			: client_messages.title.source_editor,
        src				: source?source:"../file/source_editor.form",
        appliedParams	: {type : type, resource : resource, tk : getParam('tk'), ...params},
        closeBtnId		: "cancelBtn_button",
        screenMask		: applyMask,
        draggable		: false,
        beforePopupClose: function() {
                             $('.iFramePopupSubPageScreen').remove();
                          }
    });
}