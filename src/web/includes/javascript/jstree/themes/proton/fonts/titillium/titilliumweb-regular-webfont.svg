<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="titillium_webregular" horiz-adv-x="1146" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="450" />
<glyph horiz-adv-x="2048" />
<glyph horiz-adv-x="2048" />
<glyph unicode="&#xd;" horiz-adv-x="681" />
<glyph unicode=" "  horiz-adv-x="450" />
<glyph unicode="&#x09;" horiz-adv-x="450" />
<glyph unicode="&#xa0;" horiz-adv-x="450" />
<glyph unicode="!" horiz-adv-x="505" d="M170 0v229h168v-229h-168zM172 1417h162l-10 -950h-142z" />
<glyph unicode="&#x22;" horiz-adv-x="763" d="M135 1417h154l-8 -454h-138zM475 1417h154l-8 -454h-138z" />
<glyph unicode="#" d="M49 356v131h227v381h-227v131h227v365h140v-365h315v365h139v-365h228v-131h-228v-381h228v-131h-228v-356h-139v356h-315v-356h-140v356h-227zM416 487h315v381h-315v-381z" />
<glyph unicode="$" d="M141 1010q0 362 434 362q31 0 48 -2l34 264h105l-35 -270l256 -31l-12 -127q-141 16 -260 27l-62 -477q199 -47 281 -121t82 -238q0 -416 -432 -415h-31l-29 -236q-104 6 -104 14l28 228l-292 37l16 125q158 -23 293 -31l67 512q-209 49 -298 126t-89 253zM293 1018 q0 -111 55 -158t199 -82l59 461h-20q-293 0 -293 -221zM567 115h4q289 0 289 272q0 98 -51 145.5t-178 77.5z" />
<glyph unicode="%" d="M57 1090q0 282 225.5 282t225.5 -282q0 -145 -59.5 -217t-166 -72t-166 71.5t-59.5 217.5zM182 1090q0 -184 101.5 -184.5t101.5 184.5q0 92 -23.5 135t-78 43t-78 -43t-23.5 -135zM317 -8l420 1403l99 -33l-420 -1399zM641 270q0 281 224.5 281t224.5 -283 q0 -143 -59.5 -215.5t-166 -72.5t-165 72.5t-58.5 217.5zM764 270q0 -186 100 -186q57 0 80 45t23 139t-22.5 136t-78 42t-79 -42t-23.5 -134z" />
<glyph unicode="&#x26;" horiz-adv-x="1409" d="M86 396.5q0 195.5 75 286.5t242 146q-72 82 -94 145.5t-22 160.5q0 137 94 219t266 82t261 -82t89 -226.5t-71.5 -227.5t-243.5 -163l311 -307q45 131 58 332l149 -2q-27 -256 -94 -426l264 -250l-92 -102l-248 231q-141 -233 -428 -233t-401.5 110.5t-114.5 306z M242 414q0 -295 336 -295q252 0 346 184l-432 430q-135 -43 -192.5 -110.5t-57.5 -208.5zM440 1110q0 -125 92 -223l60 -58q141 61 196.5 121t55.5 164q0 188 -202 188.5t-202 -192.5z" />
<glyph unicode="'" horiz-adv-x="425" d="M137 1417h154l-10 -454h-138z" />
<glyph unicode="(" horiz-adv-x="540" d="M104 616q0 197 53.5 427.5t106.5 361.5l53 131h148q-72 -188 -133.5 -465.5t-61.5 -453t49.5 -393.5t98.5 -349l47 -131h-148q-213 479 -213 872z" />
<glyph unicode=")" horiz-adv-x="540" d="M76 -256q70 164 132 431t62 442.5t-48 404.5t-97 371l-49 143h147q136 -305 191 -653q23 -143 22.5 -303t-53.5 -378t-107 -337l-53 -121h-147z" />
<glyph unicode="*" horiz-adv-x="860" d="M111 938l221 162l-217 157l63 86l217 -159l84 258l101 -33l-82 -260h266v-102h-270l84 -256l-101 -31l-84 258l-219 -162z" />
<glyph unicode="+" d="M113 444v140h387v399h143v-399h391v-140h-391v-403h-143v403h-387z" />
<glyph unicode="," horiz-adv-x="456" d="M70 -252l94 471h170l-137 -471h-127z" />
<glyph unicode="-" horiz-adv-x="897" d="M139 492v143h621v-143h-621z" />
<glyph unicode="." horiz-adv-x="442" d="M137 0v233h168v-233h-168z" />
<glyph unicode="/" horiz-adv-x="843" d="M66 20l577 1430l137 -51l-577 -1428z" />
<glyph unicode="0" d="M80 667q0 386 121.5 546.5t369.5 160.5q168 0 273 -63q221 -131 221 -656q0 -371 -123 -523t-368.5 -152t-369.5 150.5t-124 536.5zM244 653q0 -303 79.5 -418.5t249.5 -115.5t250 116.5t80 433t-77 440.5t-253 124q-117 0 -184 -49q-145 -100 -145 -531z" />
<glyph unicode="1" d="M219 1065l430 287h148v-1352h-158v1176l-348 -230z" />
<glyph unicode="2" d="M141 0v133l396 416q102 106 149 161.5t85 131.5t38 151q0 131 -71.5 184.5t-223.5 53.5q-133 0 -297 -35l-53 -10l-12 131q195 55 401.5 55t312 -87t105.5 -275q0 -143 -63.5 -250t-219.5 -256l-360 -367h678v-137h-865z" />
<glyph unicode="3" d="M127 35l14 129q221 -45 414 -45q293 2 293 262q0 244 -279 254h-272v135h272q86 0 166 68.5t80 181.5t-63.5 163t-212.5 50q-164 0 -324 -31l-53 -10l-15 125q193 57 413 57t318.5 -84t98.5 -268q0 -94 -25.5 -136t-41 -64.5t-40.5 -43.5q-45 -35 -71 -49l-23 -14 q119 -43 176.5 -109.5t57.5 -218.5q0 -207 -105.5 -307t-333.5 -100q-180 0 -381 40z" />
<glyph unicode="4" d="M82 264v123l385 965h172l-391 -949h489v422h158v-422h172v-139h-172v-264h-158v264h-655z" />
<glyph unicode="5" d="M129 41l18 125q236 -45 397 -45t242.5 83t81.5 232.5t-74.5 212t-211.5 62.5q-74 0 -155 -20.5t-124 -41.5l-43 -20l-108 20l36 703h789v-144h-658l-36 -440q158 84 317 84q430 0 430 -399q0 -229 -118.5 -350t-333.5 -121q-172 0 -381 45z" />
<glyph unicode="6" d="M102 682q0 354 138.5 522t390.5 168q166 0 315 -24l55 -11l-14 -129q-184 25 -358 25t-268.5 -121t-94.5 -346l51 20q51 18 136.5 39t150.5 21q455 0 455 -420q0 -215 -124 -330.5t-349 -115.5q-483 0 -484 702zM264 627q2 -233 79 -370.5t234.5 -137.5t237.5 78.5 t80 223t-79 214.5t-222 70q-70 0 -153 -19.5t-130 -38.5z" />
<glyph unicode="7" d="M158 1208v144h839v-207l-520 -1165l-149 47l510 1128v53h-680z" />
<glyph unicode="8" d="M66 344q0 145 56 221t177 142q-111 55 -161 125.5t-50 191.5q0 172 128 261t353.5 89t357.5 -89t132 -263q0 -127 -51.5 -193.5t-173.5 -121.5q123 -55 184 -130t61 -210q0 -387 -506.5 -387t-506.5 364zM233 371q0 -248 339 -248t339 260q0 109 -60 161t-179 87h-215 q-113 -31 -168.5 -91t-55.5 -169zM256 1006q0 -90 49 -145.5t152 -92.5h215q111 35 165 90.5t54 145.5q0 227 -317.5 227t-317.5 -225z" />
<glyph unicode="9" d="M84 924q0 207 127 327.5t342 120.5q248 0 368.5 -183t120.5 -538.5t-136 -513t-400 -157.5q-154 0 -318 28l-53 8l15 127q184 -24 356 -24q371 0 373 471q-205 -78 -353 -78q-442 0 -442 412zM248 924q0 -273 291 -273q125 0 288 56l54 18q-6 508 -328 508 q-143 0 -224 -83t-81 -226z" />
<glyph unicode=":" horiz-adv-x="444" d="M137 0v233h168v-233h-168zM137 659v234h168v-234h-168z" />
<glyph unicode=";" horiz-adv-x="507" d="M98 -252l92 471h172l-137 -471h-127zM180 659v234h166v-234h-166z" />
<glyph unicode="&#x3c;" d="M125 446v136l852 415v-159l-694 -322l694 -328v-161z" />
<glyph unicode="=" d="M131 254v141h885v-141h-885zM131 633v141h885v-141h-885z" />
<glyph unicode="&#x3e;" d="M168 27v161l694 328l-694 322v159l852 -415v-136z" />
<glyph unicode="?" horiz-adv-x="915" d="M76 1376q207 61 342 62q211 0 312 -77t101 -251q0 -133 -35.5 -201.5t-145 -160t-150.5 -145.5t-41 -120v-71h-129q-27 55 -27 124.5t47 130t151.5 150.5t139.5 143.5t35 148.5t-65.5 142t-207.5 47q-90 0 -264 -36l-53 -11zM307 2v229h168v-229h-168z" />
<glyph unicode="@" horiz-adv-x="2000" d="M104 535q0 494 235.5 720t692.5 226t668 -210t211 -646v-27q0 -434 -137 -547q-49 -39 -99.5 -51t-115 -12t-110.5 12t-71 31q-49 37 -67 80q-201 -121 -357 -121q-76 0 -132 18.5t-113 69.5q-115 102 -115 399t93 427t323 130q88 0 198 -39l37 -14v33h154v-391 q0 -358 22 -426q10 -27 29.5 -49.5t42.5 -26.5t78 -4t93 26.5t64.5 135t26.5 319.5v29q0 379 -168 549t-558 170t-579.5 -194t-189.5 -629t180.5 -619.5t591.5 -184.5l293 21l6 -137q-193 -18 -299 -19q-238 0 -402.5 45t-285.5 154q-240 209 -240 752zM750 500 q0 -375 215 -375q88 0 299 94q-18 111 -19 354v285q-123 41 -211 41q-166 0 -225 -94t-59 -305z" />
<glyph unicode="A" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM342 506h537l-215 776h-107z" />
<glyph unicode="B" horiz-adv-x="1261" d="M174 0v1417h514q211 0 316.5 -87t105.5 -273q0 -133 -46 -208t-136 -118q225 -78 225 -334q0 -213 -112.5 -305t-325.5 -92h-541zM332 139h377q139 0 211.5 58.5t72.5 205.5q0 78 -29.5 131.5t-78.5 75.5q-91 41 -187 41h-366v-512zM332 788h364q131 0 191.5 62.5 t60.5 188.5t-64.5 182.5t-201.5 56.5h-350v-490z" />
<glyph unicode="C" horiz-adv-x="1114" d="M121 706.5q0 188.5 21.5 316.5t77.5 226.5t158.5 143.5t266.5 45t385 -45l-6 -133q-202 36 -360 36h-9q-225 0 -297.5 -134t-72.5 -457q0 -162 13 -260.5t52 -177.5t114 -112.5t219 -33.5t341 37l6 -135q-211 -43 -379 -43t-271.5 46t-159.5 143t-77.5 223t-21.5 314.5z " />
<glyph unicode="D" horiz-adv-x="1320" d="M174 0v1417h506q346 0 459 -274q65 -156 65 -406v-8q0 -535 -266 -674q-106 -55 -258 -55h-506zM332 139h348q362 0 362 590q0 408 -182 508q-74 41 -180 41h-348v-1139z" />
<glyph unicode="E" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862z" />
<glyph unicode="F" horiz-adv-x="1077" d="M174 0v1417h848v-139h-690v-553h583v-139h-583v-586h-158z" />
<glyph unicode="G" horiz-adv-x="1261" d="M117 707.5q0 371.5 119.5 551t425.5 179.5q190 0 395 -41l69 -13l-6 -131q-254 43 -440 43q-236 0 -317.5 -136t-81.5 -452.5t81.5 -452.5t315.5 -136q143 0 293 28v437h-236v141h391v-696q-258 -49 -460 -49q-309 0 -429 178t-120 549.5z" />
<glyph unicode="H" horiz-adv-x="1382" d="M174 0v1417h158v-635h721v635h155v-1417h-155v641h-721v-641h-158z" />
<glyph unicode="I" horiz-adv-x="503" d="M174 0v1417h158v-1417h-158z" />
<glyph unicode="J" horiz-adv-x="591" d="M37 -6q147 0 189 36t42 187v1200h156l2 -1222q0 -172 -57 -252q-47 -68 -187 -84q-55 -6 -145 -6v141z" />
<glyph unicode="K" horiz-adv-x="1165" d="M174 0v1417h158v-659l233 8l363 651h180l-406 -712l428 -705h-186l-377 627l-235 -9v-618h-158z" />
<glyph unicode="L" horiz-adv-x="980" d="M174 0v1417h158v-1276h622v-141h-780z" />
<glyph unicode="M" horiz-adv-x="1720" d="M174 0v1417h285l401 -1198l402 1198h284v-1417h-157v1249h-29l-412 -1198h-176l-412 1198h-28v-1249h-158z" />
<glyph unicode="N" horiz-adv-x="1384" d="M174 0v1417h295l543 -1278h43v1278h155v-1417h-288l-551 1278h-39v-1278h-158z" />
<glyph unicode="O" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5z" />
<glyph unicode="P" horiz-adv-x="1214" d="M174 0v1417h518q231 0 342 -112.5t111 -343.5q0 -484 -453 -484h-360v-477h-158zM332 616h358q293 0 293 345q0 164 -69.5 240.5t-223.5 76.5h-358v-662z" />
<glyph unicode="Q" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -555.5q0 -260 -53.5 -416.5t-182.5 -232.5l175 -280l-148 -70l-180 295q-68 -18 -172 -18q-317 0 -438 174t-121 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 132t81 446.5t-84 457.5t-314.5 143 t-313 -142t-82.5 -453.5z" />
<glyph unicode="R" horiz-adv-x="1261" d="M174 0v1417h524q225 0 339 -104.5t114 -325.5q0 -328 -260 -412l272 -575h-174l-256 543h-401v-543h-158zM332 682h368q289 0 289 298t-291 298h-366v-596z" />
<glyph unicode="S" horiz-adv-x="1110" d="M98 1059q0 381 457 381q150 0 358 -31l70 -10l-14 -133q-285 34 -404 34q-307 0 -307 -231q0 -129 70.5 -177t286.5 -96t306.5 -125t90.5 -253q0 -436 -453 -436q-150 0 -375 34l-73 11l16 131q279 -37 424 -37q303 0 303 285q0 115 -66.5 165t-248.5 84 q-240 51 -340.5 132t-100.5 272z" />
<glyph unicode="T" horiz-adv-x="1077" d="M27 1276v141h1024v-141h-433v-1276h-155v1276h-436z" />
<glyph unicode="U" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5z" />
<glyph unicode="V" horiz-adv-x="1191" d="M49 1417h164l326 -1278h114l326 1278h164l-371 -1417h-352z" />
<glyph unicode="W" horiz-adv-x="1814" d="M61 1417h164l242 -1278h59l289 1266h184l289 -1266h60l241 1278h162l-287 -1417h-288l-269 1210l-268 -1210h-289z" />
<glyph unicode="X" horiz-adv-x="1142" d="M39 0l438 694l-438 723h178l358 -606l361 606h170l-436 -719l436 -698h-178l-357 588l-362 -588h-170z" />
<glyph unicode="Y" horiz-adv-x="1101" d="M20 1417h179l352 -663l352 663h178l-450 -821v-596h-158v596z" />
<glyph unicode="Z" horiz-adv-x="1097" d="M88 -2v182l739 1053v45h-739v139h922v-184l-742 -1053v-41h742v-141h-922z" />
<glyph unicode="[" horiz-adv-x="673" d="M162 -252v1786h430v-137h-275v-1512h275v-137h-430z" />
<glyph unicode="\" horiz-adv-x="892" d="M61 1389l138 59l630 -1419l-137 -58z" />
<glyph unicode="]" horiz-adv-x="673" d="M82 -115h274v1512h-274v137h430v-1786h-430v137z" />
<glyph unicode="^" d="M119 653l379 699h137l391 -699h-164l-293 547l-286 -547h-164z" />
<glyph unicode="_" horiz-adv-x="1294" d="M209 -188h876v-136h-876v136z" />
<glyph unicode="`" horiz-adv-x="483" d="M-2 1384l53 132l443 -201l-43 -105z" />
<glyph unicode="a" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-182 -81 -364 -81h-4q-141 0 -215 79.5t-74 229zM240 297q0 -188 155 -188 q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131z" />
<glyph unicode="b" horiz-adv-x="1077" d="M147 2v1468h152v-497q156 71 301 71q207 0 290 -117.5t83 -412.5t-103.5 -414.5t-380.5 -119.5q-86 0 -284 16zM299 127q133 -10 188 -10q201 0 265.5 91t64.5 308t-51 304t-182 87q-119 0 -244 -45l-41 -14v-721z" />
<glyph unicode="c" horiz-adv-x="892" d="M104 517q0 292 98.5 409.5t327.5 117.5q68 0 230 -24l49 -6l-6 -125q-164 18 -242 18q-174 0 -236.5 -83t-62.5 -307t58.5 -312t242.5 -88l242 18l6 -127q-184 -28 -279 -28h-6q-240 0 -331 122.5t-91 414.5z" />
<glyph unicode="d" horiz-adv-x="1083" d="M102 482.5q0 297.5 99.5 429.5t328.5 132q119 0 252 -26v452h152v-1470h-152v70q-158 -90 -311 -90q-82 0 -143.5 20t-114.5 74q-111 111 -111 408.5zM260 504q0 -238 72 -320q33 -39 69.5 -53t95 -14t131 19.5t113.5 37.5l41 19v694q-126 24 -234 24h-9 q-156 0 -217.5 -99t-61.5 -308z" />
<glyph unicode="e" horiz-adv-x="1034" d="M102 506q0 538 426 538h2q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-226 -32 -389 -32h-10q-221 0 -313.5 128t-92.5 398zM256 571h532q0 188 -60 266t-196.5 78t-205 -81.5t-70.5 -262.5z" />
<glyph unicode="f" horiz-adv-x="677" d="M61 891v133h129v92q0 217 61.5 296t215.5 79l207 -14l-2 -127q-115 4 -189.5 4t-106.5 -49.5t-32 -190.5v-90h297v-133h-297v-891h-154v891h-129z" />
<glyph unicode="g" horiz-adv-x="1052" d="M102 -172q0 84 37 136t125 118q-57 39 -57 141q0 35 51 129l18 33q-162 74 -161 309q0 350 383 350q98 0 182 -20l31 -6l303 8v-131l-195 4q68 -68 68 -205q0 -193 -95.5 -269.5t-299.5 -76.5q-55 0 -101 8q-37 -90 -37 -116q0 -63 39 -79t235.5 -16t282.5 -61t86 -241 q0 -324 -454 -324q-246 0 -343.5 66.5t-97.5 242.5zM254 -160q0 -109 60.5 -150.5t223 -41.5t233.5 45t71 150.5t-51.5 136t-200.5 30.5l-221 10q-68 -49 -91.5 -86t-23.5 -94zM268 694q0 -121 54.5 -172t179.5 -51t178 51t53 173t-53 173.5t-178 51.5t-179.5 -52.5 t-54.5 -173.5z" />
<glyph unicode="h" horiz-adv-x="1099" d="M147 0v1470h154v-503q162 77 311 77h4q205 0 276 -110.5t71 -392.5v-541h-154v537q0 213 -42 291.5t-177 78.5q-129 0 -250 -47l-39 -14v-846h-154z" />
<glyph unicode="i" horiz-adv-x="448" d="M147 0v1024h154v-1024h-154zM147 1255v179h154v-179h-154z" />
<glyph unicode="j" horiz-adv-x="448" d="M-100 -338q160 92 205 157t45 208v997h151v-999q0 -188 -67.5 -282.5t-272.5 -199.5zM150 1255v179h151v-179h-151z" />
<glyph unicode="k" horiz-adv-x="980" d="M147 0v1470h154v-868l160 8l293 414h174l-334 -471l348 -553h-174l-305 475l-162 -6v-469h-154z" />
<glyph unicode="l" horiz-adv-x="473" d="M160 0v1470h153v-1470h-153z" />
<glyph unicode="m" horiz-adv-x="1712" d="M147 0v1024h152v-72q154 92 299 92q190 0 266 -104q68 41 175.5 72.5t189.5 31.5q205 0 275.5 -109.5t70.5 -393.5v-541h-154v537q0 213 -41 291.5t-174 78.5q-68 0 -139.5 -19.5t-110.5 -37.5l-38 -19q26 -66 26 -283v-16v-532h-153v528q0 221 -40 300t-176 79 q-66 0 -134 -19.5t-105 -37.5l-35 -19v-831h-154z" />
<glyph unicode="n" horiz-adv-x="1099" d="M147 0v1024h152v-72q166 92 317 92q205 0 276 -110.5t71 -392.5v-541h-152v537q0 213 -42 291.5t-179 78.5q-66 0 -138.5 -19.5t-111.5 -37.5l-39 -19v-831h-154z" />
<glyph unicode="o" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85t-60.5 -309z" />
<glyph unicode="p" horiz-adv-x="1079" d="M147 -455v1479h152v-74q156 94 307 94q195 0 283 -123.5t88 -408.5t-103.5 -408.5t-343.5 -123.5q-125 0 -229 22v-457h-154zM301 135q139 -22 215 -22h6q166 0 232.5 94t66.5 307t-60 303t-177 90q-61 0 -132 -20.5t-112 -40.5l-39 -21v-690z" />
<glyph unicode="q" horiz-adv-x="1077" d="M102 510q0 289 106.5 411.5t377.5 122.5l342 -20v-1479h-152v502q-144 -67 -289 -67q-207 0 -296 120.5t-89 409.5zM258 509q0 -210 56.5 -301t189.5 -91q119 0 235 45l37 14v723q-127 12 -188 12q-193 0 -261.5 -96t-68.5 -306z" />
<glyph unicode="r" horiz-adv-x="708" d="M147 0v1024h152v-139q178 123 375 162v-156q-86 -16 -179.5 -51t-142.5 -62l-51 -26v-752h-154z" />
<glyph unicode="s" horiz-adv-x="952" d="M92 751.5q0 149.5 105.5 221t263.5 71.5q125 0 313 -28l57 -10l-4 -132q-227 33 -358 33q-223 0 -223 -155q0 -72 51 -101.5t233.5 -61.5t258 -89.5t75.5 -215t-101 -231t-296 -73.5q-127 0 -299 28l-62 10l9 134q233 -35 352 -35t181.5 38t62.5 127t-53.5 120.5 t-233.5 60.5t-256 84t-76 204.5z" />
<glyph unicode="t" horiz-adv-x="718" d="M55 891v133h146v313h151v-313h326v-133h-326v-490q0 -176 25.5 -231t122.5 -55l182 12l10 -127q-137 -23 -209 -23q-160 0 -221 78t-61 297v539h-146z" />
<glyph unicode="u" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t176 -76q68 0 137.5 19.5t106.5 37.5l36 19v831h154v-1024h-154v72q-154 -92 -303 -92q-209 0 -277.5 108.5t-68.5 400.5z" />
<glyph unicode="v" horiz-adv-x="985" d="M51 1024h164l240 -891h75l248 891h158l-289 -1024h-307z" />
<glyph unicode="w" horiz-adv-x="1560" d="M63 1024h154l207 -891h33l243 871h160l244 -871h35l204 891h154l-244 -1024h-256l-217 801l-217 -801h-256z" />
<glyph unicode="x" horiz-adv-x="925" d="M45 0l322 512l-322 512h168l250 -401l250 401h168l-326 -508l324 -516h-168l-248 399l-250 -399h-168z" />
<glyph unicode="y" horiz-adv-x="989" d="M51 1024h154l256 -891h67l258 891h154l-426 -1479h-154l134 455h-152z" />
<glyph unicode="z" horiz-adv-x="931" d="M86 0v137l573 750h-573v137h760v-137l-574 -750h574v-137h-760z" />
<glyph unicode="{" horiz-adv-x="720" d="M49 575v125q117 27 168 81.5t51 142.5l-14 262q0 188 87 270t296 90l2 -133q-135 -8 -185.5 -64t-50.5 -171l15 -252q0 -131 -44 -190.5t-171 -96.5q125 -37 170 -99.5t45 -191.5l-15 -237q0 -115 50.5 -173.5t185.5 -66.5l-4 -133q-207 8 -294 91t-87 267l14 250 q0 88 -51 142.5t-168 86.5z" />
<glyph unicode="|" horiz-adv-x="471" d="M160 -455v1925h151v-1925h-151z" />
<glyph unicode="}" horiz-adv-x="720" d="M82 -129q135 8 185 66.5t50 173.5l-14 237q0 129 45 191.5t170 99.5q-127 37 -171 96.5t-44 190.5l14 252q0 115 -50 171t-185 64l2 133q209 -8 296 -90t87 -270l-14 -262q0 -88 51 -142.5t168 -81.5v-125q-117 -33 -168 -87t-51 -142l14 -250q0 -184 -87 -267t-294 -91z " />
<glyph unicode="~" d="M137 557q127 109 238 109q59 0 211.5 -62.5t191.5 -62.5q72 0 180 69l35 25l21 -125q-129 -111 -238 -111q-55 0 -209.5 63.5t-192.5 63.5t-91.5 -23.5t-87.5 -47.5l-35 -25z" />
<glyph unicode="&#xa1;" horiz-adv-x="466" d="M147 795v229h168v-229h-168zM152 -393l10 950h141l10 -950h-161z" />
<glyph unicode="&#xa2;" d="M207 503.5q0 227.5 89 333t273 115.5v242h133v-248q80 0 175 -18l32 -6l-6 -123q-162 14 -264 14q-152 0 -212 -68.5t-60 -236.5t60 -232.5t224 -64.5l254 14l6 -125q-123 -18 -209 -22v-244h-133v240q-190 10 -276 106t-86 323.5z" />
<glyph unicode="&#xa3;" d="M164 0v135h174v586h-137v133h137v113q0 240 68.5 322.5t234.5 82.5q106 0 227 -24l45 -11l-2 -125q-164 23 -259 23t-130 -57.5t-35 -221.5v-102h351v-133h-351v-586h316l158 33l26 -131l-168 -37h-655z" />
<glyph unicode="&#xa4;" d="M119 170l143 143q-59 92 -59 201t59 205l-143 143l108 109l144 -146q90 61 200.5 61.5t204.5 -61.5l144 146l108 -109l-145 -143q61 -94 61 -205t-61 -201l145 -143l-108 -109l-144 146q-94 -61 -204.5 -61.5t-200.5 61.5l-144 -146zM338 516q0 -96 69.5 -165.5 t166 -69.5t166 69.5t69.5 165.5t-69.5 166t-166 70t-166 -70t-69.5 -166z" />
<glyph unicode="&#xa5;" d="M47 1352h176l352 -553l349 553h176l-350 -574h278v-133h-356l-17 -39v-139h375v-133h-375v-334h-157v334h-379v133h379v139l-17 39h-364v133h286z" />
<glyph unicode="&#xa6;" horiz-adv-x="479" d="M164 319h151v-774h-151v774zM164 705v765h151v-765h-151z" />
<glyph unicode="&#xa7;" horiz-adv-x="1021" d="M106 522q0 80 43.5 156t75.5 102q-90 66 -90 228q0 326 393 325q104 0 283 -26l53 -9l-8 -129q-223 31 -338 31t-174 -45t-59 -143.5t62.5 -136t236.5 -70.5t248.5 -92.5t74.5 -199.5t-90 -271q76 -66 76 -205q0 -344 -391 -344q-131 0 -297 24l-62 11l15 129 q223 -31 336 -31q252 0 251 203q0 92 -59 123.5t-239.5 65.5t-260.5 97.5t-80 206.5zM260 533.5q0 -78.5 51 -117.5t189.5 -64.5t199.5 -52.5q10 14 31 74.5t21 134.5t-52.5 110.5t-184.5 60t-187 46.5q-68 -113 -68 -191.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="483" d="M-25 1272v184h148v-184h-148zM385 1272v184h150v-184h-150z" />
<glyph unicode="&#xa9;" horiz-adv-x="1320" d="M121 889.5q0 236.5 153.5 397.5t387 161t386 -163t152.5 -398.5t-151.5 -396t-385 -160.5t-388 161.5t-154.5 398zM211 888.5q0 -196.5 128 -332.5t320.5 -136t321.5 136t129 332.5t-129 334t-320.5 137.5t-320.5 -137.5t-129 -334zM428 888q0 169 54.5 245.5t187.5 76.5 q76 0 137 -14l20 -6l-8 -109q-78 12 -148.5 12.5t-94 -46t-23.5 -156t25.5 -159.5t97.5 -50l143 14l8 -106q-75 -25 -159 -25q-133 0 -186.5 77t-53.5 246z" />
<glyph unicode="&#xaa;" horiz-adv-x="790" d="M111 920q0 164 196 176l180 12v45q0 45 -24.5 63.5t-75.5 18.5l-235 -14l-5 98q141 31 252 31t165 -45t54 -158v-281q18 -23 62 -35l-4 -100q-78 0 -111 12.5t-59 40.5q-113 -53 -207 -53t-141 51.5t-47 137.5zM246 922q0 -80 73 -80q59 0 144 26l24 9v137l-157 -10 q-45 -4 -64.5 -22.5t-19.5 -59.5z" />
<glyph unicode="&#xab;" horiz-adv-x="1103" d="M92 446v123l387 293v-157l-254 -193l254 -215v-160zM578 446v123l389 293v-157l-256 -193l256 -215v-160z" />
<glyph unicode="&#xac;" d="M133 561v139h877v-460h-144v321h-733z" />
<glyph unicode="&#xad;" horiz-adv-x="897" d="M139 492v143h621v-143h-621z" />
<glyph unicode="&#xae;" horiz-adv-x="1320" d="M121 889.5q0 236.5 153.5 397.5t387 161t386 -162t152.5 -398.5t-151.5 -397t-385 -160.5t-388 161.5t-154.5 398zM211 889.5q0 -195.5 129 -332.5t320.5 -137t320.5 136t129 332.5t-129 334t-320.5 137.5t-320.5 -137.5t-129 -333zM436 578v620h226q117 0 175 -45 t58 -135t-23.5 -134t-80.5 -71l112 -235h-125l-102 219h-121v-219h-119zM553 893h121q55 0 79.5 24.5t24.5 79.5q0 104 -127 105h-98v-209z" />
<glyph unicode="&#xaf;" horiz-adv-x="483" d="M-29 1278v121h564v-121h-564z" />
<glyph unicode="&#xb0;" d="M283 1149q0 129 80.5 209t209.5 80t210 -80t81 -209t-81 -209t-210 -80t-209.5 80t-80.5 209zM387 1148q0 -85 51 -137.5t135 -52.5t137.5 52.5t53.5 137.5t-53.5 138t-137.5 53t-135 -53t-51 -138z" />
<glyph unicode="&#xb1;" d="M113 78v139h921v-139h-921zM113 600v139h387v258h143v-258h391v-139h-391v-260h-143v260h-387z" />
<glyph unicode="&#xb2;" horiz-adv-x="573" d="M61 979v113l181 178q104 100 104 170q0 76 -100 76l-174 -19l-4 117q121 25 219 24q195 0 194 -186q0 -68 -28.5 -118t-96.5 -111l-129 -125h262v-119h-428z" />
<glyph unicode="&#xb3;" horiz-adv-x="573" d="M61 979l9 115q117 -16 212 -16.5t95 86.5t-96 87h-134v109h134q29 0 53 26.5t24 63.5q0 70 -92 70l-188 -15l-8 113q121 20 209 20q215 0 215 -172q0 -104 -80 -149q49 -16 72.5 -50t23.5 -110q0 -199 -217 -199q-88 0 -195 17z" />
<glyph unicode="&#xb4;" horiz-adv-x="548" d="M53 1315l443 201l53 -132l-453 -174z" />
<glyph unicode="&#xb5;" d="M172 -455v1479h154v-532q0 -223 38.5 -299t176.5 -76q68 0 137.5 19.5t105.5 37.5l37 19v831h154v-1024h-154v72q-154 -92 -288 -92t-207 34v-469h-154z" />
<glyph unicode="&#xb6;" horiz-adv-x="1214" d="M72 1038q0 166 108.5 272.5t274.5 106.5h673v-133h-147v-1284h-139v1284h-238v-1284h-137v664h-14q-166 0 -273.5 104t-107.5 270z" />
<glyph unicode="&#xb7;" horiz-adv-x="444" d="M139 469v233h168v-233h-168z" />
<glyph unicode="&#xb8;" horiz-adv-x="538" d="M86 -430l6 98q80 -6 135 -6q90 0 90 78q0 70 -90 70h-86v192h84v-102q121 0 174.5 -33t53.5 -127q0 -184 -191 -184q-94 0 -151 10z" />
<glyph unicode="&#xb9;" horiz-adv-x="573" d="M66 1475l206 143h123v-639h-131v496l-139 -97z" />
<glyph unicode="&#xba;" horiz-adv-x="778" d="M109 1044q0 158 70.5 232t210.5 74t209 -74t69 -232q0 -313 -277 -313q-283 0 -282 313zM250 1043.5q0 -99.5 31.5 -143.5t108.5 -44t106.5 44t29.5 144.5t-29.5 140.5t-106.5 40t-108.5 -41t-31.5 -140.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1103" d="M137 137v160l256 215l-256 193v157l387 -293v-123zM623 137v160l256 215l-256 193v157l389 -293v-123z" />
<glyph unicode="&#xbc;" horiz-adv-x="1093" d="M72 57l802 1295l82 -54l-804 -1292zM76 1475l207 143h123v-639h-132v496l-139 -97zM555 4l141 430h144l-150 -422h154l10 183h119v-183h55v-118h-55v-99h-129v99h-289v110z" />
<glyph unicode="&#xbd;" horiz-adv-x="1083" d="M68 57l802 1295l82 -54l-805 -1292zM84 1475l207 143h123v-639h-131v496l-140 -97zM567 -92l181 178q104 100 104 170q0 76 -100 76l-174 -19l-5 117q121 25 218 25q197 0 196 -187q0 -68 -28.5 -118t-96.5 -111l-129 -125h262v-119h-428v113z" />
<glyph unicode="&#xbe;" horiz-adv-x="1126" d="M100 979l9 115q117 -16 212 -16.5t95 86.5t-97 87h-133v109h133q29 0 53.5 26.5t24.5 63.5q0 70 -92 70l-188 -15l-8 113q120 20 208 20q215 0 215 -172q0 -104 -79 -149q49 -16 72.5 -50t23.5 -110q0 -199 -217 -199q-88 0 -197 17zM106 57l803 1295l82 -54l-805 -1292z M590 4l141 430h143l-149 -422h154l10 183h119v-183h55v-118h-55v-99h-129v99h-289v110z" />
<glyph unicode="&#xbf;" horiz-adv-x="903" d="M78 -84q0 133 36 201.5t145.5 160t150.5 145.5t41 120v71h129q27 -55 26.5 -124.5t-47.5 -130t-151.5 -150.5t-139.5 -143.5t-35 -148.5t66 -142t207 -47q109 0 264 34l53 13l11 -125q-207 -61 -342 -62q-211 0 -312.5 77t-101.5 251zM434 795v229h168v-229h-168z" />
<glyph unicode="&#xc0;" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM334 1772l53 135l442 -205l-47 -109zM342 506h537l-215 776h-107z" />
<glyph unicode="&#xc1;" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM342 506h537l-215 776h-107zM381 1702l442 205l54 -135l-449 -179z" />
<glyph unicode="&#xc2;" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM266 1620l275 276h133l274 -276h-172l-168 164l-170 -164h-172zM342 506h537l-215 776h-107z" />
<glyph unicode="&#xc3;" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM256 1772q39 45 92 81.5t100.5 36.5t172 -55t145.5 -55q41 0 119 71l24 23l37 -115q-96 -114 -180 -114q-43 0 -170 55t-154 55q-45 0 -125 -74l-26 -24zM342 506h537l-215 776h-107z" />
<glyph unicode="&#xc4;" horiz-adv-x="1220" d="M49 0l389 1417h344l389 -1417h-155l-103 365h-606l-102 -365h-156zM326 1655v184h149v-184h-149zM342 506h537l-215 776h-107zM743 1655v184h148v-184h-148z" />
<glyph unicode="&#xc5;" horiz-adv-x="1220" d="M49 0l377 1370q-61 57 -61 155.5t68.5 156t175 57.5t174 -57.5t67.5 -151.5t-57 -156l378 -1374h-155l-103 365h-606l-102 -365h-156zM342 506h537l-215 776h-107zM481 1524q0 -90 88 -107h76q90 16 90 107q0 51 -33.5 79.5t-93.5 28.5q-127 1 -127 -108z" />
<glyph unicode="&#xc6;" horiz-adv-x="1794" d="M41 0l459 1425h1198v-149h-707v-471h584v-150h-584v-505h707v-150h-862v358h-514l-119 -358h-162zM365 510h471l2 766h-224z" />
<glyph unicode="&#xc7;" horiz-adv-x="1114" d="M121 705q0 190 21.5 318t77.5 226.5t158.5 143.5t266.5 45t385 -45l-6 -133q-207 37 -369 36q-225 0 -297.5 -134t-72.5 -457q0 -162 13 -260.5t52 -177.5t114 -112.5t219 -33.5t341 37l6 -135q-188 -41 -371 -43v-80q121 0 174.5 -33t53.5 -127q0 -184 -191 -184 q-94 0 -151 10l-25 4l6 98q80 -6 136 -6q90 0 90 78q0 70 -90 70h-87v172q-264 18 -359 191t-95 532z" />
<glyph unicode="&#xc8;" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862zM346 1772l53 135l443 -205l-47 -109z" />
<glyph unicode="&#xc9;" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862zM360 1702l443 205l53 -135l-448 -179z" />
<glyph unicode="&#xca;" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862zM260 1620l275 276h133l274 -276h-172l-168 164l-170 -164h-172z" />
<glyph unicode="&#xcb;" horiz-adv-x="1132" d="M174 0v1417h862v-139h-704v-490h581v-137h-581v-512h704v-139h-862zM322 1655v184h149v-184h-149zM739 1655v184h148v-184h-148z" />
<glyph unicode="&#xcc;" horiz-adv-x="503" d="M-41 1772l53 135l443 -205l-47 -109zM174 0v1417h158v-1417h-158z" />
<glyph unicode="&#xcd;" horiz-adv-x="503" d="M27 1702l442 205l53 -135l-448 -179zM174 0v1417h158v-1417h-158z" />
<glyph unicode="&#xce;" horiz-adv-x="503" d="M-96 1620l274 276h133l275 -276h-172l-168 164l-170 -164h-172zM174 0v1417h158v-1417h-158z" />
<glyph unicode="&#xcf;" horiz-adv-x="503" d="M-31 1655v184h150v-184h-150zM174 0v1417h158v-1417h-158zM387 1655v184h148v-184h-148z" />
<glyph unicode="&#xd0;" horiz-adv-x="1325" d="M41 637v149h139v639h506q289 0 406.5 -180t117.5 -510q0 -537 -268 -678q-104 -57 -256 -57h-506v637h-139zM336 150h350q361 -1 361 585q0 397 -183 500q-72 41 -178 41h-350v-490h323v-149h-323v-487z" />
<glyph unicode="&#xd1;" horiz-adv-x="1384" d="M174 0v1417h295l543 -1278h43v1278h155v-1417h-288l-551 1278h-39v-1278h-158zM342 1772q39 45 92 81.5t100.5 36.5t172 -55t145.5 -55q41 0 119 71l24 23l37 -115q-96 -114 -180 -114q-43 0 -170 55t-154 55q-45 0 -125 -74l-26 -24z" />
<glyph unicode="&#xd2;" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5zM412 1772l53 135l442 -205l-47 -109z " />
<glyph unicode="&#xd3;" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5zM406 1702l442 205l53 -135l-448 -179z " />
<glyph unicode="&#xd4;" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5zM336 1620l274 276h133l275 -276h-172 l-168 164l-170 -164h-172z" />
<glyph unicode="&#xd5;" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5zM326 1772q39 45 92 81.5t100 36.5 t172 -55t146 -55q41 0 118 71l25 23l37 -115q-96 -114 -180 -114q-43 0 -170 55t-154 55q-45 0 -125 -74l-27 -24z" />
<glyph unicode="&#xd6;" horiz-adv-x="1351" d="M117 701.5q0 373.5 122.5 555t436 181.5t437.5 -180.5t124 -559.5t-121 -548.5t-439.5 -169.5t-439 174t-120.5 547.5zM279 702.5q0 -311.5 81.5 -447.5t316 -136t315.5 133t81 446.5t-84 456.5t-314.5 143t-313 -142t-82.5 -453.5zM393 1655v184h150v-184h-150z M811 1655v184h147v-184h-147z" />
<glyph unicode="&#xd7;" d="M145 184l332 330l-332 328l101 98l327 -332l330 334l98 -98l-333 -330l333 -330l-98 -98l-330 332l-329 -332z" />
<glyph unicode="&#xd8;" horiz-adv-x="1351" d="M117 702q0 373 122.5 554.5t436.5 181.5q154 0 258 -43l115 241l118 -49l-120 -260q190 -160 190 -625q0 -383 -121 -552.5t-440 -169.5q-141 0 -246 34l-117 -252l-116 54l120 258q-109 82 -154.5 235.5t-45.5 392.5zM279 677q0 -337 106 -456l487 1039q-78 39 -196 38 q-232 0 -314.5 -142t-82.5 -479zM494 147q69 -28 182 -28q236 0 316.5 133t80.5 462.5t-98 461.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5zM401 1772l54 135l442 -205l-47 -109z" />
<glyph unicode="&#xda;" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5zM408 1702l442 205l53 -135l-448 -179z" />
<glyph unicode="&#xdb;" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5zM317 1620l275 276h133l274 -276h-172l-168 164l-170 -164h-172z" />
<glyph unicode="&#xdc;" horiz-adv-x="1318" d="M164 430v987h158v-991q0 -307 331 -307q172 0 259 72.5t87 234.5v991h156v-987q0 -238 -126 -344t-371.5 -106t-369.5 106.5t-124 343.5zM377 1655v184h149v-184h-149zM795 1655v184h147v-184h-147z" />
<glyph unicode="&#xdd;" horiz-adv-x="1101" d="M20 1417h179l352 -663l352 663h178l-450 -821v-596h-158v596zM342 1702l442 205l54 -135l-449 -179z" />
<glyph unicode="&#xde;" horiz-adv-x="1226" d="M174 0v1425h158v-229h360q231 0 343 -112.5t112 -347t-114 -361.5t-341 -127h-360v-248h-158zM332 399h358q295 0 295 340q0 160 -70.5 232.5t-224.5 72.5h-358v-645z" />
<glyph unicode="&#xdf;" horiz-adv-x="1175" d="M147 0v1116q0 207 93.5 291t306.5 84t306 -69.5t93 -225.5q0 -109 -39 -167t-120.5 -94t-105.5 -54.5t-24 -50t34 -58.5t167 -92.5t189.5 -131t56.5 -186.5q0 -209 -93 -295.5t-319 -86.5q-102 0 -225 22l-43 8l6 129q174 -23 250 -22q147 0 205.5 54t58.5 153.5 t-43 144.5t-176 108.5t-180 112.5t-47 126t37.5 119t117.5 77t106.5 72.5t26.5 116.5t-52 116t-188 37t-190.5 -53.5t-54.5 -202.5v-1098h-154z" />
<glyph unicode="&#xe0;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM240 297q0 -188 155 -188 q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131zM248 1384l53 132l442 -201l-43 -105z" />
<glyph unicode="&#xe1;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM240 297q0 -188 155 -188 q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131zM242 1315l442 201l53 -132l-452 -174z" />
<glyph unicode="&#xe2;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM195 1212l251 287h105 l254 -287h-154l-149 179l-154 -179h-153zM240 297q0 -188 155 -188q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131z" />
<glyph unicode="&#xe3;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM158 1366q102 106 186 107 q43 0 149.5 -47.5t129.5 -47.5q45 0 122 60l25 18l35 -98q-94 -105 -176 -105q-43 0 -148.5 48.5t-132.5 48.5q-47 0 -127 -62l-26 -20zM240 297q0 -188 155 -188q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131z" />
<glyph unicode="&#xe4;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM215 1272v184h147v-184h-147z M240 297q0 -188 155 -188q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131zM625 1272v184h149v-184h-149z" />
<glyph unicode="&#xe5;" horiz-adv-x="1026" d="M82 288.5q0 149.5 75.5 220.5t237.5 87l322 31v88q0 104 -45 149t-123 45q-164 0 -340 -20l-64 -6l-6 116q225 45 399.5 45t253 -79.5t78.5 -249.5v-525q6 -76 119 -90l-6 -120q-162 0 -244 81q-184 -82 -368 -81q-141 0 -215 79.5t-74 229zM240 297q0 -188 155 -188 q139 0 275 47l47 16v338l-303 -29q-92 -8 -133 -53t-41 -131zM309 1321q0 90 61.5 151.5t150.5 61.5t150.5 -61.5t61.5 -151.5t-60.5 -151.5t-150.5 -61.5t-151.5 61.5t-61.5 151.5zM410 1320q0 -48 32.5 -82t79.5 -34t80 34t33 82t-33 82t-80 34t-79.5 -34t-32.5 -82z" />
<glyph unicode="&#xe6;" horiz-adv-x="1646" d="M82 299q0 158 84 220.5t287 82.5l264 25v84q0 186 -178 186q-135 0 -326 -16l-63 -4l-7 137q244 31 404 30q209 0 276 -149q98 150 312.5 149.5t316.5 -116t102 -363.5l-8 -119h-676q0 -168 61.5 -247.5t187.5 -79.5t335 14l59 4l5 -125q-231 -33 -415.5 -32.5 t-283.5 110.5l-65 -29q-178 -82 -394 -81q-137 0 -207.5 80.5t-70.5 238.5zM240 297q0 -184 145 -184q94 0 215 29.5t152 45.5q-39 104 -39 314l-299 -25q-90 -8 -132 -51t-42 -129zM870 571h531q0 182 -60.5 259t-196.5 77t-205 -80t-69 -256z" />
<glyph unicode="&#xe7;" horiz-adv-x="892" d="M104 532.5q0 276.5 98.5 394t327.5 117.5q68 0 230 -24l49 -6l-6 -125q-164 18 -242 18q-174 0 -236.5 -83t-62.5 -307t58.5 -312t242.5 -88l242 18l6 -127q-190 -29 -285 -28h-16v-80q121 0 174 -33t53 -127q0 -184 -190 -184q-94 0 -154 10l-22 4l6 98q80 -6 135 -6 q90 0 90 78q0 70 -90 70h-86v176q-180 20 -251 145t-71 401.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1034" d="M102 506q0 539 428 538q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-233 -33 -399 -32q-221 0 -313.5 128t-92.5 398zM248 1386l53 132l442 -201l-43 -105zM256 571h532q0 188 -60 266t-196.5 78t-205 -81.5 t-70.5 -262.5z" />
<glyph unicode="&#xe9;" horiz-adv-x="1034" d="M102 506q0 539 428 538q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-233 -33 -399 -32q-221 0 -313.5 128t-92.5 398zM256 571h532q0 188 -60 266t-196.5 78t-205 -81.5t-70.5 -262.5zM297 1315l442 201l54 -132 l-453 -174z" />
<glyph unicode="&#xea;" horiz-adv-x="1034" d="M102 506q0 539 428 538q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-233 -33 -399 -32q-221 0 -313.5 128t-92.5 398zM229 1212l252 287h105l254 -287h-154l-149 179l-154 -179h-154zM256 571h532q0 188 -60 266 t-196.5 78t-205 -81.5t-70.5 -262.5z" />
<glyph unicode="&#xeb;" horiz-adv-x="1034" d="M102 506q0 539 428 538q207 0 309.5 -115.5t102.5 -363.5l-8 -116h-676q0 -170 61.5 -252t214 -82t310.5 12l59 6l4 -121q-233 -33 -399 -32q-221 0 -313.5 128t-92.5 398zM246 1272v184h147v-184h-147zM256 571h532q0 188 -60 266t-196.5 78t-205 -81.5t-70.5 -262.5z M655 1272v184h150v-184h-150z" />
<glyph unicode="&#xec;" horiz-adv-x="448" d="M-123 1384l53 132l443 -201l-43 -105zM147 0v1024h154v-1024h-154z" />
<glyph unicode="&#xed;" horiz-adv-x="448" d="M78 1315l442 201l53 -132l-452 -174zM147 0v1024h154v-1024h-154z" />
<glyph unicode="&#xee;" horiz-adv-x="448" d="M-86 1212l252 287h104l254 -287h-153l-150 179l-153 -179h-154zM147 0v1024h154v-1024h-154z" />
<glyph unicode="&#xef;" horiz-adv-x="448" d="M-63 1272v184h147v-184h-147zM147 0v1024h154v-1024h-154zM346 1272v184h150v-184h-150z" />
<glyph unicode="&#xf0;" horiz-adv-x="1122" d="M86 429q0 212 112.5 332t317.5 120q133 0 287 -56l51 -18q-14 266 -281 424l-249 -166l-68 96l195 131q-111 47 -254 84l30 115q205 -41 355 -111l206 140l70 -97l-158 -106q315 -201 315.5 -597t-110 -567t-362.5 -171q-219 0 -338 117.5t-119 329.5zM248 430 q0 -143 74.5 -226t231.5 -83t228.5 121t73.5 428q-184 74 -323.5 73.5t-212 -85.5t-72.5 -228z" />
<glyph unicode="&#xf1;" horiz-adv-x="1099" d="M147 0v1024h152v-72l45 23q47 25 126 47t146 22q205 0 276 -110.5t71 -392.5v-541h-152v537q0 213 -42 291.5t-184.5 78.5t-283.5 -76v-831h-154zM244 1366q102 106 186 107q43 0 149.5 -47.5t129.5 -47.5q45 0 122 60l25 18l35 -98q-94 -105 -176 -105 q-43 0 -148.5 48.5t-132.5 48.5q-47 0 -127 -62l-26 -20z" />
<glyph unicode="&#xf2;" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85t-60.5 -309zM289 1384l53 132l442 -201l-43 -105z" />
<glyph unicode="&#xf3;" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85t-60.5 -309zM315 1315l443 201l53 -132l-453 -174z" />
<glyph unicode="&#xf4;" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM231 1212l252 287h105l254 -287h-154l-149 179l-154 -179h-154zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85 t-60.5 -309z" />
<glyph unicode="&#xf5;" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM215 1366q102 106 186 107q43 0 149.5 -47.5t129.5 -47.5q45 0 123 60l24 18l35 -98q-94 -105 -176 -105q-43 0 -148.5 48.5t-131.5 48.5q-47 0 -127 -62 l-27 -20zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85t-60.5 -309z" />
<glyph unicode="&#xf6;" horiz-adv-x="1081" d="M102 514q0 281 100.5 405.5t339 124.5t338 -124.5t99.5 -405.5t-94 -407.5t-344 -126.5t-344.5 126.5t-94.5 407.5zM256 1272v184h147v-184h-147zM260 517q0 -224 54.5 -314t227.5 -90t226 89t53 314t-59.5 310t-220 85t-221 -85t-60.5 -309zM666 1272v184h149v-184h-149 z" />
<glyph unicode="&#xf7;" d="M246 575v150h116v98h422v-98h115v-150h-653zM496 61v207h153v-207h-153z" />
<glyph unicode="&#xf8;" horiz-adv-x="1081" d="M102 524q0 270 100.5 395t338.5 125q80 0 151 -16l88 213l103 -39l-88 -213q184 -102 184 -465q0 -291 -94 -417.5t-344 -126.5q-84 0 -156 16l-88 -225l-102 36l90 224q-100 55 -141.5 174t-41.5 319zM260 558q0 -183 17.5 -267t68.5 -127l297 735q-43 12 -102 12 q-160 0 -220.5 -85t-60.5 -268zM436 123q37 -10 105 -10q174 0 227 89t53 331.5t-86 320.5z" />
<glyph unicode="&#xf9;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t176 -76q68 0 137.5 19.5t106.5 37.5l36 19v831h154v-1024h-154v72q-154 -92 -303 -92q-209 0 -277.5 108.5t-68.5 400.5zM233 1384l54 132l442 -201l-43 -105z" />
<glyph unicode="&#xfa;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t183 -76t273 76v831h154v-1024h-154v72l-43 -23q-43 -25 -118.5 -47t-141.5 -22q-209 0 -277.5 108.5t-68.5 400.5zM317 1315l443 201l53 -132l-453 -174z" />
<glyph unicode="&#xfb;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t183 -76t273 76v831h154v-1024h-154v72l-43 -23q-43 -25 -118.5 -47t-141.5 -22q-209 0 -277.5 108.5t-68.5 400.5zM215 1212l252 287h104l254 -287h-153l-150 179l-153 -179h-154z" />
<glyph unicode="&#xfc;" horiz-adv-x="1087" d="M137 489v535h154v-532q0 -223 39 -299t183 -76t273 76v831h154v-1024h-154v72l-43 -23q-43 -25 -118.5 -47t-141.5 -22q-209 0 -277.5 108.5t-68.5 400.5zM270 1272v184h148v-184h-148zM680 1272v184h149v-184h-149z" />
<glyph unicode="&#xfd;" horiz-adv-x="989" d="M51 1024h154l256 -891h67l258 891h154l-426 -1479h-154l134 455h-152zM315 1315l443 201l53 -132l-453 -174z" />
<glyph unicode="&#xfe;" horiz-adv-x="1079" d="M147 -455v1925h154v-497q154 71 299 71q207 0 291 -117.5t84 -410.5t-101.5 -414.5t-365.5 -121.5l-207 14v-449h-154zM301 125q133 -12 199 -12q193 0 255 93t62 309t-50 304t-181 88q-119 0 -246 -45l-39 -14v-723z" />
<glyph unicode="&#xff;" horiz-adv-x="989" d="M51 1024h154l256 -891h67l258 891h154l-426 -1479h-154l134 455h-152zM213 1272v184h147v-184h-147zM623 1272v184h149v-184h-149z" />
<glyph unicode="&#x152;" horiz-adv-x="1894" d="M117 710q0 384 118.5 560t423.5 176q162 0 289 -21h850v-151h-694v-469h571v-152h-571v-501h694v-154h-846q-188 -18 -293 -18q-313 0 -427.5 173t-114.5 557zM281 709.5q0 -316.5 79.5 -446.5t319.5 -130q106 0 268 14v1127q-215 18 -272 18q-231 0 -313 -133 t-82 -449.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1755" d="M102 515q0 282 101.5 405.5t339.5 123.5q139 0 226 -50t134 -165q92 215 348 215q207 0 309.5 -115.5t102.5 -363.5l-8 -119h-676q0 -168 61.5 -247.5t188.5 -79.5t334 14l59 4l4 -125q-231 -33 -397 -32q-244 0 -332 186q-45 -98 -129 -142t-225 -44 q-252 0 -346.5 126.5t-94.5 408.5zM260 519q0 -220 55.5 -311t226.5 -91t225 92t54 305t-64.5 302t-220 89t-216 -83t-60.5 -303zM977 571h532q0 182 -60 259t-198 77q-274 0 -274 -336z" />
<glyph unicode="&#x178;" horiz-adv-x="1101" d="M20 1417h179l352 -663l352 663h178l-450 -821v-596h-158v596zM266 1655v184h150v-184h-150zM684 1655v184h147v-184h-147z" />
<glyph unicode="&#x2c6;" horiz-adv-x="483" d="M-39 1212l252 287h104l254 -287h-153l-150 179l-153 -179h-154z" />
<glyph unicode="&#x2dc;" horiz-adv-x="483" d="M-61 1366q102 106 186 107q43 0 149.5 -47.5t131.5 -47.5q43 0 118 60l27 18l35 -98q-94 -105 -176 -105q-43 0 -148.5 48.5t-132.5 48.5q-47 0 -127 -62l-27 -20z" />
<glyph unicode="&#x2000;" horiz-adv-x="953" />
<glyph unicode="&#x2001;" horiz-adv-x="1907" />
<glyph unicode="&#x2002;" horiz-adv-x="953" />
<glyph unicode="&#x2003;" horiz-adv-x="1907" />
<glyph unicode="&#x2004;" horiz-adv-x="635" />
<glyph unicode="&#x2005;" horiz-adv-x="476" />
<glyph unicode="&#x2006;" horiz-adv-x="317" />
<glyph unicode="&#x2007;" horiz-adv-x="317" />
<glyph unicode="&#x2008;" horiz-adv-x="238" />
<glyph unicode="&#x2009;" horiz-adv-x="381" />
<glyph unicode="&#x200a;" horiz-adv-x="105" />
<glyph unicode="&#x2010;" horiz-adv-x="897" d="M139 492v143h621v-143h-621z" />
<glyph unicode="&#x2011;" horiz-adv-x="897" d="M139 492v143h621v-143h-621z" />
<glyph unicode="&#x2012;" horiz-adv-x="897" d="M139 492v143h621v-143h-621z" />
<glyph unicode="&#x2013;" horiz-adv-x="1292" d="M135 481v135h1024v-135h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2316" d="M135 481v135h2048v-135h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="448" d="M104 1001l132 449h120l-90 -449h-162z" />
<glyph unicode="&#x2019;" horiz-adv-x="442" d="M109 1001l90 449h161l-131 -449h-120z" />
<glyph unicode="&#x201a;" horiz-adv-x="446" d="M139 -43l27 88h24l-18 -88h-33z" />
<glyph unicode="&#x201c;" horiz-adv-x="763" d="M104 1001l132 449h120l-90 -449h-162zM418 1001l131 449h121l-90 -449h-162z" />
<glyph unicode="&#x201d;" horiz-adv-x="772" d="M109 1004l90 448h161l-131 -448h-120zM436 1004l90 448h162l-131 -448h-121z" />
<glyph unicode="&#x201e;" horiz-adv-x="735" d="M45 -219l131 448h121l-90 -448h-162zM356 -219l131 448h121l-90 -448h-162z" />
<glyph unicode="&#x2022;" horiz-adv-x="968" d="M227 217v586h512v-586h-512z" />
<glyph unicode="&#x2026;" horiz-adv-x="1484" d="M137 0v233h168v-233h-168zM659 0v233h166v-233h-166zM1180 0v233h168v-233h-168z" />
<glyph unicode="&#x202f;" horiz-adv-x="381" />
<glyph unicode="&#x2039;" horiz-adv-x="616" d="M92 446v123l387 293v-157l-254 -193l254 -215v-160z" />
<glyph unicode="&#x203a;" horiz-adv-x="616" d="M137 145v160l254 215l-254 193v157l387 -292v-123z" />
<glyph unicode="&#x205f;" horiz-adv-x="476" />
<glyph unicode="&#x20ac;" d="M61 455v125h123q-2 29 -2 95t2 99h-123v125h132q25 244 134 358.5t351 114.5q172 0 373 -43l-7 -127q-186 35 -354 35t-244.5 -80t-95.5 -258h586v-125h-594q-2 -33 -2 -102v-92h596v-125h-588q18 -178 95 -259t247 -81t354 37l7 -129q-203 -43 -373 -43 q-242 0 -351.5 116.5t-136.5 358.5h-129z" />
<glyph unicode="&#x2122;" horiz-adv-x="1386" d="M186 1188v102h410v-102h-135v-508h-113v508h-162zM659 678v612h148l135 -426l145 426h144v-612h-107v459l-135 -428h-88l-135 428v-459h-107z" />
<glyph unicode="&#xe000;" horiz-adv-x="1024" d="M0 0v1024h1024v-1024h-1024z" />
<hkern u1="&#x22;" u2="&#xf0;" k="27" />
<hkern u1="&#x22;" u2="&#xef;" k="-14" />
<hkern u1="&#x22;" u2="&#xee;" k="-10" />
<hkern u1="&#x22;" u2="&#xec;" k="-35" />
<hkern u1="&#x22;" u2="&#xc6;" k="109" />
<hkern u1="&#x22;" u2="&#x40;" k="25" />
<hkern u1="&#x22;" u2="&#x2f;" k="129" />
<hkern u1="&#x22;" u2="&#x26;" k="57" />
<hkern u1="&#x26;" u2="&#x201d;" k="98" />
<hkern u1="&#x26;" u2="&#x2019;" k="98" />
<hkern u1="&#x26;" u2="&#x178;" k="113" />
<hkern u1="&#x26;" u2="&#x152;" k="14" />
<hkern u1="&#x26;" u2="&#xff;" k="27" />
<hkern u1="&#x26;" u2="&#xfd;" k="27" />
<hkern u1="&#x26;" u2="&#xdd;" k="113" />
<hkern u1="&#x26;" u2="&#xdc;" k="12" />
<hkern u1="&#x26;" u2="&#xdb;" k="12" />
<hkern u1="&#x26;" u2="&#xda;" k="12" />
<hkern u1="&#x26;" u2="&#xd9;" k="12" />
<hkern u1="&#x26;" u2="&#xd8;" k="14" />
<hkern u1="&#x26;" u2="&#xd6;" k="14" />
<hkern u1="&#x26;" u2="&#xd5;" k="14" />
<hkern u1="&#x26;" u2="&#xd4;" k="14" />
<hkern u1="&#x26;" u2="&#xd3;" k="14" />
<hkern u1="&#x26;" u2="&#xd2;" k="14" />
<hkern u1="&#x26;" u2="&#xc7;" k="14" />
<hkern u1="&#x26;" u2="y" k="27" />
<hkern u1="&#x26;" u2="w" k="20" />
<hkern u1="&#x26;" u2="v" k="27" />
<hkern u1="&#x26;" u2="t" k="14" />
<hkern u1="&#x26;" u2="Y" k="113" />
<hkern u1="&#x26;" u2="W" k="41" />
<hkern u1="&#x26;" u2="V" k="68" />
<hkern u1="&#x26;" u2="U" k="12" />
<hkern u1="&#x26;" u2="T" k="84" />
<hkern u1="&#x26;" u2="Q" k="14" />
<hkern u1="&#x26;" u2="O" k="14" />
<hkern u1="&#x26;" u2="G" k="14" />
<hkern u1="&#x26;" u2="C" k="14" />
<hkern u1="&#x26;" u2="&#x27;" k="100" />
<hkern u1="&#x26;" u2="&#x22;" k="100" />
<hkern u1="&#x27;" u2="&#xf0;" k="27" />
<hkern u1="&#x27;" u2="&#xef;" k="-14" />
<hkern u1="&#x27;" u2="&#xee;" k="-10" />
<hkern u1="&#x27;" u2="&#xec;" k="-35" />
<hkern u1="&#x27;" u2="&#xc6;" k="109" />
<hkern u1="&#x27;" u2="&#x40;" k="25" />
<hkern u1="&#x27;" u2="&#x2f;" k="129" />
<hkern u1="&#x27;" u2="&#x26;" k="57" />
<hkern u1="&#x28;" u2="&#x153;" k="37" />
<hkern u1="&#x28;" u2="&#x152;" k="27" />
<hkern u1="&#x28;" u2="&#xff;" k="8" />
<hkern u1="&#x28;" u2="&#xfd;" k="8" />
<hkern u1="&#x28;" u2="&#xfc;" k="29" />
<hkern u1="&#x28;" u2="&#xfb;" k="29" />
<hkern u1="&#x28;" u2="&#xfa;" k="29" />
<hkern u1="&#x28;" u2="&#xf9;" k="29" />
<hkern u1="&#x28;" u2="&#xf8;" k="37" />
<hkern u1="&#x28;" u2="&#xf6;" k="37" />
<hkern u1="&#x28;" u2="&#xf5;" k="37" />
<hkern u1="&#x28;" u2="&#xf4;" k="37" />
<hkern u1="&#x28;" u2="&#xf3;" k="37" />
<hkern u1="&#x28;" u2="&#xf2;" k="37" />
<hkern u1="&#x28;" u2="&#xf1;" k="8" />
<hkern u1="&#x28;" u2="&#xf0;" k="8" />
<hkern u1="&#x28;" u2="&#xef;" k="-31" />
<hkern u1="&#x28;" u2="&#xec;" k="-45" />
<hkern u1="&#x28;" u2="&#xeb;" k="37" />
<hkern u1="&#x28;" u2="&#xea;" k="37" />
<hkern u1="&#x28;" u2="&#xe9;" k="37" />
<hkern u1="&#x28;" u2="&#xe8;" k="37" />
<hkern u1="&#x28;" u2="&#xe7;" k="37" />
<hkern u1="&#x28;" u2="&#xe6;" k="10" />
<hkern u1="&#x28;" u2="&#xe5;" k="10" />
<hkern u1="&#x28;" u2="&#xe4;" k="10" />
<hkern u1="&#x28;" u2="&#xe3;" k="10" />
<hkern u1="&#x28;" u2="&#xe2;" k="10" />
<hkern u1="&#x28;" u2="&#xe1;" k="10" />
<hkern u1="&#x28;" u2="&#xe0;" k="10" />
<hkern u1="&#x28;" u2="&#xd8;" k="27" />
<hkern u1="&#x28;" u2="&#xd6;" k="27" />
<hkern u1="&#x28;" u2="&#xd5;" k="27" />
<hkern u1="&#x28;" u2="&#xd4;" k="27" />
<hkern u1="&#x28;" u2="&#xd3;" k="27" />
<hkern u1="&#x28;" u2="&#xd2;" k="27" />
<hkern u1="&#x28;" u2="&#xc7;" k="25" />
<hkern u1="&#x28;" u2="&#x7b;" k="23" />
<hkern u1="&#x28;" u2="y" k="8" />
<hkern u1="&#x28;" u2="w" k="12" />
<hkern u1="&#x28;" u2="v" k="8" />
<hkern u1="&#x28;" u2="u" k="29" />
<hkern u1="&#x28;" u2="s" k="8" />
<hkern u1="&#x28;" u2="r" k="8" />
<hkern u1="&#x28;" u2="q" k="37" />
<hkern u1="&#x28;" u2="p" k="8" />
<hkern u1="&#x28;" u2="o" k="37" />
<hkern u1="&#x28;" u2="n" k="8" />
<hkern u1="&#x28;" u2="m" k="8" />
<hkern u1="&#x28;" u2="j" k="-18" />
<hkern u1="&#x28;" u2="f" k="8" />
<hkern u1="&#x28;" u2="e" k="37" />
<hkern u1="&#x28;" u2="d" k="37" />
<hkern u1="&#x28;" u2="c" k="37" />
<hkern u1="&#x28;" u2="a" k="10" />
<hkern u1="&#x28;" u2="Q" k="27" />
<hkern u1="&#x28;" u2="O" k="27" />
<hkern u1="&#x28;" u2="G" k="27" />
<hkern u1="&#x28;" u2="C" k="25" />
<hkern u1="&#x29;" u2="&#x7d;" k="16" />
<hkern u1="&#x29;" u2="]" k="16" />
<hkern u1="&#x2a;" u2="&#x153;" k="35" />
<hkern u1="&#x2a;" u2="&#xf8;" k="35" />
<hkern u1="&#x2a;" u2="&#xf6;" k="35" />
<hkern u1="&#x2a;" u2="&#xf5;" k="35" />
<hkern u1="&#x2a;" u2="&#xf4;" k="35" />
<hkern u1="&#x2a;" u2="&#xf3;" k="35" />
<hkern u1="&#x2a;" u2="&#xf2;" k="35" />
<hkern u1="&#x2a;" u2="&#xf0;" k="35" />
<hkern u1="&#x2a;" u2="&#xef;" k="-33" />
<hkern u1="&#x2a;" u2="&#xee;" k="-43" />
<hkern u1="&#x2a;" u2="&#xec;" k="-20" />
<hkern u1="&#x2a;" u2="&#xeb;" k="35" />
<hkern u1="&#x2a;" u2="&#xea;" k="35" />
<hkern u1="&#x2a;" u2="&#xe9;" k="35" />
<hkern u1="&#x2a;" u2="&#xe8;" k="35" />
<hkern u1="&#x2a;" u2="&#xe7;" k="35" />
<hkern u1="&#x2a;" u2="&#xc6;" k="98" />
<hkern u1="&#x2a;" u2="&#xc5;" k="80" />
<hkern u1="&#x2a;" u2="&#xc4;" k="80" />
<hkern u1="&#x2a;" u2="&#xc3;" k="80" />
<hkern u1="&#x2a;" u2="&#xc2;" k="80" />
<hkern u1="&#x2a;" u2="&#xc1;" k="80" />
<hkern u1="&#x2a;" u2="&#xc0;" k="80" />
<hkern u1="&#x2a;" u2="s" k="23" />
<hkern u1="&#x2a;" u2="q" k="41" />
<hkern u1="&#x2a;" u2="o" k="35" />
<hkern u1="&#x2a;" u2="g" k="29" />
<hkern u1="&#x2a;" u2="e" k="35" />
<hkern u1="&#x2a;" u2="d" k="41" />
<hkern u1="&#x2a;" u2="c" k="35" />
<hkern u1="&#x2a;" u2="Z" k="23" />
<hkern u1="&#x2a;" u2="T" k="-8" />
<hkern u1="&#x2a;" u2="J" k="43" />
<hkern u1="&#x2a;" u2="A" k="80" />
<hkern u1="&#x2c;" u2="v" k="72" />
<hkern u1="&#x2c;" u2="f" k="20" />
<hkern u1="&#x2c;" u2="V" k="109" />
<hkern u1="&#x2d;" u2="&#xc6;" k="35" />
<hkern u1="&#x2d;" u2="x" k="63" />
<hkern u1="&#x2d;" u2="v" k="27" />
<hkern u1="&#x2d;" u2="f" k="25" />
<hkern u1="&#x2d;" u2="X" k="80" />
<hkern u1="&#x2d;" u2="V" k="63" />
<hkern u1="&#x2e;" u2="v" k="72" />
<hkern u1="&#x2e;" u2="f" k="20" />
<hkern u1="&#x2e;" u2="V" k="109" />
<hkern u1="&#x2f;" u2="&#x153;" k="68" />
<hkern u1="&#x2f;" u2="&#x152;" k="12" />
<hkern u1="&#x2f;" u2="&#xff;" k="12" />
<hkern u1="&#x2f;" u2="&#xfd;" k="12" />
<hkern u1="&#x2f;" u2="&#xfc;" k="37" />
<hkern u1="&#x2f;" u2="&#xfb;" k="37" />
<hkern u1="&#x2f;" u2="&#xfa;" k="37" />
<hkern u1="&#x2f;" u2="&#xf9;" k="37" />
<hkern u1="&#x2f;" u2="&#xf8;" k="68" />
<hkern u1="&#x2f;" u2="&#xf6;" k="68" />
<hkern u1="&#x2f;" u2="&#xf5;" k="68" />
<hkern u1="&#x2f;" u2="&#xf4;" k="68" />
<hkern u1="&#x2f;" u2="&#xf3;" k="68" />
<hkern u1="&#x2f;" u2="&#xf2;" k="68" />
<hkern u1="&#x2f;" u2="&#xf1;" k="41" />
<hkern u1="&#x2f;" u2="&#xf0;" k="31" />
<hkern u1="&#x2f;" u2="&#xef;" k="-51" />
<hkern u1="&#x2f;" u2="&#xec;" k="-84" />
<hkern u1="&#x2f;" u2="&#xeb;" k="68" />
<hkern u1="&#x2f;" u2="&#xea;" k="68" />
<hkern u1="&#x2f;" u2="&#xe9;" k="68" />
<hkern u1="&#x2f;" u2="&#xe8;" k="68" />
<hkern u1="&#x2f;" u2="&#xe7;" k="68" />
<hkern u1="&#x2f;" u2="&#xe6;" k="43" />
<hkern u1="&#x2f;" u2="&#xe5;" k="43" />
<hkern u1="&#x2f;" u2="&#xe4;" k="43" />
<hkern u1="&#x2f;" u2="&#xe3;" k="43" />
<hkern u1="&#x2f;" u2="&#xe2;" k="43" />
<hkern u1="&#x2f;" u2="&#xe1;" k="43" />
<hkern u1="&#x2f;" u2="&#xe0;" k="43" />
<hkern u1="&#x2f;" u2="&#xd8;" k="12" />
<hkern u1="&#x2f;" u2="&#xd6;" k="12" />
<hkern u1="&#x2f;" u2="&#xd5;" k="12" />
<hkern u1="&#x2f;" u2="&#xd4;" k="12" />
<hkern u1="&#x2f;" u2="&#xd3;" k="12" />
<hkern u1="&#x2f;" u2="&#xd2;" k="12" />
<hkern u1="&#x2f;" u2="&#xc7;" k="10" />
<hkern u1="&#x2f;" u2="&#xc6;" k="106" />
<hkern u1="&#x2f;" u2="&#xc5;" k="88" />
<hkern u1="&#x2f;" u2="&#xc4;" k="88" />
<hkern u1="&#x2f;" u2="&#xc3;" k="88" />
<hkern u1="&#x2f;" u2="&#xc2;" k="88" />
<hkern u1="&#x2f;" u2="&#xc1;" k="88" />
<hkern u1="&#x2f;" u2="&#xc0;" k="88" />
<hkern u1="&#x2f;" u2="z" k="14" />
<hkern u1="&#x2f;" u2="y" k="12" />
<hkern u1="&#x2f;" u2="w" k="10" />
<hkern u1="&#x2f;" u2="v" k="10" />
<hkern u1="&#x2f;" u2="u" k="37" />
<hkern u1="&#x2f;" u2="s" k="53" />
<hkern u1="&#x2f;" u2="r" k="41" />
<hkern u1="&#x2f;" u2="q" k="70" />
<hkern u1="&#x2f;" u2="p" k="41" />
<hkern u1="&#x2f;" u2="o" k="68" />
<hkern u1="&#x2f;" u2="n" k="41" />
<hkern u1="&#x2f;" u2="m" k="41" />
<hkern u1="&#x2f;" u2="g" k="66" />
<hkern u1="&#x2f;" u2="e" k="68" />
<hkern u1="&#x2f;" u2="d" k="70" />
<hkern u1="&#x2f;" u2="c" k="68" />
<hkern u1="&#x2f;" u2="a" k="43" />
<hkern u1="&#x2f;" u2="Q" k="12" />
<hkern u1="&#x2f;" u2="O" k="12" />
<hkern u1="&#x2f;" u2="J" k="39" />
<hkern u1="&#x2f;" u2="G" k="12" />
<hkern u1="&#x2f;" u2="C" k="10" />
<hkern u1="&#x2f;" u2="A" k="88" />
<hkern u1="&#x2f;" u2="&#x2f;" k="575" />
<hkern u1="&#x3a;" u2="V" k="29" />
<hkern u1="&#x3b;" u2="V" k="29" />
<hkern u1="&#x40;" u2="&#x178;" k="59" />
<hkern u1="&#x40;" u2="&#xdd;" k="59" />
<hkern u1="&#x40;" u2="&#xc6;" k="12" />
<hkern u1="&#x40;" u2="Y" k="59" />
<hkern u1="&#x40;" u2="V" k="16" />
<hkern u1="&#x40;" u2="T" k="33" />
<hkern u1="&#x40;" u2="J" k="16" />
<hkern u1="A" u2="&#x2122;" k="84" />
<hkern u1="A" u2="&#xf0;" k="10" />
<hkern u1="A" u2="&#xae;" k="53" />
<hkern u1="A" u2="&#x7d;" k="41" />
<hkern u1="A" u2="v" k="37" />
<hkern u1="A" u2="f" k="14" />
<hkern u1="A" u2="]" k="47" />
<hkern u1="A" u2="\" k="96" />
<hkern u1="A" u2="V" k="57" />
<hkern u1="A" u2="&#x3f;" k="51" />
<hkern u1="A" u2="&#x2a;" k="74" />
<hkern u1="B" u2="&#x178;" k="51" />
<hkern u1="B" u2="&#xff;" k="8" />
<hkern u1="B" u2="&#xfd;" k="8" />
<hkern u1="B" u2="&#xdd;" k="51" />
<hkern u1="B" u2="&#xc6;" k="27" />
<hkern u1="B" u2="&#xc5;" k="20" />
<hkern u1="B" u2="&#xc4;" k="20" />
<hkern u1="B" u2="&#xc3;" k="20" />
<hkern u1="B" u2="&#xc2;" k="20" />
<hkern u1="B" u2="&#xc1;" k="20" />
<hkern u1="B" u2="&#xc0;" k="20" />
<hkern u1="B" u2="&#x7d;" k="41" />
<hkern u1="B" u2="y" k="8" />
<hkern u1="B" u2="x" k="10" />
<hkern u1="B" u2="w" k="8" />
<hkern u1="B" u2="v" k="8" />
<hkern u1="B" u2="g" k="18" />
<hkern u1="B" u2="]" k="55" />
<hkern u1="B" u2="\" k="12" />
<hkern u1="B" u2="Y" k="51" />
<hkern u1="B" u2="X" k="27" />
<hkern u1="B" u2="W" k="6" />
<hkern u1="B" u2="V" k="23" />
<hkern u1="B" u2="T" k="33" />
<hkern u1="B" u2="J" k="33" />
<hkern u1="B" u2="A" k="20" />
<hkern u1="B" u2="&#x3f;" k="14" />
<hkern u1="C" u2="&#xf0;" k="14" />
<hkern u1="C" u2="&#xef;" k="-29" />
<hkern u1="C" u2="&#xee;" k="-25" />
<hkern u1="C" u2="&#xec;" k="-68" />
<hkern u1="C" u2="&#xae;" k="27" />
<hkern u1="C" u2="v" k="33" />
<hkern u1="C" u2="f" k="12" />
<hkern u1="D" u2="&#xc6;" k="35" />
<hkern u1="D" u2="&#x7d;" k="53" />
<hkern u1="D" u2="x" k="6" />
<hkern u1="D" u2="]" k="59" />
<hkern u1="D" u2="\" k="14" />
<hkern u1="D" u2="X" k="43" />
<hkern u1="D" u2="V" k="25" />
<hkern u1="D" u2="&#x3f;" k="18" />
<hkern u1="D" u2="&#x2f;" k="12" />
<hkern u1="D" u2="&#x29;" k="25" />
<hkern u1="E" u2="&#xf0;" k="16" />
<hkern u1="E" u2="&#xef;" k="-27" />
<hkern u1="E" u2="&#xee;" k="-25" />
<hkern u1="E" u2="&#xec;" k="-74" />
<hkern u1="E" u2="v" k="23" />
<hkern u1="E" u2="f" k="6" />
<hkern u1="F" u2="&#x2026;" k="147" />
<hkern u1="F" u2="&#x201e;" k="147" />
<hkern u1="F" u2="&#x201a;" k="147" />
<hkern u1="F" u2="&#x2014;" k="8" />
<hkern u1="F" u2="&#x2013;" k="8" />
<hkern u1="F" u2="&#x153;" k="37" />
<hkern u1="F" u2="&#x152;" k="20" />
<hkern u1="F" u2="&#xff;" k="27" />
<hkern u1="F" u2="&#xfd;" k="27" />
<hkern u1="F" u2="&#xfc;" k="37" />
<hkern u1="F" u2="&#xfb;" k="37" />
<hkern u1="F" u2="&#xfa;" k="37" />
<hkern u1="F" u2="&#xf9;" k="37" />
<hkern u1="F" u2="&#xf8;" k="37" />
<hkern u1="F" u2="&#xf6;" k="37" />
<hkern u1="F" u2="&#xf5;" k="37" />
<hkern u1="F" u2="&#xf4;" k="37" />
<hkern u1="F" u2="&#xf3;" k="37" />
<hkern u1="F" u2="&#xf2;" k="37" />
<hkern u1="F" u2="&#xf1;" k="43" />
<hkern u1="F" u2="&#xf0;" k="41" />
<hkern u1="F" u2="&#xef;" k="-57" />
<hkern u1="F" u2="&#xee;" k="-37" />
<hkern u1="F" u2="&#xec;" k="-113" />
<hkern u1="F" u2="&#xeb;" k="37" />
<hkern u1="F" u2="&#xea;" k="37" />
<hkern u1="F" u2="&#xe9;" k="37" />
<hkern u1="F" u2="&#xe8;" k="37" />
<hkern u1="F" u2="&#xe7;" k="37" />
<hkern u1="F" u2="&#xe6;" k="68" />
<hkern u1="F" u2="&#xe5;" k="68" />
<hkern u1="F" u2="&#xe4;" k="68" />
<hkern u1="F" u2="&#xe3;" k="68" />
<hkern u1="F" u2="&#xe2;" k="68" />
<hkern u1="F" u2="&#xe1;" k="68" />
<hkern u1="F" u2="&#xe0;" k="68" />
<hkern u1="F" u2="&#xd8;" k="20" />
<hkern u1="F" u2="&#xd6;" k="20" />
<hkern u1="F" u2="&#xd5;" k="20" />
<hkern u1="F" u2="&#xd4;" k="20" />
<hkern u1="F" u2="&#xd3;" k="20" />
<hkern u1="F" u2="&#xd2;" k="20" />
<hkern u1="F" u2="&#xc7;" k="20" />
<hkern u1="F" u2="&#xc6;" k="94" />
<hkern u1="F" u2="&#xc5;" k="70" />
<hkern u1="F" u2="&#xc4;" k="70" />
<hkern u1="F" u2="&#xc3;" k="70" />
<hkern u1="F" u2="&#xc2;" k="70" />
<hkern u1="F" u2="&#xc1;" k="70" />
<hkern u1="F" u2="&#xc0;" k="70" />
<hkern u1="F" u2="z" k="41" />
<hkern u1="F" u2="y" k="27" />
<hkern u1="F" u2="x" k="47" />
<hkern u1="F" u2="w" k="29" />
<hkern u1="F" u2="v" k="23" />
<hkern u1="F" u2="u" k="37" />
<hkern u1="F" u2="t" k="14" />
<hkern u1="F" u2="s" k="35" />
<hkern u1="F" u2="r" k="43" />
<hkern u1="F" u2="q" k="39" />
<hkern u1="F" u2="p" k="43" />
<hkern u1="F" u2="o" k="37" />
<hkern u1="F" u2="n" k="43" />
<hkern u1="F" u2="m" k="43" />
<hkern u1="F" u2="g" k="43" />
<hkern u1="F" u2="f" k="14" />
<hkern u1="F" u2="e" k="37" />
<hkern u1="F" u2="d" k="39" />
<hkern u1="F" u2="c" k="37" />
<hkern u1="F" u2="a" k="68" />
<hkern u1="F" u2="X" k="8" />
<hkern u1="F" u2="S" k="20" />
<hkern u1="F" u2="Q" k="20" />
<hkern u1="F" u2="O" k="20" />
<hkern u1="F" u2="J" k="43" />
<hkern u1="F" u2="G" k="20" />
<hkern u1="F" u2="C" k="20" />
<hkern u1="F" u2="A" k="70" />
<hkern u1="F" u2="&#x2f;" k="63" />
<hkern u1="F" u2="&#x2e;" k="147" />
<hkern u1="F" u2="&#x2d;" k="8" />
<hkern u1="F" u2="&#x2c;" k="147" />
<hkern u1="G" u2="&#xef;" k="-12" />
<hkern u1="G" u2="&#xee;" k="-8" />
<hkern u1="G" u2="&#xec;" k="-31" />
<hkern u1="G" u2="v" k="14" />
<hkern u1="G" u2="f" k="12" />
<hkern u1="G" u2="\" k="8" />
<hkern u1="G" u2="V" k="18" />
<hkern u1="H" u2="&#xf0;" k="6" />
<hkern u1="H" u2="&#xec;" k="-6" />
<hkern u1="I" u2="&#xf0;" k="6" />
<hkern u1="I" u2="&#xec;" k="-6" />
<hkern u1="J" u2="&#xf0;" k="6" />
<hkern u1="J" u2="&#xec;" k="-8" />
<hkern u1="K" u2="&#xf0;" k="16" />
<hkern u1="K" u2="&#xef;" k="-55" />
<hkern u1="K" u2="&#xec;" k="-100" />
<hkern u1="K" u2="&#xae;" k="23" />
<hkern u1="K" u2="v" k="49" />
<hkern u1="K" u2="f" k="8" />
<hkern u1="L" u2="&#x2122;" k="184" />
<hkern u1="L" u2="&#xf0;" k="10" />
<hkern u1="L" u2="&#xb7;" k="115" />
<hkern u1="L" u2="&#xae;" k="172" />
<hkern u1="L" u2="&#x7d;" k="23" />
<hkern u1="L" u2="v" k="92" />
<hkern u1="L" u2="f" k="6" />
<hkern u1="L" u2="]" k="29" />
<hkern u1="L" u2="\" k="145" />
<hkern u1="L" u2="V" k="121" />
<hkern u1="L" u2="&#x3f;" k="27" />
<hkern u1="L" u2="&#x2a;" k="182" />
<hkern u1="M" u2="&#xf0;" k="6" />
<hkern u1="M" u2="&#xec;" k="-6" />
<hkern u1="N" u2="&#xf0;" k="6" />
<hkern u1="N" u2="&#xec;" k="-6" />
<hkern u1="O" u2="&#xc6;" k="31" />
<hkern u1="O" u2="&#x7d;" k="51" />
<hkern u1="O" u2="]" k="57" />
<hkern u1="O" u2="\" k="16" />
<hkern u1="O" u2="X" k="39" />
<hkern u1="O" u2="V" k="25" />
<hkern u1="O" u2="&#x3f;" k="14" />
<hkern u1="O" u2="&#x2f;" k="12" />
<hkern u1="O" u2="&#x29;" k="10" />
<hkern u1="P" u2="&#x2039;" k="18" />
<hkern u1="P" u2="&#x2026;" k="166" />
<hkern u1="P" u2="&#x201e;" k="166" />
<hkern u1="P" u2="&#x201a;" k="166" />
<hkern u1="P" u2="&#x2014;" k="18" />
<hkern u1="P" u2="&#x2013;" k="18" />
<hkern u1="P" u2="&#x178;" k="41" />
<hkern u1="P" u2="&#x153;" k="8" />
<hkern u1="P" u2="&#xf8;" k="8" />
<hkern u1="P" u2="&#xf6;" k="8" />
<hkern u1="P" u2="&#xf5;" k="8" />
<hkern u1="P" u2="&#xf4;" k="8" />
<hkern u1="P" u2="&#xf3;" k="8" />
<hkern u1="P" u2="&#xf2;" k="8" />
<hkern u1="P" u2="&#xf0;" k="35" />
<hkern u1="P" u2="&#xef;" k="-6" />
<hkern u1="P" u2="&#xee;" k="-12" />
<hkern u1="P" u2="&#xec;" k="-8" />
<hkern u1="P" u2="&#xeb;" k="8" />
<hkern u1="P" u2="&#xea;" k="8" />
<hkern u1="P" u2="&#xe9;" k="8" />
<hkern u1="P" u2="&#xe8;" k="8" />
<hkern u1="P" u2="&#xe7;" k="8" />
<hkern u1="P" u2="&#xe6;" k="10" />
<hkern u1="P" u2="&#xe5;" k="10" />
<hkern u1="P" u2="&#xe4;" k="10" />
<hkern u1="P" u2="&#xe3;" k="10" />
<hkern u1="P" u2="&#xe2;" k="10" />
<hkern u1="P" u2="&#xe1;" k="10" />
<hkern u1="P" u2="&#xe0;" k="10" />
<hkern u1="P" u2="&#xdd;" k="41" />
<hkern u1="P" u2="&#xc6;" k="74" />
<hkern u1="P" u2="&#xc5;" k="63" />
<hkern u1="P" u2="&#xc4;" k="63" />
<hkern u1="P" u2="&#xc3;" k="63" />
<hkern u1="P" u2="&#xc2;" k="63" />
<hkern u1="P" u2="&#xc1;" k="63" />
<hkern u1="P" u2="&#xc0;" k="63" />
<hkern u1="P" u2="&#xab;" k="18" />
<hkern u1="P" u2="&#x7d;" k="43" />
<hkern u1="P" u2="q" k="10" />
<hkern u1="P" u2="o" k="8" />
<hkern u1="P" u2="g" k="8" />
<hkern u1="P" u2="e" k="8" />
<hkern u1="P" u2="d" k="10" />
<hkern u1="P" u2="c" k="8" />
<hkern u1="P" u2="a" k="10" />
<hkern u1="P" u2="]" k="47" />
<hkern u1="P" u2="\" k="10" />
<hkern u1="P" u2="Z" k="14" />
<hkern u1="P" u2="Y" k="41" />
<hkern u1="P" u2="X" k="35" />
<hkern u1="P" u2="V" k="12" />
<hkern u1="P" u2="J" k="53" />
<hkern u1="P" u2="A" k="63" />
<hkern u1="P" u2="&#x2f;" k="74" />
<hkern u1="P" u2="&#x2e;" k="166" />
<hkern u1="P" u2="&#x2d;" k="18" />
<hkern u1="P" u2="&#x2c;" k="166" />
<hkern u1="P" u2="&#x29;" k="8" />
<hkern u1="Q" u2="&#xc6;" k="31" />
<hkern u1="Q" u2="&#x7d;" k="51" />
<hkern u1="Q" u2="]" k="57" />
<hkern u1="Q" u2="\" k="16" />
<hkern u1="Q" u2="X" k="39" />
<hkern u1="Q" u2="V" k="25" />
<hkern u1="Q" u2="&#x3f;" k="14" />
<hkern u1="Q" u2="&#x2f;" k="12" />
<hkern u1="Q" u2="&#x29;" k="10" />
<hkern u1="R" u2="&#xf0;" k="27" />
<hkern u1="R" u2="&#xc6;" k="16" />
<hkern u1="R" u2="&#x7d;" k="31" />
<hkern u1="R" u2="]" k="35" />
<hkern u1="R" u2="\" k="12" />
<hkern u1="R" u2="V" k="20" />
<hkern u1="S" u2="&#xef;" k="-16" />
<hkern u1="S" u2="&#xee;" k="-6" />
<hkern u1="S" u2="&#xec;" k="-41" />
<hkern u1="S" u2="&#xc6;" k="25" />
<hkern u1="S" u2="x" k="18" />
<hkern u1="S" u2="v" k="20" />
<hkern u1="S" u2="f" k="16" />
<hkern u1="S" u2="X" k="10" />
<hkern u1="S" u2="V" k="18" />
<hkern u1="T" u2="&#xf0;" k="72" />
<hkern u1="T" u2="&#xef;" k="-84" />
<hkern u1="T" u2="&#xee;" k="-45" />
<hkern u1="T" u2="&#xec;" k="-141" />
<hkern u1="T" u2="&#xe4;" k="168" />
<hkern u1="T" u2="&#xe3;" k="135" />
<hkern u1="T" u2="&#xdf;" k="8" />
<hkern u1="T" u2="&#xc6;" k="111" />
<hkern u1="T" u2="&#xae;" k="16" />
<hkern u1="T" u2="x" k="147" />
<hkern u1="T" u2="v" k="137" />
<hkern u1="T" u2="f" k="29" />
<hkern u1="T" u2="&#x40;" k="53" />
<hkern u1="T" u2="&#x2f;" k="104" />
<hkern u1="T" u2="&#x26;" k="43" />
<hkern u1="U" u2="&#xf0;" k="6" />
<hkern u1="U" u2="&#xec;" k="-12" />
<hkern u1="U" u2="&#xc6;" k="18" />
<hkern u1="U" u2="&#x2f;" k="12" />
<hkern u1="V" u2="&#x203a;" k="39" />
<hkern u1="V" u2="&#x2039;" k="57" />
<hkern u1="V" u2="&#x2026;" k="109" />
<hkern u1="V" u2="&#x201e;" k="109" />
<hkern u1="V" u2="&#x201a;" k="109" />
<hkern u1="V" u2="&#x2014;" k="63" />
<hkern u1="V" u2="&#x2013;" k="63" />
<hkern u1="V" u2="&#x153;" k="66" />
<hkern u1="V" u2="&#x152;" k="25" />
<hkern u1="V" u2="&#xff;" k="16" />
<hkern u1="V" u2="&#xfd;" k="16" />
<hkern u1="V" u2="&#xfc;" k="47" />
<hkern u1="V" u2="&#xfb;" k="47" />
<hkern u1="V" u2="&#xfa;" k="47" />
<hkern u1="V" u2="&#xf9;" k="47" />
<hkern u1="V" u2="&#xf8;" k="66" />
<hkern u1="V" u2="&#xf6;" k="66" />
<hkern u1="V" u2="&#xf5;" k="66" />
<hkern u1="V" u2="&#xf4;" k="66" />
<hkern u1="V" u2="&#xf3;" k="66" />
<hkern u1="V" u2="&#xf2;" k="66" />
<hkern u1="V" u2="&#xf1;" k="53" />
<hkern u1="V" u2="&#xf0;" k="57" />
<hkern u1="V" u2="&#xef;" k="-61" />
<hkern u1="V" u2="&#xee;" k="-25" />
<hkern u1="V" u2="&#xec;" k="-113" />
<hkern u1="V" u2="&#xeb;" k="66" />
<hkern u1="V" u2="&#xea;" k="66" />
<hkern u1="V" u2="&#xe9;" k="66" />
<hkern u1="V" u2="&#xe8;" k="66" />
<hkern u1="V" u2="&#xe7;" k="66" />
<hkern u1="V" u2="&#xe6;" k="55" />
<hkern u1="V" u2="&#xe5;" k="55" />
<hkern u1="V" u2="&#xe4;" k="55" />
<hkern u1="V" u2="&#xe3;" k="55" />
<hkern u1="V" u2="&#xe2;" k="55" />
<hkern u1="V" u2="&#xe1;" k="55" />
<hkern u1="V" u2="&#xe0;" k="55" />
<hkern u1="V" u2="&#xd8;" k="25" />
<hkern u1="V" u2="&#xd6;" k="25" />
<hkern u1="V" u2="&#xd5;" k="25" />
<hkern u1="V" u2="&#xd4;" k="25" />
<hkern u1="V" u2="&#xd3;" k="25" />
<hkern u1="V" u2="&#xd2;" k="25" />
<hkern u1="V" u2="&#xc7;" k="23" />
<hkern u1="V" u2="&#xc6;" k="66" />
<hkern u1="V" u2="&#xc5;" k="57" />
<hkern u1="V" u2="&#xc4;" k="57" />
<hkern u1="V" u2="&#xc3;" k="57" />
<hkern u1="V" u2="&#xc2;" k="57" />
<hkern u1="V" u2="&#xc1;" k="57" />
<hkern u1="V" u2="&#xc0;" k="57" />
<hkern u1="V" u2="&#xbb;" k="39" />
<hkern u1="V" u2="&#xae;" k="14" />
<hkern u1="V" u2="&#xab;" k="57" />
<hkern u1="V" u2="z" k="25" />
<hkern u1="V" u2="y" k="16" />
<hkern u1="V" u2="x" k="16" />
<hkern u1="V" u2="w" k="20" />
<hkern u1="V" u2="v" k="16" />
<hkern u1="V" u2="u" k="47" />
<hkern u1="V" u2="s" k="51" />
<hkern u1="V" u2="r" k="53" />
<hkern u1="V" u2="q" k="66" />
<hkern u1="V" u2="p" k="53" />
<hkern u1="V" u2="o" k="66" />
<hkern u1="V" u2="n" k="53" />
<hkern u1="V" u2="m" k="53" />
<hkern u1="V" u2="g" k="76" />
<hkern u1="V" u2="f" k="6" />
<hkern u1="V" u2="e" k="66" />
<hkern u1="V" u2="d" k="66" />
<hkern u1="V" u2="c" k="66" />
<hkern u1="V" u2="a" k="55" />
<hkern u1="V" u2="S" k="16" />
<hkern u1="V" u2="Q" k="25" />
<hkern u1="V" u2="O" k="25" />
<hkern u1="V" u2="J" k="55" />
<hkern u1="V" u2="G" k="25" />
<hkern u1="V" u2="C" k="23" />
<hkern u1="V" u2="A" k="57" />
<hkern u1="V" u2="&#x40;" k="29" />
<hkern u1="V" u2="&#x3b;" k="29" />
<hkern u1="V" u2="&#x3a;" k="29" />
<hkern u1="V" u2="&#x2f;" k="84" />
<hkern u1="V" u2="&#x2e;" k="109" />
<hkern u1="V" u2="&#x2d;" k="63" />
<hkern u1="V" u2="&#x2c;" k="109" />
<hkern u1="V" u2="&#x26;" k="39" />
<hkern u1="W" u2="&#xf0;" k="37" />
<hkern u1="W" u2="&#xef;" k="-43" />
<hkern u1="W" u2="&#xee;" k="-25" />
<hkern u1="W" u2="&#xec;" k="-98" />
<hkern u1="W" u2="&#xc6;" k="55" />
<hkern u1="W" u2="&#x2f;" k="55" />
<hkern u1="W" u2="&#x26;" k="12" />
<hkern u1="X" u2="&#x2039;" k="47" />
<hkern u1="X" u2="&#x2014;" k="78" />
<hkern u1="X" u2="&#x2013;" k="78" />
<hkern u1="X" u2="&#x153;" k="43" />
<hkern u1="X" u2="&#x152;" k="39" />
<hkern u1="X" u2="&#xff;" k="55" />
<hkern u1="X" u2="&#xfd;" k="55" />
<hkern u1="X" u2="&#xfc;" k="33" />
<hkern u1="X" u2="&#xfb;" k="33" />
<hkern u1="X" u2="&#xfa;" k="33" />
<hkern u1="X" u2="&#xf9;" k="33" />
<hkern u1="X" u2="&#xf8;" k="43" />
<hkern u1="X" u2="&#xf6;" k="43" />
<hkern u1="X" u2="&#xf5;" k="43" />
<hkern u1="X" u2="&#xf4;" k="43" />
<hkern u1="X" u2="&#xf3;" k="43" />
<hkern u1="X" u2="&#xf2;" k="43" />
<hkern u1="X" u2="&#xf0;" k="25" />
<hkern u1="X" u2="&#xef;" k="-74" />
<hkern u1="X" u2="&#xee;" k="-10" />
<hkern u1="X" u2="&#xec;" k="-115" />
<hkern u1="X" u2="&#xeb;" k="43" />
<hkern u1="X" u2="&#xea;" k="43" />
<hkern u1="X" u2="&#xe9;" k="43" />
<hkern u1="X" u2="&#xe8;" k="43" />
<hkern u1="X" u2="&#xe7;" k="43" />
<hkern u1="X" u2="&#xd8;" k="39" />
<hkern u1="X" u2="&#xd6;" k="39" />
<hkern u1="X" u2="&#xd5;" k="39" />
<hkern u1="X" u2="&#xd4;" k="39" />
<hkern u1="X" u2="&#xd3;" k="39" />
<hkern u1="X" u2="&#xd2;" k="39" />
<hkern u1="X" u2="&#xc7;" k="37" />
<hkern u1="X" u2="&#xae;" k="18" />
<hkern u1="X" u2="&#xab;" k="47" />
<hkern u1="X" u2="y" k="55" />
<hkern u1="X" u2="w" k="51" />
<hkern u1="X" u2="v" k="53" />
<hkern u1="X" u2="u" k="33" />
<hkern u1="X" u2="t" k="18" />
<hkern u1="X" u2="q" k="37" />
<hkern u1="X" u2="o" k="43" />
<hkern u1="X" u2="g" k="33" />
<hkern u1="X" u2="f" k="8" />
<hkern u1="X" u2="e" k="43" />
<hkern u1="X" u2="d" k="37" />
<hkern u1="X" u2="c" k="43" />
<hkern u1="X" u2="Q" k="39" />
<hkern u1="X" u2="O" k="39" />
<hkern u1="X" u2="G" k="39" />
<hkern u1="X" u2="C" k="37" />
<hkern u1="X" u2="&#x2d;" k="78" />
<hkern u1="Y" u2="&#xff;" k="70" />
<hkern u1="Y" u2="&#xf0;" k="80" />
<hkern u1="Y" u2="&#xef;" k="-90" />
<hkern u1="Y" u2="&#xee;" k="-16" />
<hkern u1="Y" u2="&#xec;" k="-131" />
<hkern u1="Y" u2="&#xeb;" k="137" />
<hkern u1="Y" u2="&#xe4;" k="117" />
<hkern u1="Y" u2="&#xe3;" k="109" />
<hkern u1="Y" u2="&#xdf;" k="12" />
<hkern u1="Y" u2="&#xc6;" k="106" />
<hkern u1="Y" u2="&#xae;" k="45" />
<hkern u1="Y" u2="x" k="82" />
<hkern u1="Y" u2="v" k="84" />
<hkern u1="Y" u2="f" k="37" />
<hkern u1="Y" u2="&#x40;" k="74" />
<hkern u1="Y" u2="&#x2f;" k="131" />
<hkern u1="Y" u2="&#x26;" k="76" />
<hkern u1="Z" u2="&#xf0;" k="16" />
<hkern u1="Z" u2="&#xef;" k="-25" />
<hkern u1="Z" u2="&#xee;" k="-31" />
<hkern u1="Z" u2="&#xec;" k="-80" />
<hkern u1="Z" u2="&#xae;" k="14" />
<hkern u1="Z" u2="v" k="25" />
<hkern u1="Z" u2="f" k="6" />
<hkern u1="[" u2="&#x153;" k="78" />
<hkern u1="[" u2="&#x152;" k="57" />
<hkern u1="[" u2="&#xff;" k="61" />
<hkern u1="[" u2="&#xfd;" k="61" />
<hkern u1="[" u2="&#xfc;" k="70" />
<hkern u1="[" u2="&#xfb;" k="70" />
<hkern u1="[" u2="&#xfa;" k="70" />
<hkern u1="[" u2="&#xf9;" k="70" />
<hkern u1="[" u2="&#xf8;" k="78" />
<hkern u1="[" u2="&#xf6;" k="78" />
<hkern u1="[" u2="&#xf5;" k="78" />
<hkern u1="[" u2="&#xf4;" k="78" />
<hkern u1="[" u2="&#xf3;" k="78" />
<hkern u1="[" u2="&#xf2;" k="78" />
<hkern u1="[" u2="&#xf1;" k="51" />
<hkern u1="[" u2="&#xf0;" k="27" />
<hkern u1="[" u2="&#xef;" k="-31" />
<hkern u1="[" u2="&#xec;" k="-80" />
<hkern u1="[" u2="&#xeb;" k="78" />
<hkern u1="[" u2="&#xea;" k="78" />
<hkern u1="[" u2="&#xe9;" k="78" />
<hkern u1="[" u2="&#xe8;" k="78" />
<hkern u1="[" u2="&#xe7;" k="78" />
<hkern u1="[" u2="&#xe6;" k="63" />
<hkern u1="[" u2="&#xe5;" k="63" />
<hkern u1="[" u2="&#xe4;" k="63" />
<hkern u1="[" u2="&#xe3;" k="63" />
<hkern u1="[" u2="&#xe2;" k="63" />
<hkern u1="[" u2="&#xe1;" k="63" />
<hkern u1="[" u2="&#xe0;" k="63" />
<hkern u1="[" u2="&#xd8;" k="57" />
<hkern u1="[" u2="&#xd6;" k="57" />
<hkern u1="[" u2="&#xd5;" k="57" />
<hkern u1="[" u2="&#xd4;" k="57" />
<hkern u1="[" u2="&#xd3;" k="57" />
<hkern u1="[" u2="&#xd2;" k="57" />
<hkern u1="[" u2="&#xc7;" k="53" />
<hkern u1="[" u2="&#xc6;" k="47" />
<hkern u1="[" u2="&#xc5;" k="47" />
<hkern u1="[" u2="&#xc4;" k="47" />
<hkern u1="[" u2="&#xc3;" k="47" />
<hkern u1="[" u2="&#xc2;" k="47" />
<hkern u1="[" u2="&#xc1;" k="47" />
<hkern u1="[" u2="&#xc0;" k="47" />
<hkern u1="[" u2="&#x7b;" k="43" />
<hkern u1="[" u2="z" k="41" />
<hkern u1="[" u2="y" k="61" />
<hkern u1="[" u2="x" k="37" />
<hkern u1="[" u2="w" k="68" />
<hkern u1="[" u2="v" k="66" />
<hkern u1="[" u2="u" k="70" />
<hkern u1="[" u2="t" k="51" />
<hkern u1="[" u2="s" k="51" />
<hkern u1="[" u2="r" k="51" />
<hkern u1="[" u2="q" k="76" />
<hkern u1="[" u2="p" k="51" />
<hkern u1="[" u2="o" k="78" />
<hkern u1="[" u2="n" k="51" />
<hkern u1="[" u2="m" k="51" />
<hkern u1="[" u2="j" k="-8" />
<hkern u1="[" u2="f" k="31" />
<hkern u1="[" u2="e" k="78" />
<hkern u1="[" u2="d" k="76" />
<hkern u1="[" u2="c" k="78" />
<hkern u1="[" u2="a" k="63" />
<hkern u1="[" u2="S" k="23" />
<hkern u1="[" u2="Q" k="57" />
<hkern u1="[" u2="O" k="57" />
<hkern u1="[" u2="G" k="57" />
<hkern u1="[" u2="C" k="53" />
<hkern u1="[" u2="A" k="47" />
<hkern u1="[" u2="&#x28;" k="16" />
<hkern u1="\" u2="&#x201d;" k="139" />
<hkern u1="\" u2="&#x2019;" k="139" />
<hkern u1="\" u2="&#x178;" k="139" />
<hkern u1="\" u2="&#x153;" k="8" />
<hkern u1="\" u2="&#x152;" k="16" />
<hkern u1="\" u2="&#xff;" k="47" />
<hkern u1="\" u2="&#xfd;" k="47" />
<hkern u1="\" u2="&#xf8;" k="8" />
<hkern u1="\" u2="&#xf6;" k="8" />
<hkern u1="\" u2="&#xf5;" k="8" />
<hkern u1="\" u2="&#xf4;" k="8" />
<hkern u1="\" u2="&#xf3;" k="8" />
<hkern u1="\" u2="&#xf2;" k="8" />
<hkern u1="\" u2="&#xeb;" k="8" />
<hkern u1="\" u2="&#xea;" k="8" />
<hkern u1="\" u2="&#xe9;" k="8" />
<hkern u1="\" u2="&#xe8;" k="8" />
<hkern u1="\" u2="&#xe7;" k="8" />
<hkern u1="\" u2="&#xdd;" k="139" />
<hkern u1="\" u2="&#xdc;" k="18" />
<hkern u1="\" u2="&#xdb;" k="18" />
<hkern u1="\" u2="&#xda;" k="18" />
<hkern u1="\" u2="&#xd9;" k="18" />
<hkern u1="\" u2="&#xd8;" k="16" />
<hkern u1="\" u2="&#xd6;" k="16" />
<hkern u1="\" u2="&#xd5;" k="16" />
<hkern u1="\" u2="&#xd4;" k="16" />
<hkern u1="\" u2="&#xd3;" k="16" />
<hkern u1="\" u2="&#xd2;" k="16" />
<hkern u1="\" u2="&#xc7;" k="16" />
<hkern u1="\" u2="y" k="47" />
<hkern u1="\" u2="w" k="37" />
<hkern u1="\" u2="v" k="47" />
<hkern u1="\" u2="t" k="25" />
<hkern u1="\" u2="o" k="8" />
<hkern u1="\" u2="f" k="10" />
<hkern u1="\" u2="e" k="8" />
<hkern u1="\" u2="c" k="8" />
<hkern u1="\" u2="Y" k="139" />
<hkern u1="\" u2="W" k="61" />
<hkern u1="\" u2="V" k="90" />
<hkern u1="\" u2="U" k="18" />
<hkern u1="\" u2="T" k="117" />
<hkern u1="\" u2="S" k="8" />
<hkern u1="\" u2="Q" k="16" />
<hkern u1="\" u2="O" k="16" />
<hkern u1="\" u2="G" k="16" />
<hkern u1="\" u2="C" k="16" />
<hkern u1="\" u2="&#x27;" k="141" />
<hkern u1="\" u2="&#x22;" k="141" />
<hkern u1="a" u2="&#x2122;" k="25" />
<hkern u1="a" u2="&#x7d;" k="18" />
<hkern u1="a" u2="v" k="10" />
<hkern u1="a" u2="]" k="23" />
<hkern u1="a" u2="\" k="72" />
<hkern u1="a" u2="V" k="51" />
<hkern u1="a" u2="&#x3f;" k="31" />
<hkern u1="a" u2="&#x2a;" k="6" />
<hkern u1="b" u2="&#x2122;" k="35" />
<hkern u1="b" u2="&#xc6;" k="14" />
<hkern u1="b" u2="&#x7d;" k="68" />
<hkern u1="b" u2="x" k="25" />
<hkern u1="b" u2="v" k="16" />
<hkern u1="b" u2="]" k="76" />
<hkern u1="b" u2="\" k="74" />
<hkern u1="b" u2="X" k="41" />
<hkern u1="b" u2="V" k="63" />
<hkern u1="b" u2="&#x3f;" k="51" />
<hkern u1="b" u2="&#x2a;" k="16" />
<hkern u1="b" u2="&#x29;" k="37" />
<hkern u1="c" u2="&#xf0;" k="23" />
<hkern u1="c" u2="&#x7d;" k="33" />
<hkern u1="c" u2="]" k="39" />
<hkern u1="c" u2="\" k="20" />
<hkern u1="c" u2="V" k="25" />
<hkern u1="c" u2="&#x3f;" k="18" />
<hkern u1="d" u2="&#xef;" k="-6" />
<hkern u1="d" u2="&#xec;" k="-14" />
<hkern u1="e" u2="&#x2122;" k="29" />
<hkern u1="e" u2="&#xc6;" k="10" />
<hkern u1="e" u2="&#x7d;" k="55" />
<hkern u1="e" u2="x" k="8" />
<hkern u1="e" u2="v" k="16" />
<hkern u1="e" u2="]" k="51" />
<hkern u1="e" u2="\" k="70" />
<hkern u1="e" u2="V" k="63" />
<hkern u1="e" u2="&#x3f;" k="41" />
<hkern u1="e" u2="&#x29;" k="10" />
<hkern u1="f" u2="&#x203a;" k="45" />
<hkern u1="f" u2="&#x2039;" k="70" />
<hkern u1="f" u2="&#x2026;" k="84" />
<hkern u1="f" u2="&#x201e;" k="84" />
<hkern u1="f" u2="&#x201a;" k="84" />
<hkern u1="f" u2="&#x2014;" k="86" />
<hkern u1="f" u2="&#x2013;" k="86" />
<hkern u1="f" u2="&#x178;" k="20" />
<hkern u1="f" u2="&#x153;" k="18" />
<hkern u1="f" u2="&#xf8;" k="18" />
<hkern u1="f" u2="&#xf6;" k="18" />
<hkern u1="f" u2="&#xf5;" k="18" />
<hkern u1="f" u2="&#xf4;" k="18" />
<hkern u1="f" u2="&#xf3;" k="18" />
<hkern u1="f" u2="&#xf2;" k="18" />
<hkern u1="f" u2="&#xf0;" k="72" />
<hkern u1="f" u2="&#xef;" k="-49" />
<hkern u1="f" u2="&#xee;" k="-37" />
<hkern u1="f" u2="&#xec;" k="-158" />
<hkern u1="f" u2="&#xeb;" k="18" />
<hkern u1="f" u2="&#xea;" k="18" />
<hkern u1="f" u2="&#xe9;" k="18" />
<hkern u1="f" u2="&#xe8;" k="18" />
<hkern u1="f" u2="&#xe7;" k="18" />
<hkern u1="f" u2="&#xdd;" k="20" />
<hkern u1="f" u2="&#xc6;" k="70" />
<hkern u1="f" u2="&#xc5;" k="61" />
<hkern u1="f" u2="&#xc4;" k="61" />
<hkern u1="f" u2="&#xc3;" k="61" />
<hkern u1="f" u2="&#xc2;" k="61" />
<hkern u1="f" u2="&#xc1;" k="61" />
<hkern u1="f" u2="&#xc0;" k="61" />
<hkern u1="f" u2="&#xbb;" k="45" />
<hkern u1="f" u2="&#xab;" k="70" />
<hkern u1="f" u2="q" k="20" />
<hkern u1="f" u2="o" k="18" />
<hkern u1="f" u2="g" k="10" />
<hkern u1="f" u2="e" k="18" />
<hkern u1="f" u2="d" k="20" />
<hkern u1="f" u2="c" k="18" />
<hkern u1="f" u2="Z" k="23" />
<hkern u1="f" u2="Y" k="20" />
<hkern u1="f" u2="X" k="27" />
<hkern u1="f" u2="T" k="63" />
<hkern u1="f" u2="J" k="51" />
<hkern u1="f" u2="A" k="61" />
<hkern u1="f" u2="&#x2f;" k="55" />
<hkern u1="f" u2="&#x2e;" k="84" />
<hkern u1="f" u2="&#x2d;" k="86" />
<hkern u1="f" u2="&#x2c;" k="84" />
<hkern u1="f" u2="&#x26;" k="18" />
<hkern u1="g" u2="&#xf0;" k="10" />
<hkern u1="g" u2="j" k="-39" />
<hkern u1="g" u2="\" k="12" />
<hkern u1="h" u2="&#x2122;" k="33" />
<hkern u1="h" u2="&#x7d;" k="49" />
<hkern u1="h" u2="v" k="10" />
<hkern u1="h" u2="]" k="53" />
<hkern u1="h" u2="\" k="74" />
<hkern u1="h" u2="V" k="59" />
<hkern u1="h" u2="&#x3f;" k="45" />
<hkern u1="h" u2="&#x2a;" k="6" />
<hkern u1="h" u2="&#x29;" k="8" />
<hkern u1="i" u2="&#xef;" k="-6" />
<hkern u1="i" u2="&#xec;" k="-14" />
<hkern u1="j" u2="&#xef;" k="-6" />
<hkern u1="j" u2="&#xec;" k="-14" />
<hkern u1="k" u2="&#xf0;" k="35" />
<hkern u1="k" u2="&#x7d;" k="27" />
<hkern u1="k" u2="]" k="35" />
<hkern u1="k" u2="\" k="14" />
<hkern u1="k" u2="V" k="18" />
<hkern u1="k" u2="&#x3f;" k="14" />
<hkern u1="l" u2="&#xec;" k="-10" />
<hkern u1="l" u2="&#xb7;" k="111" />
<hkern u1="m" u2="&#x2122;" k="33" />
<hkern u1="m" u2="&#x7d;" k="49" />
<hkern u1="m" u2="v" k="10" />
<hkern u1="m" u2="]" k="53" />
<hkern u1="m" u2="\" k="74" />
<hkern u1="m" u2="V" k="59" />
<hkern u1="m" u2="&#x3f;" k="45" />
<hkern u1="m" u2="&#x2a;" k="6" />
<hkern u1="m" u2="&#x29;" k="8" />
<hkern u1="n" u2="&#x2122;" k="33" />
<hkern u1="n" u2="&#x7d;" k="49" />
<hkern u1="n" u2="v" k="10" />
<hkern u1="n" u2="]" k="53" />
<hkern u1="n" u2="\" k="74" />
<hkern u1="n" u2="V" k="59" />
<hkern u1="n" u2="&#x3f;" k="45" />
<hkern u1="n" u2="&#x2a;" k="6" />
<hkern u1="n" u2="&#x29;" k="8" />
<hkern u1="o" u2="&#x2122;" k="31" />
<hkern u1="o" u2="&#xc6;" k="14" />
<hkern u1="o" u2="&#x7d;" k="68" />
<hkern u1="o" u2="x" k="25" />
<hkern u1="o" u2="v" k="18" />
<hkern u1="o" u2="]" k="78" />
<hkern u1="o" u2="\" k="76" />
<hkern u1="o" u2="X" k="43" />
<hkern u1="o" u2="V" k="66" />
<hkern u1="o" u2="&#x3f;" k="47" />
<hkern u1="o" u2="&#x2a;" k="6" />
<hkern u1="o" u2="&#x29;" k="37" />
<hkern u1="p" u2="&#x2122;" k="35" />
<hkern u1="p" u2="&#xc6;" k="14" />
<hkern u1="p" u2="&#x7d;" k="68" />
<hkern u1="p" u2="x" k="25" />
<hkern u1="p" u2="v" k="16" />
<hkern u1="p" u2="]" k="76" />
<hkern u1="p" u2="\" k="74" />
<hkern u1="p" u2="X" k="41" />
<hkern u1="p" u2="V" k="63" />
<hkern u1="p" u2="&#x3f;" k="51" />
<hkern u1="p" u2="&#x2a;" k="16" />
<hkern u1="p" u2="&#x29;" k="37" />
<hkern u1="q" u2="&#x2122;" k="23" />
<hkern u1="q" u2="&#x7d;" k="47" />
<hkern u1="q" u2="]" k="51" />
<hkern u1="q" u2="\" k="47" />
<hkern u1="q" u2="V" k="53" />
<hkern u1="q" u2="&#x3f;" k="27" />
<hkern u1="q" u2="&#x29;" k="8" />
<hkern u1="r" u2="&#xf0;" k="82" />
<hkern u1="r" u2="&#xc6;" k="86" />
<hkern u1="r" u2="&#x7d;" k="47" />
<hkern u1="r" u2="]" k="59" />
<hkern u1="r" u2="\" k="8" />
<hkern u1="r" u2="X" k="55" />
<hkern u1="r" u2="&#x2f;" k="70" />
<hkern u1="r" u2="&#x29;" k="8" />
<hkern u1="r" u2="&#x26;" k="20" />
<hkern u1="s" u2="&#x2122;" k="25" />
<hkern u1="s" u2="&#xc6;" k="10" />
<hkern u1="s" u2="&#x7d;" k="55" />
<hkern u1="s" u2="x" k="6" />
<hkern u1="s" u2="v" k="14" />
<hkern u1="s" u2="]" k="66" />
<hkern u1="s" u2="\" k="45" />
<hkern u1="s" u2="X" k="6" />
<hkern u1="s" u2="V" k="45" />
<hkern u1="s" u2="&#x3f;" k="27" />
<hkern u1="s" u2="&#x29;" k="10" />
<hkern u1="t" u2="&#xf0;" k="8" />
<hkern u1="t" u2="&#x7d;" k="16" />
<hkern u1="t" u2="]" k="23" />
<hkern u1="t" u2="\" k="14" />
<hkern u1="u" u2="&#x2122;" k="23" />
<hkern u1="u" u2="&#x7d;" k="47" />
<hkern u1="u" u2="]" k="51" />
<hkern u1="u" u2="\" k="47" />
<hkern u1="u" u2="V" k="53" />
<hkern u1="u" u2="&#x3f;" k="27" />
<hkern u1="u" u2="&#x29;" k="8" />
<hkern u1="v" u2="&#x2039;" k="25" />
<hkern u1="v" u2="&#x2026;" k="72" />
<hkern u1="v" u2="&#x201e;" k="72" />
<hkern u1="v" u2="&#x201a;" k="72" />
<hkern u1="v" u2="&#x2014;" k="27" />
<hkern u1="v" u2="&#x2013;" k="27" />
<hkern u1="v" u2="&#x178;" k="84" />
<hkern u1="v" u2="&#x153;" k="18" />
<hkern u1="v" u2="&#xf8;" k="18" />
<hkern u1="v" u2="&#xf6;" k="18" />
<hkern u1="v" u2="&#xf5;" k="18" />
<hkern u1="v" u2="&#xf4;" k="18" />
<hkern u1="v" u2="&#xf3;" k="18" />
<hkern u1="v" u2="&#xf2;" k="18" />
<hkern u1="v" u2="&#xf0;" k="27" />
<hkern u1="v" u2="&#xeb;" k="18" />
<hkern u1="v" u2="&#xea;" k="18" />
<hkern u1="v" u2="&#xe9;" k="18" />
<hkern u1="v" u2="&#xe8;" k="18" />
<hkern u1="v" u2="&#xe7;" k="18" />
<hkern u1="v" u2="&#xe6;" k="16" />
<hkern u1="v" u2="&#xe5;" k="16" />
<hkern u1="v" u2="&#xe4;" k="16" />
<hkern u1="v" u2="&#xe3;" k="16" />
<hkern u1="v" u2="&#xe2;" k="16" />
<hkern u1="v" u2="&#xe1;" k="16" />
<hkern u1="v" u2="&#xe0;" k="16" />
<hkern u1="v" u2="&#xdd;" k="84" />
<hkern u1="v" u2="&#xc6;" k="43" />
<hkern u1="v" u2="&#xc5;" k="37" />
<hkern u1="v" u2="&#xc4;" k="37" />
<hkern u1="v" u2="&#xc3;" k="37" />
<hkern u1="v" u2="&#xc2;" k="37" />
<hkern u1="v" u2="&#xc1;" k="37" />
<hkern u1="v" u2="&#xc0;" k="37" />
<hkern u1="v" u2="&#xab;" k="25" />
<hkern u1="v" u2="&#x7d;" k="55" />
<hkern u1="v" u2="s" k="12" />
<hkern u1="v" u2="q" k="16" />
<hkern u1="v" u2="o" k="18" />
<hkern u1="v" u2="g" k="18" />
<hkern u1="v" u2="e" k="18" />
<hkern u1="v" u2="d" k="16" />
<hkern u1="v" u2="c" k="18" />
<hkern u1="v" u2="a" k="16" />
<hkern u1="v" u2="]" k="66" />
<hkern u1="v" u2="\" k="14" />
<hkern u1="v" u2="Z" k="31" />
<hkern u1="v" u2="Y" k="84" />
<hkern u1="v" u2="X" k="53" />
<hkern u1="v" u2="V" k="16" />
<hkern u1="v" u2="T" k="137" />
<hkern u1="v" u2="J" k="53" />
<hkern u1="v" u2="A" k="37" />
<hkern u1="v" u2="&#x3f;" k="18" />
<hkern u1="v" u2="&#x2f;" k="41" />
<hkern u1="v" u2="&#x2e;" k="72" />
<hkern u1="v" u2="&#x2d;" k="27" />
<hkern u1="v" u2="&#x2c;" k="72" />
<hkern u1="v" u2="&#x29;" k="8" />
<hkern u1="w" u2="&#xf0;" k="18" />
<hkern u1="w" u2="&#xc6;" k="37" />
<hkern u1="w" u2="&#x7d;" k="57" />
<hkern u1="w" u2="]" k="68" />
<hkern u1="w" u2="\" k="14" />
<hkern u1="w" u2="X" k="51" />
<hkern u1="w" u2="V" k="20" />
<hkern u1="w" u2="&#x3f;" k="20" />
<hkern u1="w" u2="&#x2f;" k="35" />
<hkern u1="w" u2="&#x29;" k="12" />
<hkern u1="x" u2="&#x2039;" k="53" />
<hkern u1="x" u2="&#x2014;" k="66" />
<hkern u1="x" u2="&#x2013;" k="66" />
<hkern u1="x" u2="&#x178;" k="80" />
<hkern u1="x" u2="&#x153;" k="25" />
<hkern u1="x" u2="&#xf8;" k="25" />
<hkern u1="x" u2="&#xf6;" k="25" />
<hkern u1="x" u2="&#xf5;" k="25" />
<hkern u1="x" u2="&#xf4;" k="25" />
<hkern u1="x" u2="&#xf3;" k="25" />
<hkern u1="x" u2="&#xf2;" k="25" />
<hkern u1="x" u2="&#xf0;" k="37" />
<hkern u1="x" u2="&#xeb;" k="25" />
<hkern u1="x" u2="&#xea;" k="25" />
<hkern u1="x" u2="&#xe9;" k="25" />
<hkern u1="x" u2="&#xe8;" k="25" />
<hkern u1="x" u2="&#xe7;" k="25" />
<hkern u1="x" u2="&#xe6;" k="8" />
<hkern u1="x" u2="&#xe5;" k="8" />
<hkern u1="x" u2="&#xe4;" k="8" />
<hkern u1="x" u2="&#xe3;" k="8" />
<hkern u1="x" u2="&#xe2;" k="8" />
<hkern u1="x" u2="&#xe1;" k="8" />
<hkern u1="x" u2="&#xe0;" k="8" />
<hkern u1="x" u2="&#xdd;" k="80" />
<hkern u1="x" u2="&#xab;" k="53" />
<hkern u1="x" u2="&#x7d;" k="33" />
<hkern u1="x" u2="q" k="27" />
<hkern u1="x" u2="o" k="25" />
<hkern u1="x" u2="g" k="20" />
<hkern u1="x" u2="e" k="25" />
<hkern u1="x" u2="d" k="27" />
<hkern u1="x" u2="c" k="25" />
<hkern u1="x" u2="a" k="8" />
<hkern u1="x" u2="]" k="39" />
<hkern u1="x" u2="\" k="12" />
<hkern u1="x" u2="Y" k="80" />
<hkern u1="x" u2="V" k="14" />
<hkern u1="x" u2="T" k="147" />
<hkern u1="x" u2="J" k="6" />
<hkern u1="x" u2="&#x2d;" k="66" />
<hkern u1="y" u2="&#xf0;" k="29" />
<hkern u1="y" u2="&#xc6;" k="43" />
<hkern u1="y" u2="&#x7d;" k="49" />
<hkern u1="y" u2="]" k="59" />
<hkern u1="y" u2="\" k="14" />
<hkern u1="y" u2="X" k="53" />
<hkern u1="y" u2="V" k="16" />
<hkern u1="y" u2="&#x3f;" k="18" />
<hkern u1="y" u2="&#x2f;" k="43" />
<hkern u1="z" u2="&#x2122;" k="10" />
<hkern u1="z" u2="&#xf0;" k="16" />
<hkern u1="z" u2="&#x7d;" k="35" />
<hkern u1="z" u2="]" k="41" />
<hkern u1="z" u2="\" k="20" />
<hkern u1="z" u2="V" k="27" />
<hkern u1="z" u2="&#x3f;" k="16" />
<hkern u1="&#x7b;" u2="&#x153;" k="68" />
<hkern u1="&#x7b;" u2="&#x152;" k="51" />
<hkern u1="&#x7b;" u2="&#xff;" k="51" />
<hkern u1="&#x7b;" u2="&#xfd;" k="51" />
<hkern u1="&#x7b;" u2="&#xfc;" k="61" />
<hkern u1="&#x7b;" u2="&#xfb;" k="61" />
<hkern u1="&#x7b;" u2="&#xfa;" k="61" />
<hkern u1="&#x7b;" u2="&#xf9;" k="61" />
<hkern u1="&#x7b;" u2="&#xf8;" k="68" />
<hkern u1="&#x7b;" u2="&#xf6;" k="68" />
<hkern u1="&#x7b;" u2="&#xf5;" k="68" />
<hkern u1="&#x7b;" u2="&#xf4;" k="68" />
<hkern u1="&#x7b;" u2="&#xf3;" k="68" />
<hkern u1="&#x7b;" u2="&#xf2;" k="68" />
<hkern u1="&#x7b;" u2="&#xf1;" k="47" />
<hkern u1="&#x7b;" u2="&#xf0;" k="18" />
<hkern u1="&#x7b;" u2="&#xef;" k="-31" />
<hkern u1="&#x7b;" u2="&#xec;" k="-78" />
<hkern u1="&#x7b;" u2="&#xeb;" k="68" />
<hkern u1="&#x7b;" u2="&#xea;" k="68" />
<hkern u1="&#x7b;" u2="&#xe9;" k="68" />
<hkern u1="&#x7b;" u2="&#xe8;" k="68" />
<hkern u1="&#x7b;" u2="&#xe7;" k="68" />
<hkern u1="&#x7b;" u2="&#xe6;" k="55" />
<hkern u1="&#x7b;" u2="&#xe5;" k="55" />
<hkern u1="&#x7b;" u2="&#xe4;" k="55" />
<hkern u1="&#x7b;" u2="&#xe3;" k="55" />
<hkern u1="&#x7b;" u2="&#xe2;" k="55" />
<hkern u1="&#x7b;" u2="&#xe1;" k="55" />
<hkern u1="&#x7b;" u2="&#xe0;" k="55" />
<hkern u1="&#x7b;" u2="&#xd8;" k="51" />
<hkern u1="&#x7b;" u2="&#xd6;" k="51" />
<hkern u1="&#x7b;" u2="&#xd5;" k="51" />
<hkern u1="&#x7b;" u2="&#xd4;" k="51" />
<hkern u1="&#x7b;" u2="&#xd3;" k="51" />
<hkern u1="&#x7b;" u2="&#xd2;" k="51" />
<hkern u1="&#x7b;" u2="&#xc7;" k="49" />
<hkern u1="&#x7b;" u2="&#xc6;" k="43" />
<hkern u1="&#x7b;" u2="&#xc5;" k="41" />
<hkern u1="&#x7b;" u2="&#xc4;" k="41" />
<hkern u1="&#x7b;" u2="&#xc3;" k="41" />
<hkern u1="&#x7b;" u2="&#xc2;" k="41" />
<hkern u1="&#x7b;" u2="&#xc1;" k="41" />
<hkern u1="&#x7b;" u2="&#xc0;" k="41" />
<hkern u1="&#x7b;" u2="&#x7b;" k="39" />
<hkern u1="&#x7b;" u2="z" k="35" />
<hkern u1="&#x7b;" u2="y" k="51" />
<hkern u1="&#x7b;" u2="x" k="33" />
<hkern u1="&#x7b;" u2="w" k="57" />
<hkern u1="&#x7b;" u2="v" k="53" />
<hkern u1="&#x7b;" u2="u" k="61" />
<hkern u1="&#x7b;" u2="t" k="37" />
<hkern u1="&#x7b;" u2="s" k="45" />
<hkern u1="&#x7b;" u2="r" k="47" />
<hkern u1="&#x7b;" u2="q" k="68" />
<hkern u1="&#x7b;" u2="p" k="47" />
<hkern u1="&#x7b;" u2="o" k="68" />
<hkern u1="&#x7b;" u2="n" k="47" />
<hkern u1="&#x7b;" u2="m" k="47" />
<hkern u1="&#x7b;" u2="j" k="-16" />
<hkern u1="&#x7b;" u2="f" k="23" />
<hkern u1="&#x7b;" u2="e" k="68" />
<hkern u1="&#x7b;" u2="d" k="68" />
<hkern u1="&#x7b;" u2="c" k="68" />
<hkern u1="&#x7b;" u2="a" k="55" />
<hkern u1="&#x7b;" u2="S" k="23" />
<hkern u1="&#x7b;" u2="Q" k="51" />
<hkern u1="&#x7b;" u2="O" k="51" />
<hkern u1="&#x7b;" u2="G" k="51" />
<hkern u1="&#x7b;" u2="C" k="49" />
<hkern u1="&#x7b;" u2="A" k="41" />
<hkern u1="&#x7b;" u2="&#x28;" k="16" />
<hkern u1="&#x7c;" u2="&#xec;" k="-12" />
<hkern u1="&#x7d;" u2="&#x7d;" k="39" />
<hkern u1="&#x7d;" u2="]" k="43" />
<hkern u1="&#x7d;" u2="&#x29;" k="23" />
<hkern u1="&#xa1;" u2="&#x178;" k="66" />
<hkern u1="&#xa1;" u2="&#xdd;" k="66" />
<hkern u1="&#xa1;" u2="Y" k="66" />
<hkern u1="&#xa1;" u2="V" k="16" />
<hkern u1="&#xa1;" u2="T" k="98" />
<hkern u1="&#xab;" u2="V" k="39" />
<hkern u1="&#xae;" u2="&#x178;" k="47" />
<hkern u1="&#xae;" u2="&#xdd;" k="47" />
<hkern u1="&#xae;" u2="&#xc6;" k="68" />
<hkern u1="&#xae;" u2="&#xc5;" k="55" />
<hkern u1="&#xae;" u2="&#xc4;" k="55" />
<hkern u1="&#xae;" u2="&#xc3;" k="55" />
<hkern u1="&#xae;" u2="&#xc2;" k="55" />
<hkern u1="&#xae;" u2="&#xc1;" k="55" />
<hkern u1="&#xae;" u2="&#xc0;" k="55" />
<hkern u1="&#xae;" u2="Z" k="27" />
<hkern u1="&#xae;" u2="Y" k="47" />
<hkern u1="&#xae;" u2="X" k="20" />
<hkern u1="&#xae;" u2="V" k="14" />
<hkern u1="&#xae;" u2="T" k="18" />
<hkern u1="&#xae;" u2="J" k="49" />
<hkern u1="&#xae;" u2="A" k="55" />
<hkern u1="&#xb7;" u2="l" k="111" />
<hkern u1="&#xbb;" u2="&#xc6;" k="14" />
<hkern u1="&#xbb;" u2="x" k="53" />
<hkern u1="&#xbb;" u2="v" k="16" />
<hkern u1="&#xbb;" u2="f" k="8" />
<hkern u1="&#xbb;" u2="X" k="49" />
<hkern u1="&#xbb;" u2="V" k="57" />
<hkern u1="&#xbf;" u2="&#x178;" k="135" />
<hkern u1="&#xbf;" u2="&#x153;" k="68" />
<hkern u1="&#xbf;" u2="&#x152;" k="57" />
<hkern u1="&#xbf;" u2="&#xff;" k="61" />
<hkern u1="&#xbf;" u2="&#xfe;" k="63" />
<hkern u1="&#xbf;" u2="&#xfd;" k="61" />
<hkern u1="&#xbf;" u2="&#xfc;" k="63" />
<hkern u1="&#xbf;" u2="&#xfb;" k="63" />
<hkern u1="&#xbf;" u2="&#xfa;" k="63" />
<hkern u1="&#xbf;" u2="&#xf9;" k="63" />
<hkern u1="&#xbf;" u2="&#xf8;" k="68" />
<hkern u1="&#xbf;" u2="&#xf6;" k="68" />
<hkern u1="&#xbf;" u2="&#xf5;" k="68" />
<hkern u1="&#xbf;" u2="&#xf4;" k="68" />
<hkern u1="&#xbf;" u2="&#xf3;" k="68" />
<hkern u1="&#xbf;" u2="&#xf2;" k="68" />
<hkern u1="&#xbf;" u2="&#xf1;" k="63" />
<hkern u1="&#xbf;" u2="&#xf0;" k="68" />
<hkern u1="&#xbf;" u2="&#xef;" k="63" />
<hkern u1="&#xbf;" u2="&#xee;" k="63" />
<hkern u1="&#xbf;" u2="&#xed;" k="63" />
<hkern u1="&#xbf;" u2="&#xec;" k="63" />
<hkern u1="&#xbf;" u2="&#xeb;" k="68" />
<hkern u1="&#xbf;" u2="&#xea;" k="68" />
<hkern u1="&#xbf;" u2="&#xe9;" k="68" />
<hkern u1="&#xbf;" u2="&#xe8;" k="68" />
<hkern u1="&#xbf;" u2="&#xe7;" k="68" />
<hkern u1="&#xbf;" u2="&#xe6;" k="70" />
<hkern u1="&#xbf;" u2="&#xe5;" k="70" />
<hkern u1="&#xbf;" u2="&#xe4;" k="70" />
<hkern u1="&#xbf;" u2="&#xe3;" k="70" />
<hkern u1="&#xbf;" u2="&#xe2;" k="70" />
<hkern u1="&#xbf;" u2="&#xe1;" k="70" />
<hkern u1="&#xbf;" u2="&#xe0;" k="70" />
<hkern u1="&#xbf;" u2="&#xdf;" k="63" />
<hkern u1="&#xbf;" u2="&#xde;" k="57" />
<hkern u1="&#xbf;" u2="&#xdd;" k="135" />
<hkern u1="&#xbf;" u2="&#xdc;" k="59" />
<hkern u1="&#xbf;" u2="&#xdb;" k="59" />
<hkern u1="&#xbf;" u2="&#xda;" k="59" />
<hkern u1="&#xbf;" u2="&#xd9;" k="59" />
<hkern u1="&#xbf;" u2="&#xd8;" k="57" />
<hkern u1="&#xbf;" u2="&#xd6;" k="57" />
<hkern u1="&#xbf;" u2="&#xd5;" k="57" />
<hkern u1="&#xbf;" u2="&#xd4;" k="57" />
<hkern u1="&#xbf;" u2="&#xd3;" k="57" />
<hkern u1="&#xbf;" u2="&#xd2;" k="57" />
<hkern u1="&#xbf;" u2="&#xd1;" k="57" />
<hkern u1="&#xbf;" u2="&#xd0;" k="57" />
<hkern u1="&#xbf;" u2="&#xcf;" k="57" />
<hkern u1="&#xbf;" u2="&#xce;" k="57" />
<hkern u1="&#xbf;" u2="&#xcd;" k="57" />
<hkern u1="&#xbf;" u2="&#xcc;" k="57" />
<hkern u1="&#xbf;" u2="&#xcb;" k="57" />
<hkern u1="&#xbf;" u2="&#xca;" k="57" />
<hkern u1="&#xbf;" u2="&#xc9;" k="57" />
<hkern u1="&#xbf;" u2="&#xc8;" k="57" />
<hkern u1="&#xbf;" u2="&#xc7;" k="57" />
<hkern u1="&#xbf;" u2="&#xc6;" k="82" />
<hkern u1="&#xbf;" u2="&#xc5;" k="78" />
<hkern u1="&#xbf;" u2="&#xc4;" k="78" />
<hkern u1="&#xbf;" u2="&#xc3;" k="78" />
<hkern u1="&#xbf;" u2="&#xc2;" k="78" />
<hkern u1="&#xbf;" u2="&#xc1;" k="78" />
<hkern u1="&#xbf;" u2="&#xc0;" k="78" />
<hkern u1="&#xbf;" u2="z" k="59" />
<hkern u1="&#xbf;" u2="y" k="61" />
<hkern u1="&#xbf;" u2="x" k="55" />
<hkern u1="&#xbf;" u2="w" k="66" />
<hkern u1="&#xbf;" u2="v" k="66" />
<hkern u1="&#xbf;" u2="u" k="63" />
<hkern u1="&#xbf;" u2="t" k="57" />
<hkern u1="&#xbf;" u2="s" k="66" />
<hkern u1="&#xbf;" u2="r" k="63" />
<hkern u1="&#xbf;" u2="q" k="68" />
<hkern u1="&#xbf;" u2="p" k="63" />
<hkern u1="&#xbf;" u2="o" k="68" />
<hkern u1="&#xbf;" u2="n" k="63" />
<hkern u1="&#xbf;" u2="m" k="63" />
<hkern u1="&#xbf;" u2="l" k="63" />
<hkern u1="&#xbf;" u2="k" k="63" />
<hkern u1="&#xbf;" u2="j" k="63" />
<hkern u1="&#xbf;" u2="i" k="63" />
<hkern u1="&#xbf;" u2="h" k="63" />
<hkern u1="&#xbf;" u2="f" k="55" />
<hkern u1="&#xbf;" u2="e" k="68" />
<hkern u1="&#xbf;" u2="d" k="68" />
<hkern u1="&#xbf;" u2="c" k="68" />
<hkern u1="&#xbf;" u2="b" k="63" />
<hkern u1="&#xbf;" u2="a" k="70" />
<hkern u1="&#xbf;" u2="Z" k="76" />
<hkern u1="&#xbf;" u2="Y" k="135" />
<hkern u1="&#xbf;" u2="X" k="76" />
<hkern u1="&#xbf;" u2="W" k="76" />
<hkern u1="&#xbf;" u2="V" k="90" />
<hkern u1="&#xbf;" u2="U" k="59" />
<hkern u1="&#xbf;" u2="T" k="162" />
<hkern u1="&#xbf;" u2="S" k="57" />
<hkern u1="&#xbf;" u2="R" k="57" />
<hkern u1="&#xbf;" u2="Q" k="57" />
<hkern u1="&#xbf;" u2="P" k="57" />
<hkern u1="&#xbf;" u2="O" k="57" />
<hkern u1="&#xbf;" u2="N" k="57" />
<hkern u1="&#xbf;" u2="M" k="57" />
<hkern u1="&#xbf;" u2="L" k="57" />
<hkern u1="&#xbf;" u2="K" k="57" />
<hkern u1="&#xbf;" u2="J" k="27" />
<hkern u1="&#xbf;" u2="I" k="57" />
<hkern u1="&#xbf;" u2="H" k="57" />
<hkern u1="&#xbf;" u2="G" k="57" />
<hkern u1="&#xbf;" u2="F" k="57" />
<hkern u1="&#xbf;" u2="E" k="57" />
<hkern u1="&#xbf;" u2="D" k="57" />
<hkern u1="&#xbf;" u2="C" k="57" />
<hkern u1="&#xbf;" u2="B" k="57" />
<hkern u1="&#xbf;" u2="A" k="78" />
<hkern u1="&#xc0;" u2="&#x2122;" k="84" />
<hkern u1="&#xc0;" u2="&#xf0;" k="10" />
<hkern u1="&#xc0;" u2="&#xae;" k="53" />
<hkern u1="&#xc0;" u2="&#x7d;" k="41" />
<hkern u1="&#xc0;" u2="v" k="37" />
<hkern u1="&#xc0;" u2="f" k="14" />
<hkern u1="&#xc0;" u2="]" k="47" />
<hkern u1="&#xc0;" u2="\" k="96" />
<hkern u1="&#xc0;" u2="V" k="57" />
<hkern u1="&#xc0;" u2="&#x3f;" k="51" />
<hkern u1="&#xc0;" u2="&#x2a;" k="74" />
<hkern u1="&#xc1;" u2="&#x2122;" k="84" />
<hkern u1="&#xc1;" u2="&#xf0;" k="10" />
<hkern u1="&#xc1;" u2="&#xae;" k="53" />
<hkern u1="&#xc1;" u2="&#x7d;" k="41" />
<hkern u1="&#xc1;" u2="v" k="37" />
<hkern u1="&#xc1;" u2="f" k="14" />
<hkern u1="&#xc1;" u2="]" k="47" />
<hkern u1="&#xc1;" u2="\" k="96" />
<hkern u1="&#xc1;" u2="V" k="57" />
<hkern u1="&#xc1;" u2="&#x3f;" k="51" />
<hkern u1="&#xc1;" u2="&#x2a;" k="74" />
<hkern u1="&#xc2;" u2="&#x2122;" k="84" />
<hkern u1="&#xc2;" u2="&#xf0;" k="10" />
<hkern u1="&#xc2;" u2="&#xae;" k="53" />
<hkern u1="&#xc2;" u2="&#x7d;" k="41" />
<hkern u1="&#xc2;" u2="v" k="37" />
<hkern u1="&#xc2;" u2="f" k="14" />
<hkern u1="&#xc2;" u2="]" k="47" />
<hkern u1="&#xc2;" u2="\" k="96" />
<hkern u1="&#xc2;" u2="V" k="57" />
<hkern u1="&#xc2;" u2="&#x3f;" k="51" />
<hkern u1="&#xc2;" u2="&#x2a;" k="74" />
<hkern u1="&#xc3;" u2="&#x2122;" k="84" />
<hkern u1="&#xc3;" u2="&#xf0;" k="10" />
<hkern u1="&#xc3;" u2="&#xae;" k="53" />
<hkern u1="&#xc3;" u2="&#x7d;" k="41" />
<hkern u1="&#xc3;" u2="v" k="37" />
<hkern u1="&#xc3;" u2="f" k="14" />
<hkern u1="&#xc3;" u2="]" k="47" />
<hkern u1="&#xc3;" u2="\" k="96" />
<hkern u1="&#xc3;" u2="V" k="57" />
<hkern u1="&#xc3;" u2="&#x3f;" k="51" />
<hkern u1="&#xc3;" u2="&#x2a;" k="74" />
<hkern u1="&#xc4;" u2="&#x2122;" k="84" />
<hkern u1="&#xc4;" u2="&#xf0;" k="10" />
<hkern u1="&#xc4;" u2="&#xae;" k="53" />
<hkern u1="&#xc4;" u2="&#x7d;" k="41" />
<hkern u1="&#xc4;" u2="v" k="37" />
<hkern u1="&#xc4;" u2="f" k="14" />
<hkern u1="&#xc4;" u2="]" k="47" />
<hkern u1="&#xc4;" u2="\" k="96" />
<hkern u1="&#xc4;" u2="V" k="57" />
<hkern u1="&#xc4;" u2="&#x3f;" k="51" />
<hkern u1="&#xc4;" u2="&#x2a;" k="74" />
<hkern u1="&#xc5;" u2="&#x2122;" k="84" />
<hkern u1="&#xc5;" u2="&#xf0;" k="10" />
<hkern u1="&#xc5;" u2="&#xae;" k="53" />
<hkern u1="&#xc5;" u2="&#x7d;" k="41" />
<hkern u1="&#xc5;" u2="v" k="37" />
<hkern u1="&#xc5;" u2="f" k="14" />
<hkern u1="&#xc5;" u2="]" k="47" />
<hkern u1="&#xc5;" u2="\" k="96" />
<hkern u1="&#xc5;" u2="V" k="57" />
<hkern u1="&#xc5;" u2="&#x3f;" k="51" />
<hkern u1="&#xc5;" u2="&#x2a;" k="74" />
<hkern u1="&#xc6;" u2="&#xf0;" k="16" />
<hkern u1="&#xc6;" u2="&#xef;" k="-27" />
<hkern u1="&#xc6;" u2="&#xee;" k="-25" />
<hkern u1="&#xc6;" u2="&#xec;" k="-74" />
<hkern u1="&#xc6;" u2="v" k="23" />
<hkern u1="&#xc6;" u2="f" k="6" />
<hkern u1="&#xc7;" u2="&#xf0;" k="14" />
<hkern u1="&#xc7;" u2="&#xef;" k="-29" />
<hkern u1="&#xc7;" u2="&#xee;" k="-25" />
<hkern u1="&#xc7;" u2="&#xec;" k="-68" />
<hkern u1="&#xc7;" u2="&#xae;" k="27" />
<hkern u1="&#xc7;" u2="v" k="33" />
<hkern u1="&#xc7;" u2="f" k="12" />
<hkern u1="&#xc8;" u2="&#xf0;" k="16" />
<hkern u1="&#xc8;" u2="&#xef;" k="-27" />
<hkern u1="&#xc8;" u2="&#xee;" k="-25" />
<hkern u1="&#xc8;" u2="&#xec;" k="-74" />
<hkern u1="&#xc8;" u2="v" k="23" />
<hkern u1="&#xc8;" u2="f" k="6" />
<hkern u1="&#xc9;" u2="&#xf0;" k="16" />
<hkern u1="&#xc9;" u2="&#xef;" k="-27" />
<hkern u1="&#xc9;" u2="&#xee;" k="-25" />
<hkern u1="&#xc9;" u2="&#xec;" k="-74" />
<hkern u1="&#xc9;" u2="v" k="23" />
<hkern u1="&#xc9;" u2="f" k="6" />
<hkern u1="&#xca;" u2="&#xf0;" k="16" />
<hkern u1="&#xca;" u2="&#xef;" k="-27" />
<hkern u1="&#xca;" u2="&#xee;" k="-25" />
<hkern u1="&#xca;" u2="&#xec;" k="-74" />
<hkern u1="&#xca;" u2="v" k="23" />
<hkern u1="&#xca;" u2="f" k="6" />
<hkern u1="&#xcb;" u2="&#xf0;" k="16" />
<hkern u1="&#xcb;" u2="&#xef;" k="-27" />
<hkern u1="&#xcb;" u2="&#xee;" k="-25" />
<hkern u1="&#xcb;" u2="&#xec;" k="-74" />
<hkern u1="&#xcb;" u2="v" k="23" />
<hkern u1="&#xcb;" u2="f" k="6" />
<hkern u1="&#xcc;" u2="&#xf0;" k="6" />
<hkern u1="&#xcc;" u2="&#xec;" k="-6" />
<hkern u1="&#xcd;" u2="&#xf0;" k="6" />
<hkern u1="&#xcd;" u2="&#xec;" k="-6" />
<hkern u1="&#xce;" u2="&#xf0;" k="6" />
<hkern u1="&#xce;" u2="&#xec;" k="-6" />
<hkern u1="&#xcf;" u2="&#xf0;" k="6" />
<hkern u1="&#xcf;" u2="&#xec;" k="-6" />
<hkern u1="&#xd0;" u2="&#xc6;" k="35" />
<hkern u1="&#xd0;" u2="&#x7d;" k="53" />
<hkern u1="&#xd0;" u2="x" k="6" />
<hkern u1="&#xd0;" u2="]" k="59" />
<hkern u1="&#xd0;" u2="\" k="14" />
<hkern u1="&#xd0;" u2="X" k="43" />
<hkern u1="&#xd0;" u2="V" k="25" />
<hkern u1="&#xd0;" u2="&#x3f;" k="18" />
<hkern u1="&#xd0;" u2="&#x2f;" k="12" />
<hkern u1="&#xd0;" u2="&#x29;" k="25" />
<hkern u1="&#xd1;" u2="&#xf0;" k="6" />
<hkern u1="&#xd1;" u2="&#xec;" k="-6" />
<hkern u1="&#xd2;" u2="&#xc6;" k="31" />
<hkern u1="&#xd2;" u2="&#x7d;" k="51" />
<hkern u1="&#xd2;" u2="]" k="57" />
<hkern u1="&#xd2;" u2="\" k="16" />
<hkern u1="&#xd2;" u2="X" k="39" />
<hkern u1="&#xd2;" u2="V" k="25" />
<hkern u1="&#xd2;" u2="&#x3f;" k="14" />
<hkern u1="&#xd2;" u2="&#x2f;" k="12" />
<hkern u1="&#xd2;" u2="&#x29;" k="10" />
<hkern u1="&#xd3;" u2="&#xc6;" k="31" />
<hkern u1="&#xd3;" u2="&#x7d;" k="51" />
<hkern u1="&#xd3;" u2="]" k="57" />
<hkern u1="&#xd3;" u2="\" k="16" />
<hkern u1="&#xd3;" u2="X" k="39" />
<hkern u1="&#xd3;" u2="V" k="25" />
<hkern u1="&#xd3;" u2="&#x3f;" k="14" />
<hkern u1="&#xd3;" u2="&#x2f;" k="12" />
<hkern u1="&#xd3;" u2="&#x29;" k="10" />
<hkern u1="&#xd4;" u2="&#xc6;" k="31" />
<hkern u1="&#xd4;" u2="&#x7d;" k="51" />
<hkern u1="&#xd4;" u2="]" k="57" />
<hkern u1="&#xd4;" u2="\" k="16" />
<hkern u1="&#xd4;" u2="X" k="39" />
<hkern u1="&#xd4;" u2="V" k="25" />
<hkern u1="&#xd4;" u2="&#x3f;" k="14" />
<hkern u1="&#xd4;" u2="&#x2f;" k="12" />
<hkern u1="&#xd4;" u2="&#x29;" k="10" />
<hkern u1="&#xd5;" u2="&#xc6;" k="31" />
<hkern u1="&#xd5;" u2="&#x7d;" k="51" />
<hkern u1="&#xd5;" u2="]" k="57" />
<hkern u1="&#xd5;" u2="\" k="16" />
<hkern u1="&#xd5;" u2="X" k="39" />
<hkern u1="&#xd5;" u2="V" k="25" />
<hkern u1="&#xd5;" u2="&#x3f;" k="14" />
<hkern u1="&#xd5;" u2="&#x2f;" k="12" />
<hkern u1="&#xd5;" u2="&#x29;" k="10" />
<hkern u1="&#xd6;" u2="&#xc6;" k="31" />
<hkern u1="&#xd6;" u2="&#x7d;" k="51" />
<hkern u1="&#xd6;" u2="]" k="57" />
<hkern u1="&#xd6;" u2="\" k="16" />
<hkern u1="&#xd6;" u2="X" k="39" />
<hkern u1="&#xd6;" u2="V" k="25" />
<hkern u1="&#xd6;" u2="&#x3f;" k="14" />
<hkern u1="&#xd6;" u2="&#x2f;" k="12" />
<hkern u1="&#xd6;" u2="&#x29;" k="10" />
<hkern u1="&#xd8;" u2="&#xc6;" k="31" />
<hkern u1="&#xd8;" u2="&#x7d;" k="51" />
<hkern u1="&#xd8;" u2="]" k="57" />
<hkern u1="&#xd8;" u2="\" k="16" />
<hkern u1="&#xd8;" u2="X" k="39" />
<hkern u1="&#xd8;" u2="V" k="25" />
<hkern u1="&#xd8;" u2="&#x3f;" k="14" />
<hkern u1="&#xd8;" u2="&#x2f;" k="12" />
<hkern u1="&#xd8;" u2="&#x29;" k="10" />
<hkern u1="&#xd9;" u2="&#xf0;" k="6" />
<hkern u1="&#xd9;" u2="&#xec;" k="-12" />
<hkern u1="&#xd9;" u2="&#xc6;" k="18" />
<hkern u1="&#xd9;" u2="&#x2f;" k="12" />
<hkern u1="&#xda;" u2="&#xf0;" k="6" />
<hkern u1="&#xda;" u2="&#xec;" k="-12" />
<hkern u1="&#xda;" u2="&#xc6;" k="18" />
<hkern u1="&#xda;" u2="&#x2f;" k="12" />
<hkern u1="&#xdb;" u2="&#xf0;" k="6" />
<hkern u1="&#xdb;" u2="&#xec;" k="-12" />
<hkern u1="&#xdb;" u2="&#xc6;" k="18" />
<hkern u1="&#xdb;" u2="&#x2f;" k="12" />
<hkern u1="&#xdc;" u2="&#xf0;" k="6" />
<hkern u1="&#xdc;" u2="&#xec;" k="-12" />
<hkern u1="&#xdc;" u2="&#xc6;" k="18" />
<hkern u1="&#xdc;" u2="&#x2f;" k="12" />
<hkern u1="&#xdd;" u2="&#xff;" k="70" />
<hkern u1="&#xdd;" u2="&#xf0;" k="80" />
<hkern u1="&#xdd;" u2="&#xef;" k="-90" />
<hkern u1="&#xdd;" u2="&#xee;" k="-16" />
<hkern u1="&#xdd;" u2="&#xec;" k="-131" />
<hkern u1="&#xdd;" u2="&#xeb;" k="137" />
<hkern u1="&#xdd;" u2="&#xe4;" k="117" />
<hkern u1="&#xdd;" u2="&#xe3;" k="109" />
<hkern u1="&#xdd;" u2="&#xdf;" k="12" />
<hkern u1="&#xdd;" u2="&#xc6;" k="106" />
<hkern u1="&#xdd;" u2="&#xae;" k="45" />
<hkern u1="&#xdd;" u2="x" k="82" />
<hkern u1="&#xdd;" u2="v" k="84" />
<hkern u1="&#xdd;" u2="f" k="37" />
<hkern u1="&#xdd;" u2="&#x40;" k="74" />
<hkern u1="&#xdd;" u2="&#x2f;" k="131" />
<hkern u1="&#xdd;" u2="&#x26;" k="76" />
<hkern u1="&#xde;" u2="&#x2122;" k="12" />
<hkern u1="&#xde;" u2="&#x2026;" k="66" />
<hkern u1="&#xde;" u2="&#x201e;" k="66" />
<hkern u1="&#xde;" u2="&#x201a;" k="66" />
<hkern u1="&#xde;" u2="&#x178;" k="80" />
<hkern u1="&#xde;" u2="&#xdd;" k="80" />
<hkern u1="&#xde;" u2="&#xc6;" k="45" />
<hkern u1="&#xde;" u2="&#xc5;" k="37" />
<hkern u1="&#xde;" u2="&#xc4;" k="37" />
<hkern u1="&#xde;" u2="&#xc3;" k="37" />
<hkern u1="&#xde;" u2="&#xc2;" k="37" />
<hkern u1="&#xde;" u2="&#xc1;" k="37" />
<hkern u1="&#xde;" u2="&#xc0;" k="37" />
<hkern u1="&#xde;" u2="&#x7d;" k="63" />
<hkern u1="&#xde;" u2="]" k="76" />
<hkern u1="&#xde;" u2="\" k="37" />
<hkern u1="&#xde;" u2="Z" k="43" />
<hkern u1="&#xde;" u2="Y" k="80" />
<hkern u1="&#xde;" u2="X" k="82" />
<hkern u1="&#xde;" u2="W" k="12" />
<hkern u1="&#xde;" u2="V" k="31" />
<hkern u1="&#xde;" u2="T" k="100" />
<hkern u1="&#xde;" u2="J" k="47" />
<hkern u1="&#xde;" u2="A" k="37" />
<hkern u1="&#xde;" u2="&#x3f;" k="35" />
<hkern u1="&#xde;" u2="&#x2f;" k="39" />
<hkern u1="&#xde;" u2="&#x2e;" k="66" />
<hkern u1="&#xde;" u2="&#x2c;" k="66" />
<hkern u1="&#xde;" u2="&#x29;" k="31" />
<hkern u1="&#xdf;" u2="&#x2122;" k="23" />
<hkern u1="&#xdf;" u2="&#x201d;" k="29" />
<hkern u1="&#xdf;" u2="&#x201c;" k="31" />
<hkern u1="&#xdf;" u2="&#x2019;" k="29" />
<hkern u1="&#xdf;" u2="&#x2018;" k="31" />
<hkern u1="&#xdf;" u2="&#x178;" k="84" />
<hkern u1="&#xdf;" u2="&#xff;" k="35" />
<hkern u1="&#xdf;" u2="&#xfd;" k="35" />
<hkern u1="&#xdf;" u2="&#xdd;" k="84" />
<hkern u1="&#xdf;" u2="&#xc6;" k="6" />
<hkern u1="&#xdf;" u2="&#xae;" k="27" />
<hkern u1="&#xdf;" u2="&#x7d;" k="35" />
<hkern u1="&#xdf;" u2="y" k="35" />
<hkern u1="&#xdf;" u2="x" k="18" />
<hkern u1="&#xdf;" u2="w" k="23" />
<hkern u1="&#xdf;" u2="v" k="33" />
<hkern u1="&#xdf;" u2="t" k="18" />
<hkern u1="&#xdf;" u2="g" k="10" />
<hkern u1="&#xdf;" u2="f" k="10" />
<hkern u1="&#xdf;" u2="]" k="43" />
<hkern u1="&#xdf;" u2="\" k="39" />
<hkern u1="&#xdf;" u2="Y" k="84" />
<hkern u1="&#xdf;" u2="X" k="12" />
<hkern u1="&#xdf;" u2="W" k="39" />
<hkern u1="&#xdf;" u2="V" k="57" />
<hkern u1="&#xdf;" u2="T" k="63" />
<hkern u1="&#xdf;" u2="S" k="10" />
<hkern u1="&#xdf;" u2="J" k="41" />
<hkern u1="&#xdf;" u2="&#x3f;" k="18" />
<hkern u1="&#xdf;" u2="&#x2a;" k="31" />
<hkern u1="&#xdf;" u2="&#x27;" k="27" />
<hkern u1="&#xdf;" u2="&#x22;" k="27" />
<hkern u1="&#xe0;" u2="&#x2122;" k="25" />
<hkern u1="&#xe0;" u2="&#x7d;" k="18" />
<hkern u1="&#xe0;" u2="v" k="10" />
<hkern u1="&#xe0;" u2="]" k="23" />
<hkern u1="&#xe0;" u2="\" k="72" />
<hkern u1="&#xe0;" u2="V" k="51" />
<hkern u1="&#xe0;" u2="&#x3f;" k="31" />
<hkern u1="&#xe0;" u2="&#x2a;" k="6" />
<hkern u1="&#xe1;" u2="&#x2122;" k="25" />
<hkern u1="&#xe1;" u2="&#x7d;" k="18" />
<hkern u1="&#xe1;" u2="v" k="10" />
<hkern u1="&#xe1;" u2="]" k="23" />
<hkern u1="&#xe1;" u2="\" k="72" />
<hkern u1="&#xe1;" u2="V" k="51" />
<hkern u1="&#xe1;" u2="&#x3f;" k="31" />
<hkern u1="&#xe1;" u2="&#x2a;" k="6" />
<hkern u1="&#xe2;" u2="&#x2122;" k="25" />
<hkern u1="&#xe2;" u2="&#x7d;" k="18" />
<hkern u1="&#xe2;" u2="v" k="10" />
<hkern u1="&#xe2;" u2="]" k="23" />
<hkern u1="&#xe2;" u2="\" k="72" />
<hkern u1="&#xe2;" u2="V" k="51" />
<hkern u1="&#xe2;" u2="&#x3f;" k="31" />
<hkern u1="&#xe2;" u2="&#x2a;" k="6" />
<hkern u1="&#xe3;" u2="&#x2122;" k="25" />
<hkern u1="&#xe3;" u2="&#x7d;" k="18" />
<hkern u1="&#xe3;" u2="v" k="10" />
<hkern u1="&#xe3;" u2="]" k="23" />
<hkern u1="&#xe3;" u2="\" k="72" />
<hkern u1="&#xe3;" u2="V" k="51" />
<hkern u1="&#xe3;" u2="&#x3f;" k="31" />
<hkern u1="&#xe3;" u2="&#x2a;" k="6" />
<hkern u1="&#xe4;" u2="&#x2122;" k="25" />
<hkern u1="&#xe4;" u2="&#x7d;" k="18" />
<hkern u1="&#xe4;" u2="v" k="10" />
<hkern u1="&#xe4;" u2="]" k="23" />
<hkern u1="&#xe4;" u2="\" k="72" />
<hkern u1="&#xe4;" u2="V" k="51" />
<hkern u1="&#xe4;" u2="&#x3f;" k="31" />
<hkern u1="&#xe4;" u2="&#x2a;" k="6" />
<hkern u1="&#xe5;" u2="&#x2122;" k="25" />
<hkern u1="&#xe5;" u2="&#x7d;" k="18" />
<hkern u1="&#xe5;" u2="v" k="10" />
<hkern u1="&#xe5;" u2="]" k="23" />
<hkern u1="&#xe5;" u2="\" k="72" />
<hkern u1="&#xe5;" u2="V" k="51" />
<hkern u1="&#xe5;" u2="&#x3f;" k="31" />
<hkern u1="&#xe5;" u2="&#x2a;" k="6" />
<hkern u1="&#xe6;" u2="&#x2122;" k="29" />
<hkern u1="&#xe6;" u2="&#xc6;" k="10" />
<hkern u1="&#xe6;" u2="&#x7d;" k="55" />
<hkern u1="&#xe6;" u2="x" k="8" />
<hkern u1="&#xe6;" u2="v" k="16" />
<hkern u1="&#xe6;" u2="]" k="51" />
<hkern u1="&#xe6;" u2="\" k="70" />
<hkern u1="&#xe6;" u2="V" k="63" />
<hkern u1="&#xe6;" u2="&#x3f;" k="41" />
<hkern u1="&#xe6;" u2="&#x29;" k="10" />
<hkern u1="&#xe7;" u2="&#xf0;" k="23" />
<hkern u1="&#xe7;" u2="&#x7d;" k="33" />
<hkern u1="&#xe7;" u2="]" k="39" />
<hkern u1="&#xe7;" u2="\" k="20" />
<hkern u1="&#xe7;" u2="V" k="25" />
<hkern u1="&#xe7;" u2="&#x3f;" k="18" />
<hkern u1="&#xe8;" u2="&#x2122;" k="29" />
<hkern u1="&#xe8;" u2="&#xc6;" k="10" />
<hkern u1="&#xe8;" u2="&#x7d;" k="55" />
<hkern u1="&#xe8;" u2="x" k="8" />
<hkern u1="&#xe8;" u2="v" k="16" />
<hkern u1="&#xe8;" u2="]" k="51" />
<hkern u1="&#xe8;" u2="\" k="70" />
<hkern u1="&#xe8;" u2="V" k="63" />
<hkern u1="&#xe8;" u2="&#x3f;" k="41" />
<hkern u1="&#xe8;" u2="&#x29;" k="10" />
<hkern u1="&#xe9;" u2="&#x2122;" k="29" />
<hkern u1="&#xe9;" u2="&#xc6;" k="10" />
<hkern u1="&#xe9;" u2="&#x7d;" k="55" />
<hkern u1="&#xe9;" u2="x" k="8" />
<hkern u1="&#xe9;" u2="v" k="16" />
<hkern u1="&#xe9;" u2="]" k="51" />
<hkern u1="&#xe9;" u2="\" k="70" />
<hkern u1="&#xe9;" u2="V" k="63" />
<hkern u1="&#xe9;" u2="&#x3f;" k="41" />
<hkern u1="&#xe9;" u2="&#x29;" k="10" />
<hkern u1="&#xea;" u2="&#x2122;" k="29" />
<hkern u1="&#xea;" u2="&#xc6;" k="10" />
<hkern u1="&#xea;" u2="&#x7d;" k="55" />
<hkern u1="&#xea;" u2="x" k="8" />
<hkern u1="&#xea;" u2="v" k="16" />
<hkern u1="&#xea;" u2="]" k="51" />
<hkern u1="&#xea;" u2="\" k="70" />
<hkern u1="&#xea;" u2="V" k="63" />
<hkern u1="&#xea;" u2="&#x3f;" k="41" />
<hkern u1="&#xea;" u2="&#x29;" k="10" />
<hkern u1="&#xeb;" u2="&#x2122;" k="29" />
<hkern u1="&#xeb;" u2="&#xc6;" k="10" />
<hkern u1="&#xeb;" u2="&#x7d;" k="55" />
<hkern u1="&#xeb;" u2="x" k="8" />
<hkern u1="&#xeb;" u2="v" k="16" />
<hkern u1="&#xeb;" u2="]" k="51" />
<hkern u1="&#xeb;" u2="\" k="70" />
<hkern u1="&#xeb;" u2="V" k="63" />
<hkern u1="&#xeb;" u2="&#x3f;" k="41" />
<hkern u1="&#xeb;" u2="&#x29;" k="10" />
<hkern u1="&#xec;" u2="&#xef;" k="-6" />
<hkern u1="&#xec;" u2="&#xec;" k="-14" />
<hkern u1="&#xed;" u2="&#x2122;" k="-29" />
<hkern u1="&#xed;" u2="&#x201d;" k="-6" />
<hkern u1="&#xed;" u2="&#x2019;" k="-6" />
<hkern u1="&#xed;" u2="&#xfe;" k="-20" />
<hkern u1="&#xed;" u2="&#xef;" k="-6" />
<hkern u1="&#xed;" u2="&#xee;" k="-20" />
<hkern u1="&#xed;" u2="&#xed;" k="-20" />
<hkern u1="&#xed;" u2="&#xec;" k="-14" />
<hkern u1="&#xed;" u2="&#xdf;" k="-20" />
<hkern u1="&#xed;" u2="&#x7d;" k="-82" />
<hkern u1="&#xed;" u2="&#x7c;" k="-16" />
<hkern u1="&#xed;" u2="l" k="-14" />
<hkern u1="&#xed;" u2="k" k="-20" />
<hkern u1="&#xed;" u2="j" k="-20" />
<hkern u1="&#xed;" u2="i" k="-20" />
<hkern u1="&#xed;" u2="h" k="-20" />
<hkern u1="&#xed;" u2="b" k="-20" />
<hkern u1="&#xed;" u2="]" k="-82" />
<hkern u1="&#xed;" u2="\" k="-90" />
<hkern u1="&#xed;" u2="&#x3f;" k="-76" />
<hkern u1="&#xed;" u2="&#x2a;" k="-41" />
<hkern u1="&#xed;" u2="&#x29;" k="-51" />
<hkern u1="&#xed;" u2="&#x27;" k="-35" />
<hkern u1="&#xed;" u2="&#x22;" k="-35" />
<hkern u1="&#xed;" u2="&#x21;" k="-16" />
<hkern u1="&#xee;" u2="&#x2122;" k="-6" />
<hkern u1="&#xee;" u2="&#xef;" k="-6" />
<hkern u1="&#xee;" u2="&#xec;" k="-14" />
<hkern u1="&#xee;" u2="&#x3f;" k="-20" />
<hkern u1="&#xee;" u2="&#x2a;" k="-39" />
<hkern u1="&#xef;" u2="&#x2122;" k="-8" />
<hkern u1="&#xef;" u2="&#xef;" k="-6" />
<hkern u1="&#xef;" u2="&#xec;" k="-14" />
<hkern u1="&#xef;" u2="&#x7d;" k="-23" />
<hkern u1="&#xef;" u2="]" k="-23" />
<hkern u1="&#xef;" u2="\" k="-37" />
<hkern u1="&#xef;" u2="&#x3f;" k="-29" />
<hkern u1="&#xef;" u2="&#x2a;" k="-37" />
<hkern u1="&#xef;" u2="&#x29;" k="-23" />
<hkern u1="&#xf0;" u2="&#x2122;" k="14" />
<hkern u1="&#xf0;" u2="&#x2026;" k="18" />
<hkern u1="&#xf0;" u2="&#x201e;" k="18" />
<hkern u1="&#xf0;" u2="&#x201d;" k="10" />
<hkern u1="&#xf0;" u2="&#x201c;" k="10" />
<hkern u1="&#xf0;" u2="&#x201a;" k="18" />
<hkern u1="&#xf0;" u2="&#x2019;" k="10" />
<hkern u1="&#xf0;" u2="&#x2018;" k="10" />
<hkern u1="&#xf0;" u2="&#x178;" k="86" />
<hkern u1="&#xf0;" u2="&#xff;" k="14" />
<hkern u1="&#xf0;" u2="&#xfd;" k="14" />
<hkern u1="&#xf0;" u2="&#xde;" k="10" />
<hkern u1="&#xf0;" u2="&#xdd;" k="86" />
<hkern u1="&#xf0;" u2="&#xd1;" k="10" />
<hkern u1="&#xf0;" u2="&#xd0;" k="10" />
<hkern u1="&#xf0;" u2="&#xcf;" k="10" />
<hkern u1="&#xf0;" u2="&#xce;" k="10" />
<hkern u1="&#xf0;" u2="&#xcd;" k="10" />
<hkern u1="&#xf0;" u2="&#xcc;" k="10" />
<hkern u1="&#xf0;" u2="&#xcb;" k="10" />
<hkern u1="&#xf0;" u2="&#xca;" k="10" />
<hkern u1="&#xf0;" u2="&#xc9;" k="10" />
<hkern u1="&#xf0;" u2="&#xc8;" k="10" />
<hkern u1="&#xf0;" u2="&#xc6;" k="23" />
<hkern u1="&#xf0;" u2="&#xc5;" k="18" />
<hkern u1="&#xf0;" u2="&#xc4;" k="18" />
<hkern u1="&#xf0;" u2="&#xc3;" k="18" />
<hkern u1="&#xf0;" u2="&#xc2;" k="18" />
<hkern u1="&#xf0;" u2="&#xc1;" k="18" />
<hkern u1="&#xf0;" u2="&#xc0;" k="18" />
<hkern u1="&#xf0;" u2="&#x7d;" k="43" />
<hkern u1="&#xf0;" u2="y" k="14" />
<hkern u1="&#xf0;" u2="x" k="10" />
<hkern u1="&#xf0;" u2="w" k="8" />
<hkern u1="&#xf0;" u2="v" k="12" />
<hkern u1="&#xf0;" u2="]" k="49" />
<hkern u1="&#xf0;" u2="\" k="39" />
<hkern u1="&#xf0;" u2="Z" k="25" />
<hkern u1="&#xf0;" u2="Y" k="86" />
<hkern u1="&#xf0;" u2="X" k="57" />
<hkern u1="&#xf0;" u2="W" k="25" />
<hkern u1="&#xf0;" u2="V" k="43" />
<hkern u1="&#xf0;" u2="T" k="102" />
<hkern u1="&#xf0;" u2="S" k="8" />
<hkern u1="&#xf0;" u2="R" k="10" />
<hkern u1="&#xf0;" u2="P" k="10" />
<hkern u1="&#xf0;" u2="N" k="10" />
<hkern u1="&#xf0;" u2="M" k="10" />
<hkern u1="&#xf0;" u2="L" k="10" />
<hkern u1="&#xf0;" u2="K" k="10" />
<hkern u1="&#xf0;" u2="J" k="55" />
<hkern u1="&#xf0;" u2="I" k="10" />
<hkern u1="&#xf0;" u2="H" k="10" />
<hkern u1="&#xf0;" u2="F" k="10" />
<hkern u1="&#xf0;" u2="E" k="10" />
<hkern u1="&#xf0;" u2="D" k="10" />
<hkern u1="&#xf0;" u2="B" k="10" />
<hkern u1="&#xf0;" u2="A" k="18" />
<hkern u1="&#xf0;" u2="&#x3f;" k="27" />
<hkern u1="&#xf0;" u2="&#x2f;" k="10" />
<hkern u1="&#xf0;" u2="&#x2e;" k="18" />
<hkern u1="&#xf0;" u2="&#x2c;" k="18" />
<hkern u1="&#xf0;" u2="&#x29;" k="29" />
<hkern u1="&#xf1;" u2="&#x2122;" k="33" />
<hkern u1="&#xf1;" u2="&#x7d;" k="49" />
<hkern u1="&#xf1;" u2="v" k="10" />
<hkern u1="&#xf1;" u2="]" k="53" />
<hkern u1="&#xf1;" u2="\" k="74" />
<hkern u1="&#xf1;" u2="V" k="59" />
<hkern u1="&#xf1;" u2="&#x3f;" k="45" />
<hkern u1="&#xf1;" u2="&#x2a;" k="6" />
<hkern u1="&#xf1;" u2="&#x29;" k="8" />
<hkern u1="&#xf2;" u2="&#x2122;" k="31" />
<hkern u1="&#xf2;" u2="&#xc6;" k="14" />
<hkern u1="&#xf2;" u2="&#x7d;" k="68" />
<hkern u1="&#xf2;" u2="x" k="25" />
<hkern u1="&#xf2;" u2="v" k="18" />
<hkern u1="&#xf2;" u2="]" k="78" />
<hkern u1="&#xf2;" u2="\" k="76" />
<hkern u1="&#xf2;" u2="X" k="43" />
<hkern u1="&#xf2;" u2="V" k="66" />
<hkern u1="&#xf2;" u2="&#x3f;" k="47" />
<hkern u1="&#xf2;" u2="&#x2a;" k="6" />
<hkern u1="&#xf2;" u2="&#x29;" k="37" />
<hkern u1="&#xf3;" u2="&#x2122;" k="31" />
<hkern u1="&#xf3;" u2="&#xc6;" k="14" />
<hkern u1="&#xf3;" u2="&#x7d;" k="68" />
<hkern u1="&#xf3;" u2="x" k="25" />
<hkern u1="&#xf3;" u2="v" k="18" />
<hkern u1="&#xf3;" u2="]" k="78" />
<hkern u1="&#xf3;" u2="\" k="76" />
<hkern u1="&#xf3;" u2="X" k="43" />
<hkern u1="&#xf3;" u2="V" k="66" />
<hkern u1="&#xf3;" u2="&#x3f;" k="47" />
<hkern u1="&#xf3;" u2="&#x2a;" k="6" />
<hkern u1="&#xf3;" u2="&#x29;" k="37" />
<hkern u1="&#xf4;" u2="&#x2122;" k="31" />
<hkern u1="&#xf4;" u2="&#xc6;" k="14" />
<hkern u1="&#xf4;" u2="&#x7d;" k="68" />
<hkern u1="&#xf4;" u2="x" k="25" />
<hkern u1="&#xf4;" u2="v" k="18" />
<hkern u1="&#xf4;" u2="]" k="78" />
<hkern u1="&#xf4;" u2="\" k="76" />
<hkern u1="&#xf4;" u2="X" k="43" />
<hkern u1="&#xf4;" u2="V" k="66" />
<hkern u1="&#xf4;" u2="&#x3f;" k="47" />
<hkern u1="&#xf4;" u2="&#x2a;" k="6" />
<hkern u1="&#xf4;" u2="&#x29;" k="37" />
<hkern u1="&#xf5;" u2="&#x2122;" k="31" />
<hkern u1="&#xf5;" u2="&#xc6;" k="14" />
<hkern u1="&#xf5;" u2="&#x7d;" k="68" />
<hkern u1="&#xf5;" u2="x" k="25" />
<hkern u1="&#xf5;" u2="v" k="18" />
<hkern u1="&#xf5;" u2="]" k="78" />
<hkern u1="&#xf5;" u2="\" k="76" />
<hkern u1="&#xf5;" u2="X" k="43" />
<hkern u1="&#xf5;" u2="V" k="66" />
<hkern u1="&#xf5;" u2="&#x3f;" k="47" />
<hkern u1="&#xf5;" u2="&#x2a;" k="6" />
<hkern u1="&#xf5;" u2="&#x29;" k="37" />
<hkern u1="&#xf6;" u2="&#x2122;" k="31" />
<hkern u1="&#xf6;" u2="&#xc6;" k="14" />
<hkern u1="&#xf6;" u2="&#x7d;" k="68" />
<hkern u1="&#xf6;" u2="x" k="25" />
<hkern u1="&#xf6;" u2="v" k="18" />
<hkern u1="&#xf6;" u2="]" k="78" />
<hkern u1="&#xf6;" u2="\" k="76" />
<hkern u1="&#xf6;" u2="X" k="43" />
<hkern u1="&#xf6;" u2="V" k="66" />
<hkern u1="&#xf6;" u2="&#x3f;" k="47" />
<hkern u1="&#xf6;" u2="&#x2a;" k="6" />
<hkern u1="&#xf6;" u2="&#x29;" k="37" />
<hkern u1="&#xf8;" u2="&#x2122;" k="31" />
<hkern u1="&#xf8;" u2="&#xc6;" k="14" />
<hkern u1="&#xf8;" u2="&#x7d;" k="68" />
<hkern u1="&#xf8;" u2="x" k="25" />
<hkern u1="&#xf8;" u2="v" k="18" />
<hkern u1="&#xf8;" u2="]" k="78" />
<hkern u1="&#xf8;" u2="\" k="76" />
<hkern u1="&#xf8;" u2="X" k="43" />
<hkern u1="&#xf8;" u2="V" k="66" />
<hkern u1="&#xf8;" u2="&#x3f;" k="47" />
<hkern u1="&#xf8;" u2="&#x2a;" k="6" />
<hkern u1="&#xf8;" u2="&#x29;" k="37" />
<hkern u1="&#xf9;" u2="&#x2122;" k="23" />
<hkern u1="&#xf9;" u2="&#x7d;" k="47" />
<hkern u1="&#xf9;" u2="]" k="51" />
<hkern u1="&#xf9;" u2="\" k="47" />
<hkern u1="&#xf9;" u2="V" k="53" />
<hkern u1="&#xf9;" u2="&#x3f;" k="27" />
<hkern u1="&#xf9;" u2="&#x29;" k="8" />
<hkern u1="&#xfa;" u2="&#x2122;" k="23" />
<hkern u1="&#xfa;" u2="&#x7d;" k="47" />
<hkern u1="&#xfa;" u2="]" k="51" />
<hkern u1="&#xfa;" u2="\" k="47" />
<hkern u1="&#xfa;" u2="V" k="53" />
<hkern u1="&#xfa;" u2="&#x3f;" k="27" />
<hkern u1="&#xfa;" u2="&#x29;" k="8" />
<hkern u1="&#xfb;" u2="&#x2122;" k="23" />
<hkern u1="&#xfb;" u2="&#x7d;" k="47" />
<hkern u1="&#xfb;" u2="]" k="51" />
<hkern u1="&#xfb;" u2="\" k="47" />
<hkern u1="&#xfb;" u2="V" k="53" />
<hkern u1="&#xfb;" u2="&#x3f;" k="27" />
<hkern u1="&#xfb;" u2="&#x29;" k="8" />
<hkern u1="&#xfc;" u2="&#x2122;" k="23" />
<hkern u1="&#xfc;" u2="&#x7d;" k="47" />
<hkern u1="&#xfc;" u2="]" k="51" />
<hkern u1="&#xfc;" u2="\" k="47" />
<hkern u1="&#xfc;" u2="V" k="53" />
<hkern u1="&#xfc;" u2="&#x3f;" k="27" />
<hkern u1="&#xfc;" u2="&#x29;" k="8" />
<hkern u1="&#xfd;" u2="&#xf0;" k="29" />
<hkern u1="&#xfd;" u2="&#xc6;" k="43" />
<hkern u1="&#xfd;" u2="&#x7d;" k="49" />
<hkern u1="&#xfd;" u2="]" k="59" />
<hkern u1="&#xfd;" u2="\" k="14" />
<hkern u1="&#xfd;" u2="X" k="53" />
<hkern u1="&#xfd;" u2="V" k="16" />
<hkern u1="&#xfd;" u2="&#x3f;" k="18" />
<hkern u1="&#xfd;" u2="&#x2f;" k="43" />
<hkern u1="&#xfe;" u2="&#x2122;" k="35" />
<hkern u1="&#xfe;" u2="&#xc6;" k="14" />
<hkern u1="&#xfe;" u2="&#x7d;" k="68" />
<hkern u1="&#xfe;" u2="x" k="25" />
<hkern u1="&#xfe;" u2="v" k="16" />
<hkern u1="&#xfe;" u2="]" k="76" />
<hkern u1="&#xfe;" u2="\" k="74" />
<hkern u1="&#xfe;" u2="X" k="41" />
<hkern u1="&#xfe;" u2="V" k="63" />
<hkern u1="&#xfe;" u2="&#x3f;" k="51" />
<hkern u1="&#xfe;" u2="&#x2a;" k="16" />
<hkern u1="&#xfe;" u2="&#x29;" k="37" />
<hkern u1="&#xff;" u2="&#xf0;" k="29" />
<hkern u1="&#xff;" u2="&#xc6;" k="43" />
<hkern u1="&#xff;" u2="&#x7d;" k="49" />
<hkern u1="&#xff;" u2="]" k="59" />
<hkern u1="&#xff;" u2="\" k="14" />
<hkern u1="&#xff;" u2="X" k="53" />
<hkern u1="&#xff;" u2="V" k="16" />
<hkern u1="&#xff;" u2="&#x3f;" k="18" />
<hkern u1="&#xff;" u2="&#x2f;" k="43" />
<hkern u1="&#x152;" u2="&#xf0;" k="16" />
<hkern u1="&#x152;" u2="&#xef;" k="-27" />
<hkern u1="&#x152;" u2="&#xee;" k="-25" />
<hkern u1="&#x152;" u2="&#xec;" k="-74" />
<hkern u1="&#x152;" u2="v" k="23" />
<hkern u1="&#x152;" u2="f" k="6" />
<hkern u1="&#x153;" u2="&#x2122;" k="29" />
<hkern u1="&#x153;" u2="&#xc6;" k="10" />
<hkern u1="&#x153;" u2="&#x7d;" k="55" />
<hkern u1="&#x153;" u2="x" k="8" />
<hkern u1="&#x153;" u2="v" k="16" />
<hkern u1="&#x153;" u2="]" k="51" />
<hkern u1="&#x153;" u2="\" k="70" />
<hkern u1="&#x153;" u2="V" k="63" />
<hkern u1="&#x153;" u2="&#x3f;" k="41" />
<hkern u1="&#x153;" u2="&#x29;" k="10" />
<hkern u1="&#x178;" u2="&#xff;" k="70" />
<hkern u1="&#x178;" u2="&#xf0;" k="80" />
<hkern u1="&#x178;" u2="&#xef;" k="-90" />
<hkern u1="&#x178;" u2="&#xee;" k="-16" />
<hkern u1="&#x178;" u2="&#xec;" k="-131" />
<hkern u1="&#x178;" u2="&#xeb;" k="137" />
<hkern u1="&#x178;" u2="&#xe4;" k="117" />
<hkern u1="&#x178;" u2="&#xe3;" k="109" />
<hkern u1="&#x178;" u2="&#xdf;" k="12" />
<hkern u1="&#x178;" u2="&#xc6;" k="106" />
<hkern u1="&#x178;" u2="&#xae;" k="45" />
<hkern u1="&#x178;" u2="x" k="82" />
<hkern u1="&#x178;" u2="v" k="84" />
<hkern u1="&#x178;" u2="f" k="37" />
<hkern u1="&#x178;" u2="&#x40;" k="74" />
<hkern u1="&#x178;" u2="&#x2f;" k="131" />
<hkern u1="&#x178;" u2="&#x26;" k="76" />
<hkern u1="&#x2013;" u2="&#xc6;" k="35" />
<hkern u1="&#x2013;" u2="x" k="63" />
<hkern u1="&#x2013;" u2="v" k="27" />
<hkern u1="&#x2013;" u2="f" k="25" />
<hkern u1="&#x2013;" u2="X" k="80" />
<hkern u1="&#x2013;" u2="V" k="63" />
<hkern u1="&#x2014;" u2="&#xc6;" k="35" />
<hkern u1="&#x2014;" u2="x" k="63" />
<hkern u1="&#x2014;" u2="v" k="27" />
<hkern u1="&#x2014;" u2="f" k="25" />
<hkern u1="&#x2014;" u2="X" k="80" />
<hkern u1="&#x2014;" u2="V" k="63" />
<hkern u1="&#x2018;" u2="&#xf0;" k="20" />
<hkern u1="&#x2018;" u2="&#xef;" k="-29" />
<hkern u1="&#x2018;" u2="&#xee;" k="-12" />
<hkern u1="&#x2018;" u2="&#xec;" k="-63" />
<hkern u1="&#x2018;" u2="&#xc6;" k="121" />
<hkern u1="&#x2019;" u2="&#xf0;" k="20" />
<hkern u1="&#x2019;" u2="&#xef;" k="-37" />
<hkern u1="&#x2019;" u2="&#xee;" k="-8" />
<hkern u1="&#x2019;" u2="&#xec;" k="-72" />
<hkern u1="&#x2019;" u2="&#xc6;" k="125" />
<hkern u1="&#x2019;" u2="&#x40;" k="49" />
<hkern u1="&#x2019;" u2="&#x2f;" k="145" />
<hkern u1="&#x2019;" u2="&#x26;" k="63" />
<hkern u1="&#x201a;" u2="v" k="72" />
<hkern u1="&#x201a;" u2="f" k="20" />
<hkern u1="&#x201a;" u2="V" k="109" />
<hkern u1="&#x201c;" u2="&#xf0;" k="20" />
<hkern u1="&#x201c;" u2="&#xef;" k="-29" />
<hkern u1="&#x201c;" u2="&#xee;" k="-12" />
<hkern u1="&#x201c;" u2="&#xec;" k="-63" />
<hkern u1="&#x201c;" u2="&#xc6;" k="121" />
<hkern u1="&#x201d;" u2="&#xf0;" k="20" />
<hkern u1="&#x201d;" u2="&#xef;" k="-37" />
<hkern u1="&#x201d;" u2="&#xee;" k="-8" />
<hkern u1="&#x201d;" u2="&#xec;" k="-72" />
<hkern u1="&#x201d;" u2="&#xc6;" k="125" />
<hkern u1="&#x201d;" u2="&#x40;" k="49" />
<hkern u1="&#x201d;" u2="&#x2f;" k="145" />
<hkern u1="&#x201d;" u2="&#x26;" k="63" />
<hkern u1="&#x201e;" u2="v" k="72" />
<hkern u1="&#x201e;" u2="f" k="20" />
<hkern u1="&#x201e;" u2="V" k="109" />
<hkern u1="&#x2039;" u2="V" k="39" />
<hkern u1="&#x203a;" u2="&#xc6;" k="14" />
<hkern u1="&#x203a;" u2="x" k="53" />
<hkern u1="&#x203a;" u2="v" k="16" />
<hkern u1="&#x203a;" u2="f" k="8" />
<hkern u1="&#x203a;" u2="X" k="49" />
<hkern u1="&#x203a;" u2="V" k="57" />
<hkern u1="&#x2122;" u2="&#xef;" k="-29" />
<hkern u1="&#x2122;" u2="&#xee;" k="-37" />
<hkern u1="&#x2122;" u2="&#xec;" k="-37" />
<hkern u1="&#x2122;" u2="&#xc6;" k="72" />
<hkern u1="&#x2122;" u2="&#xc5;" k="57" />
<hkern u1="&#x2122;" u2="&#xc4;" k="57" />
<hkern u1="&#x2122;" u2="&#xc3;" k="57" />
<hkern u1="&#x2122;" u2="&#xc2;" k="57" />
<hkern u1="&#x2122;" u2="&#xc1;" k="57" />
<hkern u1="&#x2122;" u2="&#xc0;" k="57" />
<hkern u1="&#x2122;" u2="Z" k="10" />
<hkern u1="&#x2122;" u2="J" k="45" />
<hkern u1="&#x2122;" u2="A" k="57" />
<hkern g1="C,Ccedilla" 	g2="C,Ccedilla" 	k="16" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="8" />
<hkern g1="C,Ccedilla" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="C,Ccedilla" 	g2="d,q" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="hyphen,endash,emdash" 	k="63" />
<hkern g1="C,Ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="33" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="m,n,p,r,ntilde" 	k="8" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="23" />
<hkern g1="D,Eth" 	g2="J" 	k="41" />
<hkern g1="D,Eth" 	g2="T" 	k="47" />
<hkern g1="D,Eth" 	g2="W" 	k="6" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="59" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="D,Eth" 	g2="Z" 	k="14" />
<hkern g1="D,Eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,Ccedilla" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="S" 	k="6" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d,q" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="hyphen,endash,emdash" 	k="43" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="G" 	g2="J" 	k="20" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="35" />
<hkern g1="G" 	g2="g" 	k="12" />
<hkern g1="G" 	g2="t" 	k="10" />
<hkern g1="G" 	g2="w" 	k="14" />
<hkern g1="G" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="J" 	k="12" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="d,q" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="g" 	k="20" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="J" 	g2="d,q" 	k="10" />
<hkern g1="J" 	g2="g" 	k="16" />
<hkern g1="J" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="K" 	g2="C,Ccedilla" 	k="31" />
<hkern g1="K" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="35" />
<hkern g1="K" 	g2="d,q" 	k="29" />
<hkern g1="K" 	g2="g" 	k="25" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="K" 	g2="hyphen,endash,emdash" 	k="74" />
<hkern g1="K" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="K" 	g2="t" 	k="23" />
<hkern g1="K" 	g2="w" 	k="51" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="K" 	g2="m,n,p,r,ntilde" 	k="6" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="27" />
<hkern g1="L" 	g2="C,Ccedilla" 	k="33" />
<hkern g1="L" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="L" 	g2="T" 	k="195" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="27" />
<hkern g1="L" 	g2="W" 	k="92" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="182" />
<hkern g1="L" 	g2="d,q" 	k="10" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="80" />
<hkern g1="L" 	g2="hyphen,endash,emdash" 	k="150" />
<hkern g1="L" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="184" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="182" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="184" />
<hkern g1="L" 	g2="t" 	k="37" />
<hkern g1="L" 	g2="w" 	k="72" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="94" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="29" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="39" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="41" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="6" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="57" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="25" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="10" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="23" />
<hkern g1="R" 	g2="J" 	k="10" />
<hkern g1="R" 	g2="T" 	k="20" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="43" />
<hkern g1="R" 	g2="d,q" 	k="18" />
<hkern g1="R" 	g2="g" 	k="16" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="29" />
<hkern g1="R" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="R" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="6" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="S" 	g2="J" 	k="29" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="S" 	g2="g" 	k="16" />
<hkern g1="S" 	g2="t" 	k="18" />
<hkern g1="S" 	g2="w" 	k="18" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="S" 	g2="z" 	k="6" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="29" />
<hkern g1="T" 	g2="J" 	k="45" />
<hkern g1="T" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="T" 	g2="d,q" 	k="184" />
<hkern g1="T" 	g2="g" 	k="207" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="125" />
<hkern g1="T" 	g2="hyphen,endash,emdash" 	k="131" />
<hkern g1="T" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="186" />
<hkern g1="T" 	g2="t" 	k="53" />
<hkern g1="T" 	g2="w" 	k="141" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="139" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="119" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="176" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="178" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="96" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="129" />
<hkern g1="T" 	g2="z" 	k="176" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="182" />
<hkern g1="T" 	g2="b,thorn" 	k="14" />
<hkern g1="T" 	g2="colon,semicolon" 	k="113" />
<hkern g1="T" 	g2="s" 	k="180" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="33" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="d,q" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="g" 	k="23" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="25" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="10" />
<hkern g1="W" 	g2="J" 	k="39" />
<hkern g1="W" 	g2="d,q" 	k="41" />
<hkern g1="W" 	g2="g" 	k="49" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="W" 	g2="hyphen,endash,emdash" 	k="39" />
<hkern g1="W" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="31" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="45" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="74" />
<hkern g1="W" 	g2="z" 	k="6" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="W" 	g2="s" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,Ccedilla" 	k="55" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="59" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d,q" 	k="145" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="135" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,endash,emdash" 	k="145" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="35" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="86" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="125" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="121" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="98" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="152" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="98" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="88" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="143" />
<hkern g1="Z" 	g2="C,Ccedilla" 	k="10" />
<hkern g1="Z" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="Z" 	g2="d,q" 	k="23" />
<hkern g1="Z" 	g2="g" 	k="31" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="33" />
<hkern g1="Z" 	g2="hyphen,endash,emdash" 	k="66" />
<hkern g1="Z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="Z" 	g2="t" 	k="8" />
<hkern g1="Z" 	g2="w" 	k="27" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="Z" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="Z" 	g2="m,n,p,r,ntilde" 	k="12" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="23" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="T" 	k="162" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="8" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="W" 	k="29" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="Y,Yacute,Ydieresis" 	k="129" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle" 	k="8" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="b,p,thorn" 	g2="J" 	k="53" />
<hkern g1="b,p,thorn" 	g2="S" 	k="14" />
<hkern g1="b,p,thorn" 	g2="T" 	k="186" />
<hkern g1="b,p,thorn" 	g2="W" 	k="39" />
<hkern g1="b,p,thorn" 	g2="Y,Yacute,Ydieresis" 	k="139" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="33" />
<hkern g1="b,p,thorn" 	g2="t" 	k="6" />
<hkern g1="b,p,thorn" 	g2="w" 	k="10" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="b,p,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="12" />
<hkern g1="b,p,thorn" 	g2="Z" 	k="16" />
<hkern g1="b,p,thorn" 	g2="z" 	k="6" />
<hkern g1="b,p,thorn" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="10" />
<hkern g1="c,ccedilla" 	g2="J" 	k="14" />
<hkern g1="c,ccedilla" 	g2="T" 	k="209" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="100" />
<hkern g1="c,ccedilla" 	g2="d,q" 	k="10" />
<hkern g1="c,ccedilla" 	g2="g" 	k="10" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="63" />
<hkern g1="c,ccedilla" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="c,ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="c,ccedilla" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="colon,semicolon" 	g2="T" 	k="113" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="88" />
<hkern g1="d" 	g2="J" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="J" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="S" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="188" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="172" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="27" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="6" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Z" 	k="10" />
<hkern g1="g" 	g2="T" 	k="150" />
<hkern g1="g" 	g2="Y,Yacute,Ydieresis" 	k="66" />
<hkern g1="g" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="g" 	g2="hyphen,endash,emdash" 	k="25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="37" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="121" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="104" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quoteright,quotedblright" 	k="72" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quotedbl,quotesingle" 	k="76" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="39" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="125" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="35" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="135" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="125" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="133" />
<hkern g1="guillemotright,guilsinglright" 	g2="t" 	k="14" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="12" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="23" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="53" />
<hkern g1="hyphen,endash,emdash" 	g2="J" 	k="59" />
<hkern g1="hyphen,endash,emdash" 	g2="S" 	k="55" />
<hkern g1="hyphen,endash,emdash" 	g2="T" 	k="131" />
<hkern g1="hyphen,endash,emdash" 	g2="W" 	k="39" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="145" />
<hkern g1="hyphen,endash,emdash" 	g2="quoteright,quotedblright" 	k="141" />
<hkern g1="hyphen,endash,emdash" 	g2="quotedbl,quotesingle" 	k="152" />
<hkern g1="hyphen,endash,emdash" 	g2="t" 	k="31" />
<hkern g1="hyphen,endash,emdash" 	g2="w" 	k="16" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="Z" 	k="57" />
<hkern g1="hyphen,endash,emdash" 	g2="z" 	k="76" />
<hkern g1="hyphen,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis" 	g2="J" 	k="35" />
<hkern g1="k" 	g2="C,Ccedilla" 	k="6" />
<hkern g1="k" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="8" />
<hkern g1="k" 	g2="T" 	k="154" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="84" />
<hkern g1="k" 	g2="d,q" 	k="25" />
<hkern g1="k" 	g2="g" 	k="20" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="55" />
<hkern g1="k" 	g2="hyphen,endash,emdash" 	k="74" />
<hkern g1="k" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="6" />
<hkern g1="l" 	g2="J" 	k="35" />
<hkern g1="h,m,n,ntilde" 	g2="J" 	k="37" />
<hkern g1="h,m,n,ntilde" 	g2="S" 	k="12" />
<hkern g1="h,m,n,ntilde" 	g2="T" 	k="195" />
<hkern g1="h,m,n,ntilde" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="12" />
<hkern g1="h,m,n,ntilde" 	g2="W" 	k="37" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="141" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="25" />
<hkern g1="h,m,n,ntilde" 	g2="t" 	k="6" />
<hkern g1="h,m,n,ntilde" 	g2="w" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="12" />
<hkern g1="h,m,n,ntilde" 	g2="Z" 	k="6" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="J" 	k="53" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="S" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="T" 	k="190" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="W" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Y,Yacute,Ydieresis" 	k="143" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="23" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="t" 	k="8" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Z" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="6" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="C,Ccedilla" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="T" 	k="131" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="W" 	k="74" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="152" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="hyphen,endash,emdash" 	k="70" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteleft,quotedblleft" 	k="291" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="295" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="301" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="t" 	k="39" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="w" 	k="57" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis" 	k="74" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="45" />
<hkern g1="quoteleft,quotedblleft" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="35" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="100" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="311" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="29" />
<hkern g1="quoteright,quotedblright" 	g2="C,Ccedilla" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="45" />
<hkern g1="quoteright,quotedblright" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="d,q" 	k="70" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="43" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="162" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,endash,emdash" 	k="168" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotright,guilsinglright" 	k="94" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde" 	k="6" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="322" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="colon,semicolon" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="35" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="47" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="g" 	k="25" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="135" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,endash,emdash" 	k="154" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotright,guilsinglright" 	k="76" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="88" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="303" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="10" />
<hkern g1="r" 	g2="J" 	k="57" />
<hkern g1="r" 	g2="T" 	k="141" />
<hkern g1="r" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="r" 	g2="d,q" 	k="27" />
<hkern g1="r" 	g2="g" 	k="14" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="86" />
<hkern g1="r" 	g2="hyphen,endash,emdash" 	k="94" />
<hkern g1="r" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="45" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="68" />
<hkern g1="r" 	g2="Z" 	k="33" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="113" />
<hkern g1="s" 	g2="J" 	k="45" />
<hkern g1="s" 	g2="T" 	k="182" />
<hkern g1="s" 	g2="W" 	k="25" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="115" />
<hkern g1="s" 	g2="g" 	k="10" />
<hkern g1="s" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="s" 	g2="w" 	k="8" />
<hkern g1="s" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="t" 	g2="T" 	k="154" />
<hkern g1="t" 	g2="Y,Yacute,Ydieresis" 	k="59" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="t" 	g2="hyphen,endash,emdash" 	k="47" />
<hkern g1="t" 	g2="guillemotright,guilsinglright" 	k="35" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="J" 	k="35" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="178" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="31" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="125" />
<hkern g1="w" 	g2="J" 	k="53" />
<hkern g1="w" 	g2="T" 	k="141" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="w" 	g2="d,q" 	k="10" />
<hkern g1="w" 	g2="g" 	k="14" />
<hkern g1="w" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="w" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="w" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="33" />
<hkern g1="w" 	g2="Z" 	k="33" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="57" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="w" 	g2="s" 	k="8" />
<hkern g1="y,yacute,ydieresis" 	g2="J" 	k="53" />
<hkern g1="y,yacute,ydieresis" 	g2="T" 	k="137" />
<hkern g1="y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="84" />
<hkern g1="y,yacute,ydieresis" 	g2="d,q" 	k="18" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="25" />
<hkern g1="y,yacute,ydieresis" 	g2="hyphen,endash,emdash" 	k="29" />
<hkern g1="y,yacute,ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="y,yacute,ydieresis" 	g2="Z" 	k="33" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="74" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="18" />
<hkern g1="y,yacute,ydieresis" 	g2="s" 	k="14" />
<hkern g1="z" 	g2="J" 	k="14" />
<hkern g1="z" 	g2="T" 	k="176" />
<hkern g1="z" 	g2="W" 	k="12" />
<hkern g1="z" 	g2="Y,Yacute,Ydieresis" 	k="104" />
<hkern g1="z" 	g2="d,q" 	k="8" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="53" />
<hkern g1="z" 	g2="hyphen,endash,emdash" 	k="66" />
<hkern g1="z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="6" />
</font>
</defs></svg> 