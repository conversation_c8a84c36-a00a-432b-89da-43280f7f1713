// ********************** Condition Data Element Value Inferface Overview ***********************
// *** The value portion of each parameterized condition is dependant on a custom 
// *** interface.  The values are submitted and loaded using a string.  The expected string 
// *** formats are as follows:
// ***   Basic (Non-date):  value1@~@value2
// ***   Specific Date: 	dateType1@#@dateValue1@~@dateType2@#@dateValue2
// ***   Delta Date:
// ***		dateType1@#@operator1@#@value1@#@delta1@~@dateType2@#@operator2@#@value2@#@delta2
// *** Note: Date formats can be mixed.  For example a date may have a SpecificDate first 
// ***       component followed by a Delta Date second component.  The secondary component of
// ***       any date is optional.  In such instances no rangeSeparator is required
// ***       For example, the simple Specific Date 'dateType1@#@dateValue1'.
// **********************************************************************************************

// Set Global Variables
// dataElementValuesString separators
var rangeSeparator = "@~@";
var valueSeparator = "@#@";
// Set ID to indicate Data Element Variable range (i.e. Between)
var rangeIndicatorId = '17';
// Set ID to indicate Date Element type
var dateDataElementId = '600';
// Set ID to indicate SpecificDate type
var specificDateId = '15';
var specificDateValue = 'SpecificDate';
var simpleType;
var simpleRangeType;
var dateType;
var dateRangeType;

$( function() {
	$('#link_Save,#link_Continue,#link_Previous').each(function(){
		var flowHref = $(this).attr('href').split(':');
		$(this).attr('href', flowHref[0] + ":onTargetingSubmit();" + flowHref[1]);
	});
});

// Init data element inputs on load
function initParameterizedInterface() {
	var inputElements = document.getElementsByTagName('input');
	for (var i=0; i<inputElements.length; i++) {
		if (inputElements[i].id.indexOf('conditionValueString_') != -1) {
			var currentId = getConditionId('conditionValueString_', inputElements[i]);
			if (document.getElementById('isStaticElement_'+currentId) != null && document.getElementById('isStaticElement_'+currentId).value == "true") {
				setStaticDisplayValue( currentId );
			} else {
				setDataElementValues( currentId );
			}
		}
	}
	
	initDatepickerAccess();
}

function initTargetingReference() {
	simpleType = document.getElementById('paramSimple').value;
	simpleRangeType = document.getElementById('paramSimpleRange').value;
	dateType = document.getElementById('paramDate').value;
	dateRangeType = document.getElementById('paramDateRange').value;
}

function initDatepickerAccess() {
	$('.datePicker').each( 
		function() {
			if (this.disabled)
				$(this).datepicker('disable');
			else
				$(this).datepicker('enable');
		}
	);
}

// Reveal/Hide Date Data Element inputs depending on dateType value (i.e. 'SpecificDate')
function dateTypeChanged(dateTypeSelect,indicator) {
	var conditionId = getConditionId("dateType"+indicator+"_", dateTypeSelect);
	var dateTypeValue = dateTypeSelect.options[dateTypeSelect.selectedIndex].id;
	
	if (dateTypeValue == specificDateId) {
		document.getElementById("specificDate"+indicator+"_"+conditionId).style.display = "";
		document.getElementById("operatorSelect"+indicator+"_"+conditionId).style.display = "none";
		document.getElementById("dateDeltaValue"+indicator+"_"+conditionId).style.display = "none";
		document.getElementById("dateDeltaUnit"+indicator+"_"+conditionId).style.display = "none";
	} else {
		document.getElementById("specificDate"+indicator+"_"+conditionId).style.display = "none";
		document.getElementById("operatorSelect"+indicator+"_"+conditionId).style.display = "";
		document.getElementById("dateDeltaValue"+indicator+"_"+conditionId).style.display = "";
		document.getElementById("dateDeltaUnit"+indicator+"_"+conditionId).style.display = "";
	}
	// Hide all filter info icons
	$(".infoIcon").hide();
}

// Get Condition ID from an element id (Expected format prefix+conditionId)
function getConditionId(prefix, element) {
	return element.id.substr( element.id.indexOf(prefix) + prefix.length, element.id.length );
}

// Generate 'dataElementValueString' values for each condition
function onTargetingSubmit() {
	var inputElements = document.getElementsByTagName('input');
	for (var i=0; i<inputElements.length; i++) {
		if (inputElements[i].id.indexOf('conditionValueString_') != -1) {
			var currentId = getConditionId('conditionValueString_', inputElements[i]);
			if (document.getElementById('isStaticElement_'+currentId) != null) {
				if (document.getElementById('isStaticElement_'+currentId).value == "false")
					generateDataElementValuesString( currentId );
			} else {
				generateDataElementValuesString( currentId );
			}
		}
	}
}

// Get 'id' attribute of an option from a select input
function getSelectedOptionId(prefix, conditionId) {
	var element = document.getElementById(prefix+conditionId);
	return element.options[element.selectedIndex].id;
}
// Get 'value' attribute of an option from a select input
function getSelectedOptionValue(prefix, conditionId) {
	var element = document.getElementById(prefix+conditionId);
	return element.options[element.selectedIndex].value;
}
// Get display value of an option from a select input
function getSelectedOptionText(prefix, conditionId) {
	var element = document.getElementById(prefix+conditionId);
	return element.options[element.selectedIndex].innerHTML;
}

// *** START Data Element Value Input Setter ***
// Parse values from 'dataElementValuesString' and set associated input values.  
// See introduction at top of this script for string format details.
function setDataElementValues(conditionId) {
	var dataElementValueString = document.getElementById('conditionValueString_'+conditionId).value;
	var primaryValue = dataElementValueString.split(rangeSeparator)[0];
	var secondaryValue = dataElementValueString.split(rangeSeparator)[1];
	
	if ( document.getElementById('paramType_'+conditionId) == null )
		return;
	
	var paramType = document.getElementById('paramType_'+conditionId).value;
	
	// Parameterized or Default value init
	if (secondaryValue == null) 
		secondaryValue = '';
	
	// If: Date Type
	if (paramType == dateType || paramType == dateRangeType) {
		var dateTypeValue1 = primaryValue.split(valueSeparator)[0];
		
		if ( dateTypeValue1 != '' ) {
			setSelectInput('dateType1_',conditionId,dateTypeValue1);
			
			if (dateTypeValue1 == specificDateId || dateTypeValue1 == specificDateValue) {
				defaultToSpecificDate('dateType1_'+conditionId,'1');
				if (primaryValue.split(valueSeparator)[1] != '') {
					var myDate = new Date(primaryValue.split(valueSeparator)[1]);
					var dateValue = $.datepicker.formatDate('M, dd yy', myDate);
					document.getElementById('datePicker1_'+conditionId).value = dateValue;
				}
			} else {
				document.getElementById('specificDate1_'+conditionId).style.display = 'none';
				setSelectInput("operatorSelect1_",conditionId,primaryValue.split(valueSeparator)[1]);
				if (primaryValue.split(valueSeparator)[2] != null) {
					setInputValue("dateDeltaValue1_",conditionId,primaryValue.split(valueSeparator)[2]);
				}	
				setSelectInput("dateDeltaUnit1_",conditionId,primaryValue.split(valueSeparator)[3]);
			}
		} else {
			defaultToSpecificDate('dateType1_'+conditionId,'1');
		}

		// If: Between
		if (paramType == dateRangeType) {
			var dateTypeValue2 = secondaryValue.split(valueSeparator)[0];
			
			if ( dateTypeValue2 != '' ) {
				setSelectInput('dateType2_',conditionId,dateTypeValue2);
			} else {
				defaultToSpecificDate('dateType2_'+conditionId,'2');
				return;
			}
			
			if (dateTypeValue2 == specificDateId || dateTypeValue2 == specificDateValue) {
				defaultToSpecificDate('dateType2_'+conditionId,'2');
				if (secondaryValue.split(valueSeparator)[1] != '') {
					var myDate = new Date(secondaryValue.split(valueSeparator)[1]);
					var dateValue = $.datepicker.formatDate('M, dd yy', myDate);
					document.getElementById('datePicker2_'+conditionId).value = dateValue;
				}
			} else {
				document.getElementById('specificDate2_'+conditionId).style.display = 'none';
				setSelectInput("operatorSelect2_",conditionId,secondaryValue.split(valueSeparator)[1]);
				if (secondaryValue.split(valueSeparator)[2] != null) {		
					setInputValue("dateDeltaValue2_",conditionId,secondaryValue.split(valueSeparator)[2]);
				}
				setSelectInput("dateDeltaUnit2_",conditionId,secondaryValue.split(valueSeparator)[3]);
			}
		}
	// Else: Simple Type
	} else {
		document.getElementById("simpleComponent1_"+conditionId).value = primaryValue;
		// If: Between
		if (paramType == simpleRangeType) {
			document.getElementById("simpleComponent2_"+conditionId).value = secondaryValue;
		}		
	}
}

function setStaticDisplayValue(conditionId) {
	var dataElementValueString = document.getElementById('conditionValueString_'+conditionId).value;
	var primaryValue = dataElementValueString.split(rangeSeparator)[0];
	var secondaryValue = dataElementValueString.split(rangeSeparator)[1];
	var paramType = document.getElementById('paramType_'+conditionId).value;
	var displayString = '';
	
	// Parameterized or Default value init
	if (secondaryValue == null) 
		secondaryValue = '';
	
	// If: Date Type
	if (paramType == dateType || paramType == dateRangeType) {
		var dateTypeValue1 = primaryValue.split(valueSeparator)[0];
		if ( dateTypeValue1 != '' ) {
			if (dateTypeValue1 == specificDateId || dateTypeValue1 == specificDateValue) {
				if (primaryValue.split(valueSeparator)[1] != '') {
					var myDate = new Date(primaryValue.split(valueSeparator)[1]);
					var dateValue = $.datepicker.formatDate('M, dd yy', myDate);
					displayString += dateValue + ' ';
				}
			} else {
				displayString += dateTypeValue1 + ' ';
				displayString += primaryValue.split(valueSeparator)[1] + ' ';
				displayString += primaryValue.split(valueSeparator)[2] + ' ';
				displayString +=  getTimeIndicator(primaryValue.split(valueSeparator)[3]) + ' ';
			}
		}

		// If: Between
		if (paramType == dateRangeType) {
			var dateTypeValue2 = secondaryValue.split(valueSeparator)[0];
			displayString += '<i>and<i>&nbsp;';
			if ( dateTypeValue2 != '' ) {
				if (dateTypeValue2 == specificDateId || dateTypeValue2 == specificDateValue) {
					if (secondaryValue.split(valueSeparator)[1] != '') {
						var myDate = new Date(secondaryValue.split(valueSeparator)[1]);
						var dateValue = $.datepicker.formatDate('M, dd yy', myDate);
						displayString += dateValue;
					}
				} else {
					displayString += dateTypeValue2 + ' ';
					displayString += secondaryValue.split(valueSeparator)[1] + ' ';
					displayString += secondaryValue.split(valueSeparator)[2] + ' ';
					displayString += getTimeIndicator(secondaryValue.split(valueSeparator)[3]) + ' ';
				}
			}
		}
	// Else: Simple Type
	} else {
		// XSS Protection - escape &<> characters
		displayString += primaryValue.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
		// If: Between
		if (paramType == simpleRangeType) {
			displayString += '&nbsp;<i>' + client_messages.text.and + '<i>&nbsp;';
			displayString += secondaryValue.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
		}		
	}
	$('#noParamDisplay_'+conditionId).html(displayString);
}

function getTimeIndicator(unit) {
	if (unit == 'y') {
		return 'years';
	} else if (unit == 'm') {
		return 'months';
	} else if (unit == 'd') {
		return 'days';
	} else {
		return '';
	}
}

// Set Data Element Value to SpecificDate by default
// (For param date and initial date conditions)
function defaultToSpecificDate(selectId,indicator) {
	var selectElement = document.getElementById(selectId);
	for (var i=0; i<selectElement.options.length; i++)
		if (selectElement.options[i].id == specificDateId ) {
			selectElement.options[i].selected = true;
		}
	dateTypeChanged(selectElement,indicator);
}

// Set the value of a Data Element Value select input
function setSelectInput (prefix, conditionId, value) {
	var selectElement = document.getElementById(prefix+conditionId);
	if (selectElement.options == null)
		return;
		
	for (var i=0; i<selectElement.options.length; i++) 
		if (selectElement.options[i].id == value || selectElement.options[i].value== value || selectElement.options[i].innerHTML== value)
			selectElement.options[i].selected = true;
}

// Set the value of a Data Element Value input
function setInputValue (prefix, conditionId, value) {
	var selectElement = document.getElementById(prefix+conditionId);
	if (value != null)
		selectElement.value = value;
	else
		selectElement.value = '';
}
// *** END Data Element Value Input Setter ***

// *** START Data Element Value String Builder Functions ***
// Parse and build 'dataElementValuesString' value.  See introduction
// at top of this script for string format details.
function generateDataElementValuesString(conditionId) {
	var paramType = document.getElementById('paramType_'+conditionId).value;
	var primaryValue = ""; var secondaryValue = "";

	// If: Date Type
	if (paramType == dateType || paramType == dateRangeType) {
		var dateTypeValue1 = getSelectedOptionText('dateType1_',conditionId);
		var dateTypeValueId1 = getSelectedOptionId('dateType1_',conditionId);
		primaryValue += dateTypeValue1 + valueSeparator;
		if (dateTypeValueId1 == specificDateId) {
			primaryValue += getDateValue('datePicker1_',conditionId);
		} else {
			var components = getDateComponentValues(conditionId,'1');
			primaryValue += components.join(valueSeparator);
		}

		// If: Between
		if (paramType == dateRangeType) {
			var dateTypeValue2 = getSelectedOptionText('dateType2_',conditionId);
			var dateTypeValueId2 = getSelectedOptionId('dateType2_',conditionId);				
			secondaryValue += rangeSeparator + dateTypeValue2 + valueSeparator;
			if (dateTypeValueId2 == specificDateId) {
				secondaryValue += getDateValue('datePicker2_',conditionId);
			} else {
				var components = getDateComponentValues(conditionId,'2');
				secondaryValue += components.join(valueSeparator);
			}
		}
	// Else: Simple Type
	} else {
		primaryValue = document.getElementById("simpleComponent1_"+conditionId).value;
		// If: Between
		if (paramType == simpleRangeType) {
			secondaryValue = document.getElementById("simpleComponent2_"+conditionId).value;
			secondaryValue = rangeSeparator + secondaryValue;
		}		
	}

	$('#conditionValueString_'+conditionId).val(primaryValue+secondaryValue);
}

// Get value from a date picker interface
function getDateValue(prefix, conditionId) {
	var dateValue = document.getElementById(prefix+conditionId).value;
	if (dateValue != '')
		return $.datepicker.formatDate('yy/mm/dd', new Date(dateValue));
	else 
		return '';
}

// Get values for range date component [0] (+,-) [1] value [2] (d,m,y)
function getDateComponentValues(conditionId,indicator) {
	var element = new Array();
	element[0] = getSelectedOptionId("operatorSelect"+indicator+"_",conditionId);
	element[1] = document.getElementById("dateDeltaValue"+indicator+"_"+conditionId).value;
	element[2] = getSelectedOptionId("dateDeltaUnit"+indicator+"_",conditionId);
	return element;
}
// *** END Data Element Value String Builder Functions ***

function checkboxClick( checkboxType, targetGroupId, checked ) {
	var checkbox = document.getElementById(checkboxType+targetGroupId );
	var disabled = true;
	if (checked == true) {
		disabled = false;
	}
	checkbox.disabled = disabled; styleInput(checkbox);
}

$( function() {		    
	initTargetingReference();
});