'use strict';


var common = (function ($, window, document, undefined) {

    var commonObj = {};

    commonObj.getHorizontalPosition = function ($btn, $dropDown, position) {

        switch (position) {

            case "LEFT":
                break;

            case "CENTER":
                return ($btn.position().left + ($btn.outerWidth() / 2)) - ($dropDown.outerWidth() / 2) + 'px';

            case "RIGHT":
                break;

        }

    },

        commonObj.getVerticalPosition = function ($btn, $dropDown, position) {

            switch (position) {

                case "BELOW":
                    return $btn.position().top + $btn.height();
                    break;

                case "ABOVE":
                    return $btn.position().top - $btn.height();
                    break;

                default:
                    return $btn.position().top;
                    break;

            }

        },

        commonObj.toggleDisableElement = function ($element) {

            if (this.isDisabled($element)) {

                this.enableElement($element);

            } else {

                this.disableElement($element);

            }

        },

        commonObj.enableElement = function ($element) {

            if (this.isInputElement($element))
                $element.prop('disabled', false);

            else
                $element.attr('aria-disabled', false).removeClass('disabled');

        },

        commonObj.disableElement = function ($element) {

            if (this.isInputElement($element))
                $element.prop('disabled', true);

            else
                $element.attr('aria-disabled', true).addClass('disabled');

        },

        commonObj.isDisabled = function ($element) {

            return ($element.attr('aria-disabled') ? true : false)

        },

        commonObj.isInputElement = function ($element) {

            if ($element.is('button') ||
                $element.is('fieldset') ||
                $element.is('input') ||
                $element.is('keygen') ||
                $element.is('optgroup') ||
                $element.is('option') ||
                $element.is('select') ||
                $element.is('textarea')) {

                return true;

            }

            return false;

        },

        commonObj.makeItFocusable = function ($element) {

            $element.attr('tabindex', '0');

        },

        commonObj.makeItNonFocusable = function ($element) {

            $element.removeAttr('tabindex');

        },

        commonObj.makeItUnFocusable = function ($element) {

            $element.attr('tabindex', "-1");

        },

        commonObj.toggleTopFrameBodyClass = function (className) {

            var topBody = $('body', window.getTopFrame().document);
            topBody.toggleClass(className);

        },

        commonObj.applyTopFrameBodyClass = function (className) {

            var topBody = $('body', window.getTopFrame().document);
            if (topBody.hasClass(className)) {
                var classNameAppliedCtr = localStorage.getItem('toggleCtr_' + className);

                if (classNameAppliedCtr === null) {
                    localStorage.setItem('toggleCtr_' + className, "1");
                } else {
                    localStorage.setItem('toggleCtr_' + className, (++classNameAppliedCtr).toString());
                }
            } else {
                commonObj.toggleTopFrameBodyClass(className);
                localStorage.setItem('toggleCtr_' + className, 1);
            }
        },

        commonObj.removeTopFrameBodyClass = function (className) {
            var topBody = $('body', window.getTopFrame().document);
            var appliedCtr = localStorage.getItem('toggleCtr_' + className);

            if (appliedCtr !== null) {
                appliedCtr = parseInt(localStorage['toggleCtr_' + className]);
                localStorage.setItem('toggleCtr_' + className, (--appliedCtr).toString());

                if (appliedCtr === 0) {
                    commonObj.toggleTopFrameBodyClass(className);
                    localStorage.removeItem('toggleCtr_' + className);
                }
            } else {
                commonObj.toggleTopFrameBodyClass(className);
            }

        },

        commonObj.setIframeHeightToMatchContent = function ($iframe, containerId) {

            var iframeElement = $iframe.get(0);

            var buttonBar = getTopFrame().jQuery('.iFramePopupButtonBar'),
                containerHeight = $(iframeElement.contentWindow.document).find('#page').height();

            if(containerId !== undefined)
                containerHeight = $(iframeElement.contentWindow.document).find('#' + containerId).height();

            var newHeight = containerHeight + (buttonBar.length > 0 ? buttonBar.height() : 0);

            if (newHeight !== 0) {

                iframeElement.height = "";
                iframeElement.height = newHeight + "px";

            }

        },

        commonObj.refreshParentIframeHeight = function () {

            var scrollPos = getTopFrame().jQuery('.iFramePopupWrapper').scrollTop();

            if (window.frameElement === null) {
                return;
            }

            commonObj.setIframeHeightToMatchContent($(window.frameElement));

            getTopFrame().jQuery('.iFramePopupWrapper').scrollTop(scrollPos);

            var buttonBar = getTopFrame().jQuery('.iFramePopupButtonBar');

            if (buttonBar.length > 0) {
                var frameHeight = $(window.frameElement).height();
                if (getTopFrame().document.documentElement.clientHeight > frameHeight) {
                    buttonBar.css({
                        bottom: Math.max((getTopFrame().document.documentElement.clientHeight - frameHeight - 56 - 32) - (buttonBar.outerHeight()), 0) + 'px'
                    })
                } else {
                    buttonBar.css({
                        bottom: 0
                    })
                }
            }


        },

        commonObj.setFixedSidebar = function ($sidebar, topPadding, isInsideIframePopup) {
        console.log('setFixedSidebar1');

            if (isInsideIframePopup) {

                this.refreshParentIframeHeight();

                var $iframePopupContainer = $(window.frameElement).closest('.iFramePopupWrapper').scrollTop(0),
                    offset = $sidebar.offset(),
                    extraOffset = 88; // 56px + 32px = 88px; headerHeight + iframeMarginTop. Quick fix --> TODO Need to change this hardcoded value.

                if ( $iframePopupContainer === undefined || $iframePopupContainer.length === 0 )
                    return;

                $iframePopupContainer.on('scroll.commonFixed', function () {

                    var $container = $(this);

                    if ($container.scrollTop() > (offset.top + extraOffset)) {

                        $sidebar.stop().animate({
                            marginTop: $container.scrollTop() - offset.top - extraOffset + topPadding
                        }, 400);

                    } else {

                        $sidebar.stop().animate({
                            marginTop: 0
                        });

                    }

                });

                return;
            }

            var $content = $sidebar.next(),
                length = $content.height() - $sidebar.height() + $content.offset().top;

            $(window).on('scroll.commonFixed', function () {

                var offset = $(this).scrollTop() + topPadding,
                    height = $sidebar.height();

                // TODO: Change to animated version.

                if (offset < $content.offset().top) {

                    $sidebar.css({
                        'position': 'absolute',
                        'top': '0'
                    });

                } else if (offset > length) {

                    $sidebar.css({
                        'position': 'absolute',
                        'bottom': '0',
                        'top': 'auto'
                    });

                } else {

                    $sidebar.css({
                        'position': 'fixed',
                        'top': topPadding + 'px',
                        'height': height + 'px'
                    });

                }

            });

            _.defer(function () {
                $("html").scrollTop(0)
            });
        },

        commonObj.disableFixedSidebar = function () {

            $(window.frameElement).closest('.iFramePopupWrapper').off('scroll.commonFixed');

        },

        commonObj.setFixedTopbar = function ($topbar, topPadding) {

            var $topbarContainer = $topbar.parent(),
                $topBarContent = $topbar.children();

            $(window).scroll(function () {

                if ($(window).scrollTop() >= (($topBarContent.offset().top - topPadding) && $topbarContainer.offset().top)) {

                    $topbar.height($topBarContent.outerHeight({margin: true}));

                    $topBarContent.css({
                        'position': 'fixed',
                        'top': topPadding + 'px'
                    });

                } else {

                    $topbar.height('initial');

                    $topBarContent.css({
                        'position': 'static',
                        'top': '0'
                    });

                }

            });
        },

        commonObj.isChrome = function () {
            return /chrom(e|ium)/.test(navigator.userAgent.toLowerCase());
        },

        commonObj.fmtClientMessage = function (message, params) {
            //TO DO: Detect array; use regex for replacement
            return message.replace("{0}", params);
        },

        commonObj.isFeatureEnabled = function (featureFlag) {

            var value = "; " + document.cookie;
            var parts = value.split("; mp-feature-flag=");
            var cookie = parts.length === 2 ? parts.pop().split(";").shift() : null;

            if (cookie !== null) {
                try {
                    var features = JSON.parse(decodeURIComponent(cookie));

                    if (_.has(features, featureFlag)) {
                        return features[featureFlag] === true;
                    }
                } catch (e) {
                    return false;
                }

            }

            return false;

        },

        commonObj.log = function (message, level) {

            if (!common.isFeatureEnabled('JavaScriptLog')) {
                return;
            }

            if (_.isString(level)) {
                switch (level) {
                    case 'ERROR':
                        console.error(message);
                        break;
                    case 'WARN':
                        console.warn(message);
                        break;
                    default:
                        console.log(message);
                        break;
                }
            } else {
                console.log(message);
            }


        },

        commonObj.isIFrameAccessible = function (win) {

            try {

                win.document;

            } catch ( err ) {

                return false;

            }

            return true;

        },

        commonObj.getParentIFrame = function () {

            if (commonObj.isIFrameAccessible(window.top))
                return window.top;

            var win = window;

            while (win.parent && win.parent != null && commonObj.isIFrameAccessible(win.parent))
                win = win.parent;

            return win;

        };

        commonObj.history = {
            goBack: function () {
                window.history.back();
            },
            goForward: function () {
                window.history.forward();
            }
        };

    return commonObj;

})(window.jQuery, window, document);