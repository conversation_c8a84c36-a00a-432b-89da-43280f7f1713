function messagepointErrorModal(error, details) {
	if (window.self === window.top) {
		$('#messagepointErrorModalMessage').text(error);
		$('#messagepointErrorModalDetailsMessage').text(details);
		$('#messagepointErrorModal').modal();
	} else {
		getTopFrame().window.messagepointErrorModal(error, details);
	}

}

/**
 * The OWASP CSRFGuard Project, BSD License
 * <PERSON> (<EMAIL>), Copyright (c) 2011 
 * All rights reserved.
 * 
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 *    1. Redistributions of source code must retain the above copyright notice,
 *       this list of conditions and the following disclaimer.
 *    2. Redistributions in binary form must reproduce the above copyright
 *       notice, this list of conditions and the following disclaimer in the
 *       documentation and/or other materials provided with the distribution.
 *    3. Neither the name of OWASP nor the names of its contributors may be used
 *       to endorse or promote products derived from this software without specific
 *       prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
 * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
(function() {

	/** hook using standards based prototype **/
	function hijackStandard() {
		if (Boolean(XMLHttpRequest.prototype._open)) {
			// prevent multiple invocations of hijackStandard / prevents stackoverflow exception
			return;
		}

		XMLHttpRequest.prototype._open = XMLHttpRequest.prototype.open;
		XMLHttpRequest.prototype.open = function(method, url, async, user, pass) {
			this.url = url;
			this._open.apply(this, arguments);
		}
		
		XMLHttpRequest.prototype._send = XMLHttpRequest.prototype.send;
		XMLHttpRequest.prototype.send = function(data) {

			var _onreadystatechange = this.onloadend;

			this.onloadend = function () {
				if (_onreadystatechange !== null) {
					_onreadystatechange(event);
				}

				if (this.status === 422) {
					var jsonError = JSON.parse(this.responseText);
					messagepointErrorModal(jsonError.error, jsonError.details)
				}
			};

			if(this.onsend != null) {
				this.onsend.apply(this, arguments);
			}
			this._send.apply(this, arguments);
		}
	}

	/** ie does not properly support prototype - wrap completely **/
	function hijackExplorer() {
		var _XMLHttpRequest = window.XMLHttpRequest;
		
		function alloc_XMLHttpRequest() {
			this.base = _XMLHttpRequest ? new _XMLHttpRequest : new window.ActiveXObject("Microsoft.XMLHTTP");
		}
		
		function init_XMLHttpRequest() {
			return new alloc_XMLHttpRequest;
		}
		
		init_XMLHttpRequest.prototype = alloc_XMLHttpRequest.prototype;
		
		/** constants **/
		init_XMLHttpRequest.UNSENT = 0;
		init_XMLHttpRequest.OPENED = 1;
		init_XMLHttpRequest.HEADERS_RECEIVED = 2;
		init_XMLHttpRequest.LOADING = 3;
		init_XMLHttpRequest.DONE = 4;
		
		/** properties **/
		init_XMLHttpRequest.prototype.status = 0;
		init_XMLHttpRequest.prototype.statusText = "";
		init_XMLHttpRequest.prototype.readyState = init_XMLHttpRequest.UNSENT;
		init_XMLHttpRequest.prototype.responseText = "";
		init_XMLHttpRequest.prototype.responseXML = null;
		init_XMLHttpRequest.prototype.onsend = null;
		
		init_XMLHttpRequest.url = null;
		init_XMLHttpRequest.onreadystatechange = null;

		/** methods **/
		init_XMLHttpRequest.prototype.open = function(method, url, async, user, pass) {
			var self = this;
			this.url = url;
			
			this.base.onreadystatechange = function() {
				try { self.status = self.base.status; } catch (e) { }
				try { self.statusText = self.base.statusText; } catch (e) { }
				try { self.readyState = self.base.readyState; } catch (e) { }
				try { self.responseText = self.base.responseText; } catch(e) { }
				try { self.responseXML = self.base.responseXML; } catch(e) { }
				
				if(self.onreadystatechange != null) {
					self.onreadystatechange.apply(this, arguments);
				}
			}
			
			this.base.open(method, url, async, user, pass);
		}
		
		init_XMLHttpRequest.prototype.send = function(data) {
			if(this.onsend != null) {
				this.onsend.apply(this, arguments);
			}
			
			this.base.send(data);
		}
		
		init_XMLHttpRequest.prototype.abort = function() {
			this.base.abort();
		}
		
		init_XMLHttpRequest.prototype.getAllResponseHeaders = function() {
			return this.base.getAllResponseHeaders();
		}

		init_XMLHttpRequest.prototype.getResponseHeader = function(name) {
			return this.base.getResponseHeader(name);
		}
		
		init_XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
			return this.base.setRequestHeader(name, value);
		}
		
		/** hook **/
		window.XMLHttpRequest = init_XMLHttpRequest;
	}

	/** check if valid domain based on domainStrict **/
	function isValidDomain(current, target) {
		if(parseBoolean('%DOMAIN_STRICT_OVERRIDE%') == true){
			return true;
		}
		
		var result = false;
		
		/** check exact or subdomain match **/
		if(current == target) {
			result = true;
		} else if(parseBoolean('%DOMAIN_STRICT%') == false) {
			if(target.charAt(0) == '.') {
				result = current.endsWith(target);
			} else {
				result = current.endsWith('.' + target);
			}
		}
		
		return result;
	}

	/** determine if uri/url points to valid domain **/
	function isValidUrl(src) {
		var result = false;
		
		/** parse out domain to make sure it points to our own **/
		if(src.substring(0, 7) == "http://" || src.substring(0, 8) == "https://") {
			var token = "://";
			var index = src.indexOf(token);
			var part = src.substring(index + token.length);
			var domain = "";
			
			/** parse up to end, first slash, or anchor **/
			for(i=0; i<part.length; i++) {
				var character = part.charAt(i);
				
				if(character == '/' || character == ':' || character == '#') {
					break;
				} else {
					domain += character;
				}
			}
			
			result = isValidDomain(document.domain, domain);
			/** explicitly skip anchors **/
		} else if(src.charAt(0) == '#') {
			result = false;
			/** ensure it is a local resource without a protocol **/
		} else if(!src.startsWith("//") && (src.charAt(0) == '/' || src.indexOf(':') == -1)) {
			result = true;
		} else if ( src.split('?').length > 1 && src.split('?')[0].indexOf(':') == -1 ) {
			result = true;
		}
		
		return result;
	}

	/** Match and return true for valid messagepoint.*** production and development TLDs, false otherwise */
	function isMessagepointUrl(src) {

		// return true for all internal / relative URLs, external must start with http(s)
		if (src != null && !src.startsWith("http")) {
			return true;
		}

		var url = new URL(src);
		var domainName = url.host.substring(0, url.host.lastIndexOf("."));
		return domainName.split(".")[0].toLowerCase().endsWith(".messagepoint");
	}

	/** parse uri from url **/
	function parseUri(url) {
		var uri = "";
		var token = "://";
		var index = url.indexOf(token);
		var part = "";
		
		/**
		 * ensure to skip protocol and prepend context path for non-qualified
		 * resources (ex: "protect.html" vs
		 * "/Owasp.CsrfGuard.Test/protect.html").
		 */
		if(index > 0) {
			part = url.substring(index + token.length);
		} else if(url.charAt(0) != '/') {
			part = "%CONTEXT_PATH%/" + url;
		} else {
			part = url;
		}
		
		/** parse up to end or query string **/
		var uriContext = (index == -1);
		
		for(var i=0; i<part.length; i++) {
			var character = part.charAt(i);
			
			if(character == '/') {
				uriContext = true;
			} else if(uriContext == true && (character == '?' || character == '#')) {
				uriContext = false;
				break;
			}
			
			if(uriContext == true) {
				uri += character;
			}
		}
		
		return uri;
	}

	/** inject tokens as hidden fields into forms **/
	function injectTokenForm(form, tokenName, tokenValue, pageTokens) {
		var action = form.getAttribute("action");
		
		if(action != null && isValidUrl(action)) {
			var uri = parseUri(action);
			var hidden = document.createElement("input");
			
			hidden.setAttribute("type", "hidden");
			hidden.setAttribute("name", tokenName);
			hidden.setAttribute("value", (pageTokens[uri] != null ? pageTokens[uri] : tokenValue));
			
			form.appendChild(hidden);

			if(window.serializedObservableForm !== undefined)
				serializedObservableForm.setInitialSerialization();

		}
	}

	/** inject tokens as query string parameters into url **/
	function injectTokenAttribute(element, attr, tokenName, tokenValue, pageTokens) {

		var location = element.getAttribute(attr);

		// PRINOVA CUSTOM - Do not inject if token already exists (protection for post process token injection)
		if(location !== null && location !== "" && isValidUrl(location) && location.indexOf(tokenName) === -1) {

			// PRINOVA CUSTOM - Do not inject for CSS, Javascript or iFrame
			if (
				(element.tagName.toLowerCase() === "link" && element.getAttribute("rel").toLowerCase() === "stylesheet") ||
				element.tagName.toLowerCase() === "script" ||
				element.tagName.toLowerCase() === 'iframe' ||
				(element.tagName.toLowerCase() === 'a' && element.getAttribute("data-external-link") !== null)
			) {
				return;
			}

			var uri = parseUri(location);
			var value = ((pageTokens[uri] !== null && typeof pageTokens[uri] !== 'undefined') ? pageTokens[uri] : tokenValue);
			
			if(location.indexOf('?') !== -1) {
				location = location + '&' + tokenName + '=' + value;
			} else {
				location = location + '?' + tokenName + '=' + value;
			}

			try {
				element.setAttribute(attr, location);
			} catch (e) {
				// attempted to set/update unsupported attribute
			}
		}
	}

	/** inject csrf prevention tokens throughout dom **/
	function injectTokens(tokenName, tokenValue) {
		/** obtain reference to page tokens if enabled **/
		var pageTokens = {};
		
		if(parseBoolean('%TOKENS_PER_PAGE%') == true) {
			pageTokens = requestPageTokens();
		}
		
		/** iterate over all elements and injection token **/
		var all = document.all ? document.all : document.getElementsByTagName('*');
		var len = all.length;

		for(var i=0; i<len; i++) {
			var element = all[i];
			
			/** inject into form **/
			if(element.tagName.toLowerCase() == "form") {
				if(parseBoolean('%INJECT_FORMS%') == true) {
					injectTokenForm(element, tokenName, tokenValue, pageTokens);
					injectTokenAttribute(element, "action", tokenName, tokenValue, pageTokens);
				}
				/** inject into attribute **/
			} else if(parseBoolean('%INJECT_ATTRIBUTES%') == true) {
				injectTokenAttribute(element, "src", tokenName, tokenValue, pageTokens);
				injectTokenAttribute(element, "href", tokenName, tokenValue, pageTokens);
			}
		}
	}
	
	// PRINOVA CUSTOM: On demand token inject for objects generated async
	function injectTokensForObject(obj) {
		/** obtain reference to page tokens if enabled **/
		var pageTokens = {};
		
		if(parseBoolean('%TOKENS_PER_PAGE%') == true) {
			pageTokens = requestPageTokens();
		}
		
		/** iterate over all elements and injection token **/
		var objElements = obj.all ? obj.all : obj.getElementsByTagName('*');
		var len = objElements.length;

		for(var i=0; i<len; i++) {
			var element = objElements[i];
			
			if(parseBoolean('%INJECT_ATTRIBUTES%') == true) {
				injectTokenAttribute(element, "src", "%TOKEN_NAME%", "%TOKEN_VALUE%", pageTokens);
				injectTokenAttribute(element, "href", "%TOKEN_NAME%", "%TOKEN_VALUE%", pageTokens);
			}
		}
	}
	
	// PRINOVA CUSTOM: On demand token inject for page redirects
	function injectTokenAndRedirect(url) {
		if (isViewPdf(url)) {
			viewPdf(url);
		} else {
			window.location = getTokenInjectedURL(url);
		}
	}

	// PRINOVA CUSTOM: On demand token inject for page redirects
	function injectTokenAndRedirectTop(url) {
		if (isViewPdf(url)) {
			viewPdf(url);
		} else {
			getTopFrame().location = getTokenInjectedURL(url);
		}
	}
	
	function isViewPdf(url) {
		var lowerCaseUrl = url.toLowerCase();
		return (lowerCaseUrl.indexOf('pdf.form') != -1 || lowerCaseUrl.indexOf('.pdf') != -1 || lowerCaseUrl.indexOf('remote_comm_pdf') != -1) && lowerCaseUrl.indexOf('action=savefile') == -1;
	}

	function viewPdf(url) {
		popItUp(getTokenInjectedURL("%CONTEXT_PATH%/pdf_view.jsp?url=" + encodeURIComponent(url)));
	}
	
	// PRINOVA CUSTOM: On demand token inject for window opens
	function injectTokenAndOpenWindow(windowEle) {
		console.log('open target window:' + windowEle.name);
		var newWindow = getTopFrame().open( getTokenInjectedURL(windowEle.path), windowEle.name, windowEle.attr );
		if (newWindow != undefined)
			newWindow.focus();
		if ( windowEle.afterOpen != undefined && $.isFunction(windowEle.afterOpen) )
			windowEle.afterOpen(newWindow);
	}
	
	// PRINOVA CUSTOM: Append token to URL
	function getTokenInjectedURL(url) {
		if(url != null && isValidUrl(url) && url.indexOf("&%TOKEN_NAME%") == -1 && url.indexOf("?%TOKEN_NAME%") == -1 ) {
			var hashAppend = '';
			if ( url.indexOf('#') != -1 ) {
				hashAppend = '#' + url.split('#')[1];
				url = url.split('#')[0];
			}
			if(url.indexOf('?') != -1) {
				url = url + '&%TOKEN_NAME%=%TOKEN_VALUE%';
			} else {
				url = url + '?%TOKEN_NAME%=%TOKEN_VALUE%';
			}
			url = url + hashAppend;
		}
		return url;
	}

	/** obtain array of page specific tokens **/
	function requestPageTokens() {
		var xhr = window.XMLHttpRequest ? new window.XMLHttpRequest : new window.ActiveXObject("Microsoft.XMLHTTP");
		var pageTokens = {};
		
		xhr.open("POST", "%SERVLET_PATH%", false);
		xhr.send(null);
		
		var text = xhr.responseText;
		var name = "";
		var value = "";
		var nameContext = true;
		
		for(var i=0; i<text.length; i++) {
			var character = text.charAt(i);
			
			if(character == ':') {
				nameContext = false;
			} else if(character != ',') {
				if(nameContext == true) {
					name += character;
				} else {
					value += character;
				}
			}
			
			if(character == ',' || (i + 1) >= text.length) {
				pageTokens[name] = value;
				name = "";
				value = "";
				nameContext = true;
			}
		}
		
		return pageTokens;
	}

	/** utility method to register window.onload **/
	function addLoadEvent(func) {
		var oldonload = window.onload;
		
		if (typeof window.onload != "function") {
			window.onload = func;
		} else {
			window.onload = function() {
				oldonload();
				func();
			}
		}
	}
	
	function tokenInjectionHandler() {
		while (tokenInjectionTargets != undefined && tokenInjectionTargets.length > 0) {
			injectTokensForObject( tokenInjectionTargets[0] );
			tokenInjectionTargets.pop();
		}
		while (pageRedirectTargets != undefined && pageRedirectTargets.length > 0) {
			injectTokenAndRedirect( pageRedirectTargets[0] );
			pageRedirectTargets.pop();
		}
		while (pageRedirectTargetsTop != undefined && pageRedirectTargetsTop.length > 0) {
			injectTokenAndRedirectTop( pageRedirectTargetsTop[0] );
			pageRedirectTargetsTop.pop();
		}
		while (windowOpenTargets != undefined && windowOpenTargets.length > 0) {
			var windowOpenTarget = windowOpenTargets.pop();

			if (typeof windowOpenTarget !== 'undefined') {
				injectTokenAndOpenWindow( windowOpenTarget );
			}



		}
	}

	$( function() {
		window.setInterval( function(){tokenInjectionHandler()}, 250 );
		
		/**
		 * Only inject the tokens if the JavaScript was referenced from HTML that
		 * was served by us. Otherwise, the code was referenced from malicious HTML
		 * which may be trying to steal tokens using JavaScript hijacking
		 * techniques.
		 */
		if(isValidDomain(document.domain, "%DOMAIN_ORIGIN%")) {
			/** optionally include Ajax support **/
			if(parseBoolean('%INJECT_XHR%') == true) {
				if(navigator.appName == "Microsoft Internet Explorer") {
					hijackExplorer();
				} else {
					hijackStandard();
				}
				
				XMLHttpRequest.prototype.onsend = function(data) {
					if(isValidUrl(this.url) && isMessagepointUrl(this.url)) {
						this.setRequestHeader("%TOKEN_NAME%", "%TOKEN_VALUE%");
					}
				};
			}

			/** update nodes in DOM after load **/
			addLoadEvent(function() {
				injectTokens("%TOKEN_NAME%", "%TOKEN_VALUE%");
			});
		} else {
			alert("OWASP CSRFGuard JavaScript was included from within an unauthorized domain!");
		}
	});

	function parseBoolean(value) {
		return (value + "").toLocaleLowerCase().localeCompare("true") === 0;
	}

})();
