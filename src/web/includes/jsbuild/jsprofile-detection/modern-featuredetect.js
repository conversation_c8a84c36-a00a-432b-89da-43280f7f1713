(function (window) {
    /*
    *       Checks for modern javascript APIs that indicate ES6 support in a browser
     */
    var ModernFeatureDetect = /** @class */ (function () {
        function ModernFeatureDetect(window) {
            this.window = window;
        }
        ModernFeatureDetect.prototype.isServiceWorkerCapable = function () {
            return 'serviceWorker' in navigator;
        };
        ModernFeatureDetect.prototype.isPromiseCapable = function () {
            var checkPromise = window['Promise'];
            return 'Promise' in this.window && 'resolve' in checkPromise && 'reject' in checkPromise && 'all' in checkPromise && 'race' in checkPromise;
        };
        ModernFeatureDetect.prototype.persist = function () {
            var isModern = this.isServiceWorkerCapable() && this.isPromiseCapable();
            if (isModern) {
                this.window.document.cookie = "jsprofile=modern; path=/";
            }
        };
        return ModernFeatureDetect;
    }());
    var instance = new ModernFeatureDetect(window);
    instance.persist();
})(window);
