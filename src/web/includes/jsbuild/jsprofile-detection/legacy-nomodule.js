(function (window) {
    /*
    *   The purpose of this script it to be loaded with a script tag using the 'nomodule' attribute
    *   this will signal that the browser does not support ES6 features and that a compatibility should should
    *   be loaded instead. Currently this will target IE11 as the only non supported browser without ES6 support
    */
    var LegacyJavascriptProfile = /** @class */ (function () {
        function LegacyJavascriptProfile(window) {
            this.window = window;
        }
        LegacyJavascriptProfile.prototype.persist = function () {
            this.window.document.cookie = "jsprofile=legacy; path=/";
        };
        return LegacyJavascriptProfile;
    }());
    var instance = new LegacyJavascriptProfile(window);
    instance.persist();
})(window);
