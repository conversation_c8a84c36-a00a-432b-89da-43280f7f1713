!function(e){var n={};function t(i){if(n[i])return n[i].exports;var r=n[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,t),r.l=!0,r.exports}t.m=e,t.c=n,t.d=function(e,n,i){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:i})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(t.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var r in e)t.d(i,r,function(n){return e[n]}.bind(null,r));return i},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=0)}([function(e,n,t){"use strict";var i=this&&this.__awaiter||function(e,n,t,i){return new(t||(t=Promise))(function(r,o){function a(e){try{u(i.next(e))}catch(e){o(e)}}function s(e){try{u(i.throw(e))}catch(e){o(e)}}function u(e){e.done?r(e.value):new t(function(n){n(e.value)}).then(a,s)}u((i=i.apply(e,n||[])).next())})};Object.defineProperty(n,"__esModule",{value:!0});const r=t(1),o=t(2),a=t(3),s=t(5);class u{constructor(){let e,n,t;this.metrics={location:this.getLocation(),performance:{}},this.timeToFirstByte=new Promise(n=>e=n),this.timeToFirstPaint=new Promise(e=>n=e),this.timeToInteractive=new Promise(e=>t=e),o.default({trackTiming:(i,r,o)=>{switch(r){case"timeToFirstByte":e(o);break;case"timeToFirstPaint":n(o);break;case"timeToInteractive":t(o)}}})}all(){return i(this,void 0,void 0,function*(){return this.detectBrowser(),this.detectScreenResolution(),yield this.loadUxmMetrics(),this.metrics.performance.timeToFirstByte=yield this.timeToFirstByte,this.metrics.performance.timeToInteractive=yield this.timeToInteractive,this.metrics})}loadUxmMetrics(){return i(this,void 0,void 0,function*(){let e=yield r.uxm({all:!0});this.metrics.performance.onLoad=e.onLoad,this.metrics.performance.firstContentfulPaint=e.firstContentfulPaint,this.metrics.performance.domContentLoaded=e.domContentLoaded})}detectBrowser(){const e=a.detect();e&&(this.metrics.clientProfile={},this.metrics.clientProfile.browser=e.name,this.metrics.clientProfile.version=e.version,this.metrics.clientProfile.os=e.os)}detectScreenResolution(){this.metrics.resolution={},this.metrics.resolution.width=screen.width,this.metrics.resolution.height=screen.height,this.metrics.resolution.resolution=`${screen.width}x${screen.height}`,window===window.top&&(this.metrics.viewport={},this.metrics.viewport.width=window.innerWidth,this.metrics.viewport.height=window.innerHeight,this.metrics.viewport.resolution=`${window.innerWidth}x${window.innerHeight}`)}getLocation(){let e=document.createElement("a");return e.href=window.location.href,{host:e.hostname,path:e.pathname}}}(()=>i(this,void 0,void 0,function*(){const e=new u;let n=yield e.all();common.log(n),s.AnalyticsReporting.getInstance().send("End User Experience",n)}))()},function(e,n,t){"use strict";t.r(n),t.d(n,"uxm",function(){return r}),t.d(n,"mark",function(){return o}),t.d(n,"measure",function(){return a}),t.d(n,"getEffectiveConnectionType",function(){return s}),t.d(n,"getFirstPaint",function(){return u}),t.d(n,"getFirstContentfulPaint",function(){return c}),t.d(n,"getOnLoad",function(){return l}),t.d(n,"getDomContentLoaded",function(){return d}),t.d(n,"getDeviceType",function(){return f}),t.d(n,"getUrl",function(){return m}),t.d(n,"getUserAgent",function(){return h}),t.d(n,"getDeviceMemory",function(){return p}),t.d(n,"getUserTiming",function(){return g}),t.d(n,"getResources",function(){return w}),t.d(n,"getLongTasks",function(){return v});var i="undefined"!=typeof window?window.performance:null;function r(e){void 0===e&&(e={});var n={deviceType:f(),effectiveConnectionType:s(),firstPaint:u(),firstContentfulPaint:c(),domContentLoaded:d(),onLoad:l()};return(e.url||e.all)&&(n.url=m()),(e.userAgent||e.all)&&(n.userAgent=h()),(e.deviceMemory||e.all)&&(n.deviceMemory=p()),(e.userTiming||e.all)&&(n.userTiming=g()),(e.longTasks||e.all)&&(n.longTasks=v()),(e.resources||e.all)&&(n.resources=w()),n.onLoad<0?new Promise(function(e){return setTimeout(e,250)}).then(function(){return r(e)}):Promise.resolve(n)}function o(e){i&&i.mark&&i.mark(e)}function a(e,n){if(i&&i.measure)try{i.measure(e,n)}catch(e){console.error(e)}}function s(){var e="undefined"!=typeof navigator?navigator.connection||navigator.mozConnection||navigator.webkitConnection:null;return e?e.effectiveType:null}function u(){if("undefined"==typeof PerformancePaintTiming)return null;var e=i.getEntriesByType("paint").find(function(e){return"first-paint"===e.name});return e?Math.round(e.startTime):null}function c(){if("undefined"==typeof PerformancePaintTiming)return null;var e=i.getEntriesByType("paint").find(function(e){return"first-contentful-paint"===e.name});return e?Math.round(e.startTime):null}function l(){return i&&i.timing?i.timing.loadEventEnd-i.timing.fetchStart:null}function d(){return i&&i.timing?i.timing.domContentLoadedEventEnd-i.timing.fetchStart:null}function f(e){e=(e||h()).toLowerCase();var n=function(n){return-1!==e.indexOf(n)},t=n("windows"),i=t&&n("phone"),r=t&&n("touch")&&!i,o=!t&&n("iphone"),a=n("ipod"),s=n("ipad"),u=!t&&n("android"),c=u&&n("mobile"),l=u&&!n("mobile");return c||o||a||i?"phone":s||l||r?"tablet":"desktop"}function m(){return window.location.href}function h(){return window.navigator.userAgent}function p(){var e="undefined"!=typeof navigator?navigator.deviceMemory:void 0;return void 0===e?null:e>1?"full":"lite"}function g(){if(!i||"undefined"==typeof PerformanceMark)return null;var e=i.getEntriesByType("mark").map(function(e){return{type:"mark",name:e.name,startTime:Math.round(e.startTime)}}),n=i.getEntriesByType("measure").map(function(e){return{type:"measure",name:e.name,startTime:Math.round(e.startTime),duration:Math.round(e.duration)}});return e.concat(n)}function w(){return i&&"undefined"!=typeof PerformanceResourceTiming?i.getEntriesByType("navigation").concat(i.getEntriesByType("resource")).map(function(e){return{url:e.name,type:e.initiatorType,size:e.transferSize,startTime:Math.round(e.startTime),duration:Math.round(e.duration)}}):null}function v(){return void 0===window.__lt?null:window.__lt.e.map(function(e){return{startTime:Math.round(e.startTime),duration:Math.round(e.duration)}})}},function(e,n,t){"use strict";function i(){return!("undefined"==typeof self||!self.performance||!self.performance.timing||0===self.performance.timing.navigationStart)}function r(){return"undefined"!=typeof self&&self.performance&&self.performance.now?Math.round(window.performance.now()):null}function o(){if(!i())return null;var e=window.performance.timing;return e.responseStart-e.navigationStart}function a(){if(window.chrome&&window.chrome.loadTimes){var e=window.chrome.loadTimes(),n=1e3*(e.firstPaintTime-e.startLoadTime);return Math.round(n)}if(i()){var t=window.performance.timing;if(t.msFirstPaint)return t.msFirstPaint-t.navigationStart}return null}Object.defineProperty(n,"__esModule",{value:!0});n.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.trackTiming,t=e.sampleRate,i=void 0===t?100:t,s=e.log,u=void 0!==s&&s;if(!(Math.random()>i/100)){var c=r(),l=[{name:"timeToFirstByte",duration:o()},{name:"timeToFirstPaint",duration:a()},{name:"timeToInteractive",duration:c}];l.forEach(function(e){var t=e.name,i=e.duration;i&&i<36e5&&n("load",t,i)}),u&&console.table(l)}}},function(e,n,t){(function(n){function t(e){var n=o([["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/],["Search Bot",/(nuhk)|(Googlebot)|(Yammybot)|(Openbot)|(Slurp)|(MSNBot)|(Ask Jeeves\/Teoma)|(ia_archiver)/]]).filter(function(n){return n.rule&&n.rule.test(e)})[0];return n?n.name:null}function i(){return void 0!==n&&n.version&&{name:"node",version:n.version.slice(1),os:n.platform}}function r(e){var n=o([["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["vivaldi",/Vivaldi\/([0-9\.]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)$/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FBAV\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/]]);if(!e)return null;var i=n.map(function(n){var t=n.rule.exec(e),i=t&&t[1].split(/[._]/).slice(0,3);return i&&i.length<3&&(i=i.concat(1==i.length?[0,0]:[0])),t&&{name:n.name,version:i.join(".")}}).filter(Boolean)[0]||null;return i&&(i.os=t(e)),/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/i.test(e)&&((i=i||{}).bot=!0),i}function o(e){return e.map(function(e){return{name:e[0],rule:e[1]}})}e.exports={detect:function(){return"undefined"!=typeof navigator?r(navigator.userAgent):i()},detectOS:t,getNodeVersion:i,parseUserAgent:r}}).call(this,t(4))},function(e,n){var t,i,r=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(e){if(t===setTimeout)return setTimeout(e,0);if((t===o||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(n){try{return t.call(null,e,0)}catch(n){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:o}catch(e){t=o}try{i="function"==typeof clearTimeout?clearTimeout:a}catch(e){i=a}}();var u,c=[],l=!1,d=-1;function f(){l&&u&&(l=!1,u.length?c=u.concat(c):d=-1,c.length&&m())}function m(){if(!l){var e=s(f);l=!0;for(var n=c.length;n;){for(u=c,c=[];++d<n;)u&&u[d].run();d=-1,n=c.length}u=null,l=!1,function(e){if(i===clearTimeout)return clearTimeout(e);if((i===a||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{i(e)}catch(n){try{return i.call(null,e)}catch(n){return i.call(this,e)}}}(e)}}function h(e,n){this.fun=e,this.array=n}function p(){}r.nextTick=function(e){var n=new Array(arguments.length-1);if(arguments.length>1)for(var t=1;t<arguments.length;t++)n[t-1]=arguments[t];c.push(new h(e,n)),1!==c.length||l||s(m)},h.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=p,r.addListener=p,r.once=p,r.off=p,r.removeListener=p,r.removeAllListeners=p,r.emit=p,r.prependListener=p,r.prependOnceListener=p,r.listeners=function(e){return[]},r.binding=function(e){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(e){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}},function(e,n,t){"use strict";Object.defineProperty(n,"__esModule",{value:!0});class i{constructor(){let e;this.amplitudeClient=new Promise(n=>e=n);let n=setInterval(()=>{Boolean(window.amplitude)&&(this.client=window.amplitude,e(this.client),clearInterval(n))},100);this.amplitudeClient.then(()=>this.sendPendingEvents())}static getInstance(){return null==i.instance&&(i.instance=new i),i.instance}send(e,n){this.saveReportingData(e,n)}getReportingData(e){let n=sessionStorage.getItem("analytics-pending");e&&sessionStorage.removeItem("analytics-pending");let t=null;return t=null!==n?JSON.parse(n):new Array}saveReportingData(e,n){let t=this.getReportingData();t.push({key:e,data:n}),sessionStorage.setItem("analytics-pending",JSON.stringify(t))}sendPendingEvents(){this.getReportingData(!0).forEach(e=>this.client.logEvent(e.key,e.data))}}i.instance=null,n.AnalyticsReporting=i}]);