!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define(["react"],t):"object"==typeof exports?exports.MessagepointReactColor=t(require("react")):e.MessagepointReactColor=t(e.React)}(window,function(e){return function(e){var t={};function r(o){if(t[o])return t[o].exports;var n=t[o]={i:o,l:!1,exports:{}};return e[o].call(n.exports,n,n.exports,r),n.l=!0,n.exports}return r.m=e,r.c=t,r.d=function(e,t,o){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(r.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)r.d(o,n,function(t){return e[t]}.bind(null,n));return o},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=1)}([function(e,t,r){function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=[],o=!0,n=!1,a=void 0;try{for(var l,i=e[Symbol.iterator]();!(o=(l=i.next()).done)&&(r.push(l.value),!t||r.length!==t);o=!0);}catch(e){n=!0,a=e}finally{try{o||null==i.return||i.return()}finally{if(n)throw a}}return r}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}for(var n=r(5),a={},l=Object.keys(n),i=0;i<l.length;i++){var s=l[i];a[n[s]]=s}var c={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};e.exports=c;for(var u=Object.keys(c),h=0;h<u.length;h++){var d=u[h];if(!("channels"in c[d]))throw new Error("missing channels property: "+d);if(!("labels"in c[d]))throw new Error("missing channel labels property: "+d);if(c[d].labels.length!==c[d].channels)throw new Error("channel and label counts mismatch: "+d);var g=c[d],p=g.channels,f=g.labels;delete c[d].channels,delete c[d].labels,Object.defineProperty(c[d],"channels",{value:p}),Object.defineProperty(c[d],"labels",{value:f})}c.rgb.hsl=function(e){var t,r=e[0]/255,o=e[1]/255,n=e[2]/255,a=Math.min(r,o,n),l=Math.max(r,o,n),i=l-a;l===a?t=0:r===l?t=(o-n)/i:o===l?t=2+(n-r)/i:n===l&&(t=4+(r-o)/i),(t=Math.min(60*t,360))<0&&(t+=360);var s=(a+l)/2;return[t,100*(l===a?0:s<=.5?i/(l+a):i/(2-l-a)),100*s]},c.rgb.hsv=function(e){var t,r,o,n,a,l=e[0]/255,i=e[1]/255,s=e[2]/255,c=Math.max(l,i,s),u=c-Math.min(l,i,s),h=function(e){return(c-e)/6/u+.5};return 0===u?(n=0,a=0):(a=u/c,t=h(l),r=h(i),o=h(s),l===c?n=o-r:i===c?n=1/3+t-o:s===c&&(n=2/3+r-t),n<0?n+=1:n>1&&(n-=1)),[360*n,100*a,100*c]},c.rgb.hwb=function(e){var t=e[0],r=e[1],o=e[2];return[c.rgb.hsl(e)[0],100*(1/255*Math.min(t,Math.min(r,o))),100*(o=1-1/255*Math.max(t,Math.max(r,o)))]},c.rgb.cmyk=function(e){var t=e[0]/255,r=e[1]/255,o=e[2]/255,n=Math.min(1-t,1-r,1-o);return[100*((1-t-n)/(1-n)||0),100*((1-r-n)/(1-n)||0),100*((1-o-n)/(1-n)||0),100*n]},c.rgb.keyword=function(e){var t=a[e];if(t)return t;for(var r,o,l,i=1/0,s=Object.keys(n),c=0;c<s.length;c++){var u=s[c],h=n[u],d=(o=e,l=h,Math.pow(o[0]-l[0],2)+Math.pow(o[1]-l[1],2)+Math.pow(o[2]-l[2],2));d<i&&(i=d,r=u)}return r},c.keyword.rgb=function(e){return n[e]},c.rgb.xyz=function(e){var t=e[0]/255,r=e[1]/255,o=e[2]/255;return[100*(.4124*(t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92)+.3576*(r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92)+.1805*(o=o>.04045?Math.pow((o+.055)/1.055,2.4):o/12.92)),100*(.2126*t+.7152*r+.0722*o),100*(.0193*t+.1192*r+.9505*o)]},c.rgb.lab=function(e){var t=c.rgb.xyz(e),r=t[0],o=t[1],n=t[2];return o/=100,n/=108.883,r=(r/=95.047)>.008856?Math.pow(r,1/3):7.787*r+16/116,[116*(o=o>.008856?Math.pow(o,1/3):7.787*o+16/116)-16,500*(r-o),200*(o-(n=n>.008856?Math.pow(n,1/3):7.787*n+16/116))]},c.hsl.rgb=function(e){var t,r,o,n=e[0]/360,a=e[1]/100,l=e[2]/100;if(0===a)return[o=255*l,o,o];for(var i=2*l-(t=l<.5?l*(1+a):l+a-l*a),s=[0,0,0],c=0;c<3;c++)(r=n+1/3*-(c-1))<0&&r++,r>1&&r--,o=6*r<1?i+6*(t-i)*r:2*r<1?t:3*r<2?i+(t-i)*(2/3-r)*6:i,s[c]=255*o;return s},c.hsl.hsv=function(e){var t=e[0],r=e[1]/100,o=e[2]/100,n=r,a=Math.max(o,.01);return r*=(o*=2)<=1?o:2-o,n*=a<=1?a:2-a,[t,100*(0===o?2*n/(a+n):2*r/(o+r)),100*((o+r)/2)]},c.hsv.rgb=function(e){var t=e[0]/60,r=e[1]/100,o=e[2]/100,n=Math.floor(t)%6,a=t-Math.floor(t),l=255*o*(1-r),i=255*o*(1-r*a),s=255*o*(1-r*(1-a));switch(o*=255,n){case 0:return[o,s,l];case 1:return[i,o,l];case 2:return[l,o,s];case 3:return[l,i,o];case 4:return[s,l,o];case 5:return[o,l,i]}},c.hsv.hsl=function(e){var t,r,o=e[0],n=e[1]/100,a=e[2]/100,l=Math.max(a,.01);r=(2-n)*a;var i=(2-n)*l;return t=n*l,[o,100*(t=(t/=i<=1?i:2-i)||0),100*(r/=2)]},c.hwb.rgb=function(e){var t,r=e[0]/360,o=e[1]/100,n=e[2]/100,a=o+n;a>1&&(o/=a,n/=a);var l=Math.floor(6*r),i=1-n;t=6*r-l,0!=(1&l)&&(t=1-t);var s,c,u,h=o+t*(i-o);switch(l){default:case 6:case 0:s=i,c=h,u=o;break;case 1:s=h,c=i,u=o;break;case 2:s=o,c=i,u=h;break;case 3:s=o,c=h,u=i;break;case 4:s=h,c=o,u=i;break;case 5:s=i,c=o,u=h}return[255*s,255*c,255*u]},c.cmyk.rgb=function(e){var t=e[0]/100,r=e[1]/100,o=e[2]/100,n=e[3]/100;return[255*(1-Math.min(1,t*(1-n)+n)),255*(1-Math.min(1,r*(1-n)+n)),255*(1-Math.min(1,o*(1-n)+n))]},c.xyz.rgb=function(e){var t,r,o,n=e[0]/100,a=e[1]/100,l=e[2]/100;return r=-.9689*n+1.8758*a+.0415*l,o=.0557*n+-.204*a+1.057*l,t=(t=3.2406*n+-1.5372*a+-.4986*l)>.0031308?1.055*Math.pow(t,1/2.4)-.055:12.92*t,r=r>.0031308?1.055*Math.pow(r,1/2.4)-.055:12.92*r,o=o>.0031308?1.055*Math.pow(o,1/2.4)-.055:12.92*o,[255*(t=Math.min(Math.max(0,t),1)),255*(r=Math.min(Math.max(0,r),1)),255*(o=Math.min(Math.max(0,o),1))]},c.xyz.lab=function(e){var t=e[0],r=e[1],o=e[2];return r/=100,o/=108.883,t=(t/=95.047)>.008856?Math.pow(t,1/3):7.787*t+16/116,[116*(r=r>.008856?Math.pow(r,1/3):7.787*r+16/116)-16,500*(t-r),200*(r-(o=o>.008856?Math.pow(o,1/3):7.787*o+16/116))]},c.lab.xyz=function(e){var t,r,o,n=e[0];t=e[1]/500+(r=(n+16)/116),o=r-e[2]/200;var a=Math.pow(r,3),l=Math.pow(t,3),i=Math.pow(o,3);return r=a>.008856?a:(r-16/116)/7.787,t=l>.008856?l:(t-16/116)/7.787,o=i>.008856?i:(o-16/116)/7.787,[t*=95.047,r*=100,o*=108.883]},c.lab.lch=function(e){var t,r=e[0],o=e[1],n=e[2];return(t=360*Math.atan2(n,o)/2/Math.PI)<0&&(t+=360),[r,Math.sqrt(o*o+n*n),t]},c.lch.lab=function(e){var t=e[0],r=e[1],o=e[2]/360*2*Math.PI;return[t,r*Math.cos(o),r*Math.sin(o)]},c.rgb.ansi16=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=o(e,3),n=r[0],a=r[1],l=r[2],i=null===t?c.rgb.hsv(e)[2]:t;if(0===(i=Math.round(i/50)))return 30;var s=30+(Math.round(l/255)<<2|Math.round(a/255)<<1|Math.round(n/255));return 2===i&&(s+=60),s},c.hsv.ansi16=function(e){return c.rgb.ansi16(c.hsv.rgb(e),e[2])},c.rgb.ansi256=function(e){var t=e[0],r=e[1],o=e[2];return t===r&&r===o?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(o/255*5)},c.ansi16.rgb=function(e){var t=e%10;if(0===t||7===t)return e>50&&(t+=3.5),[t=t/10.5*255,t,t];var r=.5*(1+~~(e>50));return[(1&t)*r*255,(t>>1&1)*r*255,(t>>2&1)*r*255]},c.ansi256.rgb=function(e){if(e>=232){var t=10*(e-232)+8;return[t,t,t]}var r;return e-=16,[Math.floor(e/36)/5*255,Math.floor((r=e%36)/6)/5*255,r%6/5*255]},c.rgb.hex=function(e){var t=(((255&Math.round(e[0]))<<16)+((255&Math.round(e[1]))<<8)+(255&Math.round(e[2]))).toString(16).toUpperCase();return"000000".substring(t.length)+t},c.hex.rgb=function(e){var t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];var r=t[0];3===t[0].length&&(r=r.split("").map(function(e){return e+e}).join(""));var o=parseInt(r,16);return[o>>16&255,o>>8&255,255&o]},c.rgb.hcg=function(e){var t,r=e[0]/255,o=e[1]/255,n=e[2]/255,a=Math.max(Math.max(r,o),n),l=Math.min(Math.min(r,o),n),i=a-l;return t=i<=0?0:a===r?(o-n)/i%6:a===o?2+(n-r)/i:4+(r-o)/i+4,t/=6,[360*(t%=1),100*i,100*(i<1?l/(1-i):0)]},c.hsl.hcg=function(e){var t=e[1]/100,r=e[2]/100,o=r<.5?2*t*r:2*t*(1-r),n=0;return o<1&&(n=(r-.5*o)/(1-o)),[e[0],100*o,100*n]},c.hsv.hcg=function(e){var t=e[1]/100,r=e[2]/100,o=t*r,n=0;return o<1&&(n=(r-o)/(1-o)),[e[0],100*o,100*n]},c.hcg.rgb=function(e){var t=e[0]/360,r=e[1]/100,o=e[2]/100;if(0===r)return[255*o,255*o,255*o];var n,a=[0,0,0],l=t%1*6,i=l%1,s=1-i;switch(Math.floor(l)){case 0:a[0]=1,a[1]=i,a[2]=0;break;case 1:a[0]=s,a[1]=1,a[2]=0;break;case 2:a[0]=0,a[1]=1,a[2]=i;break;case 3:a[0]=0,a[1]=s,a[2]=1;break;case 4:a[0]=i,a[1]=0,a[2]=1;break;default:a[0]=1,a[1]=0,a[2]=s}return n=(1-r)*o,[255*(r*a[0]+n),255*(r*a[1]+n),255*(r*a[2]+n)]},c.hcg.hsv=function(e){var t=e[1]/100,r=t+e[2]/100*(1-t),o=0;return r>0&&(o=t/r),[e[0],100*o,100*r]},c.hcg.hsl=function(e){var t=e[1]/100,r=e[2]/100*(1-t)+.5*t,o=0;return r>0&&r<.5?o=t/(2*r):r>=.5&&r<1&&(o=t/(2*(1-r))),[e[0],100*o,100*r]},c.hcg.hwb=function(e){var t=e[1]/100,r=t+e[2]/100*(1-t);return[e[0],100*(r-t),100*(1-r)]},c.hwb.hcg=function(e){var t=e[1]/100,r=1-e[2]/100,o=r-t,n=0;return o<1&&(n=(r-o)/(1-o)),[e[0],100*o,100*n]},c.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},c.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},c.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},c.gray.hsl=function(e){return[0,0,e[0]]},c.gray.hsv=c.gray.hsl,c.gray.hwb=function(e){return[0,100,e[0]]},c.gray.cmyk=function(e){return[0,0,0,e[0]]},c.gray.lab=function(e){return[e[0],0,0]},c.gray.hex=function(e){var t=255&Math.round(e[0]/100*255),r=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(r.length)+r},c.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}},function(e,t,r){e.exports=r(2)},function(e,t,r){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function a(e){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function l(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function i(e,t){return(i=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}Object.defineProperty(t,"__esModule",{value:!0});var s,c=r(3),u=r(4),h=r(7);!function(e){e.hex="#",e.rgb="rgb",e.cmyk="cmyk",e.hsv="hsv",e.hsl="hsl"}(s||(s={}));var d=function(e){function t(e){var r,n,i;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),n=this,r=!(i=a(t).call(this,e))||"object"!==o(i)&&"function"!=typeof i?l(n):i;var s=t.getColorMode(e.colorValue),c=t.parseColorValue(e.colorValue,s),u=r.getColorValues(s,c),h=t.getHue(u);return r.state={colorMode:s,colorValues:u,hue:h,colorSlider:{drag:!1,height:300,width:25},colorBlock:{drag:!1,height:300,width:300}},r.colorBlockClick=r.colorBlockClick.bind(l(r)),r.colorSliderClick=r.colorSliderClick.bind(l(r)),r.colorSliderMouseUp=r.colorSliderMouseUp.bind(l(r)),r.colorSliderMouseDown=r.colorSliderMouseDown.bind(l(r)),r.colorSliderMouseMove=r.colorSliderMouseMove.bind(l(r)),r.colorBlockOnMouseUp=r.colorBlockOnMouseUp.bind(l(r)),r.colorBlockMouseDown=r.colorBlockMouseDown.bind(l(r)),r.colorBlockMouseMove=r.colorBlockMouseMove.bind(l(r)),r.onInputChange=r.onInputChange.bind(l(r)),r.colorModeChange=r.colorModeChange.bind(l(r)),r}var r,d,g;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&i(e,t)}(t,c.Component),r=t,g=[{key:"getHue",value:function(e){var t=0,r=e.rgb[0]/255,o=e.rgb[1]/255,n=e.rgb[2]/255,a=Math.min(r,o,n),l=Math.max(r,o,n);return l-a==0?t=0:r===l?t=(o-n)/(l-a):o===l?t=2+(n-r)/(l-a):n===l&&(t=4+(r-o)/(l-a)),(t*=60)<0&&(t+=360),Math.round(t)}},{key:"getColorMode",value:function(e){var t=e.toLowerCase().trim();return t.startsWith(s.rgb)?s.rgb:t.startsWith(s.hex)?s.hex:t.startsWith(s.cmyk)?s.cmyk:t.startsWith(s.hsv)?s.hsv:t.startsWith(s.hsl)?s.hsl:s.hex}},{key:"parseColorValue",value:function(e,t){return t==s.hex?e.startsWith("#")?e.substr(1):e:((e=(e=(e=(e=e.replace("cmyk(","")).replace("rgb(","")).replace("hsv(","")).replace("hsl(","")).replace(")",""),e.includes(";")?e.split(";").map(function(e){return parseInt(e.trim())}):e.split(",").map(function(e){return parseInt(e.trim())}))}}],(d=[{key:"getColorValues",value:function(e,t){var r={hex:"",rgb:[0,0,0],hsl:[0,0,0],cmyk:[0,0,0,0],hsv:[0,0,0]};switch(e){case s.hex:r.hex=t,r.rgb=u.hex.rgb(t),r.hsl=u.hex.hsl(t),r.cmyk=u.hex.cmyk(t),r.hsv=u.hex.hsv(t);break;case s.rgb:r.hex=u.rgb.hex(t),r.rgb=t,r.hsl=u.rgb.hsl(t),r.cmyk=u.rgb.cmyk(t),r.hsv=u.rgb.hsv(t);break;case s.cmyk:r.hex=u.cmyk.hex(t),r.rgb=u.cmyk.rgb(t),r.hsl=u.cmyk.hsl(t),r.cmyk=t,r.hsv=u.cmyk.hsv(t);break;case s.hsv:r.hex=u.hsv.hex(t),r.rgb=u.hsv.rgb(t),r.hsl=u.hsv.hsl(t),r.cmyk=u.hsv.cmyk(t),r.hsv=t;break;case s.hsl:r.hex=u.hsl.hex(t),r.rgb=u.hsl.rgb(t),r.hsl=t,r.cmyk=u.hsl.cmyk(t),r.hsv=u.hsl.hsv(t)}return r}},{key:"render",value:function(){var e=this,t={position:"absolute",top:"".concat(this.sliderPositionFromHue(),"px"),height:"5px",width:"".concat(this.state.colorSlider.width-2,"px"),border:"1px solid ".concat(this.invertedGreyscale(this.getColorValues(s.hsv,[this.state.hue,100,100]))),zIndex:2},r={position:"absolute",top:"".concat(this.blockPositionFromValue(),"px"),left:"".concat(this.blockPositionFromSaturation(),"px"),width:"10px",height:"10px",borderRadius:"5px",border:"1px solid ".concat(this.invertedGreyscale()),zIndex:2};return c.createElement("div",{style:{gridTemplateRows:"300px 100px"}},c.createElement("div",{style:{display:"grid",gridTemplateColumns:"300px 25px"}},c.createElement("div",{style:{height:"".concat(this.state.colorBlock.height,"px"),width:"".concat(this.state.colorBlock.width,"px")},onClick:this.colorBlockClick,onMouseDown:this.colorBlockMouseDown,onMouseUp:this.colorBlockOnMouseUp,onMouseLeave:this.colorBlockOnMouseUp,onMouseMove:this.colorBlockMouseMove},c.createElement("div",{style:r}),c.createElement("canvas",{id:"colorPickerBlock",height:"".concat(this.state.colorBlock.height,"px"),width:"".concat(this.state.colorBlock.width,"px"),style:{zIndex:1,position:"absolute"}})),c.createElement("div",{style:{height:"300px",width:"30px"}},c.createElement("div",{onClick:this.colorSliderClick,onMouseUp:this.colorSliderMouseUp,onMouseDown:this.colorSliderMouseDown,onMouseMove:this.colorSliderMouseMove,onMouseLeave:this.colorSliderMouseUp,style:{height:"".concat(this.state.colorSlider.height,"px"),width:"".concat(this.state.colorSlider.width,"px")}},c.createElement("div",{id:"pickerSliderIndicator",style:t}),c.createElement("canvas",{style:{zIndex:1,position:"absolute"},id:"colorPickerSlider",height:"300",width:this.state.colorSlider.width})))),c.createElement("div",{style:{display:"grid",gridTemplateColumns:"80px 245px"}},c.createElement("div",{style:{height:"40px",width:"40px",borderRadius:"20px",margin:"16px",backgroundColor:"#".concat(this.state.colorValues.hex),boxShadow:"rgba(0, 0, 0, 0.37) 0px 1px 4px 0px"}}),c.createElement("div",null,c.createElement("div",null,c.createElement("select",{onChange:this.colorModeChange,value:this.state.colorMode},c.createElement("option",{value:s.cmyk},"CMYK"),c.createElement("option",{value:s.rgb},"RGB"),c.createElement("option",{value:s.hsl},"HSL"),c.createElement("option",{value:s.hsv},"HSV"),c.createElement("option",{value:s.hex},"HEX"))),c.createElement("div",null,c.createElement("label",{htmlFor:"input1",hidden:this.inputHidden(0)},this.getInputLabel(0),":"),c.createElement("input",{id:"input1",hidden:this.inputHidden(0),type:"text",size:this.firstInputSize(),value:this.getInputValue(0),onKeyPress:function(t){return e.onInputKeyPress(t,0)},onChange:function(t){return e.onInputChange(t,0)}}),c.createElement("label",{htmlFor:"input2",hidden:this.inputHidden(1)},this.getInputLabel(1),":"),c.createElement("input",{id:"input2",hidden:this.inputHidden(1),type:"text",size:3,value:this.getInputValue(1),onKeyPress:function(t){return e.onInputKeyPress(t,1)},onChange:function(t){return e.onInputChange(t,1)}}),c.createElement("label",{htmlFor:"input3",hidden:this.inputHidden(2)},this.getInputLabel(2),":"),c.createElement("input",{id:"input3",hidden:this.inputHidden(2),type:"text",size:3,value:this.getInputValue(2),onKeyPress:function(t){return e.onInputKeyPress(t,2)},onChange:function(t){return e.onInputChange(t,2)}}),c.createElement("label",{htmlFor:"input4",hidden:this.inputHidden(3)},this.getInputLabel(3),":"),c.createElement("input",{id:"input4",hidden:this.inputHidden(3),type:"text",size:3,value:this.getInputValue(3),onKeyPress:function(t){return e.onInputKeyPress(t,3)},onChange:function(t){return e.onInputChange(t,3)}})))))}},{key:"inputHidden",value:function(e){return!(this.state.colorMode===s.cmyk?e>=0&&e<=3:this.state.colorMode===s.hex?0===e:e>=0&&e<=2)}},{key:"getColorDisplayString",value:function(){switch(this.state.colorMode){case s.cmyk:return"cmyk(".concat(this.state.colorValues.cmyk[0],", ").concat(this.state.colorValues.cmyk[1],", ").concat(this.state.colorValues.cmyk[2],", ").concat(this.state.colorValues.cmyk[3],")");case s.rgb:return"rgb(".concat(this.state.colorValues.rgb[0],", ").concat(this.state.colorValues.rgb[1],", ").concat(this.state.colorValues.rgb[2],")");case s.hsl:return"hsl(".concat(this.state.colorValues.hsl[0],", ").concat(this.state.colorValues.hsl[1],", ").concat(this.state.colorValues.hsl[2],")");case s.hsv:return"hsv(".concat(this.state.colorValues.hsv[0],", ").concat(this.state.colorValues.hsv[1],", ").concat(this.state.colorValues.hsv[2],")");default:return"#".concat(this.state.colorValues.hex)}}},{key:"invertedGreyscale",value:function(e){void 0===e&&(e=this.state.colorValues);var t=h("#".concat(e.hex),!1),r=u.hex.rgb(t.substr(1)),o=Math.round(.3*r[0]+.59*r[1]+.11*r[2]);return o=o>127.5?255:0,"#".concat(u.rgb.hex([o,o,o]))}},{key:"colorBlockClick",value:function(e){var t=e.clientX-e.currentTarget.getBoundingClientRect().left,r=e.clientY-e.currentTarget.getBoundingClientRect().top,o=this.getColorValues(s.hsv,[this.state.hue,this.saturationFromBlockPosition(t),this.valueFromBlockPosition(r)]);this.setState({colorValues:o})}},{key:"componentDidMount",value:function(){this.initGradientFill()}},{key:"componentDidUpdate",value:function(e,t,r){t.hue!==this.state.hue&&this.initGradientFill()}},{key:"initGradientFill",value:function(){var e=document.getElementById("colorPickerBlock"),t=e.getContext("2d"),r=document.getElementById("colorPickerSlider"),o=r.getContext("2d"),n=this.getColorValues(s.hsv,[this.state.hue,100,100]);t.fillStyle="rgb(".concat(n.rgb[0],",").concat(n.rgb[1],",").concat(n.rgb[2],")"),t.fillRect(0,0,e.width,e.height);var a=o.createLinearGradient(0,0,e.width,0);a.addColorStop(0,"rgba(255,255,255,1)"),a.addColorStop(1,"rgba(255,255,255,0)"),t.fillStyle=a,t.fillRect(0,0,e.width,e.height);var l=o.createLinearGradient(0,0,0,e.height);l.addColorStop(0,"rgba(0,0,0,0)"),l.addColorStop(1,"rgba(0,0,0,1)"),t.fillStyle=l,t.fillRect(0,0,e.width,e.height),o.rect(0,0,r.width,r.height);var i=o.createLinearGradient(0,0,0,e.height);i.addColorStop(0,"rgba(255, 0, 0, 1)"),i.addColorStop(.17,"rgba(255, 255, 0, 1)"),i.addColorStop(.34,"rgba(0, 255, 0, 1)"),i.addColorStop(.51,"rgba(0, 255, 255, 1)"),i.addColorStop(.68,"rgba(0, 0, 255, 1)"),i.addColorStop(.85,"rgba(255, 0, 255, 1)"),i.addColorStop(1,"rgba(255, 0, 0, 1)"),o.fillStyle=i,o.fill()}},{key:"colorSliderClick",value:function(e){this.setHueFromSliderY(e.clientY-e.currentTarget.getBoundingClientRect().top)}},{key:"colorSliderMouseUp",value:function(){this.setState(function(e){return e.colorSlider.drag=!1,e})}},{key:"colorSliderMouseDown",value:function(){this.setState(function(e){return e.colorSlider.drag=!0,e})}},{key:"colorSliderMouseMove",value:function(e){this.state.colorSlider.drag&&this.setHueFromSliderY(e.clientY-e.currentTarget.getBoundingClientRect().top)}},{key:"sliderPositionFromHue",value:function(){var e=Math.round(this.state.hue/360*this.state.colorSlider.height);return e>this.state.colorSlider.height-10?this.state.colorSlider.height-10:e}},{key:"setHueFromSliderY",value:function(e){var t=Math.round(e/this.state.colorSlider.height*360),r=this.getColorValues(s.hsv,[t,this.state.colorValues.hsv[1],this.state.colorValues.hsv[2]]);this.setState({hue:t,colorValues:r},this.onColorChangeHandler)}},{key:"blockPositionFromValue",value:function(){var e=this.state.colorValues.hsv[2]/100,r=this.state.colorBlock.height-Math.round(e*this.state.colorBlock.height);return r<0?0:r+t.COLOR_BLOCK_SLIDER_RADIUS_PX/2>this.state.colorBlock.height?this.state.colorBlock.height-t.COLOR_BLOCK_SLIDER_RADIUS_PX/2:r}},{key:"valueFromBlockPosition",value:function(e){var t=e/this.state.colorBlock.height;return 100-Math.round(100*t)}},{key:"blockPositionFromSaturation",value:function(){var e=this.state.colorValues.hsv[1]/100,r=Math.round(e*this.state.colorBlock.width);return r<0?0:r+t.COLOR_BLOCK_SLIDER_RADIUS_PX/2>this.state.colorBlock.width?this.state.colorBlock.width-t.COLOR_BLOCK_SLIDER_RADIUS_PX/2:r}},{key:"saturationFromBlockPosition",value:function(e){var t=e/this.state.colorBlock.width;return Math.round(100*t)}},{key:"colorBlockMouseDown",value:function(){this.setState(function(e){return e.colorBlock.drag=!0,e})}},{key:"colorBlockOnMouseUp",value:function(){this.setState(function(e){return e.colorBlock.drag=!1,e})}},{key:"colorBlockMouseMove",value:function(e){if(this.state.colorBlock.drag){var t=e.clientX-e.currentTarget.getBoundingClientRect().left,r=e.clientY-e.currentTarget.getBoundingClientRect().top,o=this.getColorValues(s.hsv,[this.state.hue,this.saturationFromBlockPosition(t),this.valueFromBlockPosition(r)]);this.setState({colorValues:o},this.onColorChangeHandler)}}},{key:"onInputChange",value:function(e,t){var r=!1,o=this.state.colorValues[this.state.colorMode];if(this.state.colorMode!==s.hex){var n=""!==e.currentTarget.value?e.currentTarget.value:"0";o[t]=parseInt(n),r=!0}else r=6===(o=""!==e.currentTarget.value?e.currentTarget.value:"000000").length;if(r){var a=this.getColorValues(this.state.colorMode,o),l=a.hsv[0];this.setState({colorValues:a,hue:l},this.onColorChangeHandler)}}},{key:"colorModeChange",value:function(e){var r=t.getColorMode(e.currentTarget.value);this.setState({colorMode:r})}},{key:"getInputLabel",value:function(e){return this.state.colorMode!=s.hex?this.state.colorMode.toUpperCase()[e]:s.hex}},{key:"getInputValue",value:function(e){return this.state.colorMode===s.hex?this.state.colorValues.hex:this.state.colorValues[this.state.colorMode][e]}},{key:"firstInputSize",value:function(){return this.state.colorMode!==s.hex?3:8}},{key:"onInputKeyPress",value:function(e,t){var r=!1;if(console.log("keyCode: ".concat(e.charCode)),8!==e.keyCode&&46!==e.keyCode){var o=e.charCode;this.state.colorMode===s.hex?o>47&&o<58&&o>64&&o<69&&o>96&&o<101&&(r=!0):o>47&&o<58&&(r=!0),r||e.preventDefault()}}},{key:"onColorChangeHandler",value:function(){"function"==typeof this.props.onColorChange&&this.props.onColorChange(this.state.colorValues)}}])&&n(r.prototype,d),g&&n(r,g),t}();d.COLOR_BLOCK_SLIDER_RADIUS_PX=25,e.exports=d},function(t,r){t.exports=e},function(e,t,r){function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var n=r(0),a=r(6),l={};Object.keys(n).forEach(function(e){l[e]={},Object.defineProperty(l[e],"channels",{value:n[e].channels}),Object.defineProperty(l[e],"labels",{value:n[e].labels});var t=a(e);Object.keys(t).forEach(function(r){var n=t[r];l[e][r]=function(e){var t=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var a=r[0];if(null==a)return a;a.length>1&&(r=a);var l=e(r);if("object"===o(l))for(var i=l.length,s=0;s<i;s++)l[s]=Math.round(l[s]);return l};return"conversion"in e&&(t.conversion=e.conversion),t}(n),l[e][r].raw=function(e){var t=function(){for(var t=arguments.length,r=new Array(t),o=0;o<t;o++)r[o]=arguments[o];var n=r[0];return null==n?n:(n.length>1&&(r=n),e(r))};return"conversion"in e&&(t.conversion=e.conversion),t}(n)})}),e.exports=l},function(e,t,r){"use strict";e.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},function(e,t,r){var o=r(0);function n(e){var t=function(){for(var e={},t=Object.keys(o),r=t.length,n=0;n<r;n++)e[t[n]]={distance:-1,parent:null};return e}(),r=[e];for(t[e].distance=0;r.length;)for(var n=r.pop(),a=Object.keys(o[n]),l=a.length,i=0;i<l;i++){var s=a[i],c=t[s];-1===c.distance&&(c.distance=t[n].distance+1,c.parent=n,r.unshift(s))}return t}function a(e,t){return function(r){return t(e(r))}}function l(e,t){for(var r=[t[e].parent,e],n=o[t[e].parent][e],l=t[e].parent;t[l].parent;)r.unshift(t[l].parent),n=a(o[t[l].parent][l],n),l=t[l].parent;return n.conversion=r,n}e.exports=function(e){for(var t=n(e),r={},o=Object.keys(t),a=o.length,i=0;i<a;i++){var s=o[i];null!==t[s].parent&&(r[s]=l(s,t))}return r}},function(e,t,r){var o,n,a;function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}
/*! @license https://github.com/onury/invert-color */a=function(){"use strict";var e=Math.sqrt(1.05*.05)-.05,t=/^(?:[0-9a-f]{3}){1,2}$/i,r={black:"#000000",white:"#ffffff",threshold:e};function o(e){if("#"===e.slice(0,1)&&(e=e.slice(1)),!t.test(e))throw new Error('Invalid HEX color: "'+e+'"');return 3===e.length&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]),[parseInt(e.slice(0,2),16),parseInt(e.slice(2,4),16),parseInt(e.slice(4,6),16)]}function n(e){if(!e)throw new Error("Invalid color value");return Array.isArray(e)?e:"string"==typeof e?o(e):[e.r,e.g,e.b]}function a(e,t,n){var a=!0===t?r:Object.assign({},r,t);return function(e){var t,r,o=[];for(t=0;t<e.length;t++)r=e[t]/255,o[t]=r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4);return.2126*o[0]+.7152*o[1]+.0722*o[2]}(e)>a.threshold?n?o(a.black):a.black:n?o(a.white):a.white}function l(e,t){return void 0===t&&(t=!1),e=n(e),t?a(e,t):"#"+e.map(function(e){return t=(255-e).toString(16),void 0===r&&(r=2),(new Array(r).join("0")+t).slice(-r);var t,r}).join("")}return function(t){function r(e,t){var r;return e=n(e),{r:(r=t?a(e,t,!0):e.map(function(e){return 255-e}))[0],g:r[1],b:r[2]}}t.asRGB=r,t.asRgbArray=function(e,t){return e=n(e),t?a(e,t,!0):e.map(function(e){return 255-e})},t.defaultThreshold=e,t.asRgbObject=r}(l||(l={})),l},"object"==l(t)&&void 0!==e?e.exports=a():void 0===(n="function"==typeof(o=a)?o.call(t,r,t,e):o)||(e.exports=n)}])});