(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("react"));
	else if(typeof define === 'function' && define.amd)
		define(["react"], factory);
	else if(typeof exports === 'object')
		exports["MessagepointReactColor"] = factory(require("react"));
	else
		root["MessagepointReactColor"] = factory(root["React"]);
})(window, function(__WEBPACK_EXTERNAL_MODULE_react__) {
return /******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./MessagepointReactColor/MessagepointReactColor.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./MessagepointReactColor/MessagepointReactColor.js":
/*!**********************************************************!*\
  !*** ./MessagepointReactColor/MessagepointReactColor.js ***!
  \**********************************************************/
/*! no static exports found */
/*! all exports used */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst React = __webpack_require__(/*! react */ \"react\");\nconst ColorConvert = __webpack_require__(/*! color-convert */ \"./node_modules/color-convert/index.js\");\nconst invert = __webpack_require__(/*! invert-color */ \"./node_modules/invert-color/lib/invert.min.js\");\nvar ColorMode;\n(function (ColorMode) {\n    ColorMode[\"hex\"] = \"#\";\n    ColorMode[\"rgb\"] = \"rgb\";\n    ColorMode[\"cmyk\"] = \"cmyk\";\n    ColorMode[\"hsv\"] = \"hsv\";\n    ColorMode[\"hsl\"] = \"hsl\";\n})(ColorMode || (ColorMode = {}));\nclass MessagepointReactColor extends React.Component {\n    constructor(props) {\n        super(props);\n        let colorMode = MessagepointReactColor.getColorMode(props.colorValue);\n        let colorValue = MessagepointReactColor.parseColorValue(props.colorValue, colorMode);\n        let colorValues = this.getColorValues(colorMode, colorValue);\n        let hue = MessagepointReactColor.getHue(colorValues);\n        this.state = {\n            colorMode: colorMode,\n            colorValues: colorValues,\n            hue: hue,\n            colorSlider: {\n                drag: false,\n                height: 300,\n                width: 25\n            },\n            colorBlock: {\n                drag: false,\n                height: 300,\n                width: 300\n            }\n        };\n        this.colorBlockClick = this.colorBlockClick.bind(this);\n        this.colorSliderClick = this.colorSliderClick.bind(this);\n        this.colorSliderMouseUp = this.colorSliderMouseUp.bind(this);\n        this.colorSliderMouseDown = this.colorSliderMouseDown.bind(this);\n        this.colorSliderMouseMove = this.colorSliderMouseMove.bind(this);\n        this.colorBlockOnMouseUp = this.colorBlockOnMouseUp.bind(this);\n        this.colorBlockMouseDown = this.colorBlockMouseDown.bind(this);\n        this.colorBlockMouseMove = this.colorBlockMouseMove.bind(this);\n        this.onInputChange = this.onInputChange.bind(this);\n        this.colorModeChange = this.colorModeChange.bind(this);\n    }\n    getColorValues(colorMode, colorValue) {\n        let colorValues = {\n            hex: \"\",\n            rgb: [0, 0, 0],\n            hsl: [0, 0, 0],\n            cmyk: [0, 0, 0, 0],\n            hsv: [0, 0, 0],\n        };\n        switch (colorMode) {\n            case ColorMode.hex:\n                colorValues.hex = colorValue;\n                colorValues.rgb = ColorConvert.hex.rgb(colorValue);\n                colorValues.hsl = ColorConvert.hex.hsl(colorValue);\n                colorValues.cmyk = ColorConvert.hex.cmyk(colorValue);\n                colorValues.hsv = ColorConvert.hex.hsv(colorValue);\n                break;\n            case ColorMode.rgb:\n                colorValues.hex = ColorConvert.rgb.hex(colorValue);\n                colorValues.rgb = colorValue;\n                colorValues.hsl = ColorConvert.rgb.hsl(colorValue);\n                colorValues.cmyk = ColorConvert.rgb.cmyk(colorValue);\n                colorValues.hsv = ColorConvert.rgb.hsv(colorValue);\n                break;\n            case ColorMode.cmyk:\n                colorValues.hex = ColorConvert.cmyk.hex(colorValue);\n                colorValues.rgb = ColorConvert.cmyk.rgb(colorValue);\n                colorValues.hsl = ColorConvert.cmyk.hsl(colorValue);\n                colorValues.cmyk = colorValue;\n                colorValues.hsv = ColorConvert.cmyk.hsv(colorValue);\n                break;\n            case ColorMode.hsv:\n                colorValues.hex = ColorConvert.hsv.hex(colorValue);\n                colorValues.rgb = ColorConvert.hsv.rgb(colorValue);\n                colorValues.hsl = ColorConvert.hsv.hsl(colorValue);\n                colorValues.cmyk = ColorConvert.hsv.cmyk(colorValue);\n                colorValues.hsv = colorValue;\n                break;\n            case ColorMode.hsl:\n                colorValues.hex = ColorConvert.hsl.hex(colorValue);\n                colorValues.rgb = ColorConvert.hsl.rgb(colorValue);\n                colorValues.hsl = colorValue;\n                colorValues.cmyk = ColorConvert.hsl.cmyk(colorValue);\n                colorValues.hsv = ColorConvert.hsl.hsv(colorValue);\n                break;\n        }\n        return colorValues;\n    }\n    render() {\n        let sliderStyle = {\n            position: 'absolute',\n            top: `${this.sliderPositionFromHue()}px`,\n            height: '5px',\n            width: `${this.state.colorSlider.width - 2}px`,\n            border: `1px solid ${this.invertedGreyscale(this.getColorValues(ColorMode.hsv, [this.state.hue, 100, 100]))}`,\n            //boxShadow: 'rgba(0, 0, 0, 0.37) 0px 1px 4px 0px',\n            zIndex: 2\n        };\n        let colorPickerStyle = { position: \"absolute\",\n            top: `${this.blockPositionFromValue()}px`,\n            left: `${this.blockPositionFromSaturation()}px`,\n            width: \"10px\",\n            height: \"10px\",\n            borderRadius: \"5px\",\n            border: `1px solid ${this.invertedGreyscale()}`,\n            //boxShadow: 'rgba(0, 0, 0, 0.37) 0px 1px 4px 0px',\n            zIndex: 2 };\n        return (React.createElement(\"div\", { style: { gridTemplateRows: '300px 100px' } },\n            React.createElement(\"div\", { style: { display: 'grid', gridTemplateColumns: '300px 25px' } },\n                React.createElement(\"div\", { style: { height: `${this.state.colorBlock.height}px`, width: `${this.state.colorBlock.width}px` }, onClick: this.colorBlockClick, onMouseDown: this.colorBlockMouseDown, onMouseUp: this.colorBlockOnMouseUp, onMouseLeave: this.colorBlockOnMouseUp, onMouseMove: this.colorBlockMouseMove },\n                    React.createElement(\"div\", { style: colorPickerStyle }),\n                    React.createElement(\"canvas\", { id: \"colorPickerBlock\", height: `${this.state.colorBlock.height}px`, width: `${this.state.colorBlock.width}px`, style: { zIndex: 1, position: \"absolute\" } })),\n                React.createElement(\"div\", { style: { height: '300px', width: '30px' } },\n                    React.createElement(\"div\", { onClick: this.colorSliderClick, onMouseUp: this.colorSliderMouseUp, onMouseDown: this.colorSliderMouseDown, onMouseMove: this.colorSliderMouseMove, onMouseLeave: this.colorSliderMouseUp, style: {\n                            height: `${this.state.colorSlider.height}px`,\n                            width: `${this.state.colorSlider.width}px`\n                        } },\n                        React.createElement(\"div\", { id: \"pickerSliderIndicator\", style: sliderStyle }),\n                        React.createElement(\"canvas\", { style: { zIndex: 1, position: \"absolute\" }, id: \"colorPickerSlider\", height: \"300\", width: this.state.colorSlider.width })))),\n            React.createElement(\"div\", { style: { display: \"grid\", gridTemplateColumns: '80px 245px' } },\n                React.createElement(\"div\", { style: {\n                        height: '40px',\n                        width: '40px',\n                        borderRadius: '20px',\n                        margin: '16px',\n                        backgroundColor: `#${this.state.colorValues.hex}`,\n                        boxShadow: 'rgba(0, 0, 0, 0.37) 0px 1px 4px 0px'\n                    } }),\n                React.createElement(\"div\", null,\n                    React.createElement(\"div\", null,\n                        React.createElement(\"select\", { onChange: this.colorModeChange, value: this.state.colorMode },\n                            React.createElement(\"option\", { value: ColorMode.cmyk }, \"CMYK\"),\n                            React.createElement(\"option\", { value: ColorMode.rgb }, \"RGB\"),\n                            React.createElement(\"option\", { value: ColorMode.hsl }, \"HSL\"),\n                            React.createElement(\"option\", { value: ColorMode.hsv }, \"HSV\"),\n                            React.createElement(\"option\", { value: ColorMode.hex }, \"HEX\"))),\n                    React.createElement(\"div\", null,\n                        React.createElement(\"label\", { htmlFor: \"input1\", hidden: this.inputHidden(0) },\n                            this.getInputLabel(0),\n                            \":\"),\n                        React.createElement(\"input\", { id: \"input1\", hidden: this.inputHidden(0), type: \"text\", size: this.firstInputSize(), value: this.getInputValue(0), onKeyPress: e => this.onInputKeyPress(e, 0), onChange: e => this.onInputChange(e, 0) }),\n                        React.createElement(\"label\", { htmlFor: \"input2\", hidden: this.inputHidden(1) },\n                            this.getInputLabel(1),\n                            \":\"),\n                        React.createElement(\"input\", { id: \"input2\", hidden: this.inputHidden(1), type: \"text\", size: 3, value: this.getInputValue(1), onKeyPress: e => this.onInputKeyPress(e, 1), onChange: e => this.onInputChange(e, 1) }),\n                        React.createElement(\"label\", { htmlFor: \"input3\", hidden: this.inputHidden(2) },\n                            this.getInputLabel(2),\n                            \":\"),\n                        React.createElement(\"input\", { id: \"input3\", hidden: this.inputHidden(2), type: \"text\", size: 3, value: this.getInputValue(2), onKeyPress: e => this.onInputKeyPress(e, 2), onChange: e => this.onInputChange(e, 2) }),\n                        React.createElement(\"label\", { htmlFor: \"input4\", hidden: this.inputHidden(3) },\n                            this.getInputLabel(3),\n                            \":\"),\n                        React.createElement(\"input\", { id: \"input4\", hidden: this.inputHidden(3), type: \"text\", size: 3, value: this.getInputValue(3), onKeyPress: e => this.onInputKeyPress(e, 3), onChange: e => this.onInputChange(e, 3) }))))));\n    }\n    inputHidden(number) {\n        let isVisible = false;\n        if (this.state.colorMode === ColorMode.cmyk) {\n            isVisible = number >= 0 && number <= 3;\n        }\n        else if (this.state.colorMode === ColorMode.hex) {\n            isVisible = number === 0;\n        }\n        else {\n            isVisible = number >= 0 && number <= 2;\n        }\n        return !isVisible;\n    }\n    getColorDisplayString() {\n        switch (this.state.colorMode) {\n            case ColorMode.cmyk:\n                return `cmyk(${this.state.colorValues.cmyk[0]}, ${this.state.colorValues.cmyk[1]}, ${this.state.colorValues.cmyk[2]}, ${this.state.colorValues.cmyk[3]})`;\n            case ColorMode.rgb:\n                return `rgb(${this.state.colorValues.rgb[0]}, ${this.state.colorValues.rgb[1]}, ${this.state.colorValues.rgb[2]})`;\n            case ColorMode.hsl:\n                return `hsl(${this.state.colorValues.hsl[0]}, ${this.state.colorValues.hsl[1]}, ${this.state.colorValues.hsl[2]})`;\n            case ColorMode.hsv:\n                return `hsv(${this.state.colorValues.hsv[0]}, ${this.state.colorValues.hsv[1]}, ${this.state.colorValues.hsv[2]})`;\n            default:\n                return `#${this.state.colorValues.hex}`;\n        }\n    }\n    invertedGreyscale(colorValue) {\n        if (colorValue === undefined) {\n            colorValue = this.state.colorValues;\n        }\n        let inverted = invert(`#${colorValue.hex}`, false);\n        let colorValues = ColorConvert.hex.rgb(inverted.substr(1));\n        let greyScale = Math.round((0.3 * colorValues[0]) + (0.59 * colorValues[1]) + (0.11 * colorValues[2]));\n        greyScale = (greyScale > (255 / 2)) ? 255 : 0;\n        return `#${ColorConvert.rgb.hex([greyScale, greyScale, greyScale])}`;\n    }\n    colorBlockClick(event) {\n        let xValue = event.clientX - (event.currentTarget.getBoundingClientRect().left);\n        let yValue = event.clientY - event.currentTarget.getBoundingClientRect().top;\n        let newColor = this.getColorValues(ColorMode.hsv, [this.state.hue, this.saturationFromBlockPosition(xValue), this.valueFromBlockPosition(yValue)]);\n        this.setState({ colorValues: newColor });\n    }\n    static getHue(color) {\n        let hue = 0;\n        let r = color.rgb[0] / 255;\n        let g = color.rgb[1] / 255;\n        let b = color.rgb[2] / 255;\n        /*\n        If Red is max, then Hue = (G-B)/(max-min)\n        If Green is max, then Hue = 2.0 + (B-R)/(max-min)\n        If Blue is max, then Hue = 4.0 + (R-G)/(max-min)\n        */\n        let min = Math.min(r, g, b);\n        let max = Math.max(r, g, b);\n        if (max - min === 0) {\n            hue = 0;\n        }\n        else {\n            if (r === max) {\n                hue = (g - b) / (max - min);\n            }\n            else if (g === max) {\n                hue = 2.0 + ((b - r) / (max - min));\n            }\n            else if (b === max) {\n                hue = 4.0 + ((r - g) / (max - min));\n            }\n        }\n        hue = hue * 60;\n        if (hue < 0) {\n            hue = hue + 360;\n        }\n        return Math.round(hue);\n    }\n    componentDidMount() {\n        this.initGradientFill();\n    }\n    componentDidUpdate(prevProps, prevState, snapshot) {\n        if (prevState.hue !== this.state.hue) {\n            this.initGradientFill();\n        }\n    }\n    static getColorMode(colorValue) {\n        let value = colorValue.toLowerCase().trim();\n        if (value.startsWith(ColorMode.rgb)) {\n            return ColorMode.rgb;\n        }\n        else if (value.startsWith(ColorMode.hex)) {\n            return ColorMode.hex;\n        }\n        else if (value.startsWith(ColorMode.cmyk)) {\n            return ColorMode.cmyk;\n        }\n        else if (value.startsWith(ColorMode.hsv)) {\n            return ColorMode.hsv;\n        }\n        else if (value.startsWith(ColorMode.hsl)) {\n            return ColorMode.hsl;\n        }\n        return ColorMode.hex;\n    }\n    initGradientFill() {\n        let colorBlock = document.getElementById('colorPickerBlock');\n        let blockContext = colorBlock.getContext('2d');\n        let colorStrip = document.getElementById('colorPickerSlider');\n        let stripContext = colorStrip.getContext('2d');\n        let fillColor = this.getColorValues(ColorMode.hsv, [this.state.hue, 100, 100]);\n        blockContext.fillStyle = `rgb(${fillColor.rgb[0]},${fillColor.rgb[1]},${fillColor.rgb[2]})`;\n        blockContext.fillRect(0, 0, colorBlock.width, colorBlock.height);\n        let whiteGradient = stripContext.createLinearGradient(0, 0, colorBlock.width, 0);\n        whiteGradient.addColorStop(0, 'rgba(255,255,255,1)');\n        whiteGradient.addColorStop(1, 'rgba(255,255,255,0)');\n        blockContext.fillStyle = whiteGradient;\n        blockContext.fillRect(0, 0, colorBlock.width, colorBlock.height);\n        let blackGradient = stripContext.createLinearGradient(0, 0, 0, colorBlock.height);\n        blackGradient.addColorStop(0, 'rgba(0,0,0,0)');\n        blackGradient.addColorStop(1, 'rgba(0,0,0,1)');\n        blockContext.fillStyle = blackGradient;\n        blockContext.fillRect(0, 0, colorBlock.width, colorBlock.height);\n        stripContext.rect(0, 0, colorStrip.width, colorStrip.height);\n        let stripGradient = stripContext.createLinearGradient(0, 0, 0, colorBlock.height);\n        stripGradient.addColorStop(0, 'rgba(255, 0, 0, 1)');\n        stripGradient.addColorStop(0.17, 'rgba(255, 255, 0, 1)');\n        stripGradient.addColorStop(0.34, 'rgba(0, 255, 0, 1)');\n        stripGradient.addColorStop(0.51, 'rgba(0, 255, 255, 1)');\n        stripGradient.addColorStop(0.68, 'rgba(0, 0, 255, 1)');\n        stripGradient.addColorStop(0.85, 'rgba(255, 0, 255, 1)');\n        stripGradient.addColorStop(1, 'rgba(255, 0, 0, 1)');\n        stripContext.fillStyle = stripGradient;\n        stripContext.fill();\n    }\n    colorSliderClick(event) {\n        this.setHueFromSliderY(event.clientY - event.currentTarget.getBoundingClientRect().top);\n    }\n    colorSliderMouseUp() {\n        this.setState(prevState => {\n            prevState.colorSlider.drag = false;\n            return prevState;\n        });\n    }\n    colorSliderMouseDown() {\n        this.setState(prevState => {\n            prevState.colorSlider.drag = true;\n            return prevState;\n        });\n    }\n    colorSliderMouseMove(event) {\n        if (this.state.colorSlider.drag) {\n            this.setHueFromSliderY(event.clientY - event.currentTarget.getBoundingClientRect().top);\n        }\n    }\n    sliderPositionFromHue() {\n        let position = Math.round((this.state.hue / 360) * this.state.colorSlider.height);\n        if (position > (this.state.colorSlider.height - 10)) {\n            return (this.state.colorSlider.height - 10);\n        }\n        return position;\n    }\n    setHueFromSliderY(hueSliderY) {\n        let newHue = Math.round((hueSliderY / this.state.colorSlider.height) * 360);\n        let newColor = this.getColorValues(ColorMode.hsv, [newHue, this.state.colorValues.hsv[1], this.state.colorValues.hsv[2]]);\n        this.setState({ hue: newHue, colorValues: newColor }, this.onColorChangeHandler);\n    }\n    blockPositionFromValue() {\n        let ratio = this.state.colorValues.hsv[2] / 100;\n        let position = this.state.colorBlock.height - Math.round(ratio * this.state.colorBlock.height);\n        if (position < 0) {\n            return 0;\n        }\n        if ((position + MessagepointReactColor.COLOR_BLOCK_SLIDER_RADIUS_PX / 2) > this.state.colorBlock.height) {\n            return this.state.colorBlock.height - (MessagepointReactColor.COLOR_BLOCK_SLIDER_RADIUS_PX / 2);\n        }\n        return position;\n    }\n    valueFromBlockPosition(yValue) {\n        let ratio = yValue / this.state.colorBlock.height;\n        return 100 - Math.round(ratio * 100);\n    }\n    blockPositionFromSaturation() {\n        let ratio = this.state.colorValues.hsv[1] / 100;\n        let position = Math.round(ratio * this.state.colorBlock.width);\n        if (position < 0) {\n            return 0;\n        }\n        if ((position + MessagepointReactColor.COLOR_BLOCK_SLIDER_RADIUS_PX / 2) > this.state.colorBlock.width) {\n            return this.state.colorBlock.width - (MessagepointReactColor.COLOR_BLOCK_SLIDER_RADIUS_PX / 2);\n        }\n        return position;\n    }\n    saturationFromBlockPosition(xValue) {\n        let ratio = xValue / this.state.colorBlock.width;\n        return Math.round(ratio * 100);\n    }\n    colorBlockMouseDown() {\n        this.setState(prevState => {\n            prevState.colorBlock.drag = true;\n            return prevState;\n        });\n    }\n    colorBlockOnMouseUp() {\n        this.setState(prevState => {\n            prevState.colorBlock.drag = false;\n            return prevState;\n        });\n    }\n    colorBlockMouseMove(event) {\n        if (this.state.colorBlock.drag) {\n            let xValue = event.clientX - event.currentTarget.getBoundingClientRect().left;\n            let yValue = event.clientY - event.currentTarget.getBoundingClientRect().top;\n            let newColor = this.getColorValues(ColorMode.hsv, [this.state.hue, this.saturationFromBlockPosition(xValue), this.valueFromBlockPosition(yValue)]);\n            this.setState({ colorValues: newColor }, this.onColorChangeHandler);\n        }\n    }\n    onInputChange(event, number) {\n        let validColor = false;\n        let color = this.state.colorValues[this.state.colorMode];\n        if (this.state.colorMode !== ColorMode.hex) {\n            let value = event.currentTarget.value !== \"\" ? event.currentTarget.value : \"0\";\n            color[number] = parseInt(value);\n            validColor = true;\n        }\n        else {\n            color = event.currentTarget.value !== \"\" ? event.currentTarget.value : \"000000\";\n            validColor = color.length === 6;\n        }\n        if (validColor) {\n            let newColor = this.getColorValues(this.state.colorMode, color);\n            let newHue = newColor.hsv[0];\n            this.setState({\n                colorValues: newColor,\n                hue: newHue\n            }, this.onColorChangeHandler);\n        }\n    }\n    colorModeChange(event) {\n        let colorMode = MessagepointReactColor.getColorMode(event.currentTarget.value);\n        this.setState({ colorMode: colorMode });\n    }\n    static parseColorValue(colorValue, colorMode) {\n        if (colorMode == ColorMode.hex) {\n            return colorValue.startsWith('#') ? colorValue.substr(1) : colorValue;\n        }\n        colorValue = colorValue.replace('cmyk(', '');\n        colorValue = colorValue.replace('rgb(', '');\n        colorValue = colorValue.replace('hsv(', '');\n        colorValue = colorValue.replace('hsl(', '');\n        colorValue.replace(')', '');\n        if (colorValue.includes(\";\")) {\n            return colorValue.split(';').map(x => parseInt(x.trim()));\n        }\n        else {\n            return colorValue.split(',').map(x => parseInt(x.trim()));\n        }\n    }\n    getInputLabel(number) {\n        return this.state.colorMode != ColorMode.hex ?\n            this.state.colorMode.toUpperCase()[number] :\n            ColorMode.hex;\n    }\n    getInputValue(number) {\n        if (this.state.colorMode === ColorMode.hex) {\n            return this.state.colorValues.hex;\n        }\n        return this.state.colorValues[this.state.colorMode][number];\n    }\n    firstInputSize() {\n        return this.state.colorMode !== ColorMode.hex ? 3 : 8;\n    }\n    onInputKeyPress(e, number) {\n        let result = false;\n        console.log(`keyCode: ${e.charCode}`);\n        if (e.keyCode === 8 || e.keyCode === 46) {\n            return;\n        }\n        let inputCode = e.charCode;\n        if (this.state.colorMode === ColorMode.hex) {\n            if ((inputCode > 47 && inputCode < 58) && // numeric (0-9)\n                (inputCode > 64 && inputCode < 69) && // upper alpha (A-F)\n                (inputCode > 96 && inputCode < 101)) { // lower alpha (a-f)\n                result = true;\n            }\n        }\n        else {\n            if ((inputCode > 47 && inputCode < 58)) {\n                // numeric (0-9)\n                result = true;\n            }\n        }\n        if (!result) {\n            e.preventDefault();\n        }\n    }\n    onColorChangeHandler() {\n        if (typeof this.props.onColorChange === 'function') {\n            this.props.onColorChange(this.state.colorValues);\n        }\n    }\n}\nMessagepointReactColor.COLOR_BLOCK_SLIDER_RADIUS_PX = 25;\nmodule.exports = MessagepointReactColor;\n\n\n//# sourceURL=webpack://MessagepointReactColor/./MessagepointReactColor/MessagepointReactColor.js?");

/***/ }),

/***/ "./node_modules/color-convert/conversions.js":
/*!***************************************************!*\
  !*** ./node_modules/color-convert/conversions.js ***!
  \***************************************************/
/*! no static exports found */
/*! all exports used */
/***/ (function(module, exports, __webpack_require__) {

eval("/* MIT license */\n/* eslint-disable no-mixed-operators */\nconst cssKeywords = __webpack_require__(/*! color-name */ \"./node_modules/color-name/index.js\");\n\n// NOTE: conversions should only return primitive values (i.e. arrays, or\n//       values that give correct `typeof` results).\n//       do not use box values types (i.e. Number(), String(), etc.)\n\nconst reverseKeywords = {};\nfor (const key of Object.keys(cssKeywords)) {\n\treverseKeywords[cssKeywords[key]] = key;\n}\n\nconst convert = {\n\trgb: {channels: 3, labels: 'rgb'},\n\thsl: {channels: 3, labels: 'hsl'},\n\thsv: {channels: 3, labels: 'hsv'},\n\thwb: {channels: 3, labels: 'hwb'},\n\tcmyk: {channels: 4, labels: 'cmyk'},\n\txyz: {channels: 3, labels: 'xyz'},\n\tlab: {channels: 3, labels: 'lab'},\n\tlch: {channels: 3, labels: 'lch'},\n\thex: {channels: 1, labels: ['hex']},\n\tkeyword: {channels: 1, labels: ['keyword']},\n\tansi16: {channels: 1, labels: ['ansi16']},\n\tansi256: {channels: 1, labels: ['ansi256']},\n\thcg: {channels: 3, labels: ['h', 'c', 'g']},\n\tapple: {channels: 3, labels: ['r16', 'g16', 'b16']},\n\tgray: {channels: 1, labels: ['gray']}\n};\n\nmodule.exports = convert;\n\n// Hide .channels and .labels properties\nfor (const model of Object.keys(convert)) {\n\tif (!('channels' in convert[model])) {\n\t\tthrow new Error('missing channels property: ' + model);\n\t}\n\n\tif (!('labels' in convert[model])) {\n\t\tthrow new Error('missing channel labels property: ' + model);\n\t}\n\n\tif (convert[model].labels.length !== convert[model].channels) {\n\t\tthrow new Error('channel and label counts mismatch: ' + model);\n\t}\n\n\tconst {channels, labels} = convert[model];\n\tdelete convert[model].channels;\n\tdelete convert[model].labels;\n\tObject.defineProperty(convert[model], 'channels', {value: channels});\n\tObject.defineProperty(convert[model], 'labels', {value: labels});\n}\n\nconvert.rgb.hsl = function (rgb) {\n\tconst r = rgb[0] / 255;\n\tconst g = rgb[1] / 255;\n\tconst b = rgb[2] / 255;\n\tconst min = Math.min(r, g, b);\n\tconst max = Math.max(r, g, b);\n\tconst delta = max - min;\n\tlet h;\n\tlet s;\n\n\tif (max === min) {\n\t\th = 0;\n\t} else if (r === max) {\n\t\th = (g - b) / delta;\n\t} else if (g === max) {\n\t\th = 2 + (b - r) / delta;\n\t} else if (b === max) {\n\t\th = 4 + (r - g) / delta;\n\t}\n\n\th = Math.min(h * 60, 360);\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tconst l = (min + max) / 2;\n\n\tif (max === min) {\n\t\ts = 0;\n\t} else if (l <= 0.5) {\n\t\ts = delta / (max + min);\n\t} else {\n\t\ts = delta / (2 - max - min);\n\t}\n\n\treturn [h, s * 100, l * 100];\n};\n\nconvert.rgb.hsv = function (rgb) {\n\tlet rdif;\n\tlet gdif;\n\tlet bdif;\n\tlet h;\n\tlet s;\n\n\tconst r = rgb[0] / 255;\n\tconst g = rgb[1] / 255;\n\tconst b = rgb[2] / 255;\n\tconst v = Math.max(r, g, b);\n\tconst diff = v - Math.min(r, g, b);\n\tconst diffc = function (c) {\n\t\treturn (v - c) / 6 / diff + 1 / 2;\n\t};\n\n\tif (diff === 0) {\n\t\th = 0;\n\t\ts = 0;\n\t} else {\n\t\ts = diff / v;\n\t\trdif = diffc(r);\n\t\tgdif = diffc(g);\n\t\tbdif = diffc(b);\n\n\t\tif (r === v) {\n\t\t\th = bdif - gdif;\n\t\t} else if (g === v) {\n\t\t\th = (1 / 3) + rdif - bdif;\n\t\t} else if (b === v) {\n\t\t\th = (2 / 3) + gdif - rdif;\n\t\t}\n\n\t\tif (h < 0) {\n\t\t\th += 1;\n\t\t} else if (h > 1) {\n\t\t\th -= 1;\n\t\t}\n\t}\n\n\treturn [\n\t\th * 360,\n\t\ts * 100,\n\t\tv * 100\n\t];\n};\n\nconvert.rgb.hwb = function (rgb) {\n\tconst r = rgb[0];\n\tconst g = rgb[1];\n\tlet b = rgb[2];\n\tconst h = convert.rgb.hsl(rgb)[0];\n\tconst w = 1 / 255 * Math.min(r, Math.min(g, b));\n\n\tb = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n\n\treturn [h, w * 100, b * 100];\n};\n\nconvert.rgb.cmyk = function (rgb) {\n\tconst r = rgb[0] / 255;\n\tconst g = rgb[1] / 255;\n\tconst b = rgb[2] / 255;\n\n\tconst k = Math.min(1 - r, 1 - g, 1 - b);\n\tconst c = (1 - r - k) / (1 - k) || 0;\n\tconst m = (1 - g - k) / (1 - k) || 0;\n\tconst y = (1 - b - k) / (1 - k) || 0;\n\n\treturn [c * 100, m * 100, y * 100, k * 100];\n};\n\nfunction comparativeDistance(x, y) {\n\t/*\n\t\tSee https://en.m.wikipedia.org/wiki/Euclidean_distance#Squared_Euclidean_distance\n\t*/\n\treturn (\n\t\t((x[0] - y[0]) ** 2) +\n\t\t((x[1] - y[1]) ** 2) +\n\t\t((x[2] - y[2]) ** 2)\n\t);\n}\n\nconvert.rgb.keyword = function (rgb) {\n\tconst reversed = reverseKeywords[rgb];\n\tif (reversed) {\n\t\treturn reversed;\n\t}\n\n\tlet currentClosestDistance = Infinity;\n\tlet currentClosestKeyword;\n\n\tfor (const keyword of Object.keys(cssKeywords)) {\n\t\tconst value = cssKeywords[keyword];\n\n\t\t// Compute comparative distance\n\t\tconst distance = comparativeDistance(rgb, value);\n\n\t\t// Check if its less, if so set as closest\n\t\tif (distance < currentClosestDistance) {\n\t\t\tcurrentClosestDistance = distance;\n\t\t\tcurrentClosestKeyword = keyword;\n\t\t}\n\t}\n\n\treturn currentClosestKeyword;\n};\n\nconvert.keyword.rgb = function (keyword) {\n\treturn cssKeywords[keyword];\n};\n\nconvert.rgb.xyz = function (rgb) {\n\tlet r = rgb[0] / 255;\n\tlet g = rgb[1] / 255;\n\tlet b = rgb[2] / 255;\n\n\t// Assume sRGB\n\tr = r > 0.04045 ? (((r + 0.055) / 1.055) ** 2.4) : (r / 12.92);\n\tg = g > 0.04045 ? (((g + 0.055) / 1.055) ** 2.4) : (g / 12.92);\n\tb = b > 0.04045 ? (((b + 0.055) / 1.055) ** 2.4) : (b / 12.92);\n\n\tconst x = (r * 0.4124) + (g * 0.3576) + (b * 0.1805);\n\tconst y = (r * 0.2126) + (g * 0.7152) + (b * 0.0722);\n\tconst z = (r * 0.0193) + (g * 0.1192) + (b * 0.9505);\n\n\treturn [x * 100, y * 100, z * 100];\n};\n\nconvert.rgb.lab = function (rgb) {\n\tconst xyz = convert.rgb.xyz(rgb);\n\tlet x = xyz[0];\n\tlet y = xyz[1];\n\tlet z = xyz[2];\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? (x ** (1 / 3)) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? (y ** (1 / 3)) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? (z ** (1 / 3)) : (7.787 * z) + (16 / 116);\n\n\tconst l = (116 * y) - 16;\n\tconst a = 500 * (x - y);\n\tconst b = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.hsl.rgb = function (hsl) {\n\tconst h = hsl[0] / 360;\n\tconst s = hsl[1] / 100;\n\tconst l = hsl[2] / 100;\n\tlet t2;\n\tlet t3;\n\tlet val;\n\n\tif (s === 0) {\n\t\tval = l * 255;\n\t\treturn [val, val, val];\n\t}\n\n\tif (l < 0.5) {\n\t\tt2 = l * (1 + s);\n\t} else {\n\t\tt2 = l + s - l * s;\n\t}\n\n\tconst t1 = 2 * l - t2;\n\n\tconst rgb = [0, 0, 0];\n\tfor (let i = 0; i < 3; i++) {\n\t\tt3 = h + 1 / 3 * -(i - 1);\n\t\tif (t3 < 0) {\n\t\t\tt3++;\n\t\t}\n\n\t\tif (t3 > 1) {\n\t\t\tt3--;\n\t\t}\n\n\t\tif (6 * t3 < 1) {\n\t\t\tval = t1 + (t2 - t1) * 6 * t3;\n\t\t} else if (2 * t3 < 1) {\n\t\t\tval = t2;\n\t\t} else if (3 * t3 < 2) {\n\t\t\tval = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n\t\t} else {\n\t\t\tval = t1;\n\t\t}\n\n\t\trgb[i] = val * 255;\n\t}\n\n\treturn rgb;\n};\n\nconvert.hsl.hsv = function (hsl) {\n\tconst h = hsl[0];\n\tlet s = hsl[1] / 100;\n\tlet l = hsl[2] / 100;\n\tlet smin = s;\n\tconst lmin = Math.max(l, 0.01);\n\n\tl *= 2;\n\ts *= (l <= 1) ? l : 2 - l;\n\tsmin *= lmin <= 1 ? lmin : 2 - lmin;\n\tconst v = (l + s) / 2;\n\tconst sv = l === 0 ? (2 * smin) / (lmin + smin) : (2 * s) / (l + s);\n\n\treturn [h, sv * 100, v * 100];\n};\n\nconvert.hsv.rgb = function (hsv) {\n\tconst h = hsv[0] / 60;\n\tconst s = hsv[1] / 100;\n\tlet v = hsv[2] / 100;\n\tconst hi = Math.floor(h) % 6;\n\n\tconst f = h - Math.floor(h);\n\tconst p = 255 * v * (1 - s);\n\tconst q = 255 * v * (1 - (s * f));\n\tconst t = 255 * v * (1 - (s * (1 - f)));\n\tv *= 255;\n\n\tswitch (hi) {\n\t\tcase 0:\n\t\t\treturn [v, t, p];\n\t\tcase 1:\n\t\t\treturn [q, v, p];\n\t\tcase 2:\n\t\t\treturn [p, v, t];\n\t\tcase 3:\n\t\t\treturn [p, q, v];\n\t\tcase 4:\n\t\t\treturn [t, p, v];\n\t\tcase 5:\n\t\t\treturn [v, p, q];\n\t}\n};\n\nconvert.hsv.hsl = function (hsv) {\n\tconst h = hsv[0];\n\tconst s = hsv[1] / 100;\n\tconst v = hsv[2] / 100;\n\tconst vmin = Math.max(v, 0.01);\n\tlet sl;\n\tlet l;\n\n\tl = (2 - s) * v;\n\tconst lmin = (2 - s) * vmin;\n\tsl = s * vmin;\n\tsl /= (lmin <= 1) ? lmin : 2 - lmin;\n\tsl = sl || 0;\n\tl /= 2;\n\n\treturn [h, sl * 100, l * 100];\n};\n\n// http://dev.w3.org/csswg/css-color/#hwb-to-rgb\nconvert.hwb.rgb = function (hwb) {\n\tconst h = hwb[0] / 360;\n\tlet wh = hwb[1] / 100;\n\tlet bl = hwb[2] / 100;\n\tconst ratio = wh + bl;\n\tlet f;\n\n\t// Wh + bl cant be > 1\n\tif (ratio > 1) {\n\t\twh /= ratio;\n\t\tbl /= ratio;\n\t}\n\n\tconst i = Math.floor(6 * h);\n\tconst v = 1 - bl;\n\tf = 6 * h - i;\n\n\tif ((i & 0x01) !== 0) {\n\t\tf = 1 - f;\n\t}\n\n\tconst n = wh + f * (v - wh); // Linear interpolation\n\n\tlet r;\n\tlet g;\n\tlet b;\n\t/* eslint-disable max-statements-per-line,no-multi-spaces */\n\tswitch (i) {\n\t\tdefault:\n\t\tcase 6:\n\t\tcase 0: r = v;  g = n;  b = wh; break;\n\t\tcase 1: r = n;  g = v;  b = wh; break;\n\t\tcase 2: r = wh; g = v;  b = n; break;\n\t\tcase 3: r = wh; g = n;  b = v; break;\n\t\tcase 4: r = n;  g = wh; b = v; break;\n\t\tcase 5: r = v;  g = wh; b = n; break;\n\t}\n\t/* eslint-enable max-statements-per-line,no-multi-spaces */\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.cmyk.rgb = function (cmyk) {\n\tconst c = cmyk[0] / 100;\n\tconst m = cmyk[1] / 100;\n\tconst y = cmyk[2] / 100;\n\tconst k = cmyk[3] / 100;\n\n\tconst r = 1 - Math.min(1, c * (1 - k) + k);\n\tconst g = 1 - Math.min(1, m * (1 - k) + k);\n\tconst b = 1 - Math.min(1, y * (1 - k) + k);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.rgb = function (xyz) {\n\tconst x = xyz[0] / 100;\n\tconst y = xyz[1] / 100;\n\tconst z = xyz[2] / 100;\n\tlet r;\n\tlet g;\n\tlet b;\n\n\tr = (x * 3.2406) + (y * -1.5372) + (z * -0.4986);\n\tg = (x * -0.9689) + (y * 1.8758) + (z * 0.0415);\n\tb = (x * 0.0557) + (y * -0.2040) + (z * 1.0570);\n\n\t// Assume sRGB\n\tr = r > 0.0031308\n\t\t? ((1.055 * (r ** (1.0 / 2.4))) - 0.055)\n\t\t: r * 12.92;\n\n\tg = g > 0.0031308\n\t\t? ((1.055 * (g ** (1.0 / 2.4))) - 0.055)\n\t\t: g * 12.92;\n\n\tb = b > 0.0031308\n\t\t? ((1.055 * (b ** (1.0 / 2.4))) - 0.055)\n\t\t: b * 12.92;\n\n\tr = Math.min(Math.max(0, r), 1);\n\tg = Math.min(Math.max(0, g), 1);\n\tb = Math.min(Math.max(0, b), 1);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.lab = function (xyz) {\n\tlet x = xyz[0];\n\tlet y = xyz[1];\n\tlet z = xyz[2];\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? (x ** (1 / 3)) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? (y ** (1 / 3)) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? (z ** (1 / 3)) : (7.787 * z) + (16 / 116);\n\n\tconst l = (116 * y) - 16;\n\tconst a = 500 * (x - y);\n\tconst b = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.lab.xyz = function (lab) {\n\tconst l = lab[0];\n\tconst a = lab[1];\n\tconst b = lab[2];\n\tlet x;\n\tlet y;\n\tlet z;\n\n\ty = (l + 16) / 116;\n\tx = a / 500 + y;\n\tz = y - b / 200;\n\n\tconst y2 = y ** 3;\n\tconst x2 = x ** 3;\n\tconst z2 = z ** 3;\n\ty = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n\tx = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n\tz = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n\n\tx *= 95.047;\n\ty *= 100;\n\tz *= 108.883;\n\n\treturn [x, y, z];\n};\n\nconvert.lab.lch = function (lab) {\n\tconst l = lab[0];\n\tconst a = lab[1];\n\tconst b = lab[2];\n\tlet h;\n\n\tconst hr = Math.atan2(b, a);\n\th = hr * 360 / 2 / Math.PI;\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tconst c = Math.sqrt(a * a + b * b);\n\n\treturn [l, c, h];\n};\n\nconvert.lch.lab = function (lch) {\n\tconst l = lch[0];\n\tconst c = lch[1];\n\tconst h = lch[2];\n\n\tconst hr = h / 360 * 2 * Math.PI;\n\tconst a = c * Math.cos(hr);\n\tconst b = c * Math.sin(hr);\n\n\treturn [l, a, b];\n};\n\nconvert.rgb.ansi16 = function (args, saturation = null) {\n\tconst [r, g, b] = args;\n\tlet value = saturation === null ? convert.rgb.hsv(args)[2] : saturation; // Hsv -> ansi16 optimization\n\n\tvalue = Math.round(value / 50);\n\n\tif (value === 0) {\n\t\treturn 30;\n\t}\n\n\tlet ansi = 30\n\t\t+ ((Math.round(b / 255) << 2)\n\t\t| (Math.round(g / 255) << 1)\n\t\t| Math.round(r / 255));\n\n\tif (value === 2) {\n\t\tansi += 60;\n\t}\n\n\treturn ansi;\n};\n\nconvert.hsv.ansi16 = function (args) {\n\t// Optimization here; we already know the value and don't need to get\n\t// it converted for us.\n\treturn convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n};\n\nconvert.rgb.ansi256 = function (args) {\n\tconst r = args[0];\n\tconst g = args[1];\n\tconst b = args[2];\n\n\t// We use the extended greyscale palette here, with the exception of\n\t// black and white. normal palette only has 4 greyscale shades.\n\tif (r === g && g === b) {\n\t\tif (r < 8) {\n\t\t\treturn 16;\n\t\t}\n\n\t\tif (r > 248) {\n\t\t\treturn 231;\n\t\t}\n\n\t\treturn Math.round(((r - 8) / 247) * 24) + 232;\n\t}\n\n\tconst ansi = 16\n\t\t+ (36 * Math.round(r / 255 * 5))\n\t\t+ (6 * Math.round(g / 255 * 5))\n\t\t+ Math.round(b / 255 * 5);\n\n\treturn ansi;\n};\n\nconvert.ansi16.rgb = function (args) {\n\tlet color = args % 10;\n\n\t// Handle greyscale\n\tif (color === 0 || color === 7) {\n\t\tif (args > 50) {\n\t\t\tcolor += 3.5;\n\t\t}\n\n\t\tcolor = color / 10.5 * 255;\n\n\t\treturn [color, color, color];\n\t}\n\n\tconst mult = (~~(args > 50) + 1) * 0.5;\n\tconst r = ((color & 1) * mult) * 255;\n\tconst g = (((color >> 1) & 1) * mult) * 255;\n\tconst b = (((color >> 2) & 1) * mult) * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.ansi256.rgb = function (args) {\n\t// Handle greyscale\n\tif (args >= 232) {\n\t\tconst c = (args - 232) * 10 + 8;\n\t\treturn [c, c, c];\n\t}\n\n\targs -= 16;\n\n\tlet rem;\n\tconst r = Math.floor(args / 36) / 5 * 255;\n\tconst g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n\tconst b = (rem % 6) / 5 * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hex = function (args) {\n\tconst integer = ((Math.round(args[0]) & 0xFF) << 16)\n\t\t+ ((Math.round(args[1]) & 0xFF) << 8)\n\t\t+ (Math.round(args[2]) & 0xFF);\n\n\tconst string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.hex.rgb = function (args) {\n\tconst match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n\tif (!match) {\n\t\treturn [0, 0, 0];\n\t}\n\n\tlet colorString = match[0];\n\n\tif (match[0].length === 3) {\n\t\tcolorString = colorString.split('').map(char => {\n\t\t\treturn char + char;\n\t\t}).join('');\n\t}\n\n\tconst integer = parseInt(colorString, 16);\n\tconst r = (integer >> 16) & 0xFF;\n\tconst g = (integer >> 8) & 0xFF;\n\tconst b = integer & 0xFF;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hcg = function (rgb) {\n\tconst r = rgb[0] / 255;\n\tconst g = rgb[1] / 255;\n\tconst b = rgb[2] / 255;\n\tconst max = Math.max(Math.max(r, g), b);\n\tconst min = Math.min(Math.min(r, g), b);\n\tconst chroma = (max - min);\n\tlet grayscale;\n\tlet hue;\n\n\tif (chroma < 1) {\n\t\tgrayscale = min / (1 - chroma);\n\t} else {\n\t\tgrayscale = 0;\n\t}\n\n\tif (chroma <= 0) {\n\t\thue = 0;\n\t} else\n\tif (max === r) {\n\t\thue = ((g - b) / chroma) % 6;\n\t} else\n\tif (max === g) {\n\t\thue = 2 + (b - r) / chroma;\n\t} else {\n\t\thue = 4 + (r - g) / chroma + 4;\n\t}\n\n\thue /= 6;\n\thue %= 1;\n\n\treturn [hue * 360, chroma * 100, grayscale * 100];\n};\n\nconvert.hsl.hcg = function (hsl) {\n\tconst s = hsl[1] / 100;\n\tconst l = hsl[2] / 100;\n\n\tconst c = l < 0.5 ? (2.0 * s * l) : (2.0 * s * (1.0 - l));\n\n\tlet f = 0;\n\tif (c < 1.0) {\n\t\tf = (l - 0.5 * c) / (1.0 - c);\n\t}\n\n\treturn [hsl[0], c * 100, f * 100];\n};\n\nconvert.hsv.hcg = function (hsv) {\n\tconst s = hsv[1] / 100;\n\tconst v = hsv[2] / 100;\n\n\tconst c = s * v;\n\tlet f = 0;\n\n\tif (c < 1.0) {\n\t\tf = (v - c) / (1 - c);\n\t}\n\n\treturn [hsv[0], c * 100, f * 100];\n};\n\nconvert.hcg.rgb = function (hcg) {\n\tconst h = hcg[0] / 360;\n\tconst c = hcg[1] / 100;\n\tconst g = hcg[2] / 100;\n\n\tif (c === 0.0) {\n\t\treturn [g * 255, g * 255, g * 255];\n\t}\n\n\tconst pure = [0, 0, 0];\n\tconst hi = (h % 1) * 6;\n\tconst v = hi % 1;\n\tconst w = 1 - v;\n\tlet mg = 0;\n\n\t/* eslint-disable max-statements-per-line */\n\tswitch (Math.floor(hi)) {\n\t\tcase 0:\n\t\t\tpure[0] = 1; pure[1] = v; pure[2] = 0; break;\n\t\tcase 1:\n\t\t\tpure[0] = w; pure[1] = 1; pure[2] = 0; break;\n\t\tcase 2:\n\t\t\tpure[0] = 0; pure[1] = 1; pure[2] = v; break;\n\t\tcase 3:\n\t\t\tpure[0] = 0; pure[1] = w; pure[2] = 1; break;\n\t\tcase 4:\n\t\t\tpure[0] = v; pure[1] = 0; pure[2] = 1; break;\n\t\tdefault:\n\t\t\tpure[0] = 1; pure[1] = 0; pure[2] = w;\n\t}\n\t/* eslint-enable max-statements-per-line */\n\n\tmg = (1.0 - c) * g;\n\n\treturn [\n\t\t(c * pure[0] + mg) * 255,\n\t\t(c * pure[1] + mg) * 255,\n\t\t(c * pure[2] + mg) * 255\n\t];\n};\n\nconvert.hcg.hsv = function (hcg) {\n\tconst c = hcg[1] / 100;\n\tconst g = hcg[2] / 100;\n\n\tconst v = c + g * (1.0 - c);\n\tlet f = 0;\n\n\tif (v > 0.0) {\n\t\tf = c / v;\n\t}\n\n\treturn [hcg[0], f * 100, v * 100];\n};\n\nconvert.hcg.hsl = function (hcg) {\n\tconst c = hcg[1] / 100;\n\tconst g = hcg[2] / 100;\n\n\tconst l = g * (1.0 - c) + 0.5 * c;\n\tlet s = 0;\n\n\tif (l > 0.0 && l < 0.5) {\n\t\ts = c / (2 * l);\n\t} else\n\tif (l >= 0.5 && l < 1.0) {\n\t\ts = c / (2 * (1 - l));\n\t}\n\n\treturn [hcg[0], s * 100, l * 100];\n};\n\nconvert.hcg.hwb = function (hcg) {\n\tconst c = hcg[1] / 100;\n\tconst g = hcg[2] / 100;\n\tconst v = c + g * (1.0 - c);\n\treturn [hcg[0], (v - c) * 100, (1 - v) * 100];\n};\n\nconvert.hwb.hcg = function (hwb) {\n\tconst w = hwb[1] / 100;\n\tconst b = hwb[2] / 100;\n\tconst v = 1 - b;\n\tconst c = v - w;\n\tlet g = 0;\n\n\tif (c < 1) {\n\t\tg = (v - c) / (1 - c);\n\t}\n\n\treturn [hwb[0], c * 100, g * 100];\n};\n\nconvert.apple.rgb = function (apple) {\n\treturn [(apple[0] / 65535) * 255, (apple[1] / 65535) * 255, (apple[2] / 65535) * 255];\n};\n\nconvert.rgb.apple = function (rgb) {\n\treturn [(rgb[0] / 255) * 65535, (rgb[1] / 255) * 65535, (rgb[2] / 255) * 65535];\n};\n\nconvert.gray.rgb = function (args) {\n\treturn [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];\n};\n\nconvert.gray.hsl = function (args) {\n\treturn [0, 0, args[0]];\n};\n\nconvert.gray.hsv = convert.gray.hsl;\n\nconvert.gray.hwb = function (gray) {\n\treturn [0, 100, gray[0]];\n};\n\nconvert.gray.cmyk = function (gray) {\n\treturn [0, 0, 0, gray[0]];\n};\n\nconvert.gray.lab = function (gray) {\n\treturn [gray[0], 0, 0];\n};\n\nconvert.gray.hex = function (gray) {\n\tconst val = Math.round(gray[0] / 100 * 255) & 0xFF;\n\tconst integer = (val << 16) + (val << 8) + val;\n\n\tconst string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.rgb.gray = function (rgb) {\n\tconst val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n\treturn [val / 255 * 100];\n};\n\n\n//# sourceURL=webpack://MessagepointReactColor/./node_modules/color-convert/conversions.js?");

/***/ }),

/***/ "./node_modules/color-convert/index.js":
/*!*********************************************!*\
  !*** ./node_modules/color-convert/index.js ***!
  \*********************************************/
/*! no static exports found */
/*! all exports used */
/***/ (function(module, exports, __webpack_require__) {

eval("const conversions = __webpack_require__(/*! ./conversions */ \"./node_modules/color-convert/conversions.js\");\nconst route = __webpack_require__(/*! ./route */ \"./node_modules/color-convert/route.js\");\n\nconst convert = {};\n\nconst models = Object.keys(conversions);\n\nfunction wrapRaw(fn) {\n\tconst wrappedFn = function (...args) {\n\t\tconst arg0 = args[0];\n\t\tif (arg0 === undefined || arg0 === null) {\n\t\t\treturn arg0;\n\t\t}\n\n\t\tif (arg0.length > 1) {\n\t\t\targs = arg0;\n\t\t}\n\n\t\treturn fn(args);\n\t};\n\n\t// Preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nfunction wrapRounded(fn) {\n\tconst wrappedFn = function (...args) {\n\t\tconst arg0 = args[0];\n\n\t\tif (arg0 === undefined || arg0 === null) {\n\t\t\treturn arg0;\n\t\t}\n\n\t\tif (arg0.length > 1) {\n\t\t\targs = arg0;\n\t\t}\n\n\t\tconst result = fn(args);\n\n\t\t// We're assuming the result is an array here.\n\t\t// see notice in conversions.js; don't use box types\n\t\t// in conversion functions.\n\t\tif (typeof result === 'object') {\n\t\t\tfor (let len = result.length, i = 0; i < len; i++) {\n\t\t\t\tresult[i] = Math.round(result[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t};\n\n\t// Preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nmodels.forEach(fromModel => {\n\tconvert[fromModel] = {};\n\n\tObject.defineProperty(convert[fromModel], 'channels', {value: conversions[fromModel].channels});\n\tObject.defineProperty(convert[fromModel], 'labels', {value: conversions[fromModel].labels});\n\n\tconst routes = route(fromModel);\n\tconst routeModels = Object.keys(routes);\n\n\trouteModels.forEach(toModel => {\n\t\tconst fn = routes[toModel];\n\n\t\tconvert[fromModel][toModel] = wrapRounded(fn);\n\t\tconvert[fromModel][toModel].raw = wrapRaw(fn);\n\t});\n});\n\nmodule.exports = convert;\n\n\n//# sourceURL=webpack://MessagepointReactColor/./node_modules/color-convert/index.js?");

/***/ }),

/***/ "./node_modules/color-convert/route.js":
/*!*********************************************!*\
  !*** ./node_modules/color-convert/route.js ***!
  \*********************************************/
/*! no static exports found */
/*! all exports used */
/***/ (function(module, exports, __webpack_require__) {

eval("const conversions = __webpack_require__(/*! ./conversions */ \"./node_modules/color-convert/conversions.js\");\n\n/*\n\tThis function routes a model to all other models.\n\n\tall functions that are routed have a property `.conversion` attached\n\tto the returned synthetic function. This property is an array\n\tof strings, each with the steps in between the 'from' and 'to'\n\tcolor models (inclusive).\n\n\tconversions that are not possible simply are not included.\n*/\n\nfunction buildGraph() {\n\tconst graph = {};\n\t// https://jsperf.com/object-keys-vs-for-in-with-closure/3\n\tconst models = Object.keys(conversions);\n\n\tfor (let len = models.length, i = 0; i < len; i++) {\n\t\tgraph[models[i]] = {\n\t\t\t// http://jsperf.com/1-vs-infinity\n\t\t\t// micro-opt, but this is simple.\n\t\t\tdistance: -1,\n\t\t\tparent: null\n\t\t};\n\t}\n\n\treturn graph;\n}\n\n// https://en.wikipedia.org/wiki/Breadth-first_search\nfunction deriveBFS(fromModel) {\n\tconst graph = buildGraph();\n\tconst queue = [fromModel]; // Unshift -> queue -> pop\n\n\tgraph[fromModel].distance = 0;\n\n\twhile (queue.length) {\n\t\tconst current = queue.pop();\n\t\tconst adjacents = Object.keys(conversions[current]);\n\n\t\tfor (let len = adjacents.length, i = 0; i < len; i++) {\n\t\t\tconst adjacent = adjacents[i];\n\t\t\tconst node = graph[adjacent];\n\n\t\t\tif (node.distance === -1) {\n\t\t\t\tnode.distance = graph[current].distance + 1;\n\t\t\t\tnode.parent = current;\n\t\t\t\tqueue.unshift(adjacent);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn graph;\n}\n\nfunction link(from, to) {\n\treturn function (args) {\n\t\treturn to(from(args));\n\t};\n}\n\nfunction wrapConversion(toModel, graph) {\n\tconst path = [graph[toModel].parent, toModel];\n\tlet fn = conversions[graph[toModel].parent][toModel];\n\n\tlet cur = graph[toModel].parent;\n\twhile (graph[cur].parent) {\n\t\tpath.unshift(graph[cur].parent);\n\t\tfn = link(conversions[graph[cur].parent][cur], fn);\n\t\tcur = graph[cur].parent;\n\t}\n\n\tfn.conversion = path;\n\treturn fn;\n}\n\nmodule.exports = function (fromModel) {\n\tconst graph = deriveBFS(fromModel);\n\tconst conversion = {};\n\n\tconst models = Object.keys(graph);\n\tfor (let len = models.length, i = 0; i < len; i++) {\n\t\tconst toModel = models[i];\n\t\tconst node = graph[toModel];\n\n\t\tif (node.parent === null) {\n\t\t\t// No possible conversion, or this node is the source model.\n\t\t\tcontinue;\n\t\t}\n\n\t\tconversion[toModel] = wrapConversion(toModel, graph);\n\t}\n\n\treturn conversion;\n};\n\n\n\n//# sourceURL=webpack://MessagepointReactColor/./node_modules/color-convert/route.js?");

/***/ }),

/***/ "./node_modules/color-name/index.js":
/*!******************************************!*\
  !*** ./node_modules/color-name/index.js ***!
  \******************************************/
/*! no static exports found */
/*! all exports used */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\r\n\r\nmodule.exports = {\r\n\t\"aliceblue\": [240, 248, 255],\r\n\t\"antiquewhite\": [250, 235, 215],\r\n\t\"aqua\": [0, 255, 255],\r\n\t\"aquamarine\": [127, 255, 212],\r\n\t\"azure\": [240, 255, 255],\r\n\t\"beige\": [245, 245, 220],\r\n\t\"bisque\": [255, 228, 196],\r\n\t\"black\": [0, 0, 0],\r\n\t\"blanchedalmond\": [255, 235, 205],\r\n\t\"blue\": [0, 0, 255],\r\n\t\"blueviolet\": [138, 43, 226],\r\n\t\"brown\": [165, 42, 42],\r\n\t\"burlywood\": [222, 184, 135],\r\n\t\"cadetblue\": [95, 158, 160],\r\n\t\"chartreuse\": [127, 255, 0],\r\n\t\"chocolate\": [210, 105, 30],\r\n\t\"coral\": [255, 127, 80],\r\n\t\"cornflowerblue\": [100, 149, 237],\r\n\t\"cornsilk\": [255, 248, 220],\r\n\t\"crimson\": [220, 20, 60],\r\n\t\"cyan\": [0, 255, 255],\r\n\t\"darkblue\": [0, 0, 139],\r\n\t\"darkcyan\": [0, 139, 139],\r\n\t\"darkgoldenrod\": [184, 134, 11],\r\n\t\"darkgray\": [169, 169, 169],\r\n\t\"darkgreen\": [0, 100, 0],\r\n\t\"darkgrey\": [169, 169, 169],\r\n\t\"darkkhaki\": [189, 183, 107],\r\n\t\"darkmagenta\": [139, 0, 139],\r\n\t\"darkolivegreen\": [85, 107, 47],\r\n\t\"darkorange\": [255, 140, 0],\r\n\t\"darkorchid\": [153, 50, 204],\r\n\t\"darkred\": [139, 0, 0],\r\n\t\"darksalmon\": [233, 150, 122],\r\n\t\"darkseagreen\": [143, 188, 143],\r\n\t\"darkslateblue\": [72, 61, 139],\r\n\t\"darkslategray\": [47, 79, 79],\r\n\t\"darkslategrey\": [47, 79, 79],\r\n\t\"darkturquoise\": [0, 206, 209],\r\n\t\"darkviolet\": [148, 0, 211],\r\n\t\"deeppink\": [255, 20, 147],\r\n\t\"deepskyblue\": [0, 191, 255],\r\n\t\"dimgray\": [105, 105, 105],\r\n\t\"dimgrey\": [105, 105, 105],\r\n\t\"dodgerblue\": [30, 144, 255],\r\n\t\"firebrick\": [178, 34, 34],\r\n\t\"floralwhite\": [255, 250, 240],\r\n\t\"forestgreen\": [34, 139, 34],\r\n\t\"fuchsia\": [255, 0, 255],\r\n\t\"gainsboro\": [220, 220, 220],\r\n\t\"ghostwhite\": [248, 248, 255],\r\n\t\"gold\": [255, 215, 0],\r\n\t\"goldenrod\": [218, 165, 32],\r\n\t\"gray\": [128, 128, 128],\r\n\t\"green\": [0, 128, 0],\r\n\t\"greenyellow\": [173, 255, 47],\r\n\t\"grey\": [128, 128, 128],\r\n\t\"honeydew\": [240, 255, 240],\r\n\t\"hotpink\": [255, 105, 180],\r\n\t\"indianred\": [205, 92, 92],\r\n\t\"indigo\": [75, 0, 130],\r\n\t\"ivory\": [255, 255, 240],\r\n\t\"khaki\": [240, 230, 140],\r\n\t\"lavender\": [230, 230, 250],\r\n\t\"lavenderblush\": [255, 240, 245],\r\n\t\"lawngreen\": [124, 252, 0],\r\n\t\"lemonchiffon\": [255, 250, 205],\r\n\t\"lightblue\": [173, 216, 230],\r\n\t\"lightcoral\": [240, 128, 128],\r\n\t\"lightcyan\": [224, 255, 255],\r\n\t\"lightgoldenrodyellow\": [250, 250, 210],\r\n\t\"lightgray\": [211, 211, 211],\r\n\t\"lightgreen\": [144, 238, 144],\r\n\t\"lightgrey\": [211, 211, 211],\r\n\t\"lightpink\": [255, 182, 193],\r\n\t\"lightsalmon\": [255, 160, 122],\r\n\t\"lightseagreen\": [32, 178, 170],\r\n\t\"lightskyblue\": [135, 206, 250],\r\n\t\"lightslategray\": [119, 136, 153],\r\n\t\"lightslategrey\": [119, 136, 153],\r\n\t\"lightsteelblue\": [176, 196, 222],\r\n\t\"lightyellow\": [255, 255, 224],\r\n\t\"lime\": [0, 255, 0],\r\n\t\"limegreen\": [50, 205, 50],\r\n\t\"linen\": [250, 240, 230],\r\n\t\"magenta\": [255, 0, 255],\r\n\t\"maroon\": [128, 0, 0],\r\n\t\"mediumaquamarine\": [102, 205, 170],\r\n\t\"mediumblue\": [0, 0, 205],\r\n\t\"mediumorchid\": [186, 85, 211],\r\n\t\"mediumpurple\": [147, 112, 219],\r\n\t\"mediumseagreen\": [60, 179, 113],\r\n\t\"mediumslateblue\": [123, 104, 238],\r\n\t\"mediumspringgreen\": [0, 250, 154],\r\n\t\"mediumturquoise\": [72, 209, 204],\r\n\t\"mediumvioletred\": [199, 21, 133],\r\n\t\"midnightblue\": [25, 25, 112],\r\n\t\"mintcream\": [245, 255, 250],\r\n\t\"mistyrose\": [255, 228, 225],\r\n\t\"moccasin\": [255, 228, 181],\r\n\t\"navajowhite\": [255, 222, 173],\r\n\t\"navy\": [0, 0, 128],\r\n\t\"oldlace\": [253, 245, 230],\r\n\t\"olive\": [128, 128, 0],\r\n\t\"olivedrab\": [107, 142, 35],\r\n\t\"orange\": [255, 165, 0],\r\n\t\"orangered\": [255, 69, 0],\r\n\t\"orchid\": [218, 112, 214],\r\n\t\"palegoldenrod\": [238, 232, 170],\r\n\t\"palegreen\": [152, 251, 152],\r\n\t\"paleturquoise\": [175, 238, 238],\r\n\t\"palevioletred\": [219, 112, 147],\r\n\t\"papayawhip\": [255, 239, 213],\r\n\t\"peachpuff\": [255, 218, 185],\r\n\t\"peru\": [205, 133, 63],\r\n\t\"pink\": [255, 192, 203],\r\n\t\"plum\": [221, 160, 221],\r\n\t\"powderblue\": [176, 224, 230],\r\n\t\"purple\": [128, 0, 128],\r\n\t\"rebeccapurple\": [102, 51, 153],\r\n\t\"red\": [255, 0, 0],\r\n\t\"rosybrown\": [188, 143, 143],\r\n\t\"royalblue\": [65, 105, 225],\r\n\t\"saddlebrown\": [139, 69, 19],\r\n\t\"salmon\": [250, 128, 114],\r\n\t\"sandybrown\": [244, 164, 96],\r\n\t\"seagreen\": [46, 139, 87],\r\n\t\"seashell\": [255, 245, 238],\r\n\t\"sienna\": [160, 82, 45],\r\n\t\"silver\": [192, 192, 192],\r\n\t\"skyblue\": [135, 206, 235],\r\n\t\"slateblue\": [106, 90, 205],\r\n\t\"slategray\": [112, 128, 144],\r\n\t\"slategrey\": [112, 128, 144],\r\n\t\"snow\": [255, 250, 250],\r\n\t\"springgreen\": [0, 255, 127],\r\n\t\"steelblue\": [70, 130, 180],\r\n\t\"tan\": [210, 180, 140],\r\n\t\"teal\": [0, 128, 128],\r\n\t\"thistle\": [216, 191, 216],\r\n\t\"tomato\": [255, 99, 71],\r\n\t\"turquoise\": [64, 224, 208],\r\n\t\"violet\": [238, 130, 238],\r\n\t\"wheat\": [245, 222, 179],\r\n\t\"white\": [255, 255, 255],\r\n\t\"whitesmoke\": [245, 245, 245],\r\n\t\"yellow\": [255, 255, 0],\r\n\t\"yellowgreen\": [154, 205, 50]\r\n};\r\n\n\n//# sourceURL=webpack://MessagepointReactColor/./node_modules/color-name/index.js?");

/***/ }),

/***/ "./node_modules/invert-color/lib/invert.min.js":
/*!*****************************************************!*\
  !*** ./node_modules/invert-color/lib/invert.min.js ***!
  \*****************************************************/
/*! no static exports found */
/*! all exports used */
/***/ (function(module, exports, __webpack_require__) {

eval("/*! @license https://github.com/onury/invert-color */\n!function(r,n){ true?module.exports=n():undefined}(this,function(){\"use strict\";var t=Math.sqrt(1.05*.05)-.05,n=/^(?:[0-9a-f]{3}){1,2}$/i,i={black:\"#000000\",white:\"#ffffff\",threshold:t};function o(r){if(\"#\"===r.slice(0,1)&&(r=r.slice(1)),!n.test(r))throw new Error('Invalid HEX color: \"'+r+'\"');return 3===r.length&&(r=r[0]+r[0]+r[1]+r[1]+r[2]+r[2]),[parseInt(r.slice(0,2),16),parseInt(r.slice(2,4),16),parseInt(r.slice(4,6),16)]}function f(r){if(!r)throw new Error(\"Invalid color value\");return Array.isArray(r)?r:\"string\"==typeof r?o(r):[r.r,r.g,r.b]}function u(r,n,t){var e=!0===n?i:Object.assign({},i,n);return function(r){var n,t,e=[];for(n=0;n<r.length;n++)t=r[n]/255,e[n]=t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4);return.2126*e[0]+.7152*e[1]+.0722*e[2]}(r)>e.threshold?t?o(e.black):e.black:t?o(e.white):e.white}function r(r,n){return void 0===n&&(n=!1),r=f(r),n?u(r,n):\"#\"+r.map(function(r){return n=(255-r).toString(16),void 0===t&&(t=2),(new Array(t).join(\"0\")+n).slice(-t);var n,t}).join(\"\")}return function(r){function n(r,n){r=f(r);var t,e=n?u(r,n,!0):r.map(function(r){return 255-r});return{r:(t=e)[0],g:t[1],b:t[2]}}r.asRGB=n,r.asRgbArray=function(r,n){return r=f(r),n?u(r,n,!0):r.map(function(r){return 255-r})},r.defaultThreshold=t,r.asRgbObject=n}(r||(r={})),r});\n\n//# sourceURL=webpack://MessagepointReactColor/./node_modules/invert-color/lib/invert.min.js?");

/***/ }),

/***/ "react":
/*!**************************************************************************************!*\
  !*** external {"root":"React","commonjs2":"react","commonjs":"react","amd":"react"} ***!
  \**************************************************************************************/
/*! no static exports found */
/*! all exports used */
/***/ (function(module, exports) {

eval("module.exports = __WEBPACK_EXTERNAL_MODULE_react__;\n\n//# sourceURL=webpack://MessagepointReactColor/external_%7B%22root%22:%22React%22,%22commonjs2%22:%22react%22,%22commonjs%22:%22react%22,%22amd%22:%22react%22%7D?");

/***/ })

/******/ });
});