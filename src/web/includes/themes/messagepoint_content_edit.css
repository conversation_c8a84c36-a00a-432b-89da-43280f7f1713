html {
    min-height: 100%;
    height: fit-content;
}

.contentContainer ul li:before {
    content: "\2022";
}
.contentContainer li ul > li:before {
    content: "\25E6";
}
.contentContainer li li ul > li:before {
    content: "\25AA";
}

.contentContainer li  {
    position: relative;
}
.contentContainer li:before {
    display: inline-block;
    position: absolute;
}
.contentContainer li > div {
    position: relative;
}

.contentContainer li, .contentContainer li li, .contentContainer li li li,
.contentContainer li li li li, .contentContainer li li li li li,
.contentContainer li li li li li li, .contentContainer li li li li li li li,
.contentContainer li li li li li li li li, .contentContainer li li li li li li li li li,
.contentContainer li li li li li li li li li li {
    left: 0px;
}

.contentContainer ol > li:before {
    content: counter(level1, decimal) ".";
}
.contentContainer li:before,
.contentContainer [listclass] li:before {
    left: 25px;
}
.contentContainer li li:before,
.contentContainer [listclass] li li:before {
    left: 50px;
}
.contentContainer li li li:before,
.contentContainer [listclass] li li li:before {
    left: 75px;
}
.contentContainer li li li li:before,
.contentContainer [listclass] li li li li:before {
    left: 100px;
}
.contentContainer li li li li li:before,
.contentContainer [listclass] li li li li li:before {
    left: 125px;
}
.contentContainer li li li li li li:before,
.contentContainer [listclass] li li li li li li:before {
    left: 150px;
}
.contentContainer li li li li li li li:before,
.contentContainer [listclass] li li li li li li li:before {
    left: 175px;
}
.contentContainer li li li li li li li li:before,
.contentContainer [listclass] li li li li li li li li:before {
    left: 200px;
}
.contentContainer li li li li li li li li li:before,
.contentContainer [listclass] li li li li li li li li li:before {
    left: 225px;
}
.contentContainer li li li li li li li li li li:before,
.contentContainer [listclass] li li li li li li li li li li:before {
    left: 250px;
}

.contentContainer li > div.mceListItemContent,
.contentContainer [listclass] li > div.mceListItemContent
{
    left: 50px;
    width: calc(100% - 50px);
}
.contentContainer li li > div.mceListItemContent,
.contentContainer [listclass] li li > div.mceListItemContent
{
    left: 75px;
    width: calc(100% - 75px);
}
.contentContainer li li li > div.mceListItemContent,
.contentContainer [listclass] li li li > div.mceListItemContent
{
    left: 100px;
    width: calc(100% - 100px);
}
.contentContainer li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li > div.mceListItemContent
{
    left: 125px;
    width: calc(100% - 125px);
}
.contentContainer li li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li li > div.mceListItemContent
{
    left: 150px;
    width: calc(100% - 150px);
}
.contentContainer li li li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li li li > div.mceListItemContent
{
    left: 175px;
    width: calc(100% - 175px);
}
.contentContainer li li li li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li li li li > div.mceListItemContent
{
    left: 200px;
    width: calc(100% - 200px);
}
.contentContainer li li li li li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li li li li li > div.mceListItemContent
{
    left: 225px;
    width: calc(100% - 225px);
}
.contentContainer li li li li li li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li li li li li li > div.mceListItemContent
{
    left: 250px;
    width: calc(100% - 250px);
}
.contentContainer li li li li li li li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li li li li li li li > div.mceListItemContent
{
    left: 275px;
    width: calc(100% - 275px);
}


.contentContainer li {
    line-height: 1em;
}
.contentContainer ul, .contentContainer ol {
    list-style: none;
    padding: 0px;
    margin: 0px;
    text-align: left;
}

.contentContainer ul[data-list-style-type=circle] > li:before {
    content: "\2022" !important;
}
.contentContainer ul[data-list-style-type=disc] > li:before {
    content: "\25E6" !important;
}
.contentContainer ul[data-list-style-type=square] > li:before {
    content: "\25AA" !important;
}

.contentContainer ol                              { counter-reset: level1 }
.contentContainer li ol                           { counter-reset: level2 }
.contentContainer li li ol                        { counter-reset: level3 }
.contentContainer li li li ol                     { counter-reset: level4 }
.contentContainer li li li li ol                  { counter-reset: level5 }
.contentContainer li li li li li ol               { counter-reset: level6 }
.contentContainer li li li li li li ol            { counter-reset: level7 }
.contentContainer li li li li li li li ol         { counter-reset: level8 }
.contentContainer li li li li li li li li ol      { counter-reset: level9 }
.contentContainer li li li li li li li li li ol   { counter-reset: level10 }
.contentContainer ol > li::before                             { content: counter(level1, decimal) "."; counter-increment: level1; }
.contentContainer li ol > li::before                          { content: counter(level2, decimal) "."; counter-increment: level2; }
.contentContainer li li ol > li::before                       { content: counter(level3, decimal) "."; counter-increment: level3; }
.contentContainer li li li ol > li::before                    { content: counter(level4, decimal) "."; counter-increment: level4; }
.contentContainer li li li li ol > li::before                 { content: counter(level5, decimal) "."; counter-increment: level5; }
.contentContainer li li li li li ol > li::before              { content: counter(level6, decimal) "."; counter-increment: level6; }
.contentContainer li li li li li li ol > li::before           { content: counter(level7, decimal) "."; counter-increment: level7; }
.contentContainer li li li li li li li ol > li::before        { content: counter(level8, decimal) "."; counter-increment: level8; }
.contentContainer li li li li li li li li ol > li::before     { content: counter(level9, decimal) "."; counter-increment: level9; }
.contentContainer li li li li li li li li li ol > li::before  { content: counter(level10, decimal) "."; counter-increment: level10; }

.contentContainer ol[data-list-style-type=lower-alpha] > li::before                             { content: counter(level1, lower-alpha) "."; counter-increment: level1; }
.contentContainer li ol[data-list-style-type=lower-alpha] > li::before                          { content: counter(level2, lower-alpha) "."; counter-increment: level2; }
.contentContainer li li ol[data-list-style-type=lower-alpha] > li::before                       { content: counter(level3, lower-alpha) "."; counter-increment: level3; }
.contentContainer li li li ol[data-list-style-type=lower-alpha] > li::before                    { content: counter(level4, lower-alpha) "."; counter-increment: level4; }
.contentContainer li li li li ol[data-list-style-type=lower-alpha] > li::before                 { content: counter(level5, lower-alpha) "."; counter-increment: level5; }
.contentContainer li li li li li ol[data-list-style-type=lower-alpha] > li::before              { content: counter(level6, lower-alpha) "."; counter-increment: level6; }
.contentContainer li li li li li li ol[data-list-style-type=lower-alpha] > li::before           { content: counter(level7, lower-alpha) "."; counter-increment: level7; }
.contentContainer li li li li li li li ol[data-list-style-type=lower-alpha] > li::before        { content: counter(level8, lower-alpha) "."; counter-increment: level8; }
.contentContainer li li li li li li li li ol[data-list-style-type=lower-alpha] > li::before     { content: counter(level9, lower-alpha) "."; counter-increment: level9; }
.contentContainer li li li li li li li li li ol[data-list-style-type=lower-alpha] > li::before  { content: counter(level10, lower-alpha) "."; counter-increment: level10; }

.contentContainer ol[data-list-style-type=upper-alpha] > li::before                             { content: counter(level1, upper-alpha) "."; counter-increment: level1; }
.contentContainer li ol[data-list-style-type=upper-alpha] > li::before                          { content: counter(level2, upper-alpha) "."; counter-increment: level2; }
.contentContainer li li ol[data-list-style-type=upper-alpha] > li::before                       { content: counter(level3, upper-alpha) "."; counter-increment: level3; }
.contentContainer li li li ol[data-list-style-type=upper-alpha] > li::before                    { content: counter(level4, upper-alpha) "."; counter-increment: level4; }
.contentContainer li li li li ol[data-list-style-type=upper-alpha] > li::before                 { content: counter(level5, upper-alpha) "."; counter-increment: level5; }
.contentContainer li li li li li ol[data-list-style-type=upper-alpha] > li::before              { content: counter(level6, upper-alpha) "."; counter-increment: level6; }
.contentContainer li li li li li li ol[data-list-style-type=upper-alpha] > li::before           { content: counter(level7, upper-alpha) "."; counter-increment: level7; }
.contentContainer li li li li li li li ol[data-list-style-type=upper-alpha] > li::before        { content: counter(level8, upper-alpha) "."; counter-increment: level8; }
.contentContainer li li li li li li li li ol[data-list-style-type=upper-alpha] > li::before     { content: counter(level9, upper-alpha) "."; counter-increment: level9; }
.contentContainer li li li li li li li li li ol[data-list-style-type=upper-alpha] > li::before  { content: counter(level10, upper-alpha) "."; counter-increment: level10; }

.contentContainer ol[data-list-style-type=lower-greek] > li::before                             { content: counter(level1, lower-greek) "."; counter-increment: level1; }
.contentContainer li ol[data-list-style-type=lower-greek] > li::before                          { content: counter(level2, lower-greek) "."; counter-increment: level2; }
.contentContainer li li ol[data-list-style-type=lower-greek] > li::before                       { content: counter(level3, lower-greek) "."; counter-increment: level3; }
.contentContainer li li li ol[data-list-style-type=lower-greek] > li::before                    { content: counter(level4, lower-greek) "."; counter-increment: level4; }
.contentContainer li li li li ol[data-list-style-type=lower-greek] > li::before                 { content: counter(level5, lower-greek) "."; counter-increment: level5; }
.contentContainer li li li li li ol[data-list-style-type=lower-greek] > li::before              { content: counter(level6, lower-greek) "."; counter-increment: level6; }
.contentContainer li li li li li li ol[data-list-style-type=lower-greek] > li::before           { content: counter(level7, lower-greek) "."; counter-increment: level7; }
.contentContainer li li li li li li li ol[data-list-style-type=lower-greek] > li::before        { content: counter(level8, lower-greek) "."; counter-increment: level8; }
.contentContainer li li li li li li li li ol[data-list-style-type=lower-greek] > li::before     { content: counter(level9, lower-greek) "."; counter-increment: level9; }
.contentContainer li li li li li li li li li ol[data-list-style-type=lower-greek] > li::before  { content: counter(level10, lower-greek) "."; counter-increment: level10; }

.contentContainer ol[data-list-style-type=lower-roman] > li::before                             { content: counter(level1, lower-roman) "."; counter-increment: level1; }
.contentContainer li ol[data-list-style-type=lower-roman] > li::before                          { content: counter(level2, lower-roman) "."; counter-increment: level2; }
.contentContainer li li ol[data-list-style-type=lower-roman] > li::before                       { content: counter(level3, lower-roman) "."; counter-increment: level3; }
.contentContainer li li li ol[data-list-style-type=lower-roman] > li::before                    { content: counter(level4, lower-roman) "."; counter-increment: level4; }
.contentContainer li li li li ol[data-list-style-type=lower-roman] > li::before                 { content: counter(level5, lower-roman) "."; counter-increment: level5; }
.contentContainer li li li li li ol[data-list-style-type=lower-roman] > li::before              { content: counter(level6, lower-roman) "."; counter-increment: level6; }
.contentContainer li li li li li li ol[data-list-style-type=lower-roman] > li::before           { content: counter(level7, lower-roman) "."; counter-increment: level7; }
.contentContainer li li li li li li li ol[data-list-style-type=lower-roman] > li::before        { content: counter(level8, lower-roman) "."; counter-increment: level8; }
.contentContainer li li li li li li li li ol[data-list-style-type=lower-roman] > li::before     { content: counter(level9, lower-roman) "."; counter-increment: level9; }
.contentContainer li li li li li li li li li ol[data-list-style-type=lower-roman] > li::before  { content: counter(level10, lower-roman) "."; counter-increment: level10; }

.contentContainer ol[data-list-style-type=upper-roman] > li::before                             { content: counter(level1, upper-roman) "."; counter-increment: level1; }
.contentContainer li ol[data-list-style-type=upper-roman] > li::before                          { content: counter(level2, upper-roman) "."; counter-increment: level2; }
.contentContainer li li ol[data-list-style-type=upper-roman] > li::before                       { content: counter(level3, upper-roman) "."; counter-increment: level3; }
.contentContainer li li li ol[data-list-style-type=upper-roman] > li::before                    { content: counter(level4, upper-roman) "."; counter-increment: level4; }
.contentContainer li li li li ol[data-list-style-type=upper-roman] > li::before                 { content: counter(level5, upper-roman) "."; counter-increment: level5; }
.contentContainer li li li li li ol[data-list-style-type=upper-roman] > li::before              { content: counter(level6, upper-roman) "."; counter-increment: level6; }
.contentContainer li li li li li li ol[data-list-style-type=upper-roman] > li::before           { content: counter(level7, upper-roman) "."; counter-increment: level7; }
.contentContainer li li li li li li li ol[data-list-style-type=upper-roman] > li::before        { content: counter(level8, upper-roman) "."; counter-increment: level8; }
.contentContainer li li li li li li li li ol[data-list-style-type=upper-roman] > li::before     { content: counter(level9, upper-roman) "."; counter-increment: level9; }
.contentContainer li li li li li li li li li ol[data-list-style-type=upper-roman] > li::before  { content: counter(level10, upper-roman) "."; counter-increment: level10; }


.mce-item-table,.mce-item-table td,.mce-item-table th,.mce-item-table caption{
    border: none;
}
.mceDraggable, .mceInline, .mceEditableContainer, .mceStaticContainer {
    height: auto;
}
div.mceEditableContainer:not(.mceFormInputLabel), div.mceStaticContainer:not(.mceFormInputLabel) {
    height: 100%;
}

.mce-content-body [contentEditable='false'][data-mce-selected] {
    outline: none;
}
.mce-content-body div.ui-resizable-e {
    cursor: e-resize;
}
.mce-content-body div.ui-resizable-s {
    cursor: s-resize;
}
.mce-content-body div.ui-resizable-se {
    cursor: se-resize;
}
.mce-content-body div.mceHorizontalGuide {
    cursor: row-resize;
}
.mce-content-body div.mceVerticalGuide {
    cursor: col-resize;
}

/* Suppress root body content container: Focus should only apply to containers in freeform */
.mce-content-body.mce-canvas > p[data-mce-caret], .mce-content-body.mce-canvas > p, .mce-content-body.mce-canvas > div.mce-visual-caret {
    display: none;
}


.contentContainer .mceDraggable:hover:not(.mceLockedContainer),.contentContainer .mceInline:hover:not(.mceLockedContainer) {
    border: 1px solid #ccc;
    border-radius: 3px 3px 6px 6px;
    box-shadow: 1.5px 1.5px 1.5px #f5f5f5;
}

.contentContainer .mceDraggableSelected {
    border: 1px solid #ccc;
    border-radius: 3px 3px 6px 6px;
    box-shadow: 1.5px 1.5px 1.5px #f5f5f5;
}
.contentContainer .mceInlineSelected {
    border: 1px solid #ccc;
    border-radius: 3px 3px 6px 6px;
    box-shadow: 1.5px 1.5px 1.5px #f5f5f5;
}
.contentContainer .mceContainerFade {
    opacity: 0.8;
}
.contentContainer div.mceContainerHighlight {
    border: 1px solid #ff00f0;
}


.contentContainer .ui-drag-n {
    height: 6px;
    width: 100%;
    top: -8px;
    left: -1px;
}
.contentContainer .mceDraggable .ui-drag-n {
    cursor: move !important;
}
.contentContainer .mceInline .ui-drag-n {
    cursor: pointer !important;
}

.contentContainer .ui-drag-handle {
    z-index: 99;
    position: absolute;
    text-align: center;
    color: #000;
    font-size: 3px;
    display: block;
    border: 1px solid #ccc;
    background-color: #f5f5f5;
    border-radius: 3px 3px 0px 0px;
    background-image: linear-gradient(to bottom, #ffffff, #ccc);
    text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.3);
    box-sizing: content-box;
}
.contentContainer .mceContentSelected .ui-drag-handle {
    background-color: #ccc;
    background-image: linear-gradient(to bottom, #e5e5e5, #294090);
    border: 1px solid #5c70ad;
    color: #fff;
    text-shadow: 1px 1px 0 rgba(255, 255, 255, 1);
}
.contentContainer .mceContainerOverlap {
    border: 1px dashed red;
}
.contentContainer .mceContainerOverlap:hover {
    border: 1px solid red;
}
.contentContainer .mceContainerOverlap .ui-drag-handle {
    border: 1px solid red;
}

.mceHorizontalGuide, .mceVerticalGuide {
    position: absolute;
    z-index: 0;
}
.mceHorizontalGuide {
    left: 0px;
    height: 3px;
    border-top: 1px solid #65fdff;
    cursor: row-resize;
}
.mceVerticalGuide {
    top: 0px;
    width: 3px;
    border-left: 1px solid #65fdff;
    cursor: col-resize;
}
.mceGuideContainer {
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: -1;
}

.mceGuideSelected {
    border-color: #bbb;
    border-width: 2px;
}

/* TABLE RESIZE: Bar must have higher z-index than freeform drag container */
.contentContainer .mce-resize-bar {
    z-index: 1000;
}

.contentContainer .ui-icon-gripsmall-diagonal-se {
    background-position: -64px -224px;
}

.contentContainer .ui-resizable-handle {
    position: absolute;
    font-size: 0.1px;
    display: block;
    -ms-touch-action: none;
    touch-action: none;
    box-sizing: content-box;
}

.contentContainer .ui-resizable-se {
    cursor: se-resize;
    width: 12px;
    height: 12px;
    right: 1px;
    bottom: 1px;
}

.contentContainer .ui-resizable-s {
    cursor: s-resize;
    height: 7px;
    width: 100%;
    bottom: -5px;
    left: 0;
}

.contentContainer .ui-resizable-e {
    cursor: e-resize;
    width: 7px;
    right: -5px;
    top: 0;
    height: 100%;
}

.contentContainer .mce-edit-focus {
    outline: none;
}
.contentContainer  [contentEditable='false'] [contentEditable='true']:focus {
    outline: none;
}
.contentContainer  [contentEditable='false'] [contentEditable='true']:hover {
    outline: none;
}
.contentContainer  [contentEditable='false'][data-mce-selected] {
    outline: none;
}
.mceTagPanelHighlight {
    background-color: rgba(200, 160, 200, 1) !important;
    opacity: 0.8;
}

.contentContainer table { border-collapse: collapse; border-spacing: 0; line-height: 1;}
.contentContainer .staticContentItem.active .staticContentItem[type]:not([type='15']) { background-color: #d6d6d6; !important }

