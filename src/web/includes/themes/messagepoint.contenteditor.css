.content-editor-page {
  min-width: 80rem;
  font-size: 1rem;
  font-family: "Museo Sans", sans-serif;
  font-style: normal;
}
.content-editor-page .breadcrumb-container {
  font-size: 0.75rem;
  border-bottom: solid 1px #e1e1e1;
}
.content-editor-page .breadcrumb-back {
  margin: 0 -0.5rem 0 0 !important;
}
.content-editor-page .breadcrumb-divider {
  margin: 0 0.25rem 0 0;
}
.content-editor-page .editorContentPanel {
  height: 100%;
}
.content-editor-page .editorContentPanel .mce-tinymce {
  position: static;
  width: 100% !important;
  height: 100% !important;
  border: 0;
  box-shadow: none;
}
.content-editor-page .editorContentPanel .mce-tinymce > .mce-stack-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.content-editor-page .editorContentPanel .mce-tinymce > .mce-stack-layout .mce-panel:not(.mce-statusbar) {
  border-top: 0;
  border-color: #e1e1e1;
}
.content-editor-page .editorContentPanel .mce-tinymce > .mce-stack-layout .mce-edit-area {
  position: relative;
  flex-grow: 1;
  flex-shrink: 0;
  border: 0;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  background: white !important;
  overflow: hidden;
}
.content-editor-page .editorContentPanel .mce-tinymce > .mce-stack-layout .mce-edit-area iframe {
  border: 0;
  width: 100% !important;
  height: 100% !important;
}
.content-editor-page .editorContentPanel .mce-statusbar {
  border-color: #e1e1e1;
  border-style: solid;
  border-width: 1px 0 !important;
}
.content-editor-page .editorContentPanel .mce-top-part::before {
  box-shadow: none;
}
.content-editor-page .editorContentPanel .mce-content-body {
  overflow: hidden;
}
.content-editor-page .editorContentPanel .contentEditor_EditorContainer {
  height: 100%;
}
.content-editor-page .editorContentPanel .contentEditor_InnerContentContainer {
  border: 0;
  background: transparent;
}
.content-editor-page .editorContentPanel .mce-flow-layout-item {
  padding: 0;
  margin: 0;
}
.content-editor-page .editorContentPanel .mce-flow-layout-item + .content-editor-page .editorContentPanel .mce-flow-layout-item {
  margin-left: 1rem;
}
.content-editor-page .editorContentPanel .mce-top-part.mce-container.mce-stack-layout-item.mce-first {
  padding: 1rem 0;
  border-bottom: solid 1px #e1e1e1;
}
.content-editor-page .editorContentPanel .mce-toolbar-grp.mce-last {
  width: fit-content;
  margin: 0 1.5rem;
  padding: 0 0.25rem;
  background: #f7f7f7;
  border-radius: 100px;
}
.content-editor-page .editorContentPanel .mce-toolbar .mce-btn-group {
  padding: 0;
  margin: 0;
}
.content-editor-page .editorContentPanel .mce-splitbtn .mce-open {
  border: 0 !important;
  padding-left: 0;
}
.content-editor-page .editorContentPanel .mce-btn-group .mce-btn {
  display: inline-flex;
  align-items: center;
  border: 0;
  margin: 0;
  background: transparent;
}
.content-editor-page .editorContentPanel .mce-btn button {
  display: flex;
  align-items: center;
  height: 2rem;
  padding: 0.25rem 0.5rem;
}
.content-editor-page .editorContentPanel .mce-colorbutton .mce-preview {
  position: absolute;
  left: 0.125rem !important;
  top: 0.125rem !important;
  transform: translate(50%, 50%);
  width: 0.875rem;
  height: 0.875rem !important;
  margin: 0;
  padding: 0;
  border-radius: 0.125rem;
}
.content-editor-page .editorContentPanel .mce-menubar {
  margin: 0 0 0.5rem 0;
  padding: 0 1rem;
  border: 0;
}
.content-editor-page .editorContentPanel .mce-menubar .mce-menubtn {
  height: 2rem;
  border-radius: 0.25rem;
}
.content-editor-page .editorContentPanel .mce-menubar .mce-menubtn button {
  display: flex;
  align-items: center;
  height: 2rem;
  font-style: normal;
  font-size: 0.75rem;
  line-height: 1.5rem;
  text-transform: uppercase;
}
.content-editor-page .editorContentPanel .mce-menubar .mce-menubtn button span {
  font-weight: 700;
  color: #515151;
}
.content-editor-page .editorContentPanel .mce-btn .mce-txt + .mce-caret {
  margin: 0 0 0 0.5rem;
}
.content-editor-page .editorContentPanel .mce-btn.mce-active button, .content-editor-page .editorContentPanel .mce-btn.mce-active:hover button, .content-editor-page .editorContentPanel .mce-btn.mce-active i, .content-editor-page .editorContentPanel .mce-btn.mce-active:hover i {
  color: #515151;
}
.content-editor-page .editorContentPanel .mce-btn i {
  text-shadow: none;
}
.content-editor-page .editorContentPanel .mce-listbox .mce-caret {
  position: relative;
  top: unset;
  bottom: unset;
  right: unset;
  left: unset;
}
.content-editor-page .editorContentPanel .mce-caret {
  position: relative;
  right: 0;
  left: 0;
  border: 0;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  margin-right: 0 !important;
  padding: 0;
  width: 0.5rem;
  height: 0.5rem;
  content: "";
  color: #515151;
  text-align: center;
}
.content-editor-page .editorContentPanel .mce-caret::before {
  position: absolute;
  left: 0;
  width: 100%;
  height: 100%;
  font-family: "Font Awesome 6 Pro";
  font-weight: 900;
  font-size: 0.5rem;
  font-style: normal;
  color: inherit;
  content: "\f0d7";
}
.content-editor-page .editorContentPanel .mce-ico {
  font-family: "Font Awesome 6 Pro";
  font-weight: 900;
  font-style: normal;
  font-size: 0.75rem;
  text-align: center;
}
.content-editor-page .editorContentPanel .mce-i-bold::before {
  content: "\f032";
}
.content-editor-page .editorContentPanel .mce-i-italic::before {
  content: "\f033";
}
.content-editor-page .editorContentPanel .mce-i-underline::before {
  content: "\f0cd";
}
.content-editor-page .editorContentPanel .mce-i-alignleft::before {
  content: "\f036";
}
.content-editor-page .editorContentPanel .mce-i-aligncenter::before {
  content: "\f037";
}
.content-editor-page .editorContentPanel .mce-i-alignright::before {
  content: "\f038";
}
.content-editor-page .editorContentPanel .mce-i-alignjustify::before {
  content: "\f039";
}
.content-editor-page .editorContentPanel .mce-i-outdent::before {
  content: "\f03b";
}
.content-editor-page .editorContentPanel .mce-i-indent::before {
  content: "\f03c";
}
.content-editor-page .editorContentPanel .mce-i-bullist::before {
  content: "\f0ca";
}
.content-editor-page .editorContentPanel .mce-i-numlist::before {
  content: "\f0cb";
}
.content-editor-page .editorContentPanel .mce-i-markeditable {
  font-size: 0.875rem;
  font-weight: 400;
}
.content-editor-page .editorContentPanel .mce-i-markeditable::before {
  content: "\f14b";
}
.content-editor-page .editorContentPanel .mce-i-inserteditpoint {
  font-size: 0.875rem;
  font-weight: 400;
}
.content-editor-page .editorContentPanel .mce-i-inserteditpoint::before {
  content: "\f055";
}
.content-editor-page .footer-main .footer-content {
  margin: 0;
}
.content-editor-page .nav-column {
  flex-direction: column;
  font-size: 1.25rem;
}
.content-editor-page .nav-column .nav-toggle {
  border-radius: 0.25rem;
}
.content-editor-page .nav-column .nav-toggle:hover, .content-editor-page .nav-column .nav-toggle:focus {
  background-color: #e1e1e1;
}
.content-editor-page .nav-column .nav-link.active {
  background: #e1e1e1;
}

.mceHorizontalRuler {
  position: relative;
  width: 100%;
  height: 1rem;
  border-left: solid 1px #cfcfcf;
}

.mceVerticalRuler {
  position: relative;
  width: 1rem;
  min-height: 100%;
  border-top: solid 1px #cfcfcf;
}

.mce-vertical-gutter-guide {
  position: absolute;
  border-left: dashed 1px #cfcfcf;
  min-height: 100% !important;
}

.ruler-set-number-list {
  display: flex;
  align-items: flex-start;
  overflow: hidden;
  width: 100%;
  height: 100%;
  user-select: none;
}

.ruler-set-number {
  box-sizing: border-box;
  flex-shrink: 0;
  font-size: 0.5rem;
  font-weight: 700;
  color: #515151;
}

.horizontal-ruler-container {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  min-width: 100%;
  padding: 1rem 2rem 0 4rem;
  border-bottom: solid 1px #cfcfcf;
  background: #fff;
  overflow: hidden;
}
.horizontal-ruler-container .ruler-set-number {
  width: 100px;
  padding: 0 0.25rem;
  cursor: default;
}
.horizontal-ruler-container.ruler-container-cm .ruler-set-number {
  width: 40px;
}

.vertical-ruler-container {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100%;
  padding: 4rem 0 2rem 1rem;
  border-right: solid 1px #cfcfcf;
  background: #fff;
  overflow: hidden;
}
.vertical-ruler-container .ruler-set-number-list {
  flex-direction: column;
}
.vertical-ruler-container .ruler-set-number {
  height: 100px;
  padding: 0.25rem 0;
  cursor: default;
}
.vertical-ruler-container.ruler-container-cm .ruler-set-number {
  height: 40px;
  padding: 0.25rem 0;
}

/*# sourceMappingURL=messagepoint.contenteditor.css.map */
