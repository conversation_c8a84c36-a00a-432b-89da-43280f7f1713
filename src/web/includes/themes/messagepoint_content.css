/* START - VAR TAG ICONS */
.contentContainer .fa-mp-ed-ico {
    color: #414141;
}
.contentContainer .mce-ico-txt.mce-ico-rev {
    color: #f9f9f9;
}
.contentContainer .fa-mp-ed-ico:not(.fa-stack) {
    top: 0px;
    position: relative;
    font-size: 0.9em;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.contentContainer .fa-mp-ed-ico.fa-stack {
    width: 1em;
    height: 1em;
    line-height: 1em;
}
.contentContainer .fa-stack span.mce-ico-txt {
    font-size: 0.6em;
    font-weight: bold;
    top: -1px;
    left: -1px;
    font-family: 'Times New Roman', Times, serif;
    font-style: normal;
    text-decoration: none;
    min-height: 0.6em;
}
/* END - VAR TAG ICONS */

/* START - LINK ICONS */
.contentContainer a[type=anchor] i.fa-anchor, .contentContainer a[type=bookmark] i.fa-bookmark-o,
.contentContainer a[type=alt_text] i.fa-comment-o, .contentContainer p i.fa-paragraph {
    padding-right: 3px;
}
.contentContainer a[type=anchor], .contentContainer a[type=bookmark] {
    background-color: lightgreen;
    text-decoration: none;
    color: inherit;
    padding: 0px 3px;
}
.contentContainer a[type=alt_text] {
    background-color: lightpink;
    text-decoration: none;
    color: inherit;
}
/* END - LINK ICONS */

/* START - CONTENT TARGETING/REPEATING ROWS */
.contentContainer .contentTargetingIcon,
.contentContainer .columnTargetingIcon,
.contentContainer .repeatingRowIcon,
.contentContainer .listPropertiesIcon,
.contentContainer .repeatingHeaderIcon,
.contentContainer .fixedHeaderIcon,
.contentContainer .repeatingFooterIcon,
.contentContainer .fixedFooterIcon {
    border-radius: 3px;
    opacity: 0.95;
    position: absolute;
    left: -24px;
    top: 0px;
    z-index: 2;
    border: 1px solid #ddd;
    width: 20px;
    height: 20px;
    color: #555;
    font-size: 14px;
}
.contentContainer .prefillIndicatorIcon,
.contentContainer .altTextIndicatorIcon,
.contentContainer .hybridAttributesIndicatorIcon {
    left: 0px;
    top: 0px;
    width: 16px;
    height: 16px;
    opacity: 0.75;
    display: inline-block;
    font-size: 12px;
}
.contentContainer .contentTargetingIcon, .contentContainer .columnTargetingIcon {
    background: #f5f5f5 url('commonimages/icons/targeting_icon.png') no-repeat center center;
}
.contentContainer .repeatingRowIcon {
    background: #f5f5f5 url('commonimages/icons/repeating_icon.png') no-repeat center center;
}
.contentContainer .repeatingHeaderIcon {
    background: #f5f5f5 url('commonimages/icons/repeating_header_icon.png') no-repeat center center;
}
.contentContainer .fixedHeaderIcon {
    background: #f5f5f5 url('commonimages/icons/fixed_header_icon.png') no-repeat center center;
}
.contentContainer .repeatingFooterIcon {
    background: #f5f5f5 url('commonimages/icons/repeating_footer_icon.png') no-repeat center center;
}
.contentContainer .fixedFooterIcon {
    background: #f5f5f5 url('commonimages/icons/fixed_footer_icon.png') no-repeat center center;
}
.contentContainer .contentTargetingIcon:not(.activeEditorIcon):hover,
.contentContainer .columnTargetingIcon:not(.activeEditorIcon):hover,
.contentContainer .listPropertiesIcon:not(.activeEditorIcon):hover,
.contentContainer .repeatingRowIcon:not(.activeEditorIcon):hover,
.contentContainer .repeatingHeaderIcon:not(.activeEditorIcon):hover,
.contentContainer .fixedHeaderIcon:not(.activeEditorIcon):hover,
.contentContainer .repeatingFooterIcon:not(.activeEditorIcon):hover,
.contentContainer .fixedFooterIcon:not(.activeEditorIcon):hover {
    background-color: #fafafa;
}
.contentContainer .activeEditorIcon {
    background-color: #e5e5e5;
    border: 1px solid #ccc;
}
.contentContainer {
    white-space: normal;
    font-family: Arial;
}

.contentContainer table td .mceStaticElement:nth-of-type(2) {
    left: 3px;
    top: 3px;
}
.contentContainer table td .mceStaticElement:nth-of-type(3) {
    left: 6px;
    top: 6px;
}

.contentContainer [content_targeting_id] {
    text-decoration: none;
}
/* END - CONTENT TARGETING/REPEATING ROWS */

/* START - LISTS CSS */
.contentContainer i, .contentContainer span, .contentContainer var, .contentContainer div, .contentContainer p:not([paragraphclass]), .contentContainer table {
    text-indent: initial;
}
/* END - LISTS CSS */

/* START - FREEFORM LAYOUT CSS */
body.contentContainer {
    position: relative;
    margin: 0;
}

.contentContainer .mceDraggable {
    box-sizing: border-box;
    position: absolute;
    border: 1px dashed rgba(238, 238, 238, .5);
}
.contentContainer .mceInline {
    box-sizing: border-box;
    display: inline-block;
    position: relative;
    border: 1px dashed rgba(238, 238, 238, .5);
    vertical-align: top;
}
.contentContainer td > .mceInline {
    left: -1px;
    top: -1px;
}

.contentContainer .mceHiddenElement {
    display: none;
}

/* Smart Canvas */
.contentContainer .mceStaticContainer {
    border-radius: 3px 3px 6px 6px;
    background-color: #62f4eb;
    height: 100%;
}
.contentContainer .mceStaticContainer .staticContentItem {
    height: 100%;
    border-radius: 3px 3px 6px 6px;
}
.contentContainer .mceLockedDraggable {
    position: absolute;
    z-index: 0;
}
.contentContainer .mceStaticContainer .staticContentItem > i {
    z-index: 1;
}

.contentContainer .staticContentItem.fastNavigation.leftPositioned > div.btn-group-vertical {
    transform: translate(115%, 3%) !important;
    right: 0;
}

/* Hide root canvas P tags (become SPANs on smart canvas rendering) */
.contentContainer .mceStaticContainer .staticContentItem > span {
    font-size: 0px;
}

.contentContainer .staticContentItem.leftPositioned > div.btn-group-vertical
{
    transform: translate(115%, -35%) !important;
    right: 0;
}

/* START - FORM ELEMENTS CSS */
.contentContainer .mceInlineFormElements {
    display: inline-block;
    vertical-align: top;
}
.contentContainer .mceTextFieldElement, .contentContainer .mceCheckboxElement, .contentContainer .mceRadioElement,
.contentContainer .mceMenuElement, .contentContainer .mceSubmitButtonElement {
    white-space: nowrap;
    text-align: left;
}
.contentContainer .mceRadioInput {
    font-size: 9px;
    text-align: center;
    vertical-align: middle;
}
.contentContainer .mceFormInput {
    position: relative;
    box-sizing: border-box;
    vertical-align: middle;
}
.contentContainer .mceFormInput[vertical_align='top'] {
    vertical-align: top;
}
.contentContainer .mceFormInput[vertical_align='middle'] {
    vertical-align: middle;
}
.contentContainer .mceFormInput[vertical_align='bottom'] {
    vertical-align: bottom;
}
.contentContainer .mceFormInputLabel {
    white-space: normal;
    vertical-align: middle;
}
.contentContainer .mceFormInputLabel[minimize_width='true'], .contentContainer .embedded_content_tag div[minimize_width='true'], .contentContainer .varTagRenderedInline,
.contentContainer .mceFormInputLabel[minimize_width='true'] p, .contentContainer .mceFormInputLabel[minimize_width='true'] p span {
    white-space: nowrap;
}
.contentContainer .mceLineElement {
    padding-top: 2px;
    padding-bottom: 2px;
}
.contentContainer .mceMenuInput {
    text-align: right;
    padding-right: 6px;
}
.contentContainer .mceMenuInput i {
    vertical-align: middle;
    font-size: 12px;
}
.contentContainer .mceTextFieldInput {
    text-align: right;
}
.contentContainer .mceTextFieldInput i {
    position: relative;
    right: -20px;
}
.contentContainer .mceSubmitButton {
    text-align: center;
}
.contentContainer .mceSubmitButton, .contentContainer .mceFormEleLabel {
    display: inline-block;
}
/* END - FORM ELEMENTS CSS */

/* START - ROTATION */
.contentContainer [rotate='90'] {
    -ms-transform: rotate(90deg); /* IE 9 */
    transform: rotate(90deg);
    transform-origin: top left;
    position: absolute;
    left: 100%;
}
.contentContainer [rotate='180'] {
    -ms-transform: rotate(180deg); /* IE 9 */
    transform: rotate(180deg);
    position: absolute;
}
.contentContainer [rotate='270'] {
    -ms-transform: rotate(270deg); /* IE 9 */
    transform: rotate(270deg);
    transform-origin: top left;
    position: absolute;
    top: 100%;
}

/* END - ROTATION */

/* START - NON EDITABLE CSS */

.contentContainer .mceHiddenContent {
    display: none;
}

.contentContainer var {
    font-style: inherit;
}
.contentContainer table .staticContentItem,
.contentContainer table .innerStaticContentItem {
    white-space: normal;
}
.contentContainer .staticContentItem,
.contentContainer .innerStaticContentItem  {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    vertical-align: middle;
}
.contentContainer .varTagRenderedBlock, .contentContainer .varTagRenderedInline {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    padding: 0px;
}
.contentContainer span[noedit='true'] {
    background-color: #fbaf5d;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    padding: 0px;
}
.contentContainer span[noedit='true']::selection {
    background-color: #e08f36; !important
}
.contentContainer span[noedit='true']::-moz-selection {
    background-color: #e08f36; !important
}
.contentContainer span[noedit='true']::-webkit-selection {
    background-color: #e08f36; !important
}
.contentContainer span[template_fixed_content]::-moz-selection {
    background-color: #d6d6d6; !important
}
.contentContainer span[template_fixed_content]::-webkit-selection {
    background-color: #d6d6d6; !important
}
.contentContainer span[template_fixed_content]::selection {
    background-color: #ffff55; !important
}
.contentContainer nbsp {
    background-color: #ff4141; !important
}
.contentContainer nbsp::selection {
    background-color: #ff4141; !important
}
.contentContainer nbsp::-moz-selection {
    background-color: #ff4141; !important
}
.contentContainer nbsp::-webkit-selection {
    background-color: #ff4141; !important
}
.contentContainer .staticContentItem.active::selection {
    background-color: #d6d6d6; !important
}
.contentContainer .staticContentItem.active::-moz-selection {
    background-color: #d6d6d6; !important
}
.contentContainer .staticContentItem.active::-webkit-selection {
    background-color: #d6d6d6; !important
}
.contentContainer .staticContentItem[type='1']::selection,
.contentContainer .staticContentItem[type='19']::selection {
    background-color: #ffff55; !important
}

.contentContainer mpr_variable::selection {
    background-color: #71a9e4; !important
}

.contentContainer .staticContentItem:not([type='1'])::selection,
.contentContainer .staticContentItem:not([type='19'])::selection {
    background-color: #62f4eb; !important
}

.contentContainer .staticContentItem[type='1'].active::-moz-selection,
.contentContainer .staticContentItem[type='19'].active::-moz-selection,
.staticContentItem[type='1']::-moz-selection,
.staticContentItem[type='19']::-moz-selection {
    background-color: #ffff55; !important
}

.contentContainer mpr_variable::-moz-selection {
    background-color: #71a9e4; !important
}

.contentContainer .staticContentItem:not([type='1']).active::-moz-selection,
.staticContentItem:not([type='1'])::-moz-selection {
    background-color: #62f4eb; !important
}
.contentContainer .staticContentItem[type='1'].active::-webkit-selection,
.contentContainer .staticContentItem[type='19'].active::-webkit-selection,
.staticContentItem[type='1']::-webkit-selection,
.staticContentItem[type='19']::-webkit-selection {
    background-color: #ffff55; !important
}

.contentContainer mpr_variable::-webkit-selection {
    background-color: #71a9e4; !important
}
.contentContainer .staticContentItem:not([type='1']).active::-webkit-selection,
.staticContentItem:not([type='1'])::-webkit-selection {
    background-color: #62f4eb; !important
}
.contentContainer .staticContentItem.embedded_content_tag.active::selection,
.contentContainer span.active .embedded_content_tag::selection,
.contentContainer span.active .embedded_content_tag span::selection,
.contentContainer span.active .embedded_content_tag li::selection,
.staticContentItem.embedded_content_tag.active::selection,
.embedded_content_tag.active li::selection,
.embedded_content_tag.active div::selection,
.embedded_content_tag.active span:not(.fa-stack):not(.mce-ico-txt)::selection {
    background-color: #d6d6d6; !important
}
.contentContainer .staticContentItem.embedded_content_tag.active::-moz-selection,
.contentContainer span.active .embedded_content_tag::-moz-selection,
.contentContainer span.active .embedded_content_tag span::-moz-selection,
.contentContainer span.active .embedded_content_tag li::-moz-selection,
.staticContentItem.embedded_content_tag.active::-moz-selection,
.embedded_content_tag.active li::-moz-selection,
.embedded_content_tag.active div::-moz-selection,
.embedded_content_tag.active span:not(.fa-stack):not(.mce-ico-txt)::-moz-selection {
    background-color: #d6d6d6; !important
}
.contentContainer .staticContentItem.embedded_content_tag.active::-webkit-selection,
.contentContainer span.active .embedded_content_tag::-webkit-selection,
.contentContainer span.active .embedded_content_tag span::-webkit-selection,
.contentContainer span.active .embedded_content_tag li::-webkit-selection,
.staticContentItem.embedded_content_tag.active::-webkit-selection,
.embedded_content_tag.active li::-webkit-selection,
.embedded_content_tag.active div::-webkit-selection,
.embedded_content_tag.active span:not(.fa-stack):not(.mce-ico-txt)::-webkit-selection {
    background-color: #d6d6d6; !important
}
.contentContainer .staticContentItem.embedded_content_tag::selection,
.contentContainer .embedded_content_tag::selection,
.contentContainer .embedded_content_tag span::selection,
.contentContainer .embedded_content_tag li::selection,
.staticContentItem.embedded_content_tag::selection {
    background-color: #edf6fb; !important
}
.contentContainer .staticContentItem.embedded_content_tag::-moz-selection,
.contentContainer .embedded_content_tag::-moz-selection,
.contentContainer .embedded_content_tag span::-moz-selection,
.contentContainer .embedded_content_tag li::-moz-selection,
.staticContentItem.embedded_content_tag::-moz-selection,
.embedded_content_tag li::-moz-selection,
.embedded_content_tag div::-moz-selection,
.embedded_content_tag span::-moz-selection {
    background-color: #edf6fb; !important
}
.contentContainer .staticContentItem.embedded_content_tag::-webkit-selection,
.contentContainer .embedded_content_tag::-webkit-selection,
.contentContainer .embedded_content_tag span::-webkit-selection,
.contentContainer .embedded_content_tag li::-webkit-selection,
.staticContentItem.embedded_content_tag::-webkit-selection,
.embedded_content_tag li::-webkit-selection,
.embedded_content_tag div::-webkit-selection,
.embedded_content_tag span::-webkit-selection {
    background-color: #edf6fb; !important
}

/* Connected: Editable text */
.contentContainer [data-edit-mode='inline_insert_point'] {
    background-color: #edeeee;
    text-decoration: none;
}
.contentContainer [data-edit-mode='block_insert_point']:not([data-default-content=true]) {
    display: block;
    border-bottom: 1px dashed #c8c6c6;
    width: 100%;
    height: 1px;
    position: relative;
}
body#tinymce [data-edit-mode='block_insert_point']:not([data-default-content=true]) {
    margin: 11px 0px;
}
.contentContainer [data-edit-mode='block_insert_point'][data-default-content=true] {
    display: block;
    width: 100%;
    background-color: #edeeee;
    text-decoration: none;
}
.contentContainer [data-edit-mode='block_insert_point'] .mceEditPointIndicator {
    position: absolute;
    bottom: -7px;
    left: 50%;
    color: #444;
}
[data-edit-mode='inline_insert_point'] .mceEditPointIndicator {
    font-size: 12px;
    color: #444;
    position: relative;
    top: 1px;
}

.contentContainer [data-edit-mode] [type='1'], .contentContainer [data-edit-mode] [type='19'] {
    background: repeating-linear-gradient(45deg, #edeeee, #edeeee 10px, #ffff55 10px, #ffff55 20px);
}
.contentContainer [data-edit-mode] .staticContentItem:not([type='1']):not([type='100']),
.contentContainer [data-edit-mode] .embedded_content_tag .varTagRenderedInline,
.contentContainer [data-edit-mode] .embedded_content_tag .varTagRenderedBlock{
    background: repeating-linear-gradient(45deg, #edeeee, #edeeee 10px, #edf6fb 10px, #edf6fb 20px);
}

/* .active Inline Insert Static Assets */
.contentContainer [data-edit-mode] [type='1'].active, .contentContainer [data-edit-mode] [type='19'].active {
    background: repeating-linear-gradient(45deg, #d6d6d6, #d6d6d6 10px, #ffff55 10px, #ffff55 20px) !important;
}
.contentContainer [data-edit-mode] .staticContentItem:not([type='1']):not([type='100']).embedded_content_tag.active,
.contentContainer [data-edit-mode] .staticContentItem:not([type='1']):not([type='100']).embedded_content_tag.active span:not(.fa-stack):not(.mce-ico-txt) {
    background: repeating-linear-gradient(45deg, #d6d6d6, #d6d6d6 10px, #edf6fb 10px, #edf6fb 20px) !important;
}

.contentContainer .staticContentItem[type='15'], .contentContainer .innerStaticContentItem[type='15'] {
    background: repeating-linear-gradient(45deg, #edeeee, #edeeee 10px, #62f4eb 10px, #62f4eb 20px);
}
.contentContainer .staticContentItem[type='15'].active, .contentContainer .active .innerStaticContentItem[type='15'],
.contentContainer .staticContentItem[type='15'].active span:not(.fa-stack):not(.mce-ico-txt),
.contentContainer .active .innerStaticContentItem[type='15'] span:not(.fa-stack):not(.mce-ico-txt)
{
    background: repeating-linear-gradient(45deg, #d6d6d6, #d6d6d6 10px, #62f4eb 10px, #62f4eb 20px) !important;
}






.contentContainer [content_targeting_id]:not(tr), .contentContainer [content_targeting_id]:not(tr) table,
.contentContainer [content_targeting_id]:not(tr) ul, .contentContainer [content_targeting_id]:not(tr) ol {
    background-color: #ffaf88;
}
.contentContainer [convert_targeting_id], .contentContainer [convert_targeting_id] table,
.contentContainer [convert_targeting_id] ul, .contentContainer [convert_targeting_id] ol  {
    background: repeating-linear-gradient(45deg, #ffaf88, #ffaf88 10px, #fff 10px, #fff 20px);
}

.contentContainer div[content_targeting_id] {
    position: relative;
}
.contentContainer div[content_targeting_id] > .mceInlineTargeting, .contentContainer div[convert_targeting_id] > .mceInlineTargeting {
    position: absolute;
    top: -17px;
    left: -20px;
    background-color: #ffaf88;
    width: 18px;
    height: 17px;
    text-align: center;
    padding: 3px;
    border-radius: 3px;
    box-sizing: border-box;
}
.contentContainer div[content_targeting_id] > .mceInlineTargeting.activeEditorIcon, .contentContainer div[convert_targeting_id] > .mceInlineTargeting.activeEditorIcon {
    border: none;
    background-color: #e5e5e5;
}
.dataTable .contentContainer div[content_targeting_id] > .mceInlineTargeting, .dataTable .contentContainer div[convert_targeting_id] > .mceInlineTargeting {
    font-size: 1em;
}

.contentContainer [content_targeting_id]:not(tr) [type='1'] {
    background: repeating-linear-gradient(45deg, #ffaf88, #ffaf88 10px, #ffff55 10px, #ffff55 20px);
}
.contentContainer [content_targeting_id]:not(tr) .staticContentItem:not([type='1']):not([type='100']):not(.active),
.contentContainer [content_targeting_id]:not(tr) .embedded_content_tag:not(.active) .varTagRenderedInline,
.contentContainer [content_targeting_id]:not(tr) .embedded_content_tag:not(.active) .varTagRenderedBlock,
.contentContainer [content_targeting_id]:not(tr) .embedded_content_tag:not(.active) table,
.contentContainer [content_targeting_id]:not(tr) .embedded_content_tag:not(.active) ol,
.contentContainer [content_targeting_id]:not(tr) .embedded_content_tag:not(.active) ul
{
    background: repeating-linear-gradient(45deg, #ffaf88, #ffaf88 10px, #edf6fb 10px, #edf6fb 20px);
}

/* Generate default CSS definition */
.contentContainer .staticContentItem[type='1'], .contentContainer .staticContentItem[type='19'], .contentContainer .staticContentItem[type='100'], .contentContainer mpr_variable { background-color: #ffff55; }
.contentContainer .staticContentItem[type='4'],
.contentContainer .staticContentItem[type='5'],
.contentContainer .staticContentItem[type='10'] { background-color: #62f4eb; }
.contentContainer .innerStaticContentItem { background-color: #d9d9d9; }
.contentContainer span[template_fixed_content] { background-color: #eeeeee; }
.contentContainer nbsp :not(.rendering-manager-content) { background-color: #ff4141; }
.contentContainer .staticContentItem.active { background-color: #d6d6d6 !important; }
.contentContainer .staticContentItem.active .innerStaticContentItem:not([type='15']) { background-color: #d6d6d6 !important; }
.contentContainer .innerStaticContentItem.active { background-color: #a8a8a8 !important; }
.contentContainer .staticContentItem .staticContentItem.embedded_content_tag { background-color: #edf6fb; white-space: normal; padding: 0px; }
.contentContainer .embedded_content_tag .varTagRenderedBlock { background-color: #edf6fb; white-space: normal; display: block; }
.contentContainer .embedded_content_tag .varTagRenderedInline { background-color: #edf6fb; white-space: normal; }
.contentContainer .staticContentItem.embedded_content_tag.active, .contentContainer .embedded_content_tag.active span:not(.fa-stack):not(.mce-ico-txt) { background-color: #d6d6d6 !important; }
.contentContainer .staticContentItem.embedded_content_tag.active [type='15'] span { background-color: #e09fe0 !important; }
.contentContainer td,.contentContainer th { min-width: 10px; }
.contentContainer ul,.contentContainer ol { margin-top: 0px; margin-bottom: 0px; }
.contentContainer [block_content=true], .contentContainer [block_content=true] [paragraphclass]:not(li) { display: block; }

/* START - TABLES CSS */
.contentContainer table, .mce-content-body table,
.contentContainer tr, .mce-content-body tr,
.contentContainer td, .mce-content-body td {
    box-sizing: border-box;
}

/* Paired with max-width on cells to force wrapping */
.contentContainer td,.contentContainer th,
.contentContainer .mce-item-table td, .contentContainer .mce-item-table th {
    word-wrap: break-word;
    position: relative;
    vertical-align: top;
    border: none;
    border-top: none;
    border-bottom: none;
    border-left: none;
    border-right: none;
}

.contentContainer table[t_align='right'] {
    margin-left: auto;
}
.contentContainer table[t_align='center'] {
    margin: 0px auto;
}

[direction="rtl"] {
    direction: rtl;
}