/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* BUTTONS: MINIMAL */

/* Direction Up/Down Button - Standard,Hover,Click,Disabled */
.directionUpBtn_minimal, .directionDownBtn_minimal, .directionUpBtnHov_minimal, .directionDownBtnHov_minimal,
.directionUpBtnClick_minimal, .directionDownBtnClick_minimal, .directionUpBtnDisabled_minimal, .directionDownBtnDisabled_minimal {
	height: 21px;
	width: 50px;	
}
.directionUpBtn_minimal {
	background: url('../commonimages/button/orderBtn_minimal.gif') no-repeat 0px 0px;
}
.directionDownBtn_minimal {
	background: url('../commonimages/button/orderBtn_minimal.gif') no-repeat -50px 0px;
}
.directionUpBtnHov_minimal {
	background: url('../commonimages/button/orderBtn_minimal.gif') no-repeat 0px -21px;
}
.directionDownBtnHov_minimal {
	background: url('../commonimages/button/orderBtn_minimal.gif') no-repeat -50px -21px;
}
.directionUpBtnClick_minimal {
	background: url('../commonimages/button/orderBtn_minimal.gif') no-repeat 0px -42px;
}
.directionDownBtnClick_minimal {
	background: url('../commonimages/button/orderBtn_minimal.gif') no-repeat -50px -42px;
}
.directionUpBtnDisabled_minimal {
	background: url('../commonimages/button/orderBtn_minimal.gif') no-repeat 0px -63px;
}
.directionDownBtnDisabled_minimal {
	background: url('../commonimages/button/orderBtn_minimal.gif') no-repeat -50px -63px;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* HORIZONTAL DIVIDER: MINIMAL */
.horizontalDivider_minimal {
	background: url('../commonimages/divider_1px_shadow.gif') repeat-x center center;
	height: 3px;
	font-size: 1px;
}


/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* HORIZONTAL DIVIDER: MINIMAL */
.infoDiv_minimal {
	margin: 10px 5px;
	padding: 5px 10px;
	background-color: #eee;
	border: 1px solid #bbb;
	color: #333;
	-webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}


/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* POPUP: MINIMAL */
.popupWindow_minimal {
	position: absolute;
	z-index: 50;
}

.popupContentArea_minimal {
	padding: 0px;
	font-size: 12px;
}
.popupTLcorner_minimal div, .popupTRcorner_minimal div, .popupBLcorner_minimal div, .popupBRcorner_minimal div {
	width: 15px;
	height: 15px;
}
.popupTopEdge_minimal div, .popupBottomEdge_minimal div {
	height: 15px;
}
.popupLeftEdge_minimal div, .popupRightEdge_minimal div {
	width: 15px;
}
.popupTLcorner_minimal {
	background: url("../commonimages/popups/corners_minimal.gif") no-repeat left top;
}
.popupTopEdge_minimal {
	background: url("../commonimages/popups/horiEdge_minimal.gif") repeat-x center top;
}
.popupTRcorner_minimal {
	background: url("../commonimages/popups/corners_minimal.gif") no-repeat right top;
}
.popupLeftEdge_minimal {
	background: url("../commonimages/popups/vertEdge_minimal.gif") repeat-y left center;
}
.popupRightEdge_minimal {
	background: url("../commonimages/popups/vertEdge_minimal.gif") repeat-y right center;
}
.popupBLcorner_minimal {
	background: url("../commonimages/popups/corners_minimal.gif") no-repeat left bottom;
}
.popupBottomEdge_minimal {
	background: url("../commonimages/popups/horiEdge_minimal.gif") repeat-x center bottom;
}
.popupBRcorner_minimal {
	background: url("../commonimages/popups/corners_minimal.gif") no-repeat right bottom;
}

.popupHeader_minimal {
	font-size: 16px;
	text-align: left;
	cursor: move;
	color: #fff;
}
.popupDiv_minimal {
	border-bottom: 1px solid #ccc;
	height: 3px;
	font-size: 1px;
}


/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* STAGE CONTAINER: MINIMAL */
/* *** Icons *** */
.stageContainer .orderUpIcon,
.stageContainer .orderDownIcon,
.stageContainer .removeIcon,
.stageContainer .removeIconDisabled {
	display: inline-block;
	width: 24px;
	height: 24px;
	text-align: center;
	line-height: 26px;
	cursor: pointer;
	font-size: 20px;
}

.stageContainer .orderUpIcon:hover,
.stageContainer .orderDownIcon:hover,
.stageContainer .removeIcon:hover {
	color: #606060;
}
.stageContainer .removeIconDisabled {
	color: #bebebe;
}

/* *** Stages and Components *** */
.stageContainer .tableOutline TD.tableIndicatorCol {
	background: #f1edf4;
	border-bottom: 1px #fff solid;
	border-right: 1px #dadada solid;
	vertical-align: middle;
    color: #444;
}
.stageContainer .tableOutline TD.tableContentCol {
	background-color: #f1edf4;
	border-bottom: 1px #fff solid;
	padding: 16px;
}

.stageCardsSectionWrapper .stageContainer .tableOutline TD.tableIndicatorCol {
	background: #fff;
	border-bottom: 1px #dadada solid;
	border-right: 1px #dadada solid;
}

.stageCardsSectionWrapper .stageContainer .tableOutline TD.tableContentCol {
	background-color: #fff;
	border-bottom: 1px #dadada solid;
}

.stageContainer .tableOutline {
	border-radius: 3px;
	-moz-border-radius :3px;
	-webkit-border-radius: 3px;
}
.stageContainer .tableOutline TD.cellTopLeft {
    -webkit-border-top-left-radius: 3px;
    -moz-border-top-left-radius: 3px;
    border-top-left-radius: 3px;
}
.stageContainer .tableOutline TD.cellTopRight {
    -webkit-border-top-right-radius: 3px;
    -moz-border-top-right-radius: 3px;
    border-top-right-radius: 3px;
}
.stageContainer .tableOutline TD.cellBottomLeft {
    -webkit-border-bottom-left-radius: 3px;
    -moz-border-bottom-left-radius: 3px;
    border-bottom-left-radius: 3px;
}
.stageContainer .tableOutline TD.cellBottomRight {
    -webkit-border-bottom-right-radius: 3px;
    -moz-border-bottom-right-radius: 3px;
    border-bottom-right-radius: 3px;
}

.stageCardsSectionWrapper {
	position: relative;
}

.stageCardsSectionWrapper .zonesSection {
	position: absolute;
	top: 0;
}

.stageCardsSectionWrapper .stageSectionContainer {
	margin: 0 0 0 386px;
}

.stageCardsSectionWrapper .stageContainer {
	-webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .2);
	-moz-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .2);
	box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .2);
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	background: #fff;
}

.stageContainer {
	position: relative;
	margin: 0 0 32px;
}

.stageContainer .stageHeader {
	border-bottom: solid 1px #dadada;
}

.stageContainer .stageHeader .stageActions {
	float: right;
	margin: 8px 8px 8px 24px;
}

.stageContainer .stageHeader .stageActions > * {
	display: inline-block;
	margin: 0 0 0 12px;
}

.stageContainer .stageHeader .stageActions table.actionBtnTable {
	margin: 0;
}

.stageContainer .stageContent {
	padding: 16px;
}

.stageContainer .stageIndicator {
	display: inline-block;
	height: 48px;
	width: 48px;
	background: #6d3075;
	border-radius: 50%;
	font-size: 20px;
	line-height: 48px;
	color: #fff;
}
.stageContainer .stageLabel {
	font-size: 16px;
	font-weight: 600;
	color: #6d3075;
	margin: 0 22px;
	line-height: 48px;
	overflow: hidden;
	max-width: 440px;
	text-overflow: ellipsis;
}

.stageCardsSectionWrapper .stageContainer .actionBarHeaderLabel {
	margin-left: 0;
}

.stageCardsSectionWrapper .stageContainer .stageLabel {
	margin: 0 30px;
	line-height: 56px;
	color: #444;
}

.stageContainer .innerCellTable TD {
	padding: 0;
	vertical-align: middle;
}

.stageContainer .stageContainerText {
	color: #333;
	font-size: 12px;
	white-space: nowrap;
}


/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* TABLE: MINIMAL */
.contentTableHeaderTR_minimal {
    text-align: left;
    font-weight: bold;
    font-size: 15px;
    color: #393939;
}
.contentTableHeaderTRsecond_minimal {
    text-align: left;
    font-weight: bold;
    font-size: 13px;
    color: #393939;
}
.listHeader_minimal TD {
	padding-bottom: 15px;	
}


/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* TABS: MINIMAL */
.workflowTabsContainer {
	margin: 0 0 32px;
	padding: 0 0 32px;
	border-bottom: solid 1px #dadada;
	overflow: hidden;
	text-align: center;
}

.workflowTabsContainer > table {
	margin: 0 auto;
}

.bodyIframe .workflowTabsContainer {
	background: #f5f5f5;
	position: relative;
	z-index: 4;
	padding: 16px 0;
	margin-bottom: 0;
	-webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, .2);
	-moz-box-shadow: 0 0 3px 0 rgba(0, 0, 0, .2);
	box-shadow: 0 0 3px 0 rgba(0, 0, 0, .2);
}

.workflowTabsWrapper {
	display: table;
	margin: 0 auto;
}
.workflowTab, .workflowTabSelected, .workflowTabDisabled {
	display: table-cell;
	min-width: 80px;
	padding: 0 30px;
	height: 38px;
	border: 1px solid #dadada;
	color: #606060;
}
.workflowTabSelected {
	border-color: #6d3075;
	background: #6d3075;
	color: #fff;
}
.workflowTabSelected + .workflowTab {
	border-left-width: 0;
}
.workflowTab {
	border-right-width: 0;
	background: #fff;
	cursor: pointer;
}
.workflowTab:hover,
.workflowTab:focus {
	background: #dadada;
	color: #444;
}
.workflowTab:active {
	background: #f5f5f5;
	color: #444;
}
.workflowTabDisabled {
	background: #f5f5f5;
}
.workflowTabFirst {
	border-top-left-radius: 3px;
	-moz-border-radius-topleft: 3px;
	-webkit-border-top-left-radius: 3px;
	border-bottom-left-radius: 3px;
	-moz-border-radius-bottomleft: 3px;
	-webkit-border-bottom-left-radius: 3px;
}
.workflowTabLast {
	border-top-right-radius: 3px;
	-moz-border-radius-topright: 3px;
	-webkit-border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;
	-moz-border-radius-bottomright: 3px;
	-webkit-border-bottom-right-radius: 3px;
	border-right-width: 1px;
}
.workflowTabText {
	font-weight: 400;
	font-size: 14px;
	line-height: 38px;
	vertical-align: middle;
}
.workflowTabDisabled .workflowTabText {
	color: #a2a2a2;
}

.workflowButtonsContainer {
	z-index: 1;
	padding: 16px 20px;
	background: #f5f5f5;
	border-top: solid 1px #dadada;
	-webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, .2);
	-moz-box-shadow: 0 0 3px 0 rgba(0, 0, 0, .2);
	box-shadow: 0 0 3px 0 rgba(0, 0, 0, .2);
	-webkit-border-bottom-left-radius: 3px;
	-moz-border-radius-bottomleft: 3px;
	border-bottom-left-radius: 3px;
	-webkit-border-bottom-right-radius: 3px;
	-moz-border-radius-bottomright: 3px;
	border-bottom-right-radius: 3px;
}

.fixedButtonBar {
	z-index: 1;
	padding: 16px 20px;
	background: #f5f5f5;
	border-top: solid 1px #dadada;
	-webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, .2);
	-moz-box-shadow: 0 0 3px 0 rgba(0, 0, 0, .2);
	box-shadow: 0 0 3px 0 rgba(0, 0, 0, .2);
	-webkit-border-bottom-left-radius: 3px;
	-moz-border-radius-bottomleft: 3px;
	border-bottom-left-radius: 3px;
	-webkit-border-bottom-right-radius: 3px;
	-moz-border-radius-bottomright: 3px;
	border-bottom-right-radius: 3px;
}

.tab_minimal, .tabHov_minimal, .tabSelected_minimal, .tabSelected_editor_minimal {
	width: 94px;
	position: relative;
	cursor: default;
	height: 27px;
	top: 3px;
}
.tab_minimal {
	background: url('../commonimages/tabs/tabs_minimal.gif') repeat-x center bottom;
	color: #565656;
}
.tabHov_minimal {
	background: url('../commonimages/tabs/tabs_minimal.gif') repeat-x center top;
	color: #565656;
}		
.tabSelected_minimal {
	background: url('../commonimages/tabs/tabs_minimal_selected.gif') repeat-x center bottom;
}
.tabSelected_editor_minimal {
	background: url('../commonimages/tabs/tabs_editor_minimal_selected.gif') repeat-x center bottom;
	font-weight: bold;
	color: #414141;
	text-shadow: 0px 1px 0px #f1f1f1;
}