.ruleConditionParameterizedContainer {
	padding: 1px 5px;
	border: 1px solid #dadada;
	background-color: #f5f5f5;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

.ruleSearchContainer, .targetGroupSearchContainer {
	position: relative;
	z-index: 2;
	width: 244px;
	background: #f5f5f5;
}

.ruleListContainer, .targetGroupListContainer {
	background: #f5f5f5;
	border-bottom: solid 1px #dadada;
	overflow: auto;
	overflow-x: hidden;
	max-height: 360px;
}
.ruleListContainer {
	padding-bottom: 24px;
}
.ruleSearchInputContainer, .targetGroupSearchInputContainer, .addTargetGroupInputContainer {
	background: #fff;
	border-bottom: 1px solid #dadada;
	padding: 8px 16px;
}
.targetGroupSearchInputContainer,
.ruleSearchInputContainer {
	border-top: solid 1px #dadada;
}
.addTargetGroupInputContainer {
	padding: 16px;
}
.addTargetGroupInputContainer > * {
	margin: 0 auto;
}
.targetGroupSearchInputContainer .advancedSearchIconDiv,
.targetGroupSearchInputContainer .advancedSearchIconDiv_active,
.ruleSearchInputContainer .advancedSearchIconDiv,
.ruleSearchInputContainer .advancedSearchIconDiv_active {
	top: 10px;
	left: 152px;
}

.targetGroupDetailsContainer {
	background: #f5f5f5;
}

.targetingGroupContent  {
	margin-top: -1px;
}

.ruleRelationshipToggleContainer, .targetGroupRelationshipToggleContainer {
	background: #fff;
	border-top: solid 1px #dadada;
	border-bottom: 1px solid #dadada;
	padding: 8px 24px;
	vertical-align: middle;
}
.targetGroupRelationshipToggleDisplay,
.ruleRelationshipToggleDisplay {
	float: right;
	margin: 4px 0;
	font-size: 12px;
	color: #606060;
}
.ruleRelationshipToggleDisplay {
	float: none;
}

.targetGroupItemsContainer,
.ruleItemsContainer {
	min-height: 56px;
	/*height: 256px;*/
	margin-bottom: -32px;
}

.ruleContainer, .targetGroupContainer {
	position: relative;
	z-index: 3;
	overflow: hidden;
	padding: 40px 56px 120px;
}

.ruleContainer {
	min-height: calc(100% - 218px);
}

.droppable.ruleContainer, .droppable .targetGroupContainer {
	-webkit-box-shadow: 0 0 8px 0 #9069a5;
	-moz-box-shadow: 0 0 8px 0 #9069a5;
	box-shadow: 0 0 8px 0 #9069a5;
	border-radius: 2px;
	padding: 120px 56px 40px;
}

.droppable-item-placement {
	display: none;
	position: absolute;
	top: 40px;
	width: 80%;
	border: dashed 1px #bebebe;
	padding: 16px;
	border-radius: 3px;
	font-size: 16px;
	line-height: 16px;
	color: #444;
}

.droppable-item-placement .mp-icon {
	position: absolute;
	left: -34px;
}

.droppable .info_noActiveTargetGroups {
	visibility: hidden;
}

.droppable .droppable-item-placement {
	display: block;
}

.ruleContentContainer, .targetGroupContentContainer {
	min-height: 592px;
	background: #fff;
	border-left: solid 1px #dadada;
}

.rulePreviewHeader, .targetGroupPreviewHeader {
	border-bottom: 1px solid #bebebe;
	padding: 5px 12px; 	
}

.ruleRelationshipIndicator, .targetGroupRelationshipIndicator {
	padding: 0 0 4px;
	line-height: 16px;
	font-size: 12px;
	font-weight: 600;
}
.ruleSearchInput, .targetGroupSearchInput {
	width: 100%;
	padding-right: 72px !important;
	padding-left: 40px !important;
	color: #bebebe;
}
.ruleSearchInput:focus, .targetGroupSearchInput:focus {
	color: #444;
}
.ruleToken, .targetGroupToken {
	position: relative;
	z-index: 1;
    background: #fff;
    border-bottom: 1px solid #dadada;
    padding: 16px 40px 16px 16px;
    -webkit-touch-callout: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	line-height: 18px;
	font-size: 12px;
}
.tokenLabel {
	overflow: hidden;
	text-overflow: ellipsis;
}
.ruleToken.ui-draggable-dragging, .targetGroupToken.ui-draggable-dragging {
	background: #f5f5f5;
	z-index: 4 !important;
}
.ruleToken .draggable-item-indicator,
.targetGroupToken .draggable-item-indicator {
	position: absolute;
	right: 16px;
	top: 50%;
	margin-top: -16px;
}
.ruleToken:hover, .targetGroupToken:hover {
	background: #f5f5f5;
}
.rulesTable {
	padding: 8px 0;
}
.targetGroupHeaderLabel {
	display: inline-block;
	font-weight: bold;
	font-size: 14px;
	line-height: 40px;
}

.targetGroupDisplayContainer.hidden,
.ruleDisplayContainer.hidden {
	visibility: hidden;
	opacity: 0;
}

.targetGroupDisplayContainer,
.ruleDisplayContainer {
	visibility: visible;
	opacity: 1;
	transition: visibility 0s, opacity 1.5s ease;
	line-height: 32px;
}

.targetGroupActionsContainer {
	padding: 0 14px;
}

.ruleValueInput, .targetingValue {
	width: 100%;
}

.targetingSegmentationAnalysis {
	padding: 16px 40px;
	background: #fbe2a0;
	border-radius: 3px;
	text-align: center;
	font-size: 16px;
	line-height: 32px;
	font-weight: 600;
}

.grow-wrap {
	display: grid;
}
.grow-wrap::after {
	/* Note the space! Needed to prevent jumpy behavior */
	content: attr(data-replicated-value) " ";
	white-space: pre-wrap;
	font-size: 12px;
	padding: 8px 18px;
	line-height: normal;
	color: red;
	visibility: hidden;
	box-sizing: content-box;
}
.grow-wrap > textarea {
	resize: none;
	overflow: hidden;
	min-height: auto;
}
.grow-wrap > textarea, .grow-wrap::after {
	/* Place on top of each other */
	grid-area: 1 / 1 / 2 / 2;
	width: 100%;
	box-sizing: inherit;
	max-width: 330px;
	min-width: 238px;
}

