
/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* Navigation Tree */
.navTreeFixedItems {
    box-sizing: content-box;
    background-color: #f5f5f5;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* jsTree - Folders */
.folderTreeDiv {
    height: 100px;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #fff;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.selectedFixedItem {
    background: url(commonimages/tree/selectedFixedItem_grad.gif) no-repeat center center;
    font-weight: bold;
    font-style: italic;
    font-size: 1.1em;
}

.jstree-legacy-default .jstree-legacy-hovered {
    color: #414141;
}

.jstree-legacy-default .disabledTreeNode.jstree-legacy-hovered {
    color: #999;
}

.jstree-proton {
    font-family: inherit !important;
    color: #444;
}

.jstree-proton .jstree-wholerow-clicked {
    background: #6d3075 !important;
    color: #ffffff;
}

.jstree-proton .jstree-wholerow-hovered {
    background: #dadada !important;
    color: #ffffff;
}

.jstree-proton .jstree-hovered {
    color: #444 !important;
}

.jstree-proton li.jstree-leaf, .jstree-proton div.jstree-wholerow, .jstree-proton a.jstree-anchor, .jstree-proton i.jstree-icon {
    height: 32px;
    line-height: 32px;
 }

.jstree-proton li.jstree-node {
    position: relative;
}

.jstree-proton a.jstree-anchor i.jstree-icon {
    /*margin-right: 4px;*/
    line-height: 30px;
}

.jstree-proton a.jstree-anchor {
    line-height: 32px;
    /*margin: 0;*/
    padding-left: 6px;
}

.jstree-proton i.jstree-icon.jstree-ocl {
    margin: 0;
    padding: 10px 4px 10px 14px;
}

.jstree-proton .jstree-wholerow-clicked + .jstree-icon {
    color: #fff;
}

.jstree-proton .jstree-wholerow-hovered + .jstree-icon {
    color: #444;
}

.jstree-proton i.jstree-icon.jstree-ocl:empty {
    width: 6px !important;
    height: auto !important;
    line-height: initial !important;
}

.jstree-proton .jstree-node,
.jstree-proton .jstree-icon {
    background: none !important;
}


.jstree-proton i.jstree-icon.jstree-ocl {
    font: normal 900 normal 10px/1 "Font Awesome 6 Pro";
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-indent: inherit;
}

.jstree-proton .jstree-clicked {
    background: #6d3075;
    color: #ffffff;

}
.jstree-proton .jstree-checked{
    background: #6d3075;
    color: #ffffff;

}
.default-selected-child{
    background: white !important;
    color: grey !important;
    box-shadow: unset !important;
}

.jstree-proton .jstree-search {
    font-style: italic;
    color: #ce80d9;
    font-weight: bold;
}

.jstree-proton > .jstree-no-dots .jstree-open > .jstree-ocl::before {
    content: "\f0d7";
}

.jstree-proton > .jstree-no-dots .jstree-closed > .jstree-ocl::before {
    content: "\f0da";
}

.jstree-proton a.jstree-disabled {
    color: #aaa;
}

.jstree-proton i.jstree-icon.fa, .jstree-proton i.jstree-icon.far, .jstree-proton i.jstree-icon.fal, .jstree-proton i.jstree-icon.fas {
    width: auto !important;
    margin-left: 6px;
    margin-right: 8px !important;
    line-height: 30px !important;
}

ul.jstree-contextmenu.vakata-context {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    background: #fff;
    z-index: 1030;
}

.jstree-contextmenu.vakata-context i {
    display: none;
}

.jstree-contextmenu.vakata-context .vakata-contextmenu-sep {
    display: none;
}

.jstree-contextmenu.vakata-context a {
    padding: 0 1em;
    text-align: left;
}

.folderTreeDiv .jstree-legacy-clicked, div#actionPopup .jstree-legacy-clicked {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    background-color: #ddd;
}

.jstree-legacy-default li.selectedTreeNodeLi {
    border-top: 1px solid #9fa7cb;
    border-bottom: none;
    -webkit-border-radius: 6px 0 0 6px;
    -moz-border-radius: 6px 0 0 6px;
    border-radius: 6px 0 0 6px;
}

/* jsTree for comparison CSS */
/* TODO: Apply new-theme colors for this feature */
a.jstree-anchor[compare_state="added"]:not(.jstree-clicked){
    color: green;
}
a.jstree-anchor[compare_state="removed"]:not(.jstree-clicked){
    color: red;
}
a.jstree-anchor[compare_state="updated"]:not(.jstree-clicked){
    color: orange;
}
a.jstree-anchor[compare_state="changed"]:not(.jstree-clicked){
    color: #000;
    font-weight: bold;
}
a.jstree-anchor[compare_state="unchanged"]:not(.jstree-clicked){
    color: #555;
}

/* START Legacy jsTree CSS */
.jstree-default .jstree-hovered {
    color: #414141;
}

.jstree-default .disabledTreeNode.jstree-hovered {
    color: #999;
}

.folderTreeDiv .jstree-default .jstree-clicked, div#actionPopup .jstree-default .jstree-clicked {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    background-color: #ddd;
}

.jstree-default li.selectedTreeNodeLi {
    border-top: 1px solid #9fa7cb;
    border-bottom: none;
    -webkit-border-radius: 6px 0 0 6px;
    -moz-border-radius: 6px 0 0 6px;
    border-radius: 6px 0 0 6px;
}

/* END Legacy jsTree CSS */

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* NAVIGATION TREE CSS */
.treeNodeLink, .selectedNavItem {
    font-size: 12px;
    white-space: nowrap;
    text-decoration: none;
    padding-left: 3px;
    padding-right: 3px;
    margin-left: 1px;
}

.treeNodeLinkBold, .selectedNavItemBold {
    font-size: 12px;
    font-weight: bold;
    white-space: nowrap;
    text-decoration: none;
    padding-left: 3px;
    padding-right: 3px;
    margin-left: 1px;
}

.treeNodeLinkItalicBold, .selectedNavItemItalicBold {
    font-size: 12px;
    font-weight: bold;
    font-style: italic;
    white-space: nowrap;
    text-decoration: none;
    padding-left: 3px;
    padding-right: 3px;
    margin-left: 1px;
}

/* Width: 2000 prevents floating items in LI from wrapping
   Requires container to not scroll (genericTreeviewContainer) */
.treeContentDiv {
    overflow: auto;
    width: 2000px;
}

.containerMinHeight {
    min-height: 341px;
    background-color: #fff;
    vertical-align: top;
    margin-bottom: 5px;
}

.genericTreeviewContainer {
    font-weight: normal;
    width: 238px; /*leftColWidth*/
    overflow-x: hidden;
    padding: 0;
    background-color: #fdfdfd;
}

