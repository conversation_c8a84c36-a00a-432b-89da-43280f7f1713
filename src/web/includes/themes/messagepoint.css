/* Body */

html, body {
    height: 100%;
}

body {
    background: #f5f5f5;
    margin: 0;
    padding: 0;
    font-weight: normal;
    font-size: .8em;
    line-height: 1;
}

iframe {
    display: block;
}

select:not(.form-control):not(.custom-select) {
    height: 40px;
    line-height: 40px;
    padding: 0 18px;
    font-family: inherit;
    font-size: 12px;
    font-weight: normal;
    width: 125px;
    resize: none;
    border: #dadada 1px solid;
    text-shadow: none;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    background: #fff;
    outline: none;
}

select:not(.form-control):not(.custom-select) option {
    text-shadow: none;
}

input:not(.form-control):not(.form-control-plaintext):not(.form-check-input):not(.custom-control-input):not([type='range']):not(.custom-range):not(.custom-file-input), textarea:not(.form-control) {
    padding: 0 18px;
    font-size: 12px;
    min-height: 38px;
    line-height: 38px;
    text-shadow: none;
    outline: none;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    border: 1px solid #dadada;
    resize: none;
    color: #444;
}

textarea:not(.form-control) {
    line-height: normal;
    min-height: 76px;
    padding: 8px 18px 8px 18px;
}

.fullModalOpen {
    overflow: hidden;
}

.fullModalOpen #page {
    -webkit-filter: blur(2px);
    -moz-filter: blur(2px);
    -ms-filter: blur(2px);
    -o-filter: blur(2px);
    filter: blur(2px);
    height: 100%;
}

#contentWrapper {
    display: table;
    width: 100%;
}

.label {
    font-size: 11px;
    font-weight: normal;
    padding: 0;
    margin: 0;
    color: #505050; /*colorMediumText*/
}

.textCapitalize {
    text-transform: capitalize;
}

.breadcrumb {
    overflow: hidden;
    margin: 0 2px 0 0;
}

.breadcrumb .breadcrumbStep {
    float: left;
    color: #606060;
}

.breadcrumb .breadcrumbStep.current {
    color: #6d3075;
}

.breadcrumb .breadcrumbStep + .breadcrumbStep {
    margin: 0 0 0 14px;
}

.breadcrumb .breadcrumbIcon {
    margin: 3px 10px 0 0;
    font-size: 18px;
    vertical-align: top;
}

.breadcrumb .breadcrumbLink {
    text-decoration: none;
    color: inherit;
}
.btn:hover
.breadcrumb .breadcrumbLink:hover,
.breadcrumb .breadcrumbLink:focus {
    text-decoration: underline;
}


/* -- New Banner -- */

.bannerContainer {
    display: table-row;
    height: 72px;
    -webkit-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
    -moz-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
}

.logoImage {
    position: absolute;
    display: inline;
    top: 22px;
    left: 28px;
    height: 28px;
    width: auto;
    cursor: pointer
}

.autoScroll .logoImage {
    position: static;
}

/* ## Banner: Navigation Tabs */
#bannerNavigationTabs {
    position: absolute;
    display: inline;
    top: 0;
    left: 266px;
}

.bannerNavigationTabText {
    font-weight: 300;
    font-size: 14px;
    margin-right: 7px;
}

.bannerNavigationTab {
    color: rgba(255, 255, 255, 0.8);
    height: 72px;
    line-height: 72px;
    display: inline;
    float: left;
    padding: 0 15px;
    box-sizing: border-box;
    cursor: pointer;
}

.bannerNavigationTab:hover,
.bannerNavigationTabActive,
#bannerBranchInstance:hover,
#bannerBranchInstance.activeItem {
    background: rgba(0, 0, 0, 0.2);
    color: rgba(255, 255, 255, 1);
}

/* ## Banner: Context Summary */

#bannerContextInfo {
    float: right;
}

#bannerBranchInstance {
    float: right;
    height: 40px;
    padding: 20px 44px 12px 24px;
    text-align: left;
    font-size: 10px;
    color: #fdfdfd;
    cursor: pointer;
}

#bannerBranchInstance .userName {
    display: block;
    margin: 0 0 6px;
    font-size: 14px;
}

#bannerBranchInstance > .fa {
    position: absolute;
    top: 28px;
    right: 24px;
    font-size: 14px;
}

#bannerBranchInstance .branchInfo {
    color: rgba(255, 255, 255, 0.8);
}

#bannerBranchInstance .bannerBranch,
#bannerBranchInstance .bannerInstance {
    font-size: 12px;
    text-transform: uppercase;
    font-weight: 300;
}

#bannerBranchInstance .bannerBranch {
    margin: 0 8px 0 0;
}

#bannerBranchInstance .bannerInstance {
    margin: 0 0 0 6px;
}

.categoryDropMenu {
    visibility: hidden;
    position: absolute;
    top: -9999px;
    min-width: 120px;
    background: #fff;
    border-radius: 3px;
    -webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .3);
    -moz-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .3);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .3);
    z-index: 1000;
    color: #444;
    font-weight: 300;
    text-align: left;
    padding: 8px 0;
}

.categoryDropMenu::before {
    content: "\00a0";
    position: absolute;
    top: -8px;
    left: 50%;
    margin-left: -8px;
    width: 0;
    height: 0;
    z-index: 9001;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
}

.categoryDropMenu .categoryDropMenuHeader {
    position: relative;
    color: #6d3075;
}

.categoryDropMenu .categoryDropMenuHeader .categoryDropMenuTitle {
    font-size: 14px;
    font-weight: 600;
    padding: 10px 36px 8px 18px;
}

.categoryDropMenu .categoryDropMenuHeader .categoryDropMenuCloseButton {
    position: absolute;
    top: 2px;
    right: 12px;
    padding: 4px;
    font-size: 12px;
    color: #444;
}

.categoryDropMenu .categoryDropMenuHeader .categoryDropMenuCloseButton:hover {
    color: #777;
}

.categoryDropMenu .menuLinkContainer > a {
    display: block;
    font-size: 12px;
    text-decoration: none;
    line-height: 32px;
    padding: 0 18px;
    color: #444;
    white-space: nowrap;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.categoryDropMenu .menuLinkContainer > a:hover {
    background: #6d3075;
    color: #fff;
}

.categoryDropMenu .menuLinkContainer.current > a {
    background: #fff;
    color: #6d3075;
    cursor: default;
}

.categoryDropMenu .menuSectionHeader {
    display: block;
    font-size: 12px;
    line-height: 32px;
    font-weight: 600;
    padding: 0 18px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.categoryDropMenu .menuSectionSeparator {
    width: 100%;
    height: 1px;
    margin: 8px 0;
    background-color: #dadada;
}

.categoryDropMenu .menuItenArrow {
    display: none;
}

.categoryDropMenu .collapsiblePanel .menuItemArrow {
    position: absolute;
    top: 8px;
    right: 16px;
    line-height: 7px;
    color: #444;
    -webkit-transition: top .2s linear, -webkit-transform .2s linear;
    transition: top .2s linear, transform .2s linear;
}

.categoryDropMenu .collapsiblePanel .menuSectionHeader {
    position: relative;
    padding-right: 36px;
    white-space: nowrap;
    cursor: pointer;
}

.categoryDropMenu .collapsiblePanel .menuSectionHeader:hover {
    background: #6d3075;
    color: #fff;
}

.categoryDropMenu .collapsiblePanel .menuSectionHeader:hover .menuItemArrow {
    color: #fff;
}

.categoryDropMenu .collapsiblePanel.collapsing .menuItemArrow,
.categoryDropMenu .collapsiblePanel.collapseIn .menuItemArrow {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
    top: 7px;
}

.categoryDropMenu .collapsiblePanel .menuGroup {
    display: none;
}

.categoryDropMenu .collapsiblePanel.collapseIn .menuGroup {
    display: block;
}

/* Touchpoint Widget */

#touchpointWidgetSection {
    float: left;
    width: 326px;
}

#widgetControlsContainer {
    height: 56px;
    margin: 0 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    -webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .3);
    -moz-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .3);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .3);
    display: inline-flex;
}

#touchpointsContentWidgetSection {
    float: right;
    width: 100%;
    -webkit-transition: width .2s linear;
    transition: width .2s linear;
}

#touchpointsContentWidgetSection.collapsed {
    width: 884px;
}

.tableComponentWrapper,
.panelComponentWrapper {
    background: #fff;
    -webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.2);
    -webkit-border-top-left-radius: 3px;
    -moz-border-radius-topleft: 3px;
    border-top-left-radius: 3px;
    -webkit-border-top-right-radius: 3px;
    -moz-border-radius-topright: 3px;
    border-top-right-radius: 3px;
}

.panelContent {
    padding: 24px 28px 32px;
}

.bottomSectionButtonsContainer {
    padding: 24px 0 0;
}

.bottomSectionButtonsContainer > * + * {
    margin: 0 0 0 20px;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* ACTION BAR CSS */
.actionsBarContainer {
    height: 56px;
    border: solid 1px #dadada;
    background: #fff;
    padding: 0 8px;
}

.actionsBarContainer .touchpointWidgetToggle {
    float: left;
    height: 56px;
    margin-left: -8px;
    padding: 0 14px;
    border-right: solid 1px #dadada;
    line-height: 56px;
    color: #444;
    font-size: 22px;
    cursor: pointer;
    -webkit-border-top-left-radius: 3px;
    -moz-border-radius-topleft: 3px;
    border-top-left-radius: 3px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.actionsBarContainer .touchpointWidgetToggle:hover,
.actionsBarContainer .touchpointWidgetToggle:focus {
    background: #dadada;
}

.actionsBarContainer {
    -webkit-border-top-left-radius: 3px;
    -moz-border-radius-topleft: 3px;
    border-top-left-radius: 3px;
    -webkit-border-top-right-radius: 3px;
    -moz-border-radius-topright: 3px;
    border-top-right-radius: 3px;
}

.actionsBarFilterContainer {
    position: relative;
    height: 40px;
    padding: 16px 0 15px;
    background: #fff;
    border: 1px solid #dadada;
    border-top: 0;
    color: #444;
    font-size: 12px;
}

.actionsBarFilterContainer TD {
    white-space: nowrap;
    padding: 5px 0;
}

.actionsBarFilterContainer .filterControl {
    float: left;
}

.actionsBarFilterContainer .mainFilterSection {
    position: absolute;
    top: 16px;
    right: 20px;
    height: 48px;
    margin: -8px 0 0;
    padding: 8px 0 0;
}

.actionsBarFilterContainer .mainFilterSection .filterControl + .filterControl {
    margin-left: 8px;
}

.actionBarHeaderLabel {
    float: left;
    line-height: 56px;
    font-weight: 300;
    font-size: 18px;
    color: #444;
    margin-left: 20px;
}

.selectionContextLabel {
    margin-top: -32px;
    padding: 0 0 32px;
    line-height: 24px;
    font-size: 16px;
    color: #606060;
    font-weight: 400;
    white-space: nowrap;
    text-align: left;
}

.selectionContextLabel .taskIndicator {
    color: #6d3075;
}

.actionBarButtonsContainer {
    float: right;
    height: 56px;
}

.actionBarButtonsContainer .actionBtnTable {
    float: left;
    margin: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
}

.colVisToggleContainer {
/*    position: relative;
    float: right; !important;
    width: 24px;
    height: 24px;
    padding: 6px;
    line-height: 26px;
    margin: 2px 0;
    text-align: center;*/
}

/*
.colVisToggleContainer .ColVis {
    position: absolute;
    right: 0;
    top: -25px;
    width: 15px;
    height: 14px;
}
*/

.dropdownListContainer {
    display: none;
    position: absolute;
    top: -9999px;
    min-width: 120px;
    background: #fff;
    border-radius: 3px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
    z-index: 1000;
    color: #444;
    text-align: left;
    padding: 8px 0;
}

.dropdownListContainer::before {
    content: "\00a0";
    position: absolute;
    top: -8px;
    left: 50%;
    margin-left: -8px;
    width: 0;
    height: 0;
    z-index: 9001;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
}

.dropndownList {
    margin: 0;
    padding: 0;
}

.dropndownList .dropndownItem {
    display: block;
    font-size: 12px;
    font-weight: 300;
    text-decoration: none;
    line-height: 32px;
    padding: 0 18px;
    color: #444;
    white-space: nowrap;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: pointer;
}

.dropndownList .dropndownItem:hover {
    background: #6d3075;
    color: #fff;
}

.contentContainer * {
	box-sizing: content-box;
}
.contentContainer * {
	line-height: 1;
}

.contentContainer .staticContentItem[type="1"], .staticContentItem[type="1"], .contentContainer .innerStaticContentItem[type="1"], .innerStaticContentItem[type="1"], mpr_variable {
    background: #ffff01;
}

.contentContainer .staticContentItem:not([type="1"]), .staticContentItem:not([type="1"]), .contentContainer .innerStaticContentItem:not([type="1"]), .innerStaticContentItem:not([type="1"]) {
    background: #62f4eb;
}

.contentContainer table .staticContentItem, table .staticContentItem, .contentContainer table .innerStaticContentItem, table .innerStaticContentItem {
    white-space: normal;
}

.contentContainer .staticContentItem, .staticContentItem, .contentContainer .innerStaticContentItem, .innerStaticContentItem {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

.contentContainer .staticContentItem .staticContentItem.embedded_content_tag, .staticContentItem.embedded_content_tag {
    background: #edf6fb;
    white-space: normal;
    padding: 0;
}

.contentContainer .staticContentItem.embedded_content_tag .varTagRenderedBlock, .staticContentItem.embedded_content_tag .varTagRenderedBlock {
    background: #edf6fb;
    display: block;
    white-space: normal;
}
[block_content=true] { 
	display: block; 
}

.contentContainer .innerStaticContentItem, .innerStaticContentItem {
    background: #dadada;
    white-space: normal;
    padding: 0;
}

.contentContainer nbsp {
    background-color: #ff4141;
}

.contentContainer nbsp::selection,
nbsp::selection {
    background-color: #ff4141;
!important
}

.contentContainer nbsp::-moz-selection,
nbsp::-moz-selection {
    background-color: #ff4141;
!important
}

.contentContainer nbsp::-webkit-selection,
nbsp::-webkit-selection {
    background-color: #ff4141;
!important
}

.contentContainer .staticContentItem.active::selection,
.staticContentItem.active::selection {
    background-color: #d6d6d6;
!important
}

.contentContainer .staticContentItem.active::-moz-selection,
.staticContentItem.active::-moz-selection {
    background-color: #d6d6d6;
!important
}

.contentContainer .staticContentItem.active::-webkit-selection,
.staticContentItem.active::-webkit-selection {
    background-color: #d6d6d6;
!important
}

.contentContainer .staticContentItem[type="1"].active::selection,
.staticContentItem[type="1"]::selection {
    background-color: #ffff55;
!important
}

.contentContainer .staticContentItem:not([type="1"]).active::selection,
.staticContentItem:not([type="1"])::selection {
    background-color: #62f4eb;
!important
}

.contentContainer .staticContentItem[type="1"].active::-moz-selection,
.staticContentItem[type="1"]::-moz-selection {
    background-color: #ffff55;
!important
}

.contentContainer .staticContentItem:not([type="1"]).active::-moz-selection,
.staticContentItem:not([type="1"])::-moz-selection {
    background-color: #62f4eb;
!important
}

.contentContainer .staticContentItem[type="1"].active::-webkit-selection,
.staticContentItem[type="1"]::-webkit-selection {
    background-color: #ffff55;
!important
}

.contentContainer .staticContentItem:not([type="1"]):not([type="100"]).active::-webkit-selection,
.staticContentItem:not([type="1"]):not([type="100"])::-webkit-selection {
    background-color: #62f4eb;
!important
}

.contentContainer .staticContentItem.embedded_content_tag.active::selection,
.contentContainer span.active .varTagRenderedBlock::selection,
.staticContentItem.embedded_content_tag.active::selection {
    background-color: #d6d6d6;
!important
}

.contentContainer .staticContentItem.embedded_content_tag.active::-moz-selection,
.contentContainer span.active .varTagRenderedBlock::-moz-selection,
.staticContentItem.embedded_content_tag.active::-moz-selection {
    background-color: #d6d6d6;
!important
}

.contentContainer .staticContentItem.embedded_content_tag.active::-webkit-selection,
.contentContainer span.active .varTagRenderedBlock::-webkit-selection,
.staticContentItem.embedded_content_tag.active::-webkit-selection {
    background-color: #d6d6d6;
!important
}

.contentContainer .staticContentItem.embedded_content_tag::selection,
.contentContainer .embedded_content_tag::selection,
.contentContainer .embedded_content_tag span::selection,
.staticContentItem.embedded_content_tag::selection {
    background-color: #edf6fb;
!important
}

.contentContainer .staticContentItem.embedded_content_tag::-moz-selection,
.contentContainer .embedded_content_tag::-moz-selection,
.contentContainer .embedded_content_tag span::-moz-selection,
.staticContentItem.embedded_content_tag::-moz-selection {
    background-color: #edf6fb;
!important
}

.contentContainer .staticContentItem.embedded_content_tag::-webkit-selection,
.contentContainer .embedded_content_tag::-webkit-selection,
.contentContainer .embedded_content_tag span::-webkit-selection,
.staticContentItem.embedded_content_tag::-webkit-selection {
    background-color: #edf6fb;
!important
}

.contentContainer ul li:before {
    content: "\2022";
}
.contentContainer li ul > li:before {
    content: "\25E6";
}
.contentContainer li li ul > li:before {
    content: "\25AA";
}

.contentContainer li  {
    position: relative;
}
.contentContainer li:before {
    display: inline-block;
    position: absolute;
}

.contentContainer ol, .contentContainer ul {
    list-style: none;
    padding: 0px;
    margin: 0px;
}
.contentContainer li > div {
    position: relative;
}

.contentContainer li, .contentContainer li li, .contentContainer li li li,
.contentContainer li li li li, .contentContainer li li li li li,
.contentContainer li li li li li li, .contentContainer li li li li li li li,
.contentContainer li li li li li li li li, .contentContainer li li li li li li li li li,
.contentContainer li li li li li li li li li li {
    left: 0px;
}

.contentContainer ol > li:before {
    content: counter(level1, decimal) ".";
}
.contentContainer li:before,
.contentContainer [listclass] li:before {
    left: 25px;
}
.contentContainer li li:before,
.contentContainer [listclass] li li:before {
    left: 50px;
}
.contentContainer li li li:before,
.contentContainer [listclass] li li li:before {
    left: 75px;
}
.contentContainer li li li li:before,
.contentContainer [listclass] li li li li:before {
    left: 100px;
}
.contentContainer li li li li li:before,
.contentContainer [listclass] li li li li li:before {
    left: 125px;
}
.contentContainer li li li li li li:before,
.contentContainer [listclass] li li li li li li:before {
    left: 150px;
}
.contentContainer li li li li li li li:before,
.contentContainer [listclass] li li li li li li li:before {
    left: 175px;
}
.contentContainer li li li li li li li li:before,
.contentContainer [listclass] li li li li li li li li:before {
    left: 200px;
}
.contentContainer li li li li li li li li li:before,
.contentContainer [listclass] li li li li li li li li li:before {
    left: 225px;
}
.contentContainer li li li li li li li li li li:before,
.contentContainer [listclass] li li li li li li li li li li:before {
    left: 250px;
}

.contentContainer li > div.mceListItemContent,
.contentContainer [listclass] li > div.mceListItemContent
{
    left: 50px;
    width: calc(100% - 50px);
}
.contentContainer li li > div.mceListItemContent,
.contentContainer [listclass] li li > div.mceListItemContent
{
    left: 75px;
    width: calc(100% - 75px);
}
.contentContainer li li li > div.mceListItemContent,
.contentContainer [listclass] li li li > div.mceListItemContent
{
    left: 100px;
    width: calc(100% - 100px);
}
.contentContainer li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li > div.mceListItemContent
{
    left: 125px;
    width: calc(100% - 125px);
}
.contentContainer li li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li li > div.mceListItemContent
{
    left: 150px;
    width: calc(100% - 150px);
}
.contentContainer li li li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li li li > div.mceListItemContent
{
    left: 175px;
    width: calc(100% - 175px);
}
.contentContainer li li li li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li li li li > div.mceListItemContent
{
    left: 200px;
    width: calc(100% - 200px);
}
.contentContainer li li li li li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li li li li li > div.mceListItemContent
{
    left: 225px;
    width: calc(100% - 225px);
}
.contentContainer li li li li li li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li li li li li li > div.mceListItemContent
{
    left: 250px;
    width: calc(100% - 250px);
}
.contentContainer li li li li li li li li li li > div.mceListItemContent,
.contentContainer [listclass] li li li li li li li li li li > div.mceListItemContent
{
    left: 275px;
    width: calc(100% - 275px);
}

.contentContainer ul[data-list-style-type=circle] > li:before {
    content: "\2022" !important;
}
.contentContainer ul[data-list-style-type=disc] > li:before {
    content: "\25E6" !important;
}
.contentContainer ul[data-list-style-type=square] > li:before {
    content: "\25AA" !important;
}

.contentContainer ol                              { counter-reset: level1 }
.contentContainer li ol                           { counter-reset: level2 }
.contentContainer li li ol                        { counter-reset: level3 }
.contentContainer li li li ol                     { counter-reset: level4 }
.contentContainer li li li li ol                  { counter-reset: level5 }
.contentContainer li li li li li ol               { counter-reset: level6 }
.contentContainer li li li li li li ol            { counter-reset: level7 }
.contentContainer li li li li li li li ol         { counter-reset: level8 }
.contentContainer li li li li li li li li ol      { counter-reset: level9 }
.contentContainer li li li li li li li li li ol   { counter-reset: level10 }
.contentContainer ol > li::before                             { content: counter(level1, decimal) "."; counter-increment: level1; }
.contentContainer li ol > li::before                          { content: counter(level2, decimal) "."; counter-increment: level2; }
.contentContainer li li ol > li::before                       { content: counter(level3, decimal) "."; counter-increment: level3; }
.contentContainer li li li ol > li::before                    { content: counter(level4, decimal) "."; counter-increment: level4; }
.contentContainer li li li li ol > li::before                 { content: counter(level5, decimal) "."; counter-increment: level5; }
.contentContainer li li li li li ol > li::before              { content: counter(level6, decimal) "."; counter-increment: level6; }
.contentContainer li li li li li li ol > li::before           { content: counter(level7, decimal) "."; counter-increment: level7; }
.contentContainer li li li li li li li ol > li::before        { content: counter(level8, decimal) "."; counter-increment: level8; }
.contentContainer li li li li li li li li ol > li::before     { content: counter(level9, decimal) "."; counter-increment: level9; }
.contentContainer li li li li li li li li li ol > li::before  { content: counter(level10, decimal) "."; counter-increment: level10; }

.contentContainer ol[data-list-style-type=lower-alpha] > li::before                             { content: counter(level1, lower-alpha) "."; counter-increment: level1; }
.contentContainer li ol[data-list-style-type=lower-alpha] > li::before                          { content: counter(level2, lower-alpha) "."; counter-increment: level2; }
.contentContainer li li ol[data-list-style-type=lower-alpha] > li::before                       { content: counter(level3, lower-alpha) "."; counter-increment: level3; }
.contentContainer li li li ol[data-list-style-type=lower-alpha] > li::before                    { content: counter(level4, lower-alpha) "."; counter-increment: level4; }
.contentContainer li li li li ol[data-list-style-type=lower-alpha] > li::before                 { content: counter(level5, lower-alpha) "."; counter-increment: level5; }
.contentContainer li li li li li ol[data-list-style-type=lower-alpha] > li::before              { content: counter(level6, lower-alpha) "."; counter-increment: level6; }
.contentContainer li li li li li li ol[data-list-style-type=lower-alpha] > li::before           { content: counter(level7, lower-alpha) "."; counter-increment: level7; }
.contentContainer li li li li li li li ol[data-list-style-type=lower-alpha] > li::before        { content: counter(level8, lower-alpha) "."; counter-increment: level8; }
.contentContainer li li li li li li li li ol[data-list-style-type=lower-alpha] > li::before     { content: counter(level9, lower-alpha) "."; counter-increment: level9; }
.contentContainer li li li li li li li li li ol[data-list-style-type=lower-alpha] > li::before  { content: counter(level10, lower-alpha) "."; counter-increment: level10; }

.contentContainer ol[data-list-style-type=upper-alpha] > li::before                             { content: counter(level1, upper-alpha) "."; counter-increment: level1; }
.contentContainer li ol[data-list-style-type=upper-alpha] > li::before                          { content: counter(level2, upper-alpha) "."; counter-increment: level2; }
.contentContainer li li ol[data-list-style-type=upper-alpha] > li::before                       { content: counter(level3, upper-alpha) "."; counter-increment: level3; }
.contentContainer li li li ol[data-list-style-type=upper-alpha] > li::before                    { content: counter(level4, upper-alpha) "."; counter-increment: level4; }
.contentContainer li li li li ol[data-list-style-type=upper-alpha] > li::before                 { content: counter(level5, upper-alpha) "."; counter-increment: level5; }
.contentContainer li li li li li ol[data-list-style-type=upper-alpha] > li::before              { content: counter(level6, upper-alpha) "."; counter-increment: level6; }
.contentContainer li li li li li li ol[data-list-style-type=upper-alpha] > li::before           { content: counter(level7, upper-alpha) "."; counter-increment: level7; }
.contentContainer li li li li li li li ol[data-list-style-type=upper-alpha] > li::before        { content: counter(level8, upper-alpha) "."; counter-increment: level8; }
.contentContainer li li li li li li li li ol[data-list-style-type=upper-alpha] > li::before     { content: counter(level9, upper-alpha) "."; counter-increment: level9; }
.contentContainer li li li li li li li li li ol[data-list-style-type=upper-alpha] > li::before  { content: counter(level10, upper-alpha) "."; counter-increment: level10; }

.contentContainer ol[data-list-style-type=lower-greek] > li::before                             { content: counter(level1, lower-greek) "."; counter-increment: level1; }
.contentContainer li ol[data-list-style-type=lower-greek] > li::before                          { content: counter(level2, lower-greek) "."; counter-increment: level2; }
.contentContainer li li ol[data-list-style-type=lower-greek] > li::before                       { content: counter(level3, lower-greek) "."; counter-increment: level3; }
.contentContainer li li li ol[data-list-style-type=lower-greek] > li::before                    { content: counter(level4, lower-greek) "."; counter-increment: level4; }
.contentContainer li li li li ol[data-list-style-type=lower-greek] > li::before                 { content: counter(level5, lower-greek) "."; counter-increment: level5; }
.contentContainer li li li li li ol[data-list-style-type=lower-greek] > li::before              { content: counter(level6, lower-greek) "."; counter-increment: level6; }
.contentContainer li li li li li li ol[data-list-style-type=lower-greek] > li::before           { content: counter(level7, lower-greek) "."; counter-increment: level7; }
.contentContainer li li li li li li li ol[data-list-style-type=lower-greek] > li::before        { content: counter(level8, lower-greek) "."; counter-increment: level8; }
.contentContainer li li li li li li li li ol[data-list-style-type=lower-greek] > li::before     { content: counter(level9, lower-greek) "."; counter-increment: level9; }
.contentContainer li li li li li li li li li ol[data-list-style-type=lower-greek] > li::before  { content: counter(level10, lower-greek) "."; counter-increment: level10; }

.contentContainer ol[data-list-style-type=lower-roman] > li::before                             { content: counter(level1, lower-roman) "."; counter-increment: level1; }
.contentContainer li ol[data-list-style-type=lower-roman] > li::before                          { content: counter(level2, lower-roman) "."; counter-increment: level2; }
.contentContainer li li ol[data-list-style-type=lower-roman] > li::before                       { content: counter(level3, lower-roman) "."; counter-increment: level3; }
.contentContainer li li li ol[data-list-style-type=lower-roman] > li::before                    { content: counter(level4, lower-roman) "."; counter-increment: level4; }
.contentContainer li li li li ol[data-list-style-type=lower-roman] > li::before                 { content: counter(level5, lower-roman) "."; counter-increment: level5; }
.contentContainer li li li li li ol[data-list-style-type=lower-roman] > li::before              { content: counter(level6, lower-roman) "."; counter-increment: level6; }
.contentContainer li li li li li li ol[data-list-style-type=lower-roman] > li::before           { content: counter(level7, lower-roman) "."; counter-increment: level7; }
.contentContainer li li li li li li li ol[data-list-style-type=lower-roman] > li::before        { content: counter(level8, lower-roman) "."; counter-increment: level8; }
.contentContainer li li li li li li li li ol[data-list-style-type=lower-roman] > li::before     { content: counter(level9, lower-roman) "."; counter-increment: level9; }
.contentContainer li li li li li li li li li ol[data-list-style-type=lower-roman] > li::before  { content: counter(level10, lower-roman) "."; counter-increment: level10; }

.contentContainer ol[data-list-style-type=upper-roman] > li::before                             { content: counter(level1, upper-roman) "."; counter-increment: level1; }
.contentContainer li ol[data-list-style-type=upper-roman] > li::before                          { content: counter(level2, upper-roman) "."; counter-increment: level2; }
.contentContainer li li ol[data-list-style-type=upper-roman] > li::before                       { content: counter(level3, upper-roman) "."; counter-increment: level3; }
.contentContainer li li li ol[data-list-style-type=upper-roman] > li::before                    { content: counter(level4, upper-roman) "."; counter-increment: level4; }
.contentContainer li li li li ol[data-list-style-type=upper-roman] > li::before                 { content: counter(level5, upper-roman) "."; counter-increment: level5; }
.contentContainer li li li li li ol[data-list-style-type=upper-roman] > li::before              { content: counter(level6, upper-roman) "."; counter-increment: level6; }
.contentContainer li li li li li li ol[data-list-style-type=upper-roman] > li::before           { content: counter(level7, upper-roman) "."; counter-increment: level7; }
.contentContainer li li li li li li li ol[data-list-style-type=upper-roman] > li::before        { content: counter(level8, upper-roman) "."; counter-increment: level8; }
.contentContainer li li li li li li li li ol[data-list-style-type=upper-roman] > li::before     { content: counter(level9, upper-roman) "."; counter-increment: level9; }
.contentContainer li li li li li li li li li ol[data-list-style-type=upper-roman] > li::before  { content: counter(level10, upper-roman) "."; counter-increment: level10; }



/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* Informational Systems */
.InfoSysContainer_info,
.InfoSysContainer_question,
.InfoSysContainer_error,
.InfoSysContainer_success {
    margin: 0 0 24px;
    padding: 14px 18px 18px;
    background: #b5dfef;
    text-align: left;
    color: #444;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

.InfoSysContainer_info:empty,
.InfoSysContainer_question:empty,
.InfoSysContainer_error:empty,
.InfoSysContainer_success:empty {
    display: none;
}

.contentEditor_InnerContentContainer .InfoSysContainer_info {
    margin: 24px;
}

.InfoSysContainer_question {
    background: #fbe2a0;
}

.InfoSysContainer_error {
    background: #fbc4bb;
}

.InfoSysContainer_success {
    background: #b9efb5;
}

.InfoSysContainer_info .icon,
.InfoSysContainer_question .icon,
.InfoSysContainer_error .icon,
.InfoSysContainer_success .icon {
    float: left;
    font-size: 24px;
}

.InfoSysContainer_info p,
.InfoSysContainer_question p,
.InfoSysContainer_error p,
.InfoSysContainer_success p {
    margin: 6px 0 0;
    padding: 0 0 0 36px;
    line-height: 18px;
    font-size: 12px;
    color: #444;
}

.InfoSysContainer_info p + p,
.InfoSysContainer_question p + p,
.InfoSysContainer_error p + p,
.InfoSysContainer_success p + p {
    margin-top: 8px;
}

.InfoSysContainer_info ul,
.InfoSysContainer_question ul,
.InfoSysContainer_error ul,
.InfoSysContainer_success ul {
    padding: 0 0 0 48px;
}

.InfoSysContainer_info ul > li,
.InfoSysContainer_question ul > li,
.InfoSysContainer_error ul > li,
.InfoSysContainer_success ul > li {
    padding: 4px 0;
    line-height: 16px;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* Pagination */
.searchPagination {
    margin: 6px; /*secondColPadding*/
    font-size: .8em;
    font-weight: bold;
    color: #505050; /*colorMediumText*/
    white-space: nowrap;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* Datepicker */
img.ui-datepicker-trigger {
    width: 1rem;
    margin-left: .5rem;
}

.hasGroupControl img.ui-datepicker-trigger,
.hasGroupControl img.ui-monthpicker-trigger {
    position: absolute;
}

.ui-datepicker select.ui-datepicker-month,
.ui-datepicker select.ui-datepicker-year {
    width: 50% !important;
    height: auto !important;
    padding-left: 8px !important;
    padding-right: 8px !important;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* Buttons */

.buttons {
    margin: 6px;
    margin-top: 0;
}

.btnSlim, .btnSlimDisabled {
    border: solid 1px;
    font-size: 12px;
    line-height: 10px;
    border-radius: 3px;
    font-weight: 600;
    display: inline-block;
}

.btnSlim {
    border-color: #6d3075;
    background: #fff;
}

.btnSlimDisabled {
    border-color: #9069a5;
    background: #9069a5;
}

.btnSlim a, .btnSlimDisabled a, .btnSlimDisabled {
    display: block;
    text-decoration: none;
}

.btnSlim a, .btnSlimDisabled a, .btnSlimDisabled {
    padding: 8px 16px;
    color: #6d3075 !important;
}

.btnSlimDisabled {
    display: inline-block;
    color: #fff !important;
}

.dataTablesContentWrapper {
    overflow-x: auto;
}

.dataTablesContentWrapper div.list-btn-container {
    position: absolute;
    right: 0px;
    top: 0px;
}
.dataTablesContentWrapper div.list-btn-group {
    margin-bottom: 6px;
}
.dataTablesContentWrapper div.list-btn-group .btn-group-name {
    font-size: 10px;
    right: 2px;
    position: relative;
    display: none;
}
.dataTablesContentWrapper tr:hover div.list-btn-group .btn-group-name {
    display: block;
}

.dataTablesContentWrapper tr div.list-btn-group .btn-list-close {
    margin-left: 6px;
}
.dataTablesContentWrapper tr div.list-btn-group .btn-no-edit i {
    color: #f11b1b;
}
.dataTablesContentWrapper .list-btn-group .btn-list-edit, .dataTablesContentWrapper .list-btn-group .btn-list-save,
.dataTablesContentWrapper .list-btn-group .btn-list-close, .dataTablesContentWrapper .list-btn-group .btn-no-edit {
    display: none;
}
.dataTablesContentWrapper tr:hover div.list-btn-group:not(.editor-enabled) .btn-list-edit,
.dataTablesContentWrapper tr div.list-btn-group.editor-enabled .btn-list-save,
.dataTablesContentWrapper tr div.list-btn-group.editor-enabled .btn-list-close,
.dataTablesContentWrapper tr:hover div.list-btn-group:not(.editor-enabled) .btn-no-edit
{
    display: initial;
}





.btnSlim > a:hover,
.btnSlim > a:focus,
.dataTables_wrapper td .btnSlim > a:hover,
.dataTables_wrapper td .btnSlim > a:focus {
    border-color: #6d3075;
    background: #6d3075;
    color: #fff !important;
}

.btnSlimDisabled a {
    color: #d6c7de;
}

/* Corner rounding */
.btn_roundAll {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

.btn_roundLeft {
    -webkit-border-radius: 3px 0 0 3px;
    -moz-border-radius: 3px 0 0 3px;
    border-radius: 3px 0 0 3px;
}

.btn_roundRight {
    -webkit-border-radius: 0 3px 3px 0;
    -moz-border-radius: 0 3px 3px 0;
    border-radius: 0 3px 3px 0;
}

/* Display: Containers and Menus */

.tableBtnPosition {
    margin: 0 0 0 6px;
}

/* .highlightBtn {
    To be changed. REMEMBER!
} */

.btnsContainer > * + * {
    margin-left: 12px;
}

.highlightedBtn, .highlightedBtnDisabled, .btnDisabled {
    /*display: inline-block;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    padding: 0 20px;
    color: #fff;*/
}

.btn {
    /*color: #444;*/
}

.highlightedBtn .icon, .highlightedBtnDisabled .icon, .btn .icon {
    /*vertical-align: middle;
    margin: 0 8px 0 0;
    line-height: 17px;
    font-size: 18px;*/
    margin: 0 8px 0 0;
}

.highlightedBtn .text, .highlightedBtnDisabled .text, .btnText, .btn .text {
    /*line-height: 38px;
    font-size: 14px;
    font-weight: 400;
    text-decoration: none;*/
}

.btnText a,
.btnText a:hover,
.btnText a:focus {
    color: inherit;
    text-decoration: none;
    cursor: inherit;
}

/*.btn {
    background: #fff;
    border: 1px solid #dadada;
}

.btn:hover {
    background: #dadada;
    border: 1px solid #dadada;
}

.btn:active {
    background: #f5f5f5;
    border: 1px solid #f5f5f5;
}*/

.btnDisabled .btnText {
    /*color: #a2a2a2;*/
}

.btnDisabled {
    /*background: #f5f5f5;
    border: 1px solid #f5f5f5;
    cursor: not-allowed !important;*/
}

.highlightedBtn {
    /*border: 1px solid #74438f;
    background: #74438f;*/
}

.highlightedBtn:hover {
    /*border-color: #5d3672;
    background: #5d3672;*/
}

.highlightedBtn:active {
    /*border-color: #9069a5;
    background: #9069a5;*/
}

.highlightedBtnDisabled {
    /*border: solid 1px #9069a5;
    background: #9069a5;
    cursor: not-allowed !important;*/
}

.highlightedBtnDisabled > * {
    /*color: #d6c7de;*/
}

.actionBarButtonsContainer > .actionBtn {
    float: left;
    margin: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
}

.hpTheme .highlightedBtn {
}

.hpTheme .highlightedBtn:hover {
}

.hpTheme .highlightedBtn:active {
}

.hpTheme .highlightedBtnDisabled {
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* Context Bar */
#contextBar {
/*    display: table-row;
    height: 48px;
    background-color: #fff;
    text-align: left;
    margin: 0;
    padding: 0;
    -webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .2);
    -moz-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .2);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .2);*/
}

.contextBarMenu {
    float: left;
    border-left: solid 1px #dadada;
    border-right: solid 1px #dadada;
}

.contextMenuIcon {
    position: relative;
    width: 80px;
    height: 48px !important;
    margin: 0;
    padding: 0;
    border: 0;
    border-left: solid 1px #dadada;
    border-right: solid 1px #dadada;
    float: left;
    opacity: 0.8;
}

.contextBarText {
    font-size: 14px;
    color: #444;
    font-weight: bold;
    white-space: nowrap;
    cursor: pointer;
}

.contextBarText.hasSubText {
    line-height: 24px;
    padding: 6px 0 0;
}

.contextBarSubText {
    font-size: 9px;
    color: #858585;
    font-wieght: bold;
    margin-left: 28px;
    line-height: 20px;
    position: absolute;
    top: 24px;
}

.contextBarBreadcrumb {
    font-size: 12.5px;
    color: #414141;
    white-space: nowrap;
}

.detailTipText, .txtFmtTipText, .menuOptionTipText {
    word-wrap: break-word;
    font-size: 10px;
    color: #414141;
    overflow: hidden;
}

.detailTip, .txtFmtTip, .contextBarTip, .menuOptionTip {
    cursor: default;
}

.contextBarBreadcrumb A {
    text-decoration: none;
    color: #2c40bb;
    cursor: pointer;
}

.touchpointContextMenuContainer {
    overflow: hidden;
}

.contextBarButton,
.contextBar_touchpointContextContainer {
    float: left;
    margin: 0;
    padding: 0 24px;
    line-height: 48px;
    background: #fff;
    text-align: left;
    font-size: 14px;
    color: #606060;
}

.contextBar_channelContext {
    line-height: 48px;
    height: 48px;
    float: left;
    border-left: solid 1px #dadada;
}
.contextBar_channelContext > ul,
.contextBar_channelContext > ul > li {
    line-height: 48px !important;
    height: 48px !important;
    top: 0 !important;
    bottom: 0 !important;
    vertical-align: 0 !important;
    margin: 0 0 0 0;
    left: 0 !important;
    right: 0 !important;
}

.contextBar_channelContext > ul > li {
    width: 48px !important;
}

.contextBar_channelContext > ul > li:hover {
    background: #dadada;
}

.contextBar_currentChannelContext {
    background: #dadada;
}



.contextBarButton .touchpointContextName,
.contextBarText {
    display: inline-block;
    vertical-align: top;
    margin: 0 0 0 10px;
    width: 362px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: inherit;
    font-weight: 400;
}

.contextBarButton .fa {
    margin: 0 0 0 10px;
}

.contextBarButton .fa-icon {
    margin: 0 -4px 0 0;
    font-size: 16px;
}

.contextBarButton + .contextBarButton {
    border-left: solid 1px #dadada;
}

.contextBarButton:hover:not(.static),
.contextBarButton:focus:not(.static),
.contextBarButton.selected:not(.static) {
    cursor: pointer;
    background: #dadada;
    color: #444;
}

.contextBarRightSecion {
    float: right;
}

.contextBarRightSecion .contextBarButton {
    border-left: solid 1px #dadada;
    position: relative;
    z-index: 1;
}

.touchpointContextMenuContentWrapper {
    position: relative;
    margin: 0 8px 8px;
    background: #fff;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    -webkit-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
    -moz-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .3);
}

.touchpointContextMenuContentWrapper::before {
    content: "\00a0";
    position: absolute;
    top: -8px;
    left: 50%;
    margin-left: -8px;
    width: 0;
    height: 0;
    z-index: 9001;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
}

.taskIndicatorContainer {
    position: relative;
    display: inline-block;
}

.taskIndicatorContainer .project_task_taskCount {
    position: absolute;
    left: 16px;
    top: 12px;
    padding: 0;
    background: none;
    line-height: 8px;
    text-shadow: none;
    font-size: 10px;
}

.contextMenuImage {
    margin-top: 15px;
    margin-left: 25px;
}

.contextMenuIcon:hover {
    background-color: #dadada;
    opacity: 1;
}

.contextBar_touchpointContextContainer_hover {
    background: url(commonimages/layout/context_bar_background_hover.gif) repeat-x left top;
}

.contextBar_touchpointContextContainer_active {
    background: #dadada;
}

.contextBar_touchpointContextDiabled {
    background-color: #e1e1e1;
}

.contextBar_touchpointContextDiabled:not(.contextBar_passive):hover {
    background-color: #e9e9e9;
}

.contextBar_globalContextContainer, .contextBar_globalContextContainerDisabled {
    height: 39px;
}

.globalContextEnabled {
    background-color: #dadada !important;
}

.contextBar_globalContextContainer {
    background: url(commonimages/layout/context_bar_background_active.gif) repeat-x left top;
}

.contextBar_globalContextContainerDisabled {
    background-color: #e5e5e5;
}

.contextBar_touchpointIcon {
    background: url(commonimages/icons/touchpoint_icon.gif) no-repeat center center;
    width: 16px;
    height: 16px;
}

.contextBar_collectionIcon {
    background: url(commonimages/icons/collection_icon.png) no-repeat center center;
    width: 16px;
    height: 16px;
}

.contextBar_workflowIcon, .contextBar_workflowIconHov, .contextBar_workflowIconDisabled {
    width: 16px;
    height: 18px;
}

.contextBar_workflowIcon {
    background: url(commonimages/icons/workflow_medium_icon.png) no-repeat 0 0;
}

.contextBar_workflowIconHov {
    background: url(commonimages/icons/workflow_medium_icon.png) no-repeat 0 -18px;
}

.contextBar_workflowIconDisabled {
    background: url(commonimages/icons/workflow_medium_icon.png) no-repeat 0 -36px;
}

.contextBar_globalIcon, .contextBar_globalIconHov, .contextBar_globalIconDisabled {
    width: 25px;
    height: 25px;
}

.contextBar_globalIcon {
    background: url(commonimages/icons/globe_medium_icon.png) no-repeat 0 0;
}

.contextBar_globalIconHov {
    background: url(commonimages/icons/globe_medium_icon.png) no-repeat 0 -25px;
}

.contextBar_globalIconDisabled {
    background: url(commonimages/icons/globe_medium_icon.png) no-repeat 0 -50px;
}

.contextBar_gearIcon {
    background: url(commonimages/icons/gear_icon.png) no-repeat center center;
    width: 16px;
    height: 17px;
}

.contextBar_wrenchIcon {
    background: url(commonimages/icons/wrench_icon.png) no-repeat 0 0;
    width: 15px;
    height: 15px;
}

.contextBar_hierarchyIcon {
    background: url(commonimages/icons/hierarchy_icon.png) no-repeat center center;
    width: 12px;
    height: 13px;
}

.contextBar_messagesIcon {
    background: url(commonimages/icons/messages_icon.png) no-repeat center center;
    width: 12px;
    height: 12px;
}

.contextBar_variantsIcon {
    background: url(commonimages/icons/variants_icon.png) no-repeat center center;
    width: 10px;
    height: 13px;
}

.contextBar_downArrowIcon {
    background: url(commonimages/icons/menu_expand_arrow.gif) no-repeat center center;
    width: 8px;
    height: 8px;
}

.contextBar_upArrowIcon {
    background: url(commonimages/icons/menu_contract_arrow.gif) no-repeat center center;
    width: 8px;
    height: 8px;
}

/* Context Bar - Menu */
.contextMenuContainer {
    background-color: #fbfbfb;
    border: 1px solid #bbb;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    -moz-box-shadow: 0 2px 7px 1px rgba(119, 119, 119, 0.5);
    -webkit-box-shadow: 0 2px 7px 1px rgba(119, 119, 119, 0.5);
    box-shadow: 0 2px 7px 1px rgba(119, 119, 119, 0.5);
}

.contextMenuHeader {
     background: url(commonimages/layout/context_menu_header_grad.gif) repeat-x left top;
     border-bottom: 1px solid #bbb;
     height: 23px;
     -webkit-border-radius: 5px 5px 0 0;
     -moz-border-radius: 5px 5px 0 0;
     border-radius: 5px 5px 0 0;
 }

.contextMenuTopArrow {
    background: url(commonimages/layout/menu_top_arrow.gif) no-repeat center center;
    width: 12px;
    height: 7px;
    position: relative;
    top: 1px;
}

.contextMenuHeaderText {
    padding-top: 4px;
}

.contextMenuText {
    font-size: 11px;
    color: #414141;
}

.contextMenuPageScreen {
    position: absolute;
    background-color: #000;
    z-index: 49;
    top: 0;
    left: 0;
    width: 100%;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* ClueTip */

.cluetip {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    color: #444;
}

.cluetip-jtip .cluetip-outer {
    border-color: #dadada;
}

.cluetip-jtip .ui-cluetip-header {
    background-color: #dadada;
}

.clueTipTitle {
    font-size: 12px;
    line-height: 18px;
}

.clueTipContent {
    font-size: 11px;
    line-height: 16px
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* ExpandableTxt */
.expandTxtInd, .contractTxtInd {
	color: blue;
	cursor: pointer;
	font-size: 11px;
	text-transform: lowercase;
}
.contractTxtInd {
	padding-left: 4px;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* SELECT INPUT */
.numericSelect {
    padding-left: 7px;
    width: 50px
}

.numericSelect option, .selectHighlight {
    padding-left: 18px;
}

.selectHighlight {
    background-color: #dddddd;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* CHECKBOX AREA INPUT */
.checkboxArea {
    padding-top: 3px;
    border: 1px solid #7f9db9;
    white-space: nowrap;
    overflow: auto;
    width: 198px;
    height: 94px;
    background-color: #fff;
}

.checkboxArea div {
    padding-left: 3px;
    border-width: 0;
}

.checkboxArea input {
    width: 14px;
    height: 12px;
    border: none;
}

.checkboxArea span {
    cursor: default;
}

.checkboxRowHover {
    background-color: #dddddd;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* FULL WIDTH IFRAME CSS */
.bodyIframe #container {
    background: transparent;
}

.bodyIframe #container {
    position: relative;
    margin: 0 auto;
    padding: 0;
    text-align: left;
}

.bodyIframe {
    background-color: transparent;
}

.bodyIframe #containerSubTabsContent {
    padding: 0;
    width: 100%;
}

.iFramePopupButtonBar {
    position: fixed;
    bottom: 0;
}

.contentTableIframe, .contentTableIframeExtended {
    background-color: #fff;
    -webkit-border-bottom-left-radius: 3px;
    -moz-border-radius-bottomleft: 3px;
    border-bottom-left-radius: 3px;
    -webkit-border-bottom-right-radius: 3px;
    -moz-border-radius-bottomright: 3px;
    border-bottom-right-radius: 3px;
}

.contentTableIframeExtended {
}


.contentTableIFrameNoTabs {
    border-bottom: solid 1px #dadada;
}

.bodyIframe .contentTableLabelContainer {
    font-weight: bold;
    color: #444;
    vertical-align: middle;
    white-space: nowrap;
}

.summaryPanel {
    display: table-cell;
    width: 341px;
    border-right: 1px solid #dadada;
    background: #fff;
    vertical-align: top;
}

.propertiesContainer {
    display: table-cell;
    width: 574px;
    padding: 24px 30px;
    background: #fff;
    vertical-align: top;
    border-bottom-right-radius: 3px;
}

.summaryPanelCollapsable {
    position: absolute;
    z-index: 20;
    background: url('commonimages/layout/content_panel_background.gif') repeat left top;
    width: 346px;
    vertical-align: top;
    padding: 10px;
    border-right: 1px solid #bbb;
    border-bottom: 1px solid #bbb;
    border-radius: 0 0 6px 0;
}

.contentPanel {
    background: transparent;
}

.editorContentPanel {
    background: #fff;
    height: 100%;
}

.variantSelectionContainer {
    float: left;
    width: 280px;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    border-right: solid 1px transparent;
    background: #fff;
}

.variants-toggle-container {
    border-right: solid 1px #dadada;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* MISC CSS */
.overviewReduced {
    background: url('commonimages/icons/overview_reduce.png') no-repeat center center;
    padding-right: 21px;
    align: center;
}

.overviewExpanded {
    background: url('commonimages/icons/overview_expand.png') no-repeat center center;
    padding-right: 21px;
    align: center;
}

.checkAlign {
    position: relative;
    top: -3px;
}

* html .checkAlign {
    position: relative;
    top: 3px;
}

#colorpickerHolder2 {
    z-index: 10;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* DIV CONTAINERS CSS */

.generic, .genericL1,
.genericInForm, .genericInFormST1, .headerForGeneric, .headerForGenericL1 {
    background-color: #fff;
    border: #5e56af 1px solid; /*colorOutline*/
    border-top: none;
    margin: 0;
    margin-bottom: 6px; /*secondColPadding*/
    padding: 6px; /*secondColPadding*/

}

.headerForGeneric, .headerForGenericL1 {
    border: none;
    border-bottom: 1px solid #bdbdbd;
    padding-left: 10px;
    margin-bottom: 0;

    color: #fff;
    font-weight: bold;
    font-size: 13px;

    background: url(commonimages/tables/table_header_grad.gif) repeat-x left top;
    color: #414141;
}

.genericL1 {
    min-height: 300px;
    padding: 0;
}

#container {
    background: url(commonimages/layout/content_panel_background.gif) repeat left top;
}

#container {
    position: relative;
    margin: 0 auto;
    padding: 0;
    text-align: left;
}

.backgroundTile_50p {
    background: url(commonimages/layout/background_tile_50p.gif) repeat left top;
}

.backgroundTile_25p {
    background: url(commonimages/layout/background_tile_25p.gif) repeat left top;
}

.backgroundTile_10p {
    background: url(commonimages/layout/background_tile_10p.gif) repeat left top;
}

.contentDataContainer {
    padding: 15px;
    min-height: 300px;
    background-color: #fdfdfd;
}

.disabledGroup, .disabledGroup TABLE DIV {
    color: #c2c2c2; /*colorDisabledText*/
}

.paddingTopBottom {
    padding-top: 4px;
    padding-bottom: 4px;
}

.disabledGroup, .enabledGroup {
    padding-left: 15px;
    padding-right: 15px;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* DIV UPPER CONTENTS CSS */

#containerUpper h1 {
    font-size: 1.20em;
    font-weight: bold;
    padding: 0;
    padding-top: 0em;
    padding-bottom: 0em;
    margin: 0;
}

#containerUpper select {
    font-size: 10px;
}

#containerUpper h1 a, #containerUpper p a {
    text-decoration: none;
}

#containerUpper p {
    font-size: .85em;
    font-weight: normal;
    padding: 0;
    margin: 0;
    padding-top: .5em;
    padding-bottom: .5em;
}

#containerUpper {
    padding: 0;
    margin: 0;
    min-width: 988px;
    border: none;
}

.autoScroll {
    overflow: auto;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* DIV LOWER CONTENTS CSS */
.oneColInnerWidthTop {
    width: 986px;
    font-size: 1em;
}

#containerContent {
    font-size: 1em;
    padding: 0;
    margin: 0;
}

#containerContentBasic {
    font-size: 1em;
    padding: 12px 0 3px;
    margin: 0;
}

.logoImage {
    border: none;
    max-width: 280px;
    max-height: 80px;
}

#messagepointLogo {
    border: none;
    margin: 12px 15px 4px 24px;
}

.contentTable, .contentTableLg, .paginationTable {
    width: 100%;
}

.innerContentTable {
    padding: 48px 0 24px
}

.popUpActionBarContainer {
    position: relative;
    border-bottom: solid 1px #dadada;
    background: #fff;
    -webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, .2);
    -moz-box-shadow: 0 0 3px 0 rgba(0, 0, 0, .2);
    box-shadow: 0 0 3px 0 rgba(0, 0, 0, .2);
}

.popUpActionBarContainer .actionsWrapper {
height: 48px;
}

.actionsWrapper .leftActions {
    float: left;
}

.actionsWrapper .rightActions {
    float: right;
}

.actionsWrapper .actionBarTaskIndicator {
    position: relative;
    float: left;
    padding: 0 24px;
    border-left: solid 1px #dadada;
    line-height: 48px;
    font-size: 16px;
}

.actionsWrapper .actionBarTaskIndicator .project_task_taskCount {
    position: absolute;
    bottom: -8px;
    font-size: 10px;
    right: 14px;
}

.contentTable, .innerContentTable, .contentTableLg, .genericTable {
    border: none;
}

.contentTable TD, .contentTableLg TD, .innerContentTable TD {
    padding: 0;
}

.contentTableButtonsTR {
    background: #cccccc;
    border: #aaaaaa 1px solid;
}

.listTableHeaderTR {
    background: #e9e9e9 url("commonimages/tables/listHeader_grad.gif") repeat-x left top;
    text-align: left;
    font-weight: bold;
}

.listTableHeaderTRsecond {
    background: #eff0e1;
    text-align: center;
}

.listTableHeaderTR td, listTableHeaderTRsecond td {
    border: #b1b1b1 1px solid; /*colorOutline*/
    border-right: none;
    border-left: none;
    padding: 5px 4px;
}

.contentTableHeaderTR {
}

.contentTableHeaderTR td {
    color: #444;
}

.contentTableHeaderTRsecond td {
    color: #414141;
    border: 1px solid #dfdfdf;
}

.contentTableHeaderTRsecond {
    background: #eff0e1; /*colorPaginationBG*/
    text-align: center;
}

.contentTableContentTR {
}

.contentTableContentTR td {
    /* padding: 24px 0 !important; */
}

.contentTableSubsection {
    background-color: #cccccc;
    font-weight: bold;
}

.contentTableContentTREven {
    text-align: center;
    background-color: #fbfbfb;
}

.contentTableStacked .contentTableLabelContainer {
    padding: 0px 0 4px !important;
    font-weight: 600;
    font-size: 12px;
    line-height: 12px;
    color: #626262;
}

.contentTableStacked .contentTableContentContainer {
    font-size: 14px;
    position: relative;
    padding: 0 0 24px !important;
    line-height: 24px;
}

.contentTableHeaderTD {
    padding-left: 15px;
}

.contentTableStacked .contentTableHeaderTD {
    position: relative;
    font-size: 20px;
    line-height: 48px;
    padding: 8px 0 33px !important;
}

.contentTableStacked .contentTableHeaderTD::after {
    content: "\00a0";
    position: absolute;
    bottom: 32px;
    left: 0;
    right: 0;
    height: 1px;
    background: #dadada;
}

.contentTableContainer {
    border: 1px solid #bdbdbd;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.contentListTableContentTR {
    background-color: #ededed;

}

.contentListTableContentTR td {
    border-bottom: 2px solid #fff;
}

.contentTableContentColumn {
}

.dataTable .contentTableContentTREven, .dataTable .contentListTableContentTR, .dataTable .contentTableContentColumn {
    background: none;
}

.dataTable > tbody > tr:hover .cardContainer_name, .dataTable > tbody > tr:hover .cardContainer_details,
.dataTable > tbody > tr:hover .cardContainer_name .fa, .dataTable > tbody > tr:hover .cardContainer_details .fa,
.dataTable > tbody > tr:hover .cardContainer_name .far, .dataTable > tbody > tr:hover .cardContainer_details .far,
.dataTable > tbody > tr:hover .cardContainer_name .fal, .dataTable > tbody > tr:hover .cardContainer_details .fal,
.dataTable > tbody > tr:hover .cardContainer_name .mce-ico-txt:not(.mce-ico-rev), .dataTable > tbody > tr:hover .cardContainer_details .mce-ico-txt:not(.mce-ico-rev) {
    color: #7c1a87 !important;
}
.cardContainerIcons .fa-stack {
    margin-right: 6px;
}

.contentTableContentColumnEven {
}

.tableTREven {
    background-color: #f2f2f2;
}

.left {
    text-align: left;
}

.right {
    text-align: right;
}

.center {
    text-align: center;
}

.errorMsg {
    color: #ff0000;
    font-weight: bold;
}

.zonePrioritiesContainer {
    width: 200px;
}

#zonePrioritiesContainer table, #zonePrioritiesContainer table tr, #zonePrioritiesContainer table td {
    border: none;
}

#containerContent {
    width: 720px;
}

.graphicContainer {
    overflow: auto;
    width: 654px;
    margin: 0;
    padding: 0;
}

.graphicContainerPrintable {
    overflow: scroll;
    width: 546px;

    border: #dddddd 1px solid;
    margin: 0;
    padding: 0;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* ICON CSS */
.fa-mp-style, .fa-mp-btn {
    color: #444;
    cursor: default;
}

.fa-mp-button-icon {
    padding-right: 4px;
}

.fa-mp-disabled {
    color: #ccc;
}

.fa-mp-container.disabled, .fa-mp-container.disabled i.fa-mp-btn, .fa-mp-container.disabled:hover, .fa-mp-container.disabled:hover i.fa-mp-btn {
    color: #999;
}

.fa-mp-error {
    color: #a94442;
}

.fa-mp-state {
    position: relative;
    top: -1px;
}

.fa-mp-ed-ico {
    color: #414141;
}

.fa-mp-ed-ico:not(.fa-stack) {
    position: relative;
    top: 1px;
}

.fa-mp-ed-ico.fa-stack {
    width: 1em;
    height: 1em;
    line-height: 1em;
}

.fa-stack span.mce-ico-txt {
    font-size: 0.6em;
    font-weight: bold;
    top: -1px;
    font-family: "Times New Roman", Times, serif;
    font-style: normal;
    text-decoration: none;
    min-height: 0.6em;
}

.mce-ico-txt.mce-ico-rev {
    color: #f9f9f9;
}
.mce-btn .mce-ico[style*='far-lock.svg'] {
    width: 14px;
    height: 16px;
}
.mce-btn .mce-ico[style*='far-lock.svg'], .mce-btn .mce-ico[style*='far-pen-square.svg'], .mce-btn .mce-ico[style*='far-plus-circle.svg'] {
    opacity: 0.75;
}

.actionBtn_disabled .fa-mp-btn, .actionBtn_disabled.fa-mp-container:hover .fa-mp-btn {
    color: #aaa;
}

i.fa-small {
    font-size: 12px;
}

i.fa-med {
    font-size: 16px;
    line-height: 14px;
    vertical-align: -17%;
}

i.fa-lgx {
    font-size: 22px;
}

i.sourceEditor {
    margin-left: 4px;
    cursor: pointer;
}

i.sourceEditor:hover {
    color: #999;
}

i.sourceEditor:active {
    color: #aaa;
}

.touchpointContentEditContainer {
    position: absolute;
    top: 0;
    left: -20px;
    background-color: #f5f5f5;
    border-radius: 3px;
    opacity: 0.85;
    z-index: 2;
    border: 1px solid #ddd;
    width: 20px;
    height: 20px;
    text-align: center;
    cursor: pointer;
}

.messageIcon, .zoneIcon, .documentIcon, .taskIcon, .eventIcon,
.emptyCell,
.errorIcon, .warningIcon {
    padding-left: 22px;
    padding-top: 4px;
    margin: 0;
    height: 18px;
}

.checkmarkIcon {
    width: 12px;
    height: 12px;
}

.disabledIcon {
    width: 13px;
    height: 13px;
}

.activeIconDiv, .workingCopyIconDiv, .archiveIconDiv, .activeActionIcon, .workingCopyActionIcon, .activeActionIcon_disabled,
.workingCopyActionIcon_disabled, .blankStatusIcon, .holdIconDiv, .suppressIconDiv, .pendingApprovalIconDiv {
    width: 12px;
    height: 12px;
    cursor: default;
}

.archiveIcon, .closeIcon {
    padding-left: 18px;
    padding-top: 2px;
    margin: 0;
    height: 14px;
}

.activeIcon, .workingCopyIcon {
    height: 14px;
    width: 14px;
}

.activeStatusIcon, .inactiveStatusIcon {
    width: 16px;
    height: 16px;
}

.activeIconLrg, .workingCopyIconLrg, .archiveIconLrg {
    padding-left: 18px;
    padding-top: 2px;
    margin: 0;
    height: 12px;
}

.activeIcon, .workingCopyIcon, .archiveIcon, .activeStatusIcon, inactiveStatusIcon {
    cursor: pointer;
}

.errorLarge {
    background: url('commonimages/error.png') no-repeat left top;
    height: 32px;
    padding-left: 25px;
    padding-top: 6px;
    margin: 0;
}

.listItemArrow, .listItemCircle {
    top: 2px;
    padding-left: 22px;
    padding-top: 4px;
    margin: 0;
    height: 18px;
}

.tableWedgeIcon {
    padding-top: 4px;
    margin: 0;
    height: 18px;
    position: relative;
    top: 2px;
    padding-left: 8px;
    padding-right: 8px;
    background: url('commonimages/icons/thinWedge.gif') no-repeat center center;
    opacity: 0.8;
    filter: alpha(opacity=80, style=0);
}

.reportIcon {
    padding-left: 22px;
    padding-top: 2px;
    margin: 0;
    height: 18px;
    padding-bottom: 2px;
}

.tableExpandAll, .tableContractAll, .tableHelp {
    position: relative;
    display: inline-block;
    top: 1px;
    height: 17px;
    width: 17px;
    cursor: pointer;
}

.infoIcon {
    position: relative;
    top: 2px;
    margin: 0 0 0 1px;
    width: 18px;
    height: 18px;
    padding-top: 4px;
    padding-left: 18px;
}

.expansionArrow_closed {
    background: url('commonimages/icons/expansionArrow.png') no-repeat right bottom;
}

.expansionArrow_open {
    background: url('commonimages/icons/expansionArrow.png') no-repeat right top;
}

.expansionArrowDiv_closed, .expansionArrowDiv_open {
    cursor: pointer;
    -webkit-transition: -webkit-transform .1s linear;
    transition: transform .1s linear;
}

.expansionArrowDiv_open {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
}

.tableExpandAll {
    background: url('commonimages/icons/expand_all.gif') no-repeat left center;
}

.tableContractAll {
    background: url('commonimages/icons/contract_all.gif') no-repeat left center;
}

.tableHelp {
    background: url('commonimages/icons/help.gif') no-repeat left center;
}

.messageIcon {
    background: url('commonimages/icons/message.gif') no-repeat left top;
}

.zoneIcon {
    background: url('commonimages/icons/zone.gif') no-repeat left top;
}

.documentIcon {
    background: url('commonimages/icons/document.gif') no-repeat left top;
}

.taskIcon {
    background: url('commonimages/icons/task.gif') no-repeat left top;
}

.eventIcon {
    background: url('commonimages/icons/event.gif') no-repeat left top;
}

.reportIcon {
    background: url('commonimages/icons/report.gif') no-repeat left top;
}

.listItemArrow, .listItemArrowDiv {
    background: url('commonimages/icons/liItemArrow.png') no-repeat center center;
}

.listItemCircle {
    background: url('commonimages/icons/liItemCircle.gif') no-repeat center center;
}

.listItemArrowDiv {
    width: 7px;
    height: 7px;
}

.checkmarkIcon {
    background: url('commonimages/icons/table_checkmark.png') no-repeat center center;
    vertical-align: top;
}

.disabledIcon {
    background: url('commonimages/icons/table_disabled.gif') no-repeat center center;
    vertical-align: top;
}

.emptyCell {
    background: url('commonimages/icons/dot.gif') no-repeat center center;
}

.closeIcon {
    background: url('commonimages/icons/close_x.gif') no-repeat center center;
}

.infoIcon {
    background: url('commonimages/icons/infoIcon.gif') no-repeat left top;
}

.errorIcon {
    background: url('commonimages/icons/_e_error.gif') no-repeat left top;
}

.warningIcon {
    background: url('commonimages/icons/_e_warning.gif') no-repeat left top;
}

.activeStatusIcon {
    background: url('commonimages/icons/activeStatusIcon.gif') no-repeat center center;
}

.inactiveStatusIcon {
    background: url('commonimages/icons/inactiveStatusIcon.gif') no-repeat center center;
}

.archiveIcon {
    background: url('commonimages/icons/archiveIcon.gif') no-repeat center center;
}

.blankStatusIcon {
    background: url('commonimages/icons/dot.gif') no-repeat center center;
}

.activeIconLrg {
    background: url('commonimages/icons/activeIcon_large.gif') no-repeat center center;
}

.workingCopyIconLrg {
    background: url('commonimages/icons/workingCopyIcon_large.gif') no-repeat center center;
}

.archiveIconLrg {
    background: url('commonimages/icons/archiveIcon_large.gif') no-repeat center center;
}

.stateIconContainer {
    display: inline-block;
    height: 12px;
    width: 12px;
}

.workingCopyActionIcon {
    color: #555;
}

.actionBtnError_toggleSelect .activeActionIcon, .actionBtnError .activeActionIcon, .actionBtnError_hov .activeActionIcon {
    background: url('commonimages/icons/active_icon.png') no-repeat 0 -36px;
}

.actionBtnError_toggleSelect .workingCopyActionIcon, .actionBtnError .workingCopyActionIcon, .actionBtnError_hov .workingCopyActionIcon {
    background: url('commonimages/icons/working_copy_icon.png') no-repeat 0 -36px;
}

.actionBtnError_toggleSelect i.activeActionIcon, .actionBtnError i.activeActionIcon, .actionBtnError_hov i.activeActionIcon {
    color: red;
    background: none;
}

.actionBtnError_toggleSelect i.workingCopyActionIcon, .actionBtnError i.workingCopyActionIcon, .actionBtnError_hov i.workingCopyActionIcon {
    color: red;
    background: none;
}

i.pendingApprovalIconDiv {
    font-size: 13px;
}

.xIconDiv {
    width: 11px;
    height: 12px;
    background: url('commonimages/icons/x_icon.png') no-repeat center top;
}

.xIconDiv:hover {
    background: url('commonimages/icons/x_icon.png') no-repeat center center;
}

.xIconDiv:active {
    background: url('commonimages/icons/x_icon.png') no-repeat center bottom;
}

.moveIcon {
    width: 16px;
    height: 16px;
    background: url('commonimages/icons/move_icon.png') no-repeat 0 0;
}

.restrictedIconDiv {
    background: url('commonimages/icons/restricted_icon.png') no-repeat 0 0;
    width: 11px;
    height: 12px;
}

.leftBorderIcon, .rightBorderIcon, .topBorderIcon, .bottomBorderIcon, .allBorderIcon, .allTableBorderIcon {
    width: 16px;
    height: 16px;
}

.leftBorderIcon {
    background: url('commonimages/icons/icons_borders_16x16.png') no-repeat 0 0;
}

.rightBorderIcon {
    background: url('commonimages/icons/icons_borders_16x16.png') no-repeat -16px 0;
}

.topBorderIcon {
    background: url('commonimages/icons/icons_borders_16x16.png') no-repeat -32px 0;
}

.bottomBorderIcon {
    background: url('commonimages/icons/icons_borders_16x16.png') no-repeat -48px 0;
}

.allBorderIcon {
    background: url('commonimages/icons/icons_borders_16x16.png') no-repeat -64px 0;
}

.allTableBorderIcon {
    background: url('commonimages/icons/icons_borders_16x16.png') no-repeat -80px 0;
}

.iconListIconDiv, .iconListIconDiv_disabled, .iconListClearIconDiv, .iconListClearIconDiv_disabled,
.textIconDiv, .textIconDiv_disabled, .multipartIconDiv, .multipartIconDiv_disabled, .graphicIconDiv,
.graphicIconDiv_disabled, .collectionIconDiv, .collectionIconDiv_disabled, .deliveryIconDiv,
.deliveryIconDiv_disabled, .docIconDiv, .docIconDiv_disabled {
    width: 17px;
    height: 17px;
}

.docIconDiv {
    background: url('commonimages/icons/icons_17x17.png') no-repeat -119px 0;
}

.docIconDiv_disabled {
    background: url('commonimages/icons/icons_17x17.png') no-repeat -119px -17px;
}

.deliveryIconDiv {
    background: url('commonimages/icons/icons_17x17.png') no-repeat -102px 0;
}

.iconListIconDiv, .graphicIconDiv {

}

.iconListIconDiv_disabled, .graphicIconDiv_disabled {

}

.iconListClearIconDiv {
    background: url('commonimages/icons/icons_17x17.png') no-repeat -17px 0;
}

.iconListClearIconDiv_disabled {
    background: url('commonimages/icons/icons_17x17.png') no-repeat -17px -17px;
}

.textIconDiv {
    background: url('commonimages/icons/icons_17x17.png') no-repeat -34px 0;
}

.textIconDiv_disabled {
    background: url('commonimages/icons/icons_17x17.png') no-repeat -34px -17px;
}

.multipartIconDiv {
    background: url('commonimages/icons/icons_17x17.png') no-repeat -51px 0;
}

.multipartIconDiv_disabled {
    background: url('commonimages/icons/icons_17x17.png') no-repeat -51px -17px;
}

.collectionIconDiv {
    background: url('commonimages/icons/icons_17x17.png') no-repeat -85px 0;
}

.collectionIconDiv_disabled {
    background: url('commonimages/icons/icons_17x17.png') no-repeat -85px -17px;
}

.infoIconDiv {
    background: url('commonimages/icons/info_icon.png') no-repeat 0 0;
    width: 12px;
    height: 12px;
}

.infoIconDiv:hover {
    background: url('commonimages/icons/info_icon.png') no-repeat 0 -12px;
}

.loadingIcon {
    background: url('commonimages/icons/loadingAnimIcon.gif') no-repeat center center;
    font-size: 5px;
    padding: 9px 13px 0 0;
    position: relative;
    top: 3px;
}

.loadingLightBackground {
    background: url('commonimages/layout/loading.gif') no-repeat center center;
}

.loadingIconDiv {
    background: url('commonimages/icons/loadingAnimIcon.gif') no-repeat center center;
    width: 14px;
    height: 14px;
}

.loadingClock {
    background: url('commonimages/icons/inProcess_clock.gif') no-repeat center center;
    width: 14px;
    height: 14px;
}

.advancedSearchIconDiv {
    width: 16px;
    height: 16px;
    opacity: 0.75;
    filter: alpha(opacity=75, style=0);
}

.advancedSearchIconDiv i {
    color: #414141;
}

.advancedSearchIconDiv:hover i {
    color: #313131;
}

.advancedSearchIconDiv_active {
    width: 16px;
    height: 16px;
    opacity: 0.75;
    filter: alpha(opacity=75, style=0);
    top: 7px !important;
    right: 18px;
}

.advancedSearchIconDiv_active i {
    color: #459e49;
}

.dragIndicatorIconDiv {
    width: 4px;
    height: 14px;
    background: url('commonimages/icons/drag_indicator_icon.png') no-repeat 0 0;
}

.searchIconDiv, .contextBar_searchIcon {
    width: 16px;
    height: 16px;
}

.searchIconDiv {
    opacity: 0.25;
    filter: alpha(opacity=25, style=0);
    display: inline-block;
    position: absolute;
    top: 12px;
    left: 16px;
}

.codeIconDiv, .contextBar_codeIcon {
    background: url('commonimages/icons/code_medium_icon.png') no-repeat 0 0;
    width: 16px;
    height: 16px;
}

.hierarchyIconDiv {
    background: url('commonimages/icons/hierarchy_medium_icon.png') no-repeat 0 0;
    width: 16px;
    height: 16px;
}

.toggleLeftIcon,
.toggleRightIcon {
    position: relative;
    -webkit-transition: left .2s linear, -webkit-transform .2s linear;
    transition: left .2s linear, transform .2s linear;
}

.toggleRightIcon {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
    left: 2px;
}

.arrowRightIcon, .arrowRightHighlightIcon {
    width: 5px;
    height: 6px;
}

.arrowRightIcon, .arrowRightIconDiv {
    background: url('commonimages/icons/right_arrow_icon.png') no-repeat 0 0;
}

.arrowRightIconDiv {
    width: 5px;
    height: 6px;
}

.arrowRightHighlightIcon {
    background: url('commonimages/icons/right_arrow_icon.png') no-repeat 0 -6px;
}

.targetingIcon {
    background: url('commonimages/icons/targeting_icon.png') no-repeat 0 0;
    width: 12px;
    height: 13px;
}

.timingIcon {
    background: url('commonimages/icons/timing_icon.png') no-repeat 0 0;
    width: 12px;
    height: 13px;
}

.variantsIcon {
    background: url('commonimages/icons/selectable_icon.png') no-repeat 0 0;
    width: 14px;
    height: 13px;
}

.inheritIcon {
    background: url('commonimages/icons/inherited_icon.png') no-repeat 0 0;
    width: 7px;
    height: 13px;
}

.directionUpIcon {
    background: url('commonimages/icons/direction_icons.png') no-repeat 0 0;
    width: 15px;
    height: 15px;
}

.directionDownIcon {
    background: url('commonimages/icons/direction_icons.png') no-repeat -15px 0;
    width: 15px;
    height: 15px;
}

.directionLeftIcon {
    background: url('commonimages/icons/direction_icons.png') no-repeat -30px 0;
    width: 15px;
    height: 15px;
}

.directionRightIcon {
    background: url('commonimages/icons/direction_icons.png') no-repeat -45px 0;
    width: 15px;
    height: 15px;
}

.addIcon {
    background: url('commonimages/icons/add_icon.gif') no-repeat 0 0;
    width: 11px;
    height: 11px;
}

.lessIcon {
    background: url('commonimages/icons/remove_icon.png') no-repeat 0 0;
    width: 11px;
    height: 11px;
}

.cancelIcon {
    width: 14px;
    height: 14px;
    background: url('commonimages/icons/button_icons.png') no-repeat -14px 0;
}

.uploadIcon {
    width: 14px;
    height: 14px;
    background: url('commonimages/icons/button_icons.png') no-repeat -28px 0;
}

.deleteIcon {
    width: 14px;
    height: 14px;
    background: url('commonimages/icons/button_icons.png') no-repeat -42px 0;
}

.remove_x_Icon {
    width: 14px;
    height: 14px;
    background: url('commonimages/icons/button_icons.png') no-repeat -56px 0;
}

.actionBtn_disabled .remove_x_Icon {
    background: url('commonimages/icons/button_icons.png') no-repeat -56px -14px;
}

.drawIcon {
    width: 14px;
    height: 14px;
    background: url('commonimages/icons/button_icons.png') no-repeat -70px 0;
}

.forwardIcon, .forwardBtnIcon {
    width: 14px;
    height: 14px;
    background: url('commonimages/icons/button_icons.png') no-repeat -84px 0;
}

.forwardBtnIconDisabled {
    width: 14px;
    height: 14px;
    background: url('commonimages/icons/button_icons.png') no-repeat -84px -14px;
}

.forwardBtnIcon:hover {
    background: url('commonimages/icons/button_icons.png') no-repeat -84px -28px;
}

.forwardBtnIcon:active {
    background: url('commonimages/icons/button_icons.png') no-repeat -84px -42px;
}

.noVisibilityIcon {
    background: url('commonimages/icons/no_visibility_icon.png') no-repeat 0 0;
    width: 16px;
    height: 10px;
}

.validationErrorIcon {
    background: url('commonimages/icons/info_status_icons.png') no-repeat 0 0;
    width: 20px;
    height: 20px;
}

.successIcon {
    background: url('commonimages/icons/info_status_icons.png') no-repeat -20px 0;
    width: 20px;
    height: 20px;
}

.workflowIcon {
    background: url('commonimages/icons/workflow_icon.png') no-repeat 0 0;
    width: 13px;
    height: 15px;
}

.contentIconM, .contentIconM_noHov, .contentIconM_disabled {
    width: 14px;
    height: 19px;
}

.contentIconM, .contentIconM_noHov {
    background: url('commonimages/icons/content_medium_icon.png') no-repeat 0 0;
}

.contentIconM:hover {
    background: url('commonimages/icons/content_medium_icon.png') no-repeat 0 -19px;
}

.contentIconM_disabled {
    background: url('commonimages/icons/content_medium_icon.png') no-repeat 0 -38px;
}

.timingIconM, .timingIconM_noHov, .timingIconM_disabled {
    width: 18px;
    height: 19px;
}

.timingIconM, .timingIconM_noHov {
    background: url('commonimages/icons/timing_medium_icon.png') no-repeat 0 0;
}

.timingIconM:hover {
    background: url('commonimages/icons/timing_medium_icon.png') no-repeat 0 -19px;
}

.timingIconM_disabled {
    background: url('commonimages/icons/timing_medium_icon.png') no-repeat 0 -38px;
}

.targetingIconM, .targetingIconM_noHov, .targetingIconM_disabled {
    width: 18px;
    height: 19px;
}

.targetingIconM, .targetingIconM_noHov {
    background: url('commonimages/icons/targeting_medium_icon.png') no-repeat 0 0;
}

.targetingIconM:hover {
    background: url('commonimages/icons/targeting_medium_icon.png') no-repeat 0 -19px;
}

.targetingIconM_disabled {
    background: url('commonimages/icons/targeting_medium_icon.png') no-repeat 0 -38px;
}

.priorityIconM, .priorityIconM_noHov, .priorityIconM_disabled {
    width: 18px;
    height: 19px;
}

.priorityIconM, .priorityIconM_noHov {
    background: url('commonimages/icons/priority_medium_icon.png') no-repeat 0 0;
}

.priorityIconM:hover {
    background: url('commonimages/icons/priority_medium_icon.png') no-repeat 0 -19px;
}

.priorityIconM_disabled {
    background: url('commonimages/icons/priority_medium_icon.png') no-repeat 0 -38px;
}

.newFrameIcon, .newFrameIcon_disabled {
    width: 16px;
    height: 16px;
}

.contextMenuHeaderText .newFrameIcon {
    float: right;
}

.newFrameIcon {
    cursor: pointer;
}

.newFrameIcon:hover {
    color: #5d3672;
}

.newFrameIcon:active {
    color: #9069a5;
}

.newFrameIcon_disabled {
    color: #d6c7de;
}

.dataValueIcon, .dataValueIcon_disabled {
    width: 16px;
    height: 17px;
}

.dataValueIcon {
    background: url('commonimages/icons/data_value_icon.png') no-repeat 0 0;
}

.dataValueIcon_disabled {
    background: url('commonimages/icons/data_value_icon.png') no-repeat 0 -17px;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* MESSAGE LIST CSS */
.previewDetail, .previewNoResourceDetail, .timingDetail, .targetingDetail, .selectableContentDetail, .contentDetail,
.previewDetail_Hov, .previewNoResourceDetail_Hov, .timingDetail_Hov, .targetingDetail_Hov, .selectableContentDetail_Hov,
.contentDetail_Hov, .detailBackground, .previewDetail_Disabled, .previewNoResourceDetail_Disabled, .timingDetail_Disabled,
.targetingDetail_Disabled, .selectableContentDetail_Disabled, .contentDetail_Disabled, .selectableContentIcon,
.selectableContentIcon_Hov, .insertDetail_Disabled, .insertDetail, .insertDetail_Hov, .reservationDetail_Disabled,
.reservationDetail, .reservationDetail_Hov, .selectorDetail_Disabled, .selectorDetail, .selectorDetail_Hov,
.scheduleDetail_Disabled, .scheduleDetail, .scheduleDetail_Hov, .textDetail, .textDetail_Hov, .textDetail_Disabled,
.graphicDetail, .graphicDetail_Hov, .graphicDetail_Disabled, .multipartDetail, .multipartDetail_Hov,
.multipartDetail_Disabled, .associationsDetail, .associationsDetail_Hov, .associationsDetail_Disabled {
    width: 28px;
    height: 28px;
}

.previewDetail, .previewNoResourceDetail, .timingDetail, .targetingDetail, .selectableContentDetail, .contentDetail,
.previewDetail_Hov, .previewNoResourceDetail_Hov, .timingDetail_Hov, .targetingDetail_Hov, .selectableContentDetail_Hov,
.contentDetail_Hov, .previewDetail_Disabled, .timingDetail_Disabled, .targetingDetail_Disabled, .selectableContentDetail_Disabled,
.contentDetail_Disabled, .selectableContentIcon, .selectableContentIcon_Hov, .selectorDetail_Hov, .scheduleDetail_Disabled,
.scheduleDetail, .scheduleDetail_Hov, .textDetail, .textDetail_Hov, .textDetail_Disabled, .graphicDetail, .graphicDetail_Hov,
.graphicDetail_Disabled, .multipartDetail, .multipartDetail_Hov, .multipartDetail_Disabled, .associationsDetail, .associationsDetail_Hov,
.associationsDetail_Disabled {
    cursor: pointer;
}

.detailBackground {
    background: url("commonimages/tables/detailBackgroundIcon.gif") no-repeat center center;
}

.previewDetail {
    background: url('commonimages/tables/previewDetailIcon.gif') no-repeat center center;
}

.previewNoResourceDetail {
    background: url('commonimages/tables/previewNoResourceDetailIcon.gif') no-repeat center center;
}

.timingDetail {
    background: url('commonimages/tables/timingDetailIcon.gif') no-repeat center center;
}

.targetingDetail {
    background: url('commonimages/tables/targetingDetailIcon.gif') no-repeat center center;
}

.contentDetail {
    background: url('commonimages/tables/contentDetailIcon.gif') no-repeat center center;
}

.selectableContentDetail {
    background: url('commonimages/tables/selectableContentDetailIcon.gif') no-repeat center center;
}

.previewDetail_Hov {
    background: url('commonimages/tables/previewDetailIcon.gif') no-repeat center top;
}

.previewNoResourceDetail_Hov {
    background: url('commonimages/tables/previewNoResourceDetailIcon.gif') no-repeat center top;
}

.timingDetail_Hov {
    background: url('commonimages/tables/timingDetailIcon.gif') no-repeat center top;
}

.targetingDetail_Hov {
    background: url('commonimages/tables/targetingDetailIcon.gif') no-repeat center top;
}

.contentDetail_Hov {
    background: url('commonimages/tables/contentDetailIcon.gif') no-repeat center top;
}

.selectableContentDetail_Hov {
    background: url('commonimages/tables/selectableContentDetailIcon.gif') no-repeat center top;
}

.previewDetail_Disabled {
    background: url('commonimages/tables/previewDetailIcon.gif') no-repeat center bottom;
}

.previewNoResourceDetail_Disabled {
    background: url('commonimages/tables/previewNoResourceDetailIcon.gif') no-repeat center bottom;
}

.timingDetail_Disabled {
    background: url('commonimages/tables/timingDetailIcon.gif') no-repeat center bottom;
}

.targetingDetail_Disabled {
    background: url('commonimages/tables/targetingDetailIcon.gif') no-repeat center bottom;
}

.selectableContentDetail_Disabled {
    background: url('commonimages/tables/selectableContentDetailIcon.gif') no-repeat center bottom;
}

.contentDetail_Disabled {
    background: url('commonimages/tables/contentDetailIcon.gif') no-repeat center bottom;
}

.selectableContentIcon {
    background: url('commonimages/tables/selectableContentIcon.gif') no-repeat center bottom;
}

.selectableContentIcon_Hov {
    background: url('commonimages/tables/selectableContentIcon.gif') no-repeat center top;
}

.insertDetail_Disabled {
    background: url('commonimages/tables/insertDetailIcon.gif') no-repeat center bottom;
}

.insertDetail {
    background: url('commonimages/tables/insertDetailIcon.gif') no-repeat center center;
}

.insertDetail_Hov {
    background: url('commonimages/tables/insertDetailIcon.gif') no-repeat center top;
}

.reservationDetail_Disabled {
    background: url('commonimages/tables/reservedDetailIcon.gif') no-repeat center bottom;
}

.reservationDetail {
    background: url('commonimages/tables/reservedDetailIcon.gif') no-repeat center center;
}

.reservationDetail_Hov {
    background: url('commonimages/tables/reservedDetailIcon.gif') no-repeat center top;
}

.selectorDetail_Disabled {
    background: url('commonimages/tables/selectorDetailIcon.gif') no-repeat center bottom;
}

.selectorDetail {
    background: url('commonimages/tables/selectorDetailIcon.gif') no-repeat center center;
}

.selectorDetail_Hov {
    background: url('commonimages/tables/selectorDetailIcon.gif') no-repeat center top;
}

.scheduleDetail_Disabled {
    background: url('commonimages/tables/scheduleDetailIcon.gif') no-repeat center bottom;
}

.scheduleDetail {
    background: url('commonimages/tables/scheduleDetailIcon.gif') no-repeat center center;
}

.scheduleDetail_Hov {
    background: url('commonimages/tables/scheduleDetailIcon.gif') no-repeat center top;
}

.textDetail_Disabled {
    background: url('commonimages/tables/textDetailIcon.gif') no-repeat center bottom;
}

.textDetail {
    background: url('commonimages/tables/textDetailIcon.gif') no-repeat center center;
}

.textDetail_Hov {
    background: url('commonimages/tables/textDetailIcon.gif') no-repeat center top;
}

.graphicDetail_Disabled {
    background: url('commonimages/tables/graphicDetailIcon.gif') no-repeat center bottom;
}

.graphicDetail {
    background: url('commonimages/tables/graphicDetailIcon.gif') no-repeat center center;
}

.graphicDetail_Hov {
    background: url('commonimages/tables/graphicDetailIcon.gif') no-repeat center top;
}

.multipartDetail_Disabled {
    background: url('commonimages/tables/multipartDetailIcon.gif') no-repeat center bottom;
}

.multipartDetail {
    background: url('commonimages/tables/multipartDetailIcon.gif') no-repeat center center;
}

.multipartDetail_Hov {
    background: url('commonimages/tables/multipartDetailIcon.gif') no-repeat center top;
}

.associationsDetail_Disabled {
    background: url('commonimages/tables/associationsDetailIcon.gif') no-repeat center bottom;
}

.associationsDetail {
    background: url('commonimages/tables/associationsDetailIcon.gif') no-repeat center center;
}

.associationsDetail_Hov {
    background: url('commonimages/tables/associationsDetailIcon.gif') no-repeat center top;
}

.searchIcon {
    background: url('commonimages/tables/searchIcon.gif') no-repeat center bottom;
}

.searchIcon_Hov {
    background: url('commonimages/tables/searchIcon.gif') no-repeat center top;
}

.searchIcon, .searchIcon_Hov {
    cursor: pointer;
    width: 18px;
    height: 18px;
}

.searchBarTopLeft, .searchBarTopRight, .searchBarBottomLeft, .searchBarBottomRight {
    width: 15px;
    height: 15px;
}

.searchBarTopLeft {
    background: url('commonimages/tables/searchBarCorners.gif') no-repeat left top;
}

.searchBarTopRight {
    background: url('commonimages/tables/searchBarCorners.gif') no-repeat right top;
}

.searchBarBottomLeft {
    background: url('commonimages/tables/searchBarCorners.gif') no-repeat left bottom;
}

.searchBarBottomRight {
    background: url('commonimages/tables/searchBarCorners.gif') no-repeat right bottom;
}

.searchBarRightEdge {
    background: url('commonimages/tables/searchBarVerticalEdges.gif') repeat-y right center;
}

.searchBarCenter {
    background: #e1e1e1 url('commonimages/tables/searchBarBottomEdge.gif') repeat-x center bottom;
    padding: 5px;
}

.searchBarContent {
    background-color: #e1e1e1;
    padding: 5px;
}

.searchBarTopEdge, .searchBarLeftEdge {
    background-color: #e1e1e1;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* BUTTON CSS */

/* group multiple buttons in a row */
.box {
    display: block;
    padding-top: 10px;
    padding-bottom: 0;
    padding-left: 10px;
    float: left;
    margin-right: 10px;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* TABS CSS */
.disabled {
    /*color: #a2a2a2;*/
}

#containerSubTabsContent {
    padding: 12px;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* Sign In/Sign Out */

.signinTopContainer {
    background-color: #fff;
    height: 300px;
}

.signinMidContainer {
    width: 50%;
    vertical-align: middle;
    display: table-cell;
    text-align: center;
}

.signinLowerContainer {
    position: fixed;
    background: #f5f5f5;
    bottom: 0;
    height: 48px;
    width: 100%;
}

.signinLowerContainer::before {
    content: '\00a0';
    position: absolute;
    left: 0;
    top: -16px;
    width: 100%;
    height: 16px;
    background: -moz-linear-gradient(top, rgba(245,245,245,0) 0%, rgba(245,245,245,0) 1%, rgba(245,245,245,1) 100%);
    background: -webkit-linear-gradient(top, rgba(245,245,245,0) 0%,rgba(245,245,245,0) 1%,rgba(245,245,245,1) 100%);
    background: linear-gradient(to bottom, rgba(245,245,245,0) 0%,rgba(245,245,245,0) 1%,rgba(245,245,245,1) 100%);
}

.signinUpperContainer {
    display: table;
    width: 100%;
    height: 100%;
    background: #f5f5f5;
}

.singinInterfaceContainer {
    display: inline-block;
    width: 380px;
}

.mobile .singinInterfaceContainer {
    width: 100%;
}

.signinInterfaceHeader {
    margin: 48px 0 40px;
}

.signinInterfaceHeaderText {
    margin: 0 0 8px;
    color: #6d3075;
    font-size: 18px;
    text-transform: uppercase;
}

.signinMessagepointLogo {
}

.signinMessagepointLogo:hover,
.signinMessagepointLogo:focus {
    opacity: .9;
}

.signinMessagepointLogo:active {
    opacity: 1;
}


.signinBodyTable {
    display: table;
    width: 100%;
    height: 100%;
}

.signinContentTable {
    margin: 0 0 88px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

.mobile .signinContentTable {
    margin: 0 0 42px;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
}

.signinContentTable .inputGroup {
    position: relative;
}

.signinContentTable .inputComponent {
    position: relative;
    padding: 6px 0 24px;
    text-align: left;
}

.signinContentTable .inputGroup .inputComponent {
    padding-right: 48px;
}

.signinContentTable .inputComponent .filterTip {
    position: absolute;
    top: 16px;
    right: 10px;
    font-size: 16px;
    background: #fbf3a0;
    padding: 6px 8px;
    color: #444;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

.signinContentTable .inputGroup .inputComponent .filterTip {
    right: 50px;
}

.signinBtn {
    padding: 14px 30px;
    font-size: 16px;
    text-transform: uppercase;
}

.signinBtn {
    background: #6d3075;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    cursor: pointer;
    color: #fff;
}

.signinBtn:hover,
.signinBtn:focus {
    background: #5d3672;
}

.signinBtn:active {
    background: #9069a5;
}

#showInstaceInput .fas {
    -webkit-transition: -webkit-transform .2s linear;
    transition: transform .2s linear;
}

#showInstaceInput .fas.rotate-45 {
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}

.buttonsSection {
    margin: 8px 0 16px;
}

.signinInput {
    padding: 3.5px 3px;
    font-size: 12px;
    outline: none;
    background: -webkit-gradient(linear, left top, left bottombottom, from(#efefef), to(#fff));
    background: -moz-linear-gradient(top, #efefef, #fff);
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    border: 1px solid #bdbdbd;
    -webkit-box-shadow: 1px 1px 0 #efefef;
    -moz-box-shadow: 1px 1px 0 #efefef;
    box-shadow: 1px 1px 0 #efefef;
}

.signinInput:focus {

}

.signinWelcomeText {
    color: #fcfcfc;
    margin: 0;
    padding: 10px 0;
    font-size: 22px;
}

.signinText,
.signoutText {
    color: #606060;
    font-size: 14px;
    padding: 8px;
}

.signoutText {
    line-height: 20px;
}

.signinText a, .signoutText a {

}

.signinText a:hover, .signoutText a:hover,
.signinText a:focus, .signoutText a:focus {
    text-decoration: none;
    color: #5d3672;
}

.signinText a:active, .signoutText a:active {
    color: #9069a5;
}

.signinForgotUNPWText {
    color: #fcfcfc;
    text-justify: newspaper;
    padding: 5px 5px 5px;
}

.signinForgotUNPWText a {
    color: #fcfcfc;
}

.signinErrorDiv {
    margin: 0 0 24px;
    padding: 14px 18px 18px;
    background: #fabbb0;
    text-align: left;
    color: #444;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

.signinErrorDiv .icon {
    float: left;
    font-size: 24px;
}

.signinErrorDiv p {
    margin: 6px 0 0;
    padding: 0 0 0 36px;
    line-height: 18px;
    font-size: 12px;
    color: #444;
}

.signinErrorDiv p + p {
    margin-top: 8px;
}

#containerLowerSignin {
    width: 600px;
    background-color: #fff;
}

#containerLowerSigninInner {
    width: 528px;
    border: #dddddd 1px solid;
    background-color: #fff;
    min-height: 300px;
    padding: 12px;
    margin-left: 24px;
    margin-bottom: 12px;
}

#containerLowerSigninInner h1 {
    margin: 0;
    padding: 0;
    padding-bottom: 0.5em;
    font-size: 1.35em;
}

#containerSignin {
    width: 600px;
    border: #dddddd 1px solid;
    position: relative;
    margin: 0 auto;
    padding: 0;
    text-align: left;
    background: #fff;
    border-top: none;
}

#containerUpperSignin {
    padding: 0;
    margin: 0;
    border: none;
    width: 600px;
}

.logoImageSignin {
    border: none;
    margin: 24px;
}

#signinHeader {
    width: 600px;
    font-size: 1em;
}

#signinLine {
    margin: 0;
    padding: 0;
}

.signinCopyright {
    font-size: 11px;
    line-height: 40px;
    text-align: center;
}

.signinCopyright .version {
    margin: 0 20px 0 0;
}

.signinCopyright a {
}

.signinCopyright a:hover,
.signinCopyright a:focus {
}

.signinCopyright a:active {
}

#loginNav {
    margin-right: 24px;
    margin-bottom: 0;
    padding-top: 3px;
    font-size: .75em;
    float: right;
}

#loginNav a {
    color: #fff;
}

#marketingContainer {
    display: none;
}

@media (min-width: 1240px) {

    #marketingContainer {
        position: relative;
        width: 50%;
        display: table-cell;
        background: #6d3075;
    }

    #marketingContainer iframe {
        overflow: hidden;
        position: fixed;
        height: 100%;
        width: 50%;
        top: 0;
        right: 0;
    }

    .signinLowerContainer.hasMarketing {
        width: 50%;
    }

}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* PRINTABLE CSS */

.filterContent {
    border: none;
    font-size: 0.9em;
    width: 100%;
    margin-left: 7px;
    margin-bottom: 4px;
}

.filterContent input {
    font-size: 0.9em;
    margin-top: 1px;
    margin-left: 6px;
}

.filterContent td {
    font-size: 0.9em;
    margin-top: 1px;
}

#containerLowerPrintable {
    background-color: #fff;
    border: none;
    padding: 0;
    padding-left: 12px;
    width: 688px;
}

#containerPrintable {
    width: 700px;
    border: #dddddd 1px solid;
    border-top: none;
    position: relative;
    margin: 0 auto;
    padding: 0;
    text-align: left;
    background: #fff;
}

#containerUpperPrintable {
    background-color: #fff;
    padding: 0;
    margin: 0;
    width: 700px;
    height: 74px;
    border: none;
}

.oneColInnerWidthTopPrintable {
    width: 698px;
    font-size: 1em;
}

.oneColInnerWidthPrintable {
    font-size: 1em;
    width: 656px;
}

#containerContentBasicPrintable {
    width: 674px;
    padding: 0;
    margin: 0;
    padding-top: 12px;
    padding-bottom: 3px;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* FORM CSS */
.inputEnabled {
    background-color: #fff;
}

.inputDisabled {
    background-color: #dedede;
}

.formNoMargin {
    margin: 0;
}

.formSubtabTableHeaderTR, .commentHistoryHeaderTR {
    background: #eff0e1; /*colorPaginationBG*/
    text-align: center;
}

.labelRequired {
    font-weight: bold;
}

.inputFile {
    height: 18px;
    padding: 8px 12px;
    line-height: 18px;
    font-size: 12px;
    font-weight: normal;
    color: #444;
    box-sizing: border-box;
    width: 256px;
}

.controlWrapper > input.inputFile {
    overflow-x: hidden;
    overflow-y: hidden;
}

optgroup, .inputL, .inputXL, .inputXXL, .input325, .input375, .inputM, .inputS, .input35, .input45, .input5digit, .input100, .input125, .input135, .input160,
.input185, .input250, .input510, .input, .inputDate, .fontSet, .input1digit, .input2digit, .input3digit, .input5digit, .input6digit, .input7digit, .input10digit,
.inputFile, .inputXXXL_menu, .input510 {
}

.input185 {
    width: 185px;
}

.inputL {
    width: 200px;
}

.input250 {
    width: 250px;
}

.inputXL {
    width: 300px;
}

.inputXXL {
    width: 450px;
}

.inputXXXL {
    width: 660px;
}

.inputXXXL_menu {
    width: 633px;
}

.inputExtended {
    width: 800px;
}

.input325 {
    width: 325px;
}

.input375 {
    width: 375px;
}

.input510 {
    width: 510px;
}

.input510_menu {
    width: 487px;
}

.input550 {
    width: 550px;
}

.inputM {
    width: 150px;
}

.input160 {
    width: 160px;
}

.input135 {
    width: 135px;
}

.input125 {
    width: 125px;
}

.input100 {
    width: 100px;
}

.inputS {
    width: 70px;
}

.input35 {
    width: 35px;
}

.input45 {
    width: 45px;
}

.inputDate {
    width: 85px !important;
    cursor: default;
}

.inputPostal {
    text-transform: uppercase;
}

.input1digit {
    width: 15px;
}

.input2digit {
    width: 22px;
}

.input3digit {
    width: 30px;
}

.input5digit {
    width: 42px !important;
}

.input6digit {
    width: 45px;
}

.input7digit {
    width: 48px;
}

.input10digit {
    width: 80px !important;
}

.input {
    width: 50px;
}

.selectDataElement {
    width: 275px;
}

.selectComparator {
    width: 135px;
}

.selectTimeMarker {
    width: 165px;
}

.selectTimeUnit {
    width: 62px;
}

.selectTime {
    width: 48px;
    padding: 0;
}

.selectDataElement, .selectComparator, .selectTimeMarker, .selectTimeUnit, .selectTime, .operator, .fontSet {
    font-size: 11px;
    box-sizing: content-box;
}

.textboxLarge {
    width: 400px;
}

.selectInput {
    width: 175px;
    height: 66px;
}

.rundate {
    width: 200px;
}

.operator {
    width: 36px;
}

.textbox {
    width: 30px;
}

.zoneSelectBox {
    width: 200px;
    color: #000;
}

.radioBtn,
.checkbox {
    width: 1rem;
    height: 1rem;
    margin-right: .25rem;
    min-height: unset !important;
    line-height: 1rem !important;
    vertical-align: text-bottom;
}

.formSubtabTable, .formSubtabTable TABLE, .formSubtabTableLg, .formSubtabTableLg TABLE {
    font-size: 1em;
}

.formSubtabTable ol, .formSubtabTable ul, .formSubtabTable li {
    padding-top: 2px;
    padding-bottom: 2px;
}

.formSubtabTable TD, .formSubtabTableLg TD {
    padding: 4px;
    padding: 4px 4px 0 4px \0/ IE8+9; /* IE8+9  */
}

.formBorderlessTable {
    width: 100%;
}

.formBorderlessTableLg {
    width: 704px;
    font-size: 1em;
}

.formBorderlessTable ol, .formBorderlessTable ul, .formBorderlessTable li {
    padding-top: 2px;
    padding-bottom: 2px;
}

.formBorderlessTable, .formBorderlessTableLg {
    border-right: #dadada 0 solid;
    border-bottom: #dadada 0 solid;
}

.formBorderlessTable TD {
    border: #dadada 0 solid;
    border-right: none;
    border-bottom: none;
    padding: 0 8px;
}

.formSubtabTable, .genericInFormST1 {
    width: auto;
}

.genericInFormST1 {
    width: 664px;
}

.frmInlineTargetGroupContainer {
    padding-left: 7px;
    line-height: 32px;
    word-break: break-word;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* RELATED CSS */

.tableVisibility {
    margin-bottom: 4px;
    background-color: #dadada; /*colorOutline*/

}

.tableVisibility TD {
    background-color: #fff;
}

.tableVisibility {
    width: 720px;
}

input.tree-check-box {
    width: auto;
    margin: 0;
    padding: 0;
    height: 14px;
    vertical-align: middle;
    font-size: 0.9em;
    border: none;
}

input.targetingInput {
    width: auto;
    margin: 0;
    padding: 0;
    height: 18px;
    vertical-align: middle;
    border: 1px solid #8a84cd;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* Banner */
#welcome {
    color: /*colorWelcomeText*/ #aa3729;
    padding-left: 9px;
}

.bannerLink, .bannerText, .bannerDivider {
    font-size: 10px;
}

.bannerLink {
    text-decoration: none;
    color: #f0f0f0;
}

.bannerThemeLight .bannerLink {
    color: #414141;
}

.bannerThemeHP {
    background: #0072AA url("commonimages/layout/banner_background.png") repeat left top;
    color: #fdfdfd;
}

.bannerThemeDark {
    background: #6d3075;
    color: #f0f0f0;
}

.bannerThemeDark .bannerNodeLabel {
    color: #f0f0f0;
}

.bannerThemeDark select {
    background: #222;
    border-color: #999;
    color: #f0f0f0;
}

.bannerThemeHP select {
    background: #0da3e3;
    border-color: #bbb;
    color: #f0f0f0;
}

.bannerThemeDark option {
    background: #222;
    color: #f0f0f0;
}

.bannerThemeHP option {
    background: #0da3e3;
    color: #f0f0f0;
}

.bannerThemeLight {
    background: none;
    background-color: #fff;
    -webkit-box-shadow: inset -0px -2px 2px 2px #f1f1f1;
    -moz-box-shadow: inset -0px -2px 2px 2px #f1f1f1;
    box-shadow: inset -0px -2px 2px 2px #f1f1f1;
    color: #414141;
}

.bannerText {
    color: #f0f0f0;
    white-space: nowrap;
}

.bannerThemeLight .bannerText {
    color: #414141;
}

.bannerDivider {
    color: #484848;
}

.bannerThemeHP .bannerDivider {
    color: #87898b;
}

.bannerCategory a, .bannerCategoryDisabled, .bannerCategorySelected a {
    text-decoration: none;
    font-size: 13px;
    font-weight: bold;
    color: #fdfdfd;
}

.bannerCategory a:hover, .bannerCategorySelected a:hover {
    text-decoration: none;
}

.bannerThemeLight .bannerCategory a, .bannerThemeLight .bannerCategoryDisabled, .bannerThemeLight .bannerCategorySelected a {
    color: #444;
}

.bannerCatergoryIndicator {
    position: relative;
    top: 31px;
    border: 1px solid #ccc;
    background-color: #fafafa;
    height: 5px;
    width: 100%;
    border-radius: 2px;
    -webkit-box-shadow: 0 3px 5px #fafafa;
    -moz-box-shadow: 0 3px 5px #fafafa;
    box-shadow: 0 3px 5px #fafafa;
    opacity: 0.35;
    filter: alpha(opacity=35, style=0);
}

.bannerThemeLight .bannerCatergoryIndicator, .bannerThemeHP .bannerCatergoryIndicator {
    opacity: 0.45;
}

.bannerCategory, .bannerCategoryDisabled, .bannerCategorySelected {
    margin: 0 10px;
}

.bannerCategory, .bannerCategorySelected {
    cursor: pointer;
}

.bannerCategoryDisabled {
    color: #777;
}

.bannerThemeLight .bannerCategoryDisabled, .bannerCategoryDisabled {
    color: #bbb;
}

.bannerCategorySelected {
    background: #2196f3;
    padding-left: 15px;
}

.bannerThemeLight .bannerCategorySelected {
    background: #2196f3;
}

.bannerCategoryArrowIcon, .bannerCategoryMenuArrowIcon {

}

.bannerCategoryArrowIcon {
    color: #f0f0f0;
}

.bannerCategoryMenuArrowIcon {
    color: #adadad;
}

.bannerThemeLight .bannerCategoryArrowIcon {
    color: #444;
}

.bannerThemeLight .bannerCategoryMenuArrowIcon, .bannerThemeHP .bannerCategoryMenuArrowIcon {
    color: #adadad;
}

.categoryLoadingIndicator {
    text-align: center;
    font-size: 16px;
    padding-top: 20px;
    color: #f5f5f5;
    width: 150px;
    height: 45px;
}

.bannerThemeLight .categoryLoadingIndicator, .bannerThemeHP .categoryLoadingIndicator {
    color: #999;
    text-shadow: none;
}

.searchLoadingIndicator {
    background: url(commonimages/layout/loading_grey.gif) no-repeat center center;
}

.searchLoadingIndicator {
    width: 16px;
    height: 16px;
}

.categoryMenuContainer {
    position: absolute;
    z-index: 1000;
}

.categoryMenuLabelTable, .categoryMenuItemsTable {
    background-color: #1a1a1a;
    border: 1px solid #000;
}

.bannerThemeLight .categoryMenuLabelTable, .bannerThemeLight .categoryMenuItemsTable,
.bannerThemeHP .categoryMenuLabelTable, .bannerThemeHP .categoryMenuItemsTable {
    background-color: #e0e0e0;
    border: 1px solid #bbb;
}

.categoryMenuLabelTable {
    border-radius: 4px 4px 0 0;
    -moz-border-radius: 4px 4px 0 0;
    -webkit-border-radius: 4px 4px 0 0;
    position: relative;
    z-index: 2;
}

.categoryMenuItemsTable {
    border-radius: 4px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    vertical-align: top;
    position: relative;
    top: -2px;
    z-index: 1;
}

.categoryMenuLabelCellLeft, .categoryMenuLabelCellRight {
    padding: 5px 7px;
    border: 1px solid #313131;
    text-shadow: none;
    font-size: 13px;
    font-weight: bold;
    color: #c3caed;
}

.bannerThemeDark a:hover {
    color: #c3caed;
}

.bannerThemeLight a:hover {
    color: #5f6ba9;
}

.bannerThemeHP a:hover {
    color: #bbb;
}

.bannerThemeLight .categoryMenuLabelCellLeft, .bannerThemeLight .categoryMenuLabelCellRight,
.bannerThemeHP .categoryMenuLabelCellLeft, .bannerThemeHP .categoryMenuLabelCellRight {
    border: 1px solid #fcfcfc;
    color: #5f6ba9;
}

.bannerThemeHP .categoryMenuLabelCellLeft, .bannerThemeHP .categoryMenuLabelCellRight {
    color: #444;
}

.bannerThemeLight .categoryMenuLabelCellLeft, .categoryMenuLabelCellLeft,
.bannerThemeHP .categoryMenuLabelCellLeft {
    padding-right: 2px;
    border-right: none;
    border-radius: 4px 0 0 0;
    -moz-border-radius: 4px 0 0 0;
    -webkit-border-radius: 4px 0 0 0;
}

.bannerThemeLight .categoryMenuLabelCellRight, .categoryMenuLabelCellRight,
.bannerThemeHP .categoryMenuLabelCellRight {
    padding-left: 2px;
    border-left: none;
    border-radius: 0 4px 0 0;
    -moz-border-radius: 0 4px 0 0;
    -webkit-border-radius: 0 4px 0 0;
}

.categoryMenuLabelTable, .categoryMenuLabelCellLeft, .categoryMenuLabelCellRight,
.bannerThemeLight .categoryMenuLabelTable, .bannerThemeLight .categoryMenuLabelCellLeft, .bannerThemeLight .categoryMenuLabelCellRight,
.bannerThemeHP .categoryMenuLabelTable, .bannerThemeHP .categoryMenuLabelCellLeft, .bannerThemeHP .categoryMenuLabelCellRight {
    border-bottom: none;
}

.categoryMenuItemsCell {
    border: 1px solid #313131;
    font-size: 11px;
    font-weight: bold;
    color: #f0f0f0;
    border-radius: 4px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-box-shadow: 0 3px 3px 0 rgba(119, 119, 119, 0.15);
    -webkit-box-shadow: 0 3px 3px 0 rgba(119, 119, 119, 0.15);
    box-shadow: 0 3px 3px 0 rgba(119, 119, 119, 0.15);
}

.bannerThemeLight .categoryMenuItemsCell, .bannerThemeHP .categoryMenuItemsCell {
    border: 1px solid #fcfcfc;
    color: #444;
}

.categoryMenuTitle {
    font-size: 12px;
    padding: 2px 10px;
    font-weight: bold;
    color: #f0f0f0;
}

.bannerThemeLight .categoryMenuTitle, .bannerThemeHP .categoryMenuTitle {
    color: #444;
}

.categoryMenuItem {
    padding: 2px 15px 2px 20px;
    font-weight: normal;
    min-width: 65px;
    white-space: nowrap;
    font-size: 11px;
    color: #f0f0f0;
}

.bannerThemeLight .categoryMenuItem, .bannerThemeHP .categoryMenuItem {
    color: #444;
    text-shadow: none;
}

.categoryMenuItem:hover {
    color: #c3caed;
    text-shadow: none;
}

.bannerThemeLight .categoryMenuItem:hover {
    color: #5f6ba9;
}

.bannerThemeHP .categoryMenuItem:hover {
    color: #777;
}

.categoryMenuDividerBottom {
    border-top: 1px solid #313131;
}

.bannerThemeLight .categoryMenuDividerBottom, .bannerThemeHP .categoryMenuDividerBottom {
    border-top: 1px solid #fcfcfc;
}

.categoryMenuDividerTop {
    border-bottom: 1px solid #000;
}

.bannerThemeLight .categoryMenuDividerTop, .bannerThemeHP .categoryMenuDividerTop {
    border-bottom: 1px solid #bbb;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* Misc & Tables CSS */
.mce-widget button {
    height: 28px;
}

.mce-container.mce-menu {
    max-width: 450px;
    z-index: 65540 !important;
}

.mce-container.mce-menu .mceMenuSearchContainer {
    padding: 6px;
    border-bottom: 1px solid #d5d5d5;
    width: 100%;
    box-sizing: border-box;
}

.mce-container.mce-menu .mceMenuSearchInput {
    font-size: 14px !important;
    padding: 0 40px !important;
}
.mce-menu i.mce-i-none, .mce-menu i.mce-ico { 
	display: inline-block;
	margin-right: 3px;
}

.mce-menu .mce-custom-button .mce-text {
    color: #7c1a87;
    font-weight: bold;
}

.mceHiddenContent {
	display: none;
}

p.maintenanceNoticeParagraph {
    color: #833920;
}

.navPanelContainer {
    box-sizing: content-box;
    background-color: #fdfdfd;
    -webkit-box-shadow: inset -2px -2px 2px 2px #f1f1f1;
    -moz-box-shadow: inset -2px -2px 2px 2px #f1f1f1;
    box-shadow: inset -2px -2px 2px 2px #f1f1f1;
}

.contentTableIframeExtended .navPanelContainer {
}

.navItemsContainer {
    border-top: solid 1px #dadada;
}

.navItem {
    padding: 0 30px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #dadada;
    font-size: 16px;
    line-height: 48px;
    color: #444;
    cursor: pointer;
}

.navItem:hover {
    background-color: #dadada;
}

.navItem:active {
    background-color: #fff;
}

.navItemLinkIcon {
    float: right;
    font-size: 18px !important;
    position: relative;
    top: 14px;
}

.contentPanelContainer {
    background-color: #fdfdfd;
}

.containerLowerFull {
    /* Content panel */
    display: table-row;
    height: 100%;
}

#containerLower {
    padding: 48px 0 0;
    height: 100%;
}

#containerLower .contentPanelInnerContainer {
    margin: 0 auto;
    width: 1108px;
    height: 100%;
}

#containerLower .contentPanelInnerContainer.extendedPanel {
    width: 1240px;
}

.cheater, .cheater TD {
    font-size: 1px;
    margin: 0;
    padding: 0;
    border: none;
    height: 1px;
}

.cheater span {
    font-size: 1px;
}

.colorFrame {
    background: url("commonimages/icons/color_frame.gif") no-repeat center center;
}

.contentTableLg {
    width: 949px;
}

.fullLineLabel {
    font-weight: bold;
    font-size: 11px;
    overflow: hidden;
    white-space: nowrap;

}

.statusLabel {
    margin-left: 12px;
    font-size: 12px;
    font-weight: 400;
    opacity: 0.7;
}

.statusLabel .labelText + .labelText {
    margin-left: 8px;
}

#lteIE8 .fullLineLabel {
    letter-spacing: -0.9px;
}

.contentTableIframe .fullLineLabel, .detailsSummaryTable .fullLineLabel {
    color: #414141;
}

.noWrap {
    white-space: nowrap;
}

.tableSpacerThin {
    padding-top: 7px;
}

.rowHighlight {
    background-color: #ededed;
}

.textDisable {
    color: gray;
}

.themeDiv {
    border: 1px solid #bbb;
}

.themeDiv {
    height: 261px;
    width: 350px;
}

.horizontalSeparator {
    min-height: 3px;
    height: 3px;
    overflow: hidden;
}

.verticalScroll {
    overflow: auto;
}

* + html .verticalScroll {
    overflow-y: hidden; /* IE7 */
    padding-bottom: 20px; /* IE7 */
}

* html .verticalScroll {
    overflow-y: hidden;
    padding-bottom: 20px;
}

.zeroOpacity {
    opacity: 0;
    filter: alpha(opacity=0, style=0);
}

.roundAllCorners_4px {
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.roundAllCorners_6px {
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
}

.roundTopLeft_6px {
    -webkit-border-top-left-radius: 6px;
    -moz-border-top-left-radius: 6px;
    border-top-left-radius: 6px;
}

.roundTopRight_6px {
    -webkit-border-top-right-radius: 6px;
    -moz-border-top-right-radius: 6px;
    border-top-right-radius: 6px;
}

.roundBottomLeft_6px {
    -webkit-border-bottom-left-radius: 6px;
    -moz-border-bottom-left-radius: 6px;
    border-bottom-left-radius: 6px;
}

.roundBottomRight_6px {
    -webkit-border-bottom-right-radius: 6px;
    -moz-border-bottom-right-radius: 6px;
    border-bottom-right-radius: 6px;
}

.blockItem {
    border: 1px #d6c7de solid;
    background-color: #f1edf4;
    color: #6d3075;
    white-space: nowrap;
    padding: 0 10px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    font-size: 10px;
    display: inline-block;
    cursor: default;
    line-height: 22px;
    font-weight: 600;
}

.blockItem:hover {
    background-color: #d6c7de;
}

.tokenContainer, .tokenContainerDisabled {
    padding: 2px 6px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    margin: 3px;
    display: inline-block;
    font-size: 11px;
}

.tokenContainer {
    border: 1px #c0c6e4 solid;
    background-color: #e2e7ff;
    color: #3b477e;
    cursor: pointer;
}

.tokenContainer:hover {
    background-color: #e8ecff;
}

.tokenContainerDisabled {
    border: 1px #bbb solid;
    background-color: #e5e5e5;
    color: #bbb;
    cursor: default;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* Navigation Tree */
.navTreeFixedItems {
    box-sizing: content-box;
    background-color: #f5f5f5;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* jsTree - Folders */
.folderTreeDiv {
    height: 100px;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #fff;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.selectedFixedItem {
    background: url(commonimages/tree/selectedFixedItem_grad.gif) no-repeat center center;
    font-weight: bold;
    font-style: italic;
    font-size: 1.1em;
}

.jstree-legacy-default .jstree-legacy-hovered {
    color: #414141;
}

.jstree-legacy-default .disabledTreeNode.jstree-legacy-hovered {
    color: #999;
}

.jstree-proton {
    font-family: inherit !important;
    color: #444;
}

.jstree-proton .jstree-wholerow-clicked {
    background: none !important;
}

.jstree-proton .jstree-wholerow-default-selected {
    background: #6d3075 !important;
}
.jstree-proton .jstree-wholerow-hovered{
    background: #dadada !important;
}

.jstree-proton .jstree-wholerow-clicked {
    background: #dadada;
}
.jstree-proton .jstree-hovered {
    color: #444 !important;
}
.jstree-proton .jstree-default-selected {
    color: #fff !important;
}
.jstree-proton a.jstree-hovered.jstree-checked:not(.default-selected-child) {
    color: #ddcbdd !important;
}

.jstree-proton li.jstree-leaf, .jstree-proton div.jstree-wholerow, .jstree-proton a.jstree-anchor, .jstree-proton i.jstree-icon {
    height: 32px;
    line-height: 32px;
}

.jstree-proton li.jstree-node {
    position: relative;
}

.jstree-proton a.jstree-anchor i.jstree-icon {
    margin-right: 4px;
    line-height: 30px;
}

.jstree-proton a.jstree-anchor {
    line-height: 32px;
    margin: 0;
    padding-left: 6px;
}

.jstree-proton i.jstree-icon.jstree-ocl {
    margin: 0;
    padding: 10px 4px 10px 14px;
}

.jstree-proton .jstree-wholerow-hovered + .jstree-icon {
    color: #444;
}

.jstree-proton .jstree-wholerow-default-selected + .jstree-icon {
    color: #fff;
}

.jstree-proton .jstree-icon:empty {
    width: 6px !important;
    height: auto !important;
    line-height: initial !important;
}

.jstree-proton .jstree-node,
.jstree-proton .jstree-icon {
    background: none !important;
}

.jstree-proton i.jstree-icon.jstree-ocl {
    font: normal 900 normal 10px/1 "Font Awesome 6 Pro";
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-indent: inherit;
}

.jstree-proton > .jstree-no-dots .jstree-open > .jstree-ocl::before {
    content: "\f0d7";
}

.jstree-proton > .jstree-no-dots .jstree-closed > .jstree-ocl::before {
    content: "\f0da";
}

.jstree-proton a.jstree-disabled {
    color: #aaa;
}

.jstree-proton i.jstree-icon.fa, .jstree-proton i.jstree-icon.far, .jstree-proton i.jstree-icon.fal, .jstree-proton i.jstree-icon.fas {
    width: auto !important;
    margin-left: 6px;
    margin-right: 8px !important;
    line-height: 30px !important;
}

ul.jstree-contextmenu.vakata-context {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    background: #fff;
    z-index: 1030;
}

.jstree-contextmenu.vakata-context i {
    display: none;
}

.jstree-contextmenu.vakata-context .vakata-contextmenu-sep {
    display: none;
}

.jstree-contextmenu.vakata-context a {
    padding: 0 1em;
    text-align: left;
}

.folderTreeDiv .jstree-legacy-clicked, div#actionPopup .jstree-legacy-clicked {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    background-color: #ddd;
}

.jstree-legacy-default li.selectedTreeNodeLi {
    border-top: 1px solid #9fa7cb;
    border-bottom: none;
    -webkit-border-radius: 6px 0 0 6px;
    -moz-border-radius: 6px 0 0 6px;
    border-radius: 6px 0 0 6px;
}

/* jsTree for comparison CSS */
/* TODO: Apply new-theme colors for this feature */
a.jstree-anchor[compare_state="added"]:not(.jstree-clicked){
	color: green;
}
a.jstree-anchor[compare_state="removed"]:not(.jstree-clicked){
	color: red;
}
a.jstree-anchor[compare_state="updated"]:not(.jstree-clicked){
	color: orange;
}
a.jstree-anchor[compare_state="changed"]:not(.jstree-clicked){
	color: #000;
	font-weight: bold;
}
a.jstree-anchor[compare_state="unchanged"]:not(.jstree-clicked){
	color: #555;
}

/* START Legacy jsTree CSS */
.jstree-default .jstree-hovered {
    color: #414141;
}

.jstree-default .disabledTreeNode.jstree-hovered {
    color: #999;
}

.folderTreeDiv .jstree-default .jstree-clicked, div#actionPopup .jstree-default .jstree-clicked {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    background-color: #ddd;
}

.jstree-default li.selectedTreeNodeLi {
    border-top: 1px solid #9fa7cb;
    border-bottom: none;
    -webkit-border-radius: 6px 0 0 6px;
    -moz-border-radius: 6px 0 0 6px;
    border-radius: 6px 0 0 6px;
}

/* END Legacy jsTree CSS */

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* NAVIGATION TREE CSS */
.treeNodeLink, .selectedNavItem {
    font-size: 12px;
    white-space: nowrap;
    text-decoration: none;
    padding-left: 3px;
    padding-right: 3px;
    margin-left: 1px;
}

.treeNodeLinkBold, .selectedNavItemBold {
    font-size: 12px;
    font-weight: bold;
    white-space: nowrap;
    text-decoration: none;
    padding-left: 3px;
    padding-right: 3px;
    margin-left: 1px;
}

.treeNodeLinkItalicBold, .selectedNavItemItalicBold {
    font-size: 12px;
    font-weight: bold;
    font-style: italic;
    white-space: nowrap;
    text-decoration: none;
    padding-left: 3px;
    padding-right: 3px;
    margin-left: 1px;
}

/* Width: 2000 prevents floating items in LI from wrapping
   Requires container to not scroll (genericTreeviewContainer) */
.treeContentDiv {
    overflow: auto;
    width: 2000px;
}

.containerMinHeight {
    min-height: 341px;
    background-color: #fff;
    vertical-align: top;
    margin-bottom: 5px;
}

.genericTreeviewContainer {
    font-weight: normal;
    width: 238px; /*leftColWidth*/
    overflow-x: hidden;
    padding: 0;
    background-color: #fdfdfd;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* All below for IE */

* html a:hover {
    visibility: visible
}

* html #containerLowerSigninInner {
    height: 200px;
}

* html #containerUpper {
    mix-width: 986px;
    height: 74px;
}

* html .headerForGenericL1, * html .genericL1 {
    width: 100%;
}

* html .graphicContainer {
    overflow: auto;
    width: 666px;
    overflow-y: hidden;
    padding-bottom: 20px;
}

* html .formSubtabTable, * html .genericInFormST1 {
    width: 680px;
}

* html .containerSubTabsContent {
    width: 600px;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* NEW FOR IE */

* html #containerLowerPrintable {
    width: 698px;
}

* html #containerPrintable {
    width: 700px;
}

* html #containerUpperPrintable {
    width: 698px;
}

* html .oneColInnerWidthTopPrintable {
    width: 698px;
}

* html .oneColInnerWidthPrintable {
    width: 656px;
}

* hmtl #containerContentBasicPrintable {
    width: 674px;
}

* html #containerLowerSigninInner {
    width: 554px;
}

* html .genericL1 {
    height: auto;
    height: 300px;
}

* html .contentDataDivWidth {
    width: 720px;
}

* html .contentLogDataDivWidth {
    width: 675px;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* Document, Zones and Parts  */
.zoneTipText {
    text-align: left;
    font-size: 11px;
}

.shadowFrameTRcorner {
    background: url("commonimages/shadowFrame_vert.png") no-repeat left top;
    height: 20px;
    width: 6px;
}

.shadowFrameRightEdge {
    background: url("commonimages/shadowFrame_vert.png") no-repeat left bottom;
    width: 6px;
}

.shadowFrameBLcorner {
    background: url("commonimages/shadowFrame_hori.png") no-repeat left top;
    height: 8px;
    width: 20px;
}

.shadowFrameBottomEdge {
    background: url("commonimages/shadowFrame_hori.png") no-repeat right top;
    height: 8px;
}

.rolledCorners {
    -webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .3);
    -moz-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .3);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .3);
}

.emailSection {
    background: #fafafa;
}

.sectionFrame {
    background: #fff;
    display: inline-block;
}

.smsWidgetContainer .zoneNotSelectedClass, .smsWidgetContainer .zoneNotSelectedClass {
    background-color: #d6dbe2;
}

.emailHeader {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    margin-bottom: 5px;
    -moz-box-shadow: 3px 3px 3px 0 rgba(119, 119, 119, 0.25);
    -webkit-box-shadow: 3px 3px 3px 0 rgba(119, 119, 119, 0.25);
    box-shadow: 3px 3px 3px 0 rgba(119, 119, 119, 0.25);
}

.webHeader {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    -moz-box-shadow: 3px 3px 3px 0 rgba(119, 119, 119, 0.25);
    -webkit-box-shadow: 3px 3px 3px 0 rgba(119, 119, 119, 0.25);
    box-shadow: 3px 3px 3px 0 rgba(119, 119, 119, 0.25);
}

.emailWidgetContentArea {
    background: #fff;
}

.editMultipartZonePart {
    height: 75px;
    width: 75px;
    right: 0;
    left: 25px;
    position: absolute;
    overflow: hidden;
}

.editZonePartSelectedClass {
    background: #6e8b9e url(commonimages/gradient_zoneEnabled.gif) repeat-x left top;
    color: /*colorTabSelectedText*/ #fff;
    border: 1px solid #3d5f79;
    opacity: 0.7;
    filter: alpha(opacity=70, style=0);
}

.editZonePartNotSelectedClass {
    background: #aaaaaa url(commonimages/gradient_zoneDisabled.gif) repeat-x left top;
    color: /*colorTabSelectedText*/ #fff;
    border: 1px solid #727272;
    opacity: 0.7;
    filter: alpha(opacity=70, style=0);
}

.graphicWatermark {
    cursor: default;
    background: url('commonimages/graphic_watermark.gif') no-repeat center center;
    opacity: 0.3;
    filter: alpha(opacity=30, style=0);
}

.pageClass {
    background: #dddddd;
    margin-bottom: 5px;
    margin-left: 5px;
    margin-right: 5px;
    border: 1px solid #bbbbbb;
}

.pageLabelDiv {
    margin: 2px 15px 0 15px;
    padding-left: 9px;
    padding-right: 9px;
    height: 14px;
    max-height: 14px;
    font-size: 8pt;
    color: #000;
    cursor: default;
    border: none;
    position: absolute;
    white-space: nowrap;
}

.pageWatermark {
    color: #d5d5d5;
    font-size: 110px;
    font-weight: bold;
    top: -10px;
    position: relative;
    cursor: default;
    overflow: hidden;
}

.multipartZone {
    height: 300px;
    width: 250px;
    background-color: /*colorHighlight*/ #c8c8c8;
    border: 1px solid #9a9a9a;
    display: block;
    position: relative;
    overflow: hidden;
    cursor: default;
}

.multipartZonePart {
    position: absolute;
    overflow: hidden;
    font-size: 1px; /* IE fix for oversizing part height */
}

div.zoneSelectedClass {
    background-color: #c5cae3;
    cursor: default;
}
div.zoneSelectedClass.flowZone, div.zoneNotSelectedClass.flowZone {
    background-color: #4e73be;
}
div.zoneSelectedClass.flowZone i, div.zoneNotSelectedClass.flowZone i {
    position: absolute;
    left: 2px;
    top: 1px;
    font-size: 11px;
    color: white;
}
div.zoneSelectedClass.flowIntoZone i, div.zoneNotSelectedClass.flowIntoZone i {
    position: absolute;
    right: 2px;
    bottom: 1px;
    font-size: 11px;
    color: #4e73be;
}
div.zoneSelectedClass[backer=true], div.zoneNotSelectedClass[backer=true] {
    z-index: 0;
}
div.zoneSelectedClass, div.zoneNotSelectedClass {
    z-index: 1;
}

.widgetSize_small .zoneSelectedClass, .widgetSize_small .zoneNotSelectedClass,
.widgetSize_medium .zoneSelectedClass, .widgetSize_medium .zoneNotSelectedClass,
.widgetSize_large .zoneSelectedClass, .widgetSize_large .zoneNotSelectedClass {
    border-color: #6f7bb9;
    border-style: dashed;
}
.widgetSize_small .zoneSelectedClass.flowZone, .widgetSize_small .zoneNotSelectedClass.flowZone,
.widgetSize_medium .zoneSelectedClass.flowZone, .widgetSize_medium .zoneNotSelectedClass.flowZone,
.widgetSize_large .zoneSelectedClass.flowZone, .widgetSize_large .zoneNotSelectedClass.flowZone {
    border-color: #2b509a;
    border-style: dashed;
}

.widgetSize_small .zoneSelectedClass, .widgetSize_small .zoneNotSelectedClass {
    border-width: 1px;
}

.widgetSize_medium .zoneSelectedClass, .widgetSize_medium .zoneNotSelectedClass {
    border-width: 1px;
}

.widgetSize_large .zoneSelectedClass, .widgetSize_large .zoneNotSelectedClass {
    border-width: 1px;
}

.widgetSize_small .multipartZonePart, .widgetSize_medium .multipartZonePart, .widgetSize_large .multipartZonePart {
    border-color: #6f7bb9;
    border-style: dotted;
}

.widgetSize_small.smsWidgetContainer .zoneSelectedClass, .widgetSize_small.smsWidgetContainer .zoneNotSelectedClass {
    border-radius: 10px;
}
.widgetSize_medium.smsWidgetContainer .zoneSelectedClass, .widgetSize_medium.smsWidgetContainer .zoneNotSelectedClass {
    border-radius: 20px;
}
.widgetSize_large.smsWidgetContainer .zoneSelectedClass, .widgetSize_large.smsWidgetContainer .zoneNotSelectedClass {
    border-radius: 35px;
}

.widgetSize_small .multipartZonePart {
    border-width: 0.5px;
}

.widgetSize_medium .multipartZonePart {
    border-width: 1px;
}

.widgetSize_large .multipartZonePart {
    border-width: 1px;
}

.textWatermark {
    cursor: default;
    background: url('commonimages/text_watermark.gif') no-repeat center center;
    opacity: 0.3;
    filter: alpha(opacity=30, style=0);
}

.zoneNotSelectedCircle {
    color: #fff;
    position: relative;
    top: -3px;
    cursor: default;
    height: 21;
    min-width: 21;
    filter: alpha(opacity=100, style=0); /* IE fix */
}

.zoneNotSelectedClass {
    background-color: transparent;
    cursor: default;
}

.zoneNotSelectedClass:hover {
    background-color: #eaebf5;
}

.zonePartNotSelectedClass {

}

.zonePartSelectedClass {
    background-color: #b4b2ff;
}

.zonePartSelected {
    background-color: #b4b2ff;
    opacity: 0.5;
    filter: alpha(opacity=50, style=0);
}

[verticalAlign="middle"] .textContentContainer {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

[verticalAlign="bottom"] .textContentContainer {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* Popups  */
.actionPopupContainer {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    -moz-box-shadow: 0 0 20px 4px rgba(0, 0, 0, 0.2);
    -webkit-box-shadow: 0 0 20px 4px rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 20px 4px rgba(0, 0, 0, 0.2);
}

.popupWindow {
    margin-left: -310px;
    margin-right: auto;
    position: absolute;
    z-index: 50;
    font-size: 14px;
    padding: 0 20px 24px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    -moz-box-shadow: 0 0 20px 4px rgba(0, 0, 0, 0.2);
    -webkit-box-shadow: 0 0 20px 4px rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 20px 4px rgba(0, 0, 0, 0.2);
    background: #fff;
}

.popupWindow p {
    margin: 0 0 16px;
    line-height: 24px;
}

.popupWindow .popupWindowButtonsContainer {
    margin: 24px 0 0;
    text-align: right;
}

.popupWindow .popupWindowButtonsContainer > * {
    margin: 0 0 0 12px;
}

.popupContentWidth {
    width: 320px;
}

* html .popupWindow {
    margin-left: -340px;
}

.popupHeader {
    background: #6d3075;
    border-radius: 3px 3px 0 0;
    padding: 0 20px;
    margin: 0 -20px 15px;
    line-height: 48px;
    font-size: 16px;
    text-align: left;
    cursor: move;
    color: #fff;
}

.popupGraphicContent {
    height: 100px;
    width: 290px;
    overflow-y: hidden;
}

.popupTextContent {
    padding: 5px;
    width: 290px;
}

.popupContent, .loadingContent {
    margin: 2px;
    border: 1px solid #ccc;
}

.popupContent {
    padding: 16px 20px 24px;
    background: #fff;
    -webkit-border-radius: 0 0 3px 3px;
    -moz-border-radius: 0 0 3px 3px;
    border-radius: 0 0 3px 3px;
    font-size: 14px;
    color: #444;
}

.popupContent span {
    white-space: normal;
}

.loadingContent {
    background-color: #fff;
    padding: 6px;
}

/* Datatable Summary Detail Info */

.summaryDetailInfoContainer {
    overflow: hidden;
    padding: 16px 0;
}

.summaryDetailInfoContainer .actionBtn,
.summaryDetailInfoContainer .actionBtn_hov,
.summaryDetailInfoContainer .actionBtn_selected,
.summaryDetailInfoContainer .actionBtn_toggleSelect,
.summaryDetailInfoContainer .actionBtn_disabled {
    float: left;
    margin: 0 40px 0 0;
}

.summaryDetailInfo {
    float: left;
    color: #444;
}

.summaryDetailInfo + .summaryDetailInfo {
    margin: 0 0 0 40px;
}

.summaryDetailInfo > * {
    display: block;
}

.summaryDetailInfo em {
    margin: 4px 0 0;
    font-style: normal;
    font-size: 12px;
    line-height: 18px;
}

.summaryDetailInfo span {
    font-weight: 600;
    font-size: 10px;
    line-height: 16px;
    color: #7b7b7b;
}

/* Context Classes */

.dangerTxt,
.suppressIconDiv {
    color: #d8351c !important;
}

.successTxt {
    color: #459e49 !important;
}

.infoTxt {
    color: #407fad !important;
}

.warningTxt,
.holdIconDiv {
    color: #d87e2f !important;
}

/* Form layouts */

div.formControl {
    width: 100%;
    min-height: 40px;
    /* TEMP! */
    box-sizing: content-box !important;
}

.formControl.inlineControl {
    display: inline-block;
    margin: 0 0 16px;
}

.formControl.horizontal-control .labelText {
    line-height: 16px;
    margin: 0;
    padding: 12px 0;
}

.formControl.inlineControl.noSpacing {
    width: auto;
    margin: 0;
}

.formControl.inlineControl + .formControl.inlineControl {
    margin: 0 0 0 20px;
}

.formControl + .formControl {
    margin-top: 16px;
}

.formControl > .controlWrapper {
    position: relative;
    min-height: 40px;
    line-height: 40px;
}

.formControl > .controlWrapper.hasGroupControl {
    padding-right: 30px;
}

.formControl > label {
    display: block;
}

.formControl.inlineControl > label {
    display: inline-block;
    margin-right: 10px;
    vertical-align: middle;
}

.formControl > label > .labelText {
    display: block;
    margin: 0 0 6px;
    font-size: 12px;
    line-height: 18px;
    font-weight: 600;
    text-align: left;
    color: #626262;
}

.formControl > label > .labelTextRight {
    text-align: right;
}

.formControl.inlineControl > label > .labelText {
    margin: 0;
}

.formControl.inlineControl > .controlWrapper {
    display: inline-block;
    vertical-align: middle;
}

.formControl > .controlWrapper > .control-static {
    color: #444;
    font-size: 14px;
    line-height: 40px;
}

.formControl > .controlWrapper > .control-hint {
    display: block;
    margin-top: 8px;
    line-height: 16px;
    font-style: italic;
    font-size: 12px;
    font-weight: 600;
    color: #6d3075;
}

.formControl > .controlWrapper > input[type='text'],
.formControl > .controlWrapper > input[type='password'],
.formControl > .controlWrapper > textarea,
.formControl > .controlWrapper.hasGroupControl > .inputDate,
.formControl > .controlWrapper > .tagCloudInputContainer > input[type='text'] {
    width: calc(100% - 38px);
}

.formControl > .controlWrapper > select {
    box-sizing: border-box;
    width: 100%;
}

.formControl.horizontal > label,
.formControl.horizontal > .controlWrapper {
    float: left;
}

.formControl.horizontal > label > .labelText {
    margin: 10px 16px 0 0;
}

.formControl > .controlWrapper > .actionBtnTable,
.formButtonsContainer > .actionBtnTable {
    margin: 0;
}

.formControl > .w1-4 {
    width: 25%;
}

.formControl > .w2-4 {
    width: 50%;
}

.formControl > .w3-4 {
    width: 75%;
}

.formControl > .w4-4 {
    width: 100%;
}

.formButtonsContainer {
    margin: 24px 0 0;
}

.zonesNavigation {
    text-align: center;
}

.zonesNavigation .navigationBtn {
    background: #dadada;
    display: inline-block;
    width: 32px;
    height: 32px;
    margin: 0 4px;
    line-height: 32px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    border: 0;
    text-align: center;
    font-size: 18px;
    color: #737373;
    cursor: pointer;
}

.zonesNavigation .navigationBtn .fa {
    margin-right: 3px;
}

.zonesNavigation .navigationBtn + .navigationBtn .fa {
    margin: 0 0 0 3px;
}

.zonesNavigation .navigationBtn:hover {
    background: #d6c7de;
    color: #6d3075;
}

.zonesNavigation .navigationBtn:active {
    background: #f1edf4;
    color: #a2a2a2;
}

/* Description lists */

.descriptionList {
    font-size: 14px;
    line-height: 21px;
    padding: 0 14px;
    overflow: hidden;
}

.descriptionList > * {
    width: 46%;
}

.descriptionList > .leftCol {
    float: left;

}

.descriptionList > .rightCol {
    float: right;
}

.descriptionList .descriptionTerm {
    display: inline-block;
    width: calc(50% - 20px);
    margin-right: 20px;
    padding: 10px 0 9px;
    vertical-align: top;
    color: #7b7b7b;
}

.descriptionList .descriptionTerm:not(:empty)::after {
    content: ':';
}

.descriptionList .descriptionDefinition,
.descriptionList .descriptionDefinitionList {
    display: inline-block;
    width: 50%;
    margin-left: -4px;
    padding: 10px 0 9px;
    vertical-align: top;
    color: #444;
}

.descriptionList.singleCol .descriptionDefinition {
    white-space: nowrap;
}

.descriptionList .descriptionDefinitionList {
    padding: 0;
    list-style: inside disc;
}

.descriptionList .descriptionDefinitionList > li {
    padding: 10px 0 9px;
    font-size: 14px;
    color: #444;
}


/*region gridLayoutTag style implementation*/

.gridLayout_innerWrapper {
    display: inline-block;
}

.gridLayout{
    display: table;
    width: 100%;
    text-align: initial;
}
.gridLayoutRow {
    display: table-row;
}

.gridLayoutCell {
    display: table-cell;
    margin: 4px;
}
.gridLayoutBody {
    display: table-row-group;
}
/*endregion*/

.flowLayout {
    display: flex;
}

.flowLayoutVertical {
    display: flex;
    flex-direction: column;
}

.flowLayoutCenter {
    align-items: center;
    align-content: center;
    justify-content: center;
}

.flowLayoutItem {
    display: flex;
}

.flowLayoutItem:nth-child(even) {
    margin-left: 8px;
    margin-right: 8px;
}

.flowLayoutItem:last-child {

}

.flowLayoutItemVertical {
    margin-top: 8px;
    margin-bottom: 8px;
}

.flowLayoutItemVerticalsectionHeader {
    font-size: 16px;
    color: #444;
    margin-bottom: 24px;
    font-weight: 400;
}

.flowLayout_innerWrapper {
    flex: 1;
}

.flowLayoutFillSpace {
    width: 100%
}

/* section header */

.sectionHeader {
    position: relative;
    font-size: 20px;
    line-height: 48px;
}

/* form layout styles */

.formLayout_wrapper {
    width: 100%;
    display:inline-block;
}

.formLayoutHeader {
    font-size: 20px;
    line-height: 48px;
}

.formLayoutIcon {
    line-height: 38px !important;
    margin: 0 16px 0 16px;
}

.formLayoutGroup {
    float: left;
    margin-right: 15px;
}

.formLayoutCenter {
    display: flex;
    align-items: center;
    align-content: center;
    justify-content: center;
}

/* IFrameWindowContainer */

.iFrameWindowContainer {
    padding: 15px;
}

.draggable {
    cursor: url('data:image/vnd.microsoft.icon;base64,AAACAAEAICACAAcABQAwAQAAFgAAACgAAAAgAAAAQAAAAAEAAQAAAAAAAAEAAAAAAAAAAAAAAgAAAAAAAAAAAAAA////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD8AAAA/AAAAfwAAAP+AAAH/gAAB/8AAA//AAAd/wAAGf+AAAH9gAADbYAAA2yAAAZsAAAGbAAAAGAAAAAAAAA//////////////////////////////////////////////////////////////////////////////////////gH///4B///8Af//+AD///AA///wAH//4AB//8AAf//AAD//5AA///gAP//4AD//8AF///AB///5A////5///8='), auto !important;
    cursor: -webkit-grab !important;
    cursor: -moz-grab !important;
    cursor: -o-grab !important;
    cursor: -ms-grab !important;
    cursor: grab !important;
}

.draggable:active,
.draggable.ui-draggable-dragging {
    cursor: url('data:image/vnd.microsoft.icon;base64,AAACAAEAICACAAcABQAwAQAAFgAAACgAAAAgAAAAQAAAAAEAAQAAAAAAAAEAAAAAAAAAAAAAAgAAAAAAAAAAAAAA////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD8AAAA/AAAAfwAAAP+AAAH/gAAB/8AAAH/AAAB/wAAA/0AAANsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//////////////////////////////////////////////////////////////////////////////////////gH///4B///8Af//+AD///AA///wAH//+AB///wAf//4AH//+AD///yT/////////////////////////////8='), auto !important;
    cursor: -webkit-grabbing !important;
    cursor: -moz-grabbing !important;
    cursor: -o-grabbing !important;
    cursor: -ms-grabbing !important;
    cursor: grabbing !important;
}

.draggable.ui-state-disabled,
.ui-state-disabled:active{
    cursor: not-allowed !important;
}

.draggable-item-indicator {
    color: #a2a2a2;
    line-height: 32px;
    font-size: 14px;
}

.draggable-item-indicator > .fa {
    display: inline-block;
    margin: 0 1px;
}

.smooth-scroll-wrapper {
    position: relative;
}

.smooth-scroll-wrapper > * {
    padding-bottom: 24px;
}

.smooth-scroll-wrapper::before {
    content: '\00a0';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 24px;
    background: -moz-linear-gradient(top, rgba(255,255,255,0) 0%, rgba(255,255,255,0) 1%, rgba(255,255,255,1) 100%);
    background: -webkit-linear-gradient(top, rgba(255,255,255,0) 0%,rgba(255,255,255,0) 1%,rgba(255,255,255,1) 100%);
    background: linear-gradient(to bottom, rgba(255,255,255,0) 0%,rgba(255,255,255,0) 1%,rgba(255,255,255,1) 100%);
    z-index: 2;
}

.smooth-scroll-wrapper.dark::before {
    background: -moz-linear-gradient(top, rgba(245,245,245,0) 0%, rgba(245,245,245,0) 1%, rgba(245,245,245,1) 100%);
    background: -webkit-linear-gradient(top, rgba(245,245,245,0) 0%,rgba(245,245,245,0) 1%,rgba(245,245,245,1) 100%);
    background: linear-gradient(to bottom, rgba(245,245,245,0) 0%,rgba(245,245,245,0) 1%,rgba(245,245,245,1) 100%);
}

/* Grid system */


.row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -16px;
    margin-left: -16px;

    /* TEMP! */
    box-sizing: border-box !important;
}

.inline-item {
    display: inline !important;
}

.block-item {
    display: block !important;
}

.inline-block-item {
    display: inline-block !important;
}

.align-content-left {
    text-align: left !important;
}

.align-content-right {
 text-align: right !important;
}

.align-content-center {
    text-align: center !important;
}

.align-items-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
}

.align-items-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
}

.align-items-center {
/*    -ms-flex-pack: center !important;
    justify-content: center !important;*/
}

.align-items-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
}

.align-items-top {
    -ms-flex-align: start !important;
    align-items: flex-start!important;
}

.align-items-middle {
    -ms-flex-align: center !important;
    align-items: center!important;
}

.align-items-bottom {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
}

.align-self-top {
    -ms-flex-item-align: flex-start !important;
    align-self: flex-start !important;
}

.align-self-middle {
    -ms-flex-item-align: center !important;
    align-self: center !important;
}

.align-self-bottom {
    -ms-flex-item-align: flex-end !important;
    align-self: flex-end !important;
}

.no-gutters {
    margin-right: 0;
    margin-left: 0
}

.no-gutters > .col,
.no-gutters > [class*=col-] {
    padding-right: 0;
    padding-left: 0
}

.col,
[class*='col-'] {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 16px;
    padding-left: 16px;
}

/*
.col > *,
[class*='col-'] > * {
    box-sizing: content-box; // TEMP
}
*/

.col {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%
}

.col-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none
}

.col-1of12 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%
}

.col-2of12 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%
}

.col-3of12 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%
}

.col-4of12 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%
}

.col-5of12 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%
}

.col-6of12 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%
}

.col-7of12 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%
}

.col-8of12 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%
}

.col-9of12 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%
}

.col-10of12 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%
}

.col-11of12 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%
}

.col-12of12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%
}

/* Helper classes */

.small-content {
    min-height: 520px !important;
    background: #fff;
}

.side-content {
    background: #dadada !important;
}

.cleared::after {
    display: block;
    clear: both;
    content: "";
}

.brdr-top-lv0 {
    border-top: 0 !important;
}

.brdr-bottom-lv0 {
    border-bottom: 0 !important;
}

.brdr-vertical-lv0 {
    border-top: 0 !important;
    border-bottom: 0 !important;
}

.brdr-left-lv0 {
    border-left: 0 !important;
}

.brdr-right-lv0 {
    border-right: 0 !important;
}

.brdr-horizontal-lv0 {
    border-left: 0 !important;
    border-right: 0 !important;
}

.mrgn-right-auto {
    margin-right: auto!important
}

.mrgn-left-auto {
    margin-left: auto!important
}

.mrgn-horizontal-auto {
    margin-right: auto!important;
    margin-left: auto!important
}

.mrgn-top-lv0 {
    margin-top: 0 !important;
}

.mrgn-bottom-lv0 {
    margin-bottom: 0 !important;
}

.mrgn-vertical-lv0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

.mrgn-left-lv0 {
    margin-left: 0 !important;
}

.mrgn-right-lv0 {
    margin-right: 0 !important;
}

.mrgn-horizontal-lv0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.mrgn-bottom-lv1 {
    margin-bottom: 8px !important;
}

.mrgn-bottom-lv2 {
    margin-bottom: 16px !important;
}

.mrgn-bottom-lv3 {
    margin-bottom: 24px !important;
}

.mrgn-bottom-lv4 {
    margin-bottom: 32px !important;
}

.mrgn-bottom-lv5 {
    margin-bottom: 40px !important;
}

.mrgn-bottom-lv6 {
    margin-bottom: 48px !important;
}

.mrgn-bottom-lv7 {
    margin-bottom: 56px !important;
}

.mrgn-bottom-lv8 {
    margin-bottom: 64px !important;
}

.mrgn-left-lv1 {
    margin-left: 8px !important;
}

.mrgn-left-lv2 {
    margin-left: 16px !important;
}

.mrgn-left-lv3 {
    margin-left: 24px !important;
}

.mrgn-left-lv4 {
    margin-left: 32px !important;
}

.mrgn-left-lv5 {
    margin-left: 40px !important;
}

.mrgn-left-lv6 {
    margin-left: 48px !important;
}

.mrgn-left-lv7 {
    margin-left: 56px !important;
}

.mrgn-left-lv8 {
    margin-left: 64px !important;
}

.mrgn-right-lv1 {
    margin-right: 8px !important;
}

.mrgn-right-lv2 {
    margin-right: 16px !important;
}

.mrgn-right-lv3 {
    margin-right: 24px !important;
}

.mrgn-right-lv4 {
    margin-right: 32px !important;
}

.mrgn-right-lv5 {
    margin-right: 40px !important;
}

.mrgn-right-lv6 {
    margin-right: 48px !important;
}

.mrgn-right-lv7 {
    margin-right: 56px !important;
}

.mrgn-right-lv8 {
    margin-right: 64px !important;
}

.pddng-lv8 {
    padding: 64px !important;
}

.pddng-lv7 {
    padding: 56px !important;
}

.pddng-lv6 {
    padding: 48px !important;
}

.pddng-lv5 {
    padding: 40px !important;
}

.pddng-lv4 {
    padding: 32px !important;
}

.pddng-lv3 {
    padding: 24px !important;
}

.pddng-lv2 {
    padding: 16px !important;
}

.pddng-lv1 {
    padding: 8px !important;
}

.pddng-vertical-lv0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.pddng-vertical-lv1 {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
}

.pddng-vertical-lv2 {
    padding-top: 16px !important;
    padding-bottom: 16px !important;
}

.pddng-vertical-lv3 {
    padding-top: 24px !important;
    padding-bottom: 24px !important;
}

.pddng-vertical-lv4 {
    padding-top: 32px !important;
    padding-bottom: 32px !important;
}

.pddng-vertical-lv5 {
    padding-top: 40px !important;
    padding-bottom: 40px !important;
}

.pddng-vertical-lv6 {
    padding-top: 48px !important;
    padding-bottom: 48px !important;
}

.pddng-top-lv0 {
    padding-top: 0 !important;
}

.pddng-bottom-lv0 {
    padding-bottom: 0 !important;
}

.pddng-left-lv0 {
    padding-left: 0 !important;
}

.pddng-right-lv0 {
    padding-right: 0 !important;
}

.pddng-right-lv1 {
    padding-right: 8px !important;
}

.pddng-right-lv2 {
    padding-right: 16px !important;
}

.pddng-right-lv3 {
    padding-right: 24px !important;
}

.pddng-right-lv4 {
    padding-right: 32px !important;
}

.pddng-horizontal-lv0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.pddng-horizontal-lv1 {
    padding-left: 8px !important;
    padding-right: 8px !important;
}

.pddng-horizontal-lv2 {
    padding-left: 16px !important;
    padding-right: 16px !important;
}

.pddng-horizontal-lv3 {
    padding-left: 24px !important;
    padding-right: 24px !important;
}

.contentStatusDirty:not(.jstree-disabled) {
    text-decoration: underline !important;
}

.contentStatusReferences:not(.jstree-default-selected):not(.jstree-disabled),
.contentStatusReferences.jstree-hovered {
    color: #2c40bb !important;
}
.contentStatusSuppresses:not(.jstree-default-selected):not(.jstree-disabled),
.contentStatusSuppresses.jstree-hovered {
    color: #bb0002 !important;
}

.contentStatusOutOfSync:not(.jstree-default-selected) {
    color: #bb0002 !important;
    /*text-decoration: line-through !important; */
    cursor: not-allowed;
}

.contentStatusOutOfSync.jstree-clicked {
    color: white !important;
    background: transparent !important;
}

.shiftContainer.shiftLeft:not(.noShiftSpacing) {
    margin-left: 32px !important;
}

.shiftFrameContainer.shiftFrameLeft {
    margin-left: 50px !important;
    left: 0px !important;
}

/* varTagAttrManager, smartTextTagAttrManager, placeholderTagAttrManager */
.tagAttrManagerLabelMiddle {
    vertical-align: middle;
    padding: 2px;
    white-space: nowrap;
}
.tagAttrManagerLabelTop {
    vertical-align: top;
    padding: 2px;
    white-space: nowrap;
}

.clipboardItemContent ul, .clipboardItemContent ol {
    margin-left: 15px !important;
}

.clipboardItemContent .renderedSampleValueContainer {
    display: none !important;
}