@import "../../_ux/scss/themes/messagepoint/theme";
@import "../../_ux/scss/helpers/functions";
@import "../../_ux/scss/helpers/variables";
@import "../../includes/node_modules/bootstrap/scss/mixins";

.content-editor-page {
  $font-size-base: 1rem;
  $line-height-base: 1.5rem;

  $fa-style-family: "Font Awesome 6 Pro";
  $fa-solid: 900;
  $fa-regular: 400;
  $icon-caret-down: "\f0d7";

  min-width: 80rem; // 1280px
  font-size: $font-size-base;
  font-family: $font-family-sans-serif;
  font-style: normal;

  .breadcrumb-container {
    font-size: .75rem;
    border-bottom: solid 1px $gray-100;
  }

  .breadcrumb-back {
    margin: 0 -.5rem 0 0 !important;
  }

  .breadcrumb-divider {
    margin: 0 .25rem 0 0;
  }

  .editorContentPanel {
    height: 100%;

    .mce-tinymce {
      position: static;
      width: 100% !important;
      height: 100% !important;
      border: 0;
      box-shadow: none;

      & > {
        .mce-stack-layout {
          height: 100%;
          display: flex;
          flex-direction: column;

          .mce-panel:not(.mce-statusbar) {
            border-top: 0;
            border-color: $gray-100;
          }

          .mce-edit-area {
            position: relative;
            flex-grow: 1;
            flex-shrink: 0;
            border: 0;
            padding: 0;
            margin: 0;
            display: flex;
            align-items: center;
            background: white !important;
            overflow: hidden;

            iframe {
              border: 0;
              width: 100% !important;
              height: 100% !important;
            }
          }
        }
      }
    }

    .mce-statusbar {
      border-color: $gray-100;
      border-style: solid;
      border-width: 1px 0 !important;
    }

    .mce-top-part::before {
      box-shadow: none;
    }

    .mce-content-body {
      overflow: hidden;
    }

    .contentEditor_EditorContainer {
      height: 100%;
    }

    .contentEditor_InnerContentContainer {
      border: 0;
      background: transparent;
    }

    .mce-flow-layout-item {
      padding: 0;
      margin: 0;

      & + & {
        margin-left: 1rem;
      }
    }

    .mce-top-part.mce-container.mce-stack-layout-item.mce-first {
      padding: 1rem 0;
      border-bottom: solid 1px $gray-100;
    }

    .mce-toolbar-grp.mce-last {
      width: fit-content;
      margin: 0 1.5rem;
      padding: 0 0.25rem;
      background: $gray-000;
      border-radius: 100px;
    }

    .mce-toolbar .mce-btn-group {
      padding: 0;
      margin: 0;
    }

    .mce-splitbtn .mce-open {
      border: 0 !important;
      padding-left: 0;
    }

    .mce-btn-group .mce-btn {
      display: inline-flex;
      align-items: center;
      border: 0;
      margin: 0;
      background: transparent;
    }

    .mce-btn button {
      display: flex;
      align-items: center;
      height: 2rem;
      padding: .25rem .5rem;
    }

    .mce-colorbutton .mce-preview {
      position: absolute;
      left: .125rem !important;
      top: .125rem !important;
      transform: translate(50%, 50%);
      width: .875rem;
      height: .875rem !important;
      margin: 0;
      padding: 0;
      border-radius: .125rem;
    }

    .mce-menubar {
      margin: 0 0 .5rem 0;
      padding: 0 1rem;
      border: 0;
    }

    .mce-menubar .mce-menubtn {
      height: 2rem;
      border-radius: $border-radius;
    }

    .mce-menubar .mce-menubtn button {
      display: flex;
      align-items: center;
      height: 2rem;
      font-style: normal;
      font-size: $font-size-xs;
      line-height: $line-height-base;
      text-transform: uppercase;
    }

    .mce-menubar .mce-menubtn button span {
      font-weight: $font-weight-bold;
      color: $gray-700;
    }

    .mce-btn .mce-txt {
      & + .mce-caret {
        margin: 0 0 0 .5rem;
      }
    }

    .mce-btn.mce-active button, .mce-btn.mce-active:hover button, .mce-btn.mce-active i, .mce-btn.mce-active:hover i {
      color: $gray-700;
    }

    .mce-btn i {
      text-shadow: none;
    }

    .mce-listbox .mce-caret {
      position: relative;
      top: unset;
      bottom: unset;
      right: unset;
      left: unset;
    }

    .mce-caret {
      position: relative;
      right: 0;
      left: 0;
      border: 0;
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      margin-right: 0 !important;
      padding: 0;
      width: .5rem;
      height: .5rem;
      content: "";
      color: $gray-700;
      text-align: center;

      &::before {
        position: absolute;
        left: 0;
        width: 100%;
        height: 100%;
        font-family: $fa-style-family;
        font-weight: $fa-solid;
        font-size: .5rem;
        font-style: normal;
        color: inherit;
        content: $icon-caret-down;
      }
    }

    .mce-ico {
      font-family: $fa-style-family;
      font-weight: $fa-solid;
      font-style: normal;
      font-size: $font-size-xs;
      text-align: center;
    }

    .mce-i-bold::before {
      content: "\f032";
    }

    .mce-i-italic::before {
      content: "\f033";
    }

    .mce-i-underline::before {
      content: "\f0cd";
    }

    .mce-i-alignleft::before {
      content: "\f036";
    }

    .mce-i-aligncenter::before {
      content: "\f037";
    }

    .mce-i-alignright::before {
      content: "\f038";
    }

    .mce-i-alignjustify::before {
      content: "\f039";
    }

    .mce-i-outdent::before {
      content: "\f03b";
    }

    .mce-i-indent::before {
      content: "\f03c";
    }

    .mce-i-bullist::before {
      content: "\f0ca";
    }

    .mce-i-numlist::before {
      content: "\f0cb";
    }

    .mce-i-markeditable {
      & {
        font-size: $font-size-sm;
        font-weight: $fa-regular;
      }

      &::before {
        content: "\f14b";
      }
    }

    .mce-i-inserteditpoint {
      & {
        font-size: $font-size-sm;
        font-weight: $fa-regular;
      }

      &::before {
        content: "\f055";
      }
    }
  }

  .footer-main .footer-content {
    margin: 0;
  }

  .nav-column {
    flex-direction: column;
    font-size: 1.25rem;

    .nav-toggle {
      border-radius: $border-radius;

      &:hover, &:focus {
        background-color: $gray-100;
      }
    }

    .nav-link.active {
      background: $gray-100;
    }
  }
}

.mceHorizontalRuler {
  position: relative;
  width: 100%;
  height: 1rem;
  border-left: solid 1px $gray-200;
}

.mceVerticalRuler {
  position: relative;
  width: 1rem;
  min-height: 100%;
  border-top: solid 1px $gray-200;
}

.mce-vertical-gutter-guide {
  position: absolute;
  border-left: dashed 1px $gray-200;
  min-height: 100% !important;
}

.ruler-set-number-list {
  display: flex;
  align-items: flex-start;
  overflow: hidden;
  width: 100%;
  height: 100%;
  user-select: none;
}

.ruler-set-number {
  box-sizing: border-box;
  flex-shrink: 0;
  font-size: .5rem;
  font-weight: $font-weight-bold;
  color: $gray-700;
}

.horizontal-ruler-container {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  min-width: 100%;
  padding: 1rem 2rem 0 4rem;
  border-bottom: solid 1px $gray-200;
  background: $white;
  overflow: hidden;

  .ruler-set-number {
    width: 100px;
    padding: 0 .25rem;
    cursor: default;
  }

  &.ruler-container-cm {
    .ruler-set-number {
      width: 40px;
    }
  }
}

.vertical-ruler-container {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100%;
  padding: 4rem 0 2rem 1rem;
  border-right: solid 1px $gray-200;
  background: $white;
  overflow: hidden;

  .ruler-set-number-list {
    flex-direction: column;
  }

  .ruler-set-number {
    height: 100px;
    padding: .25rem 0;
    cursor: default;
  }

  &.ruler-container-cm {
    .ruler-set-number {
      height: 40px;
      padding: .25rem 0;
    }
  }
}
