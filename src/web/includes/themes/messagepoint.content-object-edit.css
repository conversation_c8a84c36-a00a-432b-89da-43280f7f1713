.content-object-edit-page .breadcrumb-container {
  font-size: 0.75rem;
  border-bottom: solid 1px #e1e1e1;
}
.content-object-edit-page .breadcrumb-back {
  margin: 0 -0.5rem 0 0 !important;
}
.content-object-edit-page .breadcrumb-divider {
  margin: 0 0.25rem 0 0;
}
.content-object-edit-page .footer-main .footer-content {
  margin: 0;
}
.content-object-edit-page .nav-column {
  flex-direction: column;
  font-size: 1.25rem;
}
.content-object-edit-page .nav-column .nav-toggle {
  border-radius: 0.25rem;
}
.content-object-edit-page .nav-column .nav-toggle:hover, .content-object-edit-page .nav-column .nav-toggle:focus {
  background-color: #e1e1e1;
}
.content-object-edit-page .nav-column .nav-link.active {
  background: #e1e1e1;
}

/*# sourceMappingURL=messagepoint.content-object-edit.css.map */
