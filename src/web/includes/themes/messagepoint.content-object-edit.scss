@import "../../_ux/scss/themes/messagepoint/theme";
@import "../../_ux/scss/helpers/functions";
@import "../../_ux/scss/helpers/variables";
@import "../../includes/node_modules/bootstrap/scss/mixins";

.content-object-edit-page {
  .breadcrumb-container {
    font-size: .75rem;
    border-bottom: solid 1px $gray-100;
  }

  .breadcrumb-back {
    margin: 0 -.5rem 0 0 !important;
  }

  .breadcrumb-divider {
    margin: 0 .25rem 0 0;
  }

  .footer-main .footer-content {
    margin: 0;
  }

  .nav-column {
    flex-direction: column;
    font-size: 1.25rem;

    .nav-toggle {
      border-radius: $border-radius;

      &:hover, &:focus {
        background-color: $gray-100;
      }
    }

    .nav-link.active {
      background: $gray-100;
    }
  }
}

