.iFramePopupHeaderContainer,
.ibutton-container,
.actionPopupHeader,
.ibutton-handle-right,
.ibutton-handle,
div.ibutton-label-on,
div.ibutton-label-off {
    background: #121d57;
}

div.actionOptionHov:not(.actionOptionSelected) {
    background: #121d57 !important;
}

.ibutton-container,
.inputComponent .adaptiveLabel:hover,
.inputComponent .adaptiveLabel:focus,
.searchInput:hover,
.searchInput:focus,
.actionInput_searchFill_focus,
.actionInput_searchRight_focus,
.actionInput_searchLeft_focus {
    border-color: #121d57 !important;
}

.actionInput_searchFill_focus {
    border-top: solid 1px #121d57;
    border-bottom: solid 1px #121d57;
}

.workflowTabSelected {
    background: #444444;
}

.categoryDropMenu .menuLinkContainer > a:hover {
    background: #444444;
    color: #fff;
}

.paging_full_numbers .fg-button.fg-current {
    background: #dadada;
    color: #444444;
}

.contextMenuHeader {
    color: #444444;
}

.contextMenu li:not(.contextMenuCategory) a:not(.disabled).hover,
.contextMenu li:not(.contextMenuCategory) a:not(.disabled):hover {
    background: #6e6a6f;
    color: #FFF;
}

.contextMenu .contextMenuItem_selected a,
.contextMenu .contextMenuItem_selected .itemText {
    color: #6e6a6f;
}

.contextMenu .contextMenuItem:hover {
    background: #6e6a6f;
}


.actionBtn.actionBtnHighlighted, .actionBtn_disabled.actionBtnHighlighted {
    background: #6e6a6f;
    border-color: #6e6a6f;
    color: #fff;
}

.actionBtn.actionBtnHighlighted.actionBtn_toggleSelectHighlight,
.actionBtn.actionBtnHighlighted:active {
    background: #9069a5;
    border-color: #a2a2a2;
}

.actionBtn_disabled.actionBtnHighlighted .actionBtnText {
    color: #dadada;
}

.actionBtn_hov.actionBtnHighlighted{
    background: #858585;
    border-color: #858585;
    color: #fff;
}

.actionBtn_selected.actionBtnHighlighted,
.actionBtn_toggleSelect.actionBtnHighlighted {
    background: #a2a2a2;
    border-color: #a2a2a2;
    color: #dadada;
}

.actionBtn_disabled.actionBtnHighlighted .fa {
    color: #dadada;
}

.jstree-proton .jstree-wholerow-clicked {
    background: #6e6a6f !important;
}

.jstree-proton .jstree-hovered {
    color: #fff !important;
}