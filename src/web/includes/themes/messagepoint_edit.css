/*   middleColWidth = 967   */
/*   ieAdjustment = -6.0   */


#containerContent {
									/* width: <%=(middleColWidth) %>px; */
									/* width: <%=(967) %>px; */
	width: 967px;
	padding-left: 0px;
}
.formBorderlessTable, .contentTableLg, .paginationTable {
    width: 939px;									/* contentTable (953) - padding (14) ?*/
}
.bodyIframe .formBorderlessTable {
	width: 100%;
	padding: 4px 8px;
}

#containerSubTabsContent {
	width: 930px;
}
* html #containerSubTabsContent, .formSubtabTable, .genericInFormST1 {
    width: 920px;
}
.graphicContainer {
	width: 884px;
}


/* ----- ----- ----- ----- ----- */
/* ----- ----- ----- ----- ----- */
/* All below for IE */

* html .contentTable, * html .contentTableLg, * html .paginationTable {
												    /* width: =(middleColWidth -(spacingFromContainerEdge * 1.5))px; */
												    /* width: =(967 -(12 * 1.5))px; */
												    /* width: =(967 -(18) - 2px; */
    width: 947px;
}
* html .headerForGenericL1, * html .genericL1 {
									/*TODO width: =(middleColWidth + ieAdjustment )px;*/
									/*TODO width: =(967 + (-6) )px;*/
	width: 961px;
}
* html .graphicContainer {
									/*=(middleColWidth - (spacingFromContainerEdge * 4.5) + ieAdjustment )*/
									/*=(967 - (12 * 4.5) + (-6) )*/
									/*=(967 - (54) + (-6) )*/
    width: 907;
}
* html .formSubtabTable, * html .genericInFormST1, * html {
								    /*(middleColWidth - (spacingFromContainerEdge * 3.5) + 2 + ieAdjustment ) */
								    /*(967 - (12 * 3.5) + 2 + (-6) ) */
								    /*(967 - (42) + 2 + (-6) ) */
    width: 921;
}
* html .contentDataDivWidth {
	width: 960px;
}
* html .formBorderlessTable {
	width: 921px;
}