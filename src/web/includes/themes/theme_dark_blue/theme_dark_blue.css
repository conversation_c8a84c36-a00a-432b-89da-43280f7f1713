.actionsBarContainer {
    border: 0;
    background-color: #606060;
}

.actionsBarContainer .touchpointWidgetToggle,
.actionBarHeaderLabel,
.actionsBarContainer .actionBtn_disabled div.actionBtnText,
.actionsBarContainer .actionSelectMenuText,
.actionsBarContainer .actionBtn .actionBtnText,
.actionsBarContainer .actionSelectMenuText,
.actionsBarContainer .actionBtn_hov .actionBtnText,
.actionsBarContainer .actionSelectMenuArrow,
.actionsBarContainer .actionSelectMenuArrow_hov,
.actionsBarContainer .actionSelectMenuArrow_selected,
.actionsBarContainer .actionBtn,
.actionsBarContainer .actionBtn_hov,
.actionsBarContainer .actionBtn_disabled,
.actionsBarContainer .actionBtn_selected,
.actionsBarContainer .actionBtn_selected .actionBtnText{
    color: #fff;
}

.actionsBarContainer .touchpointWidgetToggle:hover,
.actionsBarContainer .touchpointWidgetToggle:focus,
.actionsBarContainer .actionBtn_hov,
.actionsBarContainer .actionBtn_selected,
.actionsBarContainer .actionSelectMenuArrow_hov,
.actionsBarContainer .actionSelectMenuArrow_selected,
.actionBarButtonsContainer > .actionBtn:hover {
    background-color: rgba(0, 0, 0, 0.2);
}

.actionsBarContainer .actionBarButtonsContainer .actionBtn,
.actionsBarContainer .actionBarButtonsContainer .actionBtn_hov,
.actionsBarContainer .actionBarButtonsContainer .actionBtn_selected,
.actionsBarContainer .actionBarButtonsContainer .actionBtn_disabled,
.actionBarButtonsContainer > .actionBtn:hover,
.actionsBarContainer .touchpointWidgetToggle {
    border-color: rgba(0, 0, 0, 0.2);
}