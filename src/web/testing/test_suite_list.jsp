<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>
<%@page import="com.prinova.messagepoint.util.ApplicationUtil" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.testing" extendedScripts="true">
        <msgpt:Script src="includes/javascript/popupActions.js"/>
        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>
        <msgpt:CalendarIncludes/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/popupBoundary/jquery.popupBoundary.js"/>
        <msgpt:Script src="includes/javascript/jQueryPlugins/actionStatusPolling/jquery.actionStatusPolling.js"/>
        <msgpt:Script>
            <script>

                var iFramePopup_fullFrameAttr_cust = iFramePopup_fullFrameAttr;
                iFramePopup_fullFrameAttr_cust.width = 800;

                var $updateBtn, $deleteBtn, $reRunBtn, $reRunBtn2, $sendBtn;

                $(function () {

                    setupActionPopupVisibilityObserver();

                    $("#actionMenu").styleActionElement();

                    $updateBtn = $('#updateBtn');
                    $deleteBtn = $('#deleteBtn');
                    $reRunBtn = $('#reRunBtn');
                    $reRunBtn2 = $('#reRunBtn2');
                    $sendBtn = $('#sendBtn');

                    validateActionReq();

                    $('#addTestSuiteBtn').iFramePopup($.extend({
                        src: context + "/testing/test_suite_edit.form",
                        appliedParams: {tk: "${param.tk}"},
                        beforePopup: function (inst) {
                            if ($('#addTestSuiteBtn').hasClass('blueBtnDisabled'))
                                return false;
                        },
                        beforePopupClose: function () {
                            location.reload();
                        },
                        onSave: function () {
                        }
                    }, iFramePopup_fullFrameAttr_cust));

                    // Test result polling init
                    $("[id^='testStatusPollingContainer_']").each(function () {
                        if ($(this).attr('pollingInit') == undefined || $(this).attr('pollingInit') != "true") {
                            $(this).attr('pollingInit', 'true');
                            pollForTestResult(parseId(this));
                        }
                    });

                    // Test Suite status polling init
                    initTestSuiteStatusPoll();
                });

                // *********  LIST TABLE FUNCTIONS: START  *********
                function pollForTestResult(testId) {

                    if ($('#testStatusPollingContainer_' + testId).length == 0)
                        return;

                    var stampDate = new Date();
                    $.ajax({
                        type: "GET",
                        url: context + "/getTestResultStatus.form?testSuiteId=" + testId + "&tk=" + getParam('tk') + "&cacheStamp=" + stampDate.getTime(),
                        dataType: "json",
                        success: function (data) {
                            processTestPollResult(data);
                        }
                    });

                    window.setTimeout(function () {
                        pollForTestResult(testId)
                    }, 10000);
                }

                function processTestPollResult(data) {

                    var pollingStatusDiv = $('#testStatusPollingContainer_' + data.test_suite_id);
                    var testStatusContainer = $('#testStatusPollingContainer_' + data.test_suite_id).closest('td');
                    var testResultContainer = $('#testResultContainer_' + data.test_suite_id).closest('td');

                    $(testStatusContainer).html(data.status);

                    var $jobId = $('[data-testsuiteid="' + data.test_suite_id + '"]');

                    // Check whether the job number has been appended to the test name
                    if (!$jobId.is('.has-job') && data.job_id) {
                        $jobId.text(' # ' + data.job_id).addClass('has-job');
                    }
                }

                function pollForTestSuiteStatus(deliveryEventId) {
                    if ( $('#testSuiteStatusPollingContainer_'+deliveryEventId).length == 0 )
                        return;

                    var stampDate = new Date();
                    $.ajax({
                        type: "GET",
                        url: context+"/getDeliveryEventStatus.form?deliveryEventId=" + deliveryEventId + "&tk=" + getParam('tk') + "&cacheStamp="+stampDate.getTime(),
                        dataType: "json",
                        success: function(data) {
                            processTestSuiteStatusPollResult(data);
                        }
                    });

                    window.setTimeout( function() { pollForTestSuiteStatus(deliveryEventId) }, 5000 );
                }

                function processTestSuiteStatusPollResult(data) {
                    var testSuiteStatusPollingContainer = $('#testSuiteStatusPollingContainer_' + data.deliveryEventId);

                    if(data.status){
                        testSuiteStatusPollingContainer.text(data.status);
                    }
                }

                function iFrameView(path, eleId, e) {
                    if (e != undefined) {
                        if (!e.shiftKey && !e.ctrlKey && !e.metaKey) {
                            $("#" + eleId).iFramePopup($.extend({
                                src: path,
                                displayOnInit: true,
                                id: "testFrame",
                                appliedParams: {tk: "${param.tk}"},
                                beforePopupClose: function () {
                                    rebuildListTable();
                                }
                            }, iFramePopup_fullFrameAttr_cust));
                        }
                        e.preventDefault ? e.stopPropagation() : e.returnValue = false;
                    }
                }

                function toggleFilter(select) {
                    rebuildListTable();
                }

                function rebuildListTable() {
                    $('#testSuiteList').DataTable().ajax.reload(null, true);
                }

                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    obj.columns = [
                        {
                            columnMap: 'name',
                            columnName: client_messages.text.name,
                            sort: true,
                            width: '40%',
                            colVisToggle: false
                        },
                        {
                            columnMap: 'job',
                            columnName: client_messages.text.job,
                            sort: false,
                            width: '10%',
                            colVisToggle: false
                        },
                        {
                            columnMap: 'bundle',
                            columnName: client_messages.text.staged.bundle.status,
                            sort: true,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'status',
                            columnName: client_messages.text.status,
                            sort: true,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'date',
                            columnName: client_messages.text.last_run_date,
                            sort: true,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'scheduled',
                            columnName: client_messages.text.next_scheduled_date,
                            sort: false,
                            width: '10%',
                            colVisToggle: true
                        },
                        {
                            columnMap: 'user',
                            columnName: client_messages.text.user,
                            sort: true,
                            width: '10%',
                            colVisToggle: true
                        }
                    ];
                    return obj;
                }

                function getAsyncExtParams() {
                    var stampDate = new Date();
                    var obj = [
                        {"name": "listTableType", "value": "39"},
                        {"name": "displayMode", "value": "full"},
                        {"name": "documentId", "value": getParam('documentId') != "" ? getParam('documentId') : -1},
                        {
                            "name": "collectionId",
                            "value": getParam('collectionId') != "" ? getParam('collectionId') : -1
                        },
                        {"name": "testSuiteAssignmentFilterId", "value": $('#testSuiteListAssignmentFilter').val()},
                        {"name": "testsStatusFilterId", "value": $('#testSuiteListStatusFilter').val()},
                        {"name": "globalContext", "value": $("#globalContextContainer").is('.globalContextEnabled')},
                        {"name": "cacheStamp", "value": stampDate.getTime()}
                    ];
                    return obj;
                }

                function initTestSuiteStatusPoll(){
                    $("[id^='testSuiteStatusPollingContainer_']").each( function() {
                        if ( $(this).attr('testSuiteStatusPollingInit') == undefined || $(this).attr('testSuiteStatusPollingInit') != "true" ) {
                            $(this).attr('testSuiteStatusPollingInit','true');
                            pollForTestSuiteStatus(parseId(this));
                        }
                    });
                }

                function setupActionPopupVisibilityObserver(){
                    var observer = new MutationObserver(onActionPopupVisible);
                    observer.observe(document.querySelector('div#actionPopup'), {
                        attributes: true,
                        attributeFilter: ["style"],
                        attributeOldValue: false
                    });
                }

                function onActionPopupVisible(mutations) {
                    var stop = false;
                    mutations.forEach(function(mutation) {
                        if(!stop && mutation.attributeName === 'style'){
                            if(mutation.target.style.display !== 'none'){
                                setDefaultCheckboxForPopup();
                                stop = true;
                            }
                        }
                    });
                }

                function setDefaultCheckboxForPopup(){
                    $('#rerunTestSuiteCheckbox').prop("checked",true);
                    changeRerunChecks($('#rerunTestSuiteCheckbox'));
                }

                function postListDrawCallback(nTable) {
                    // Proof polling init
                    $(nTable).find("[id^='testStatusPollingContainer_']").each(function () {
                        if ($(this).attr('pollingInit') == undefined || $(this).attr('pollingInit') != "true") {
                            $(this).attr('pollingInit', 'true');
                            pollForTestResult(parseId(this));
                        }
                    });

                    initTestSuiteStatusPoll();
                }

                function postListRenderFlagInjection(oObj) {
                    var binding = oObj.aData.binding;
                    var text = oObj.aData.name;

                    //Selection check select
                    text += binding;

                    //Selection permission flags
                    if (oObj.aData.flags.canUpdate)
                        text += "<input type='hidden' id='canUpdate_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canDelete)
                        text += "<input type='hidden' id='canDelete_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.stateChanged)
                        text += "<input type='hidden' id='stateChanged_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canReuseBundle)
                        text += "<input type='hidden' id='canReuseBundle_" + oObj.aData.dt_RowId + "' />";
                    if (oObj.aData.flags.canRerun)
                        text += "<input type='hidden' id='canRerun_" + oObj.aData.dt_RowId + "' />";

                    return text;
                }

                // *********  LIST TABLE FUNCTIONS: END  *********

                // List actions: Edit
                function iFrameAction(actionId) {
                    var testId;
                    $("input[id^='listItemCheck_']:checked").each(function () {
                        testId = this.id.replace('listItemCheck_', '');
                    });

                    if (actionId == '1' || actionId == '6') {
                        $(actionId == '1' ? '#actioniFrame_' + actionId : '#actionOption_' + actionId).iFramePopup($.extend({
                            src: context + "/testing/test_suite_edit.form",
                            displayOnInit: true,
                            id: "testFrame",
                            appliedParams: {'tk': "${param.tk}", 'testSuiteId': testId},
                            beforePopupClose: function () {
                                rebuildListTable();
                            }
                        }, iFramePopup_fullFrameAttr_cust));
                    }

                    actionCancel();
                }

                // Test list validation
                function validateActionReq() {
                    var singleSelect = true;
                    var canUpdate = true;
                    var canDelete = true;
                    var stateChanged = false;
                    var canReuseBundle = true;
                    var editTestPerm = exists('editTestPerm');
                    var canRerun = true;

                    // Resolve selection flags
                    if ($("input[id^='listItemCheck_']:checked").length != 1) {
                        singleSelect = false;
                    }
                    $("input[id^='listItemCheck_']:checked").each(
                        function () {
                            var testId = this.id.replace('listItemCheck_', '');
                            if (exists('canUpdate_' + testId)) {
                                canUpdate = true;
                            }
                            if (!exists('canDelete_' + testId)) {
                                canDelete = false;
                            }
                            if (exists('stateChanged_' + testId)) {
                                stateChanged = true;
                            }
                            if (!exists('canReuseBundle_' + testId)) {
                                canReuseBundle = false;
                            }
                            if (!exists('canRerun_' + testId)) {
                                canRerun = false;
                            }
                        }
                    );

                    //Disable all context menu entries
                    $('ul.contextMenu').find("a[id^='actionOption']").addClass('disabled');
                    $('ul.contextMenu').find("a[id^='actioniFrame']").addClass('disabled');
                    common.disableElement($updateBtn);
                    common.disableElement($deleteBtn);
                    common.disableElement($reRunBtn);
                    common.disableElement($reRunBtn2);
                    if (${showSendToQaModule}) {
                        common.disableElement($sendBtn);
                    }

                    if ($("input[id^='listItemCheck_']:checked").length > 0) {
                        if (singleSelect) {
                            if (canUpdate) {
                                $('a#actioniFrame_1').removeClass('disabled');
                                common.enableElement($updateBtn);
                            }
                        }
                        if (canUpdate) {
                            if (!stateChanged) {	// State does not change
                                if(canRerun){
                                    $('a#actionOption_3').removeClass('disabled');
                                    common.enableElement($reRunBtn);
                                    $reRunBtn.removeClass('d-none');
                                }
                                $('a#actionOption_6').hide();
                                $reRunBtn2.addClass('d-none');
                            } else {				// State chagned
                                $reRunBtn.addClass('d-none');
                                $('a#actionOption_3').hide();
                                if(canRerun){
                                    $('a#actionOption_6').removeClass('disabled');
                                    common.enableElement($reRunBtn2);
                                    $reRunBtn2.removeClass('d-none');
                                }
                            }
                        }
                        if (canDelete) {
                            $('a#actionOption_2').removeClass('disabled');
                            common.enableElement($deleteBtn);
                        }

                        if (${showSendToQaModule and enableSendToQaModule}) {
                            $('a#actionOption_8').removeClass('disabled');
                            common.enableElement($sendBtn);
                        }

                        if(!canReuseBundle){
                            $('input#rerunAllTestScenariosCheckbox').attr('disabled','disabled');
                        }
                        else{
                            $('input#rerunAllTestScenariosCheckbox').removeAttr('disabled');
                        }
                    }
                }

                // Retrieve selected ids seperated by ','
                function getSelectedIds() {
                    var selectedIds = '';
                    var selectedCount = $("input[id^='listItemCheck']:checked").length;
                    var count = 0;
                    $("input[id^='listItemCheck']:checked").each(
                        function () {
                            selectedIds += this.id.replace('listItemCheck_', '');
                            count++;
                            if (count != selectedCount)
                                selectedIds += '_';
                        }
                    );
                    return selectedIds;
                }

                function changeRerunChecks(ele) {
                    if ($(ele).is(':checked')) {
                        $('input.rerunCheckbox').each(function () {
                            if ($(this).attr('id') != $(ele).attr('id')) {
                                $(this).prop('checked', false);
                            }
                        });
                    }

                    if ($('input.rerunCheckbox:checked').length != 1) {
                        $('#continueBtnEnabled').hide();
                        $('#continueBtnDisabled').show();
                    } else {
                        $('#continueBtnEnabled').show();
                        $('#continueBtnDisabled').hide();
                    }
                }

            </script>
        </msgpt:Script>
    </msgpt:HeaderNew>
    <msgpt:BodyNew>
        <!-- FLAGS AND PERMISSIONS -->
        <msgpt:IfAuthGranted authority="ROLE_TEST_EDIT">
            <input type="hidden" id="editTestPerm">
        </msgpt:IfAuthGranted>
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_TESTING %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_TESTING %>"/>
        <msgpt:ContextBarNew languageContextApplied="false" collectionContextApplied="true" globalContextApplied="true"
                             channelContextApplied="${not isGlobalContext}"/>
        <msgpt:LowerContainer fullPanel="true">
            <msgpt:DropDownMenu/>
            <msgpt:ContentPanel>
                <c:if test="${not trashTpContext }">
                    <form:form method="post" modelAttribute="command">
                        <form:errors path="*">
                            <msgpt:Information errorMsgs="${messages}" type="error"/>
                        </form:errors>
                        <msgpt:IfAuthGranted authority="ROLE_TEST_EDIT">
                            <c:choose>
                                <c:when test="${isGlobalContext}">
                                    <div id="infoMsg_singleSelectorVariant" class="alert alert-info" role="alert">
                                        <strong class="mr-2">
                                            <i class="fas fa-info-circle fa-lg mr-2"
                                               aria-hidden="true"></i><fmtSpring:message
                                                code="page.label.info"/>:
                                        </strong>
                                        <fmtSpring:message
                                                code="page.text.select.touchpoint.context.to.add.test.suite"/>
                                    </div>
                                </c:when>
                            </c:choose>
                        </msgpt:IfAuthGranted>
                        <h1 class="h4 d-flex justify-content-start align-items-center pb-2 mb-4">
                            <fmtSpring:message code="page.label.suites"/>
                            <span class="ml-3 pl-3 border-left">
                                <a class="d-flex btn btn-primary btn-sm"
                                   href="javascript:javascriptHref('testing_list.form');">
                                    <fmtSpring:message code="page.label.tests"/>
                                </a>
                            </span>
                        </h1>
                        <div class="box-shadow-4 rounded bg-white p-4">
                            <div class="px-2 py-1">
                                <div class="mb-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <form:hidden path="actionValue" id="actionElement"/>
                                        <msgpt:IfAuthGranted authority="ROLE_TEST_EDIT">
                                            <div class="mr-3">
                                                <button id="addTestSuiteBtn" class="btn btn-primary"
                                                        type="button" ${isGlobalContext ? 'disabled' : '' }>
                                                    <i class="far fa-plus-circle mr-2"
                                                       aria-hidden="true"></i>
                                                    <c:out value='${msgpt:getMessage("page.label.add")}'/>
                                                </button>
                                            </div>
                                        </msgpt:IfAuthGranted>
                                        <div class="btn-group border-separate mr-auto" role="group"
                                             aria-label="${msgpt:getMessage("page.label.actions")}">
                                            <button id="updateBtn" type="button"
                                                    class="btn btn-dark"
                                                    onclick="iFrameAction(1);" disabled>
                                                <i class="far fa-edit mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="action.button.label.update"/>
                                            </button>
                                            <button id="reRunBtn" type="button"
                                                    class="btn btn-dark"
                                                    onclick="actionSelected(3);" disabled>
                                                <i class="far fa-repeat mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="page.label.rerun"/>
                                            </button>
                                            <button id="reRunBtn2" type="button" class="btn btn-dark d-none"
                                                    onclick="actionSelected(6);" disabled>
                                                <i class="far fa-repeat mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="page.label.rerun"/>
                                            </button>
                                            <button id="deleteBtn" type="button" class="btn btn-dark"
                                                    onclick="actionSelected(2);" disabled>
                                                <i class="far fa-trash-alt mr-2" aria-hidden="true"></i>
                                                <fmtSpring:message code="page.label.delete"/>
                                            </button>
                                            <c:if test="${showSendToQaModule}">
                                                <button id="sendBtn" type="button" class="btn btn-dark"
                                                        onclick="actionSelected(8);" disabled>
                                                    <i class="far fa-cloud-upload-alt mr-2" aria-hidden="true"></i>
                                                    <fmtSpring:message code="page.label.upload.blue.relay"/>
                                                </button>
                                            </c:if>
                                        </div>
                                        <div class="mr-3 colVisToggleContainer" data-toggle="tooltip"
                                             title="${msgpt:getMessage('client_messages.label.toggle_columns')}">
                                        </div>
                                        <div class="form-group position-relative d-inline-block m-0">
                                            <label for="listSearchInput"
                                                   class="sr-only"><fmtSpring:message
                                                    code="page.label.search"/></label>
                                            <i class="far fa-search ml-3 position-absolute text-dark top-30"
                                               style="z-index: 1;" aria-hidden="true"></i>
                                            <msgpt:InputFilter type="description">
                                                <input id="listSearchInput" type="text" size="25"
                                                       class="form-control bg-lightest has-control-l border-0"
                                                       placeholder="${msgpt:getMessage('page.label.search')}"/>
                                            </msgpt:InputFilter>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center pt-1">
                                                    <span class="text-dark mr-1" id="filter">
                                                        <fmtSpring:message code="page.label.filter"/>:
                                                    </span>
                                        <div class="mx-2">
                                            <select id="testSuiteListAssignmentFilter"
                                                    class="complex-dropdown-select persistedValue"
                                                    aria-labelledby="filter"
                                                    data-toggle="complex-dropdown"
                                                    data-menu-class="dropdown-custom mt-2"
                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                    onchange="toggleFilter(this)">
                                                <c:forEach items="${testAssignmentFilterTypes}"
                                                           var="currentFilter">
                                                    <option id="testSuiteListAssignmentFilterOption_${currentFilter.id}"
                                                            value="${currentFilter.id}"><fmtSpring:message
                                                            code="${currentFilter.displayMessageCode}"/></option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                        <span class="text-dark mx-1" id="test-status">
                                                        <fmtSpring:message code="page.text.tests.which.are"/>
                                                    </span>
                                        <div class="ml-2">
                                            <select id="testSuiteListStatusFilter"
                                                    class="complex-dropdown-select persistedValue"
                                                    aria-labelledby="test-status"
                                                    data-toggle="complex-dropdown"
                                                    data-menu-class="dropdown-custom mt-2"
                                                    data-dropdown-class="btn-link btn-link-inline font-weight-bold"
                                                    onchange="toggleFilter(this)">
                                                <option id="testSuiteListStatusFilter_0" value="0">
                                                    <fmtSpring:message
                                                            code="page.label.any.status"/></option>
                                                <c:forEach items="${testStatusFilterTypes}"
                                                           var="currentFilter">
                                                    <option id="testSuiteListStatusFilter_${currentFilter.id}"
                                                            value="${currentFilter.id}"><fmtSpring:message
                                                            code="${currentFilter.displayMessageCode}"/></option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white">
                                    <msgpt:DataTable id="testSuiteList" listHeader="page.label.tests"
                                                     async="true"
                                                     columnReorder="true" numUnreorderableCols="2"
                                                     columnVisibility="true"
                                                     drillDown="false" multiSelect="true"
                                                     searchFilter="true">
                                    </msgpt:DataTable>
                                </div>
                            </div>
                        </div>

                        <!-- POPUP DATA -->
                        <div id="actionSpecs" style="display: none;">
                            <!-- ACTIONS POPUP DATA -->
                            <div id="actionSpec_2" type="simpleConfirm" submitId="2"> <!-- Delete test suite -->
                                <div id="actionTitle_2"><fmtSpring:message
                                        code="page.label.confirm.delete.test.suite"/></div>
                                <div id="actionInfo_2"><fmtSpring:message code="page.text.delete.test.suites"/></div>
                            </div>
                            <div id="actionSpec_3" submitId="3"> <!-- Rerun test suite -->
                                <div id="actionTitle_3"><fmtSpring:message
                                        code="page.label.confirm.rerun.test.suite"/></div>
                                <div id="actionInfo_3"><fmtSpring:message code="page.text.rerun.test.suites"/></div>
                                <div id="actionRerunOptions_3"></div>
                            </div>
                            <div id="actionSpec_6"> <!-- Test state changed, redirect confirmation -->
                                <div id="actionTitle_6"><fmtSpring:message code="page.label.update.test"/></div>
                                <div id="actionInfo_6"><fmtSpring:message
                                        code="page.text.working.copies.associated.with.test.have.changed"/></div>
                                <div id="actionCustomButtons_6"></div>
                            </div>
                            <div id="actionSpec_8" type="simpleConfirm" submitId="8">
                                <div id="actionTitle_8"><fmtSpring:message
                                        code="page.label.confirm.upload.blue.relay"/></div>
                                <div id="actionInfo_8"><fmtSpring:message
                                        code="page.text.upload.suite.blue.relay"/></div>
                            </div>
                        </div>

                        <!-- POPUP INTERFACE -->
                        <msgpt:Popup id="actionPopup" theme="minimal">
                            <div id="actionPopupInfoFrame">
                                <div id="actionPopupInfo">&nbsp;</div>
                            </div>
                            <div id="actionPopupRerunOptions" style="padding: 2px 8px 6px 8px;" align="center">
                                <table class="innerContentTable" cellspacing="0" cellpadding="0" border="0">
                                    <tr>
                                        <td style="padding: 2px 0px; vertical-align: middle; color: #444; white-space: nowrap;">
                                            <fmtSpring:message code="page.label.rerun.test.suite"/>:
                                        </td>
                                        <td style="padding: 2px 0px; vertical-align: middle; color: #444;">
                                            <form:checkbox id="rerunTestSuiteCheckbox" path="rerunTestSuite"
                                                           cssClass="checkbox rerunCheckbox"
                                                           cssStyle="padding-left: 10px;"
                                                           onclick="changeRerunChecks(this)"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 2px 0px; vertical-align: middle; color: #444; white-space: nowrap;">
                                            <fmtSpring:message code="page.label.rerun.all.test.scenarios"/>:
                                        </td>
                                        <td style="padding: 2px 0px; vertical-align: middle; color: #444;">
                                            <form:checkbox id="rerunAllTestScenariosCheckbox"
                                                           path="rerunAllTestScenarios"
                                                           cssClass="checkbox rerunCheckbox"
                                                           cssStyle="padding-left: 10px;"
                                                           onclick="changeRerunChecks(this)"/>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div id="actionPopupCustomButtons" class="actionPopupButtonsContainer">
                                <span id="customCancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                                                label="page.label.cancel"/></span>
                                <span id="customContinueBtnEnabled"><msgpt:Button URL="javascript:iFrameAction(6)"
                                                                                  label="page.label.continue"
                                                                                  primary="true"/></span>
                            </div>
                            <div id="actionPopupStandardButtons" class="actionPopupButtonsContainer">
                                <span id="cancelBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                  label="page.label.cancel"
                                                                                                  disabled="true"/></span>
                                <span id="cancelBtnEnabled"><msgpt:Button URL="javascript:actionCancel();"
                                                                          label="page.label.cancel"/></span>
                                <span id="continueBtnDisabled" style="display: none;"><msgpt:Button URL="#"
                                                                                                    label="page.label.continue"
                                                                                                    disabled="true"/></span>
                                <span id="continueBtnEnabled"><msgpt:Button URL="#" label="page.label.continue"
                                                                            primary="true"/></span>
                            </div>
                        </msgpt:Popup>
                    </form:form>
                </c:if>

                <c:if test="${trashTpContext}">
                    <div class="alert alert-info" role="alert">
                        <strong class="mr-2">
                            <i class="fas fa-info-circle fa-lg mr-2"
                               aria-hidden="true"></i><fmtSpring:message
                                code="page.label.info"/>:
                        </strong>
                        <fmtSpring:message code="page.text.trash.touchpoint.does.not.apply"/>
                    </div>
                </c:if>
            </msgpt:ContentPanel>
        </msgpt:LowerContainer>
        <msgpt:ContextMenu name="testSuiteList">
            <msgpt:ContextMenuEntry name="actioniFrame_1" link="#iFrameAction:1"><fmtSpring:message
                    code="page.label.edit"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_2" link="#actionSelected:2"><fmtSpring:message
                    code="page.label.delete"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_3" link="#actionSelected:3"><fmtSpring:message
                    code="page.label.rerun"/></msgpt:ContextMenuEntry>
            <msgpt:ContextMenuEntry name="actionOption_6" link="#actionSelected:6"><fmtSpring:message
                    code="page.label.rerun"/></msgpt:ContextMenuEntry>
            <c:if test="${showSendToQaModule}">
                <msgpt:ContextMenuEntry name="actionOption_8" link="#actionSelected:8">
                    <fmtSpring:message code="page.label.upload.blue.relay"/>
                </msgpt:ContextMenuEntry>
            </c:if>
        </msgpt:ContextMenu>
    </msgpt:BodyNew>
</msgpt:Html5>