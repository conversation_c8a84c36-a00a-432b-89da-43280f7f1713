<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
<msgpt:HeaderNew title="page.label.testing">


    <msgpt:Script>
        <script>
            $( function() {
                $("input:button").styleActionElement();
            });
        </script>
    </msgpt:Script>
</msgpt:HeaderNew>

<msgpt:GetHibernateObject hibernateObjectName="com.prinova.messagepoint.model.testing.TestScenario" id="${param.scenarioid}" outputName="command" />
<msgpt:GetHibernateObject hibernateObjectName="com.prinova.messagepoint.model.deliveryevent.Job" id="${param.jobid}" outputName="job" />

<msgpt:BodyNew>
	<msgpt:BannerNew  edit="true" type="full" />
	
	<!-- Report Control Bar -->
	<div style="border: 1px solid #bbb; border-left: none; border-right: none; background-color: #eee; padding: 5px;">
		<table width="100%" cellspacing="0" cellpadding="0" border="0" class="innerContentTable">
			<tr>
				<td width="98%" style="padding: 0px; vertical-align: middle;"/>
				<td width="1%" align="right" style="padding: 0px; vertical-align: middle;">
					<input title="${msgpt:getMessage('page.label.PRINT')}" type="button" onclick="window.print()" style="display: none;" />
				</td>
				<td width="1%" align="right" style="padding: 0px; vertical-align: middle;">
					<input title="${msgpt:getMessage('page.label.CLOSE')}" type="button" onclick="window.close();" style="display: none;" />
				</td>
			</tr>
		</table>
	</div>	

	<msgpt:LowerContainer>
		<msgpt:ContentPanel>
			<msgpt:ContentData title='page.label.insert.delivery.summary'>
				<!-- Message Report Summary -->
				<msgpt:DataTable multiColumn="true" labelWidths="20%" contentWidth="20%">
					<msgpt:TableHeader label="page.label.insert.report.summary" />
					<!-- Generated By -->
					<msgpt:TableItem label="page.label.generated.by">
						<c:out value="${command.updatedByName }"/>
					</msgpt:TableItem>
					<!-- Actual Deliveries -->
					<msgpt:TableItem label="page.label.actual.deliveries">
						<c:out value="${insertDeliveries}"/>
					</msgpt:TableItem>
					<!-- Date -->
					<msgpt:TableItem label="page.label.date">
						<fmtJSTL:formatDate value="${command.updated}" pattern="${dateTimeFormat}" />
					</msgpt:TableItem>
					<!-- Touchpoint -->
					<msgpt:TableItem label="page.label.touchpoint">
						<c:out value="${command.document.name}"/>
					</msgpt:TableItem>
					<!-- Time -->
					<msgpt:TableItem label="page.label.time">
						<fmtJSTL:formatDate value="${command.updated}" pattern="${timeFormatInput}" />
					</msgpt:TableItem>
					<!-- Data File -->
					<msgpt:TableItem label="page.label.data.resource"> 
						<c:out value="${command.dataResource.name}"/>
					</msgpt:TableItem>
					<!-- Run Date -->
					<msgpt:TableItem label="page.label.test.run.date">
						<fmtJSTL:formatDate value="${command.startDate}" pattern="${dateFormat}" />
					</msgpt:TableItem>
					
					<c:choose>
						<c:when test="${noInsertSchedules}">
							<msgpt:TableItem label="page.label.note">
								<c:out value='${msgpt:getMessage("page.label.no.insert.schedules")}' />
							</msgpt:TableItem>
						</c:when>
					</c:choose>
				</msgpt:DataTable>
				
				<c:choose>
					<c:when test="${empty jobStats }">
						<msgpt:DataTable>
							<msgpt:TableHeader label="page.label.production.message.report" />
							<msgpt:TableItem>
								<fmtSpring:message code="page.text.no.inserts.report" />
							</msgpt:TableItem>
						</msgpt:DataTable>
						</c:when>
						<c:otherwise>
							<msgpt:DataTable listHeader="page.label.insert.report">
								<msgpt:TableHeader label="" type="LISTHEADER">
									<tr class="contentTableHeaderTRsecond">
										<td>&nbsp;</td>
										<td colspan="2"><fmtSpring:message code="page.label.qualified" /></td>
										<td colspan="2"><fmtSpring:message code="page.label.played" /></td>
									</tr>
								</msgpt:TableHeader>
								<c:forEach var="completeReport" items="${jobStats}" varStatus="loopStatus">
									<msgpt:TableListGroup>
						   		<msgpt:GetHibernateObject hibernateObjectName="com.prinova.messagepoint.model.insert.Insert" id="${completeReport.insertId }" outputName="insert" />
									<msgpt:TableElement label="page.label.insert">
										<c:out value="${completeReport.item.name}"/>
									</msgpt:TableElement>
									<msgpt:TableElement label="#">
								    	<c:choose>
								        	<c:when test="${completeReport.qualified > 0}">
										    	<a href="javascript:popItUp('reports_insert_sub.form?scenarioid=${param.scenarioid}&jobid=${param.jobid}&insid=${completeReport.insertId}&qualified=true')">
										        	<div class="reportIcon" style="float: left; padding-top: 0px; height: 14px;">
										        	<c:out value="${completeReport.qualified }"/>
										        	</div>
									        	</a>
								         </c:when>
								       	<c:otherwise>
									        	<div class="reportIcon" style="float: left; padding-top: 0px; height: 14px;">
									        	<c:out value="${completeReport.qualified }"/>
									        	</div>
								        	</c:otherwise>
								   	</c:choose>
									</msgpt:TableElement>
									<msgpt:TableElement label="%">
										<fmtJSTL:formatNumber maxFractionDigits="2" value="${ ( completeReport.recipientCount > 0 ) ? ( completeReport.qualified/completeReport.recipientCount * 100 ) : 0 }" />%
									</msgpt:TableElement>
									<msgpt:TableElement label="#">
								    	<c:choose>
								        	<c:when test="${completeReport.delivered > 0}">
							            	<a href="javascript:popItUp('reports_insert_sub.form?scenarioid=${param.scenarioid}&jobid=${param.jobid}&insid=${completeReport.insertId}&delivered=true')">
							                	<div class="reportIcon" style="float: left; padding-top: 0px; height: 14px;">
							                	<c:out value="${ completeReport.delivered }"/>
							                	</div>
									        	</a>
								        	</c:when>
								    		<c:otherwise>
						                	<div class="reportIcon" style="float: left; padding-top: 0px;  height: 14px;">
						                	<c:out value="${ completeReport.delivered }"/>
						                	</div>
								        	</c:otherwise>
								    	</c:choose>
									</msgpt:TableElement>
									<msgpt:TableElement label="%">
										<fmtJSTL:formatNumber maxFractionDigits="2" value="${ ( completeReport.recipientCount > 0 ) ? ( completeReport.delivered/completeReport.recipientCount * 100 ) : 0 }" />%
									</msgpt:TableElement>
									</msgpt:TableListGroup>
								</c:forEach>
							</msgpt:DataTable>
						</c:otherwise>
					</c:choose>
			</msgpt:ContentData>
		</msgpt:ContentPanel>
	</msgpt:LowerContainer>
</msgpt:BodyNew>
</msgpt:Html5>
