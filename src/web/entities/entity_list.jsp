<%@page import="com.prinova.messagepoint.model.navigation.NavigationTab" %>

<%@ include file="../includes/includes.jsp" %>

<msgpt:Html5>
    <msgpt:HeaderNew title="page.label.object" extendedScripts="true">

        <msgpt:Stylesheet href="includes/themes/commoncss/theme_minimal.css"/>

        <msgpt:Script src="includes/javascript/jQueryPlugins/iButton/jquery.ibutton-1.0.03.js"/>
        <msgpt:Stylesheet href="includes/javascript/jQueryPlugins/iButton/css/jquery.ibutton-1.0.03.css"/>

        <msgpt:Script>
            <script>

                // *********  INIT: START  *********

                $(function () {
                    var resourceInfo = getResourceInfo();

                    // Should be the same as RestResourceType enum
                    var typeCodeToTitleMap = {
                        "1": client_messages.title.touchpoints,
                        "2": client_messages.title.variables,
                        "3": client_messages.title.data_sources,
                        "4": client_messages.title.background_tasks
                    };

                    var title = typeCodeToTitleMap[resourceInfo.typeCode];
                    if (title) {
                        $('.entityType').text(title);
                    }
                });

                function getSelectedItemIds(itemsSelectFilter) {
                    var selectedItemIdArr = [],
                        selectedItemIds,
                        $itemsSelectFilter = itemsSelectFilter,
                        $selectedItems = $itemsSelectFilter.children(':selected'),
                        itemSelectCount = $selectedItems.length;
                    if (itemSelectCount === 1 && $selectedItems.is('.fixedOption')) {
                        $selectedItems.siblings().each(function () {
                            selectedItemIdArr.push($(this).val());
                        });
                    } else {
                        if (itemSelectCount > 0) {
                            $selectedItems.each(function () {
                                selectedItemIdArr.push($(this).val());
                            });
                        }
                    }
                    selectedItemIds = selectedItemIdArr.join(',');
                    return selectedItemIds ? selectedItemIds : '0';
                }

                // *********  INIT END  *********

                // *********  LIST TABLE FUNCTIONS: START  *********
                function getAsyncExtListTableConfig() {
                    var obj = {};
                    obj.ajaxSource = "getListTable.form";
                    obj.columns = [
                        {
                            columnMap: 'name',
                            columnName: client_messages.text.name,
                            sort: true,
                            width: '43%',
                            colVisToggle: true
                        }
                    ];

                    var resourceInfo = getResourceInfo();

                    // TODO : Change typecode to right code when Data Elements is implemented in the rest resource type
                    if (resourceInfo.typeCode === 5) {
                        obj.columns.push({
                            columnMap: 'parentHierarchy',
                            columnName: client_messages.text.parent_hierarchy,
                            sort: false,
                            width: '43%',
                            colVisToggle: true
                        });
                    }

                    return obj;
                }

                function getAsyncExtParams() {
                    var resourceInfo = getResourceInfo();

                    var obj = [
                        {"name": "listTableType", "value": "73"},
                        {"name": "resourceTypeCode", "value": resourceInfo.typeCode},
                        {"name": "resourceGUIDs", "value": resourceInfo.GUIDs},
                        {"name": "resourceIds", "value": resourceInfo.IDs},
                        {"name": "displayMode", "value": "full"},
                    ];
                    return obj;
                }

                function getResourceInfo() {
                    var apiMessageId = gup('apiMessageId');
                    var chatApiResponse = localStorage.getItem('chat-' + apiMessageId);
                    var resourceTypeCode = null;
                    var resourceGUIDs = [];
                    var resourceIds = [];

                    if (chatApiResponse) {
                        var chatApiResponseJSON = JSON.parse(chatApiResponse);

                        if (chatApiResponseJSON.metadata) {
                            resourceTypeCode = chatApiResponseJSON.metadata.resourceType || "";
                        }

                        if (chatApiResponseJSON.data) {
                            for (var i = 0; i < chatApiResponseJSON.data.length; i++) {
                                var item = chatApiResponseJSON.data[i];
                                if (item.guid){
                                    resourceGUIDs.push(item.guid);
                                }
                                if (item.id) {
                                    resourceIds.push(item.id);
                                }
                            }
                        }

                    }
                    return {
                        typeCode: resourceTypeCode,
                        GUIDs: JSON.stringify(resourceGUIDs),
                        IDs: JSON.stringify(resourceIds),
                    };
                }
            </script>
        </msgpt:Script>

    </msgpt:HeaderNew>

    <msgpt:BodyNew type="full" cssClass="">
        <msgpt:BannerNew edit="false" tab="<%= NavigationTab.TAB_ID_TASKS %>"/>
        <msgpt:NewNavigationTabs edit="false" tab="<%= NavigationTab.TAB_ID_TASKS %>"/>
        <msgpt:ContextBarNew languageContextApplied="false" globalContextApplied="false" touchpointContextApplied="false"/>

        <div class="container mt-5">
            <div class="px-4" style="width: 1200px;">
                <form:form method="post" modelAttribute="command">
                    <form:errors path="*">
                        <msgpt:Information errorMsgs="${messages}" type="error"/>
                    </form:errors>
                    <h1 class="h4 pb-2 mb-4 entityType"><fmtSpring:message code="page.label.objects"/></h1>
                    <div class="box-shadow-4 rounded bg-white p-4">
                        <div class="px-2 py-1">
                            <div class="mb-1">
                                <div class="d-flex align-items-end mb-1">
                                    <div class="w-75" style="display: none"></div>
                                    <div class="mr-3 colVisToggleContainer" data-toggle="tooltip"
                                         title="${msgpt:getMessage('client_messages.label.toggle_columns')}">
                                    </div>
                                    <div class="form-group position-relative d-inline-block m-0">
                                        <label for="listSearchInput" class="sr-only"><fmtSpring:message
                                                code="page.label.search"/></label>
                                        <i class="far fa-search ml-3 position-absolute text-dark top-30"
                                           style="z-index: 1;" aria-hidden="true"></i>
                                        <msgpt:InputFilter type="description">
                                            <input id="listSearchInput" type="text"
                                                   class="form-control bg-lightest has-control-x border-0"
                                                   placeholder="${msgpt:getMessage('page.label.search')}"
                                                   size="40"/>
                                        </msgpt:InputFilter>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-4"></div>
                            <div class="bg-white">
                                <msgpt:DataTable id="objectList" listHeader="page.label.objects" async="true"
                                                 columnReorder="true" numUnreorderableCols="1"
                                                 columnVisibility="true" tableTools="false" tableToolsButtons="true"
                                                 drillDown="false" multiSelect="true" searchFilter="true"
                                                 asyncPaginate="false"
                                                 serverMethod="POST">
                                </msgpt:DataTable>
                            </div>
                        </div>
                    </div>
                </form:form>
            </div>
        </div>

        <msgpt:ContextMenu name="objectList">
        </msgpt:ContextMenu>

    </msgpt:BodyNew>
</msgpt:Html5>