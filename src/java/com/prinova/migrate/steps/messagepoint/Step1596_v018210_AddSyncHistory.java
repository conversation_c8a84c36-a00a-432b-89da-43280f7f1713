package com.prinova.migrate.steps.messagepoint;

import com.prinova.migrate.Migrate;
import com.prinova.migrate.MigrationStep;
import com.prinova.migrate.models.ColumnDefinition;

import java.sql.Connection;

public class Step1596_v018210_AddSyncHistory extends MigrationStep {
    static final String step_number = "1588";

    private void log(String msg) {
        Migrate.getLog().logTimed("    Step 1588 AddSyncHistory " + msg);
    }

    @Override
    public void migrate(Connection conn) throws Exception {
    	if (!tableExists(conn, "sync_history")) {
    		executeUpdate(conn, "create table sync_history (id number(19,0) not null, document_id number(19,0) not null, object_id number(19,0) not null, object_type_id number(10,0) not null, instance_guid varchar2(255 char), sync_type_id number(10,0) not null, sync_time timestamp not null, sync_by_id number(19,0) not null, source_document_id number(19,0) not null, source_object_id number(19,0) not null, source_model_hash varchar2(90 char) not null, source_active_copy_hash varchar2(90 char), source_working_copy_hash varchar2(90 char), target_model_hash varchar2(90 char) not null, target_active_copy_hash varchar2(90 char), target_working_copy_hash varchar2(90 char), primary key (id))");
    	}

        if (!columnExists(conn, "sync_history", "target_archived_copy_hash")) {
            addColumn(conn, "sync_history", ColumnDefinition.createString("target_archived_copy_hash", 90, true));
        }

        if (!columnExists(conn, "sync_history", "source_archived_copy_hash")) {
            addColumn(conn, "sync_history", ColumnDefinition.createString("source_archived_copy_hash", 90, true));
        }

    	if (!indexExists(conn, "CI_SYNC_HISTORY_01")) {
    		executeUpdate(conn, "CREATE INDEX CI_SYNC_HISTORY_01 ON SYNC_HISTORY (DOCUMENT_ID, OBJECT_ID, OBJECT_TYPE_ID, SYNC_TYPE_ID)");
    	}

    	if (!indexExists(conn, "CI_SYNC_HISTORY_02")) {
    		executeUpdate(conn, "CREATE INDEX CI_SYNC_HISTORY_02 ON SYNC_HISTORY (DOCUMENT_ID, OBJECT_ID, OBJECT_TYPE_ID, SYNC_TYPE_ID, INSTANCE_GUID, SOURCE_DOCUMENT_ID, SOURCE_OBJECT_ID)");
    	}

        if (!columnExists(conn, "document_section", "sha256_hash")) {
            addColumn(conn, "document_section", ColumnDefinition.createString("sha256_hash", 90, true));
        }

        if (!columnExists(conn, "zone", "sha256_hash")) {
            addColumn(conn, "zone", ColumnDefinition.createString("sha256_hash", 90, true));
        }

        if (!columnExists(conn, "zone_part", "sha256_hash")) {
            addColumn(conn, "zone_part", ColumnDefinition.createString("sha256_hash", 90, true));
        }

        if (!columnExists(conn, "touchpoint_selection", "sha256_hash")) {
            addColumn(conn, "touchpoint_selection", ColumnDefinition.createString("sha256_hash", 90, true));
        }

    	if (!columnExists(conn, "content_library", "sha256_hash")) {
			addColumn(conn, "content_library", ColumnDefinition.createString("sha256_hash", 90, true));
    	}

    	if (!columnExists(conn, "content_library_instance", "sha256_hash")) {
			addColumn(conn, "content_library_instance", ColumnDefinition.createString("sha256_hash", 90, true));
    	}

    	if (!columnExists(conn, "embedded_content", "sha256_hash")) {
			addColumn(conn, "embedded_content", ColumnDefinition.createString("sha256_hash", 90, true));
    	}

    	if (!columnExists(conn, "embedded_content_instance", "sha256_hash")) {
			addColumn(conn, "embedded_content_instance", ColumnDefinition.createString("sha256_hash", 90, true));
    	}

        if (!columnExists(conn, "target_group", "sha256_hash")) {
            addColumn(conn, "target_group", ColumnDefinition.createString("sha256_hash", 90, true));
        }

        if (!columnExists(conn, "condition_element", "sha256_hash")) {
            addColumn(conn, "condition_element", ColumnDefinition.createString("sha256_hash", 90, true));
        }

        if (!columnExists(conn, "parameter_group", "sha256_hash")) {
            addColumn(conn, "parameter_group", ColumnDefinition.createString("sha256_hash", 90, true));
        }

        if (!columnExists(conn, "data_element_variable", "sha256_hash")) {
            addColumn(conn, "data_element_variable", ColumnDefinition.createString("sha256_hash", 90, true));
        }

        if (!tableExists(conn, "dev_doc_hash_map")) {
        	executeUpdate(conn, "create table dev_doc_hash_map (dev_id number(19,0) not null, sha256_hash varchar2(90 char), document_id number(19,0) not null, primary key (dev_id, document_id))");
        }

    }
}
